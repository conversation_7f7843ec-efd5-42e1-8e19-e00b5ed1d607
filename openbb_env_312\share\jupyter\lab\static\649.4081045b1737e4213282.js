"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[649],{19163:(t,e,a)=>{a.d(e,{S:()=>n});var i=a(75905);function n(t,e){if(t.accDescr){e.setAccDescription?.(t.accDescr)}if(t.accTitle){e.setAccTitle?.(t.accTitle)}if(t.title){e.setDiagramTitle?.(t.title)}}(0,i.K2)(n,"populateCommonDb")},70649:(t,e,a)=>{a.d(e,{diagram:()=>K});var i=a(19163);var n=a(96049);var r=a(93113);var s=a(75905);var o=a(24010);var l=a(24982);var c=s.UI.pie;var p={sections:new Map,showData:false,config:c};var d=p.sections;var u=p.showData;var g=structuredClone(c);var f=(0,s.K2)((()=>structuredClone(g)),"getConfig");var v=(0,s.K2)((()=>{d=new Map;u=p.showData;(0,s.IU)()}),"clear");var h=(0,s.K2)((({label:t,value:e})=>{if(!d.has(t)){d.set(t,e);s.Rm.debug(`added new section: ${t}, with value: ${e}`)}}),"addSection");var m=(0,s.K2)((()=>d),"getSections");var S=(0,s.K2)((t=>{u=t}),"setShowData");var x=(0,s.K2)((()=>u),"getShowData");var w={getConfig:f,clear:v,setDiagramTitle:s.ke,getDiagramTitle:s.ab,setAccTitle:s.SV,getAccTitle:s.iN,setAccDescription:s.EI,getAccDescription:s.m7,addSection:h,getSections:m,setShowData:S,getShowData:x};var D=(0,s.K2)(((t,e)=>{(0,i.S)(t,e);e.setShowData(t.showData);t.sections.map(e.addSection)}),"populateDb");var $={parse:(0,s.K2)((async t=>{const e=await(0,o.qg)("pie",t);s.Rm.debug(e);D(e,w)}),"parse")};var y=(0,s.K2)((t=>`\n  .pieCircle{\n    stroke: ${t.pieStrokeColor};\n    stroke-width : ${t.pieStrokeWidth};\n    opacity : ${t.pieOpacity};\n  }\n  .pieOuterCircle{\n    stroke: ${t.pieOuterStrokeColor};\n    stroke-width: ${t.pieOuterStrokeWidth};\n    fill: none;\n  }\n  .pieTitleText {\n    text-anchor: middle;\n    font-size: ${t.pieTitleTextSize};\n    fill: ${t.pieTitleTextColor};\n    font-family: ${t.fontFamily};\n  }\n  .slice {\n    font-family: ${t.fontFamily};\n    fill: ${t.pieSectionTextColor};\n    font-size:${t.pieSectionTextSize};\n    // fill: white;\n  }\n  .legend text {\n    fill: ${t.pieLegendTextColor};\n    font-family: ${t.fontFamily};\n    font-size: ${t.pieLegendTextSize};\n  }\n`),"getStyles");var T=y;var C=(0,s.K2)((t=>{const e=[...t.entries()].map((t=>({label:t[0],value:t[1]}))).sort(((t,e)=>e.value-t.value));const a=(0,l.rLf)().value((t=>t.value));return a(e)}),"createPieArcs");var b=(0,s.K2)(((t,e,a,i)=>{s.Rm.debug("rendering pie chart\n"+t);const o=i.db;const c=(0,s.D7)();const p=(0,n.$t)(o.getConfig(),c.pie);const d=40;const u=18;const g=4;const f=450;const v=f;const h=(0,r.D)(e);const m=h.append("g");m.attr("transform","translate("+v/2+","+f/2+")");const{themeVariables:S}=c;let[x]=(0,n.I5)(S.pieOuterStrokeWidth);x??=2;const w=p.textPosition;const D=Math.min(v,f)/2-d;const $=(0,l.JLW)().innerRadius(0).outerRadius(D);const y=(0,l.JLW)().innerRadius(D*w).outerRadius(D*w);m.append("circle").attr("cx",0).attr("cy",0).attr("r",D+x/2).attr("class","pieOuterCircle");const T=o.getSections();const b=C(T);const k=[S.pie1,S.pie2,S.pie3,S.pie4,S.pie5,S.pie6,S.pie7,S.pie8,S.pie9,S.pie10,S.pie11,S.pie12];const K=(0,l.UMr)(k);m.selectAll("mySlices").data(b).enter().append("path").attr("d",$).attr("fill",(t=>K(t.data.label))).attr("class","pieCircle");let A=0;T.forEach((t=>{A+=t}));m.selectAll("mySlices").data(b).enter().append("text").text((t=>(t.data.value/A*100).toFixed(0)+"%")).attr("transform",(t=>"translate("+y.centroid(t)+")")).style("text-anchor","middle").attr("class","slice");m.append("text").text(o.getDiagramTitle()).attr("x",0).attr("y",-(f-50)/2).attr("class","pieTitleText");const R=m.selectAll(".legend").data(K.domain()).enter().append("g").attr("class","legend").attr("transform",((t,e)=>{const a=u+g;const i=a*K.domain().length/2;const n=12*u;const r=e*a-i;return"translate("+n+","+r+")"}));R.append("rect").attr("width",u).attr("height",u).style("fill",K).style("stroke",K);R.data(b).append("text").attr("x",u+g).attr("y",u-g).text((t=>{const{label:e,value:a}=t.data;if(o.getShowData()){return`${e} [${a}]`}return e}));const z=Math.max(...R.selectAll("text").nodes().map((t=>t?.getBoundingClientRect().width??0)));const M=v+d+u+g+z;h.attr("viewBox",`0 0 ${M} ${f}`);(0,s.a$)(h,f,M,p.useMaxWidth)}),"draw");var k={draw:b};var K={parser:$,db:w,renderer:k,styles:T}}}]);