#!/usr/bin/env python3
"""
OpenBB创新技术指标库
包含5个原创技术指标的完整实现
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False


class InnovativeIndicators:
    """创新技术指标类"""
    
    def __init__(self, data):
        """
        初始化
        
        Args:
            data: DataFrame with columns ['open', 'high', 'low', 'close', 'volume']
        """
        self.data = data.copy()
        self.data.index = pd.to_datetime(self.data.index)
        
    def dmfi(self, period=20, lambda_decay=10):
        """
        动态资金流强度指标 (Dynamic Money Flow Intensity)
        
        创新点：结合波动率、时间衰减和市场情绪的资金流指标
        
        Args:
            period: 计算周期
            lambda_decay: 时间衰减参数
            
        Returns:
            Series: DMFI指标值
        """
        high = self.data['high']
        low = self.data['low']
        close = self.data['close']
        volume = self.data['volume']
        
        # 1. 计算Money Flow Volume
        hl_diff = high - low
        hl_diff = hl_diff.replace(0, 0.0001)  # 避免除零
        mfv = volume * ((close - low) - (high - close)) / hl_diff
        
        # 2. 计算波动率比率
        atr = self._calculate_atr(14)
        atr_sma = atr.rolling(50).mean()
        vr = atr / atr_sma
        vr = vr.fillna(1)
        
        # 3. 计算时间衰减
        td = np.exp(-np.arange(len(self.data)) / lambda_decay)
        td = pd.Series(td, index=self.data.index)
        
        # 4. 计算市场情绪
        sma_20 = close.rolling(20).mean()
        ms = (close / sma_20 - 1) * 100
        ms = ms.fillna(0)
        
        # 5. 计算DMFI
        dmfi_raw = mfv * vr * td * (1 + ms / 100)
        dmfi = dmfi_raw.rolling(period).sum()
        
        # 标准化到-100到100区间
        dmfi_std = (dmfi - dmfi.rolling(252).mean()) / dmfi.rolling(252).std() * 20
        dmfi_std = dmfi_std.clip(-100, 100)
        
        return dmfi_std.fillna(0)
    
    def aisi(self, ml_period=60, retrain_freq=20):
        """
        自适应智能情绪指标 (Adaptive Intelligent Sentiment Index)
        
        创新点：使用机器学习动态调整权重的情绪指标
        
        Args:
            ml_period: 机器学习训练周期
            retrain_freq: 重新训练频率
            
        Returns:
            Series: AISI指标值
        """
        close = self.data['close']
        volume = self.data['volume']
        high = self.data['high']
        low = self.data['low']
        
        # 1. 计算基础特征
        returns = close.pct_change()
        volatility = returns.rolling(20).std()
        rsi = self._calculate_rsi(14)
        volume_ratio = volume / volume.rolling(20).mean()
        price_position = (close - low.rolling(20).min()) / (high.rolling(20).max() - low.rolling(20).min())
        
        # 2. 创建特征矩阵
        features = pd.DataFrame({
            'returns': returns,
            'volatility': volatility,
            'rsi': rsi,
            'volume_ratio': volume_ratio,
            'price_position': price_position,
            'momentum': close / close.shift(10) - 1
        }).fillna(0)
        
        # 3. 目标变量（未来5日收益率）
        target = close.shift(-5) / close - 1
        
        aisi_values = []
        
        for i in range(ml_period, len(self.data)):
            if i % retrain_freq == 0 or i == ml_period:
                # 重新训练模型
                train_features = features.iloc[i-ml_period:i]
                train_target = target.iloc[i-ml_period:i]
                
                # 移除NaN值
                valid_idx = ~(train_target.isna() | train_features.isna().any(axis=1))
                train_features_clean = train_features[valid_idx]
                train_target_clean = train_target[valid_idx]
                
                if len(train_features_clean) > 10:
                    # 标准化特征
                    scaler = StandardScaler()
                    train_features_scaled = scaler.fit_transform(train_features_clean)
                    
                    # 训练随机森林模型
                    model = RandomForestRegressor(n_estimators=50, random_state=42)
                    model.fit(train_features_scaled, train_target_clean)
                    
                    # 获取特征重要性作为权重
                    weights = model.feature_importances_
                else:
                    weights = np.array([1/6] * 6)  # 等权重
                    scaler = StandardScaler()
                    scaler.fit(train_features.fillna(0))
            
            # 计算当前AISI值
            current_features = features.iloc[i].values
            current_features_scaled = scaler.transform(current_features.reshape(1, -1))[0]
            
            # 加权计算情绪指数
            aisi_value = np.sum(current_features_scaled * weights) * 100
            aisi_values.append(aisi_value)
        
        # 创建完整的Series
        aisi_series = pd.Series(index=self.data.index)
        aisi_series.iloc[ml_period:] = aisi_values
        aisi_series = aisi_series.fillna(0)
        
        # 标准化到-100到100区间
        aisi_series = (aisi_series - aisi_series.rolling(252).mean()) / aisi_series.rolling(252).std() * 20
        aisi_series = aisi_series.clip(-100, 100).fillna(0)
        
        return aisi_series
    
    def mvci(self, short_period=10, long_period=30, volume_period=20):
        """
        多维度价值收敛指标 (Multi-Dimensional Value Convergence Index)
        
        创新点：结合价格、成交量、波动率的多维度收敛分析
        
        Args:
            short_period: 短期周期
            long_period: 长期周期
            volume_period: 成交量周期
            
        Returns:
            Series: MVCI指标值
        """
        close = self.data['close']
        volume = self.data['volume']
        high = self.data['high']
        low = self.data['low']
        
        # 1. 价格收敛度
        sma_short = close.rolling(short_period).mean()
        sma_long = close.rolling(long_period).mean()
        price_convergence = (sma_short - sma_long) / sma_long * 100
        
        # 2. 成交量收敛度
        volume_sma = volume.rolling(volume_period).mean()
        volume_convergence = (volume - volume_sma) / volume_sma * 100
        
        # 3. 波动率收敛度
        atr = self._calculate_atr(14)
        atr_sma = atr.rolling(long_period).mean()
        volatility_convergence = (atr - atr_sma) / atr_sma * 100
        
        # 4. 价格位置收敛度
        highest = high.rolling(long_period).max()
        lowest = low.rolling(long_period).min()
        price_position = (close - lowest) / (highest - lowest) * 100
        position_convergence = price_position - 50  # 中位数为0
        
        # 5. 动态权重计算
        recent_volatility = close.pct_change().rolling(10).std()
        volatility_weight = 1 / (1 + recent_volatility * 100)  # 波动率越高权重越低
        
        # 6. 综合MVCI
        mvci = (
            price_convergence * 0.4 * volatility_weight +
            volume_convergence * 0.3 +
            volatility_convergence * 0.2 +
            position_convergence * 0.1
        )
        
        # 平滑处理
        mvci_smooth = mvci.rolling(5).mean()
        
        return mvci_smooth.fillna(0)
    
    def tafi(self, base_period=20, adaptive_factor=0.1):
        """
        时间自适应趋势指标 (Time-Adaptive Trend Following Index)
        
        创新点：根据市场状态自动调整计算周期的趋势指标
        
        Args:
            base_period: 基础周期
            adaptive_factor: 自适应因子
            
        Returns:
            Series: TAFI指标值
        """
        close = self.data['close']
        volume = self.data['volume']
        
        # 1. 计算市场状态
        returns = close.pct_change()
        volatility = returns.rolling(20).std()
        trend_strength = abs(close.rolling(20).mean() / close.rolling(60).mean() - 1)
        
        # 2. 自适应周期计算
        market_efficiency = 1 - volatility / volatility.rolling(60).mean()
        market_efficiency = market_efficiency.fillna(0.5).clip(0.1, 2.0)
        
        adaptive_period = (base_period * market_efficiency).round().astype(int)
        adaptive_period = adaptive_period.clip(5, 60)  # 限制周期范围
        
        tafi_values = []
        
        for i in range(len(self.data)):
            if i < base_period:
                tafi_values.append(0)
                continue
            
            period = adaptive_period.iloc[i]
            start_idx = max(0, i - period + 1)
            
            # 计算自适应移动平均
            weights = np.exp(np.linspace(-1, 0, period))
            weights = weights / weights.sum()
            
            price_window = close.iloc[start_idx:i+1]
            volume_window = volume.iloc[start_idx:i+1]
            
            if len(price_window) == period:
                # 价格趋势
                price_trend = np.sum(price_window.values * weights)
                current_price = close.iloc[i]
                price_signal = (current_price - price_trend) / price_trend * 100
                
                # 成交量确认
                volume_trend = np.sum(volume_window.values * weights)
                current_volume = volume.iloc[i]
                volume_signal = (current_volume - volume_trend) / volume_trend * 100
                
                # 综合信号
                tafi_value = price_signal * 0.7 + volume_signal * 0.3
                tafi_values.append(tafi_value)
            else:
                tafi_values.append(0)
        
        tafi_series = pd.Series(tafi_values, index=self.data.index)
        
        # 平滑和标准化
        tafi_smooth = tafi_series.rolling(3).mean()
        tafi_std = (tafi_smooth - tafi_smooth.rolling(252).mean()) / tafi_smooth.rolling(252).std() * 15
        tafi_final = tafi_std.clip(-100, 100).fillna(0)
        
        return tafi_final
    
    def fmsi(self, fundamental_weight=0.3, technical_weight=0.7):
        """
        基本面-技术面综合指标 (Fundamental-Technical Momentum Synthesis Index)
        
        创新点：将基本面因子与技术面指标有机结合
        
        Args:
            fundamental_weight: 基本面权重
            technical_weight: 技术面权重
            
        Returns:
            Series: FMSI指标值
        """
        close = self.data['close']
        volume = self.data['volume']
        high = self.data['high']
        low = self.data['low']
        
        # 1. 技术面分析
        # 价格动量
        price_momentum = close / close.shift(20) - 1
        
        # 相对强弱
        rsi = self._calculate_rsi(14)
        rsi_normalized = (rsi - 50) / 50  # 标准化到-1到1
        
        # 成交量动量
        volume_momentum = volume / volume.rolling(20).mean() - 1
        
        # 波动率调整
        volatility = close.pct_change().rolling(20).std()
        volatility_adjustment = 1 / (1 + volatility * 10)
        
        # 技术面综合得分
        technical_score = (
            price_momentum * 0.4 +
            rsi_normalized * 0.3 +
            volume_momentum * 0.3
        ) * volatility_adjustment
        
        # 2. 基本面代理指标（基于价格行为推断）
        # 价值回归倾向
        price_to_ma = close / close.rolling(252).mean()
        value_reversion = 1 / price_to_ma  # 价格越高，价值回归压力越大
        
        # 成长性指标
        price_growth = close.rolling(60).mean() / close.rolling(252).mean()
        growth_momentum = (price_growth - 1) * 2
        
        # 质量指标（基于价格稳定性）
        price_stability = 1 / (close.pct_change().rolling(60).std() + 0.01)
        quality_score = (price_stability - price_stability.rolling(252).mean()) / price_stability.rolling(252).std()
        
        # 基本面综合得分
        fundamental_score = (
            value_reversion * 0.4 +
            growth_momentum * 0.4 +
            quality_score * 0.2
        )
        
        # 3. 综合FMSI
        fmsi = (
            technical_score * technical_weight +
            fundamental_score * fundamental_weight
        )
        
        # 标准化处理
        fmsi_std = (fmsi - fmsi.rolling(252).mean()) / fmsi.rolling(252).std() * 25
        fmsi_final = fmsi_std.clip(-100, 100).fillna(0)
        
        return fmsi_final
    
    def _calculate_atr(self, period=14):
        """计算ATR"""
        high = self.data['high']
        low = self.data['low']
        close = self.data['close']
        
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(period).mean()
        
        return atr
    
    def _calculate_rsi(self, period=14):
        """计算RSI"""
        close = self.data['close']
        delta = close.diff()
        
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        avg_gain = gain.rolling(period).mean()
        avg_loss = loss.rolling(period).mean()
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi

    def calculate_all_indicators(self):
        """计算所有创新指标"""
        print("🔄 计算创新技术指标...")

        indicators = {}

        try:
            indicators['DMFI'] = self.dmfi()
            print("✅ DMFI - 动态资金流强度指标")
        except Exception as e:
            print(f"❌ DMFI计算失败: {e}")
            indicators['DMFI'] = pd.Series(0, index=self.data.index)

        try:
            indicators['AISI'] = self.aisi()
            print("✅ AISI - 自适应智能情绪指标")
        except Exception as e:
            print(f"❌ AISI计算失败: {e}")
            indicators['AISI'] = pd.Series(0, index=self.data.index)

        try:
            indicators['MVCI'] = self.mvci()
            print("✅ MVCI - 多维度价值收敛指标")
        except Exception as e:
            print(f"❌ MVCI计算失败: {e}")
            indicators['MVCI'] = pd.Series(0, index=self.data.index)

        try:
            indicators['TAFI'] = self.tafi()
            print("✅ TAFI - 时间自适应趋势指标")
        except Exception as e:
            print(f"❌ TAFI计算失败: {e}")
            indicators['TAFI'] = pd.Series(0, index=self.data.index)

        try:
            indicators['FMSI'] = self.fmsi()
            print("✅ FMSI - 基本面-技术面综合指标")
        except Exception as e:
            print(f"❌ FMSI计算失败: {e}")
            indicators['FMSI'] = pd.Series(0, index=self.data.index)

        return indicators

    def plot_indicators(self, indicators=None, figsize=(15, 20)):
        """绘制所有指标图表"""
        if indicators is None:
            indicators = self.calculate_all_indicators()

        fig, axes = plt.subplots(6, 1, figsize=figsize)
        fig.suptitle('OpenBB创新技术指标分析', fontsize=16, fontweight='bold')

        # 价格图
        axes[0].plot(self.data.index, self.data['close'], label='收盘价', color='black', linewidth=1.5)
        axes[0].set_title('股价走势', fontsize=12, fontweight='bold')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)

        # 指标图
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
        indicator_names = ['DMFI', 'AISI', 'MVCI', 'TAFI', 'FMSI']
        indicator_titles = [
            'DMFI - 动态资金流强度指标',
            'AISI - 自适应智能情绪指标',
            'MVCI - 多维度价值收敛指标',
            'TAFI - 时间自适应趋势指标',
            'FMSI - 基本面-技术面综合指标'
        ]

        for i, (name, title) in enumerate(zip(indicator_names, indicator_titles)):
            if name in indicators:
                axes[i+1].plot(indicators[name].index, indicators[name],
                             color=colors[i], linewidth=2, label=name)
                axes[i+1].axhline(y=0, color='gray', linestyle='--', alpha=0.5)
                axes[i+1].axhline(y=50, color='red', linestyle=':', alpha=0.5)
                axes[i+1].axhline(y=-50, color='green', linestyle=':', alpha=0.5)
                axes[i+1].set_title(title, fontsize=11, fontweight='bold')
                axes[i+1].legend()
                axes[i+1].grid(True, alpha=0.3)
                axes[i+1].set_ylim(-100, 100)

        plt.tight_layout()
        return fig

    def generate_signals(self, indicators=None):
        """生成交易信号"""
        if indicators is None:
            indicators = self.calculate_all_indicators()

        signals = pd.DataFrame(index=self.data.index)

        # DMFI信号
        dmfi = indicators.get('DMFI', pd.Series(0, index=self.data.index))
        signals['DMFI_Signal'] = np.where(dmfi > 30, 1, np.where(dmfi < -30, -1, 0))

        # AISI信号
        aisi = indicators.get('AISI', pd.Series(0, index=self.data.index))
        signals['AISI_Signal'] = np.where(aisi > 40, 1, np.where(aisi < -40, -1, 0))

        # MVCI信号
        mvci = indicators.get('MVCI', pd.Series(0, index=self.data.index))
        signals['MVCI_Signal'] = np.where(mvci > 20, 1, np.where(mvci < -20, -1, 0))

        # TAFI信号
        tafi = indicators.get('TAFI', pd.Series(0, index=self.data.index))
        signals['TAFI_Signal'] = np.where(tafi > 25, 1, np.where(tafi < -25, -1, 0))

        # FMSI信号
        fmsi = indicators.get('FMSI', pd.Series(0, index=self.data.index))
        signals['FMSI_Signal'] = np.where(fmsi > 35, 1, np.where(fmsi < -35, -1, 0))

        # 综合信号
        signals['Composite_Signal'] = (
            signals['DMFI_Signal'] * 0.25 +
            signals['AISI_Signal'] * 0.25 +
            signals['MVCI_Signal'] * 0.2 +
            signals['TAFI_Signal'] * 0.15 +
            signals['FMSI_Signal'] * 0.15
        )

        # 最终信号
        signals['Final_Signal'] = np.where(
            signals['Composite_Signal'] > 0.5, 'BUY',
            np.where(signals['Composite_Signal'] < -0.5, 'SELL', 'HOLD')
        )

        return signals
