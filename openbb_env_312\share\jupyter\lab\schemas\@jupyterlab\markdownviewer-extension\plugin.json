{"jupyter.lab.setting-icon": "ui-components:markdown", "jupyter.lab.setting-icon-label": "Markdown Viewer", "title": "Markdown Viewer", "description": "Markdown viewer settings.", "jupyter.lab.menus": {"context": [{"command": "markdownviewer:edit", "selector": ".jp-RenderedMarkdown"}]}, "definitions": {"fontFamily": {"type": ["string", "null"]}, "fontSize": {"type": ["integer", "null"], "minimum": 1, "maximum": 100}, "lineHeight": {"type": ["number", "null"]}, "lineWidth": {"type": ["number", "null"]}, "hideFrontMatter": {"type": "boolean"}, "renderTimeout": {"type": "number"}}, "properties": {"fontFamily": {"title": "Font Family", "description": "The font family used to render markdown.\nIf `null`, value from current theme is used.", "$ref": "#/definitions/fontFamily", "default": null}, "fontSize": {"title": "Font Size", "description": "The size in pixel of the font used to render markdown.\nIf `null`, value from current theme is used.", "$ref": "#/definitions/fontSize", "default": null}, "lineHeight": {"title": "Line Height", "description": "The line height used to render markdown.\nIf `null`, value from current theme is used.", "$ref": "#/definitions/lineHeight", "default": null}, "lineWidth": {"title": "Line Width", "description": "The text line width expressed in CSS ch units.\nIf `null`, lines fit the viewport width.", "$ref": "#/definitions/lineWidth", "default": null}, "hideFrontMatter": {"title": "Hide Front Matter", "description": "Whether to hide YAML front matter.\nThe YAML front matter must be placed at the top of the document,\nstarted by a line of three dashes (---) and ended by a line of\nthree dashes (---) or three points (...).", "$ref": "#/definitions/hideFrontMatter", "default": true}, "renderTimeout": {"title": "Render Timeout", "description": "The render timeout in milliseconds.", "$ref": "#/definitions/renderTimeout", "default": 1000}}, "additionalProperties": false, "type": "object"}