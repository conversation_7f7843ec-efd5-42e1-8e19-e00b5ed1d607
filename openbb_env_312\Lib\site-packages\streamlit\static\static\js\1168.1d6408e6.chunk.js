"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[1168],{71168:(l,e,o)=>{o.r(e),o.d(e,{default:()=>b});var r=o(66845),t=o(25621),c=o(62622),a=o(42736),n=o(96825),i=o.n(n),p=o(92627),s=o(63765),f=o(23849);function y(l,e,o){return l=function(l,e){return(l=(l=(l=(l=(l=(l=(l=(l=l.replaceAll("#000032",(0,p.By)(e))).replaceAll("#000033",(0,p.He)(e))).replaceAll("#000034",(0,p.Iy)(e)?e.colors.blue80:e.colors.blue40)).replaceAll("#000035",(0,p.ny)(e))).replaceAll("#000036",(0,p.Xy)(e))).replaceAll("#000037",(0,p.yq)(e))).replaceAll("#000038",e.colors.bgColor)).replaceAll("#000039",e.colors.fadedText05)).replaceAll("#000040",e.colors.bgMix)}(l,e),l=function(l,e,o){const r="#000001",t="#000002",c="#000003",a="#000004",n="#000005",i="#000006",s="#000007",f="#000008",y="#000009",d="#000010";if("streamlit"===o){const o=(0,p.iY)(e);l=(l=(l=(l=(l=(l=(l=(l=(l=(l=l.replaceAll(r,o[0])).replaceAll(t,o[1])).replaceAll(c,o[2])).replaceAll(a,o[3])).replaceAll(n,o[4])).replaceAll(i,o[5])).replaceAll(s,o[6])).replaceAll(f,o[7])).replaceAll(y,o[8])).replaceAll(d,o[9])}else l=(l=(l=(l=(l=(l=(l=(l=(l=(l=l.replaceAll(r,"#636efa")).replaceAll(t,"#EF553B")).replaceAll(c,"#00cc96")).replaceAll(a,"#ab63fa")).replaceAll(n,"#FFA15A")).replaceAll(i,"#19d3f3")).replaceAll(s,"#FF6692")).replaceAll(f,"#B6E880")).replaceAll(y,"#FF97FF")).replaceAll(d,"#FECB52");return l}(l,e,o),l=function(l,e,o){const r="#000011",t="#000012",c="#000013",a="#000014",n="#000015",i="#000016",s="#000017",f="#000018",y="#000019",d="#000020";if("streamlit"===o){const o=(0,p.Gy)(e);l=(l=(l=(l=(l=(l=(l=(l=(l=(l=l.replaceAll(r,o[0])).replaceAll(t,o[1])).replaceAll(c,o[2])).replaceAll(a,o[3])).replaceAll(n,o[4])).replaceAll(i,o[5])).replaceAll(s,o[6])).replaceAll(f,o[7])).replaceAll(y,o[8])).replaceAll(d,o[9])}else l=(l=(l=(l=(l=(l=(l=(l=(l=(l=l.replaceAll(r,"#0d0887")).replaceAll(t,"#46039f")).replaceAll(c,"#7201a8")).replaceAll(a,"#9c179e")).replaceAll(n,"#bd3786")).replaceAll(i,"#d8576b")).replaceAll(s,"#ed7953")).replaceAll(f,"#fb9f3a")).replaceAll(y,"#fdca26")).replaceAll(d,"#f0f921");return l}(l,e,o),l=function(l,e,o){const r="#000021",t="#000022",c="#000023",a="#000024",n="#000025",i="#000026",s="#000027",f="#000028",y="#000029",d="#000030",u="#000031";if("streamlit"===o){const o=(0,p.ru)(e);l=(l=(l=(l=(l=(l=(l=(l=(l=(l=(l=l.replaceAll(r,o[0])).replaceAll(t,o[1])).replaceAll(c,o[2])).replaceAll(a,o[3])).replaceAll(n,o[4])).replaceAll(i,o[5])).replaceAll(s,o[6])).replaceAll(f,o[7])).replaceAll(y,o[8])).replaceAll(d,o[9])).replaceAll(u,o[10])}else l=(l=(l=(l=(l=(l=(l=(l=(l=(l=(l=l.replaceAll(r,"#8e0152")).replaceAll(t,"#c51b7d")).replaceAll(c,"#de77ae")).replaceAll(a,"#f1b6da")).replaceAll(n,"#fde0ef")).replaceAll(i,"#f7f7f7")).replaceAll(s,"#e6f5d0")).replaceAll(f,"#b8e186")).replaceAll(y,"#7fbc41")).replaceAll(d,"#4d9221")).replaceAll(u,"#276419");return l}(l,e,o),l}function d(l,e){try{!function(l,e){const{genericFonts:o,colors:r,fontSizes:t}=e,c={font:{color:(0,p.Xy)(e),family:o.bodyFont,size:t.twoSmPx},title:{color:r.headingColor,subtitleColor:r.bodyText,font:{family:o.headingFont,size:t.mdPx,color:r.headingColor},pad:{l:e.spacing.twoXSPx},xanchor:"left",x:0},legend:{title:{font:{size:t.twoSmPx,color:(0,p.Xy)(e)},side:"top"},valign:"top",bordercolor:r.transparent,borderwidth:e.spacing.nonePx,font:{size:t.twoSmPx,color:(0,p.yq)(e)}},paper_bgcolor:r.bgColor,plot_bgcolor:r.bgColor,yaxis:{ticklabelposition:"outside",zerolinecolor:(0,p.ny)(e),title:{font:{color:(0,p.Xy)(e),size:t.smPx},standoff:e.spacing.twoXLPx},tickcolor:(0,p.ny)(e),tickfont:{color:(0,p.Xy)(e),size:t.twoSmPx},gridcolor:(0,p.ny)(e),minor:{gridcolor:(0,p.ny)(e)},automargin:!0},xaxis:{zerolinecolor:(0,p.ny)(e),gridcolor:(0,p.ny)(e),showgrid:!1,tickfont:{color:(0,p.Xy)(e),size:t.twoSmPx},tickcolor:(0,p.ny)(e),title:{font:{color:(0,p.Xy)(e),size:t.smPx},standoff:e.spacing.xlPx},minor:{gridcolor:(0,p.ny)(e)},zeroline:!1,automargin:!0,rangeselector:{bgcolor:r.bgColor,bordercolor:(0,p.ny)(e),borderwidth:1,x:0}},margin:{pad:e.spacing.smPx,r:e.spacing.nonePx,l:e.spacing.nonePx},hoverlabel:{bgcolor:r.bgColor,bordercolor:r.fadedText10,font:{color:(0,p.Xy)(e),family:o.bodyFont,size:t.twoSmPx}},coloraxis:{colorbar:{thickness:16,xpad:e.spacing.twoXLPx,ticklabelposition:"outside",outlinecolor:r.transparent,outlinewidth:8,len:.75,y:.5745,title:{font:{color:(0,p.Xy)(e),size:t.smPx}},tickfont:{color:(0,p.Xy)(e),size:t.twoSmPx}}},ternary:{gridcolor:(0,p.Xy)(e),bgcolor:r.bgColor,title:{font:{family:o.bodyFont,size:t.smPx}},color:(0,p.Xy)(e),aaxis:{gridcolor:(0,p.Xy)(e),linecolor:(0,p.Xy)(e),tickfont:{family:o.bodyFont,size:t.twoSmPx}},baxis:{linecolor:(0,p.Xy)(e),gridcolor:(0,p.Xy)(e),tickfont:{family:o.bodyFont,size:t.twoSmPx}},caxis:{linecolor:(0,p.Xy)(e),gridcolor:(0,p.Xy)(e),tickfont:{family:o.bodyFont,size:t.twoSmPx}}}};i()(l,c)}(l.layout.template.layout,e)}catch(o){const l=(0,s.b)(o);(0,f.H)(l)}"title"in l.layout&&(l.layout.title=i()(l.layout.title,{text:"<b>".concat(l.layout.title.text,"</b>")}))}var u=o(40864);const A=450;function g(l){let{element:e,width:o,height:c,isFullScreen:n}=l;const i=e.figure,p=(0,t.u)(),s=(0,r.useCallback)((()=>{const l=JSON.parse(y(i.spec,p,e.theme)),r=l.layout.height,t=l.layout.width;return n?(l.layout.width=o,l.layout.height=c):e.useContainerWidth?l.layout.width=o:(l.layout.width=t,l.layout.height=r),"streamlit"===e.theme?d(l,p):l.layout=function(l,e){const{colors:o,genericFonts:r}=e,t={font:{color:o.bodyText,family:r.bodyFont},paper_bgcolor:o.bgColor,plot_bgcolor:o.secondaryBg};return{...l,font:{...t.font,...l.font},paper_bgcolor:l.paper_bgcolor||t.paper_bgcolor,plot_bgcolor:l.plot_bgcolor||t.plot_bgcolor}}(l.layout,p),l}),[e.theme,e.useContainerWidth,i.spec,c,p,o,n]),[f,A]=(0,r.useState)(JSON.parse(i.config)),[g,b]=(0,r.useState)(s());(0,r.useLayoutEffect)((()=>{A(JSON.parse(i.config)),b(s())}),[e,p,c,o,i.config,s]);const{data:h,layout:m,frames:x}=g;return(0,u.jsx)(a.Z,{className:"stPlotlyChart",data:h,layout:m,config:f,frames:x},n?"fullscreen":"original")}const b=(0,c.Z)((function(l){let{width:e,element:o,height:r,isFullScreen:t}=l;switch(o.chart){case"url":return function(l){let{url:e,width:o,height:r}=l;const t=r||A;return(0,u.jsx)("iframe",{title:"Plotly",src:e,style:{width:o,height:t,colorScheme:"normal"}})}({url:o.url,height:r,width:e});case"figure":return(0,u.jsx)(g,{width:e,element:o,height:r,isFullScreen:t});default:throw new Error("Unrecognized PlotlyChart type: ".concat(o.chart))}}))}}]);