# OpenBB-CN (A股/港股/期货) — 最小可运行骨架

面向中国市场（A股、港股、期货）的开放金融数据平台骨架，包含：

- FastAPI REST API（可扩展 Provider 适配器）
- Python SDK 调用范式（`obb.cn.*` 风格）
- PostgreSQL 缓存/快照（SQLAlchemy 自动建表）
- Docker 一键部署（API + Postgres）
- Typer CLI（生产可用雏形）

## 功能范围（MVP）
- Equity 历史行情（日线/分钟线占位）：`/equity/price/historical`
- 交易日历、代码解析（占位）
- Provider 机制：Tushare（需 `TUSHARE_TOKEN`），其余按需接入

## 快速开始（本地开发）
1) 创建并激活虚拟环境（示例）
```powershell
python -m venv .venv
.\.venv\Scripts\activate
```

2) 安装依赖
```powershell
pip install -r requirements.txt
```

3) 设置环境变量（或复制为 `.env`）
```env
DATABASE_URL=postgresql+psycopg2://openbb:openbb@localhost:5432/openbb_cn
TUSHARE_TOKEN=你的Tushare Token
APP_HOST=0.0.0.0
APP_PORT=8000
```

4) 启动 API
```powershell
uvicorn openbb_cn.api.main:app --host 0.0.0.0 --port 8000 --reload
```
访问 `http://127.0.0.1:8000/docs` 查看接口。

## Docker 部署（生产推荐）
```bash
docker compose up -d --build
```
默认暴露 `8000` 端口，Postgres 在 `db:5432`。

## Python SDK 用法（示例）
```python
from openbb_cn.sdk import obb_cn

out = obb_cn.equity.price.historical(
    symbol="600519.SH",
    start_date="2023-01-01",
    end_date="2023-12-31",
    interval="1d",
    provider="tushare",
)
print(out.to_dataframe().head())
```

## CLI 示例
```bash
python -m openbb_cn.cli equity price historical 600519.SH --start 2023-01-01 --end 2023-12-31 --interval 1d --provider tushare
```

## 目录结构
```
openbb_cn/
  api/            # FastAPI 应用与路由
  core/           # 模型、服务、Provider基类、缓存仓储
  providers/      # 各 Provider 适配器（tushare 等）
  cli/            # Typer CLI
  sdk/            # Python SDK 外观封装
docker/
  Dockerfile
docker-compose.yml
.env.example
requirements.txt
```

## 说明与合规
- 本项目仅提供合规数据源的接入能力（如 Tushare/JQData 等持证或授权 API）。
- 本地开发 / 开发环境(dev) / 生产环境(prod) 均使用真实数据源，不提供任何“测试/模拟/回放”数据路径；缺少凭据会直接报错，不会降级为伪数据。
- 严禁接入仅允许“学习研究用途”的抓取型来源于生产环境。
- 实时/盘口数据需对应源的授权与带宽支持，默认以 Provider 能力为准。

