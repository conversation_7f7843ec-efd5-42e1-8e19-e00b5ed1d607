#!/usr/bin/env python3
"""
OpenBB项目快速启动和监控脚本
"""

import subprocess
import time
import requests
import os
import sys
from pathlib import Path


def check_service(url, name):
    """检查服务是否运行"""
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            print(f"✅ {name} 运行正常")
            return True
    except:
        pass
    print(f"❌ {name} 未运行")
    return False


def start_service(cmd, name, cwd=None):
    """启动服务"""
    try:
        print(f"🚀 启动 {name}...")
        process = subprocess.Popen(
            cmd,
            cwd=cwd or Path.cwd(),
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
        )
        print(f"✅ {name} 启动成功 (PID: {process.pid})")
        return process
    except Exception as e:
        print(f"❌ {name} 启动失败: {e}")
        return None


def main():
    """主函数"""
    print("🚀 OpenBB项目启动器")
    print("=" * 50)
    
    project_root = Path.cwd()
    python_exe = project_root / "openbb_env_312" / "Scripts" / "python.exe"
    
    if not python_exe.exists():
        print(f"❌ Python环境不存在: {python_exe}")
        return
    
    # 服务配置
    services = [
        {
            "name": "A股API服务",
            "cmd": [str(python_exe), "openbb_astock_api.py", "--host", "127.0.0.1", "--port", "6901"],
            "health_url": "http://127.0.0.1:6901/health",
            "wait": 8
        },
        {
            "name": "免费API服务", 
            "cmd": [str(python_exe), "openbb_free_api_server.py"],
            "health_url": "http://127.0.0.1:6902/health",
            "wait": 8
        },
        {
            "name": "Streamlit客户端",
            "cmd": [str(python_exe), "-m", "streamlit", "run", "OpenBB完整客户端.py", "--server.port", "8502"],
            "health_url": "http://localhost:8502",
            "wait": 15
        }
    ]
    
    processes = []
    
    try:
        # 启动所有服务
        for service in services:
            # 检查是否已经运行
            if check_service(service["health_url"], service["name"]):
                print(f"⚠️ {service['name']} 已在运行")
                continue
            
            # 启动服务
            process = start_service(service["cmd"], service["name"])
            if process:
                processes.append((process, service["name"]))
                print(f"⏳ 等待 {service['name']} 启动...")
                time.sleep(service["wait"])
                
                # 验证启动
                if check_service(service["health_url"], service["name"]):
                    print(f"🎉 {service['name']} 启动并验证成功")
                else:
                    print(f"⚠️ {service['name']} 启动但验证失败")
        
        print("\n" + "=" * 50)
        print("🎉 所有服务启动完成!")
        print("\n📊 服务地址:")
        print("- A股API: http://127.0.0.1:6901")
        print("- 免费API: http://127.0.0.1:6902") 
        print("- Streamlit: http://localhost:8502")
        
        print("\n🔧 现在启动监控系统...")
        
        # 启动监控系统
        monitor_process = start_service(
            [str(python_exe), "自动监控修复系统.py"],
            "监控系统"
        )
        
        if monitor_process:
            print("\n✅ 监控系统已启动，将自动检测和修复问题")
            print("按 Ctrl+C 停止所有服务")
            
            try:
                monitor_process.wait()
            except KeyboardInterrupt:
                print("\n🛑 正在停止所有服务...")
                monitor_process.terminate()
                
                for process, name in processes:
                    try:
                        process.terminate()
                        print(f"✅ {name} 已停止")
                    except:
                        pass
        
    except KeyboardInterrupt:
        print("\n🛑 正在停止所有服务...")
        for process, name in processes:
            try:
                process.terminate()
                print(f"✅ {name} 已停止")
            except:
                pass


if __name__ == "__main__":
    main()
