#!/usr/bin/env python3
"""
OpenBB A股数据提供商
集成Tushare Pro、AKShare、东方财富API到OpenBB平台
"""

from typing import Any, Dict, List, Optional
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

try:
    from openbb_core.provider.abstract.provider import Provider
    from openbb_core.provider.abstract.fetcher import Fetcher
    from openbb_core.provider.standard_models.equity_historical import EquityHistoricalData
    from openbb_core.provider.standard_models.equity_quote import EquityQuoteData
    OPENBB_AVAILABLE = True
except ImportError:
    OPENBB_AVAILABLE = False
    print("⚠️ OpenBB Core 不可用，使用独立模式")


class AStockDataFetcher:
    """A股数据获取器"""
    
    def __init__(self, tushare_token: Optional[str] = None):
        """
        初始化A股数据获取器
        
        Args:
            tushare_token: Tushare Pro API Token
        """
        self.tushare_token = tushare_token
        self.providers = []
        
        # 初始化数据源
        self._init_providers()
    
    def _init_providers(self):
        """初始化数据提供商"""
        # 1. 初始化Tushare Pro
        try:
            import tushare as ts
            if self.tushare_token:
                ts.set_token(self.tushare_token)
                self.ts_pro = ts.pro_api()
                self.providers.append("tushare_pro")
                print("✅ Tushare Pro 已初始化")
            else:
                self.ts = ts
                self.providers.append("tushare_free")
                print("✅ Tushare 免费版已初始化")
        except ImportError:
            print("❌ Tushare 未安装")
        except Exception as e:
            print(f"⚠️ Tushare 初始化失败: {e}")
        
        # 2. 初始化AKShare
        try:
            import akshare as ak
            self.ak = ak
            self.providers.append("akshare")
            print("✅ AKShare 已初始化")
        except ImportError:
            print("❌ AKShare 未安装")
        except Exception as e:
            print(f"⚠️ AKShare 初始化失败: {e}")
        
        # 3. 初始化东方财富
        try:
            import efinance as ef
            self.ef = ef
            self.providers.append("efinance")
            print("✅ 东方财富 已初始化")
        except ImportError:
            print("❌ 东方财富 未安装")
        except Exception as e:
            print(f"⚠️ 东方财富 初始化失败: {e}")
        
        print(f"🚀 可用数据源: {', '.join(self.providers)}")
    
    def fetch_equity_historical(
        self,
        symbol: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        provider: str = "auto"
    ) -> Optional[pd.DataFrame]:
        """
        获取股票历史数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            provider: 数据提供商 ("tushare_pro", "akshare", "efinance", "auto")
        """
        print(f"📈 获取 {symbol} 历史数据 (provider: {provider})")
        
        # 标准化股票代码
        normalized_symbol = self._normalize_symbol(symbol)
        
        # 选择数据提供商
        if provider == "auto":
            providers_to_try = self.providers
        else:
            providers_to_try = [provider] if provider in self.providers else self.providers
        
        # 尝试各个数据源
        for prov in providers_to_try:
            try:
                if prov == "tushare_pro" and hasattr(self, 'ts_pro'):
                    data = self._fetch_tushare_pro_data(normalized_symbol, start_date, end_date)
                elif prov == "tushare_free" and hasattr(self, 'ts'):
                    data = self._fetch_tushare_free_data(normalized_symbol, start_date, end_date)
                elif prov == "akshare" and hasattr(self, 'ak'):
                    data = self._fetch_akshare_data(normalized_symbol, start_date, end_date)
                elif prov == "efinance" and hasattr(self, 'ef'):
                    data = self._fetch_efinance_data(normalized_symbol, start_date, end_date)
                else:
                    continue
                
                if data is not None and not data.empty:
                    print(f"✅ 使用 {prov} 获取到 {len(data)} 条数据")
                    return self._standardize_data(data)
                    
            except Exception as e:
                print(f"⚠️ {prov} 获取失败: {e}")
                continue
        
        print("❌ 所有数据源都失败")
        return None
    
    def fetch_equity_quote(
        self,
        symbols: List[str],
        provider: str = "auto"
    ) -> Optional[pd.DataFrame]:
        """
        获取股票实时报价
        
        Args:
            symbols: 股票代码列表
            provider: 数据提供商
        """
        print(f"💰 获取实时报价: {symbols} (provider: {provider})")
        
        # 选择数据提供商
        if provider == "auto":
            providers_to_try = ["efinance", "akshare", "tushare_pro"]  # 实时数据优先级
        else:
            providers_to_try = [provider] if provider in self.providers else ["efinance", "akshare"]
        
        # 尝试各个数据源
        for prov in providers_to_try:
            try:
                if prov == "efinance" and hasattr(self, 'ef'):
                    data = self._fetch_efinance_quotes(symbols)
                elif prov == "akshare" and hasattr(self, 'ak'):
                    data = self._fetch_akshare_quotes(symbols)
                elif prov == "tushare_pro" and hasattr(self, 'ts_pro'):
                    data = self._fetch_tushare_quotes(symbols)
                else:
                    continue
                
                if data is not None and not data.empty:
                    print(f"✅ 使用 {prov} 获取到 {len(data)} 只股票报价")
                    return data
                    
            except Exception as e:
                print(f"⚠️ {prov} 获取失败: {e}")
                continue
        
        print("❌ 所有数据源都失败")
        return None
    
    def _normalize_symbol(self, symbol: str) -> str:
        """标准化股票代码"""
        # 移除可能的后缀
        clean_symbol = symbol.replace('.SH', '').replace('.SZ', '').replace('.BJ', '')
        
        # 如果已经有后缀，直接返回
        if '.' in symbol:
            return symbol
        
        # 根据代码判断交易所
        if clean_symbol.startswith('6'):
            return f"{clean_symbol}.SH"  # 上交所
        elif clean_symbol.startswith(('0', '3')):
            return f"{clean_symbol}.SZ"  # 深交所
        elif clean_symbol.startswith(('4', '8')):
            return f"{clean_symbol}.BJ"  # 北交所
        else:
            return f"{clean_symbol}.SH"  # 默认上交所
    
    def _fetch_tushare_pro_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """使用Tushare Pro获取数据"""
        # 转换日期格式
        start_date_ts = start_date.replace('-', '') if start_date else None
        end_date_ts = end_date.replace('-', '') if end_date else None
        
        # 获取日线数据
        df = self.ts_pro.daily(
            ts_code=symbol,
            start_date=start_date_ts,
            end_date=end_date_ts
        )
        
        if df is not None and not df.empty:
            # 重命名列
            df = df.rename(columns={
                'trade_date': 'date',
                'vol': 'volume',
                'amount': 'turnover'
            })
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
        
        return df
    
    def _fetch_akshare_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """使用AKShare获取数据"""
        symbol_code = symbol.split('.')[0]
        
        # 转换日期格式
        start_date_ak = start_date.replace('-', '') if start_date else None
        end_date_ak = end_date.replace('-', '') if end_date else None
        
        # 获取历史数据
        df = self.ak.stock_zh_a_hist(
            symbol=symbol_code,
            period="daily",
            start_date=start_date_ak,
            end_date=end_date_ak,
            adjust=""
        )
        
        if df is not None and not df.empty:
            # 重命名列
            df = df.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '最高': 'high',
                '最低': 'low',
                '收盘': 'close',
                '成交量': 'volume',
                '成交额': 'turnover'
            })
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
        
        return df
    
    def _fetch_efinance_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """使用东方财富获取数据"""
        symbol_code = symbol.split('.')[0]
        
        # 获取历史数据
        df = self.ef.stock.get_quote_history(symbol_code, klt=101)  # 日K线
        
        if df is not None and not df.empty:
            # 重命名列
            df = df.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '最高': 'high',
                '最低': 'low',
                '收盘': 'close',
                '成交量': 'volume',
                '成交额': 'turnover'
            })
            
            df['date'] = pd.to_datetime(df['date'])
            
            # 过滤日期范围
            if start_date:
                df = df[df['date'] >= pd.to_datetime(start_date)]
            if end_date:
                df = df[df['date'] <= pd.to_datetime(end_date)]
            
            df = df.sort_values('date')
        
        return df
    
    def _fetch_efinance_quotes(self, symbols: List[str]) -> pd.DataFrame:
        """使用东方财富获取实时报价"""
        data = []
        
        for symbol in symbols:
            try:
                symbol_code = symbol.split('.')[0] if '.' in symbol else symbol
                
                # 获取实时数据
                quote = self.ef.stock.get_quote_history(symbol_code, klt=1, count=1)
                
                if quote is not None and not quote.empty:
                    latest = quote.iloc[-1]
                    
                    data.append({
                        'symbol': symbol,
                        'name': latest.get('股票名称', ''),
                        'price': latest.get('收盘', 0),
                        'change': latest.get('涨跌额', 0),
                        'change_percent': latest.get('涨跌幅', 0),
                        'volume': latest.get('成交量', 0),
                        'turnover': latest.get('成交额', 0),
                        'high': latest.get('最高', 0),
                        'low': latest.get('最低', 0),
                        'open': latest.get('开盘', 0)
                    })
                    
            except Exception as e:
                print(f"⚠️ 获取 {symbol} 报价失败: {e}")
                continue
        
        return pd.DataFrame(data) if data else None
    
    def _fetch_akshare_quotes(self, symbols: List[str]) -> pd.DataFrame:
        """使用AKShare获取实时报价"""
        try:
            # 获取所有A股实时数据
            all_quotes = self.ak.stock_zh_a_spot_em()
            
            if all_quotes is None or all_quotes.empty:
                return None
            
            data = []
            for symbol in symbols:
                symbol_code = symbol.split('.')[0] if '.' in symbol else symbol
                
                # 查找对应股票
                stock_data = all_quotes[all_quotes['代码'] == symbol_code]
                
                if not stock_data.empty:
                    row = stock_data.iloc[0]
                    
                    data.append({
                        'symbol': symbol,
                        'name': row.get('名称', ''),
                        'price': row.get('最新价', 0),
                        'change': row.get('涨跌额', 0),
                        'change_percent': row.get('涨跌幅', 0),
                        'volume': row.get('成交量', 0),
                        'turnover': row.get('成交额', 0),
                        'high': row.get('最高', 0),
                        'low': row.get('最低', 0),
                        'open': row.get('今开', 0)
                    })
            
            return pd.DataFrame(data) if data else None
            
        except Exception as e:
            print(f"⚠️ AKShare获取报价失败: {e}")
            return None
    
    def _standardize_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化数据格式"""
        # 确保必要的列存在
        required_columns = ['date', 'open', 'high', 'low', 'close', 'volume']
        
        for col in required_columns:
            if col not in df.columns:
                if col == 'volume':
                    df[col] = 0
                else:
                    df[col] = np.nan
        
        # 设置日期为索引
        if 'date' in df.columns:
            df.set_index('date', inplace=True)
        
        # 确保数值类型
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        return df


# OpenBB Provider 集成 (如果OpenBB可用)
if OPENBB_AVAILABLE:
    class AStockProvider(Provider):
        """A股数据提供商"""
        
        website: str = "https://tushare.pro"
        description: str = "A股数据提供商，集成Tushare Pro、AKShare、东方财富"
        
        fetcher_dict = {
            "EquityHistorical": "AStockHistoricalFetcher",
            "EquityQuote": "AStockQuoteFetcher",
        }
    
    class AStockHistoricalFetcher(Fetcher):
        """A股历史数据获取器"""
        
        @staticmethod
        def transform_query(params: Dict[str, Any]) -> Dict[str, Any]:
            return params
        
        @staticmethod
        def extract_data(
            query: Dict[str, Any],
            credentials: Optional[Dict[str, str]] = None,
            **kwargs: Any,
        ) -> List[Dict]:
            """提取历史数据"""
            fetcher = AStockDataFetcher(
                tushare_token=credentials.get("tushare_token") if credentials else None
            )
            
            df = fetcher.fetch_equity_historical(
                symbol=query["symbol"],
                start_date=query.get("start_date"),
                end_date=query.get("end_date"),
                provider=query.get("provider", "auto")
            )
            
            if df is not None:
                return df.reset_index().to_dict('records')
            return []
        
        @staticmethod
        def transform_data(
            query: Dict[str, Any], data: List[Dict], **kwargs: Any
        ) -> List[EquityHistoricalData]:
            """转换数据格式"""
            return [EquityHistoricalData(**item) for item in data]


def test_astock_provider():
    """测试A股数据提供商"""
    print("🧪 测试A股数据提供商")
    print("=" * 50)
    
    # 初始化获取器
    fetcher = AStockDataFetcher()
    
    # 测试历史数据
    print("\n📈 测试历史数据")
    symbols = ['000001', '600519', '000858']
    
    for symbol in symbols:
        data = fetcher.fetch_equity_historical(
            symbol=symbol,
            start_date='2024-01-01',
            end_date='2024-12-31'
        )
        
        if data is not None:
            print(f"{symbol}: {len(data)} 条数据")
            print(data.tail(2))
        print()
    
    # 测试实时报价
    print("\n💰 测试实时报价")
    quotes = fetcher.fetch_equity_quote(['000001', '600519'])
    
    if quotes is not None:
        print("实时报价:")
        print(quotes)
    
    print("\n🎉 测试完成!")


if __name__ == "__main__":
    test_astock_provider()
