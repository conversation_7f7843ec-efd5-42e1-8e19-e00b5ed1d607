{"tu": {"stattype_code": {"10100": "Number of persons (in thousands)", "10101": "Average hours per day", "20100": "Number of participants in an activity on an average day (in thousands)", "20101": "Average hours per day for participants in an activity", "30105": "Percent of population engaged in activity on an average day", "30107": "Number of participants in an activity on an average day (in thousands)", "50212": "Percent - Preprocessing response rate", "50213": "Percent - Postprocessing response rate"}, "datays_code": {"01": "1 year of ATUS data"}, "sex_code": {"0": "Both sexes", "1": "Men", "2": "Women"}, "region_code": {"00": null}, "lfstat_code": {"00": null, "01": "Employed", "04": "Employed full time", "07": "Employed part time", "20": "Not Employed"}, "educ_code": {"00": "All education levels", "30": "Less than a high school diploma", "31": "High school graduates, no college", "33": "Bachelor's degree and higher", "35": "Some college or associate degree", "37": "Bachelor's degree only", "38": "Advanced degree"}, "maritlstat_code": {"00": null, "01": "Married, spouse present in household", "04": "Other marital statuses (not married or married with a spouse living elsewhere)", "07": "Spouse or unmarried partner present in household", "08": "Lives alone"}, "age_code": {"000": "15 years and over", "015": "18 years and over", "020": "20 to 24 years", "028": "25 years and over", "031": "25 to 34 years", "033": "25 to 54 years", "038": "35 to 44 years", "042": "45 to 54 years", "049": "55 to 64 years", "065": "65 years and over", "078": "75 years and over", "102": "15 to 19 years", "103": "15 to 24 years", "113": "65 to 74 years"}, "orig_code": {"00": null, "01": "Hispanic or Latino"}, "race_code": {"00": "All races", "01": "White", "03": "Black or African American", "04": "Asian"}, "mjcow_code": {"00": null, "01": "Wage and salary workers", "20": "Self-employed, unincorporated"}, "nmet_code": {"0": null}, "where_code": {"00": null, "01": "Home or yard", "02": "Workplace"}, "sjmj_code": {"0": null, "1": "Multiple jobholders", "6": "Single jobholders"}, "timeday_code": {"00": null, "01": "4 AM to 5 AM", "02": "5 AM to 6 AM", "03": "6 AM to 7 AM", "04": "7 AM to 8 AM", "05": "8 AM to 9 AM", "06": "9 AM to 10 AM", "07": "10 AM to 11 AM", "08": "11 AM to 12 Noon", "09": "12 Noon to 1 PM", "10": "1 PM to 2 PM", "11": "2 PM to 3 PM", "12": "3 PM to 4 PM", "13": "4 PM to 5 PM", "14": "5 PM to 6 PM", "15": "6 PM to 7 PM", "16": "7 PM to 8 PM", "17": "8 PM to 9 PM", "18": "9 PM to 10 PM", "19": "10 PM to 11 PM", "20": "11 PM to 12 Midnight", "21": "12 Midnight to 1 AM", "22": "1 AM to 2 AM", "23": "2 AM to 3 AM", "24": "3 AM to 4 AM", "41": "4 AM", "42": "5 AM", "43": "6 AM", "44": "7 AM", "45": "8 AM", "46": "9 AM", "47": "10 AM", "48": "11 AM", "49": "12 PM", "50": "1 PM", "51": "2 PM", "52": "3 PM", "53": "4 PM", "54": "5 PM", "55": "6 PM", "56": "7 PM", "57": "8 PM", "58": "9 PM", "59": "10 PM", "60": "11 PM", "61": "12 AM", "62": "1 AM", "63": "2 AM", "64": "3 AM"}, "actcode_code": {"000000": "Total, all activities", "010100": "Sleeping", "010200": "Grooming", "010300": "Health-related self care", "010400": "Personal activities", "020100": "Housework", "020101": "Interior cleaning", "020102": "<PERSON><PERSON><PERSON>", "020104": "Storing interior household items, including food", "020200": "Food preparation and cleanup", "020201": "Food and drink preparation", "020203": "Kitchen and food cleanup", "020300": "Interior maintenance, repair, and decoration", "020400": "Exterior maintenance, repair, and decoration", "020500": "Lawn and garden care", "020600": "Animal and pet care", "020601": "Care for animals and pets (not veterinary care)", "020602": "Walking, exercising, and playing with animals", "020700": "Vehicle care (by self)", "020800": "Appliance, tool, and toy maintenance (by self)", "020901": "Financial management", "020902": "Household and personal organization and planning", "020903": "Household and personal mail and messages", "020904": "Household and personal e-mail and messages", "030100": "Caring for and helping household children (except activities related to education and health)", "030101": "Physical care for household children", "030102": "Reading to/with household children", "030103": "Playing with household children, not sports", "030106": "Talking with/listening to household children", "030109": "Looking after household children (as a primary activity)", "030110": "Attending household children's events", "030200": "Activities related to household children's education", "030201": "Helping household children with homework", "030300": "Activities related to household children's health", "030400": "Caring for household adults", "030401": "Physical care for household adults", "030500": "Helping household adults", "040400": "Caring for nonhousehold adults", "040500": "Helping nonhousehold adults", "050100": "Working", "050101": "Working, main job", "050200": "Work-related activities", "050300": "Other income-generating activities", "050400": "Job search and interviewing", "060100": "Attending class", "060101": "Taking class for degree, certification, or licensure", "060300": "Homework and research", "070000": "Consumer goods purchases", "070101": "Grocery shopping", "080000": "Professional and personal care services", "080200": "Financial services and banking", "080400": "Medical and care services", "080500": "Personal care services", "090000": "Household services", "090200": "Home maintenance, repair, decoration, and construction (not done by self)", "090500": "Vehicle maintenance and repair services (not done by self)", "100200": "Civic obligations and participation", "110100": "Eating and drinking", "120000": "Socializing, relaxing, and leisure", "120100": "Socializing and communicating (except social events)", "120200": "Attending or hosting social events", "120300": "Relaxing and leisure", "120301": "Relaxing and thinking", "120307": "Playing games", "120308": "Computer use for leisure (excluding games)", "120312": "Reading for personal interest", "120400": "Arts and entertainment (other than sports)", "130000": "Sports, exercise, and recreation", "130100": "Participating in sports, exercise, or recreation", "130131": "Walking", "130200": "Attending sporting or recreational events", "140000": "Religious and spiritual activities", "140101": "Attending religious services", "140102": "Participation in religious practices", "150000": "Volunteer activities", "150100": "Administrative and support activities (volunteering)", "150200": "Social service and care activities (volunteering)", "150300": "Indoor and outdoor maintenance, building, and cleanup activities (volunteering)", "150400": "Participating in performance and cultural activities (volunteering)", "150500": "Attending meetings, conferences, and training (volunteering)", "160100": "Telephone calls (to or from)", "180000": "Traveling", "180100": "Travel related to personal care", "180200": "Travel related to household activities", "180300": "Travel related to caring for and helping household members", "180400": "Travel related to caring for and helping nonhousehold members", "180500": "Travel related to work", "180600": "Travel related to education", "181100": "Travel related to eating and drinking", "181600": "Travel related to telephone calls", "600001": "Personal care activities (includes travel)", "600003": "Household activities (includes travel)", "600005": "Household management", "600007": "Caring for and helping household members (includes travel)", "600008": "Caring for and helping household children under 18", "600009": "Caring for and helping household adults", "600010": "Caring for and helping nonhousehold members (includes travel)", "600011": "Caring for and helping nonhousehold children", "600012": "Caring for and helping nonhousehold adults", "600013": "Working and work-related activities (includes travel)", "600016": "Educational activities (includes travel)", "600018": "Purchasing goods and services (includes travel)", "600021": "Travel related to purchasing goods and services", "600022": "Eating and drinking (includes travel)", "600023": "Leisure and sports (includes travel)", "600024": "Socializing and communicating", "600025": "Watching TV", "600027": "Participating in sports, exercise, and recreation (includes related waiting and security)", "600029": "Travel related to leisure and sports", "600030": "Organizational, civic, and religious activities", "600032": "Volunteering (organizational and civic activities)", "600033": "Telephone calls, mail, and e-mail (includes travel)", "600034": "Other activities, not elsewhere classified (includes misc. travel and data codes)", "600039": "Doing hobbies or playing with household children", "600043": "Caring for household children as a primary activity (includes travel)", "600057": "Caring for household children under 13 as a secondary activity, total", "600058": "Playing games and computer use for leisure", "600059": "Other leisure and sports activities, including travel", "600060": "Other activities", "600066": "Travel related to organizational, civic, and religious activities", "600067": "Travel related to care of household children", "600069": "Government services", "600072": "Household and personal messages", "600083": "Caring for and helping others", "600085": "Awake time", "700001": "Talking with/listening to household children", "700171": "Other childcare activities"}, "industry_code": {"0000": null}, "occ_code": {"0000": null, "0008": "Management, business, and financial operations occupations", "0998": "Professional and related occupations", "3597": "Service occupations", "4699": "Sales and related occupations", "4999": "Office and administrative support occupations", "5999": "Farming, fishing, and forestry occupations", "6199": "Construction and extraction occupations", "6999": "Installation, maintenance, and repair occupations", "7699": "Production occupations", "8999": "Transportation and material moving occupations"}, "prhhchild_code": {"00": "All persons", "01": "No household children under age 18", "05": "Youngest household child age 6-12", "10": "Youngest household child age 13-17", "12": "Household child under age 13", "13": "Household child under age 18", "14": "Household child under age 6", "15": "Youngest household child age 6-17"}, "earn_code": {"00": "All persons", "01": "Weekly earnings less than or equal to the 25th percentile", "02": "Weekly earnings greater than 25th percentile and less than or equal to 50th percentile", "03": "Weekly earnings greater than 50th percentile and less than or equal to 75th percentile", "04": "Weekly earnings greater than the 75th percentile"}, "disability_code": {"00": null}, "who_code": {"00": null, "01": "Alone", "02": "Others present (not alone)", "03": "Spouse only", "24": "Family (living in HH)", "29": "WHO data not available", "35": "Co-workers, colleagues, clients", "36": "Own household child under age 18", "40": "Household members", "41": "Nonhousehold members", "42": "Family (not living in household)", "43": "Friends, neighbors, or acquaintances", "44": "Other, nonhousehold members", "45": "Nonfamily (living in household)"}, "hhnscc03_code": {"00": null, "06": "Secondary childcare of household child under 13"}, "schenr_code": {"0": null}, "prownhhchild_code": {"00": "All persons", "01": "No own household children under age 18", "05": "Youngest own household child age 6-12", "10": "Youngest own household child age 13-17", "12": "Own household child under age 13", "13": "Own household child under age 18", "14": "Own household child under age 6", "15": "Youngest own household child age 6-17"}, "work_code": {"0": null, "1": "On days worked", "3": "On days worked at main job"}, "elnum_code": {"00": "All"}, "ecage_code": {"000": "Persons of all ages"}, "elfreq_code": {"0": "All frequencies"}, "eldur_code": {"00": "All durations of care"}, "elwho_code": {"00": null}, "ecytd_code": {"0": "All days"}, "elder_code": {"0": "All persons"}, "lfstatw_code": {"000": "All persons", "010": "Employed", "011": "Employed and on days worked", "013": "Employed and on days worked at main job", "040": "Employed full time", "041": "Employed full time and on days worked", "070": "Employed part time", "071": "Employed part time and on days worked", "200": "Not Employed"}, "pertype_code": {"00": "All days", "16": "Weekend days and holidays", "18": "Weekdays", "19": "Nonholiday weekdays"}, "footnote_code": {"A": "Includes naps and spells of sleeplessness.", "B": "Individuals may have worked at more than one location.", "C": "See Footnote C on <a href=\"/tus/footnote.htm#c\" target=\"new\">www.bls.gov/tus/footnote.htm</a>.", "D": "See Footnote D on <a href=\"/tus/footnote.htm#d\" target=\"new\">www.bls.gov/tus/footnote.htm</a>.", "F": "See Footnote F on <a href=\"/tus/footnote.htm#f\" target=\"new\">www.bls.gov/tus/footnote.htm</a>.", "G": "See Footnote G on <a href=\"/tus/footnote.htm#g\" target=\"new\">www.bls.gov/tus/footnote.htm</a>.", "H": "Persons of Hispanic or Latino ethnicity may be of any race.", "I": "Includes persons with a high school diploma or equivalent.", "J": "Includes persons with bachelor's, master's, professional, and doctoral degrees.", "K": "Excludes household and personal mail, e-mail, and messages.", "M": "See Footnote M on <a href=\"/tus/footnote.htm#m\" target=\"new\">www.bls.gov/tus/footnote.htm</a>.", "N": "See Footnote N on <a href=\"/tus/footnote.htm#n\" target=\"new\">www.bls.gov/tus/footnote.htm</a>.", "S": "Estimate is suppressed because it does not meet the American Time Use Survey publication standards.", "Z": "Estimate is approximately zero."}}}