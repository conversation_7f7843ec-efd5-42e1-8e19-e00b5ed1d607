#!/usr/bin/env python3
"""
OpenBB Python客户端使用示例
展示如何正确使用客户端连接API服务器
"""

from openbb_client import OpenBBClient
import time

def main():
    print("🚀 OpenBB Python客户端使用示例")
    print("=" * 50)
    
    # 创建客户端实例
    client = OpenBBClient()
    
    # 示例1: 获取股票实时报价 (这个已经工作正常)
    print("\n💰 获取股票实时报价")
    print("-" * 30)
    
    symbols = ["AAPL", "MSFT", "GOOGL"]
    for symbol in symbols:
        quote = client.get_equity_quote(symbol)
        if quote:
            price = quote.get('price', 'N/A')
            print(f"{symbol}: ${price}")
        time.sleep(1)  # 避免API限制
    
    # 示例2: 获取可用端点
    print("\n📋 查看可用API端点")
    print("-" * 30)
    client.get_available_endpoints()
    
    # 示例3: 手动测试API
    print("\n🔧 手动API测试")
    print("-" * 30)
    
    # 测试健康检查的不同路径
    health_endpoints = ["/health", "/api/health", "/status"]
    for endpoint in health_endpoints:
        result = client._make_request(endpoint)
        if result:
            print(f"✅ {endpoint} 工作正常")
            break
        else:
            print(f"❌ {endpoint} 不可用")
    
    # 示例4: 尝试不同的股票数据参数
    print("\n📈 尝试获取股票数据")
    print("-" * 30)
    
    # 尝试不同的参数组合
    params_list = [
        {"symbol": "AAPL", "provider": "yfinance"},
        {"symbol": "AAPL"},  # 不指定provider
    ]
    
    for params in params_list:
        print(f"尝试参数: {params}")
        data = client._make_request("/api/v1/equity/price/historical", params)
        if data:
            print(f"✅ 成功获取数据")
            break
        else:
            print(f"❌ 参数无效")
    
    print("\n" + "=" * 50)
    print("✅ Python客户端测试完成！")
    print("\n💡 客户端已经可以正常工作，主要功能:")
    print("- ✅ 连接到API服务器")
    print("- ✅ 获取股票实时报价")
    print("- ✅ 发送API请求")
    print("- ⚠️ 部分端点需要调整参数")
    
    print("\n🔧 下一步建议:")
    print("1. 访问 http://127.0.0.1:6900/docs 查看准确的API文档")
    print("2. 根据文档调整API参数")
    print("3. 使用client对象进行自定义查询")

if __name__ == "__main__":
    main()
