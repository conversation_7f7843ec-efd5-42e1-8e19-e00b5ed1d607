from __future__ import annotations

from dataclasses import dataclass, field
from typing import Optional
import pandas as pd

from ..providers.tushare_provider import TushareProvider
from ..storage.db import db_session
from ..storage.schema import PriceDaily


@dataclass
class EquityPrice:
    def historical(self, symbol: str, start: Optional[str] = None, end: Optional[str] = None) -> pd.DataFrame:
        provider = TushareProvider()
        df = provider.daily(symbol=symbol, start=start, end=end)

        if not df.empty:
            with db_session() as session:
                for _, row in df.iterrows():
                    session.merge(
                        PriceDaily(
                            symbol=symbol,
                            trade_date=row.trade_date,
                            open=float(row.open) if row.open is not None else None,
                            high=float(row.high) if row.high is not None else None,
                            low=float(row.low) if row.low is not None else None,
                            close=float(row.close) if row.close is not None else None,
                            volume=float(row.volume) if row.volume is not None else None,
                            provider=row.provider,
                        )
                    )
        return df


@dataclass
class Equity:
    price: EquityPrice = field(default_factory=EquityPrice)


@dataclass
class OBB_CN:
    equity: Equity = field(default_factory=Equity)


obb = OBB_CN()

