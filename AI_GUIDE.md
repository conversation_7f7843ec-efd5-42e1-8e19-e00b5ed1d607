## OpenBB-CN AI 开发说明（面向编程智能体）

本文件为“编程AI/代理”准备，提供最小而精确的上下文：目标、接口契约、运行命令、扩展点、任务清单与验收标准。请严格遵循模块化与清晰边界的设计[[memory:4806727]]。

## 🚨 **数据准确性与功能完整性规范**

### **重要变更：数据源完全迁移**
- ✅ **已完全迁移到AData开源数据源**（v0.2.0+）
- ✅ **支持5738只A股，无积分限制**
- ✅ **多数据源聚合**：同花顺、东方财富、百度股市通等
- ❌ **已移除Tushare依赖**，不再需要TUSHARE_TOKEN

### **🚨 端口使用规则（严格遵守）**
- ✅ **固定端口6900**：系统必须使用6900端口，不允许更改
- ✅ **端口占用处理**：如果6900端口被占用，说明服务已启动，需要先关闭现有服务
- ❌ **禁止使用其他端口**：不得使用8000、8001等其他端口
- ❌ **禁止端口冲突**：启动前必须确保6900端口可用

### **严禁的错误**
- ❌ **使用错误的股票名称或代码**
- ❌ **AI分析基于错误数据**
- ❌ **数值计算错误**
- ❌ **功能存在但页面未显示**
- ❌ **估值倍数参数缺失**
- ❌ **尝试使用已删除的tushare_provider**
- ❌ **使用6900以外的端口启动服务**
- ❌ **端口占用时强制启动或更换端口**

### **必须验证的功能**
- ✅ **搜索功能**: 6位代码自动转换，显示去除.SH/.SZ后缀
- ✅ **量化算法**: GA优化、PSO选股必须在页面显示
- ✅ **智能模拟炒股**: 集成AI经验学习的完整交易系统
- ✅ **AI经验积累**: 从每次交易中学习，优化策略建议
- ✅ **自研指标**: VCM/TEI/LVR/API/BLCE/MTR必须正确显示
- ✅ **估值倍数**: PE/PB/PS/市值等必须完整显示
- ✅ **AI分析**: 基于历史交易经验的智能诊断和建议

### **AI经验学习系统核心特性**
- 🧠 **交易经验记录**: 每次买卖都记录市场条件、技术指标、策略类型
- 📊 **策略性能分析**: 统计各策略的成功率、平均收益、夏普比率
- 🎯 **智能建议生成**: 基于历史相似情况给出买入/卖出/观望建议
- 🔄 **算法自优化**: 根据实战结果调整PSO和GA参数
- 📈 **风险评估**: 基于历史数据评估投资风险等级
- 🏆 **经验置信度**: 样本数量越多，AI建议越准确

### 1) 目标与范围
- 目标：提供中国市场（A股/港股/期货）的统一数据访问平台，暴露 Python SDK 与 REST API，支持分钟/分时/盘口的后续扩展，数据合规可用。
- MVP 范围：
  - 日线历史行情（A股，基于 AData开源数据）。
  - PostgreSQL 缓存/快照（唯一索引避免重复写入）。
  - REST API 与 Python SDK 一致的查询语义。
  - 支持5738只A股，无积分限制，多数据源聚合。

### 2) 关键约束
- 必须模块化、可插拔 Provider，清晰接口与职责边界[[memory:4806727]]。
- 合规数据源优先（商业授权/明确许可），不接受“仅限学习用途”的抓取来源。
- 生产运行在 Docker；开发在本地虚拟环境或 Cursor 内置环境。

### 3) 目录结构（核心）
- `openbb_cn/api/app.py`：FastAPI 入口（ASGI 应用）。
- `openbb_cn/sdk/obb.py`：Python SDK（`obb.equity.price.historical`）。
- `openbb_cn/providers/adata_provider.py`：AData Provider 实现（唯一数据源）。
- `openbb_cn/storage/schema.py`：SQLAlchemy 数据表结构（`price_daily`）。
- `openbb_cn/storage/db.py`：DB 初始化与会话管理。
- `openbb_cn/config.py`：环境变量与配置装载（`.env`）。
- `openbb_cn/cli.py`：CLI（启动 API）。
- `openbb_cn/ai/llm_analyzer.py`：AI分析模块（阿里云百炼）。
- `.env`：运行时配置（AI密钥、数据库、端口）。
- `Dockerfile`、`docker-compose.yml`：容器化部署。

### 4) 运行方式（命令）
- 本地（Windows PowerShell）：
  ```
  python -m venv .venv
  .\.venv\Scripts\Activate.ps1
  pip install -r requirements.txt
  # 可选：docker compose up -d db （或使用已有 PostgreSQL）
  uvicorn openbb_cn.api.app:app --host 0.0.0.0 --port 6900 --reload  # 必须使用6900端口
  ```
- Docker：
  ```
  docker compose up -d --build
  # API: http://127.0.0.1:6900/docs （固定6900端口）
  ```
- CLI：
  ```
  python -m openbb_cn.cli api --reload
  # 或安装后：openbb-cn api
  ```

#### 4.1) 开发 / 生产 环境分层（非常重要）
- 目录与脚本：
  - 开发环境：`envs/dev/`
    - `docker-compose.dev.yml`：挂载源码、开启 `--reload`，热更新
    - `start.ps1` / `stop.ps1`：一键启动/停止（默认使用项目根 `.env`）
    - 运行：
      ```powershell
      .\envs\dev\start.ps1 -Rebuild   # 首次或改动依赖建议加 -Rebuild
      .\envs\dev\stop.ps1
      ```
  - 生产环境：`envs/prod/`
    - `docker-compose.prod.yml`：不挂载源码，使用镜像产物
    - `start.ps1` / `stop.ps1`：一键部署/回收
    - 运行：
      ```powershell
      # 请先在项目根准备 .env（含生产凭据，见下文 5) 环境变量）
      .\envs\prod\start.ps1 -Rebuild
      .\envs\prod\stop.ps1
      ```
    - 生产部署说明（Docker 上运行）：
      - 前置条件：已安装 Docker（Linux 服务器或 Docker Desktop）。
      - 组件：`api`（FastAPI）与 `db`（PostgreSQL）两个容器，通过同一 `docker-compose.prod.yml` 启动。
      - 监听端口：对外暴露 `6900`（**固定端口，不允许修改**）。
      - 数据持久化：数据库数据保存在 `db_data_prod` 卷中，容器重启不丢失。
      - 环境变量：从项目根 `.env` 注入（含 `BAILIAN_API_KEY`、`POSTGRES_PASSWORD` 等）。
      - 健康检查与监控：建议在上层加 Nginx/ALB 做反向代理与 HTTPS，探活可用 `GET /` 或 `GET /docs`。
      - 灰度/回滚：使用 `-Rebuild` 生成新镜像，验证后替换；异常时 `stop.ps1` 回收并回滚到上一镜像版本。
- 说明：
  - 需要安装 Docker Desktop 才能使用上述 compose 脚本；如未安装，请走“本地（Windows PowerShell）”方式启动。
  - 开发环境将源码挂载到容器，保存文件即自动生效；生产环境不挂载源码，镜像构建后再启动。
  - 生产数据库密码需来自 `.env` 的 `POSTGRES_PASSWORD`，compose 会将 `DATABASE_URL` 指向容器内数据库服务 `db`。

### 5) 环境变量（.env）
- **数据源配置**：
  - 已移除 `TUSHARE_TOKEN`（不再需要）
  - AData为开源免费数据源，无需配置密钥
- `DATABASE_URL`：默认 `postgresql+psycopg2://openbb:openbb@localhost:5432/openbb_cn`。
- `APP_HOST`：默认 `0.0.0.0`。
- `APP_PORT`：**固定 `6900`**（不允许更改，系统标准端口）。
- 阿里云百炼（通义）AI：
  - `BAILIAN_API_KEY`：百炼 API Key（获取：`https://bailian.console.aliyun.com/?tab=model#/api-key`）
  - `BAILIAN_BASE_URL`：默认 `https://dashscope.aliyuncs.com/compatible-mode/v1`
  - `BAILIAN_MODEL`：默认 `qwen-plus`
- 生产数据库（仅生产）：
  - `POSTGRES_PASSWORD`：生产数据库密码（compose 使用）

### 6.1) 账户与权限（Admin 管理其他账号）
- 模型：`users`（username、role、active、scopes、symbols、markets、rate_limit、expires_at、api_key）
- 接口：
  - 注册：`POST /auth/register`（admin或开放注册）
  - 登录：`POST /auth/login` → 返回 `token`（作为 `X-API-KEY`）
  - 列表：`GET /admin/users`（需 admin + `X-API-KEY`）
  - 后续：`PATCH /admin/users/{id}`、重置密码/Key（待实现）
- 前端：`/app` 顶栏“管理后台”按钮进入 `/admin`（简版），支持登录、注册与用户列表。

### 6) REST API 参考（/app 前端即基于这些接口）

- 通用说明
  - Base URL：`http://127.0.0.1:6900`（**固定6900端口，不可更改**）
  - 鉴权：当前无，需要时可加 Header Token
  - 返回格式：JSON

- 健康检查
  - GET `/healthz`
  - 200 → `{ "status": "ok" }`

- 搜索（基于AData，无限制）
  - GET `/search?query=任意字符串`
  - 返回：`[{ ts_code, name, area, industry, list_date }]`（最多50个结果，按相关性排序）
  - 支持精确匹配、前缀匹配、模糊匹配，覆盖5738只A股

- 行情（历史）
  - GET `/equity/price/historical?symbol=600519.SH&start=YYYYMMDD&end=YYYYMMDD`
  - 返回：`{ symbol, rows }`（写库即缓存）
  - GET `/equity/price/historical/data?symbol=600519.SH&start=YYYYMMDD&end=YYYYMMDD`
  - 返回：`[{ trade_date, open, high, low, close, volume, provider }]`

- 估值倍数
  - GET `/equity/basic/ratios?symbol=600519.SH&start=YYYYMMDD&end=YYYYMMDD`
  - 返回：`[{ trade_date, pe, pb, ps }]`

- 简要指标（均线/收益）
  - GET `/equity/price/indicators?symbol=600519.SH`
  - 返回：`{ symbol, size, latest: { close, ma5, ma20, ma60, ret5, ret20 } }`

- 自研指标
  - GET `/indicator/vcm?symbol=600519.SH&window=20&decay=0.85` → `[{ t, v }]`
  - GET `/indicator/tei?symbol=600519.SH&window=20` → `[{ t, v }]`
  - GET `/indicator/lvr?symbol=600519.SH&window=20&atr_window=14` → `[{ t, v }]`
  - GET `/indicator/api?symbol=600519.SH&window=20&gamma=0.2` → `[{ t, v }]`
  - GET `/indicator/blce?symbol=600519.SH&window=20&alpha=0.6&beta=0.8&atr_window=14` → `[{ t, energy, dir_bias, prob_up }]`
  - GET `/indicator/mtr?symbol=600519.SH` → `[{ t, v }]`

- 量化优化（策略与选股）
  - GET `/quant/ga?symbol=600519.SH&start=YYYYMMDD&end=YYYYMMDD` → 遗传算法优化因子权重，返回 `{ best, fitness }`
  - POST `/quant/pso` with body `{ "symbols":["600519.SH","000001.SZ"], "start":"YYYYMMDD", "end":"YYYYMMDD", "top_n":10 }` → 粒子群选股，返回 `{ selected, weights, fitness }`

- 模拟炒股回测
  - POST `/sim/backtest` with body `{ "start":"YYYYMMDD", "end":"YYYYMMDD", "symbols":["600519.SH","000001.SZ"], "initial_cash":100000 }` → 基于PSO选股的等权调仓回测，返回 `{ initial_cash, final_value, total_return, trades }`

- 智能交易系统（AI经验学习）
  - GET `/smart/account` → 获取智能交易账户信息，返回 `{ cash, market_value, total_value, positions, recent_trades, strategy_performance }`
  - POST `/smart/buy` with body `{ "symbol":"600519.SH", "amount":10000, "strategy":"manual" }` → 智能买入，返回交易结果和AI经验记录
  - POST `/smart/sell` with body `{ "symbol":"600519.SH", "quantity":100, "strategy":"manual" }` → 智能卖出，返回交易结果和盈亏分析
  - POST `/smart/rebalance` → 基于PSO算法的智能调仓，返回调仓结果
  - POST `/smart/config` with body `{ "pso_enabled":true, "ga_enabled":true, "rebalance_freq":"W-FRI" }` → 配置智能策略开关
  - GET `/smart/advice/{symbol}` → 基于历史交易经验的AI投资建议，返回 `{ action, confidence, reason, suggested_strategy, risk_level }`

- AI 分析（阿里云百炼，增强数据支撑）
  - POST `/ai/analyze`
  - Body：
    ```json
    {
      "symbol": "600519.SH",
      "indicators": {
        "latest": {"vcm": {"value": 0.6, "trend": "up", "strength": 0.6}, "rsi": {"value": 45.67, "trend": "stable"}},
        "summary": {"categories": {"momentum": ["rsi", "vcm"], "trend": ["macd"]}},
        "count": 12,
        "data_quality": "excellent"
      },
      "price_data": {"current_price": 1700, "change_percent": "2.5", "volume": 1234567},
      "news": {"data": [{"title": "公司财报", "summary": "业绩增长", "sentiment": "positive"}], "count": 5},
      "prompt": "基于完整数据进行深度分析，给出明确的买入/卖出/持仓建议"
    }
    ```
  - 返回：`{ symbol, text }`（包含结构化的投资建议：买入/卖出/持仓条件和策略）
  - 需在 `.env` 配置 `BAILIAN_API_KEY`等参数；参考：`https://bailian.console.aliyun.com/?tab=api#/api/?type=model&url=2712576`

- 典型 cURL
  - 拉取数据并画图前端即用：
    ```bash
    curl "http://127.0.0.1:6900/equity/price/historical/data?symbol=600519.SH&start=20240101&end=20240630"
    ```
  - 获取自研指标：
    ```bash
    curl "http://127.0.0.1:6900/indicator/blce?symbol=600519.SH"
    ```
  - AI 分析：
    ```bash
    curl -X POST http://127.0.0.1:6900/ai/analyze \
      -H "Content-Type: application/json" \
      -d '{
        "symbol":"600519.SH",
        "indicators":{"custom":{"vcm":0.6,"tei":0.25,"lvr":0.3,"api":0.05,"mtr":0.55,"blce_prob_up":0.68}},
        "temperature":0.3,
        "top_p":0.9,
        "max_tokens":800
      }'
    ```

### 7) SDK 契约
- 导入：
  ```python
  from openbb_cn.sdk.obb import obb
  ```
- `obb.equity.price.historical(symbol: str, start: str|None, end: str|None) -> pandas.DataFrame`
  - 行为：
    1) 从 Provider 拉取数据（当前 `TushareProvider.daily`）。
    2) 写入 PostgreSQL（唯一键：`symbol + trade_date`）。
    3) 返回 `DataFrame`（列：`trade_date, open, high, low, close, volume, provider`）。

### 8) Provider 接口与扩展点
- 目标：统一 Provider 协议，支持 A股/港股/期货/分钟/盘口扩展。
- 现有实现：
  - `openbb_cn.providers.adata_provider.AdataProvider`（唯一数据源）
    - `daily(symbol: str, start: str|None, end: str|None) -> pandas.DataFrame`
    - `search(query: str, limit: int = 50) -> pandas.DataFrame`
    - `get_current_market(symbols: List[str]) -> pandas.DataFrame`
    - 支持5738只A股，无API限制，多数据源聚合（同花顺、东方财富等）
- 待抽象（建议接口）：
  ```python
  class EquityPriceProviderProtocol(Protocol):
      def daily(self, symbol: str, start: str|None, end: str|None) -> pd.DataFrame: ...
      def search(self, query: str, limit: int = 50) -> pd.DataFrame: ...
      def minute(self, symbol: str, start: str|None, end: str|None, interval: str) -> pd.DataFrame: ...
      def orderbook(self, symbol: str, depth: int = 5) -> dict: ...
  ```
- 新增 Provider 时仅需实现协议并在 SDK/路由处注册。

### 9) 存储模型（SQLAlchemy）
- 表：`price_daily`
  - 列：`id, symbol, trade_date, open, high, low, close, volume, provider, created_at`
  - 索引：唯一(`symbol`,`trade_date`)；普通索引(`symbol`)、(`trade_date`)。

### 10) 非功能与合规
- 合规：仅接入有明确授权的 API/数据接口。
- 缓存：写库即缓存，后续引入 TTL/冷热分层与只读优先策略。
- 限流/重试：Provider 层实现退避重试与节流（TODO）。
- 观察性：接入基础日志与错误分层（TODO）。

### 10.1) 数据来源与环境一致性（重要）
- 本地开发、dev、prod 三个环境均使用真实数据源（当前为 Tushare），不提供任何“测试/模拟/回放”数据路径。
- 若缺少凭据将直接报错，不会降级到伪数据；所有策略/回测/优化均基于真实历史数据与当前可得数据。

### 11) 开发任务清单（优先级从上到下）
1. 定义 `EquityPriceProviderProtocol` 并改造 TushareProvider 适配协议。
2. API 返回结构改为数据+分页（支持 `limit/offset` 或游标）。
3. 新增分钟线接口：`/equity/price/minute` + SDK `minute(...)`。
4. 新增盘口接口占位：`/equity/orderbook`，Provider 预留实现。
5. 港股 Provider：`HKExProvider`（日线/分钟线）。
6. 期货 Provider：中金所/上期所/大商所/郑商所（日线/分钟线）。
7. 缓存策略与索引优化（批量写入、幂等 upsert、读缓存优先）。
8. CLI 查询子命令（如 `openbb-cn equity price historical ...`）。
9. 健康检查与就绪探针（`/healthz`）。
10. 基础单元/集成测试与 CI。

### 12) 验收标准（MVP）
- 启动 API 后，调用：
  ```bash
  curl "http://127.0.0.1:6900/equity/price/historical?symbol=600519.SH&start=20240101&end=20240630"
  ```
  - 返回 `{ symbol: "600519.SH", rows: n }` 且数据库 `price_daily` 行数随之增长。
- SDK 调用返回 `pandas.DataFrame`，包含规范化列且按日期升序。
- 通过 Docker 启动 `db+api` 成功（`docker compose up -d --build`）。

### 13) 故障排查
- 无法启动：检查 `.env` 是否包含 `BAILIAN_API_KEY`（AI分析需要），数据库连接是否可用。
- **端口占用**：如果6900端口被占用，说明服务已启动，需要先关闭现有服务，**不得使用其他端口**。
- 拉取为空：核对 `symbol` 是否为标准格式（如 `600519.SH`），检查网络连接到AData数据源。
- 搜索无结果：AData支持5738只A股，如无结果可能是网络问题或股票代码不存在。
- AI分析失败：检查阿里云百炼API密钥是否有效，确保指标数据已完全加载。
- 写库失败：检查 PostgreSQL 权限/端口/网络与唯一索引冲突。

### 14) 参考链接
- AData开源项目：[`https://github.com/1nchaos/adata`](https://github.com/1nchaos/adata)
- AData官方文档：[`https://adata.30006124.xyz/`](https://adata.30006124.xyz/)
- 阿里云百炼API：[`https://bailian.console.aliyun.com/?tab=model#/api-key`](https://bailian.console.aliyun.com/?tab=model#/api-key)


### 15) 规则与维护约定（请所有智能体遵循）
- 文档即“单一事实来源”：每次代码或配置改动，必须同步更新本文件对应章节（接口、环境、部署、运行命令、注意事项）。
- **端口使用规则**：
  - **必须使用6900端口**：系统固定使用6900端口，任何情况下都不允许更改。
  - **端口占用处理**：如果6900端口被占用，说明服务已启动，必须先关闭现有服务。
  - **禁止端口变更**：不得使用8000、8001等其他端口，不得在代码中修改端口配置。
  - **服务管理**：启动前检查端口状态，确保6900端口可用。
- 开发/生产分层：
  - 开发使用 `envs/dev`（源码挂载、热更新）；生产使用 `envs/prod`（镜像产物、不可变部署）。
  - 任何生产变更在 dev 先验证通过再同步到 prod。
- 合规与数据源：仅接入有授权的数据源；严禁使用“仅限学习用途”的抓取来源到生产。
- AI 配置：默认使用阿里云百炼（通义）兼容 OpenAI 接口；密钥放 `.env`，禁止写入代码或日志。
- 模块化：新增功能以独立 Provider/模块形式实现，保持清晰接口与边界。
- 提交流程：
  1) 修改代码 → 更新本文档相关条目 → 运行本地或 dev 验证
  2) 若涉及生产：更新 `envs/prod` 说明与参数 → 执行 `start.ps1 -Rebuild`
  3) 在变更日志“16) Changelog”新增一条，包括：目的、影响范围、接口/配置变化、回滚方式
- 质量基线：不引入 lint 错误；接口与兼容性变更需在 `API 契约` 中显式标注；提供最小可复现步骤。


### 16) Changelog（重要改动按时间倒序追加）
- v0.2.6：**自选股功能修复** - 修复自选股列表中股票名称显示缺失问题；添加currentName变量存储当前股票名称；修复addFromInput()函数使用正确的股票名称；增强renderWatch()函数的调试信息和错误处理；添加数据清理功能；新增调试工具帮助排查问题；改进用户提示和交互体验。
- v0.2.5：**快捷分析功能完善** - 实现4个快捷分析按钮的完整功能（技术分析、新闻影响、风险评估、投资策略）；增强quickAnalysis函数，添加分析类型标识和调试信息；优化askAI函数错误处理；新增测试功能验证快捷分析；改进按钮样式和交互效果。
- v0.2.4：**端口使用规则明确** - 在文档中明确规定系统必须使用6900端口，不允许更改；端口占用时必须关闭现有服务，禁止使用其他端口；更新所有相关章节和故障排查说明。
- v0.2.3：**AI分析功能全面增强** - 优化数据收集时序，确保指标数据完整后再进行AI分析；增强数据传递结构，包含指标趋势、强度、分类汇总；集成价格数据和新闻信息；重构AI提示词，要求明确的买入/卖出/持仓建议；解决"缺少技术指标数据"问题。
- v0.2.2：**搜索功能优化** - 完全移除Tushare的limit=10限制；优化搜索算法，支持精确匹配、前缀匹配、模糊匹配的分层排序；提升搜索结果数量从10个到50个；基于AData的5738只股票无限制搜索。
- v0.2.1：**页面布局重大重构** - 实现更大的主展示区域（2:1双栏布局）；新增实时行情面板显示价格、涨跌幅、成交量；增加新闻板块，选择股票后自动显示相关新闻；AI分析集成新闻数据；新增4个快捷分析按钮；优化图表性能，K线图限制120天数据提升加载速度。
- v0.2.0：**完全迁移到AData数据源** - 彻底移除Tushare依赖，删除tushare_provider.py；AData成为唯一数据源，支持5738只A股；重构所有API使用AData；更新requirements.txt移除tushare依赖；优化.env配置，移除TUSHARE_TOKEN；解决积分限制问题，实现无限制数据访问。
- v0.1.7：严格声明三环境均使用真实数据；禁止“测试/模拟数据”降级；README 与本指南同步更新此约束。
- v0.1.6：加入自研指标 VCM/TEI/LVR/API/BLCE/MTR；新增接口 `/indicator/*`；`/workspace` 页面添加“指标卡片与阈值提示”，AI 自动引用自研指标上下文。
- v0.1.5：接入阿里云百炼（通义）兼容 OpenAI 接口，`/ai/analyze` 优先调用；新增环境变量 `BAILIAN_*`。
- v0.1.4：`/workspace` 工作台上线（左侧搜索选股、右侧AI分析、K线与估值倍数图）。
- v0.1.3：估值倍数接口 `/equity/basic/ratios` 与搜索 `/search`；页面绘制 PE/PB/PS。
- v0.1.2：补充“开发/生产环境分层”，明确 `envs/dev` 与 `envs/prod` 的启动/停止与注意事项（生产运行在 Docker）。
- v0.1.1：项目骨架（API/SDK/Provider/DB/Docker）、Tushare Provider、`/equity/price/historical` 接口、README 与 `.env` 模板。
