## OpenBB-CN AI 开发说明（面向编程智能体）

本文件为“编程AI/代理”准备，提供最小而精确的上下文：目标、接口契约、运行命令、扩展点、任务清单与验收标准。请严格遵循模块化与清晰边界的设计[[memory:4806727]]。

### 1) 目标与范围
- 目标：提供中国市场（A股/港股/期货）的统一数据访问平台，暴露 Python SDK 与 REST API，支持分钟/分时/盘口的后续扩展，数据合规可用。
- MVP 范围：
  - 日线历史行情（A股，基于 Tushare）。
  - PostgreSQL 缓存/快照（唯一索引避免重复写入）。
  - REST API 与 Python SDK 一致的查询语义。

### 2) 关键约束
- 必须模块化、可插拔 Provider，清晰接口与职责边界[[memory:4806727]]。
- 合规数据源优先（商业授权/明确许可），不接受“仅限学习用途”的抓取来源。
- 生产运行在 Docker；开发在本地虚拟环境或 Cursor 内置环境。

### 3) 目录结构（核心）
- `openbb_cn/api/app.py`：FastAPI 入口（ASGI 应用）。
- `openbb_cn/sdk/obb.py`：Python SDK（`obb.equity.price.historical`）。
- `openbb_cn/providers/tushare_provider.py`：Tushare Provider 实现。
- `openbb_cn/storage/schema.py`：SQLAlchemy 数据表结构（`price_daily`）。
- `openbb_cn/storage/db.py`：DB 初始化与会话管理。
- `openbb_cn/config.py`：环境变量与配置装载（`.env`）。
- `openbb_cn/cli.py`：CLI（启动 API）。
- `.env`：运行时配置（token、数据库、端口）。
- `Dockerfile`、`docker-compose.yml`：容器化部署。

### 4) 运行方式（命令）
- 本地（Windows PowerShell）：
  ```
  python -m venv .venv
  .\.venv\Scripts\Activate.ps1
  pip install -r requirements.txt
  # 可选：docker compose up -d db （或使用已有 PostgreSQL）
  uvicorn openbb_cn.api.app:app --host 0.0.0.0 --port 6900 --reload
  ```
- Docker：
  ```
  docker compose up -d --build
  # API: http://127.0.0.1:6900/docs
  ```
- CLI：
  ```
  python -m openbb_cn.cli api --reload
  # 或安装后：openbb-cn api
  ```

### 5) 环境变量（.env）
- `TUSHARE_TOKEN`：Tushare 访问令牌（获取：`https://tushare.pro/user/token`）。
- `DATABASE_URL`：默认 `postgresql+psycopg2://openbb:openbb@localhost:5432/openbb_cn`。
- `APP_HOST`：默认 `0.0.0.0`。
- `APP_PORT`：默认 `6900`。

### 6) API 契约（当前）
- GET `/equity/price/historical`
  - Query：
    - `symbol`（必填）：Tushare `ts_code`，如 `600519.SH`、`000001.SZ`。
    - `start`（可选）：`YYYYMMDD`。
    - `end`（可选）：`YYYYMMDD`。
  - Response（JSON）：
    ```json
    { "symbol": "600519.SH", "rows": 1234 }
    ```
  - 说明：数据会写入 `price_daily` 表做缓存/快照（去重）。后续版本将返回完整数据或分页游标。

### 7) SDK 契约
- 导入：
  ```python
  from openbb_cn.sdk.obb import obb
  ```
- `obb.equity.price.historical(symbol: str, start: str|None, end: str|None) -> pandas.DataFrame`
  - 行为：
    1) 从 Provider 拉取数据（当前 `TushareProvider.daily`）。
    2) 写入 PostgreSQL（唯一键：`symbol + trade_date`）。
    3) 返回 `DataFrame`（列：`trade_date, open, high, low, close, volume, provider`）。

### 8) Provider 接口与扩展点
- 目标：统一 Provider 协议，支持 A股/港股/期货/分钟/盘口扩展。
- 现有实现：
  - `openbb_cn.providers.tushare_provider.TushareProvider`
    - `daily(symbol: str, start: str|None, end: str|None) -> pandas.DataFrame`
- 待抽象（建议接口）：
  ```python
  class EquityPriceProviderProtocol(Protocol):
      def daily(self, symbol: str, start: str|None, end: str|None) -> pd.DataFrame: ...
      def minute(self, symbol: str, start: str|None, end: str|None, interval: str) -> pd.DataFrame: ...
      def orderbook(self, symbol: str, depth: int = 5) -> dict: ...
  ```
- 新增 Provider 时仅需实现协议并在 SDK/路由处注册。

### 9) 存储模型（SQLAlchemy）
- 表：`price_daily`
  - 列：`id, symbol, trade_date, open, high, low, close, volume, provider, created_at`
  - 索引：唯一(`symbol`,`trade_date`)；普通索引(`symbol`)、(`trade_date`)。

### 10) 非功能与合规
- 合规：仅接入有明确授权的 API/数据接口。
- 缓存：写库即缓存，后续引入 TTL/冷热分层与只读优先策略。
- 限流/重试：Provider 层实现退避重试与节流（TODO）。
- 观察性：接入基础日志与错误分层（TODO）。

### 11) 开发任务清单（优先级从上到下）
1. 定义 `EquityPriceProviderProtocol` 并改造 TushareProvider 适配协议。
2. API 返回结构改为数据+分页（支持 `limit/offset` 或游标）。
3. 新增分钟线接口：`/equity/price/minute` + SDK `minute(...)`。
4. 新增盘口接口占位：`/equity/orderbook`，Provider 预留实现。
5. 港股 Provider：`HKExProvider`（日线/分钟线）。
6. 期货 Provider：中金所/上期所/大商所/郑商所（日线/分钟线）。
7. 缓存策略与索引优化（批量写入、幂等 upsert、读缓存优先）。
8. CLI 查询子命令（如 `openbb-cn equity price historical ...`）。
9. 健康检查与就绪探针（`/healthz`）。
10. 基础单元/集成测试与 CI。

### 12) 验收标准（MVP）
- 启动 API 后，调用：
  ```bash
  curl "http://127.0.0.1:6900/equity/price/historical?symbol=600519.SH&start=20240101&end=20240630"
  ```
  - 返回 `{ symbol: "600519.SH", rows: n }` 且数据库 `price_daily` 行数随之增长。
- SDK 调用返回 `pandas.DataFrame`，包含规范化列且按日期升序。
- 通过 Docker 启动 `db+api` 成功（`docker compose up -d --build`）。

### 13) 故障排查
- 无法启动：检查 `.env` 是否包含 `TUSHARE_TOKEN`，数据库连接是否可用。
- 拉取为空：核对 `symbol` 是否为 Tushare `ts_code`（如 `600519.SH`）。
- 写库失败：检查 PostgreSQL 权限/端口/网络与唯一索引冲突。

### 14) 参考链接
- Tushare Token 获取：[`https://tushare.pro/user/token`](https://tushare.pro/user/token)

