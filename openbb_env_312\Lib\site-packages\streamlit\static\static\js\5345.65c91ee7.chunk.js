"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[5345],{46979:(e,t,r)=>{r.r(t),r.d(t,{FileSystemDirectoryHandle:()=>o,default:()=>l});var i=r(64649),n=r(93232);let a;const s=Symbol("adapter");a=Symbol.asyncIterator;class o extends n.Z{constructor(e){super(e),(0,i.Z)(this,s,void 0),this[s]=e}async getDirectoryHandle(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(""===e)throw new TypeError("Name can't be an empty string.");if("."===e||".."===e||e.includes("/"))throw new TypeError("Name contains invalid characters.");t.create=!!t.create;const r=await this[s].getDirectoryHandle(e,t);return new o(r)}async*entries(){const{FileSystemFileHandle:e}=await Promise.resolve().then(r.bind(r,8192));for await(const[t,r]of this[s].entries())yield[r.name,"file"===r.kind?new e(r):new o(r)]}async*getEntries(){const{FileSystemFileHandle:e}=await Promise.resolve().then(r.bind(r,8192));console.warn("deprecated, use .entries() instead");for await(let t of this[s].entries())yield"file"===t.kind?new e(t):new o(t)}async getFileHandle(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{FileSystemFileHandle:i}=await Promise.resolve().then(r.bind(r,8192));if(""===e)throw new TypeError("Name can't be an empty string.");if("."===e||".."===e||e.includes("/"))throw new TypeError("Name contains invalid characters.");t.create=!!t.create;return new i(await this[s].getFileHandle(e,t))}async removeEntry(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(""===e)throw new TypeError("Name can't be an empty string.");if("."===e||".."===e||e.includes("/"))throw new TypeError("Name contains invalid characters.");return t.recursive=!!t.recursive,this[s].removeEntry(e,t)}async resolve(e){if(await e.isSameEntry(this))return[];const t=[{handle:this,path:[]}];for(;t.length;){let{handle:r,path:i}=t.pop();for await(const n of r.values()){if(await n.isSameEntry(e))return[...i,n.name];"directory"===n.kind&&t.push({handle:n,path:[...i,n.name]})}}return null}async*keys(){for await(const[e]of this[s].entries())yield e}async*values(){for await(const[e,t]of this)yield t}[a](){return this.entries()}}Object.defineProperty(o.prototype,Symbol.toStringTag,{value:"FileSystemDirectoryHandle",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(o.prototype,{getDirectoryHandle:{enumerable:!0},entries:{enumerable:!0},getFileHandle:{enumerable:!0},removeEntry:{enumerable:!0}});const l=o},8192:(e,t,r)=>{r.r(t),r.d(t,{FileSystemFileHandle:()=>o,default:()=>l});var i=r(64649),n=r(93232),a=r(82572);const s=Symbol("adapter");class o extends n.Z{constructor(e){super(e),(0,i.Z)(this,s,void 0),this[s]=e}async createWritable(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new a.Z(await this[s].createWritable(e))}async getFile(){return this[s].getFile()}}Object.defineProperty(o.prototype,Symbol.toStringTag,{value:"FileSystemFileHandle",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(o.prototype,{createWritable:{enumerable:!0},getFile:{enumerable:!0}});const l=o},93232:(e,t,r)=>{r.d(t,{Z:()=>s});var i=r(64649);const n=Symbol("adapter");class a{constructor(e){(0,i.Z)(this,n,void 0),(0,i.Z)(this,"name",void 0),(0,i.Z)(this,"kind",void 0),this.kind=e.kind,this.name=e.name,this[n]=e}async queryPermission(){let{mode:e="read"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=this[n];if(t.queryPermission)return t.queryPermission({mode:e});if("read"===e)return"granted";if("readwrite"===e)return t.writable?"granted":"denied";throw new TypeError("Mode ".concat(e," must be 'read' or 'readwrite'"))}async requestPermission(){let{mode:e="read"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=this[n];if(t.requestPermission)return t.requestPermission({mode:e});if("read"===e)return"granted";if("readwrite"===e)return t.writable?"granted":"denied";throw new TypeError("Mode ".concat(e," must be 'read' or 'readwrite'"))}async remove(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};await this[n].remove(e)}async isSameEntry(e){return this===e||!(!e||"object"!==typeof e||this.kind!==e.kind||!e[n])&&this[n].isSameEntry(e[n])}}Object.defineProperty(a.prototype,Symbol.toStringTag,{value:"FileSystemHandle",writable:!1,enumerable:!1,configurable:!0});const s=a},82572:(e,t,r)=>{r.d(t,{Z:()=>s});var i=r(60643);const{WritableStream:n}=i.Z;class a extends n{constructor(){super(...arguments),Object.setPrototypeOf(this,a.prototype),this._closed=!1}close(){this._closed=!0;const e=this.getWriter(),t=e.close();return e.releaseLock(),t}seek(e){return this.write({type:"seek",position:e})}truncate(e){return this.write({type:"truncate",size:e})}write(e){if(this._closed)return Promise.reject(new TypeError("Cannot write to a CLOSED writable stream"));const t=this.getWriter(),r=t.write(e);return t.releaseLock(),r}}Object.defineProperty(a.prototype,Symbol.toStringTag,{value:"FileSystemWritableFileStream",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(a.prototype,{close:{enumerable:!0},seek:{enumerable:!0},truncate:{enumerable:!0},write:{enumerable:!0}});const s=a},60643:(e,t,r)=>{r.d(t,{Z:()=>i});const i={ReadableStream:globalThis.ReadableStream,WritableStream:globalThis.WritableStream,TransformStream:globalThis.TransformStream,DOMException:globalThis.DOMException,Blob:globalThis.Blob,File:globalThis.File}},95345:(e,t,r)=>{r.r(t),r.d(t,{FileSystemDirectoryHandle:()=>m.default,FileSystemFileHandle:()=>y.default,FileSystemHandle:()=>u.Z,FileSystemWritableFileStream:()=>h.Z,getOriginPrivateDirectory:()=>d,showDirectoryPicker:()=>n,showOpenFilePicker:()=>o,showSaveFilePicker:()=>c});const i=globalThis.showDirectoryPicker;const n=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(i&&!e._preferPolyfill)return i(e);const t=document.createElement("input");if(t.type="file",!("webkitdirectory"in t))throw new Error("HTMLInputElement.webkitdirectory is not supported");t.style.position="fixed",t.style.top="-100000px",t.style.left="-100000px",document.body.appendChild(t),t.webkitdirectory=!0;const n=r.e(9758).then(r.bind(r,69758));return await new Promise((e=>{t.addEventListener("change",e),t.click()})),n.then((e=>e.getDirHandlesFromInput(t)))},a={accepts:[]},s=globalThis.showOpenFilePicker;const o=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t={...a,...e};if(s&&!e._preferPolyfill)return s(t);const i=document.createElement("input");i.type="file",i.multiple=t.multiple,i.accept=(t.accepts||[]).map((e=>[...(e.extensions||[]).map((e=>"."+e)),...e.mimeTypes||[]])).flat().join(","),i.style.position="fixed",i.style.top="-100000px",i.style.left="-100000px",document.body.appendChild(i);const n=r.e(9758).then(r.bind(r,69758));return await new Promise((e=>{i.addEventListener("change",e),i.click()})),n.then((e=>e.getFileHandlesFromInput(i)))},l=globalThis.showSaveFilePicker;const c=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(l&&!e._preferPolyfill)return l(e);e._name&&(console.warn("deprecated _name, spec now have `suggestedName`"),e.suggestedName=e._name);const{FileSystemFileHandle:t}=await Promise.resolve().then(r.bind(r,8192)),{FileHandle:i}=await r.e(474).then(r.bind(r,60474));return new t(new i(e.suggestedName))};globalThis.DataTransferItem&&!DataTransferItem.prototype.getAsFileSystemHandle&&(DataTransferItem.prototype.getAsFileSystemHandle=async function(){const e=this.webkitGetAsEntry(),[{FileHandle:t,FolderHandle:i},{FileSystemDirectoryHandle:n},{FileSystemFileHandle:a}]=await Promise.all([r.e(3631).then(r.bind(r,13053)),Promise.resolve().then(r.bind(r,46979)),Promise.resolve().then(r.bind(r,8192))]);return e.isFile?new a(new t(e,!1)):new n(new i(e,!1))});const d=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var i,n;if(!e)return(null===(i=globalThis.navigator)||void 0===i||null===(n=i.storage)||void 0===n?void 0:n.getDirectory())||globalThis.getOriginPrivateDirectory();const{FileSystemDirectoryHandle:a}=await Promise.resolve().then(r.bind(r,46979)),s=await e;return new a(await(s.default?s.default(t):s(t)))};var m=r(46979),y=r(8192),u=r(93232),h=r(82572)}}]);