Metadata-Version: 2.3
Name: openbb-econdb
Version: 1.3.2
Summary: EconDB extension for OpenBB
License: AGPL-3.0-only
Author: OpenBB Team
Author-email: <EMAIL>
Requires-Python: >=3.9.21,<3.13
Classifier: License :: OSI Approved :: GNU Affero General Public License v3
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Dist: aiohttp-client-cache (>=0.11.0,<0.12.0)
Requires-Dist: aiosqlite (>=0.20.0,<0.21.0)
Requires-Dist: openbb-core (>=1.4.8,<2.0.0)
Description-Content-Type: text/markdown

# OpenBB EconDB Provider

This extension integrates the [EconDB](https://econdb.com/) data provider into the OpenBB Platform.

## Installation

To install the extension:

```bash
pip install openbb-econdb
```

Documentation available [here](https://docs.openbb.co/platform/developer_guide/contributing).

