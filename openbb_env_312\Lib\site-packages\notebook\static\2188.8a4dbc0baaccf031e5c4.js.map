{"version": 3, "file": "2188.8a4dbc0baaccf031e5c4.js?v=8a4dbc0baaccf031e5c4", "mappings": ";;;;;;;;;;AAAA;AACA;AACA,kBAAkB,kBAAkB;AACpC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;AACA,IAAI,iBAAiB,KAAK;AAC1B;AACA,gBAAgB;AAChB;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,6CAA6C,EAAE;AAC/C,wDAAwD,sBAAsB;AAC9E,kDAAkD,EAAE;AACpD,kDAAkD,EAAE;AACpD,sDAAsD,IAAI;AAC1D;AACA,MAAM;AACN;AACA;AACA,6BAA6B,4BAA4B;AACzD,4BAA4B,kBAAkB;AAC9C;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA,YAAY;AACZ,kBAAkB;AAClB;AACA,kCAAkC;AAClC;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,sBAAsB,kBAAkB,kBAAkB;AAC1D,qBAAqB,iBAAiB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,iEAAiE;AACjE;AACA;AACA,GAAG;;AAEH;AACA;AACA,oBAAoB,UAAU;AAC9B;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/r.js"], "sourcesContent": ["function wordObj(words) {\n  var res = {};\n  for (var i = 0; i < words.length; ++i) res[words[i]] = true;\n  return res;\n}\nvar commonAtoms = [\"NULL\", \"NA\", \"Inf\", \"NaN\", \"NA_integer_\", \"NA_real_\", \"NA_complex_\", \"NA_character_\", \"TRUE\", \"FALSE\"];\nvar commonBuiltins = [\"list\", \"quote\", \"bquote\", \"eval\", \"return\", \"call\", \"parse\", \"deparse\"];\nvar commonKeywords = [\"if\", \"else\", \"repeat\", \"while\", \"function\", \"for\", \"in\", \"next\", \"break\"];\nvar commonBlockKeywords = [\"if\", \"else\", \"repeat\", \"while\", \"function\", \"for\"];\n\nvar atoms = wordObj(commonAtoms);\nvar builtins = wordObj(commonBuiltins);\nvar keywords = wordObj(commonKeywords);\nvar blockkeywords = wordObj(commonBlockKeywords);\nvar opChars = /[+\\-*\\/^<>=!&|~$:]/;\nvar curPunc;\n\nfunction tokenBase(stream, state) {\n  curPunc = null;\n  var ch = stream.next();\n  if (ch == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  } else if (ch == \"0\" && stream.eat(\"x\")) {\n    stream.eatWhile(/[\\da-f]/i);\n    return \"number\";\n  } else if (ch == \".\" && stream.eat(/\\d/)) {\n    stream.match(/\\d*(?:e[+\\-]?\\d+)?/);\n    return \"number\";\n  } else if (/\\d/.test(ch)) {\n    stream.match(/\\d*(?:\\.\\d+)?(?:e[+\\-]\\d+)?L?/);\n    return \"number\";\n  } else if (ch == \"'\" || ch == '\"') {\n    state.tokenize = tokenString(ch);\n    return \"string\";\n  } else if (ch == \"`\") {\n    stream.match(/[^`]+`/);\n    return \"string.special\";\n  } else if (ch == \".\" && stream.match(/.(?:[.]|\\d+)/)) {\n    return \"keyword\";\n  } else if (/[a-zA-Z\\.]/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    var word = stream.current();\n    if (atoms.propertyIsEnumerable(word)) return \"atom\";\n    if (keywords.propertyIsEnumerable(word)) {\n      // Block keywords start new blocks, except 'else if', which only starts\n      // one new block for the 'if', no block for the 'else'.\n      if (blockkeywords.propertyIsEnumerable(word) &&\n          !stream.match(/\\s*if(\\s+|$)/, false))\n        curPunc = \"block\";\n      return \"keyword\";\n    }\n    if (builtins.propertyIsEnumerable(word)) return \"builtin\";\n    return \"variable\";\n  } else if (ch == \"%\") {\n    if (stream.skipTo(\"%\")) stream.next();\n    return \"variableName.special\";\n  } else if (\n    (ch == \"<\" && stream.eat(\"-\")) ||\n      (ch == \"<\" && stream.match(\"<-\")) ||\n      (ch == \"-\" && stream.match(/>>?/))\n  ) {\n    return \"operator\";\n  } else if (ch == \"=\" && state.ctx.argList) {\n    return \"operator\";\n  } else if (opChars.test(ch)) {\n    if (ch == \"$\") return \"operator\";\n    stream.eatWhile(opChars);\n    return \"operator\";\n  } else if (/[\\(\\){}\\[\\];]/.test(ch)) {\n    curPunc = ch;\n    if (ch == \";\") return \"punctuation\";\n    return null;\n  } else {\n    return null;\n  }\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    if (stream.eat(\"\\\\\")) {\n      var ch = stream.next();\n      if (ch == \"x\") stream.match(/^[a-f0-9]{2}/i);\n      else if ((ch == \"u\" || ch == \"U\") && stream.eat(\"{\") && stream.skipTo(\"}\")) stream.next();\n      else if (ch == \"u\") stream.match(/^[a-f0-9]{4}/i);\n      else if (ch == \"U\") stream.match(/^[a-f0-9]{8}/i);\n      else if (/[0-7]/.test(ch)) stream.match(/^[0-7]{1,2}/);\n      return \"string.special\";\n    } else {\n      var next;\n      while ((next = stream.next()) != null) {\n        if (next == quote) { state.tokenize = tokenBase; break; }\n        if (next == \"\\\\\") { stream.backUp(1); break; }\n      }\n      return \"string\";\n    }\n  };\n}\n\nvar ALIGN_YES = 1, ALIGN_NO = 2, BRACELESS = 4\n\nfunction push(state, type, stream) {\n  state.ctx = {type: type,\n               indent: state.indent,\n               flags: 0,\n               column: stream.column(),\n               prev: state.ctx};\n}\nfunction setFlag(state, flag) {\n  var ctx = state.ctx\n  state.ctx = {type: ctx.type,\n               indent: ctx.indent,\n               flags: ctx.flags | flag,\n               column: ctx.column,\n               prev: ctx.prev}\n}\nfunction pop(state) {\n  state.indent = state.ctx.indent;\n  state.ctx = state.ctx.prev;\n}\n\nexport const r = {\n  name: \"r\",\n  startState: function(indentUnit) {\n    return {tokenize: tokenBase,\n            ctx: {type: \"top\",\n                  indent: -indentUnit,\n                  flags: ALIGN_NO},\n            indent: 0,\n            afterIdent: false};\n  },\n\n  token: function(stream, state) {\n    if (stream.sol()) {\n      if ((state.ctx.flags & 3) == 0) state.ctx.flags |= ALIGN_NO\n      if (state.ctx.flags & BRACELESS) pop(state)\n      state.indent = stream.indentation();\n    }\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n    if (style != \"comment\" && (state.ctx.flags & ALIGN_NO) == 0) setFlag(state, ALIGN_YES)\n\n    if ((curPunc == \";\" || curPunc == \"{\" || curPunc == \"}\") && state.ctx.type == \"block\") pop(state);\n    if (curPunc == \"{\") push(state, \"}\", stream);\n    else if (curPunc == \"(\") {\n      push(state, \")\", stream);\n      if (state.afterIdent) state.ctx.argList = true;\n    }\n    else if (curPunc == \"[\") push(state, \"]\", stream);\n    else if (curPunc == \"block\") push(state, \"block\", stream);\n    else if (curPunc == state.ctx.type) pop(state);\n    else if (state.ctx.type == \"block\" && style != \"comment\") setFlag(state, BRACELESS)\n    state.afterIdent = style == \"variable\" || style == \"keyword\";\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize != tokenBase) return 0;\n    var firstChar = textAfter && textAfter.charAt(0), ctx = state.ctx,\n        closing = firstChar == ctx.type;\n    if (ctx.flags & BRACELESS) ctx = ctx.prev\n    if (ctx.type == \"block\") return ctx.indent + (firstChar == \"{\" ? 0 : cx.unit);\n    else if (ctx.flags & ALIGN_YES) return ctx.column + (closing ? 0 : 1);\n    else return ctx.indent + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    wordChars: \".\",\n    commentTokens: {line: \"#\"},\n    autocomplete: commonAtoms.concat(commonBuiltins, commonKeywords)\n  }\n};\n"], "names": [], "sourceRoot": ""}