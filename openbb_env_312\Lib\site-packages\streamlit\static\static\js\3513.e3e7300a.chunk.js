"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[3513],{33513:(e,t,i)=>{i.r(t),i.d(t,{default:()=>r});i(66845);var l=i(81354),s=i(27446),a=i(9003),d=i(21e3),n=i(40864);const r=function(e){const{disabled:t,element:i,widgetMgr:r,width:o,endpoints:u,fragmentId:c}=e,h={width:o},p="primary"===i.type?l.nW.PRIMARY:l.nW.SECONDARY,b=!i.help||o;return(0,n.jsx)("div",{className:"row-widget stDownloadButton","data-testid":"stDownloadButton",style:h,children:(0,n.jsx)(s.t,{help:i.help,children:(0,n.jsx)(a.ZP,{kind:p,size:l.V5.SMALL,disabled:t,onClick:()=>{r.setTriggerValue(i,{fromUi:!0},c);const e=document.createElement("a"),t=u.buildMediaURL(i.url);e.setAttribute("href",t),e.setAttribute("target","_self"),e.setAttribute("download",""),e.click()},fluidWidth:!!i.useContainerWidth&&b,children:(0,n.jsx)(d.ZP,{source:i.label,allowHTML:!1,isLabel:!0,largerLabel:!0,disableLinks:!0})})})})}}}]);