"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[1451],{61451:(t,e,s)=>{s.r(e),s.d(e,{default:()=>C});var a=s(66845),i=s(53608),r=s.n(i),o=s(25621),n=s(15791),l=s(28278),d=s(13553),p=s(87814),m=s(98478),h=s(86659),c=s(8879),u=s(68411),g=s(50641),f=s(40864);const v="YYYY/MM/DD";function y(t){return t.map((t=>new Date(t)))}class b extends a.PureComponent{constructor(){super(...arguments),this.formClearHelper=new p.K,this.state={values:this.initialValue,isRange:this.props.element.isRange,isEmpty:!1},this.commitWidgetValue=t=>{const{widgetMgr:e,element:s,fragmentId:a}=this.props;var i;e.setStringArrayValue(s,(i=this.state.values)?i.map((t=>r()(t).format(v))):[],t,a)},this.onFormCleared=()=>{const t=y(this.props.element.default);this.setState({values:t,isEmpty:!t},(()=>this.commitWidgetValue({fromUi:!0})))},this.handleChange=t=>{let{date:e}=t;if(null===e||void 0===e)return void this.setState({values:[],isEmpty:!0});const s=[];Array.isArray(e)?e.forEach((t=>{t&&s.push(t)})):s.push(e),this.setState({values:s,isEmpty:!s},(()=>{this.state.isEmpty||this.commitWidgetValue({fromUi:!0})}))},this.handleClose=()=>{const{isEmpty:t}=this.state;t&&this.setState(((t,e)=>({values:y(e.element.default),isEmpty:!y(e.element.default)})),(()=>{this.commitWidgetValue({fromUi:!0})}))},this.getMaxDate=()=>{const{element:t}=this.props,e=t.max;return e&&e.length>0?r()(e,v).toDate():void 0}}get initialValue(){const t=this.props.widgetMgr.getStringArrayValue(this.props.element);return y(void 0!==t?t:this.props.element.default||[])}componentDidMount(){this.props.element.setValue?this.updateFromProtobuf():this.commitWidgetValue({fromUi:!1})}componentDidUpdate(){this.maybeUpdateFromProtobuf()}componentWillUnmount(){this.formClearHelper.disconnect()}maybeUpdateFromProtobuf(){const{setValue:t}=this.props.element;t&&this.updateFromProtobuf()}updateFromProtobuf(){const{value:t}=this.props.element;this.props.element.setValue=!1;const e=t.map((t=>new Date(t)));this.setState({values:e,isEmpty:!e},(()=>{this.commitWidgetValue({fromUi:!1})}))}render(){var t;const{width:e,element:s,disabled:a,theme:i,widgetMgr:o}=this.props,{values:p,isRange:y}=this.state,{colors:b,fontSizes:C,lineHeights:x}=i,D={width:e},I=r()(s.min,v).toDate(),W=this.getMaxDate(),F=0===s.default.length&&!a,V=s.format.replaceAll(/[a-zA-Z]/g,"9"),S=s.format.replaceAll("Y","y").replaceAll("D","d");return this.formClearHelper.manageFormClearListener(o,s.formId,this.onFormCleared),(0,f.jsxs)("div",{className:"stDateInput",style:D,"data-testid":"stDateInput",children:[(0,f.jsx)(m.O,{label:s.label,disabled:a,labelVisibility:(0,g.iF)(null===(t=s.labelVisibility)||void 0===t?void 0:t.value),children:s.help&&(0,f.jsx)(h.dT,{children:(0,f.jsx)(c.Z,{content:s.help,placement:u.u.TOP_RIGHT})})}),(0,f.jsx)(n.Z,{density:l.pw.high,formatString:S,mask:y?"".concat(V," \u2013 ").concat(V):V,placeholder:y?"".concat(s.format," \u2013 ").concat(s.format):s.format,disabled:a,onChange:this.handleChange,onClose:this.handleClose,overrides:{Popover:{props:{placement:d.r4.bottomLeft,overrides:{Body:{style:{border:"1px solid ".concat(b.fadedText10)}}}}},CalendarContainer:{style:{fontSize:C.sm,paddingRight:i.spacing.sm,paddingLeft:i.spacing.sm,paddingBottom:i.spacing.sm,paddingTop:i.spacing.sm}},Week:{style:{fontSize:C.sm}},Day:{style:t=>{let{$pseudoHighlighted:e,$pseudoSelected:s,$selected:a,$isHovered:i}=t;return{fontSize:C.sm,lineHeight:x.base,"::before":{backgroundColor:a||s||e||i?"".concat(b.secondaryBg," !important"):b.transparent},"::after":{borderColor:b.transparent}}}},PrevButton:{style:()=>({display:"flex",alignItems:"center",justifyContent:"center",":active":{backgroundColor:b.transparent},":focus":{backgroundColor:b.transparent,outline:0}})},NextButton:{style:{display:"flex",alignItems:"center",justifyContent:"center",":active":{backgroundColor:b.transparent},":focus":{backgroundColor:b.transparent,outline:0}}},Input:{props:{maskChar:null,overrides:{Root:{style:{borderLeftWidth:"1px",borderRightWidth:"1px",borderTopWidth:"1px",borderBottomWidth:"1px"}},ClearIcon:{props:{overrides:{Svg:{style:{color:i.colors.darkGray,transform:"scale(1.41)",width:i.spacing.twoXL,marginRight:"-8px",":hover":{fill:i.colors.bodyText}}}}}},Input:{style:{paddingRight:".5rem",paddingLeft:".5rem",paddingBottom:".5rem",paddingTop:".5rem",lineHeight:1.4},props:{"data-testid":"stDateInput-Input"}}}}}},value:p,minDate:I,maxDate:W,range:y,clearable:F})]})}}const C=(0,o.b)(b)},87814:(t,e,s)=>{s.d(e,{K:()=>i});var a=s(50641);class i{constructor(){this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}manageFormClearListener(t,e,s){null!=this.formClearListener&&this.lastWidgetMgr===t&&this.lastFormId===e||(this.disconnect(),(0,a.bM)(e)&&(this.formClearListener=t.addFormClearedListener(e,s),this.lastWidgetMgr=t,this.lastFormId=e))}disconnect(){var t;null===(t=this.formClearListener)||void 0===t||t.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}}}]);