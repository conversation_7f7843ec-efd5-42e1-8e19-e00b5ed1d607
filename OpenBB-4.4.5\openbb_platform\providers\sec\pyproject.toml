[tool.poetry]
name = "openbb-sec"
version = "1.4.3"
description = "SEC extension for OpenBB"
authors = ["OpenBB Team <<EMAIL>>"]
license = "AGPL-3.0-only"
readme = "README.md"
packages = [{ include = "openbb_sec" }]

[tool.poetry.dependencies]
python = ">=3.9.21,<3.13"
openbb-core = "^1.4.6"
aiohttp-client-cache = "^0.11.0"
aiosqlite = "^0.20.0"
xmltodict = "^0.13.0"
beautifulsoup4 = "^4.12"
lxml = "^5.2.1"
trafilatura = "^2.0"
inscriptis = "^2.5.3"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.plugins."openbb_provider_extension"]
sec = "openbb_sec:sec_provider"
