{"version": 3, "file": "3230.29b02fdb14e1bdf52d07.js?v=29b02fdb14e1bdf52d07", "mappings": ";;;;;;;;;;AAAA;AACA;AACA,aAAa;AACb;AACA;AACA,qBAAqB,QAAQ;AAC7B;AACA,YAAY;AACZ,mBAAmB;AACnB,iBAAiB;;AAEjB;AACA;AACA;AACA;;AAEA;AACA,MAAM,kCAAkC,kBAAkB;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,OAAO,OAAO;;AAE7C;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,OAAO,OAAO;;AAE7C,+DAA+D;AAC/D;AACA,mCAAmC,OAAO,OAAO;;AAEjD,sEAAsE;AACtE;AACA;AACA,yCAAyC,OAAO,OAAO;;AAEvD;AACA,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,kBAAkB;AAClB,4BAA4B,kBAAkB;AAC9C;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,gBAAgB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,cAAc;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,gBAAgB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,2CAA2C;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0GAA0G,uBAAuB;;AAEjI;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,wBAAwB;AAChC;AACA,gCAAgC,kBAAkB;AAClD;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,0BAA0B,uCAAuC;AACjE;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,6CAA6C;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,4BAA4B,4CAA4C;;AAExE;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA,+BAA+B;AAC/B,wCAAwC;AACxC,qCAAqC;AACrC,6BAA6B;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,oBAAoB,QAAQ;AAC5B;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/xquery.js"], "sourcesContent": ["// The keywords object is set to the result of this self executing\n// function. Each keyword is a property of the keywords object whose\n// value is {type: atype, style: astyle}\nvar keywords = function(){\n  // convenience functions used to build keywords object\n  function kw(type) {return {type: type, style: \"keyword\"};}\n  var operator = kw(\"operator\")\n  , atom = {type: \"atom\", style: \"atom\"}\n  , punctuation = {type: \"punctuation\", style: null}\n  , qualifier = {type: \"axis_specifier\", style: \"qualifier\"};\n\n  // kwObj is what is return from this function at the end\n  var kwObj = {\n    ',': punctuation\n  };\n\n  // a list of 'basic' keywords. For each add a property to kwObj with the value of\n  // {type: basic[i], style: \"keyword\"} e.g. 'after' --> {type: \"after\", style: \"keyword\"}\n  var basic = ['after', 'all', 'allowing', 'ancestor', 'ancestor-or-self', 'any', 'array', 'as',\n               'ascending', 'at', 'attribute', 'base-uri', 'before', 'boundary-space', 'by', 'case', 'cast',\n               'castable', 'catch', 'child', 'collation', 'comment', 'construction', 'contains', 'content',\n               'context', 'copy', 'copy-namespaces', 'count', 'decimal-format', 'declare', 'default', 'delete',\n               'descendant', 'descendant-or-self', 'descending', 'diacritics', 'different', 'distance',\n               'document', 'document-node', 'element', 'else', 'empty', 'empty-sequence', 'encoding', 'end',\n               'entire', 'every', 'exactly', 'except', 'external', 'first', 'following', 'following-sibling',\n               'for', 'from', 'ftand', 'ftnot', 'ft-option', 'ftor', 'function', 'fuzzy', 'greatest', 'group',\n               'if', 'import', 'in', 'inherit', 'insensitive', 'insert', 'instance', 'intersect', 'into',\n               'invoke', 'is', 'item', 'language', 'last', 'lax', 'least', 'let', 'levels', 'lowercase', 'map',\n               'modify', 'module', 'most', 'namespace', 'next', 'no', 'node', 'nodes', 'no-inherit',\n               'no-preserve', 'not', 'occurs', 'of', 'only', 'option', 'order', 'ordered', 'ordering',\n               'paragraph', 'paragraphs', 'parent', 'phrase', 'preceding', 'preceding-sibling', 'preserve',\n               'previous', 'processing-instruction', 'relationship', 'rename', 'replace', 'return',\n               'revalidation', 'same', 'satisfies', 'schema', 'schema-attribute', 'schema-element', 'score',\n               'self', 'sensitive', 'sentence', 'sentences', 'sequence', 'skip', 'sliding', 'some', 'stable',\n               'start', 'stemming', 'stop', 'strict', 'strip', 'switch', 'text', 'then', 'thesaurus', 'times',\n               'to', 'transform', 'treat', 'try', 'tumbling', 'type', 'typeswitch', 'union', 'unordered',\n               'update', 'updating', 'uppercase', 'using', 'validate', 'value', 'variable', 'version',\n               'weight', 'when', 'where', 'wildcards', 'window', 'with', 'without', 'word', 'words', 'xquery'];\n  for(var i=0, l=basic.length; i < l; i++) { kwObj[basic[i]] = kw(basic[i]);};\n\n  // a list of types. For each add a property to kwObj with the value of\n  // {type: \"atom\", style: \"atom\"}\n  var types = ['xs:anyAtomicType', 'xs:anySimpleType', 'xs:anyType', 'xs:anyURI',\n               'xs:base64Binary', 'xs:boolean', 'xs:byte', 'xs:date', 'xs:dateTime', 'xs:dateTimeStamp',\n               'xs:dayTimeDuration', 'xs:decimal', 'xs:double', 'xs:duration', 'xs:ENTITIES', 'xs:ENTITY',\n               'xs:float', 'xs:gDay', 'xs:gMonth', 'xs:gMonthDay', 'xs:gYear', 'xs:gYearMonth', 'xs:hexBinary',\n               'xs:ID', 'xs:IDREF', 'xs:IDREFS', 'xs:int', 'xs:integer', 'xs:item', 'xs:java', 'xs:language',\n               'xs:long', 'xs:Name', 'xs:NCName', 'xs:negativeInteger', 'xs:NMTOKEN', 'xs:NMTOKENS',\n               'xs:nonNegativeInteger', 'xs:nonPositiveInteger', 'xs:normalizedString', 'xs:NOTATION',\n               'xs:numeric', 'xs:positiveInteger', 'xs:precisionDecimal', 'xs:QName', 'xs:short', 'xs:string',\n               'xs:time', 'xs:token', 'xs:unsignedByte', 'xs:unsignedInt', 'xs:unsignedLong',\n               'xs:unsignedShort', 'xs:untyped', 'xs:untypedAtomic', 'xs:yearMonthDuration'];\n  for(var i=0, l=types.length; i < l; i++) { kwObj[types[i]] = atom;};\n\n  // each operator will add a property to kwObj with value of {type: \"operator\", style: \"keyword\"}\n  var operators = ['eq', 'ne', 'lt', 'le', 'gt', 'ge', ':=', '=', '>', '>=', '<', '<=', '.', '|', '?', 'and', 'or', 'div', 'idiv', 'mod', '*', '/', '+', '-'];\n  for(var i=0, l=operators.length; i < l; i++) { kwObj[operators[i]] = operator;};\n\n  // each axis_specifiers will add a property to kwObj with value of {type: \"axis_specifier\", style: \"qualifier\"}\n  var axis_specifiers = [\"self::\", \"attribute::\", \"child::\", \"descendant::\", \"descendant-or-self::\", \"parent::\",\n                         \"ancestor::\", \"ancestor-or-self::\", \"following::\", \"preceding::\", \"following-sibling::\", \"preceding-sibling::\"];\n  for(var i=0, l=axis_specifiers.length; i < l; i++) { kwObj[axis_specifiers[i]] = qualifier; };\n\n  return kwObj;\n}();\n\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\n\n// the primary mode tokenizer\nfunction tokenBase(stream, state) {\n  var ch = stream.next(),\n      mightBeFunction = false,\n      isEQName = isEQNameAhead(stream);\n\n  // an XML tag (if not in some sub, chained tokenizer)\n  if (ch == \"<\") {\n    if(stream.match(\"!--\", true))\n      return chain(stream, state, tokenXMLComment);\n\n    if(stream.match(\"![CDATA\", false)) {\n      state.tokenize = tokenCDATA;\n      return \"tag\";\n    }\n\n    if(stream.match(\"?\", false)) {\n      return chain(stream, state, tokenPreProcessing);\n    }\n\n    var isclose = stream.eat(\"/\");\n    stream.eatSpace();\n    var tagName = \"\", c;\n    while ((c = stream.eat(/[^\\s\\u00a0=<>\\\"\\'\\/?]/))) tagName += c;\n\n    return chain(stream, state, tokenTag(tagName, isclose));\n  }\n  // start code block\n  else if(ch == \"{\") {\n    pushStateStack(state, { type: \"codeblock\"});\n    return null;\n  }\n  // end code block\n  else if(ch == \"}\") {\n    popStateStack(state);\n    return null;\n  }\n  // if we're in an XML block\n  else if(isInXmlBlock(state)) {\n    if(ch == \">\")\n      return \"tag\";\n    else if(ch == \"/\" && stream.eat(\">\")) {\n      popStateStack(state);\n      return \"tag\";\n    }\n    else\n      return \"variable\";\n  }\n  // if a number\n  else if (/\\d/.test(ch)) {\n    stream.match(/^\\d*(?:\\.\\d*)?(?:E[+\\-]?\\d+)?/);\n    return \"atom\";\n  }\n  // comment start\n  else if (ch === \"(\" && stream.eat(\":\")) {\n    pushStateStack(state, { type: \"comment\"});\n    return chain(stream, state, tokenComment);\n  }\n  // quoted string\n  else if (!isEQName && (ch === '\"' || ch === \"'\"))\n    return startString(stream, state, ch);\n  // variable\n  else if(ch === \"$\") {\n    return chain(stream, state, tokenVariable);\n  }\n  // assignment\n  else if(ch ===\":\" && stream.eat(\"=\")) {\n    return \"keyword\";\n  }\n  // open paren\n  else if(ch === \"(\") {\n    pushStateStack(state, { type: \"paren\"});\n    return null;\n  }\n  // close paren\n  else if(ch === \")\") {\n    popStateStack(state);\n    return null;\n  }\n  // open paren\n  else if(ch === \"[\") {\n    pushStateStack(state, { type: \"bracket\"});\n    return null;\n  }\n  // close paren\n  else if(ch === \"]\") {\n    popStateStack(state);\n    return null;\n  }\n  else {\n    var known = keywords.propertyIsEnumerable(ch) && keywords[ch];\n\n    // if there's a EQName ahead, consume the rest of the string portion, it's likely a function\n    if(isEQName && ch === '\\\"') while(stream.next() !== '\"'){}\n    if(isEQName && ch === '\\'') while(stream.next() !== '\\''){}\n\n    // gobble up a word if the character is not known\n    if(!known) stream.eatWhile(/[\\w\\$_-]/);\n\n    // gobble a colon in the case that is a lib func type call fn:doc\n    var foundColon = stream.eat(\":\");\n\n    // if there's not a second colon, gobble another word. Otherwise, it's probably an axis specifier\n    // which should get matched as a keyword\n    if(!stream.eat(\":\") && foundColon) {\n      stream.eatWhile(/[\\w\\$_-]/);\n    }\n    // if the next non whitespace character is an open paren, this is probably a function (if not a keyword of other sort)\n    if(stream.match(/^[ \\t]*\\(/, false)) {\n      mightBeFunction = true;\n    }\n    // is the word a keyword?\n    var word = stream.current();\n    known = keywords.propertyIsEnumerable(word) && keywords[word];\n\n    // if we think it's a function call but not yet known,\n    // set style to variable for now for lack of something better\n    if(mightBeFunction && !known) known = {type: \"function_call\", style: \"def\"};\n\n    // if the previous word was element, attribute, axis specifier, this word should be the name of that\n    if(isInXmlConstructor(state)) {\n      popStateStack(state);\n      return \"variable\";\n    }\n    // as previously checked, if the word is element,attribute, axis specifier, call it an \"xmlconstructor\" and\n    // push the stack so we know to look for it on the next word\n    if(word == \"element\" || word == \"attribute\" || known.type == \"axis_specifier\") pushStateStack(state, {type: \"xmlconstructor\"});\n\n    // if the word is known, return the details of that else just call this a generic 'word'\n    return known ? known.style : \"variable\";\n  }\n}\n\n// handle comments, including nested\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, maybeNested = false, nestedCount = 0, ch;\n  while (ch = stream.next()) {\n    if (ch == \")\" && maybeEnd) {\n      if(nestedCount > 0)\n        nestedCount--;\n      else {\n        popStateStack(state);\n        break;\n      }\n    }\n    else if(ch == \":\" && maybeNested) {\n      nestedCount++;\n    }\n    maybeEnd = (ch == \":\");\n    maybeNested = (ch == \"(\");\n  }\n\n  return \"comment\";\n}\n\n// tokenizer for string literals\n// optionally pass a tokenizer function to set state.tokenize back to when finished\nfunction tokenString(quote, f) {\n  return function(stream, state) {\n    var ch;\n    while (ch = stream.next()) {\n      if (ch == quote) {\n        popStateStack(state);\n        if (f) state.tokenize = f;\n        break;\n      } else if (stream.match(\"{\", false) && isInXmlAttributeBlock(state)) {\n        // if we're in a string and in an XML block, allow an embedded code block in an attribute\n        pushStateStack(state, { type: \"codeblock\"});\n        state.tokenize = tokenBase;\n        return \"string\";\n      }\n    }\n\n    return \"string\";\n  };\n}\n\nfunction startString(stream, state, quote, f) {\n  let tokenize = tokenString(quote, f);\n  pushStateStack(state, { type: \"string\", name: quote, tokenize });\n  return chain(stream, state, tokenize);\n}\n\n// tokenizer for variables\nfunction tokenVariable(stream, state) {\n  var isVariableChar = /[\\w\\$_-]/;\n\n  // a variable may start with a quoted EQName so if the next character is quote, consume to the next quote\n  if(stream.eat(\"\\\"\")) {\n    while(stream.next() !== '\\\"'){};\n    stream.eat(\":\");\n  } else {\n    stream.eatWhile(isVariableChar);\n    if(!stream.match(\":=\", false)) stream.eat(\":\");\n  }\n  stream.eatWhile(isVariableChar);\n  state.tokenize = tokenBase;\n  return \"variable\";\n}\n\n// tokenizer for XML tags\nfunction tokenTag(name, isclose) {\n  return function(stream, state) {\n    stream.eatSpace();\n    if(isclose && stream.eat(\">\")) {\n      popStateStack(state);\n      state.tokenize = tokenBase;\n      return \"tag\";\n    }\n    // self closing tag without attributes?\n    if(!stream.eat(\"/\"))\n      pushStateStack(state, { type: \"tag\", name: name, tokenize: tokenBase});\n    if(!stream.eat(\">\")) {\n      state.tokenize = tokenAttribute;\n      return \"tag\";\n    }\n    else {\n      state.tokenize = tokenBase;\n    }\n    return \"tag\";\n  };\n}\n\n// tokenizer for XML attributes\nfunction tokenAttribute(stream, state) {\n  var ch = stream.next();\n\n  if(ch == \"/\" && stream.eat(\">\")) {\n    if(isInXmlAttributeBlock(state)) popStateStack(state);\n    if(isInXmlBlock(state)) popStateStack(state);\n    return \"tag\";\n  }\n  if(ch == \">\") {\n    if(isInXmlAttributeBlock(state)) popStateStack(state);\n    return \"tag\";\n  }\n  if(ch == \"=\")\n    return null;\n  // quoted string\n  if (ch == '\"' || ch == \"'\")\n    return startString(stream, state, ch, tokenAttribute);\n\n  if(!isInXmlAttributeBlock(state))\n    pushStateStack(state, { type: \"attribute\", tokenize: tokenAttribute});\n\n  stream.eat(/[a-zA-Z_:]/);\n  stream.eatWhile(/[-a-zA-Z0-9_:.]/);\n  stream.eatSpace();\n\n  // the case where the attribute has not value and the tag was closed\n  if(stream.match(\">\", false) || stream.match(\"/\", false)) {\n    popStateStack(state);\n    state.tokenize = tokenBase;\n  }\n\n  return \"attribute\";\n}\n\n// handle comments, including nested\nfunction tokenXMLComment(stream, state) {\n  var ch;\n  while (ch = stream.next()) {\n    if (ch == \"-\" && stream.match(\"->\", true)) {\n      state.tokenize = tokenBase;\n      return \"comment\";\n    }\n  }\n}\n\n\n// handle CDATA\nfunction tokenCDATA(stream, state) {\n  var ch;\n  while (ch = stream.next()) {\n    if (ch == \"]\" && stream.match(\"]\", true)) {\n      state.tokenize = tokenBase;\n      return \"comment\";\n    }\n  }\n}\n\n// handle preprocessing instructions\nfunction tokenPreProcessing(stream, state) {\n  var ch;\n  while (ch = stream.next()) {\n    if (ch == \"?\" && stream.match(\">\", true)) {\n      state.tokenize = tokenBase;\n      return \"processingInstruction\";\n    }\n  }\n}\n\n\n// functions to test the current context of the state\nfunction isInXmlBlock(state) { return isIn(state, \"tag\"); }\nfunction isInXmlAttributeBlock(state) { return isIn(state, \"attribute\"); }\nfunction isInXmlConstructor(state) { return isIn(state, \"xmlconstructor\"); }\nfunction isInString(state) { return isIn(state, \"string\"); }\n\nfunction isEQNameAhead(stream) {\n  // assume we've already eaten a quote (\")\n  if(stream.current() === '\"')\n    return stream.match(/^[^\\\"]+\\\"\\:/, false);\n  else if(stream.current() === '\\'')\n    return stream.match(/^[^\\\"]+\\'\\:/, false);\n  else\n    return false;\n}\n\nfunction isIn(state, type) {\n  return (state.stack.length && state.stack[state.stack.length - 1].type == type);\n}\n\nfunction pushStateStack(state, newState) {\n  state.stack.push(newState);\n}\n\nfunction popStateStack(state) {\n  state.stack.pop();\n  var reinstateTokenize = state.stack.length && state.stack[state.stack.length-1].tokenize;\n  state.tokenize = reinstateTokenize || tokenBase;\n}\n\n// the interface for the mode API\nexport const xQuery = {\n  name: \"xquery\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      cc: [],\n      stack: []\n    };\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n    return style;\n  },\n\n  languageData: {\n    commentTokens: {block: {open: \"(:\", close: \":)\"}}\n  }\n};\n"], "names": [], "sourceRoot": ""}