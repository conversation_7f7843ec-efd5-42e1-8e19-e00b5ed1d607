#!/usr/bin/env python3
"""
AI实战经验学习引擎
从模拟炒股中积累经验，优化算法和策略
"""

import json
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import numpy as np


@dataclass
class TradingExperience:
    """交易经验记录"""
    trade_id: str
    symbol: str
    action: str  # 'buy' or 'sell'
    price: float
    quantity: int
    timestamp: str
    strategy_used: str  # 'pso', 'ga', 'manual'
    market_condition: str  # 'bull', 'bear', 'sideways'
    technical_indicators: Dict  # 当时的技术指标
    result_pnl: Optional[float] = None  # 最终盈亏
    result_return: Optional[float] = None  # 收益率
    holding_days: Optional[int] = None  # 持有天数
    success: Optional[bool] = None  # 是否成功


@dataclass
class MarketInsight:
    """市场洞察"""
    condition: str
    indicators: Dict
    successful_strategies: List[str]
    failed_strategies: List[str]
    avg_return: float
    confidence: float


class AIExperienceEngine:
    """AI经验学习引擎"""
    
    def __init__(self, db_path: str = "ai_experience.db"):
        self.db_path = db_path
        self._init_database()
        self.experiences: List[TradingExperience] = []
        self.insights: Dict[str, MarketInsight] = {}
        
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建交易经验表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_experiences (
                trade_id TEXT PRIMARY KEY,
                symbol TEXT,
                action TEXT,
                price REAL,
                quantity INTEGER,
                timestamp TEXT,
                strategy_used TEXT,
                market_condition TEXT,
                technical_indicators TEXT,
                result_pnl REAL,
                result_return REAL,
                holding_days INTEGER,
                success BOOLEAN
            )
        ''')
        
        # 创建市场洞察表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS market_insights (
                condition TEXT PRIMARY KEY,
                indicators TEXT,
                successful_strategies TEXT,
                failed_strategies TEXT,
                avg_return REAL,
                confidence REAL,
                updated_at TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def record_trade(self, experience: TradingExperience):
        """记录交易经验"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO trading_experiences 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            experience.trade_id,
            experience.symbol,
            experience.action,
            experience.price,
            experience.quantity,
            experience.timestamp,
            experience.strategy_used,
            experience.market_condition,
            json.dumps(experience.technical_indicators),
            experience.result_pnl,
            experience.result_return,
            experience.holding_days,
            experience.success
        ))
        
        conn.commit()
        conn.close()
        
        # 更新内存中的经验
        self.experiences.append(experience)
        
        # 如果交易已完成，分析经验
        if experience.result_pnl is not None:
            self._analyze_experience(experience)
    
    def _analyze_experience(self, experience: TradingExperience):
        """分析单次交易经验"""
        # 根据市场条件和策略分析成功率
        condition = experience.market_condition
        strategy = experience.strategy_used
        
        # 更新市场洞察
        self._update_market_insights(condition, strategy, experience)
    
    def _update_market_insights(self, condition: str, strategy: str, experience: TradingExperience):
        """更新市场洞察"""
        # 获取该市场条件下的所有经验
        similar_experiences = [
            exp for exp in self.experiences 
            if exp.market_condition == condition and exp.result_pnl is not None
        ]
        
        if len(similar_experiences) < 3:  # 样本太少
            return
        
        # 分析成功和失败的策略
        successful_strategies = []
        failed_strategies = []
        returns = []
        
        for exp in similar_experiences:
            if exp.success:
                if exp.strategy_used not in successful_strategies:
                    successful_strategies.append(exp.strategy_used)
            else:
                if exp.strategy_used not in failed_strategies:
                    failed_strategies.append(exp.strategy_used)
            
            if exp.result_return is not None:
                returns.append(exp.result_return)
        
        # 计算平均收益和置信度
        avg_return = np.mean(returns) if returns else 0.0
        confidence = min(len(similar_experiences) / 20.0, 1.0)  # 20个样本达到最高置信度
        
        # 创建或更新洞察
        insight = MarketInsight(
            condition=condition,
            indicators=experience.technical_indicators,
            successful_strategies=successful_strategies,
            failed_strategies=failed_strategies,
            avg_return=avg_return,
            confidence=confidence
        )
        
        self.insights[condition] = insight
        self._save_insight(insight)
    
    def _save_insight(self, insight: MarketInsight):
        """保存市场洞察到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO market_insights 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            insight.condition,
            json.dumps(insight.indicators),
            json.dumps(insight.successful_strategies),
            json.dumps(insight.failed_strategies),
            insight.avg_return,
            insight.confidence,
            datetime.now().isoformat()
        ))
        
        conn.commit()
        conn.close()
    
    def get_ai_recommendation(self, symbol: str, current_indicators: Dict, market_condition: str) -> Dict:
        """基于历史经验给出AI建议"""
        # 加载历史洞察
        self._load_insights()
        
        # 查找相似市场条件的经验
        relevant_insight = self.insights.get(market_condition)
        
        if not relevant_insight or relevant_insight.confidence < 0.3:
            return {
                "action": "观望",
                "confidence": 0.1,
                "reason": "历史经验不足，建议谨慎观望",
                "suggested_strategy": "manual",
                "risk_level": "高"
            }
        
        # 分析当前指标与历史成功案例的相似度
        similarity_score = self._calculate_indicator_similarity(
            current_indicators, 
            relevant_insight.indicators
        )
        
        # 推荐策略
        if relevant_insight.successful_strategies:
            suggested_strategy = relevant_insight.successful_strategies[0]
        else:
            suggested_strategy = "manual"
        
        # 生成建议
        if relevant_insight.avg_return > 0.05:  # 平均收益超过5%
            action = "买入"
            risk_level = "中等" if relevant_insight.confidence > 0.7 else "较高"
        elif relevant_insight.avg_return > -0.02:  # 小幅亏损
            action = "观望"
            risk_level = "中等"
        else:
            action = "避免"
            risk_level = "高"
        
        confidence = min(relevant_insight.confidence * similarity_score, 0.95)
        
        return {
            "action": action,
            "confidence": confidence,
            "reason": f"基于{len(self.experiences)}次历史交易经验，在{market_condition}市场条件下，{suggested_strategy}策略平均收益{relevant_insight.avg_return:.2%}",
            "suggested_strategy": suggested_strategy,
            "risk_level": risk_level,
            "historical_success_rate": len(relevant_insight.successful_strategies) / max(len(relevant_insight.successful_strategies) + len(relevant_insight.failed_strategies), 1),
            "sample_size": len([exp for exp in self.experiences if exp.market_condition == market_condition])
        }
    
    def _calculate_indicator_similarity(self, current: Dict, historical: Dict) -> float:
        """计算技术指标相似度"""
        if not current or not historical:
            return 0.5
        
        common_keys = set(current.keys()) & set(historical.keys())
        if not common_keys:
            return 0.5
        
        similarities = []
        for key in common_keys:
            try:
                curr_val = float(current[key])
                hist_val = float(historical[key])
                
                if hist_val == 0:
                    similarity = 1.0 if curr_val == 0 else 0.0
                else:
                    # 计算相对差异
                    diff = abs(curr_val - hist_val) / abs(hist_val)
                    similarity = max(0, 1 - diff)
                
                similarities.append(similarity)
            except (ValueError, TypeError):
                continue
        
        return np.mean(similarities) if similarities else 0.5
    
    def _load_insights(self):
        """从数据库加载洞察"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM market_insights')
        rows = cursor.fetchall()
        
        for row in rows:
            condition, indicators, successful, failed, avg_return, confidence, updated_at = row
            
            insight = MarketInsight(
                condition=condition,
                indicators=json.loads(indicators),
                successful_strategies=json.loads(successful),
                failed_strategies=json.loads(failed),
                avg_return=avg_return,
                confidence=confidence
            )
            
            self.insights[condition] = insight
        
        conn.close()
    
    def get_strategy_performance(self) -> Dict:
        """获取各策略的表现统计"""
        self._load_experiences()
        
        strategy_stats = {}
        
        for exp in self.experiences:
            if exp.result_return is None:
                continue
            
            strategy = exp.strategy_used
            if strategy not in strategy_stats:
                strategy_stats[strategy] = {
                    "total_trades": 0,
                    "successful_trades": 0,
                    "total_return": 0.0,
                    "returns": []
                }
            
            stats = strategy_stats[strategy]
            stats["total_trades"] += 1
            stats["total_return"] += exp.result_return
            stats["returns"].append(exp.result_return)
            
            if exp.success:
                stats["successful_trades"] += 1
        
        # 计算统计指标
        for strategy, stats in strategy_stats.items():
            if stats["total_trades"] > 0:
                stats["success_rate"] = stats["successful_trades"] / stats["total_trades"]
                stats["avg_return"] = stats["total_return"] / stats["total_trades"]
                stats["sharpe_ratio"] = np.mean(stats["returns"]) / max(np.std(stats["returns"]), 0.001)
            else:
                stats["success_rate"] = 0
                stats["avg_return"] = 0
                stats["sharpe_ratio"] = 0
        
        return strategy_stats
    
    def _load_experiences(self):
        """从数据库加载所有经验"""
        if self.experiences:  # 已加载
            return
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM trading_experiences')
        rows = cursor.fetchall()
        
        for row in rows:
            experience = TradingExperience(
                trade_id=row[0],
                symbol=row[1],
                action=row[2],
                price=row[3],
                quantity=row[4],
                timestamp=row[5],
                strategy_used=row[6],
                market_condition=row[7],
                technical_indicators=json.loads(row[8]) if row[8] else {},
                result_pnl=row[9],
                result_return=row[10],
                holding_days=row[11],
                success=row[12]
            )
            
            self.experiences.append(experience)
        
        conn.close()


# 全局AI经验引擎实例
ai_experience = AIExperienceEngine()
