# Python 3.12 + OpenBB 自动化配置脚本
# 检测Python 3.12并自动配置OpenBB环境

param(
    [switch]$Force,
    [string]$EnvName = "openbb_env_312"
)

Write-Host "🐍 Python 3.12 + OpenBB 自动化配置" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# 函数：检查命令是否存在
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# 函数：检查Python版本
function Test-PythonVersion {
    param($PythonCmd)
    try {
        $version = & $PythonCmd --version 2>&1
        if ($version -match "Python 3\.12\.") {
            return $true, $version
        }
        return $false, $version
    } catch {
        return $false, "未找到"
    }
}

# 1. 检查Python 3.12安装
Write-Host "🔍 检查Python 3.12安装..." -ForegroundColor Yellow

$python312Found = $false
$python312Cmd = ""
$python312Version = ""

# 检查py启动器
if (Test-Command "py") {
    Write-Host "   检查py启动器..." -ForegroundColor Cyan
    $hasVersion, $version = Test-PythonVersion "py -3.12"
    if ($hasVersion) {
        $python312Found = $true
        $python312Cmd = "py -3.12"
        $python312Version = $version
        Write-Host "   ✅ 通过py启动器找到: $version" -ForegroundColor Green
    }
}

# 检查直接python命令
if (-not $python312Found -and (Test-Command "python")) {
    Write-Host "   检查python命令..." -ForegroundColor Cyan
    $hasVersion, $version = Test-PythonVersion "python"
    if ($hasVersion) {
        $python312Found = $true
        $python312Cmd = "python"
        $python312Version = $version
        Write-Host "   ✅ 通过python命令找到: $version" -ForegroundColor Green
    }
}

# 检查python3命令
if (-not $python312Found -and (Test-Command "python3")) {
    Write-Host "   检查python3命令..." -ForegroundColor Cyan
    $hasVersion, $version = Test-PythonVersion "python3"
    if ($hasVersion) {
        $python312Found = $true
        $python312Cmd = "python3"
        $python312Version = $version
        Write-Host "   ✅ 通过python3命令找到: $version" -ForegroundColor Green
    }
}

# 如果没有找到Python 3.12
if (-not $python312Found) {
    Write-Host "❌ 未找到Python 3.12" -ForegroundColor Red
    Write-Host ""
    Write-Host "📥 请按以下步骤安装Python 3.12:" -ForegroundColor Yellow
    Write-Host "1. 访问: https://www.python.org/downloads/" -ForegroundColor White
    Write-Host "2. 下载Python 3.12.x (Windows installer 64-bit)" -ForegroundColor White
    Write-Host "3. 安装时务必勾选 'Add Python to PATH'" -ForegroundColor White
    Write-Host "4. 重新运行此脚本" -ForegroundColor White
    Write-Host ""
    
    # 询问是否打开下载页面
    $openBrowser = Read-Host "是否打开Python下载页面? (y/n)"
    if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
        Start-Process "https://www.python.org/downloads/"
    }
    
    exit 1
}

Write-Host "✅ Python 3.12检查通过: $python312Version" -ForegroundColor Green

# 2. 检查虚拟环境
Write-Host ""
Write-Host "📦 检查虚拟环境..." -ForegroundColor Yellow

if (Test-Path $EnvName) {
    if ($Force) {
        Write-Host "   🗑️ 删除现有环境 (Force模式)..." -ForegroundColor Yellow
        Remove-Item -Recurse -Force $EnvName
    } else {
        Write-Host "   ⚠️ 虚拟环境已存在: $EnvName" -ForegroundColor Yellow
        $recreate = Read-Host "是否重新创建? (y/n)"
        if ($recreate -eq "y" -or $recreate -eq "Y") {
            Remove-Item -Recurse -Force $EnvName
        } else {
            Write-Host "   使用现有环境" -ForegroundColor Cyan
            $useExisting = $true
        }
    }
}

# 3. 创建虚拟环境
if (-not $useExisting) {
    Write-Host "   🔨 创建虚拟环境: $EnvName" -ForegroundColor Cyan
    try {
        if ($python312Cmd -eq "py -3.12") {
            & py -3.12 -m venv $EnvName
        } else {
            & $python312Cmd -m venv $EnvName
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ 虚拟环境创建成功" -ForegroundColor Green
        } else {
            throw "虚拟环境创建失败"
        }
    } catch {
        Write-Host "   ❌ 虚拟环境创建失败: $_" -ForegroundColor Red
        exit 1
    }
}

# 4. 安装OpenBB
Write-Host ""
Write-Host "📦 安装OpenBB组件..." -ForegroundColor Yellow

$pipCmd = ".\$EnvName\Scripts\pip.exe"
$pythonCmd = ".\$EnvName\Scripts\python.exe"

try {
    # 升级pip
    Write-Host "   📦 升级pip..." -ForegroundColor Cyan
    & $pythonCmd -m pip install --upgrade pip
    
    # 安装OpenBB核心
    Write-Host "   📦 安装OpenBB核心..." -ForegroundColor Cyan
    & $pipCmd install openbb-core
    
    # 安装基本扩展
    Write-Host "   📦 安装OpenBB扩展..." -ForegroundColor Cyan
    & $pipCmd install openbb-yfinance openbb-equity openbb-economy openbb-news
    
    # 安装额外工具
    Write-Host "   📦 安装分析工具..." -ForegroundColor Cyan
    & $pipCmd install pandas numpy matplotlib seaborn openpyxl jupyter
    
    Write-Host "   ✅ 所有组件安装完成" -ForegroundColor Green
    
} catch {
    Write-Host "   ❌ 安装过程中出错: $_" -ForegroundColor Red
    exit 1
}

# 5. 验证安装
Write-Host ""
Write-Host "🧪 验证安装..." -ForegroundColor Yellow

try {
    # 测试Python版本
    $envPythonVersion = & $pythonCmd --version
    Write-Host "   ✅ 环境Python版本: $envPythonVersion" -ForegroundColor Green
    
    # 测试OpenBB导入
    $testResult = & $pythonCmd -c "from openbb_core.app.static.app_factory import create_app; app = create_app(); print('OpenBB导入成功')" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ OpenBB导入测试通过" -ForegroundColor Green
    } else {
        Write-Host "   ❌ OpenBB导入测试失败: $testResult" -ForegroundColor Red
    }
    
    # 测试数据处理
    $pandasTest = & $pythonCmd -c "import pandas as pd; import numpy as np; print('数据处理库正常')" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ 数据处理库测试通过" -ForegroundColor Green
    } else {
        Write-Host "   ❌ 数据处理库测试失败: $pandasTest" -ForegroundColor Red
    }
    
} catch {
    Write-Host "   ❌ 验证过程中出错: $_" -ForegroundColor Red
}

# 6. 创建激活脚本
Write-Host ""
Write-Host "📝 创建便捷脚本..." -ForegroundColor Yellow

$activateScript = @"
@echo off
echo 🐍 激活Python 3.12 OpenBB环境...
echo ================================

call $EnvName\Scripts\activate.bat

echo ✅ Python 3.12 OpenBB环境已激活！
echo 当前Python版本:
python --version

echo.
echo 💡 OpenBB已准备就绪！
echo - 运行测试: python test_openbb_venv.py
echo - 开始分析: python your_script.py  
echo - 启动Jupyter: jupyter notebook
echo - 退出环境: deactivate
echo.

cmd /k
"@

$activateScript | Out-File -FilePath "activate_python312.bat" -Encoding ASCII
Write-Host "   ✅ 创建激活脚本: activate_python312.bat" -ForegroundColor Green

# 7. 导出依赖列表
Write-Host "   📋 导出依赖列表..." -ForegroundColor Cyan
& $pipCmd freeze | Out-File -FilePath "requirements_python312.txt" -Encoding UTF8
Write-Host "   ✅ 依赖列表: requirements_python312.txt" -ForegroundColor Green

# 8. 完成总结
Write-Host ""
Write-Host "🎉 Python 3.12 + OpenBB 配置完成！" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""
Write-Host "📊 配置总结:" -ForegroundColor Cyan
Write-Host "   Python版本: $python312Version" -ForegroundColor White
Write-Host "   虚拟环境: $EnvName" -ForegroundColor White
Write-Host "   激活脚本: activate_python312.bat" -ForegroundColor White
Write-Host "   依赖列表: requirements_python312.txt" -ForegroundColor White
Write-Host ""
Write-Host "🚀 快速开始:" -ForegroundColor Cyan
Write-Host "   1. 双击运行: activate_python312.bat" -ForegroundColor White
Write-Host "   2. 或手动激活: $EnvName\Scripts\activate" -ForegroundColor White
Write-Host "   3. 开始编程: python your_script.py" -ForegroundColor White
Write-Host ""
Write-Host "💡 下一步建议:" -ForegroundColor Cyan
Write-Host "   - 运行测试验证: python test_openbb_venv.py" -ForegroundColor White
Write-Host "   - 启动Jupyter: jupyter notebook" -ForegroundColor White
Write-Host "   - 查看示例: python openbb_demo.py" -ForegroundColor White
Write-Host ""

# 询问是否立即激活环境
$activateNow = Read-Host "是否立即激活环境? (y/n)"
if ($activateNow -eq "y" -or $activateNow -eq "Y") {
    Write-Host "🚀 启动环境..." -ForegroundColor Green
    Start-Process "activate_python312.bat"
}
