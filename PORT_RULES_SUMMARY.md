# 端口使用规则总结

## 🚨 重要规则：固定使用6900端口

根据您的要求，我已经在`AI_GUIDE.md`中明确规定了端口使用规则，确保所有AI开发者都能一读就明白并严格遵守。

## ✅ 已更新的文档内容

### 1. **开头重要提示**
```markdown
### **🚨 端口使用规则（严格遵守）**
- ✅ **固定端口6900**：系统必须使用6900端口，不允许更改
- ✅ **端口占用处理**：如果6900端口被占用，说明服务已启动，需要先关闭现有服务
- ❌ **禁止使用其他端口**：不得使用8000、8001等其他端口
- ❌ **禁止端口冲突**：启动前必须确保6900端口可用
```

### 2. **严禁错误列表**
新增了端口相关的严禁错误：
```markdown
- ❌ **使用6900以外的端口启动服务**
- ❌ **端口占用时强制启动或更换端口**
```

### 3. **环境变量说明**
```markdown
- `APP_PORT`：**固定 `6900`**（不允许更改，系统标准端口）
```

### 4. **运行命令注释**
```bash
uvicorn openbb_cn.api.app:app --host 0.0.0.0 --port 6900 --reload  # 必须使用6900端口
```

### 5. **Docker配置说明**
```markdown
- 监听端口：对外暴露 `6900`（**固定端口，不允许修改**）
```

### 6. **API文档说明**
```markdown
- Base URL：`http://127.0.0.1:6900`（**固定6900端口，不可更改**）
```

### 7. **故障排查更新**
```markdown
- **端口占用**：如果6900端口被占用，说明服务已启动，需要先关闭现有服务，**不得使用其他端口**
```

### 8. **规则与维护约定**
新增了详细的端口使用规则：
```markdown
- **端口使用规则**：
  - **必须使用6900端口**：系统固定使用6900端口，任何情况下都不允许更改
  - **端口占用处理**：如果6900端口被占用，说明服务已启动，必须先关闭现有服务
  - **禁止端口变更**：不得使用8000、8001等其他端口，不得在代码中修改端口配置
  - **服务管理**：启动前检查端口状态，确保6900端口可用
```

## 🎯 规则的重要性

### 1. **系统一致性**
- 确保所有环境（开发、测试、生产）使用相同端口
- 避免端口冲突和配置混乱
- 保持文档和实际部署的一致性

### 2. **运维标准化**
- 固定端口便于监控和管理
- 简化防火墙和负载均衡配置
- 减少部署错误和故障排查时间

### 3. **团队协作**
- 所有开发者使用相同的访问地址
- 避免因端口不同导致的沟通问题
- 统一的开发和测试环境

### 4. **AI开发规范**
- 让AI一读就明白端口规则
- 防止AI在开发过程中随意更改端口
- 确保AI生成的代码符合项目规范

## 🔧 实际执行情况

### 当前状态
- ✅ 服务器已在6900端口正常运行
- ✅ 健康检查通过：http://localhost:6900/health
- ✅ 主页面可访问：http://localhost:6900/app
- ✅ 所有API端点使用6900端口

### 端口检查命令
```bash
# 检查6900端口状态
netstat -ano | findstr :6900

# 如果端口被占用，关闭所有Python进程
taskkill /f /im python.exe

# 使用正确端口启动服务
python -m uvicorn openbb_cn.api.app:app --host 0.0.0.0 --port 6900 --reload
```

## 📋 AI开发者必读规则

### 启动服务时
1. **检查端口**: 确认6900端口是否可用
2. **关闭冲突**: 如果端口被占用，先关闭现有服务
3. **使用正确端口**: 必须使用6900端口启动
4. **验证服务**: 启动后访问http://localhost:6900/health验证

### 开发过程中
1. **不得更改端口**: 任何代码中都不能修改端口配置
2. **不得使用其他端口**: 禁止使用8000、8001等端口
3. **文档一致性**: 所有文档和示例都必须使用6900端口
4. **测试验证**: 所有测试都必须基于6900端口

### 故障排查时
1. **端口冲突**: 优先检查6900端口状态
2. **服务重启**: 关闭现有服务后重新启动
3. **不得绕过**: 不能通过更换端口来解决问题
4. **根本解决**: 必须解决6900端口的占用问题

## 🎉 版本更新记录

### v0.2.4：端口使用规则明确
- **文档更新**: 在AI_GUIDE.md中明确规定6900端口规则
- **多处强调**: 在8个不同章节中强调端口规则
- **严禁列表**: 新增端口相关的严禁错误
- **故障排查**: 更新端口占用的处理方法
- **规则约定**: 详细的端口使用规范和维护约定

## 🔍 规则验证

### 文档检查清单
- ✅ 开头重要提示包含端口规则
- ✅ 严禁错误列表包含端口限制
- ✅ 环境变量说明标注固定端口
- ✅ 运行命令注释端口要求
- ✅ Docker配置说明端口固定
- ✅ API文档标注端口不可更改
- ✅ 故障排查包含端口处理
- ✅ 规则约定详细说明端口规范
- ✅ Changelog记录端口规则更新

### 实际运行验证
- ✅ 服务在6900端口正常运行
- ✅ 所有API端点响应正常
- ✅ 前端页面正常访问
- ✅ 健康检查通过

## 📖 给AI开发者的明确指令

**重要提醒**: 
1. **端口6900是系统标准，绝对不允许更改**
2. **端口占用时必须关闭现有服务，不得使用其他端口**
3. **所有开发、测试、部署都必须使用6900端口**
4. **违反端口规则的代码不得提交**

这些规则现在已经写入`AI_GUIDE.md`的多个章节中，确保任何AI开发者都能一读就明白并严格遵守。

---

**总结**: 端口使用规则已经全面写入项目文档，成为项目开发的强制性规范。所有AI开发者都必须严格遵守6900端口规则，确保系统的一致性和稳定性。🚀
