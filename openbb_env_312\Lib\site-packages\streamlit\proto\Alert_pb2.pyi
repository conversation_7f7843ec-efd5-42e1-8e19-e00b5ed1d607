"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2024)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""
import builtins
import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class Alert(google.protobuf.message.Message):
    """NOTE: This proto type is used by some external services so needs to remain
    relatively stable. While it isn't entirely set in stone, changing it
    may require a good amount of effort so should be avoided if possible.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Format:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _FormatEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[Alert._Format.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        UNUSED: Alert._Format.ValueType  # 0
        """Plain, fixed width text."""
        ERROR: Alert._Format.ValueType  # 1
        """Shows an error message."""
        WARNING: Alert._Format.ValueType  # 2
        """Shows a warning message."""
        INFO: Alert._Format.ValueType  # 3
        """Shows an info log."""
        SUCCESS: Alert._Format.ValueType  # 4
        """Shows a success message."""

    class Format(_Format, metaclass=_FormatEnumTypeWrapper):
        """Type of Alert"""

    UNUSED: Alert.Format.ValueType  # 0
    """Plain, fixed width text."""
    ERROR: Alert.Format.ValueType  # 1
    """Shows an error message."""
    WARNING: Alert.Format.ValueType  # 2
    """Shows a warning message."""
    INFO: Alert.Format.ValueType  # 3
    """Shows an info log."""
    SUCCESS: Alert.Format.ValueType  # 4
    """Shows a success message."""

    BODY_FIELD_NUMBER: builtins.int
    FORMAT_FIELD_NUMBER: builtins.int
    ICON_FIELD_NUMBER: builtins.int
    body: builtins.str
    """Content to display."""
    format: global___Alert.Format.ValueType
    icon: builtins.str
    def __init__(
        self,
        *,
        body: builtins.str = ...,
        format: global___Alert.Format.ValueType = ...,
        icon: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["body", b"body", "format", b"format", "icon", b"icon"]) -> None: ...

global___Alert = Alert
