[tool.poetry]
name = "openbb-tradier"
version = "1.3.1"
description = "Tradier Provider Extension for the OpenBB Platform"
authors = ["OpenBB <<EMAIL>>"]
license = "AGPL-3.0-only"
readme = "README.md"
packages = [{ include = "openbb_tradier" }]

[tool.poetry.dependencies]
python = ">=3.9.21,<3.13"
openbb-core = "^1.4.6"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.plugins."openbb_provider_extension"]
tradier = "openbb_tradier:tradier_provider"
