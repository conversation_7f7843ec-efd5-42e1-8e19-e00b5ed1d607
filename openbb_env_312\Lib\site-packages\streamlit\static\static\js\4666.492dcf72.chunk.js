"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[4666],{90186:(e,t,i)=>{i.d(t,{$:()=>p,Z:()=>c});var s=i(66845),n=i(25621),l=i(66694),a=i(92627),r=i(38570),o=i(80318),d=i(40864);let p;!function(e){e.EXTRASMALL="xs",e.SMALL="sm",e.MEDIUM="md",e.LARGE="lg",e.EXTRALARGE="xl"}(p||(p={}));const c=function(e){let{value:t,width:i,size:c=p.SMALL,overrides:g}=e;const h=(0,n.u)(),u={xs:h.spacing.twoXS,sm:h.spacing.sm,md:h.spacing.lg,lg:h.spacing.xl,xl:h.spacing.twoXL},{activeTheme:m}=s.useContext(l.E),f=!(0,a.MJ)(m),x={BarContainer:{style:{marginTop:h.spacing.none,marginBottom:h.spacing.none,marginRight:h.spacing.none,marginLeft:h.spacing.none}},Bar:{style:e=>{let{$theme:t}=e;return{width:i?i.toString():void 0,marginTop:h.spacing.none,marginBottom:h.spacing.none,marginRight:h.spacing.none,marginLeft:h.spacing.none,height:u[c],backgroundColor:t.colors.progressbarTrackFill,borderTopLeftRadius:h.spacing.twoXS,borderTopRightRadius:h.spacing.twoXS,borderBottomLeftRadius:h.spacing.twoXS,borderBottomRightRadius:h.spacing.twoXS}}},BarProgress:{style:()=>({backgroundColor:f?h.colors.primary:h.colors.blue70,borderTopLeftRadius:h.spacing.twoXS,borderTopRightRadius:h.spacing.twoXS,borderBottomLeftRadius:h.spacing.twoXS,borderBottomRightRadius:h.spacing.twoXS})}};return(0,d.jsx)(r.Z,{value:t,overrides:(0,o.aO)(x,g)})}},77367:(e,t,i)=>{i.d(t,{R:()=>s});class s{setStatus(e){return new s(this.name,this.size,this.id,e)}constructor(e,t,i,s){this.name=void 0,this.size=void 0,this.status=void 0,this.id=void 0,this.name=e,this.size=t,this.id=i,this.status=s}}},14666:(e,t,i)=>{i.r(t),i.d(t,{default:()=>he});var s=i(18080),n=i(62813),l=i.n(n),a=i(84693),r=i.n(a),o=i(66845),d=i(16295),p=i(87814),c=i(50641);let g;!function(e){e.Gigabyte="gb",e.Megabyte="mb",e.Kilobyte="kb",e.Byte="b"}(g||(g={}));const h=(0,c.rA)()?1024:1e3,u=[g.Gigabyte,g.Megabyte,g.Kilobyte,g.Byte],m=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;if(t||(t=g.Byte),i<0&&(i=0),e<0)throw new Error("Size must be greater than or equal to 0");const s=u.indexOf(t);return s&&e>h/2?m(e/h,u[s-1],i):"".concat(e.toFixed(i)).concat(t.toUpperCase())};var f=i(98478),x=i(86659),w=i(8879),y=i(68411),b=i(51622),F=i(9003),S=i(81354),v=i(1515);const j=(0,v.Z)("section",{target:"e1b2p2ww15"})((e=>{let{isDisabled:t,theme:i}=e;return{display:"flex",alignItems:"center",padding:i.spacing.lg,backgroundColor:i.colors.secondaryBg,borderRadius:i.radii.lg,":focus":{outline:"none"},":focus-visible":{boxShadow:"0 0 0 1px ".concat(i.colors.primary)},color:t?i.colors.gray:i.colors.bodyText}}),""),I=(0,v.Z)("div",{target:"e1b2p2ww14"})((()=>({marginRight:"auto",alignItems:"center",display:"flex"})),""),U=(0,v.Z)("span",{target:"e1b2p2ww13"})((e=>{let{theme:t}=e;return{color:t.colors.darkenedBgMix100,marginRight:t.spacing.lg}}),""),M=(0,v.Z)("span",{target:"e1b2p2ww12"})((e=>{let{theme:t}=e;return{marginBottom:t.spacing.twoXS}}),""),z=(0,v.Z)("div",{target:"e1b2p2ww11"})({name:"1fttcpj",styles:"display:flex;flex-direction:column"}),L=(0,v.Z)("div",{target:"e1b2p2ww10"})((e=>{let{theme:t}=e;return{left:0,right:0,lineHeight:t.lineHeights.tight,paddingTop:t.spacing.md,paddingLeft:t.spacing.lg,paddingRight:t.spacing.lg}}),""),C=(0,v.Z)("ul",{target:"e1b2p2ww9"})((()=>({listStyleType:"none",marginBottom:0})),""),B=(0,v.Z)("li",{target:"e1b2p2ww8"})((e=>{let{theme:t}=e;return{margin:t.spacing.none,padding:t.spacing.none}}),""),Z=(0,v.Z)("div",{target:"e1b2p2ww7"})((e=>{let{theme:t}=e;return{display:"flex",alignItems:"baseline",flex:1,paddingLeft:t.spacing.lg,overflow:"hidden"}}),""),R=(0,v.Z)("div",{target:"e1b2p2ww6"})((e=>{let{theme:t}=e;return{marginRight:t.spacing.sm,marginBottom:t.spacing.twoXS,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}}),""),k=(0,v.Z)("div",{target:"e1b2p2ww5"})((e=>{let{theme:t}=e;return{display:"flex",alignItems:"center",marginBottom:t.spacing.twoXS}}),""),T=(0,v.Z)("span",{target:"e1b2p2ww4"})((e=>{let{theme:t}=e;return{marginRight:t.spacing.twoXS}}),""),D=(0,v.Z)("div",{target:"e1b2p2ww3"})((e=>{let{theme:t}=e;return{display:"flex",padding:t.spacing.twoXS,color:t.colors.darkenedBgMix100}}),""),X=(0,v.Z)("small",{target:"e1b2p2ww2"})((e=>{let{theme:t}=e;return{color:t.colors.danger,fontSize:t.fontSizes.sm,height:t.fontSizes.sm,lineHeight:t.fontSizes.sm,display:"flex",alignItems:"center",whiteSpace:"nowrap"}}),""),P=(0,v.Z)("span",{target:"e1b2p2ww1"})({name:"0",styles:""}),E=e=>({[j]:{display:"flex",flexDirection:"column",alignItems:"flex-start"},[I]:{marginBottom:e.spacing.lg},[U]:{display:"none"},[L]:{paddingRight:e.spacing.lg},[k]:{maxWidth:"inherit",flex:1,alignItems:"flex-start",marginBottom:e.spacing.sm},[R]:{width:e.sizes.full},[Z]:{flexDirection:"column"},[X]:{height:"auto",whiteSpace:"initial"},[P]:{display:"none"},[B]:{margin:e.spacing.none,padding:e.spacing.none}}),A=(0,v.Z)("div",{target:"e1b2p2ww0"})((e=>{let{theme:t}=e;return t.inSidebar?E(t):{["@media (max-width: ".concat(t.breakpoints.sm,")")]:E(t)}}),"");var V=i(74529),N=i(46927),W=i(33746),O=i(40864);const H=e=>{let{multiple:t,acceptedExtensions:i,maxSizeBytes:s}=e;return(0,O.jsxs)(I,{"data-testid":"stFileUploaderDropzoneInstructions",children:[(0,O.jsx)(U,{children:(0,O.jsx)(N.Z,{content:V.n,size:"threeXL"})}),(0,O.jsxs)(z,{children:[(0,O.jsxs)(M,{children:["Drag and drop file",t?"s":""," here"]}),(0,O.jsxs)(W.x,{children:["Limit ".concat(m(s,g.Byte,0)," per file"),i.length?" \u2022 ".concat(i.map((e=>e.replace(/^\./,"").toUpperCase())).join(", ")):null]})]})]})},_=e=>{let{onDrop:t,multiple:i,acceptedExtensions:s,maxSizeBytes:n,disabled:l,label:a}=e;return(0,O.jsx)(b.ZP,{onDrop:t,multiple:i,accept:s.length?s:void 0,maxSize:n,disabled:l,useFsAccessApi:!1,children:e=>{let{getRootProps:t,getInputProps:r}=e;return(0,O.jsxs)(j,{...t(),"data-testid":"stFileUploaderDropzone",isDisabled:l,"aria-label":a,children:[(0,O.jsx)("input",{"data-testid":"stFileUploaderDropzoneInput",...r()}),(0,O.jsx)(H,{multiple:i,acceptedExtensions:s,maxSizeBytes:n}),(0,O.jsx)(F.ZP,{kind:S.nW.SECONDARY,disabled:l,size:S.V5.SMALL,children:"Browse files"})]})}})};var G=i(13005),K=i.n(G),$=i(30351),q=i(14609);const J=(0,v.Z)("div",{target:"e16k0npc1"})((e=>{let{theme:t}=e;return{display:"flex",alignItems:"center",justifyContent:"space-between",paddingBottom:t.spacing.twoXS,marginBottom:t.spacing.twoXS}}),""),Y=(0,v.Z)("div",{target:"e16k0npc0"})((e=>{let{theme:t}=e;return{display:"flex",alignItems:"center",justifyContent:"center",color:t.colors.fadedText40}}),""),Q=e=>{let{className:t,currentPage:i,totalPages:s,onNext:n,onPrevious:l}=e;return(0,O.jsxs)(J,{className:t,"data-testid":"stPagination",children:[(0,O.jsx)(W.x,{children:"Showing page ".concat(i," of ").concat(s)}),(0,O.jsxs)(Y,{children:[(0,O.jsx)(F.ZP,{onClick:l,kind:S.nW.MINIMAL,children:(0,O.jsx)(N.Z,{content:$.s,size:"xl"})}),(0,O.jsx)(F.ZP,{onClick:n,kind:S.nW.MINIMAL,children:(0,O.jsx)(N.Z,{content:q._,size:"xl"})})]})]})};var ee=i(88235);const te=(e,t)=>Math.ceil(e.length/t),ie=e=>K()((t=>{let{pageSize:i,items:s,resetOnAdd:n,...l}=t;const[a,r]=(0,o.useState)(0),[d,p]=(0,o.useState)(te(s,i)),c=(0,ee.D)(s);(0,o.useEffect)((()=>{c&&c.length!==s.length&&p(te(s,i)),c&&c.length<s.length?n&&r(0):a+1>=d&&r(d-1)}),[s,a,i,c,n,d]);const g=s.slice(a*i,a*i+i);return(0,O.jsxs)(O.Fragment,{children:[(0,O.jsx)(e,{items:g,...l}),s.length>i?(0,O.jsx)(Q,{className:"streamlit-paginator",pageSize:i,totalPages:d,currentPage:a+1,onNext:()=>{r(Math.min(a+1,d-1))},onPrevious:()=>{r(Math.max(0,a-1))}}):null]})}),e);var se=i(62288),ne=i(87847),le=i(31197),ae=i(90186);const re=e=>{let{fileInfo:t}=e;return"uploading"===t.status.type?(0,O.jsx)(ae.Z,{value:t.status.progress,size:ae.$.SMALL,overrides:{Bar:{style:{marginLeft:0,marginTop:"4px"}}}}):"error"===t.status.type?(0,O.jsxs)(X,{children:[(0,O.jsx)(T,{"data-testid":"stFileUploaderFileErrorMessage",children:t.status.errorMessage}),(0,O.jsx)(P,{children:(0,O.jsx)(N.Z,{content:se.j,size:"lg"})})]}):"uploaded"===t.status.type?(0,O.jsx)(W.x,{children:m(t.size,g.Byte)}):null},oe=e=>{let{fileInfo:t,onDelete:i}=e;return(0,O.jsxs)(k,{className:"stFileUploaderFile","data-testid":"stFileUploaderFile",children:[(0,O.jsx)(D,{children:(0,O.jsx)(N.Z,{content:ne.h,size:"twoXL"})}),(0,O.jsxs)(Z,{className:"stFileUploaderFileData",children:[(0,O.jsx)(R,{className:"stFileUploaderFileName","data-testid":"stFileUploaderFileName",title:t.name,children:t.name}),(0,O.jsx)(re,{fileInfo:t})]}),(0,O.jsx)("div",{"data-testid":"stFileUploaderDeleteBtn",children:(0,O.jsx)(F.ZP,{onClick:()=>i(t.id),kind:S.nW.MINIMAL,children:(0,O.jsx)(N.Z,{content:le.U,size:"lg"})})})]})},de=ie((e=>{let{items:t,onDelete:i}=e;return(0,O.jsx)(C,{children:t.map((e=>(0,O.jsx)(B,{children:(0,O.jsx)(oe,{fileInfo:e,onDelete:i})},e.id)))})})),pe=e=>(0,O.jsx)(L,{children:(0,O.jsx)(de,{...e})});var ce=i(77367);class ge extends o.PureComponent{constructor(e){super(e),this.formClearHelper=new p.K,this.localFileIdCounter=1,this.forceUpdatingStatus=!1,this.componentDidUpdate=()=>{if("ready"!==this.status)return;const e=this.createWidgetValue(),{element:t,widgetMgr:i,fragmentId:s}=this.props,n=i.getFileUploaderStateValue(t);l()(e,n)||i.setFileUploaderStateValue(t,e,{fromUi:!0},s)},this.reset=()=>{this.setState({files:[]})},this.dropHandler=(e,t)=>{const{element:i}=this.props,{multipleFiles:s}=i;if(!s&&0===e.length&&t.length>1){const i=t.findIndex((e=>1===e.errors.length&&"too-many-files"===e.errors[0].code));i>=0&&(e.push(t[i].file),t.splice(i,1))}if(this.props.uploadClient.fetchFileURLs(e).then((t=>{if(!s&&e.length>0){const e=this.state.files.find((e=>"error"!==e.status.type));e&&(this.forceUpdatingStatus=!0,this.deleteFile(e.id),this.forceUpdatingStatus=!1)}r()(t,e).forEach((e=>{let[t,i]=e;this.uploadFile(t,i)}))})).catch((t=>{this.addFiles(e.map((e=>new ce.R(e.name,e.size,this.nextLocalFileId(),{type:"error",errorMessage:t}))))})),t.length>0){const e=t.map((e=>{const{file:t}=e;return new ce.R(t.name,t.size,this.nextLocalFileId(),{type:"error",errorMessage:this.getErrorMessage(e.errors[0].code,e.file)})}));this.addFiles(e)}},this.uploadFile=(e,t)=>{const i=s.Z.CancelToken.source(),n=new ce.R(t.name,t.size,this.nextLocalFileId(),{type:"uploading",cancelToken:i,progress:1});this.addFile(n),this.props.uploadClient.uploadFile(this.props.element,e.uploadUrl,t,(e=>this.onUploadProgress(e,n.id)),i.token).then((()=>this.onUploadComplete(n.id,e))).catch((e=>{s.Z.isCancel(e)||this.updateFile(n.id,n.setStatus({type:"error",errorMessage:e?e.toString():"Unknown error"}))}))},this.onUploadComplete=(e,t)=>{const i=this.getFile(e);null!=i&&"uploading"===i.status.type&&this.updateFile(i.id,i.setStatus({type:"uploaded",fileId:t.fileId,fileUrls:t}))},this.getErrorMessage=(e,t)=>{switch(e){case"file-too-large":return"File must be ".concat(m(this.maxUploadSizeInBytes,g.Byte)," or smaller.");case"file-invalid-type":return"".concat(t.type," files are not allowed.");case"file-too-small":return"File size is too small.";case"too-many-files":return"Only one file is allowed.";default:return"Unexpected error. Please try again."}},this.deleteFile=e=>{const t=this.getFile(e);null!=t&&("uploading"===t.status.type&&t.status.cancelToken.cancel(),"uploaded"===t.status.type&&t.status.fileUrls.deleteUrl&&this.props.uploadClient.deleteFile(t.status.fileUrls.deleteUrl),this.removeFile(e))},this.addFile=e=>{this.setState((t=>({files:[...t.files,e]})))},this.addFiles=e=>{this.setState((t=>({files:[...t.files,...e]})))},this.removeFile=e=>{this.setState((t=>({files:t.files.filter((t=>t.id!==e))})))},this.getFile=e=>this.state.files.find((t=>t.id===e)),this.updateFile=(e,t)=>{this.setState((i=>({files:i.files.map((i=>i.id===e?t:i))})))},this.onUploadProgress=(e,t)=>{const i=this.getFile(t);if(null==i||"uploading"!==i.status.type)return;const s=Math.round(100*e.loaded/e.total);i.status.progress!==s&&this.updateFile(t,i.setStatus({type:"uploading",cancelToken:i.status.cancelToken,progress:s}))},this.onFormCleared=()=>{this.setState({files:[]},(()=>{const e=this.createWidgetValue();if(null==e)return;const{widgetMgr:t,element:i,fragmentId:s}=this.props;t.setFileUploaderStateValue(i,e,{fromUi:!0},s)}))},this.state=this.initialValue}get initialValue(){const e={files:[],newestServerFileId:0},{widgetMgr:t,element:i}=this.props,s=t.getFileUploaderStateValue(i);if(null==s)return e;const{uploadedFileInfo:n}=s;return null==n||0===n.length?e:{files:n.map((e=>{const t=e.name,i=e.size,s=e.fileId,n=e.fileUrls;return new ce.R(t,i,this.nextLocalFileId(),{type:"uploaded",fileId:s,fileUrls:n})}))}}componentWillUnmount(){this.formClearHelper.disconnect()}get maxUploadSizeInBytes(){return((e,t,i)=>{if(e<0)throw Error("Size must be 0 or greater");const s=u.findIndex((e=>e===t)),n=u.findIndex((e=>e===i));if(-1===s||-1===n)throw Error("Unexpected byte unit provided");if(s===n)return e;const l=Math.abs(s-n),a=h**l;return s>n?e/a:e*a})(this.props.element.maxUploadSizeMb,g.Megabyte,g.Byte)}get status(){return this.state.files.some((e=>"uploading"===e.status.type))||this.forceUpdatingStatus?"updating":"ready"}componentDidMount(){const e=this.createWidgetValue(),{element:t,widgetMgr:i,fragmentId:s}=this.props;void 0===i.getFileUploaderStateValue(t)&&i.setFileUploaderStateValue(t,e,{fromUi:!1},s)}createWidgetValue(){const e=this.state.files.filter((e=>"uploaded"===e.status.type)).map((e=>{const{name:t,size:i,status:s}=e,{fileId:n,fileUrls:l}=s;return new d.jM({fileId:n,fileUrls:l,name:t,size:i})}));return new d.xO({uploadedFileInfo:e})}render(){var e;const{files:t}=this.state,{element:i,disabled:s,widgetMgr:n}=this.props,l=i.type;this.formClearHelper.manageFormClearListener(n,i.formId,this.onFormCleared);const a=t.slice().reverse();return(0,O.jsxs)(A,{"data-testid":"stFileUploader",children:[(0,O.jsx)(f.O,{label:i.label,disabled:s,labelVisibility:(0,c.iF)(null===(e=i.labelVisibility)||void 0===e?void 0:e.value),children:i.help&&(0,O.jsx)(x.dT,{children:(0,O.jsx)(w.Z,{content:i.help,placement:y.u.TOP_RIGHT})})}),(0,O.jsx)(_,{onDrop:this.dropHandler,multiple:i.multipleFiles,acceptedExtensions:l,maxSizeBytes:this.maxUploadSizeInBytes,label:i.label,disabled:s}),a.length>0&&(0,O.jsx)(pe,{items:a,pageSize:3,onDelete:this.deleteFile,resetOnAdd:!0})]})}nextLocalFileId(){return this.localFileIdCounter++}}const he=ge},87814:(e,t,i)=>{i.d(t,{K:()=>n});var s=i(50641);class n{constructor(){this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}manageFormClearListener(e,t,i){null!=this.formClearListener&&this.lastWidgetMgr===e&&this.lastFormId===t||(this.disconnect(),(0,s.bM)(t)&&(this.formClearListener=e.addFormClearedListener(t,i),this.lastWidgetMgr=e,this.lastFormId=t))}disconnect(){var e;null===(e=this.formClearListener)||void 0===e||e.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}}}]);