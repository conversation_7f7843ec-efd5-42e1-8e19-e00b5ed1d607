"""OpenBB Innovative Indicators Models."""

from datetime import date
from typing import Optional, Union, Literal
from pydantic import Field, field_validator
from openbb_core.provider.abstract.data import Data
from openbb_core.provider.abstract.query_params import QueryParams
from openbb_core.provider.utils.descriptions import (
    DATA_DESCRIPTIONS,
    QUERY_DESCRIPTIONS,
)


class InnovativeIndicatorsQueryParams(QueryParams):
    """Innovative Technical Indicators Query Parameters."""

    symbol: str = Field(description=QUERY_DESCRIPTIONS.get("symbol", ""))
    start_date: Optional[date] = Field(
        default=None, description=QUERY_DESCRIPTIONS.get("start_date", "")
    )
    end_date: Optional[date] = Field(
        default=None, description=QUERY_DESCRIPTIONS.get("end_date", "")
    )
    indicator: Literal["dmfi", "aisi", "mvci", "tafi", "fmsi", "all"] = Field(
        default="all",
        description="Innovative indicator to calculate. Options: dmfi, aisi, mvci, tafi, fmsi, all"
    )
    period: Optional[int] = Field(
        default=20,
        description="Period for indicator calculation",
        ge=5,
        le=252
    )


class InnovativeIndicatorsData(Data):
    """Innovative Technical Indicators Data."""

    date: date = Field(description=DATA_DESCRIPTIONS.get("date", ""))
    dmfi: Optional[float] = Field(
        default=None,
        description="Dynamic Money Flow Intensity - 动态资金流强度指标"
    )
    aisi: Optional[float] = Field(
        default=None,
        description="Adaptive Intelligent Sentiment Index - 自适应智能情绪指标"
    )
    mvci: Optional[float] = Field(
        default=None,
        description="Multi-Dimensional Value Convergence Index - 多维度价值收敛指标"
    )
    tafi: Optional[float] = Field(
        default=None,
        description="Time-Adaptive Trend Following Index - 时间自适应趋势指标"
    )
    fmsi: Optional[float] = Field(
        default=None,
        description="Fundamental-Technical Momentum Synthesis Index - 基本面-技术面综合指标"
    )
    signal: Optional[str] = Field(
        default=None,
        description="Composite trading signal: BUY, SELL, HOLD"
    )
    signal_strength: Optional[float] = Field(
        default=None,
        description="Signal strength from -1.0 to 1.0"
    )

    @field_validator("dmfi", "aisi", "mvci", "tafi", "fmsi", mode="before")
    @classmethod
    def validate_indicator_values(cls, v):
        """Validate indicator values are within reasonable range."""
        if v is not None and (v < -100 or v > 100):
            return max(-100, min(100, v))  # Clamp to [-100, 100]
        return v

    @field_validator("signal_strength", mode="before")
    @classmethod
    def validate_signal_strength(cls, v):
        """Validate signal strength is within [-1, 1] range."""
        if v is not None and (v < -1 or v > 1):
            return max(-1, min(1, v))  # Clamp to [-1, 1]
        return v


class TradingSignalsQueryParams(QueryParams):
    """Trading Signals Query Parameters."""

    symbol: str = Field(description=QUERY_DESCRIPTIONS.get("symbol", ""))
    start_date: Optional[date] = Field(
        default=None, description=QUERY_DESCRIPTIONS.get("start_date", "")
    )
    end_date: Optional[date] = Field(
        default=None, description=QUERY_DESCRIPTIONS.get("end_date", "")
    )
    threshold: Optional[float] = Field(
        default=0.5,
        description="Signal threshold for BUY/SELL decisions",
        ge=0.1,
        le=1.0
    )


class TradingSignalsData(Data):
    """Trading Signals Data."""

    date: date = Field(description=DATA_DESCRIPTIONS.get("date", ""))
    signal: str = Field(description="Trading signal: BUY, SELL, HOLD")
    strength: float = Field(description="Signal strength from -1.0 to 1.0")
    confidence: float = Field(description="Signal confidence from 0.0 to 1.0")
    indicators_consensus: int = Field(
        description="Number of indicators agreeing with the signal"
    )
    price: Optional[float] = Field(
        default=None,
        description="Stock price at signal generation"
    )
    volume: Optional[int] = Field(
        default=None,
        description="Trading volume at signal generation"
    )
