from pydantic import BaseModel
from functools import lru_cache
import os
from dotenv import load_dotenv


class AppConfig(BaseModel):
    app_host: str = "0.0.0.0"
    app_port: int = 6900
    database_url: str = "postgresql+psycopg2://openbb:openbb@localhost:5432/openbb_cn"
    tushare_token: str | None = None
    bailian_api_key: str | None = None
    bailian_base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    bailian_model: str = "qwen-plus"


@lru_cache(maxsize=1)
def get_settings() -> AppConfig:
    load_dotenv()
    return AppConfig(
        app_host=os.getenv("APP_HOST", "0.0.0.0"),
        app_port=int(os.getenv("APP_PORT", "6900")),
        database_url=os.getenv(
            "DATABASE_URL",
            "postgresql+psycopg2://openbb:openbb@localhost:5432/openbb_cn",
        ),
        tushare_token=os.getenv("TUSHARE_TOKEN"),
        bailian_api_key=os.getenv("BAILIAN_API_KEY"),
        bailian_base_url=os.getenv("BAILIAN_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
        bailian_model=os.getenv("BAILIAN_MODEL", "qwen-plus"),
    )

