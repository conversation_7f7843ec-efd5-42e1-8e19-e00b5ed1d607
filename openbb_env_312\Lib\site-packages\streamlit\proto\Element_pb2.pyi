"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2024)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""
import builtins
import google.protobuf.descriptor
import google.protobuf.message
import streamlit.proto.Alert_pb2
import streamlit.proto.ArrowVegaLiteChart_pb2
import streamlit.proto.Arrow_pb2
import streamlit.proto.Audio_pb2
import streamlit.proto.Balloons_pb2
import streamlit.proto.BokehChart_pb2
import streamlit.proto.Button_pb2
import streamlit.proto.CameraInput_pb2
import streamlit.proto.ChatInput_pb2
import streamlit.proto.Checkbox_pb2
import streamlit.proto.Code_pb2
import streamlit.proto.ColorPicker_pb2
import streamlit.proto.Components_pb2
import streamlit.proto.DataFrame_pb2
import streamlit.proto.DateInput_pb2
import streamlit.proto.DeckGlJsonChart_pb2
import streamlit.proto.DocString_pb2
import streamlit.proto.DownloadButton_pb2
import streamlit.proto.Empty_pb2
import streamlit.proto.Exception_pb2
import streamlit.proto.Favicon_pb2
import streamlit.proto.FileUploader_pb2
import streamlit.proto.GraphVizChart_pb2
import streamlit.proto.Heading_pb2
import streamlit.proto.Html_pb2
import streamlit.proto.IFrame_pb2
import streamlit.proto.Image_pb2
import streamlit.proto.Json_pb2
import streamlit.proto.LinkButton_pb2
import streamlit.proto.Markdown_pb2
import streamlit.proto.Metric_pb2
import streamlit.proto.MultiSelect_pb2
import streamlit.proto.NumberInput_pb2
import streamlit.proto.PageLink_pb2
import streamlit.proto.PlotlyChart_pb2
import streamlit.proto.Progress_pb2
import streamlit.proto.Radio_pb2
import streamlit.proto.Selectbox_pb2
import streamlit.proto.Skeleton_pb2
import streamlit.proto.Slider_pb2
import streamlit.proto.Snow_pb2
import streamlit.proto.Spinner_pb2
import streamlit.proto.TextArea_pb2
import streamlit.proto.TextInput_pb2
import streamlit.proto.Text_pb2
import streamlit.proto.TimeInput_pb2
import streamlit.proto.Toast_pb2
import streamlit.proto.VegaLiteChart_pb2
import streamlit.proto.Video_pb2
import sys

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class Element(google.protobuf.message.Message):
    """An element which can be displayed on the screen."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ALERT_FIELD_NUMBER: builtins.int
    ARROW_DATA_FRAME_FIELD_NUMBER: builtins.int
    ARROW_TABLE_FIELD_NUMBER: builtins.int
    ARROW_VEGA_LITE_CHART_FIELD_NUMBER: builtins.int
    AUDIO_FIELD_NUMBER: builtins.int
    BALLOONS_FIELD_NUMBER: builtins.int
    BOKEH_CHART_FIELD_NUMBER: builtins.int
    BUTTON_FIELD_NUMBER: builtins.int
    DOWNLOAD_BUTTON_FIELD_NUMBER: builtins.int
    CAMERA_INPUT_FIELD_NUMBER: builtins.int
    CHAT_INPUT_FIELD_NUMBER: builtins.int
    CHECKBOX_FIELD_NUMBER: builtins.int
    COLOR_PICKER_FIELD_NUMBER: builtins.int
    COMPONENT_INSTANCE_FIELD_NUMBER: builtins.int
    DATA_FRAME_FIELD_NUMBER: builtins.int
    TABLE_FIELD_NUMBER: builtins.int
    DATE_INPUT_FIELD_NUMBER: builtins.int
    DECK_GL_JSON_CHART_FIELD_NUMBER: builtins.int
    DOC_STRING_FIELD_NUMBER: builtins.int
    EMPTY_FIELD_NUMBER: builtins.int
    EXCEPTION_FIELD_NUMBER: builtins.int
    FAVICON_FIELD_NUMBER: builtins.int
    FILE_UPLOADER_FIELD_NUMBER: builtins.int
    GRAPHVIZ_CHART_FIELD_NUMBER: builtins.int
    HTML_FIELD_NUMBER: builtins.int
    IFRAME_FIELD_NUMBER: builtins.int
    IMGS_FIELD_NUMBER: builtins.int
    JSON_FIELD_NUMBER: builtins.int
    LINK_BUTTON_FIELD_NUMBER: builtins.int
    MARKDOWN_FIELD_NUMBER: builtins.int
    METRIC_FIELD_NUMBER: builtins.int
    MULTISELECT_FIELD_NUMBER: builtins.int
    NUMBER_INPUT_FIELD_NUMBER: builtins.int
    PAGE_LINK_FIELD_NUMBER: builtins.int
    PLOTLY_CHART_FIELD_NUMBER: builtins.int
    PROGRESS_FIELD_NUMBER: builtins.int
    RADIO_FIELD_NUMBER: builtins.int
    SELECTBOX_FIELD_NUMBER: builtins.int
    SKELETON_FIELD_NUMBER: builtins.int
    SLIDER_FIELD_NUMBER: builtins.int
    SNOW_FIELD_NUMBER: builtins.int
    SPINNER_FIELD_NUMBER: builtins.int
    TEXT_FIELD_NUMBER: builtins.int
    TEXT_AREA_FIELD_NUMBER: builtins.int
    TEXT_INPUT_FIELD_NUMBER: builtins.int
    TIME_INPUT_FIELD_NUMBER: builtins.int
    TOAST_FIELD_NUMBER: builtins.int
    VEGA_LITE_CHART_FIELD_NUMBER: builtins.int
    VIDEO_FIELD_NUMBER: builtins.int
    HEADING_FIELD_NUMBER: builtins.int
    CODE_FIELD_NUMBER: builtins.int
    @property
    def alert(self) -> streamlit.proto.Alert_pb2.Alert: ...
    @property
    def arrow_data_frame(self) -> streamlit.proto.Arrow_pb2.Arrow: ...
    @property
    def arrow_table(self) -> streamlit.proto.Arrow_pb2.Arrow: ...
    @property
    def arrow_vega_lite_chart(self) -> streamlit.proto.ArrowVegaLiteChart_pb2.ArrowVegaLiteChart: ...
    @property
    def audio(self) -> streamlit.proto.Audio_pb2.Audio: ...
    @property
    def balloons(self) -> streamlit.proto.Balloons_pb2.Balloons: ...
    @property
    def bokeh_chart(self) -> streamlit.proto.BokehChart_pb2.BokehChart: ...
    @property
    def button(self) -> streamlit.proto.Button_pb2.Button: ...
    @property
    def download_button(self) -> streamlit.proto.DownloadButton_pb2.DownloadButton: ...
    @property
    def camera_input(self) -> streamlit.proto.CameraInput_pb2.CameraInput: ...
    @property
    def chat_input(self) -> streamlit.proto.ChatInput_pb2.ChatInput: ...
    @property
    def checkbox(self) -> streamlit.proto.Checkbox_pb2.Checkbox: ...
    @property
    def color_picker(self) -> streamlit.proto.ColorPicker_pb2.ColorPicker: ...
    @property
    def component_instance(self) -> streamlit.proto.Components_pb2.ComponentInstance: ...
    @property
    def data_frame(self) -> streamlit.proto.DataFrame_pb2.DataFrame:
        """DEPRECATED: This element is deprecated and unused:"""
    @property
    def table(self) -> streamlit.proto.DataFrame_pb2.DataFrame:
        """DEPRECATED: This element is deprecated and unused:"""
    @property
    def date_input(self) -> streamlit.proto.DateInput_pb2.DateInput: ...
    @property
    def deck_gl_json_chart(self) -> streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart: ...
    @property
    def doc_string(self) -> streamlit.proto.DocString_pb2.DocString: ...
    @property
    def empty(self) -> streamlit.proto.Empty_pb2.Empty: ...
    @property
    def exception(self) -> streamlit.proto.Exception_pb2.Exception: ...
    @property
    def favicon(self) -> streamlit.proto.Favicon_pb2.Favicon: ...
    @property
    def file_uploader(self) -> streamlit.proto.FileUploader_pb2.FileUploader: ...
    @property
    def graphviz_chart(self) -> streamlit.proto.GraphVizChart_pb2.GraphVizChart: ...
    @property
    def html(self) -> streamlit.proto.Html_pb2.Html: ...
    @property
    def iframe(self) -> streamlit.proto.IFrame_pb2.IFrame: ...
    @property
    def imgs(self) -> streamlit.proto.Image_pb2.ImageList: ...
    @property
    def json(self) -> streamlit.proto.Json_pb2.Json: ...
    @property
    def link_button(self) -> streamlit.proto.LinkButton_pb2.LinkButton: ...
    @property
    def markdown(self) -> streamlit.proto.Markdown_pb2.Markdown: ...
    @property
    def metric(self) -> streamlit.proto.Metric_pb2.Metric: ...
    @property
    def multiselect(self) -> streamlit.proto.MultiSelect_pb2.MultiSelect: ...
    @property
    def number_input(self) -> streamlit.proto.NumberInput_pb2.NumberInput: ...
    @property
    def page_link(self) -> streamlit.proto.PageLink_pb2.PageLink: ...
    @property
    def plotly_chart(self) -> streamlit.proto.PlotlyChart_pb2.PlotlyChart: ...
    @property
    def progress(self) -> streamlit.proto.Progress_pb2.Progress: ...
    @property
    def radio(self) -> streamlit.proto.Radio_pb2.Radio: ...
    @property
    def selectbox(self) -> streamlit.proto.Selectbox_pb2.Selectbox: ...
    @property
    def skeleton(self) -> streamlit.proto.Skeleton_pb2.Skeleton: ...
    @property
    def slider(self) -> streamlit.proto.Slider_pb2.Slider: ...
    @property
    def snow(self) -> streamlit.proto.Snow_pb2.Snow: ...
    @property
    def spinner(self) -> streamlit.proto.Spinner_pb2.Spinner: ...
    @property
    def text(self) -> streamlit.proto.Text_pb2.Text: ...
    @property
    def text_area(self) -> streamlit.proto.TextArea_pb2.TextArea: ...
    @property
    def text_input(self) -> streamlit.proto.TextInput_pb2.TextInput: ...
    @property
    def time_input(self) -> streamlit.proto.TimeInput_pb2.TimeInput: ...
    @property
    def toast(self) -> streamlit.proto.Toast_pb2.Toast: ...
    @property
    def vega_lite_chart(self) -> streamlit.proto.VegaLiteChart_pb2.VegaLiteChart:
        """DEPRECATED: This element is deprecated and unused:"""
    @property
    def video(self) -> streamlit.proto.Video_pb2.Video: ...
    @property
    def heading(self) -> streamlit.proto.Heading_pb2.Heading: ...
    @property
    def code(self) -> streamlit.proto.Code_pb2.Code:
        """Next ID: 55"""
    def __init__(
        self,
        *,
        alert: streamlit.proto.Alert_pb2.Alert | None = ...,
        arrow_data_frame: streamlit.proto.Arrow_pb2.Arrow | None = ...,
        arrow_table: streamlit.proto.Arrow_pb2.Arrow | None = ...,
        arrow_vega_lite_chart: streamlit.proto.ArrowVegaLiteChart_pb2.ArrowVegaLiteChart | None = ...,
        audio: streamlit.proto.Audio_pb2.Audio | None = ...,
        balloons: streamlit.proto.Balloons_pb2.Balloons | None = ...,
        bokeh_chart: streamlit.proto.BokehChart_pb2.BokehChart | None = ...,
        button: streamlit.proto.Button_pb2.Button | None = ...,
        download_button: streamlit.proto.DownloadButton_pb2.DownloadButton | None = ...,
        camera_input: streamlit.proto.CameraInput_pb2.CameraInput | None = ...,
        chat_input: streamlit.proto.ChatInput_pb2.ChatInput | None = ...,
        checkbox: streamlit.proto.Checkbox_pb2.Checkbox | None = ...,
        color_picker: streamlit.proto.ColorPicker_pb2.ColorPicker | None = ...,
        component_instance: streamlit.proto.Components_pb2.ComponentInstance | None = ...,
        data_frame: streamlit.proto.DataFrame_pb2.DataFrame | None = ...,
        table: streamlit.proto.DataFrame_pb2.DataFrame | None = ...,
        date_input: streamlit.proto.DateInput_pb2.DateInput | None = ...,
        deck_gl_json_chart: streamlit.proto.DeckGlJsonChart_pb2.DeckGlJsonChart | None = ...,
        doc_string: streamlit.proto.DocString_pb2.DocString | None = ...,
        empty: streamlit.proto.Empty_pb2.Empty | None = ...,
        exception: streamlit.proto.Exception_pb2.Exception | None = ...,
        favicon: streamlit.proto.Favicon_pb2.Favicon | None = ...,
        file_uploader: streamlit.proto.FileUploader_pb2.FileUploader | None = ...,
        graphviz_chart: streamlit.proto.GraphVizChart_pb2.GraphVizChart | None = ...,
        html: streamlit.proto.Html_pb2.Html | None = ...,
        iframe: streamlit.proto.IFrame_pb2.IFrame | None = ...,
        imgs: streamlit.proto.Image_pb2.ImageList | None = ...,
        json: streamlit.proto.Json_pb2.Json | None = ...,
        link_button: streamlit.proto.LinkButton_pb2.LinkButton | None = ...,
        markdown: streamlit.proto.Markdown_pb2.Markdown | None = ...,
        metric: streamlit.proto.Metric_pb2.Metric | None = ...,
        multiselect: streamlit.proto.MultiSelect_pb2.MultiSelect | None = ...,
        number_input: streamlit.proto.NumberInput_pb2.NumberInput | None = ...,
        page_link: streamlit.proto.PageLink_pb2.PageLink | None = ...,
        plotly_chart: streamlit.proto.PlotlyChart_pb2.PlotlyChart | None = ...,
        progress: streamlit.proto.Progress_pb2.Progress | None = ...,
        radio: streamlit.proto.Radio_pb2.Radio | None = ...,
        selectbox: streamlit.proto.Selectbox_pb2.Selectbox | None = ...,
        skeleton: streamlit.proto.Skeleton_pb2.Skeleton | None = ...,
        slider: streamlit.proto.Slider_pb2.Slider | None = ...,
        snow: streamlit.proto.Snow_pb2.Snow | None = ...,
        spinner: streamlit.proto.Spinner_pb2.Spinner | None = ...,
        text: streamlit.proto.Text_pb2.Text | None = ...,
        text_area: streamlit.proto.TextArea_pb2.TextArea | None = ...,
        text_input: streamlit.proto.TextInput_pb2.TextInput | None = ...,
        time_input: streamlit.proto.TimeInput_pb2.TimeInput | None = ...,
        toast: streamlit.proto.Toast_pb2.Toast | None = ...,
        vega_lite_chart: streamlit.proto.VegaLiteChart_pb2.VegaLiteChart | None = ...,
        video: streamlit.proto.Video_pb2.Video | None = ...,
        heading: streamlit.proto.Heading_pb2.Heading | None = ...,
        code: streamlit.proto.Code_pb2.Code | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["alert", b"alert", "arrow_data_frame", b"arrow_data_frame", "arrow_table", b"arrow_table", "arrow_vega_lite_chart", b"arrow_vega_lite_chart", "audio", b"audio", "balloons", b"balloons", "bokeh_chart", b"bokeh_chart", "button", b"button", "camera_input", b"camera_input", "chat_input", b"chat_input", "checkbox", b"checkbox", "code", b"code", "color_picker", b"color_picker", "component_instance", b"component_instance", "data_frame", b"data_frame", "date_input", b"date_input", "deck_gl_json_chart", b"deck_gl_json_chart", "doc_string", b"doc_string", "download_button", b"download_button", "empty", b"empty", "exception", b"exception", "favicon", b"favicon", "file_uploader", b"file_uploader", "graphviz_chart", b"graphviz_chart", "heading", b"heading", "html", b"html", "iframe", b"iframe", "imgs", b"imgs", "json", b"json", "link_button", b"link_button", "markdown", b"markdown", "metric", b"metric", "multiselect", b"multiselect", "number_input", b"number_input", "page_link", b"page_link", "plotly_chart", b"plotly_chart", "progress", b"progress", "radio", b"radio", "selectbox", b"selectbox", "skeleton", b"skeleton", "slider", b"slider", "snow", b"snow", "spinner", b"spinner", "table", b"table", "text", b"text", "text_area", b"text_area", "text_input", b"text_input", "time_input", b"time_input", "toast", b"toast", "type", b"type", "vega_lite_chart", b"vega_lite_chart", "video", b"video"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["alert", b"alert", "arrow_data_frame", b"arrow_data_frame", "arrow_table", b"arrow_table", "arrow_vega_lite_chart", b"arrow_vega_lite_chart", "audio", b"audio", "balloons", b"balloons", "bokeh_chart", b"bokeh_chart", "button", b"button", "camera_input", b"camera_input", "chat_input", b"chat_input", "checkbox", b"checkbox", "code", b"code", "color_picker", b"color_picker", "component_instance", b"component_instance", "data_frame", b"data_frame", "date_input", b"date_input", "deck_gl_json_chart", b"deck_gl_json_chart", "doc_string", b"doc_string", "download_button", b"download_button", "empty", b"empty", "exception", b"exception", "favicon", b"favicon", "file_uploader", b"file_uploader", "graphviz_chart", b"graphviz_chart", "heading", b"heading", "html", b"html", "iframe", b"iframe", "imgs", b"imgs", "json", b"json", "link_button", b"link_button", "markdown", b"markdown", "metric", b"metric", "multiselect", b"multiselect", "number_input", b"number_input", "page_link", b"page_link", "plotly_chart", b"plotly_chart", "progress", b"progress", "radio", b"radio", "selectbox", b"selectbox", "skeleton", b"skeleton", "slider", b"slider", "snow", b"snow", "spinner", b"spinner", "table", b"table", "text", b"text", "text_area", b"text_area", "text_input", b"text_input", "time_input", b"time_input", "toast", b"toast", "type", b"type", "vega_lite_chart", b"vega_lite_chart", "video", b"video"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["type", b"type"]) -> typing_extensions.Literal["alert", "arrow_data_frame", "arrow_table", "arrow_vega_lite_chart", "audio", "balloons", "bokeh_chart", "button", "download_button", "camera_input", "chat_input", "checkbox", "color_picker", "component_instance", "data_frame", "table", "date_input", "deck_gl_json_chart", "doc_string", "empty", "exception", "favicon", "file_uploader", "graphviz_chart", "html", "iframe", "imgs", "json", "link_button", "markdown", "metric", "multiselect", "number_input", "page_link", "plotly_chart", "progress", "radio", "selectbox", "skeleton", "slider", "snow", "spinner", "text", "text_area", "text_input", "time_input", "toast", "vega_lite_chart", "video", "heading", "code"] | None: ...

global___Element = Element
