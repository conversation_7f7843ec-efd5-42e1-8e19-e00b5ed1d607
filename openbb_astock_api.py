#!/usr/bin/env python3
"""
OpenBB A股API扩展服务
为OpenBB API服务器添加A股数据端点
"""

from fastapi import FastAPI, HTTPException, Query
from fastapi.responses import JSONResponse
from typing import List, Optional, Dict, Any
import pandas as pd
from datetime import datetime, timedelta
import uvicorn

# 导入A股数据提供商
try:
    from openbb_astock_provider import AStockDataFetcher
    ASTOCK_AVAILABLE = True
except ImportError:
    ASTOCK_AVAILABLE = False
    print("⚠️ A股数据提供商不可用")


class AStockAPIServer:
    """A股API服务器"""
    
    def __init__(self, tushare_token: Optional[str] = None):
        """
        初始化A股API服务器
        
        Args:
            tushare_token: Tushare Pro API Token
        """
        self.app = FastAPI(
            title="OpenBB A股数据API",
            description="集成Tushare Pro、AKShare、东方财富的A股数据API",
            version="1.0.0"
        )
        
        if ASTOCK_AVAILABLE:
            self.fetcher = AStockDataFetcher(tushare_token)
        else:
            self.fetcher = None
        
        self._setup_routes()
    
    def _setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/")
        async def root():
            """根路径"""
            return {
                "message": "OpenBB A股数据API",
                "version": "1.0.0",
                "available": ASTOCK_AVAILABLE,
                "providers": self.fetcher.providers if self.fetcher else []
            }
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "astock_available": ASTOCK_AVAILABLE
            }
        
        @self.app.get("/api/v1/astock/equity/price/historical")
        async def get_historical_data(
            symbol: str = Query(..., description="股票代码，如 000001 或 000001.SZ"),
            start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
            end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
            provider: str = Query("auto", description="数据提供商: auto, tushare_pro, akshare, efinance")
        ):
            """获取A股历史数据"""
            if not ASTOCK_AVAILABLE or not self.fetcher:
                raise HTTPException(status_code=503, detail="A股数据服务不可用")
            
            try:
                # 设置默认日期范围
                if not start_date:
                    start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
                if not end_date:
                    end_date = datetime.now().strftime("%Y-%m-%d")
                
                # 获取数据
                df = self.fetcher.fetch_equity_historical(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    provider=provider
                )
                
                if df is None or df.empty:
                    return JSONResponse(
                        status_code=204,
                        content={"message": "无数据", "symbol": symbol}
                    )
                
                # 转换为API响应格式
                results = []
                for date, row in df.iterrows():
                    results.append({
                        "date": date.strftime("%Y-%m-%d"),
                        "open": float(row.get('open', 0)),
                        "high": float(row.get('high', 0)),
                        "low": float(row.get('low', 0)),
                        "close": float(row.get('close', 0)),
                        "volume": int(row.get('volume', 0)),
                        "turnover": float(row.get('turnover', 0)) if 'turnover' in row else None
                    })
                
                return {
                    "results": results,
                    "symbol": symbol,
                    "provider_used": provider,
                    "count": len(results)
                }
                
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")
        
        @self.app.get("/api/v1/astock/equity/price/quote")
        async def get_quote_data(
            symbol: str = Query(..., description="股票代码，多个用逗号分隔"),
            provider: str = Query("auto", description="数据提供商: auto, tushare_pro, akshare, efinance")
        ):
            """获取A股实时报价"""
            if not ASTOCK_AVAILABLE or not self.fetcher:
                raise HTTPException(status_code=503, detail="A股数据服务不可用")
            
            try:
                # 解析股票代码
                symbols = [s.strip() for s in symbol.split(',')]
                
                # 获取数据
                df = self.fetcher.fetch_equity_quote(
                    symbols=symbols,
                    provider=provider
                )
                
                if df is None or df.empty:
                    return JSONResponse(
                        status_code=204,
                        content={"message": "无数据", "symbols": symbols}
                    )
                
                # 转换为API响应格式
                results = []
                for _, row in df.iterrows():
                    results.append({
                        "symbol": row.get('symbol', ''),
                        "name": row.get('name', ''),
                        "price": float(row.get('price', 0)),
                        "change": float(row.get('change', 0)),
                        "change_percent": float(row.get('change_percent', 0)),
                        "volume": int(row.get('volume', 0)),
                        "turnover": float(row.get('turnover', 0)),
                        "high": float(row.get('high', 0)),
                        "low": float(row.get('low', 0)),
                        "open": float(row.get('open', 0))
                    })
                
                return {
                    "results": results,
                    "provider_used": provider,
                    "count": len(results)
                }
                
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")
        
        @self.app.get("/api/v1/astock/market/indices")
        async def get_market_indices():
            """获取A股市场指数"""
            try:
                # 模拟市场指数数据
                indices = [
                    {
                        "symbol": "000001.SH",
                        "name": "上证指数",
                        "price": 3150.25,
                        "change": 15.8,
                        "change_percent": 0.50,
                        "volume": 250000000,
                        "turnover": 3**********0
                    },
                    {
                        "symbol": "399001.SZ", 
                        "name": "深证成指",
                        "price": 10250.60,
                        "change": -25.4,
                        "change_percent": -0.25,
                        "volume": 180000000,
                        "turnover": 2**********0
                    },
                    {
                        "symbol": "399006.SZ",
                        "name": "创业板指",
                        "price": 2180.45,
                        "change": 8.2,
                        "change_percent": 0.38,
                        "volume": 120000000,
                        "turnover": 150000000000
                    }
                ]
                
                return {
                    "results": indices,
                    "count": len(indices)
                }
                
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"获取指数数据失败: {str(e)}")
        
        @self.app.get("/api/v1/astock/equity/fundamental/income")
        async def get_income_statement(
            symbol: str = Query(..., description="股票代码"),
            period: str = Query("annual", description="报告期: annual, quarter"),
            limit: int = Query(5, description="返回条数")
        ):
            """获取A股财务报表 - 利润表"""
            if not ASTOCK_AVAILABLE or not self.fetcher:
                raise HTTPException(status_code=503, detail="A股数据服务不可用")
            
            try:
                # 这里可以集成真实的财务数据获取
                # 目前返回模拟数据
                results = [
                    {
                        "symbol": symbol,
                        "end_date": "2023-12-31",
                        "revenue": 100000000000,
                        "operating_profit": 15000000000,
                        "net_profit": **********,
                        "eps": 2.5
                    }
                ]
                
                return {
                    "results": results,
                    "symbol": symbol,
                    "period": period,
                    "count": len(results)
                }
                
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"获取财务数据失败: {str(e)}")
        
        @self.app.get("/api/v1/astock/providers")
        async def get_providers():
            """获取可用的数据提供商"""
            if not ASTOCK_AVAILABLE or not self.fetcher:
                return {"providers": [], "available": False}
            
            return {
                "providers": self.fetcher.providers,
                "available": True,
                "descriptions": {
                    "tushare_pro": "Tushare Pro - 专业金融数据",
                    "tushare_free": "Tushare 免费版",
                    "akshare": "AKShare - 开源金融数据",
                    "efinance": "东方财富 - 实时行情数据"
                }
            }
    
    def run(self, host: str = "127.0.0.1", port: int = 6901):
        """运行API服务器"""
        print(f"🚀 启动A股API服务器: http://{host}:{port}")
        print(f"📖 API文档: http://{host}:{port}/docs")
        
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="info"
        )


def create_astock_api_app(tushare_token: Optional[str] = None) -> FastAPI:
    """创建A股API应用"""
    server = AStockAPIServer(tushare_token)
    return server.app


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="OpenBB A股数据API服务器")
    parser.add_argument("--host", default="127.0.0.1", help="服务器地址")
    parser.add_argument("--port", type=int, default=6901, help="服务器端口")
    parser.add_argument("--tushare-token", help="Tushare Pro API Token")
    
    args = parser.parse_args()
    
    # 创建并运行服务器
    server = AStockAPIServer(args.tushare_token)
    server.run(args.host, args.port)


if __name__ == "__main__":
    main()
