from __future__ import annotations

from dataclasses import dataclass
from typing import List, Dict, Tuple
import numpy as np
import pandas as pd

from ..sdk.obb import obb
from ..quant.ga_pso import PSOConfig, pso_select


def _load_close(symbol: str, start: str | None, end: str | None) -> pd.DataFrame:
    df = obb.equity.price.historical(symbol=symbol, start=start, end=end)
    df = df[["trade_date", "close"]].rename(columns={"close": symbol})
    return df.set_index("trade_date")


def _align_prices(symbols: List[str], start: str | None, end: str | None) -> pd.DataFrame:
    frames = []
    for s in symbols:
        try:
            # 确保股票代码包含交易所后缀
            normalized_symbol = s
            if '.' not in s and len(s) == 6 and s.isdigit():
                if s.startswith('6'):
                    normalized_symbol = f"{s}.SH"
                elif s.startswith(('0', '3')):
                    normalized_symbol = f"{s}.SZ"

            print(f"📊 获取价格数据: {s} -> {normalized_symbol}")
            frames.append(_load_close(normalized_symbol, start, end))
        except Exception as e:
            print(f"❌ 获取价格失败: {s} - {e}")
            continue

    if not frames:
        print("❌ 没有获取到任何价格数据")
        return pd.DataFrame()

    prices = pd.concat(frames, axis=1).sort_index().dropna(how="all")
    print(f"✅ 价格数据合并完成: {prices.shape}")
    return prices


@dataclass
class SimConfig:
    start: str | None = None
    end: str | None = None
    universe: List[str] | None = None
    initial_cash: float = 100_000.0
    top_n: int = 10
    rebalance_freq: str = "W-FRI"  # pandas offset alias
    fee_bps: float = 5.0  # 单边费率，基点


def backtest(cfg: SimConfig) -> Dict:
    universe = cfg.universe or []
    if not universe:
        return {"ok": False, "error": "universe required"}

    prices = _align_prices(universe, cfg.start, cfg.end)
    if prices.empty:
        return {"ok": False, "error": "no prices"}

    dates = prices.index
    if len(dates) < 30:
        return {"ok": False, "error": "insufficient data"}

    # 资金上限 1,000,000 RMB
    equity = float(min(max(cfg.initial_cash, 0.0), 1_000_000.0))
    curve = []
    holdings: Dict[str, float] = {}
    last_weights: Dict[str, float] = {}
    trades = []

    # 选择调仓日期（对齐可交易日）
    rebal_days = pd.Series(1, index=dates).resample(cfg.rebalance_freq).last().dropna()
    rebal_idx = [d for d in rebal_days.index if d in dates]

    prev_date = dates[0]
    for d in dates:
        # 到达调仓日：用PSO选择TopN并等权
        if d in rebal_idx:
            sel = pso_select(universe, start=cfg.start, end=str(d), top_n=cfg.top_n)
            selected = sel.get("selected", [])
            # 计算等权目标权重
            target_w = {s: (1.0 / len(selected)) for s in selected if s in prices.columns and not np.isnan(prices.loc[d, s])}
            # 成本：按换仓金额计费
            turnover = 0.0
            for s in set(list(holdings.keys()) + list(target_w.keys())):
                old_w = last_weights.get(s, 0.0)
                new_w = target_w.get(s, 0.0)
                turnover += abs(new_w - old_w)
            fee = equity * turnover * (cfg.fee_bps / 10000.0)
            equity -= fee
            last_weights = target_w
            trades.append({"date": str(d), "selected": list(target_w.keys()), "turnover": float(turnover), "fee": float(fee)})

        # 当日收益：按昨日权重乘以当日收益
        if d != prev_date and last_weights:
            ret = 0.0
            for s, w in last_weights.items():
                try:
                    prev_p = float(prices.loc[prev_date, s])
                    cur_p = float(prices.loc[d, s])
                    if prev_p > 0:
                        ret += w * ((cur_p / prev_p) - 1.0)
                except Exception:
                    continue
            equity *= (1.0 + ret)
        curve.append({"date": str(d), "equity": float(equity)})
        prev_date = d

    # 绩效
    eq = pd.Series([c["equity"] for c in curve], index=[pd.to_datetime(c["date"]) for c in curve])
    daily_ret = eq.pct_change().dropna()
    total_ret = float(eq.iloc[-1] / eq.iloc[0] - 1.0)
    sharpe = float(daily_ret.mean() / (daily_ret.std(ddof=0) + 1e-9) * np.sqrt(252)) if len(daily_ret) > 10 else None
    max_dd = float(((eq / eq.cummax()) - 1.0).min())

    return {
        "ok": True,
        "initial": cfg.initial_cash,
        "final": float(eq.iloc[-1]),
        "total_return": total_ret,
        "sharpe": sharpe,
        "max_drawdown": max_dd,
        "curve": curve,
        "trades": trades,
    }

