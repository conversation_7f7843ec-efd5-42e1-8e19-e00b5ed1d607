Version 7.45.6 [requires libcurl-7.19.0 or better] - 2025-03-06
---------------------------------------------------------------

        * Re-enable building Linux wheels with CA bundle autodetection

Version 7.45.5 [requires libcurl-7.19.0 or better] - 2025-03-06
---------------------------------------------------------------

        * Enable GSS-API and brotli support in wheels (patch by <PERSON>).
        * Add support for calling getinfo with CURLOPT_*_T arguments
          (patch by <PERSON>)
        * Change wheels to build using shared libraries (vice static libraries)
          (patch by <PERSON>)
        * Build wheels with curl 8.12.1 (mainly for security fixes)

Version 7.45.4 [requires libcurl-7.19.0 or better] - 2024-12-12
---------------------------------------------------------------

        * Add support for CURLOPT_HAPROXY_CLIENT_IP (patch by Scott Talbert).
        * Port tests from bottle to flask (patch by Miro Hrončok).
        * Add constant for CURL_HTTP_VERSION_3ONLY (patch by Pavel Horáček).
        * Add EFFECTIVE_METHOD info option (patch by Pavel Horáček).
        * Don't use `-flat_namespace` on macOS (patch by Michael Cho).
        * Add some missing GIL checks to callback functions
          (patch by Scott Talbert).
        * Fix assorted bugs in pycurl tests, including a segfault
          (patch by Scott Talbert).  All tests should now pass on Linux and
          macOS.
        * Fix minor bug in examples/multi-socket_action-select.py
          (patch by Oleg Broytman).
        * Build all wheels using the latest version of libcurl and its
          dependencies (patch by Scott Talbert).  All wheels should now have
          openssl, HTTP2, and SSH support.
        * Implement Certificate Authority path autodetection when building
          Linux wheels (patch by Scott Talbert).

Version 7.45.3 [requires libcurl-7.19.0 or better] - 2024-02-17
---------------------------------------------------------------

        * Add CURLOPT_REQUEST_TARGET option (patch by Marcel Brouwers).
        * Add missing 2nd parameters to METH_NOARGS functions
          (patch by Scott Talbert).
        * Add CURLOPT_AWS_SIGV4 option (patch by Scott Talbert).
        * Add consistent names for newer Curl version constants
          (patch by Scott Talbert).
        * Only run HTTP version 3 option constant test if curl supported
          (patch by Scott Talbert).
        * Expose COMPILE_SSL_LIB in Python and use for test filtering
          (patch by Scott Talbert).
        * Filter tests based on *compile* libcurl version not runtime version
          (patch by Scott Talbert).
        * Use print function in callbacks documentation
          (patch by Scott Talbert).
        * Add missing shebang to tests/ext/test-suite.sh
          (patch by Scott Talbert).
        * Officially declare support for Python 3.12
          (patch by Scott Talbert).
        * Fix curl_multi_info_read flow that loses messages
          (patch by Dom Sekotill).
        * Support using environment variables for setup on Windows
          (patch by Scott Talbert).
        * Add support for Schannel SSL backend (patch by Scott Talbert)
        * Skip HTTP2 tests based on a curl support check
          (patch by Scott Talbert).
        * Fix fake-curl tests so they work when run out of tree
          (patch by Scott Talbert).
        * xfail test_easy_pause_unpause unconditionally
          (patch by Scott Talbert).
        * Provide generic error strings in pycurl.error objects
          (patch by Scott Talbert).
        * Change URLs to new curl mailing list (patch by Michael C).
        * Add missing HTTPS proxy options (patch by Jean Hominal).
        * Add support for setting CURLOPT_SSLCERT_BLOB
          (patch by Vesa Jääskeläinen).
        * Add support for setting rest of CURLOPTTYPE_BLOB fields
          (patch by Vesa Jääskeläinen).
        * Build wheels on Linux/macOS/Windows (patch by Scott Talbert).

Version 7.45.2 [requires libcurl-7.19.0 or better] - 2022-12-16
---------------------------------------------------------------

        * Python 3.9 compatibility for Py_TRASHCAN_SAFE_BEGIN
          (patch by Scott Talbert).
        * Add support for CURL_HTTP_VERSION_3 (patch by Scott Talbert).
        * Add CURLOPT_TLS13_CIPHERS and CURLOPT_PROXY_TLS13_CIPHERS options
          (patch by Scott Talbert).
        * Added HTTP09_ALLOWED option (patch by Scott Talbert).
        * Removed use of distutils (patch by Scott Talbert).


Version 7.45.1 [requires libcurl-7.19.0 or better] - 2022-03-13
---------------------------------------------------------------

        * Fixed build against libcurl < 7.64.1 (patch by Scott Talbert).


Version 7.45.0 [requires libcurl-7.64.1 or better] - 2022-03-09
---------------------------------------------------------------

        * Add CURLOPT_MAXLIFETIME_CONN (patch by fsbs).

        * Easy handle duplication support (patch by fsbs).

        * Support for unsetting a number of multi options (patch by fsbs).

        * pycurl classes can now be subclassed (patch by fsbs).

        * Multi callbacks' thread state management fixed (patch by fsbs).

        * Add CURL_LOCK_DATA_PSL (patch by fsbs).

        * Add support for SecureTransport SSL backend (MacOS)
          (patch by Scott Talbert).


Version 7.44.1 [requires libcurl-7.19.0 or better] - 2021-08-15
---------------------------------------------------------------

        * Fixed Python thread initialization causing hangs on operations
          (patch by Scott Talbert).


Version 7.44.0 [requires libcurl-7.19.0 or better] - 2021-08-08
---------------------------------------------------------------

        * getinfo(CURLINFO_FTP_ENTRY_PATH) now handles NULL return from
          libcurl, returning None in this case.
        
        * Python 3.9 is now officially supported (patch by Bill Collins).
        
        * Added CURLOPT_DOH_URL (patch by resokou).
        
        * Best effort Python 2 support has been reinstated.
        
        * Added missing fields to curl_version_info struct (patch by Hasan).
        
        * Added CURLINFO_CONDITION_UNMET (patch by Dima Tisnek).
        
        * Exposed MAX_CONCURRENT_STREAMS in CurlMulti (patch by Alexandre Pion).
        
        * Compilation fixed against Python 3.10 alpha (patch by Kamil Dudka).


Version 7.43.0.6 [requires libcurl-7.19.0 or better] - 2020-09-02
-----------------------------------------------------------------

        * Fixed offset parameter usage in seek callback (patch by Scott Talbert).

        * Added support for libcurl SSL backend detection via
          `curl-config --ssl-backends` (patch by Scott Talbert).

        * Added support for libcurl MultiSSL (patch by Bo Anderson).

        * Added ability to unset CURLOPT_PROXY.

        * Added support for CURLOPT_UPLOAD_BUFFERSIZE (patch by Artur Sobierak).

        * Added support for CURLOPT_MAXAGE_CONN (patch by Artur Sobierak).

        * Added support for sharing connection cache in libcurl (patch by
          Artur Sobierak).

        * Added support for CURLOPT_HAPROXYPROTOCOL (patch by
          Russell McConnachie).

        * CC and CFLAGS environment variables are now respected when building
          (patch by Michał Górny).

        * Fixed OpenSSL detection on CentOS 7 and 8 (patch by Nicolas Pauss).

        * surrogateescape error handler is used in multi_info_read to handle
          invalid UTF-8.


Version 7.43.0.5 [requires libcurl-7.19.0 or better] - 2020-01-29
-----------------------------------------------------------------

        * Fixed build with recent Pythons on RHEL/CentOS.


Version 7.43.0.4 [requires libcurl-7.19.0 or better] - 2020-01-15
-----------------------------------------------------------------

        * Minimum supported Python 3 version is now 3.5.

        * Python 2 is no longer officially supported.
        
        * Improved thread safety of multi code.
        
        * Added Python 3.8 support (patch by Michael Treanor).
        
        * Fixed link order when linking statically against OpenSSL (patch by
          Ashley Whetter).
        
        * Fixed Darwin detection.
        
        * Added support for wolfSSL (patch by Eneas U de Queiroz).
        
        * Added PROXY_SSL_VERIFYHOST (patch by Amir Rossert).


Version ******** [requires libcurl-7.19.0 or better] - 2019-06-17
-----------------------------------------------------------------

        * Fixed use with libcurl 7.65+ when FTP support is disabled.

        * Added support for mbedTLS (patch by Josef Schlehofer).

        * Fixed string processing on Python 3 (patch by Dmitriy Taychenachev).

        * Added CURLOPT_TCP_FASTOPEN and CURL_HTTP_VERSION_2_PRIOR_KNOWLEDGE
          (patch by Khavish Anshudass Bhundoo).

        * Repaired inability to install PycURL when libcurl is using an SSL
          backend other than the ones PycURL explicitly recognizes and
          handles (OpenSSL, LibreSSL, BoringSSL, GnuTLS, NSS).
          The requirement for setup.py to detect an SSL backend if libcurl
          is configured to use SSL, added in ********, has been changed
          to a warning to allow this.


Version ******** [requires libcurl-7.19.0 or better] - 2018-06-02
-----------------------------------------------------------------

        * Official Windows builds now include HTTP 2 support via
          libnghttp2 and international domain name support via WINIDN.

        * Added perform_rb and perform_rs methods to Curl objects to
          return response body as byte string and string, respectively.

        * Added OPT_COOKIELIST constant for consistency with other
          option constants.

        * PycURL is now able to report errors triggered by libcurl
          via CURLOPT_FAILONERROR mechanism when the error messages are
          not decodable in Python's default encoding (GitHub issue #259).

        * Added getinfo_raw method to Curl objects to return byte strings
          as is from libcurl without attempting to decode them
          (GitHub issue #493).

        * When adding a Curl easy object to CurlMulti via add_handle,
          the easy objects now have their reference counts increased so that
          the application is no longer required to keep references to them
          to keep them from being garbage collected (GitHub issue #171).

        * PycURL easy, multi and share objects can now be weak referenced.

        * Python 3.2 and 3.3 support officially dropped as those versions
          are end of lifed.

        * set_ca_certs now accepts byte strings as it should have been
          all along.

        * PycURL now skips automatic SSL backend detection if curl-config
          indicates that libcurl is not built with SSL support, and will warn
          if an SSL backend is explicitly specified in this case.

        * PycURL now requires that SSL backend is determined by setup.py
          to provide earlier failure compared to the existing warning
          during compilation and failing during module import on mismatched
          SSL backends.

        * Use OpenSSL 1.1 and 1.0 specific APIs for controlling thread locks
          depending on OpenSSL version (patch by Vitaly Murashev).

        * Fixed a crash when closesocket callback failed (patch by
          Gisle Vanem and toddrme2178).

        * Added CURLOPT_PROXY_SSLCERT, CURLOPT_PROXY_SSLCERTTYPE,
          CURLOPT_PROXY_SSLKEY, CURLOPT_PROXY_SSLKEYTYPE,
          CURLOPT_PROXY_SSL_VERIFYPEER (libcurl 7.52.0+,
          patch by Casey Miller).

        * Added CURLOPT_PRE_PROXY (libcurl 7.52.0+, patch by ziggy).

        * Support for Python 2.6 officially dropped.

        * Added SOCKET_BAD constant and it is now recognized as a valid
          return value from OPENSOCKET callback.

        * BoringSSL is now recognized as equivalent to OpenSSL backend
          (patch by Gisle Vanem).


Version 7.43.0.1 [requires libcurl-7.19.0 or better] - 2017-12-07
-----------------------------------------------------------------

        * WRITEHEADER/WRITEFUNCTION and WRITEDATA/WRITEFUNCTION can now
          be set on the same handle. The last call will take precedence over
          previous calls. Previously some combinations were not allowed.

        * Fixed a crash when using WRITEDATA with a file-like object followed
          by WRITEDATA with a real file object (patch by Léo El Amri).

        * Fixed a theoretical memory leak in module initialization (patch by
          ideal).

        * Added support for CURL_SSLVERSION_MAX_* constants (libcurl 7.52.0+,
          patch by Jozef Melicher).

        * Added support for CURLSSH_AUTH_AGENT (libcurl 7.28.0+,
          patch by kxrd).

        * Added support for CURLOPT_CONNECT_TO (patch by Iain R. Learmonth).

        * Added support for CURLINFO_HTTP_VERSION (patch by Iain R. Learmonth).

        * Fixed build against OpenSSL l.1 on Windows.

        * Added set_ca_certs method to the Easy object to set CA certificates
          from a string (OpenSSL only, patch by Lipin Dmitriy).

        * Python 3.6 is now officially supported (patch by Samuel
          Dion-Girardeau).

        * Added support for CURLOPT_PROXY_CAPATH (libcurl 7.52.0+,
          patch by Jan Kryl).

        * C-Ares updated to 1.12.0 in Windows builds, fixing DNS resolution
          issues on Windows (patch by Wei C).

        * Added --openssl-lib-name="" option to support building against
          OpenSSL 1.1.0 on Windows.

        * Fixed a possible double free situation in all Curl objects
          due to a misuse of the trashcan API (patch by Benjamin Peterson).

        * High level Curl objects can now be reused.

        * LARGE options fixed under Windows and Python 3 (INFILESIZE,
          MAX_RECV_SPEED_LARGE, MAX_SEND_SPEED_LARGE, MAXFILESIZE,
          POSTFILESIZE, RESUME_FROM).

        * Fixed compilation on Solaris (patch by Yiteng Zhang).

        * ENCODING option can now be unset (patch by Yves Bastide).


Version 7.43.0 [requires libcurl-7.19.0 or better] - 2016-02-02
---------------------------------------------------------------

        * Added CURLINFO_RTSP_* constants (libcurl 7.20.0+).

        * Added CURLOPT_XOAUTH2_BEARER (libcurl 7.33.0+).

        * Added CURLOPT_SASL_IR (libcurl 7.31.0+).

        * Added CURLOPT_LOGIN_OPTIONS (libcurl 7.34.0+).

        * Added CURLOPT_FTP_USE_PRET (libcurl 7.20.0+).

        * Added setopt_string method to Curl objects to set arbitrary
          string options.

        * Switched to Bintray for hosting release distributions.

        * Added CURLOPT_DEFAULT_PROTOCOL (libcurl 7.45.0+).

        * Added CURLOPT_TLSAUTH_* options (libcurl 7.21.4+).

        * Added CURLPROTO_SMB and CURLPROTO_SMBS constants (libcurl 7.40.0+).

        * Added CURL_SOCKOPT_* constants (libcurl 7.21.5+).

        * Added CURL_HTTP_VERSION_2_0, CURL_HTTP_VERSION_2 and
          CURL_HTTP_VERSION_2TLS constants for CURLOPT_HTTP_VERSION
          (various libcurl versions required for these).

        * winbuild.py can now build binary wheels on Windows.

        * Added failed memory allocation handling during SSL lock initialization.

        * CURLOPT_IOCTLDATA option support has been removed.
          This option is used internally by PycURL and is not settable by
          applications.

        * HTTPHEADER and PROXYHEADER options can now be unset.

        * Added CURLPIPE_* constants (libcurl 7.43.0+).

        * Added CURLOPT_PIPEWAIT (libcurl 7.43.0+).

        * Added CURLOPT_PATH_AS_IS (libcurl 7.42.0+).

        * Added CURLOPT_PROXYHEADER and CURLOPT_HEADEROPT as well as
          CURLHEADER_UNIFIED and CURLHEADER_SEPARATE (libcurl 7.37.0+).

        * Added CURLOPT_EXPECT_100_TIMEOUT_MS (libcurl 7.36.0+).

        * Added CURLOPT_XFERINFOFUNCTION (libcurl 7.32.0+).

        * Added CURLM_ADDED_ALREADY error constant (libcurl 7.32.1+).

        * Added remaining CURLE_* constants through libcurl 7.46.0.

        * Unbroken `curl' module import on Windows - apparently Windows now
          has a `signal' Python module but no `SIGPIPE' (patch by Gabi Davar).

        * Added CURLMOPT_PIPELINING_SITE_BL and CURLMOPT_PIPELINING_SERVER_BL
          options (libcurl 7.30.0+).

        * Added CURLOPT_TCP_KEEPALIVE, CURLOPT_TCP_KEEPIDLE and
          CURLOPT_TCP_KEEPINTVL options (libcurl 7.25.0+).

        * Added CURLOPT_ACCEPTTIMEOUT_MS (libcurl 7.24.0+).

        * Added CURLOPT_ACCEPT_ENCODING and CURLOPT_TRANSFER_ENCODING
          options (libcurl 7.21.6+).

        * OPENSOCKETFUNCTION callback for AF_UNIX sockets was mistakenly
          invoked with the address as a `string' rather than `bytes' on
          Python 3. The callback now receives a `bytes' instance as was
          documented.


Version 7.21.5 [requires libcurl-7.19.0 or better] - 2016-01-05
---------------------------------------------------------------

        * --with-openssl and its --win-ssl alias setup.py options are now
          accepted under Windows in order to use OpenSSL's crypto locks
          when building against OpenSSL.

        * --with-openssl added as an alias for --with-ssl option to setup.py.

        * Official Windows builds are now linked against C-Ares and libssh2.

        * Official Windows builds are now linked against OpenSSL instead of
          WinSSL.

        * Official Windows builds are now statically linked against
          their dependencies (libcurl and zlib).

        * Added CURLOPT_USE_SSL and CURLUSESSL_* constants.

        * Added CURLOPT_APPEND, CURLOPT_COOKIESESSION, CURLOPT_DIRLISTONLY,
          CURLOPT_KEYPASSWD, CURLOPT_TELNETOPTIONS.

        * Several CURLE_* and CURLM_* constants added.

        * Add VERSION_* constants, corresponding to CURL_VERSION_*.

        * Breaking change: OPENSOCKETFUNCTION callback API now mirrors that
          of libcurl:
          1. The callback now takes two arguments, `purpose' and `address`.
             Previously the callback took `family', `socktype', `protocol`
             and `addr' arguments.
          2. The second argument to the callback, `address', is a
             `namedtuple' with `family', `socktype', `protocol' and
             `addr' fields.
          3. `addr' field on `address' for AF_INET6 family addresses is a
             4-tuple of (address, port, flow info, scope id) which matches
             Python's `socket.getaddrinfo' API.

          It seems that libcurl may mishandle error return from an
          opensocket callback, as would happen when code written for
          pre-PycURL 7.21.5 API is run with PycURL 7.21.5 or newer,
          resulting in the application hanging.

        * OPENSOCKETFUNCTION callback can now be unset.

        * Added CURLOPT_CLOSESOCKETFUNCTION (libcurl 7.21.7+).
          CURLOPT_CLOSESOCKETDATA is used internally by PycURL.

        * Added CURLOPT_SOCKOPTFUNCTION. CURLOPT_SOCKOPTDATA is used
          internally by PycURL.

        * Added CURLOPT_SSH_KEYFUNCTION (libcurl 7.19.6+).
          CURLOPT_SSH_KEYDATA is used internally by PycURL.

        * Added CURLOPT_SSL_OPTIONS (libcurl 7.25.0+).

        * Added CURLOPT_KRBLEVEL.

        * Added CURLOPT_SSL_FALSESTART (libcurl 7.42.0+).

        * Added CURLOPT_SSL_ENABLE_NPN (libcurl 7.36.0+).

        * Added CURLOPT_SSL_ENABLE_ALPN (libcurl 7.36.0+).

        * Added CURLOPT_UNIX_SOCKET_PATH (libcurl 7.40.0+).

        * Added CURLOPT_WILDCARDMATCH (libcurl 7.21.0+).

        * C module initialization changed to raise exceptions on failure
          rather than trigger a fatal error and abort the Python interpreter.

        * Added CURLOPT_PINNEDPUBLICKEY (libcurl 7.39.0-7.44.0+
          depending on SSL backend and encoding algorithm).

        * Fixed incorrect detection of libcurl 7.19.5 and 7.19.6
          (thanks to bataniya).


Version 7.19.5.3 [requires libcurl-7.19.0 or better] - 2015-11-03
-----------------------------------------------------------------

        * python and nosetests binaries can now be overridden when running
          the test suite (patch by Kamil Dudka).

        * Files needed to run the test suite are distributed in sdist
          (patch by Kamil Dudka).


Version 7.19.5.2 [requires libcurl-7.19.0 or better] - 2015-11-02
-----------------------------------------------------------------

        * C sources made 64-bit clean on Windows.

        * Support for building against Python 3.5 added to winbuild.py.

        * Fixed build on Windows when using MS SDK 8.1+ or MSVC 14/2015
          (patch by Gisle Vanem).

        * Added automatic SSL library detection on CentOS 6 by loading
          libcurl shared library in setup.py. This automatic detection is
          meant to permit installing pycurl seamlessly via `pip install pycurl`
          on CentOS; as such, it is only employed when no other configuration
          options or configuration environment variables are given to setup.py
          (original patch by Francisco Alves).

        * Added --libcurl-dll option to setup.py to take SSL library
          information out of libcurl shared library (original patch by
          Francisco Alves). This option is only usable
          with Python 2.5 or higher.

        * --with-ssl, --with-gnutls and --with-nss options to setup.py now
          result in PycURL explicitly linking against the respective SSL
          library. Previously setup.py relied on curl-config to supply the
          needed libraries in this case.

        * List and tuples are now accepted in all positions of HTTPPOST
          option values.

        * Tuples are now accepted for options taking list values (e.g.
          HTTPHEADER).

        * Fixed a use after free in HTTPPOST when using FORM_BUFFERPTR with
          a Unicode string (patch by Clint Clayton).

        * Fixed a memory leak in HTTPPOST for multiple FORM_BUFFERPTR
          (patch by Clint Clayton).

        * CURLMOPT_* option constants were mistakenly defined on Curl
          instances but not on CurlMulti instances. These option constants
          are now defined on CurlMulti instances and on pycurl module,
          but not on Curl instances.

        * Fixed several memory leaks when setting string options to
          Unicode values failed.

        * Fixed a memory leak when using POSTFIELDS with unicode objects
          on Python 2 (patch by Clint Clayton).

        * Official support for Python 2.4 and 2.5 dropped. PycURL is no
          longer tested against these Python versions on Travis.

        * Added CURLAUTH_NEGOTIATE (libcurl 7.38.0+), CURLAUTH_NTLM_WB
          (libcurl 7.22.0+), CURLAUTH_ONLY (libcurl 7.21.3+),

        * Added CURLOPT_SERVICE_NAME (libcurl 7.43.0+).

        * Added CURLOPT_PROXY_SERVICE_NAME (libcurl 7.43.0+).

        * Added CURLE_SSL_CRL_BADFILE, CURLE_SSL_INVALIDCERTSTATUS
          (libcurl 7.41.0+), CURLE_SSL_ISSUER_ERROR and
          CURLE_SSL_PINNEDPUBKEYNOTMATCH (libcurl 7.39.0+).

        * Added CURLOPT_SSL_VERIFYSTATUS (libcurl 7.41.0+).

        * Added CURL_SSLVERSION_TLSv1_0, CURL_SSLVERSION_TLSv1_1
          and CURL_SSLVERSION_TLSv1_2 (libcurl 7.34.0+).

        * The second argument of DEBUGFUNCTION callback is now of type bytes on
          Python 3. When response body contains non-ASCII data and
          DEBUGFUNCTION is enabled, this argument would receive non-ASCII data.
          Which encoding this data is in is unknown by PycURL, and e.g. in
          the case of HTTP requires parsing response headers. GitHub issue
          #210, patch by Barry Warsaw with help from Gregory Petukhov.

        * Fixed build on GCC 4.4.5 (patch by Travis Jensen).

        * Added CURLOPT_GSSAPI_DELEGATION, CURLGSSAPI_DELEGATION_FLAG,
          CURLGSSAPI_DELEGATION_NONE and CURLGSSAPI_DELEGATION_POLICY_FLAG
          (libcurl 7.22.0+, patch by Dmitry Ketov).


Version ******** [requires libcurl-7.19.0 or better] - 2015-01-06
-----------------------------------------------------------------

        * Added CURLPROXY_SOCKS4A and CURLPROXY_SOCKS5_HOSTNAME.

        * setup.py now prints PycURL-specific option help when -h is used.

        * LibreSSL is now supported (patch by JiCiT).

        * Fixed an oversight that broke PycURL building against libcurl 7.19.4
          through 7.21.1. The bug was introduced in PycURL 7.19.5.

        * Tests are now included in source distributions again, thanks to
          Kamil Dudka and Johan Bergstroem.

        * Added CURLOPT_MAIL_FROM and CURLOPT_MAIL_RCPT (libcurl 7.20.0+)
          and CURLOPT_MAIL_AUTH (libcurl 7.25.0+).


Version 7.19.5 [requires libcurl-7.21.2 or better] - 2014-07-12
---------------------------------------------------------------

        * Tests removed from source and binary distributions.

        * Documentation greatly improved. Quickstart guide added.

        * pycurl.Curl, pycurl.CurlMulti and pycurl.CurlShare are now classes
          rather than factory functions. Previously, the classes were "hidden"
          (they were accessible as e.g. type(pycurl.Curl()), but could not be
          instantiated, nor could class methods be obtained from the classes.
          Please see this mailing list post for further information:
          https://curl.haxx.se/mail/curlpython-2014-06/0004.html

        * When passing a file-like object to READDATA option, PycURL was
          mistakenly looking for write method on this object. Now read method
          is looked up, as would be expected.

        * Python 3.4 is now officially supported.

        * Windows packages now build libcurl against zlib.

        * CherryPy is no longer required for the test suite, ssl module from
          the Python standard library is used instead.

        * Fixed a reference leak of SOCKET and TIMER callbacks on
          CurlMulti instances, thanks to Ben Darnell.

        * Fixed build against openssl on cygwin, where pycurl needs to link
          against libcrypto rather than libssl.

        * Added CURLOPT_SSH_KNOWNHOSTS (libcurl 7.19.6+).

        * Added CURLE_FTP_ACCEPT_FAILED (libcurl 7.24.0+).

        * Added CURLE_NOT_BUILT_IN and CURLE_UNKNOWN_OPTION (libcurl 7.21.5+).

        * Added CURL_SEEKFUNC_OK, CURL_SEEKFUNC_FAIL and
          CURL_SEEKFUNC_CANTSEEK. All constants require libcurl 7.19.5+;
          numeric values of CURL_SEEKFUNC_OK and CURL_SEEKFUNC_FAIL were
          understood earlier but constants only exist as of libcurl 7.19.5.

        * Added CURLINFO_CONDITION_UNMET (libcurl 7.19.4+).

        * Added CURLPROXY_HTTP_1_0 (libcurl 7.19.4+).

        * Added CURLOPT_SOCKS5_GSSAPI_SERVICE and
          CURLOPT_SOCKS5_GSSAPI_NEC (libcurl 7.19.4+).

        * Added CURLOPT_TFTP_BLKSIZE (libcurl 7.19.4+).

        * Added CURLOPT_PROTOCOLS, CURLOPT_REDIR_PROTOCOLS and associated
          CURLPROTO_* constants, which require libcurl 7.19.4+.

        * Fixed a reference leak of OPENSOCKET and SEEK callbacks, thanks to
          Ben Darnell.

        * C source is now split into several files.

        * Documentation is now processed by sphinx.


Version ******** [requires libcurl-7.19.0 or better] - 2014-02-05
-----------------------------------------------------------------

        * Added --avoid-stdio setup.py option to avoid passing FILE
          pointers from Python to libcurl. Applies to Python 2 only.

        * Added CURLMOPT_CHUNK_LENGTH_PENALTY_SIZE,
          CURLMOPT_CONTENT_LENGTH_PENALTY_SIZE, CURLMOPT_MAX_HOST_CONNECTIONS
          CURLMOPT_MAX_PIPELINE_LENGTH, CURLMOPT_MAX_TOTAL_CONNECTIONS
          multi options (patch by Jakob Truelsen).

        * SSL detection logic changed to consult `curl-config --static-libs`
          even if `curl-config --libs` succeeded. This should achieve
          pre-7.19.3 behavior with respect to automatic SSL detection
          (patch by Andjelko Horvat).


Version 7.19.3 [requires libcurl-7.19.0 or better] - 2014-01-09
---------------------------------------------------------------

        * Added CURLOPT_NOPROXY.

        * Added CURLINFO_LOCAL_PORT, CURLINFO_PRIMARY_PORT and
          CURLINFO_LOCAL_IP (patch by Adam Jacob Muller).

        * When running on Python 2.x, for compatibility with Python 3.x,
          Unicode strings containing ASCII code points only are now accepted
          in setopt() calls.

        * PycURL now requires that compile time SSL backend used by libcurl
          is the same as the one used at runtime. setup.py supports
          --with-ssl, --with-gnutls and --with-nss options like libcurl does,
          to specify which backend libcurl uses. On some systems PycURL can
          automatically figure out libcurl's backend.
          If the backend is not one for which PycURL provides crypto locks
          (i.e., any of the other backends supported by libcurl),
          no runtime SSL backend check is performed.

        * Default PycURL user agent string is now built at runtime, and will
          include the user agent string of libcurl loaded at runtime rather
          than the one present at compile time.

        * PycURL will now use WSAduplicateSocket rather than dup on Windows
          to duplicate sockets obtained from OPENSOCKETFUNCTION.
          Using dup may have caused crashes, OPENSOCKETFUNCTION should
          now be usable on Windows.

        * A new script, winbuild.py, was added to build PycURL on Windows
          against Python 2.6, 2.7, 3.2 and 3.3.

        * Added CURL_LOCK_DATA_SSL_SESSION (patch by Tom Pierce).

        * Added E_OPERATION_TIMEDOUT (patch by Romuald Brunet).

        * setup.py now handles --help argument and will print PycURL-specific
          configuration options in addition to distutils help.

        * Windows build configuration has been redone:
          PYCURL_USE_LIBCURL_DLL #define is gone, use --use-libcurl-dll
          argument to setup.py to build against a libcurl DLL.
          CURL_STATICLIB is now #defined only when --use-libcurl-dll is not
          given to setup.py, and PycURL is built against libcurl statically.
          --libcurl-lib-name option can be used to override libcurl import
          library name.

        * Added CURLAUTH_DIGEST_IE as pycurl.HTTPAUTH_DIGEST_IE.

        * Added CURLOPT_POSTREDIR option and CURL_REDIR_POST_301,
          CURL_REDIR_POST_302, CURL_REDIR_POST_303 and CURL_REDIR_POST_ALL
          constants. CURL_REDIR_POST_303 requires libcurl 7.26.0 or higher,
          all others require libcurl 7.19.1 or higher.

        * As part of Python 3 support, WRITEDATA option now accepts
          any object with a write method on Python 2 and Python 3.
          For non-file objects, c.setopt(c.WRITEDATA, buf) is equivalent to
          c.setopt(c.WRITEFUNCTION, buf.write).

        * PycURL now supports Python 3.1 through 3.3. Python 3.0 might
          work but it appears to ship with broken distutils, making virtualenv
          not function on it.

        * PycURL multi objects now have the multi constants defined on them.
          Previously the constants were only available on pycurl module.
          The new behavior matches that of curl and share objects.

        * PycURL share objects can now be closed via the close() method.

        * PycURL will no longer call `curl-config --static-libs` if
          `curl-config --libs` succeeds and returns output.
          Systems on which neither `curl-config --libs` nor
          `curl-config --static-libs` do the right thing should provide
          a `curl-config` wrapper that is sane.

        * Added CURLFORM_BUFFER and CURLFORM_BUFFERPTR.

        * pycurl.version and user agent string now include both
          PycURL version and libcurl version as separate items.

        * Added CURLOPT_DNS_SERVERS.

        * PycURL can now be dynamically linked against libcurl on Windows
          if PYCURL_USE_LIBCURL_DLL is #defined during compilation.

        * Breaking change: opensocket callback now takes an additional
          (address, port) tuple argument. Existing callbacks will need to
          be modified to accept this new argument.
          https://github.com/pycurl/pycurl/pull/18


Version ******** [requires libcurl-7.19.0 or better] - 2013-12-24
-----------------------------------------------------------------

        * Re-release of ******** with minor changes to build Windows packages
          due to botched ******** files on PyPi.
          https://curl.haxx.se/mail/curlpython-2013-12/0021.html


Version ******** [requires libcurl-7.19.0 or better] - 2013-10-08
-----------------------------------------------------------------

        * Fixed a bug in a commit made in 2008 but not released until ********
          which caused CURLOPT_POSTFIELDS to not correctly increment reference
          count of the object being given as its argument, despite libcurl not
          copying the data provided by said object.

        * Added support for libcurl pause/unpause functionality,
          via curl_easy_pause call and returning READFUNC_PAUSE from
          read callback function.


Version ******** [requires libcurl-7.19.0 or better] - 2013-09-23
-----------------------------------------------------------------

        * Test matrix tool added to test against all supported Python and
          libcurl versions.

        * Python 2.4 is now the minimum required version.

        * Source code, bugs and patches are now kept on GitHub.

        * Added CURLINFO_CERTINFO and CURLOPT_CERTINFO.

        * Added CURLOPT_RESOLVE.

        * PycURL can now be used with Python binaries without thread
          support.

        * gcrypt is no longer initialized when a newer version of gnutls
          is used.

        * Marked NSS as supported.

        * Fixed relative URL request logic.

        * Fixed a memory leak in util_curl_init.

        * Added CURLOPT_USERNAME and CURLOPT_PASSWORD.

        * Fixed handling of big timeout values.

        * Added GLOBAL_ACK_EINTR.

        * setopt(..., None) can be used as unsetopt().

        * CURLOPT_RANGE can now be unset.

        * Write callback can return -1 to signal user abort.

        * Reorganized tests into an automated test suite.

        * Added CURLOPT_SEEKFUNCTION and CURLOPT_SEEKDATA.

        * Cleaned up website.

        * Fix pycurl.reset() (patch by <johansen at sun.com>).

        * Fix install routine in setup.py where
          certain platforms (Solaris, Mac OSX, etc)
          would search for a static copy of libcurl (dbp).

        * Fixed build on OpenSolaris 0906 and other platforms on which
          curl-config does not have a --static-libs option.

        * No longer keep string options copies in the
          Curl Python objects, since string options are
          now managed by libcurl.


Version 7.19.0 [requires libcurl-7.19.0 or better]
--------------------------------------------------

        * Added CURLFILE, ADDRESS_SCOPE and ISSUERCERT options,
          as well as the APPCONNECT_TIME info.

        * Added PRIMARY_IP info (patch by
          Yuhui H <eyecat at gmail.com>).

        * Added support for curl_easy_reset through a
          new 'reset' method on curl objects
          (patch by Nick Pilon <npilon at oreilly.com>).

        * Added support for OPENSOCKET callbacks.
          See 'tests/test_opensocket.py' for example
          usage (patch by Thomas Hunger <teh at camvine.com>).


Version 7.18.2
--------------

        * Added REDIRECT_URL info and M_MAXCONNECTS option
          (patch by Yuhui H <eyecat at gmail.com>).

        * Added socket_action() method to CurlMulti objects.
          See 'tests/test_multi_socket_select.py' for example
          usage (patch by Yuhui H <eyecat at gmail.com>).

        * Added AUTOREFERER option.

        * Allow resetting some list operations (HTTPHEADER,
          QUOTE, POSTQUOTE, PREQUOTE) by passing an empty
          list to setopt (patch by Jim Patterson).


Version 7.18.1
--------------

        * Added POST301, SSH_HOST_PUBLIC_KEY_MD5,
          COPYPOSTFIELDS and PROXY_TRANSFER_MODE options.

        * Check for static libs in setup.py to better detect
          whether libcurl was linked with OpenSSL or GNUTLS.

        * PycURL is now dual licensed under the LGPL and
          a license similar to the cURL license (an MIT/X
          derivative).


Version 7.16.4
--------------

        * Allow any callable object as the callback function.
          This change comes handy when you would like to use objects
          which are callable but are not functions or methods, for
          example those objects created by the functions in the functools
          module (patch by Daniel Pena Arteaga <dpena at ph.tum.de>).

        * Added NEW_DIRECTORY_PERMS and NEW_FILE_PERMS options.


Version ********
----------------

        * Added IOCMD_NOP and IOCMD_RESTARTREAD for ioctl callback
          handling (patch by Mark Eichin).

        * Use Py_ssize_t where appropriate for Python 2.5 and 64-bit
          compatibility.  This fixes the problem reported by Aaron
          Hill, where the exception "pycurl.error: (2, '')" is thrown
          when calling setopt(pycurl.POSTFIELDS,...) on 64-bit
          platforms.


Version 7.16.2
--------------

        * Added options HTTP_TRANSFER_DECODING, HTTP_CONTENT_DECODING,
          TIMEOUT_MS, CONNECTTIMEOUT_MS from libcurl 7.16.2.

        * Right-strip URLs read from files in the test scripts
          to avoid sending requests with '\n' at the end.


Version 7.16.1
--------------

        * Added constants for all libcurl (error) return codes.  They
          are named the same as the macro constants in curl.h but prefixed
          with E_ instead of CURLE.  Return codes for the multi API are
          prefixed with M_ instead of CURLM.

        * Added CURLOPT_FTP_SSL_CCC, CURLOPT_SSH_PUBLIC_KEYFILE,
          CURLOPT_SSH_PRIVATE_KEYFILE, CURLOPT_SSH_AUTH_TYPES.

        * Removed CLOSEPOLICY and friends since this option is now
          deprecated in libcurl.

        * Set the _use_datetime attribute on the CURLTransport class
          to unbreak xmlrpc_curl.py on Python 2.5.


Version 7.16.0 [no public release]
--------------

        * Added CURLOPT_SSL_SESSIONID_CACHE.

        * Removed SOURCE_* options since they are no longer
          supported by libcurl.


Version ********
----------------

        * Added test for basic ftp usage (tests/test_ftp.py).

        * Fix broken ssl mutex lock function when using
          GNU TLS (Debian bug #380156, fix by Bastian Kleineidam)


Version 7.15.5
--------------

        * Added CURLOPT_FTP_ALTERNATIVE_TO_USER,
          CURLOPT_MAX_SEND_SPEED_LARGE,
          and CURLOPT_MAX_RECV_SPEED_LARGE.


Version 7.15.4.2
----------------

        * Use SSL locking callbacks, fixes random
          crashes for multithreaded SSL connections
          (patch by Jayne <corvine at gmail.com>).


Version 7.15.4.1
----------------

        * Fixed compilation problem with C compilers
          not allowing declarations in the middle of
          code blocks (patch by
          K.S.Sreeram <sreeram at tachyontech.net>).

        * Fixed bug in curl_multi_fdset wrapping,
          max_fd < 0 is not an error (patch by
          K.S.Sreeram <sreeram at tachyontech.net>).


Version 7.15.4
--------------

        * Added support for libcurl shares, patch from
        Victor Lascurain <bittor at eleka.net>.  See the
        file tests/test_share.py for example usage.

        * Added support for CURLINFO_FTP_ENTRY_PATH.


Version 7.15.2
--------------

        * Added CURLOPT_CONNECT_ONLY, CURLINFO_LASTSOCKET,
          CURLOPT_LOCALPORT and CURLOPT_LOCALPORTRANGE.


Version 7.15.1
--------------

2006-01-31 Kjetil Jacobsen <kjetilja>

        * Fixed memory leak for getinfo calls that return a
          list as result.  Patch by Paul Pacheco.


Version 7.15.0
--------------

2005-10-18  Kjetil Jacobsen  <kjetilja>

        * Added CURLOPT_FTP_SKIP_PASV_IP.


Version 7.14.1
--------------

2005-09-05  Kjetil Jacobsen  <kjetilja>

        * Added CURLOPT_IGNORE_CONTENT_LENGTH, CURLOPT_COOKIELIST as
          COOKIELIST and CURLINFO_COOKIELIST as INFO_COOKIELIST.


Version 7.14.0
--------------

2005-05-18  Kjetil Jacobsen  <kjetilja>

        * Added missing information returned from the info() method
          in the high-level interface.

        * Added the FORM_FILENAME option to the CURLFORM API
          with HTTPPOST.


Version 7.13.2
--------------

2005-03-30  Kjetil Jacobsen  <kjetilja>

        * Unbreak tests/test_gtk.py and require pygtk >= 2.0.

2005-03-15  Kjetil Jacobsen  <kjetilja>

        * Cleaned up several of the examples.

2005-03-11  Kjetil Jacobsen  <kjetilja>

        * WARNING: multi.select() now requires the previously optional
          timeout parameter.  Updated the tests and examples to reflect
          this change.  If the timeout is not set, select could block
          infinitely and cause problems for the internal timeout handling
          in the multi stack.  The problem was identified by
          <unknownsoldier93 at yahoo.com>.


Version 7.13.1
--------------

2005-03-04  Kjetil Jacobsen  <kjetilja>

        * Use METH_NOARGS where appropriate.

2005-03-03  Kjetil Jacobsen  <kjetilja>

        * Added support for CURLFORM API with HTTPPOST: Supports a
          a tuple with pairs of options and values instead of just
          supporting string contents.  See tests/test_post2.py
          for example usage.  Options are FORM_CONTENTS, FORM_FILE and
          FORM_CONTENTTYPE, corresponding to the CURLFORM_* options,
          and values are strings.

2005-02-13  Markus F.X.J. Oberhumer <mfx>

        * Read callbacks (pycurl.READFUNCTION) can now return
          pycurl.READFUNC_ABORT to immediately abort the current transfer.

        * The INFILESIZE, MAXFILESIZE, POSTFIELDSIZE and RESUME_FROM
          options now automatically use the largefile version to handle
          files > 2GB.

        * Added missing pycurl.PORT constant.


Version 7.13.0
--------------

2005-02-10  Kjetil Jacobsen  <kjetilja>

        * Added file_upload.py to examples, shows how to upload
          a file.

        * Added CURLOPT_IOCTLFUNCTION/DATA.

        * Added options from libcurl 7.13.0: FTP_ACCOUNT, SOURCE_URL,
          SOURCE_QUOTE.

        * Obsoleted options: SOURCE_HOST, SOURCE_PATH, SOURCE_PORT,
          PASV_HOST.


Version 7.12.3
--------------

2004-12-22  Markus F.X.J. Oberhumer <mfx>

        * Added CURLINFO_NUM_CONNECTS and CURLINFO_SSL_ENGINES.

        * Added some other missing constants.

        * Updated pycurl.version_info() to return a 12-tuple
          instead of a 9-tuple.


Version 7.12.2
--------------

2004-10-15  Kjetil Jacobsen  <kjetilja>

        * Added CURLOPT_FTPSSLAUTH (and CURLFTPAUTH_*).

        * Added CURLINFO_OS_ERRNO.

2004-08-17 Kjetil Jacobsen <kjetilja>

        * Use LONG_LONG instead of PY_LONG_LONG to make pycurl compile
          on Python versions < 2.3 (fix from Domenico Andreoli
          <cavok at libero.it>).


Version 7.12.1
--------------

2004-08-02  Kjetil Jacobsen  <kjetilja>

        * Added INFOTYPE_SSL_DATA_IN/OUT.

2004-07-16  Markus F.X.J. Oberhumer <mfx>

        * WARNING: removed deprecated PROXY_, TIMECOND_ and non-prefixed
          INFOTYPE constant names. See ChangeLog entry 2003-06-10.

2004-06-21  Kjetil Jacobsen  <kjetilja>

        * Added test program for HTTP post using the read callback (see
          tests/test_post3.py for details).

        * Use the new CURL_READFUNC_ABORT return code where appropriate
          to avoid hanging in perform() when read callbacks are used.

        * Added support for libcurl 7.12.1 CURLOPT features:
          SOURCE_HOST, SOURCE_USERPWD, SOURCE_PATH, SOURCE_PORT,
          PASV_HOST, SOURCE_PREQUOTE, SOURCE_POSTQUOTE.

2004-06-08  Markus F.X.J. Oberhumer <mfx>

        * Setting CURLOPT_POSTFIELDS now allows binary data and
          automatically sets CURLOPT_POSTFIELDSIZE for you. If you really
          want a different size you have to manually set POSTFIELDSIZE
          after setting POSTFIELDS.
          (Based on a patch by Martin Muenstermann).

2004-06-05  Markus F.X.J. Oberhumer <mfx>

        * Added stricter checks within the callback handlers.

        * Unify the behaviour of int and long parameters where appropriate.


Version 7.12
------------

2004-05-18  Kjetil Jacobsen  <kjetilja>

        * WARNING: To simplify code maintenance pycurl now requires
          libcurl 7.11.2 and Python 2.2 or newer to work.

        * GC support is now always enabled.


Version 7.11.3
--------------

2004-04-30  Kjetil Jacobsen  <kjetilja>

        * Do not use the deprecated curl_formparse function.
          API CHANGE: HTTPPOST now takes a list of tuples where each
          tuple contains a form name and a form value, both strings
          (see test/test_post2.py for example usage).

        * Found a possible reference count bug in the multithreading
          code which may have contributed to the long-standing GC
          segfault which has haunted pycurl.  Fingers crossed.


Version 7.11.2
--------------

2004-04-21  Kjetil Jacobsen  <kjetilja>

        * Added support for libcurl 7.11.2 CURLOPT features:
          CURLOPT_TCP_NODELAY.

2004-03-25 Kjetil Jacobsen   <kjetilja>

        * Store Python longs in off_t with PyLong_AsLongLong instead
          of PyLong_AsLong.  Should make the options which deal
          with large files behave a little better.  Note that this
          requires the long long support in Python 2.2 or newer to
          work properly.


Version 7.11.1
--------------

2004-03-16  Kjetil Jacobsen  <kjetilja>

        * WARNING: Removed support for the PASSWDFUNCTION callback, which
          is no longer supported by libcurl.

2004-03-15  Kjetil Jacobsen  <kjetilja>

        * Added support for libcurl 7.11.1 CURLOPT features:
          CURLOPT_POSTFIELDSIZE_LARGE.


Version 7.11.0
--------------

2004-02-11  Kjetil Jacobsen  <kjetilja>

        * Added support for libcurl 7.11.0 CURLOPT features:
          INFILESIZE_LARGE, RESUME_FROM_LARGE, MAXFILESIZE_LARGE
          and FTP_SSL.

        * Circular garbage collection support can now be enabled or
          disabled by passing the '--use-gc=[1|0]' parameter to setup.py
          when building pycurl.

        * HTTP_VERSION options are known as CURL_HTTP_VERSION_NONE,
          CURL_HTTP_VERSION_1_0, CURL_HTTP_VERSION_1_1 and
          CURL_HTTP_VERSION_LAST.

2003-11-16  Markus F.X.J. Oberhumer <mfx>

        * Added support for these new libcurl 7.11.0 features:
          CURLOPT_NETRC_FILE.


Version 7.10.8
--------------

2003-11-04  Markus F.X.J. Oberhumer <mfx>

        * Added support for these new libcurl 7.10.8 features:
          CURLOPT_FTP_RESPONSE_TIMEOUT, CURLOPT_IPRESOLVE,
          CURLOPT_MAXFILESIZE,
          CURLINFO_HTTPAUTH_AVAIL, CURLINFO_PROXYAUTH_AVAIL,
          CURL_IPRESOLVE_* constants.

        * Added support for these new libcurl 7.10.7 features:
          CURLOPT_FTP_CREATE_MISSING_DIRS, CURLOPT_PROXYAUTH,
          CURLINFO_HTTP_CONNECTCODE.


2003-10-28  Kjetil Jacobsen  <kjetilja>

        * Added missing CURLOPT_ENCODING option (patch by Martijn
          Boerwinkel <xim at xs4all.nl>)


Version 7.10.6
--------------

2003-07-29  Markus F.X.J. Oberhumer <mfx>

        * Started working on support for CURLOPT_SSL_CTX_FUNCTION and
          CURLOPT_SSL_CTX_DATA (libcurl-7.10.6) - not yet finished.

2003-06-10  Markus F.X.J. Oberhumer <mfx>

        * Added support for CURLOPT_HTTPAUTH (libcurl-7.10.6), including
          the new HTTPAUTH_BASIC, HTTPAUTH_DIGEST, HTTPAUTH_GSSNEGOTIATE
          and HTTPAUTH_NTML constants.

        * Some constants were renamed for consistency:

          All curl_infotype constants are now prefixed with "INFOTYPE_",
          all curl_proxytype constants are prefixed with "PROXYTYPE_" instead
          of "PROXY_", and all curl_TimeCond constants are now prefixed
          with "TIMECONDITION_" instead of "TIMECOND_".

          (The old names are still available but will get removed
          in a future release.)

        * WARNING: Removed the deprecated pycurl.init() and pycurl.multi_init()
          names - use pycurl.Curl() and pycurl.CurlMulti() instead.

        * WARNING: Removed the deprecated Curl.cleanup() and
          CurlMulti.cleanup() methods - use Curl.close() and
          CurlMulti.close() instead.


Version 7.10.5
--------------

2003-05-15  Markus F.X.J. Oberhumer <mfx>

        * Added support for CURLOPT_FTP_USE_EPRT (libcurl-7.10.5).

        * Documentation updates.

2003-05-07  Eric S. Raymond  <esr>

        * Lifted all HTML docs to clean XHTML, verified by tidy.

2003-05-02  Markus F.X.J. Oberhumer <mfx>

        * Fixed some `int' vs. `long' mismatches that affected 64-bit systems.

        * Fixed wrong pycurl.CAPATH constant.

2003-05-01  Markus F.X.J. Oberhumer <mfx>

        * Added new method Curl.errstr() which returns the internal
        libcurl error buffer string of the handle.


Version ********
----------------

2003-04-15  Markus F.X.J. Oberhumer <mfx>

        * Allow compilation against the libcurl-7.10.3 release - some
        recent Linux distributions (e.g. Mandrake 9.1) ship with 7.10.3,
        and apart from the new CURLOPT_UNRESTRICTED_AUTH option there is
        no need that we require libcurl-7.10.4.


Version 7.10.4
--------------

2003-04-01  Kjetil Jacobsen  <kjetilja>

        * Markus added CURLOPT_UNRESTRICTED_AUTH (libcurl-7.10.4).

2003-02-25  Kjetil Jacobsen  <kjetilja>

        * Fixed some broken test code and removed the fileupload test
        since it didn't work properly.

2003-01-28  Kjetil Jacobsen  <kjetilja>

        * Some documentation updates by Markus and me.

2003-01-22  Kjetil Jacobsen  <kjetilja>

        * API CHANGE: the CurlMulti.info_read() method now returns
        a separate array with handles that failed.  Each entry in this array
        is a tuple with (curl object, error number, error message).
        This addition makes it simpler to do error checking of individual
        curl objects when using the multi interface.


Version 7.10.3
--------------

2003-01-13  Kjetil Jacobsen  <kjetilja>

        * PycURL memory usage has been reduced.

2003-01-10  Kjetil Jacobsen  <kjetilja>

        * Added 'examples/retriever-multi.py' which shows how to retrieve
        a set of URLs concurrently using the multi interface.

2003-01-09  Kjetil Jacobsen  <kjetilja>

        * Added support for CURLOPT_HTTP200ALIASES.

2002-11-22  Kjetil Jacobsen  <kjetilja>

        * Updated pycurl documentation in the 'doc' directory.

2002-11-21  Kjetil Jacobsen  <kjetilja>

        * Updated and improved 'examples/curl.py'.

        * Added 'tests/test_multi6.py' which shows how to use the
        info_read method with CurlMulti.

2002-11-19  Kjetil Jacobsen  <kjetilja>

        * Added new method CurlMulti.info_read().


Version 7.10.2
--------------

2002-11-14  Kjetil Jacobsen <kjetilja>

        * Free options set with setopt after cleanup is called, as cleanup
        assumes that options are still valid when invoked.  This fixes the
        bug with COOKIEJAR reported by Bastiaan Naber
        <bastiaan at ricardis.tudelft.nl>.

2002-11-06  Markus F.X.J. Oberhumer <mfx>

        * Install documentation under /usr/share/doc instead of /usr/doc.
        Also, start shipping the (unfinished) HTML docs and some
        basic test scripts.

2002-10-30  Markus F.X.J. Oberhumer <mfx>

        * API CHANGE: For integral values, Curl.getinfo() now returns a
        Python-int instead of a Python-long.


Version 7.10.1
--------------

2002-10-03  Markus F.X.J. Oberhumer <mfx>

        * Added new module-level function version_info() from
        libcurl-7.10.


Version 7.10
------------

2002-09-13  Kjetil Jacobsen  <kjetilja>

        * Added commandline options to setup.py for specifying the path to
        'curl-config' (non-windows) and the curl installation directory
        (windows).  See the 'INSTALL' file for details.

        * Added CURLOPT_ENCODING, CURLOPT_NOSIGNAL and CURLOPT_BUFFERSIZE
        from libcurl-7.10 (by Markus Oberhumer).


Version *******
---------------

2002-08-28  Kjetil Jacobsen  <kjetilja>

        * Added a simple web-browser example based on gtkhtml and pycurl.
        See the file 'examples/gtkhtml_demo.py' for details.  The example
        requires a working installation of gnome-python with gtkhtml
        bindings enabled (pass --with-gtkhtml to gnome-python configure).

2002-08-14  Kjetil Jacobsen  <kjetilja>

        * Added new method 'select' on CurlMulti objects.  Example usage
        in 'tests/test_multi5.py'.  This method is just an optimization of
        the combined use of fdset and select.

2002-08-12  Kjetil Jacobsen  <kjetilja>

        * Added support for curl_multi_fdset.  See the file
        'tests/test_multi4.py' for example usage.  Contributed by Conrad
        Steenberg <conrad at hep.caltech.edu>.

        * perform() on multi objects now returns a tuple (result, number
        of handles) like the libcurl interface does.

2002-08-08  Kjetil Jacobsen  <kjetilja>

        * Added the 'sfquery' script which retrieves a SourceForge XML
        export object for a given project.  See the file 'examples/sfquery.py'
        for details and usage.  'sfquery' was contributed by Eric
        S. Raymond <esr at thyrsus.com>.

2002-07-20  Markus F.X.J. Oberhumer <mfx>

        * API enhancements: added Curl() and CurlMulti() as aliases for
        init() and multi_init(), and added close() methods as aliases
        for the cleanup() methods. The new names much better match
        the actual intended use of the objects, and they also nicely
        correspond to Python's file object.

        * Also, all constants for Curl.setopt() and Curl.getinfo() are now
        visible from within Curl objects.

        All changes are fully backward-compatible.


Version *******
---------------

2002-07-16  Markus F.X.J. Oberhumer <mfx>

        * Under Python 2.2 or better, Curl and CurlMulti objects now
        automatically participate in cyclic garbage collection
        (using the gc module).


Version *******
---------------

2002-07-05  Markus F.X.J. Oberhumer <mfx>

        * Curl and CurlMulti objects now support standard Python attributes.
        See tests/test_multi2.py for an example.

2002-07-02  Kjetil Jacobsen  <kjetilja>

        * Added support for the multi-interface.


Version *******
---------------

2002-06-25  Markus F.X.J. Oberhumer <mfx>

        * Fixed a couple of `int' vs. `size_t' mismatches in callbacks
        and Py_BuildValue() calls.

2002-06-25  Kjetil Jacobsen  <kjetilja>

        * Use 'double' type instead of 'size_t' for progress callbacks
        (by Conrad Steenberg <conrad at hep.caltech.edu>).  Also cleaned up
        some other type mismatches in the callback interfaces.

2002-06-24  Kjetil Jacobsen  <kjetilja>

        * Added example code on how to upload a file using HTTPPOST in
        pycurl (code by Amit Mongia <amit_mongia at hotmail.com>).  See the
        file 'test_fileupload.py' for details.


Version 7.9.8
-------------

2002-06-24  Kjetil Jacobsen  <kjetilja>

        * Resolved some build problems on Windows (by Markus Oberhumer).

2002-06-19  Kjetil Jacobsen  <kjetilja>

        * Added CURLOPT_CAPATH.

        * Added option constants for CURLOPT_NETRC: CURL_NETRC_OPTIONAL,
        CURL_NETRC_IGNORED and CURL_NETRC_REQUIRED.

        * Added option constants for CURLOPT_TIMECONDITION:
        TIMECOND_IFMODSINCE and TIMECOND_IFUNMODSINCE.

        * Added an simple example crawler, which downloads documents
        listed in a file with a configurable number of worker threads.
        See the file 'crawler.py' in the 'tests' directory for details.

        * Removed the redundant 'test_xmlrpc2.py' test script.

        * Disallow recursive callback invocations (by Markus Oberhumer).

2002-06-18  Kjetil Jacobsen  <kjetilja>

        * Made some changes to setup.py which should fix the build
        problems on RedHat 7.3 (suggested by Benji <benji at kioza.net>).

        * Use CURLOPT_READDATA instead of CURLOPT_INFILE, and
        CURLOPT_WRITEDATA instead of CURLOPT_FILE.  Also fixed some
        reference counting bugs with file objects.

        * CURLOPT_FILETIME and CURLINFO_FILETIME had a namespace clash
        which caused them not to work.  Use OPT_FILETIME for setopt() and
        INFO_FILETIME for getinfo().  See example usage in
        'test_getinfo.py' for details.


Version 7.9.7
-------------

2002-05-20  Kjetil Jacobsen  <kjetilja>

        * New versioning scheme.  Pycurl now has the same version number
        as the libcurl version it was built with.  The pycurl version
        number thus indicates which version of libcurl is required to run.

2002-05-17  Kjetil Jacobsen  <kjetilja>

        * Added CURLINFO_REDIRECT_TIME and CURLINFO_REDIRECT_COUNT.

2002-04-27  Kjetil Jacobsen  <kjetilja>

        * Fixed potential memory leak and thread race (by Markus
        Oberhumer).


Version 0.4.9
-------------

2002-04-15  Kjetil Jacobsen  <kjetilja>

        * Added CURLOPT_DEBUGFUNCTION to allow debug callbacks to be
        specified (see the file 'test_debug.py' for details on how to use
        debug callbacks).

        * Added CURLOPT_DNS_USE_GLOBAL_CACHE and
        CURLOPT_DNS_CACHE_TIMEOUT.

        * Fixed a segfault when finalizing curl objects in Python 1.5.2.

        * Now requires libcurl 7.9.6 or greater.

2002-04-12  Kjetil Jacobsen  <kjetilja>

        * Added 'test_post2.py' file which is another example on how to
        issue POST requests.

2002-04-11  Markus F.X.J. Oberhumer <mfx>

        * Added the 'test_post.py' file which demonstrates the use of
        POST requests.


Version 0.4.8
-------------

2002-03-07  Kjetil Jacobsen  <kjetilja>

        * Added CURLOPT_PREQUOTE.

        * Now requires libcurl 7.9.5 or greater.

        * Other minor code cleanups and bugfixes.

2002-03-05  Kjetil Jacobsen  <kjetilja>

        * Do not allow WRITEFUNCTION and WRITEHEADER on the same handle.


Version 0.4.7
-------------

2002-02-27  Kjetil Jacobsen  <kjetilja>

        * Abort callback if the thread state of the calling thread cannot
        be determined.

        * Check that the installed version of libcurl matches the
        requirements of pycurl.

2002-02-26  Kjetil Jacobsen  <kjetilja>

        * Clarence Garnder <clarence at silcom.com> found a bug where string
        arguments to setopt sometimes were prematurely deallocated, this
        should now be fixed.

2002-02-21  Kjetil Jacobsen  <kjetilja>

        * Added the 'xmlrpc_curl.py' file which implements a transport
        for xmlrpclib (xmlrpclib is part of Python 2.2).

        * Added CURLINFO_CONTENT_TYPE.

        * Added CURLOPT_SSLCERTTYPE, CURLOPT_SSLKEY, CURLOPT_SSLKEYTYPE,
        CURLOPT_SSLKEYPASSWD, CURLOPT_SSLENGINE and
        CURLOPT_SSLENGINE_DEFAULT.

        * When thrown, the pycurl.error exception is now a tuple consisting
        of the curl error code and the error message.

        * Now requires libcurl 7.9.4 or greater.

2002-02-19  Kjetil Jacobsen  <kjetilja>

        * Fixed docstring for getopt() function.

2001-12-18  Kjetil Jacobsen  <kjetilja>

        * Updated the INSTALL information for Win32.

2001-12-12  Kjetil Jacobsen  <kjetilja>

        * Added missing link flag to make pycurl build on MacOS X (by Matt
        King <matt at gnik.com>).

2001-12-06  Kjetil Jacobsen  <kjetilja>

        * Added CURLINFO_STARTTRANSFER_TIME and CURLOPT_FTP_USE_EPSV from
        libcurl 7.9.2.

2001-12-01  Markus F.X.J. Oberhumer <mfx>

        * Added the 'test_stringio.py' file which demonstrates the use of
        StringIO objects as callback.

2001-12-01  Markus F.X.J. Oberhumer <mfx>

        * setup.py: Do not remove entries from a list while iterating
        over it.

2001-11-29  Kjetil Jacobsen  <kjetilja>

        * Added code in setup.py to install on Windows.  Requires some
        manual configuration (by Tino Lange <Tino.Lange at gmx.de>).

2001-11-27  Kjetil Jacobsen  <kjetilja>

        * Improved detection of where libcurl is installed in setup.py.
        Should make it easier to install pycurl when libcurl is not
        located in regular lib/include paths.

2001-11-05  Kjetil Jacobsen  <kjetilja>

        * Some of the newer options to setopt were missing, this should
        now be fixed.

2001-11-04  Kjetil Jacobsen  <kjetilja>

        * Exception handling has been improved and should no longer throw
        spurious exceptions (by Markus F.X.J. Oberhumer
        <markus at oberhumer.com>).

2001-10-15  Kjetil Jacobsen  <kjetilja>

        * Refactored the test_gtk.py script to avoid global variables.

2001-10-12  Kjetil Jacobsen  <kjetilja>

        * Added module docstrings, terse perhaps, but better than nothing.

        * Added the 'basicfirst.py' file which is a Python version of the
        corresponding Perl script by Daniel.

        * PycURL now works properly under Python 1.5 and 1.6 (by Markus
        F.X.J. Oberhumer <markus at oberhumer.com>).

        * Allow C-functions and Python methods as callbacks (by Markus
        F.X.J. Oberhumer <markus at oberhumer.com>).

        * Allow None as success result of write, header and progress
        callback invocations (by Markus F.X.J. Oberhumer
        <markus at oberhumer.com>).

        * Added the 'basicfirst2.py' file which demonstrates the use of a
        class method as callback instead of just a function.

2001-08-21  Kjetil Jacobsen  <kjetilja>

        * Cleaned up the script with GNOME/PycURL integration.

2001-08-20  Kjetil Jacobsen  <kjetilja>

        * Added another test script for shipping XML-RPC requests which
        uses py-xmlrpc to encode the arguments (tests/test_xmlrpc2.py).

2001-08-20  Kjetil Jacobsen  <kjetilja>

        * Added test script for using PycURL and GNOME (tests/test_gtk.py).

2001-08-20  Kjetil Jacobsen  <kjetilja>

        * Added test script for using XML-RPC (tests/test_xmlrpc.py).

        * Added more comments to the test sources.

2001-08-06  Kjetil Jacobsen  <kjetilja>

        * Renamed module namespace to pycurl instead of curl.

2001-08-06  Kjetil Jacobsen  <kjetilja>

        * Set CURLOPT_VERBOSE to 0 by default.

2001-06-29  Kjetil Jacobsen  <kjetilja>

        * Updated INSTALL, curl version 7.8 or greater is now mandatory to
        use pycurl.

2001-06-13  Kjetil Jacobsen  <kjetilja>

        * Set NOPROGRESS to 1 by default.

2001-06-07  Kjetil Jacobsen  <kjetilja>

        * Added global_init/cleanup.

2001-06-06  Kjetil Jacobsen  <kjetilja>

        * Added HEADER/PROGRESSFUNCTION callbacks (see files in tests/).

        * Added PASSWDFUNCTION callback (untested).

        * Added READFUNCTION callback (untested).

2001-06-05  Kjetil Jacobsen  <kjetilja>

        * WRITEFUNCTION callbacks now work (see tests/test_cb.py for details).

        * Preliminary distutils installation.

        * Added CLOSEPOLICY constants to module namespace.

2001-06-04  Kjetil Jacobsen  <kjetilja>

        * Return -1 on error from Python callback in WRITEFUNCTION callback.

2001-06-01  Kjetil Jacobsen  <kjetilja>

        * Moved source to src and tests to tests directory.

2001-05-31  Kjetil Jacobsen  <kjetilja>

        * Added better type checking for setopt.

2001-05-30  Kjetil Jacobsen  <kjetilja>

        * Moved code to sourceforge.

        * Added getinfo support.


# vi:ts=8:et
