#!/usr/bin/env python3
"""
本地开发环境启动脚本
启动改进版股票搜索系统（无需Docker）
"""

import subprocess
import sys
import time
import requests
import os
from pathlib import Path


def install_dependencies():
    """安装必要的依赖"""
    print("📦 检查并安装依赖...")
    
    required_packages = [
        "fastapi",
        "uvicorn[standard]", 
        "pandas",
        "requests",
        "python-dotenv",
        "pydantic",
        "sqlalchemy",
        "psycopg2-binary",
        "tushare"
    ]
    
    for package in required_packages:
        try:
            __import__(package.split('[')[0].replace('-', '_'))
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📦 安装 {package}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {package} 安装失败")
                return False
    
    return True


def setup_environment():
    """设置环境变量"""
    print("🔧 设置环境变量...")
    
    # 设置基本环境变量
    os.environ["DATABASE_URL"] = "sqlite:///./openbb_cn.db"  # 使用SQLite简化开发
    os.environ["APP_HOST"] = "127.0.0.1"
    os.environ["APP_PORT"] = "6900"
    
    # 检查.env文件
    env_file = Path(".env")
    if env_file.exists():
        print("✅ 找到.env文件")
        from dotenv import load_dotenv
        load_dotenv()
    else:
        print("⚠️ 未找到.env文件，使用默认配置")
        # 创建基本的.env文件
        with open(".env", "w", encoding="utf-8") as f:
            f.write("# OpenBB-CN 开发环境配置\n")
            f.write("DATABASE_URL=sqlite:///./openbb_cn.db\n")
            f.write("APP_HOST=127.0.0.1\n")
            f.write("APP_PORT=6900\n")
            f.write("# TUSHARE_TOKEN=你的token  # 可选，用于真实数据\n")
        print("✅ 创建了基本的.env文件")


def check_service(url, name, timeout=5):
    """检查服务是否运行"""
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            print(f"✅ {name} 运行正常")
            return True
    except:
        pass
    return False


def start_api_server():
    """启动API服务器"""
    print("🚀 启动OpenBB-CN API服务器...")
    
    try:
        # 检查是否已经运行
        if check_service("http://127.0.0.1:6900", "API服务"):
            print("⚠️ 服务已在运行")
            return True
        
        # 启动服务
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "openbb_cn.api.app:app",
            "--host", "127.0.0.1",
            "--port", "6900",
            "--reload",
            "--log-level", "info"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        process = subprocess.Popen(
            cmd,
            cwd=Path.cwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print(f"✅ 服务启动中... (PID: {process.pid})")
        
        # 等待服务启动并显示日志
        print("⏳ 等待服务启动...")
        start_time = time.time()
        
        while time.time() - start_time < 30:  # 最多等待30秒
            # 检查进程是否还在运行
            if process.poll() is not None:
                print("❌ 服务进程已退出")
                # 读取错误输出
                stdout, stderr = process.communicate()
                if stdout:
                    print("输出:", stdout)
                return False
            
            # 检查服务是否可访问
            if check_service("http://127.0.0.1:6900", "API服务", timeout=2):
                print("🎉 API服务启动成功！")
                return True
            
            time.sleep(1)
            print(".", end="", flush=True)
        
        print("\n⚠️ 服务启动超时")
        return False
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False


def show_usage_info():
    """显示使用说明"""
    print("\n" + "="*80)
    print("🎉 改进版股票搜索系统已启动！")
    print("="*80)
    
    print("\n🌐 访问地址:")
    print("- 📊 股票搜索界面: http://127.0.0.1:6900/ui")
    print("- 📋 API文档: http://127.0.0.1:6900/docs")
    print("- 🏠 首页: http://127.0.0.1:6900/")
    
    print("\n✨ 新功能特性:")
    print("- 🔍 智能搜索：输入股票名称或代码自动搜索")
    print("- ⚡ 自动加载：删除加载按钮，搜索即加载")
    print("- 🇨🇳 中国股市：自动匹配上海/深圳交易所")
    print("- 💡 实时反馈：输入即搜索，智能防抖")
    print("- 🎨 界面优化：更好的用户体验和视觉效果")
    
    print("\n💡 使用方法:")
    print("1. 打开浏览器访问: http://127.0.0.1:6900/ui")
    print("2. 在搜索框中输入:")
    print("   📝 股票名称: 平安银行、贵州茅台、五粮液")
    print("   🔢 6位代码: 000001、600519、000858")
    print("   📋 完整代码: 000001.SZ、600519.SH")
    print("3. 系统自动搜索并加载第一个匹配结果")
    print("4. 支持实时搜索，输入即可看到结果")
    
    print("\n🧪 测试建议:")
    test_cases = [
        ("平安银行", "中文名称搜索"),
        ("000001", "6位代码自动匹配"),
        ("贵州茅台", "热门股票名称"),
        ("600519", "上海股票代码"),
        ("五粮液", "消费股搜索"),
        ("000858.SZ", "完整代码格式")
    ]
    
    for i, (case, desc) in enumerate(test_cases, 1):
        print(f"  {i}. {case:<10} - {desc}")
    
    print("\n🔧 技术改进:")
    improvements = [
        "删除加载按钮，搜索即加载",
        "智能代码匹配(.SH/.SZ自动添加)",
        "中文名称模糊搜索优化",
        "实时搜索防抖(300-600ms)",
        "搜索结果智能排序",
        "用户体验改进(加载状态、错误提示)",
        "界面样式优化和动画效果"
    ]
    
    for improvement in improvements:
        print(f"  ✅ {improvement}")


def main():
    """主函数"""
    print("🚀 OpenBB-CN 本地开发环境启动器")
    print("=" * 60)
    
    try:
        # 1. 安装依赖
        if not install_dependencies():
            print("❌ 依赖安装失败")
            return
        
        # 2. 设置环境
        setup_environment()
        
        # 3. 启动API服务
        if start_api_server():
            show_usage_info()
            
            print("\n" + "="*80)
            print("🔄 服务运行中... 按 Ctrl+C 停止")
            print("="*80)
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 正在停止服务...")
        else:
            print("❌ 服务启动失败")
            print("\n🔧 可能的解决方案:")
            print("1. 检查端口6900是否被占用")
            print("2. 确保所有依赖已正确安装")
            print("3. 检查Python版本 (建议3.8+)")
            print("4. 查看上方的错误信息")
    
    except Exception as e:
        print(f"❌ 启动过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
