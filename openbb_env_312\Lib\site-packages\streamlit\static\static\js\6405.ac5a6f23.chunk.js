"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[6405],{16405:(e,t,r)=>{r.r(t),r.d(t,{default:()=>A});var n=r(66845);const a=(0,r(74559).z)("div")({name:"NumberOverlayEditorStyle",class:"gdg-n15fjm3e",propsAsIs:!1});function o(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)t.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]])}return r}var u;function i(){}function l(e){return!!(e||"").match(/\d/)}function s(e){return null===e||void 0===e}function c(e){return"number"===typeof e&&isNaN(e)}function f(e){return e.replace(/[-[\]/{}()*+?.\\^$|]/g,"\\$&")}function v(e,t){void 0===t&&(t=!0);var r="-"===e[0],n=r&&t,a=(e=e.replace("-","")).split(".");return{beforeDecimal:a[0],afterDecimal:a[1]||"",hasNegation:r,addNegation:n}}function d(e,t,r){for(var n="",a=r?"0":"",o=0;o<=t-1;o++)n+=e[o]||a;return n}function g(e,t){return Array(t+1).join(e)}function m(e){var t=e+"",r="-"===t[0]?"-":"";r&&(t=t.substring(1));var n=t.split(/[eE]/g),a=n[0],o=n[1];if(!(o=Number(o)))return r+a;var u=1+o,i=(a=a.replace(".","")).length;return u<0?a="0."+g("0",Math.abs(u))+a:u>=i?a+=g("0",u-i):a=(a.substring(0,u)||"0")+"."+a.substring(u),r+a}function p(e,t,r){if(-1!==["","-"].indexOf(e))return e;var n=(-1!==e.indexOf(".")||r)&&t,a=v(e),o=a.beforeDecimal,u=a.afterDecimal,i=a.hasNegation,l=parseFloat("0."+(u||"0")),s=(u.length<=t?"0."+u:l.toFixed(t)).split(".");return""+(i?"-":"")+o.split("").reverse().reduce((function(e,t,r){return e.length>r?(Number(e[0])+Number(t)).toString()+e.substring(1,e.length):t+e}),s[0])+(n?".":"")+d(s[1]||"",t,r)}function h(e,t){if(e.value=e.value,null!==e){if(e.createTextRange){var r=e.createTextRange();return r.move("character",t),r.select(),!0}return e.selectionStart||0===e.selectionStart?(e.focus(),e.setSelectionRange(t,t),!0):(e.focus(),!1)}}function S(e){return{from:{start:0,end:0},to:{start:0,end:e.length},lastValue:""}}function b(e,t,r,n){var a,o,u,i=e.length;if(a=t,o=0,u=i,t=Math.min(Math.max(a,o),u),"left"===n){for(;t>=0&&!r[t];)t--;-1===t&&(t=r.indexOf(!0))}else{for(;t<=i&&!r[t];)t++;t>i&&(t=r.lastIndexOf(!0))}return-1===t&&(t=i),t}function y(e){for(var t=Array.from({length:e.length+1}).map((function(){return!0})),r=0,n=t.length;r<n;r++)t[r]=Boolean(l(e[r])||l(e[r-1]));return t}function w(e,t,r,a,o,u){void 0===u&&(u=i);var l=(0,n.useRef)(),f=function(e){var t=(0,n.useRef)(e);t.current=e;var r=(0,n.useRef)((function(){for(var e=[],r=arguments.length;r--;)e[r]=arguments[r];return t.current.apply(t,e)}));return r.current}((function(e){var t,n;return s(e)||c(e)?(n="",t=""):"number"===typeof e||r?(n="number"===typeof e?m(e):e,t=a(n)):(n=o(e,void 0),t=e),{formattedValue:t,numAsString:n}})),v=(0,n.useState)((function(){return f(t)})),d=v[0],g=v[1];return(0,n.useMemo)((function(){s(e)?l.current=void 0:(l.current=f(e),g(l.current))}),[e,f]),[d,function(e,t){g({formattedValue:e.formattedValue,numAsString:e.value}),u(e,t)}]}function x(e){return e.replace(/[^0-9]/g,"")}function D(e){return e}function O(e){var t=e.type;void 0===t&&(t="text");var r=e.displayType;void 0===r&&(r="input");var a=e.customInput,s=e.renderText,c=e.getInputRef,f=e.format;void 0===f&&(f=D);var v=e.removeFormatting;void 0===v&&(v=x);var d=e.defaultValue,g=e.valueIsNumericString,m=e.onValueChange,p=e.isAllowed,S=e.onChange;void 0===S&&(S=i);var O=e.onKeyDown;void 0===O&&(O=i);var V=e.onMouseUp;void 0===V&&(V=i);var N=e.onFocus;void 0===N&&(N=i);var E=e.onBlur;void 0===E&&(E=i);var C=e.value,R=e.getCaretBoundary;void 0===R&&(R=y);var T=o(e,["type","displayType","customInput","renderText","getInputRef","format","removeFormatting","defaultValue","valueIsNumericString","onValueChange","isAllowed","onChange","onKeyDown","onMouseUp","onFocus","onBlur","value","getCaretBoundary"]),A=w(C,d,Boolean(g),f,v,m),B=A[0],j=B.formattedValue,F=B.numAsString,I=A[1],k=(0,n.useRef)();(0,n.useEffect)((function(){var e=f(F);if(void 0===k.current||e!==k.current){var t=L.current;z({formattedValue:e,numAsString:F,input:t,setCaretPosition:!0,source:u.props,event:void 0})}}));var M=(0,n.useState)(!1),P=M[0],K=M[1],L=(0,n.useRef)(null),W=(0,n.useRef)({setCaretTimeout:null,focusTimeout:null});(0,n.useEffect)((function(){return K(!0),function(){clearTimeout(W.current.setCaretTimeout),clearTimeout(W.current.focusTimeout)}}),[]);var U=f,_=function(e,t){var r=parseFloat(t);return{formattedValue:e,value:t,floatValue:isNaN(r)?void 0:r}},G=function(e,t,r){h(e,t),W.current.setCaretTimeout=setTimeout((function(){e.value===r&&h(e,t)}),0)},$=function(e,t,r){return b(e,t,R(e),r)},Z=function(e,t,r){var n=R(t),a=function(e,t,r,n,a){var o=a.findIndex((function(e){return e})),u=e.slice(0,o);t||r.startsWith(u)||(r=u+r,n+=u.length);for(var i=r.length,s=e.length,c={},f=new Array(i),v=0;v<i;v++){f[v]=-1;for(var d=0,g=s;d<g;d++)if(r[v]===e[d]&&!0!==c[d]){f[v]=d,c[d]=!0;break}}for(var m=n;m<i&&(-1===f[m]||!l(r[m]));)m++;var p=m===i||-1===f[m]?s:f[m];for(m=n-1;m>0&&-1===f[m];)m--;var h=-1===m||-1===f[m]?0:f[m]+1;return h>p?p:n-h<p-n?h:p}(t,j,e,r,n);return a=b(t,a,n)},z=function(e){var t=e.formattedValue;void 0===t&&(t="");var r=e.input,n=e.setCaretPosition;void 0===n&&(n=!0);var a,o=e.source,u=e.event,i=e.numAsString,l=e.caretPos;if(r){if(void 0===l&&n){var s=e.inputValue||r.value,c=(a=r,Math.max(a.selectionStart,a.selectionEnd));r.value=t,l=Z(s,t,c)}r.value=t,n&&void 0!==l&&G(r,l,t)}t!==j&&function(e,t){k.current=e.formattedValue,I(e,t)}(_(t,i),{event:u,source:o})},q=!P||"undefined"===typeof navigator||navigator.platform&&/iPhone|iPod/.test(navigator.platform)?void 0:"numeric",H=Object.assign({inputMode:q},T,{type:t,value:j,onChange:function(e){var t=function(e,t,r){var n=function(e,t){for(var r=0,n=0,a=e.length,o=t.length;e[r]===t[r]&&r<a;)r++;for(;e[a-1-n]===t[o-1-n]&&o-n>r&&a-n>r;)n++;return{from:{start:r,end:a-n},to:{start:r,end:o-n}}}(j,e),a=Object.assign(Object.assign({},n),{lastValue:j}),o=v(e,a),u=U(o);return!(p&&!p(_(u,o)))&&(z({formattedValue:u,numAsString:o,inputValue:e,event:t,source:r,setCaretPosition:!0,input:t.target}),!0)}(e.target.value,e,u.event);t&&S(e)},onKeyDown:function(e){var t,r=e.target,n=e.key,a=r.selectionStart,o=r.selectionEnd,u=r.value;if(void 0===u&&(u=""),"ArrowLeft"===n||"Backspace"===n?t=Math.max(a-1,0):"ArrowRight"===n?t=Math.min(a+1,u.length):"Delete"===n&&(t=a),void 0!==t&&a===o){var i=t;if("ArrowLeft"===n||"ArrowRight"===n)i=$(u,t,"ArrowLeft"===n?"left":"right");else"Delete"!==n||l(u[t])?"Backspace"!==n||l(u[t])||(i=$(u,t,"left")):i=$(u,t,"right");i!==t&&G(r,i,u),e.isUnitTestRun&&G(r,i,u),O(e)}else O(e)},onMouseUp:function(e){var t=e.target,r=t.selectionStart,n=t.selectionEnd,a=t.value;if(void 0===a&&(a=""),r===n){var o=$(a,r);o!==r&&G(t,o,a)}V(e)},onFocus:function(e){e.persist();var t=e.target;L.current=t,W.current.focusTimeout=setTimeout((function(){var r=t.selectionStart,n=t.selectionEnd,a=t.value;void 0===a&&(a="");var o=$(a,r);o===r||0===r&&n===a.length||G(t,o,a),N(e)}),0)},onBlur:function(e){L.current=null,clearTimeout(W.current.focusTimeout),clearTimeout(W.current.setCaretTimeout),E(e)}});if("text"===r)return s?n.createElement(n.Fragment,null,s(j,T)||null):n.createElement("span",Object.assign({},T,{ref:c}),j);if(a){var J=a;return n.createElement(J,Object.assign({},H,{ref:c}))}return n.createElement("input",Object.assign({},H,{ref:c}))}function V(e,t){var r=t.decimalScale,n=t.fixedDecimalScale,a=t.prefix;void 0===a&&(a="");var o=t.suffix;void 0===o&&(o="");var u=t.allowNegative;void 0===u&&(u=!0);var i=t.thousandsGroupStyle;if(void 0===i&&(i="thousand"),""===e||"-"===e)return e;var l=N(t),s=l.thousandSeparator,c=l.decimalSeparator,f=0!==r&&-1!==e.indexOf(".")||r&&n,g=v(e,u),m=g.beforeDecimal,p=g.afterDecimal,h=g.addNegation;return void 0!==r&&(p=d(p,r,!!n)),s&&(m=function(e,t,r){var n=function(e){switch(e){case"lakh":return/(\d+?)(?=(\d\d)+(\d)(?!\d))(\.\d+)?/g;case"wan":return/(\d)(?=(\d{4})+(?!\d))/g;default:return/(\d)(?=(\d{3})+(?!\d))/g}}(r),a=e.search(/[1-9]/);return a=-1===a?e.length:a,e.substring(0,a)+e.substring(a,e.length).replace(n,"$1"+t)}(m,s,i)),a&&(m=a+m),o&&(p+=o),h&&(m="-"+m),e=m+(f&&c||"")+p}function N(e){var t=e.decimalSeparator;void 0===t&&(t=".");var r=e.thousandSeparator,n=e.allowedDecimalSeparators;return!0===r&&(r=","),n||(n=[t,"."]),{decimalSeparator:t,thousandSeparator:r,allowedDecimalSeparators:n}}function E(e,t,r){void 0===t&&(t=S(e));var n=r.allowNegative;void 0===n&&(n=!0);var a=r.prefix;void 0===a&&(a="");var o=r.suffix;void 0===o&&(o="");var u=r.decimalScale,i=t.from,l=t.to,s=l.start,c=l.end,d=N(r),g=d.allowedDecimalSeparators,m=d.decimalSeparator,p=e[c]===m;if(c-s===1&&-1!==g.indexOf(e[s])){var h=0===u?"":m;e=e.substring(0,s)+h+e.substring(s+1,e.length)}var b=!1;a.startsWith("-")?b=e.startsWith("--"):o.startsWith("-")&&e.length===o.length?b=!1:"-"===e[0]&&(b=!0),b&&(e=e.substring(1),s-=1,c-=1);var y=0;e.startsWith(a)?y+=a.length:s<a.length&&(y=s),c-=y;var w=(e=e.substring(y)).length,x=e.length-o.length;e.endsWith(o)?w=x:c>e.length-o.length&&(w=c),e=e.substring(0,w),e=function(e,t){void 0===e&&(e="");var r=new RegExp("(-)"),n=new RegExp("(-)(.)*(-)"),a=r.test(e),o=n.test(e);return e=e.replace(/-/g,""),a&&!o&&t&&(e="-"+e),e}(b?"-"+e:e,n),e=(e.match(function(e,t){return new RegExp("(^-)|[0-9]|"+f(e),t?"g":void 0)}(m,!0))||[]).join("");var D=e.indexOf(m),O=v(e=e.replace(new RegExp(f(m),"g"),(function(e,t){return t===D?".":""})),n),V=O.beforeDecimal,E=O.afterDecimal,C=O.addNegation;return l.end-l.start<i.end-i.start&&""===V&&p&&!parseFloat(E)&&(e=C?"-":""),e}function C(e){e.decimalSeparator,e.allowedDecimalSeparators,e.thousandsGroupStyle,e.suffix,e.allowNegative;var t=e.allowLeadingZeros,r=e.onKeyDown;void 0===r&&(r=i);var n=e.onBlur;void 0===n&&(n=i);var a=e.thousandSeparator,l=e.decimalScale,f=e.fixedDecimalScale,v=e.prefix;void 0===v&&(v="");var d=e.defaultValue,g=e.value,S=e.valueIsNumericString,b=e.onValueChange,y=o(e,["decimalSeparator","allowedDecimalSeparators","thousandsGroupStyle","suffix","allowNegative","allowLeadingZeros","onKeyDown","onBlur","thousandSeparator","decimalScale","fixedDecimalScale","prefix","defaultValue","value","valueIsNumericString","onValueChange"]);!function(e){var t=N(e),r=t.thousandSeparator,n=t.decimalSeparator;if(r===n)throw new Error("\n        Decimal separator can't be same as thousand separator.\n        thousandSeparator: "+r+' (thousandSeparator = {true} is same as thousandSeparator = ",")\n        decimalSeparator: '+n+" (default value for decimalSeparator is .)\n     ")}(e);var x=function(t){return V(t,e)},D=function(t,r){return E(t,r,e)},O=S;s(g)?s(d)||(O=null!==S&&void 0!==S?S:"number"===typeof d):O=null!==S&&void 0!==S?S:"number"===typeof g;var C=function(e){return s(e)||c(e)?e:("number"===typeof e&&(e=m(e)),O&&"number"===typeof l?p(e,l,Boolean(f)):e)},R=w(C(g),C(d),Boolean(O),x,D,b),T=R[0],A=T.numAsString,B=T.formattedValue,j=R[1];return Object.assign(Object.assign({},y),{value:B,valueIsNumericString:!1,onValueChange:j,format:x,removeFormatting:D,getCaretBoundary:function(t){return function(e,t){var r=t.prefix;void 0===r&&(r="");var n=t.suffix;void 0===n&&(n="");var a=Array.from({length:e.length+1}).map((function(){return!0})),o="-"===e[0];a.fill(!1,0,r.length+(o?1:0));var u=e.length;return a.fill(!1,u-n.length+1,u+1),a}(t,e)},onKeyDown:function(t){var n=t.target,o=t.key,u=n.selectionStart,i=n.selectionEnd,s=n.value;if(void 0===s&&(s=""),u===i){"Backspace"===o&&"-"===s[0]&&u===v.length+1&&h(n,1);var c=N(e).decimalSeparator;"Backspace"===o&&s[u-1]===c&&l&&f&&(h(n,u-1),t.preventDefault());var d=!0===a?",":a;"Backspace"===o&&s[u-1]===d&&h(n,u-1),"Delete"===o&&s[u]===d&&h(n,u+1),r(t)}else r(t)},onBlur:function(r){var a=A;if(a.match(/\d/g)||(a=""),t||(a=function(e){if(!e)return e;var t="-"===e[0];t&&(e=e.substring(1,e.length));var r=e.split("."),n=r[0].replace(/^0+/,"")||"0",a=r[1]||"";return(t?"-":"")+n+(a?"."+a:"")}(a)),f&&l&&(a=p(a,l,f)),a!==A){var o=V(a,e);j({formattedValue:o,value:a,floatValue:parseFloat(a)},{event:r,source:u.event})}n(r)}})}function R(e){var t=C(e);return n.createElement(O,Object.assign({},t))}function T(){var e,t,r;const n=null===(e=Intl.NumberFormat())||void 0===e||null===(t=e.formatToParts(1.1))||void 0===t||null===(r=t.find((e=>"decimal"===e.type)))||void 0===r?void 0:r.value;return null!==n&&void 0!==n?n:"."}!function(e){e.event="event",e.props="prop"}(u||(u={}));const A=e=>{const{value:t,onChange:r,disabled:o,highlight:u,validatedSelection:i,fixedDecimals:l,allowNegative:s,thousandSeparator:c,decimalSeparator:f}=e,v=n.useRef();return n.useLayoutEffect((()=>{if(void 0!==i){var e;const t="number"===typeof i?[i,null]:i;null===(e=v.current)||void 0===e||e.setSelectionRange(t[0],t[1])}}),[i]),n.createElement(a,null,n.createElement(R,{autoFocus:!0,getInputRef:v,className:"gdg-input",onFocus:e=>e.target.setSelectionRange(u?0:e.target.value.length,e.target.value.length),disabled:!0===o,decimalScale:l,allowNegative:s,thousandSeparator:null!==c&&void 0!==c?c:"."===T()?",":".",decimalSeparator:null!==f&&void 0!==f?f:T(),value:Object.is(t,-0)?"-":null!==t&&void 0!==t?t:"",onValueChange:r}))}}}]);