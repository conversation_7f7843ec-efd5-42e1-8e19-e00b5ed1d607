#!/usr/bin/env python3
"""
A股数据源简化版
不依赖外部包，使用模拟数据演示A股功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random


class AStockDataProvider:
    """A股数据提供商 - 简化版"""
    
    def __init__(self, tushare_token=None):
        """初始化A股数据提供商"""
        self.providers = ["模拟数据源"]
        print("🚀 A股数据源初始化完成 (简化版)")
        print("📊 使用模拟数据演示A股功能")
        
        # A股股票基本信息
        self.stock_info = {
            '000001': {'name': '平安银行', 'sector': '银行', 'market': '深交所'},
            '000002': {'name': '万科A', 'sector': '房地产', 'market': '深交所'},
            '600000': {'name': '浦发银行', 'sector': '银行', 'market': '上交所'},
            '600036': {'name': '招商银行', 'sector': '银行', 'market': '上交所'},
            '600519': {'name': '贵州茅台', 'sector': '食品饮料', 'market': '上交所'},
            '000858': {'name': '五粮液', 'sector': '食品饮料', 'market': '深交所'},
            '002415': {'name': '海康威视', 'sector': '电子', 'market': '深交所'},
            '300059': {'name': '东方财富', 'sector': '非银金融', 'market': '创业板'},
        }
    
    def get_stock_basic(self):
        """获取股票基本信息"""
        print("📋 获取A股基本信息...")
        
        data = []
        for code, info in self.stock_info.items():
            data.append({
                'ts_code': f"{code}.{'SZ' if code.startswith(('0', '3')) else 'SH'}",
                'symbol': code,
                'name': info['name'],
                'area': '中国',
                'industry': info['sector'],
                'market': info['market'],
                'list_date': '20100101'
            })
        
        df = pd.DataFrame(data)
        print(f"✅ 获取到 {len(df)} 只股票信息")
        return df
    
    def get_stock_data(self, symbol, start_date=None, end_date=None, period="daily"):
        """获取股票历史数据"""
        print(f"📈 获取 {symbol} 历史数据...")
        
        # 清理股票代码
        clean_symbol = symbol.replace('.SH', '').replace('.SZ', '')
        
        if clean_symbol not in self.stock_info:
            print(f"❌ 未找到股票 {symbol}")
            return None
        
        # 生成模拟历史数据
        if not end_date:
            end_date = datetime.now()
        else:
            end_date = pd.to_datetime(end_date)
        
        if not start_date:
            start_date = end_date - timedelta(days=365)
        else:
            start_date = pd.to_datetime(start_date)
        
        # 生成日期序列（只包含工作日）
        dates = pd.bdate_range(start=start_date, end=end_date)
        
        # 设置随机种子以保证数据一致性
        np.random.seed(hash(clean_symbol) % 1000)
        
        # 基础价格（根据股票不同设置不同基础价格）
        base_prices = {
            '000001': 12.5,   # 平安银行
            '000002': 18.2,   # 万科A
            '600000': 10.8,   # 浦发银行
            '600036': 35.6,   # 招商银行
            '600519': 1680.0, # 贵州茅台
            '000858': 158.0,  # 五粮液
            '002415': 32.5,   # 海康威视
            '300059': 15.8,   # 东方财富
        }
        
        base_price = base_prices.get(clean_symbol, 20.0)
        
        # 生成价格序列
        returns = np.random.normal(0.001, 0.025, len(dates))  # 日收益率
        prices = [base_price]
        
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(max(new_price, 0.1))  # 确保价格不为负
        
        # 生成OHLC数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            # 生成开高低价
            volatility = 0.02
            high = close * (1 + random.uniform(0, volatility))
            low = close * (1 - random.uniform(0, volatility))
            
            if i == 0:
                open_price = close
            else:
                open_price = prices[i-1] * (1 + random.uniform(-0.01, 0.01))
            
            # 确保价格关系合理
            high = max(high, open_price, close)
            low = min(low, open_price, close)
            
            # 生成成交量
            volume = random.randint(1000000, 50000000)
            
            data.append({
                'date': date,
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close, 2),
                'volume': volume,
                'amount': round(volume * close, 2)
            })
        
        df = pd.DataFrame(data)
        df.set_index('date', inplace=True)
        
        print(f"✅ 生成 {len(df)} 条历史数据")
        return df
    
    def get_realtime_data(self, symbols):
        """获取实时行情数据"""
        print(f"💰 获取实时行情: {symbols}")
        
        if isinstance(symbols, str):
            symbols = [symbols]
        
        data = []
        for symbol in symbols:
            clean_symbol = symbol.replace('.SH', '').replace('.SZ', '')
            
            if clean_symbol not in self.stock_info:
                continue
            
            info = self.stock_info[clean_symbol]
            
            # 获取最新历史数据作为当前价格
            hist_data = self.get_stock_data(clean_symbol, 
                start_date=(datetime.now() - timedelta(days=5)).strftime("%Y-%m-%d"))
            
            if hist_data is not None and not hist_data.empty:
                latest = hist_data.iloc[-1]
                prev = hist_data.iloc[-2] if len(hist_data) > 1 else latest
                
                current_price = latest['close']
                prev_close = prev['close']
                change = current_price - prev_close
                change_pct = (change / prev_close) * 100
                
                data.append({
                    'symbol': symbol,
                    'name': info['name'],
                    'price': current_price,
                    'change': change,
                    'change_pct': change_pct,
                    'volume': latest['volume'],
                    'turnover': latest['amount'],
                    'high': latest['high'],
                    'low': latest['low'],
                    'open': latest['open']
                })
        
        if data:
            df = pd.DataFrame(data)
            print(f"✅ 获取到 {len(df)} 只股票实时数据")
            return df
        
        print("❌ 获取实时数据失败")
        return None
    
    def get_financial_data(self, symbol, report_type="income"):
        """获取财务数据"""
        print(f"💼 获取 {symbol} 财务数据 ({report_type})")
        
        clean_symbol = symbol.replace('.SH', '').replace('.SZ', '')
        
        if clean_symbol not in self.stock_info:
            print(f"❌ 未找到股票 {symbol}")
            return None
        
        # 生成模拟财务数据
        years = ['2021', '2022', '2023', '2024']
        
        if report_type == "income":
            # 收入表数据
            base_revenue = hash(clean_symbol) % 100000000000  # 基础营收
            data = []
            
            for i, year in enumerate(years):
                growth = 1 + (i * 0.1) + random.uniform(-0.05, 0.15)
                revenue = base_revenue * growth
                
                data.append({
                    'end_date': f"{year}1231",
                    'revenue': round(revenue, 2),
                    'operating_profit': round(revenue * 0.15, 2),
                    'net_profit': round(revenue * 0.08, 2),
                    'eps': round(revenue * 0.08 / 1000000000, 2)
                })
        
        elif report_type == "balance":
            # 资产负债表数据
            base_assets = hash(clean_symbol) % 500000000000
            data = []
            
            for i, year in enumerate(years):
                growth = 1 + (i * 0.08) + random.uniform(-0.03, 0.12)
                assets = base_assets * growth
                
                data.append({
                    'end_date': f"{year}1231",
                    'total_assets': round(assets, 2),
                    'total_liab': round(assets * 0.6, 2),
                    'total_equity': round(assets * 0.4, 2),
                    'total_current_assets': round(assets * 0.3, 2)
                })
        
        elif report_type == "cashflow":
            # 现金流量表数据
            base_cashflow = hash(clean_symbol) % 50000000000
            data = []
            
            for i, year in enumerate(years):
                growth = 1 + (i * 0.12) + random.uniform(-0.08, 0.20)
                operating_cf = base_cashflow * growth
                
                data.append({
                    'end_date': f"{year}1231",
                    'net_cash_flows_oper_act': round(operating_cf, 2),
                    'net_cash_flows_inv_act': round(operating_cf * -0.5, 2),
                    'net_cash_flows_fnc_act': round(operating_cf * -0.3, 2),
                    'net_incr_cash_cash_equ': round(operating_cf * 0.2, 2)
                })
        
        df = pd.DataFrame(data)
        print(f"✅ 生成财务数据 ({len(df)} 年)")
        return df
    
    def get_market_overview(self):
        """获取市场概览"""
        print("📊 获取A股市场概览...")
        
        # 模拟市场指数数据
        indices = {
            '上证指数': {'code': '000001.SH', 'price': 3150.25, 'change': 15.8, 'change_pct': 0.50},
            '深证成指': {'code': '399001.SZ', 'price': 10250.60, 'change': -25.4, 'change_pct': -0.25},
            '创业板指': {'code': '399006.SZ', 'price': 2180.45, 'change': 8.2, 'change_pct': 0.38},
            '科创50': {'code': '000688.SH', 'price': 980.15, 'change': -5.6, 'change_pct': -0.57}
        }
        
        data = []
        for name, info in indices.items():
            data.append({
                'name': name,
                'code': info['code'],
                'price': info['price'],
                'change': info['change'],
                'change_pct': info['change_pct']
            })
        
        df = pd.DataFrame(data)
        print(f"✅ 获取到 {len(df)} 个指数数据")
        return df


def test_a_stock_simple():
    """测试简化版A股数据源"""
    print("🧪 测试A股数据源 (简化版)")
    print("=" * 50)
    
    provider = AStockDataProvider()
    
    # 测试股票列表
    print("\n📋 测试股票基本信息")
    basic_info = provider.get_stock_basic()
    print(basic_info)
    
    # 测试历史数据
    print("\n📈 测试历史数据")
    data = provider.get_stock_data('000001', start_date='2024-01-01', end_date='2024-12-31')
    if data is not None:
        print(f"000001: 获取到 {len(data)} 条历史数据")
        print(data.tail())
    
    # 测试实时数据
    print("\n💰 测试实时数据")
    realtime = provider.get_realtime_data(['000001', '600519'])
    if realtime is not None:
        print("实时行情:")
        print(realtime)
    
    # 测试财务数据
    print("\n💼 测试财务数据")
    financial = provider.get_financial_data('000001', 'income')
    if financial is not None:
        print("财务数据:")
        print(financial)
    
    # 测试市场概览
    print("\n📊 测试市场概览")
    market = provider.get_market_overview()
    if market is not None:
        print("市场指数:")
        print(market)
    
    print("\n🎉 A股数据源测试完成!")


if __name__ == "__main__":
    test_a_stock_simple()
