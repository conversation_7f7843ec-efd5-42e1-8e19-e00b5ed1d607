"""OpenBB Innovative Indicators Router."""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from openbb_core.app.model.command_context import CommandContext
from openbb_core.app.model.obbject import OBBject
from openbb_core.app.provider_interface import ProviderInterface
from openbb_core.app.query import Query as OpenBBQuery

from openbb_innovative_indicators.models import (
    InnovativeIndicatorsData,
    InnovativeIndicatorsQueryParams,
    TradingSignalsData,
    TradingSignalsQueryParams,
)

router = APIRouter(prefix="/technical", tags=["Technical Analysis - Innovative"])


@router.get(
    "/innovative_indicators",
    summary="Get Innovative Technical Indicators",
    description="""
    Calculate innovative technical indicators including:
    - DMFI: Dynamic Money Flow Intensity
    - AISI: Adaptive Intelligent Sentiment Index  
    - MVCI: Multi-Dimensional Value Convergence Index
    - TAFI: Time-Adaptive Trend Following Index
    - FMSI: Fundamental-Technical Momentum Synthesis Index
    
    These indicators use advanced algorithms combining multiple market dimensions
    and machine learning techniques for enhanced market analysis.
    """,
    response_model=List[InnovativeIndicatorsData],
)
async def get_innovative_indicators(
    cc: CommandContext = Depends(),
    symbol: str = Query(..., description="Stock symbol to analyze"),
    start_date: str = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: str = Query(None, description="End date (YYYY-MM-DD)"),
    indicator: str = Query(
        "all", 
        description="Specific indicator to calculate",
        regex="^(dmfi|aisi|mvci|tafi|fmsi|all)$"
    ),
    period: int = Query(20, description="Calculation period", ge=5, le=252),
    provider: str = Query("innovative", description="Data provider"),
) -> OBBject[List[InnovativeIndicatorsData]]:
    """Get innovative technical indicators."""
    
    # First, get historical price data from a standard provider
    try:
        # This would typically use the equity.price.historical endpoint
        price_query = OpenBBQuery(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            provider="polygon",  # or another configured provider
        )
        
        # Get price data (this is a simplified example)
        # In practice, this would use the ProviderInterface to get data
        price_data = await ProviderInterface().equity.price.historical(price_query, cc)
        
        if not price_data.results:
            raise HTTPException(status_code=404, detail="No price data found for symbol")
        
        # Convert to DataFrame for indicator calculation
        import pandas as pd
        price_df = pd.DataFrame([item.model_dump() for item in price_data.results])
        price_df.set_index('date', inplace=True)
        
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to fetch price data: {str(e)}"
        )
    
    # Create query for innovative indicators
    query_params = InnovativeIndicatorsQueryParams(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        indicator=indicator,
        period=period,
    )
    
    try:
        # Calculate innovative indicators
        from openbb_innovative_indicators.provider import InnovativeIndicatorsFetcher
        
        fetcher = InnovativeIndicatorsFetcher()
        
        # Extract data
        raw_data = fetcher.extract_data(
            query_params, 
            credentials=None,
            price_data=price_df
        )
        
        # Transform data
        results = fetcher.transform_data(query_params, raw_data)
        
        return OBBject(
            results=results,
            provider="innovative",
            warnings=None,
            chart=None,
            extra={
                "symbol": symbol,
                "indicator": indicator,
                "period": period,
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to calculate innovative indicators: {str(e)}"
        )


@router.get(
    "/trading_signals",
    summary="Get Trading Signals from Innovative Indicators",
    description="""
    Generate trading signals based on innovative technical indicators.
    
    Combines multiple innovative indicators to produce:
    - BUY/SELL/HOLD signals
    - Signal strength (-1.0 to 1.0)
    - Confidence level (0.0 to 1.0)
    - Indicator consensus count
    
    Uses advanced signal processing and machine learning techniques
    to reduce false signals and improve accuracy.
    """,
    response_model=List[TradingSignalsData],
)
async def get_trading_signals(
    cc: CommandContext = Depends(),
    symbol: str = Query(..., description="Stock symbol to analyze"),
    start_date: str = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: str = Query(None, description="End date (YYYY-MM-DD)"),
    threshold: float = Query(
        0.5, 
        description="Signal threshold for BUY/SELL decisions",
        ge=0.1,
        le=1.0
    ),
    provider: str = Query("innovative", description="Data provider"),
) -> OBBject[List[TradingSignalsData]]:
    """Get trading signals from innovative indicators."""
    
    # Get historical price data
    try:
        price_query = OpenBBQuery(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            provider="polygon",
        )
        
        price_data = await ProviderInterface().equity.price.historical(price_query, cc)
        
        if not price_data.results:
            raise HTTPException(status_code=404, detail="No price data found for symbol")
        
        import pandas as pd
        price_df = pd.DataFrame([item.model_dump() for item in price_data.results])
        price_df.set_index('date', inplace=True)
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch price data: {str(e)}"
        )
    
    # Create query for trading signals
    query_params = TradingSignalsQueryParams(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        threshold=threshold,
    )
    
    try:
        from openbb_innovative_indicators.provider import TradingSignalsFetcher
        
        fetcher = TradingSignalsFetcher()
        
        # Extract data
        raw_data = fetcher.extract_data(
            query_params,
            credentials=None,
            price_data=price_df
        )
        
        # Transform data
        results = fetcher.transform_data(query_params, raw_data)
        
        return OBBject(
            results=results,
            provider="innovative",
            warnings=None,
            chart=None,
            extra={
                "symbol": symbol,
                "threshold": threshold,
                "total_signals": len(results),
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate trading signals: {str(e)}"
        )


@router.get(
    "/indicator_performance",
    summary="Get Indicator Performance Metrics",
    description="""
    Calculate performance metrics for innovative indicators including:
    - Total return from following signals
    - Win rate percentage
    - Average return per trade
    - Sharpe ratio
    - Maximum drawdown
    - Total number of trades
    
    Useful for backtesting and evaluating indicator effectiveness.
    """,
)
async def get_indicator_performance(
    cc: CommandContext = Depends(),
    symbol: str = Query(..., description="Stock symbol to analyze"),
    start_date: str = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: str = Query(None, description="End date (YYYY-MM-DD)"),
    holding_period: int = Query(5, description="Days to hold position", ge=1, le=30),
    threshold: float = Query(0.5, description="Signal threshold", ge=0.1, le=1.0),
) -> OBBject[dict]:
    """Get performance metrics for innovative indicators."""
    
    try:
        # Get price data and signals
        price_query = OpenBBQuery(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            provider="polygon",
        )
        
        price_data = await ProviderInterface().equity.price.historical(price_query, cc)
        
        if not price_data.results:
            raise HTTPException(status_code=404, detail="No price data found")
        
        import pandas as pd
        price_df = pd.DataFrame([item.model_dump() for item in price_data.results])
        price_df.set_index('date', inplace=True)
        
        # Get trading signals
        signals_query = TradingSignalsQueryParams(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            threshold=threshold,
        )
        
        from openbb_innovative_indicators.provider import TradingSignalsFetcher
        from openbb_innovative_indicators.utils.helpers import calculate_signal_performance
        
        fetcher = TradingSignalsFetcher()
        signals_data = fetcher.extract_data(signals_query, None, price_data=price_df)
        
        # Convert to series for performance calculation
        signals_df = pd.DataFrame(signals_data)
        signals_df.set_index('date', inplace=True)
        
        # Calculate performance
        performance = calculate_signal_performance(
            signals_df['signal'],
            price_df['close'],
            holding_period=holding_period
        )
        
        return OBBject(
            results=performance,
            provider="innovative",
            warnings=None,
            chart=None,
            extra={
                "symbol": symbol,
                "holding_period": holding_period,
                "threshold": threshold,
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to calculate performance: {str(e)}"
        )
