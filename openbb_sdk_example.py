#!/usr/bin/env python3
"""
OpenBB SDK 正式使用示例
展示如何在生产环境中使用OpenBB进行金融数据分析
"""

import time
import pandas as pd
from datetime import datetime, timedelta
from openbb_core.app.static.app_factory import create_app


class OpenBBAnalyzer:
    """OpenBB 金融数据分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.app = create_app()
        print("✅ OpenBB 分析器初始化成功")
    
    def get_stock_data(self, symbol, period="1mo", delay=1):
        """
        获取股票数据
        
        Args:
            symbol: 股票代码
            period: 时间周期
            delay: 请求延迟（避免API限制）
        """
        try:
            import yfinance as yf
            
            print(f"📈 获取 {symbol} 数据...")
            time.sleep(delay)
            
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period)
            
            if not data.empty:
                print(f"✅ 成功获取 {len(data)} 条数据")
                return data
            else:
                print("⚠️ 数据为空")
                return None
                
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return None
    
    def analyze_portfolio(self, symbols, weights=None):
        """
        分析投资组合
        
        Args:
            symbols: 股票代码列表
            weights: 权重列表（可选）
        """
        if weights is None:
            weights = [1/len(symbols)] * len(symbols)
        
        portfolio_data = {}
        
        print(f"📊 分析投资组合: {symbols}")
        
        for symbol in symbols:
            data = self.get_stock_data(symbol, period="3mo")
            if data is not None:
                portfolio_data[symbol] = data
        
        if not portfolio_data:
            print("❌ 无法获取投资组合数据")
            return None
        
        # 计算投资组合收益率
        returns = {}
        for symbol, data in portfolio_data.items():
            returns[symbol] = data['Close'].pct_change().dropna()
        
        returns_df = pd.DataFrame(returns)
        
        # 投资组合统计
        portfolio_returns = (returns_df * weights).sum(axis=1)
        
        stats = {
            'annual_return': portfolio_returns.mean() * 252,
            'annual_volatility': portfolio_returns.std() * (252**0.5),
            'sharpe_ratio': (portfolio_returns.mean() * 252) / (portfolio_returns.std() * (252**0.5)),
            'max_drawdown': self._calculate_max_drawdown(portfolio_returns)
        }
        
        print(f"📈 投资组合分析结果:")
        print(f"   年化收益率: {stats['annual_return']*100:.2f}%")
        print(f"   年化波动率: {stats['annual_volatility']*100:.2f}%")
        print(f"   夏普比率: {stats['sharpe_ratio']:.2f}")
        print(f"   最大回撤: {stats['max_drawdown']*100:.2f}%")
        
        return stats, returns_df
    
    def _calculate_max_drawdown(self, returns):
        """计算最大回撤"""
        cumulative = (1 + returns).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdown = (cumulative - rolling_max) / rolling_max
        return drawdown.min()
    
    def technical_analysis(self, symbol, period="6mo"):
        """技术分析"""
        data = self.get_stock_data(symbol, period=period)
        
        if data is None:
            return None
        
        # 计算技术指标
        data['MA_20'] = data['Close'].rolling(window=20).mean()
        data['MA_50'] = data['Close'].rolling(window=50).mean()
        data['RSI'] = self._calculate_rsi(data['Close'])
        data['MACD'], data['MACD_Signal'] = self._calculate_macd(data['Close'])
        
        # 生成交易信号
        latest = data.iloc[-1]
        signals = []
        
        if latest['Close'] > latest['MA_20']:
            signals.append("价格在20日均线上方")
        if latest['MA_20'] > latest['MA_50']:
            signals.append("短期均线在长期均线上方")
        if latest['RSI'] < 30:
            signals.append("RSI超卖信号")
        elif latest['RSI'] > 70:
            signals.append("RSI超买信号")
        
        print(f"🔍 {symbol} 技术分析:")
        print(f"   当前价格: ${latest['Close']:.2f}")
        print(f"   20日均线: ${latest['MA_20']:.2f}")
        print(f"   50日均线: ${latest['MA_50']:.2f}")
        print(f"   RSI: {latest['RSI']:.1f}")
        print(f"   交易信号: {', '.join(signals) if signals else '无明显信号'}")
        
        return data
    
    def _calculate_rsi(self, prices, window=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, prices, fast=12, slow=26, signal=9):
        """计算MACD指标"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        return macd, macd_signal
    
    def save_analysis_report(self, data, filename):
        """保存分析报告"""
        try:
            # 保存为Excel文件
            with pd.ExcelWriter(f"{filename}.xlsx") as writer:
                if isinstance(data, dict):
                    for sheet_name, df in data.items():
                        df.to_excel(writer, sheet_name=sheet_name)
                else:
                    data.to_excel(writer, sheet_name="Analysis")
            
            print(f"✅ 分析报告已保存: {filename}.xlsx")
            
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")


def main():
    """主函数 - 演示正式系统使用"""
    print("🚀 OpenBB 正式系统启动")
    print("=" * 50)
    
    # 初始化分析器
    analyzer = OpenBBAnalyzer()
    
    # 示例1: 单股票技术分析
    print("\n📈 示例1: 技术分析")
    print("-" * 30)
    tech_data = analyzer.technical_analysis("AAPL", period="3mo")
    
    # 示例2: 投资组合分析
    print("\n📊 示例2: 投资组合分析")
    print("-" * 30)
    portfolio_symbols = ["AAPL", "MSFT", "GOOGL"]
    portfolio_weights = [0.4, 0.3, 0.3]
    
    try:
        stats, returns = analyzer.analyze_portfolio(portfolio_symbols, portfolio_weights)
        
        if stats and returns is not None:
            # 保存分析结果
            analysis_data = {
                "Portfolio_Returns": returns,
                "Technical_Analysis": tech_data if tech_data is not None else pd.DataFrame()
            }
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            analyzer.save_analysis_report(analysis_data, f"analysis_report_{timestamp}")
            
    except Exception as e:
        print(f"⚠️ 投资组合分析遇到API限制: {e}")
        print("建议稍后重试或使用其他数据源")
    
    print("\n" + "=" * 50)
    print("✅ 分析完成！")
    print("\n💡 生产环境建议:")
    print("1. 配置API密钥获得更好的数据服务")
    print("2. 实现数据缓存减少API调用")
    print("3. 添加错误处理和重试机制")
    print("4. 使用数据库存储历史数据")


if __name__ == "__main__":
    main()
