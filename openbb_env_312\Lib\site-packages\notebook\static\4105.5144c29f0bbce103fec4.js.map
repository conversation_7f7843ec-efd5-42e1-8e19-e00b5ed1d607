{"version": 3, "file": "4105.5144c29f0bbce103fec4.js?v=5144c29f0bbce103fec4", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,2BAA2B,GAAG,qCAAqC,GAAG,iCAAiC,GAAG,yBAAyB;AACnI,oBAAoB,mBAAO,CAAC,KAAqB;AACjD,gCAAgC,mBAAO,CAAC,KAAyC;AACjF,mBAAmB,mBAAO,CAAC,IAAoB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,uBAAuB;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2EAA2E,UAAU;AACrF;AACA;AACA;AACA;AACA,oCAAoC,QAAQ;AAC5C;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,yCAAyC,2BAA2B,0FAA0F,yCAAyC,kDAAkD,IAAI;AAC7P;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;;;;;;;AClMa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB;AAClB,sBAAsB,mBAAO,CAAC,KAAiB;AAC/C,mBAAmB,mBAAO,CAAC,KAAoB;AAC/C;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;;;;;;;AC5Ca;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,4BAA4B,GAAG,gBAAgB,GAAG,eAAe;AACjE,sBAAsB,mBAAO,CAAC,KAAiB;AAC/C,mBAAmB,mBAAO,CAAC,KAAc;AACzC,cAAc,mBAAO,CAAC,KAAkB;AACxC,eAAe;AACf,8BAA8B,+DAA+D;AAC7F,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;AACA,6EAA6E,UAAU;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC,gCAAgC,sBAAsB;AACtD,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,4BAA4B;AAC5B;;;;;;;ACpMa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB,gBAAgB,mBAAO,CAAC,KAAW;AACnC;AACA;AACA;AACA;AACA;AACA,wEAAwE,UAAU;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA,yEAAyE,UAAU;AACnF;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,uBAAuB;AACvB", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/a11y/assistive-mml.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/MmlVisitor.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MmlTree/SerializedMmlVisitor.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/Tree/Visitor.js"], "sourcesContent": ["\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AssistiveMmlHandler = exports.AssistiveMmlMathDocumentMixin = exports.AssistiveMmlMathItemMixin = exports.LimitedMmlVisitor = void 0;\nvar MathItem_js_1 = require(\"../core/MathItem.js\");\nvar SerializedMmlVisitor_js_1 = require(\"../core/MmlTree/SerializedMmlVisitor.js\");\nvar Options_js_1 = require(\"../util/Options.js\");\nvar LimitedMmlVisitor = (function (_super) {\n    __extends(LimitedMmlVisitor, _super);\n    function LimitedMmlVisitor() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    LimitedMmlVisitor.prototype.getAttributes = function (node) {\n        return _super.prototype.getAttributes.call(this, node).replace(/ ?id=\".*?\"/, '');\n    };\n    return LimitedMmlVisitor;\n}(SerializedMmlVisitor_js_1.SerializedMmlVisitor));\nexports.LimitedMmlVisitor = LimitedMmlVisitor;\n(0, MathItem_js_1.newState)('ASSISTIVEMML', 153);\nfunction AssistiveMmlMathItemMixin(BaseMathItem) {\n    return (function (_super) {\n        __extends(class_1, _super);\n        function class_1() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        class_1.prototype.assistiveMml = function (document, force) {\n            if (force === void 0) { force = false; }\n            if (this.state() >= MathItem_js_1.STATE.ASSISTIVEMML)\n                return;\n            if (!this.isEscaped && (document.options.enableAssistiveMml || force)) {\n                var adaptor = document.adaptor;\n                var mml = document.toMML(this.root).replace(/\\n */g, '').replace(/<!--.*?-->/g, '');\n                var mmlNodes = adaptor.firstChild(adaptor.body(adaptor.parse(mml, 'text/html')));\n                var node = adaptor.node('mjx-assistive-mml', {\n                    unselectable: 'on', display: (this.display ? 'block' : 'inline')\n                }, [mmlNodes]);\n                adaptor.setAttribute(adaptor.firstChild(this.typesetRoot), 'aria-hidden', 'true');\n                adaptor.setStyle(this.typesetRoot, 'position', 'relative');\n                adaptor.append(this.typesetRoot, node);\n            }\n            this.state(MathItem_js_1.STATE.ASSISTIVEMML);\n        };\n        return class_1;\n    }(BaseMathItem));\n}\nexports.AssistiveMmlMathItemMixin = AssistiveMmlMathItemMixin;\nfunction AssistiveMmlMathDocumentMixin(BaseDocument) {\n    var _a;\n    return _a = (function (_super) {\n            __extends(BaseClass, _super);\n            function BaseClass() {\n                var args = [];\n                for (var _i = 0; _i < arguments.length; _i++) {\n                    args[_i] = arguments[_i];\n                }\n                var _this = _super.apply(this, __spreadArray([], __read(args), false)) || this;\n                var CLASS = _this.constructor;\n                var ProcessBits = CLASS.ProcessBits;\n                if (!ProcessBits.has('assistive-mml')) {\n                    ProcessBits.allocate('assistive-mml');\n                }\n                _this.visitor = new LimitedMmlVisitor(_this.mmlFactory);\n                _this.options.MathItem =\n                    AssistiveMmlMathItemMixin(_this.options.MathItem);\n                if ('addStyles' in _this) {\n                    _this.addStyles(CLASS.assistiveStyles);\n                }\n                return _this;\n            }\n            BaseClass.prototype.toMML = function (node) {\n                return this.visitor.visitTree(node);\n            };\n            BaseClass.prototype.assistiveMml = function () {\n                var e_1, _a;\n                if (!this.processed.isSet('assistive-mml')) {\n                    try {\n                        for (var _b = __values(this.math), _c = _b.next(); !_c.done; _c = _b.next()) {\n                            var math = _c.value;\n                            math.assistiveMml(this);\n                        }\n                    }\n                    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n                    finally {\n                        try {\n                            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                        }\n                        finally { if (e_1) throw e_1.error; }\n                    }\n                    this.processed.set('assistive-mml');\n                }\n                return this;\n            };\n            BaseClass.prototype.state = function (state, restore) {\n                if (restore === void 0) { restore = false; }\n                _super.prototype.state.call(this, state, restore);\n                if (state < MathItem_js_1.STATE.ASSISTIVEMML) {\n                    this.processed.clear('assistive-mml');\n                }\n                return this;\n            };\n            return BaseClass;\n        }(BaseDocument)),\n        _a.OPTIONS = __assign(__assign({}, BaseDocument.OPTIONS), { enableAssistiveMml: true, renderActions: (0, Options_js_1.expandable)(__assign(__assign({}, BaseDocument.OPTIONS.renderActions), { assistiveMml: [MathItem_js_1.STATE.ASSISTIVEMML] })) }),\n        _a.assistiveStyles = {\n            'mjx-assistive-mml': {\n                position: 'absolute !important',\n                top: '0px', left: '0px',\n                clip: 'rect(1px, 1px, 1px, 1px)',\n                padding: '1px 0px 0px 0px !important',\n                border: '0px !important',\n                display: 'block !important',\n                width: 'auto !important',\n                overflow: 'hidden !important',\n                '-webkit-touch-callout': 'none',\n                '-webkit-user-select': 'none',\n                '-khtml-user-select': 'none',\n                '-moz-user-select': 'none',\n                '-ms-user-select': 'none',\n                'user-select': 'none'\n            },\n            'mjx-assistive-mml[display=\"block\"]': {\n                width: '100% !important'\n            }\n        },\n        _a;\n}\nexports.AssistiveMmlMathDocumentMixin = AssistiveMmlMathDocumentMixin;\nfunction AssistiveMmlHandler(handler) {\n    handler.documentClass =\n        AssistiveMmlMathDocumentMixin(handler.documentClass);\n    return handler;\n}\nexports.AssistiveMmlHandler = AssistiveMmlHandler;\n//# sourceMappingURL=assistive-mml.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MmlVisitor = void 0;\nvar MmlFactory_js_1 = require(\"./MmlFactory.js\");\nvar Visitor_js_1 = require(\"../Tree/Visitor.js\");\nvar MmlVisitor = (function (_super) {\n    __extends(MmlVisitor, _super);\n    function MmlVisitor(factory) {\n        if (factory === void 0) { factory = null; }\n        if (!factory) {\n            factory = new MmlFactory_js_1.MmlFactory();\n        }\n        return _super.call(this, factory) || this;\n    }\n    MmlVisitor.prototype.visitTextNode = function (_node) {\n        var _args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            _args[_i - 1] = arguments[_i];\n        }\n    };\n    MmlVisitor.prototype.visitXMLNode = function (_node) {\n        var _args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            _args[_i - 1] = arguments[_i];\n        }\n    };\n    return MmlVisitor;\n}(Visitor_js_1.AbstractVisitor));\nexports.MmlVisitor = MmlVisitor;\n//# sourceMappingURL=MmlVisitor.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.SerializedMmlVisitor = exports.toEntity = exports.DATAMJX = void 0;\nvar MmlVisitor_js_1 = require(\"./MmlVisitor.js\");\nvar MmlNode_js_1 = require(\"./MmlNode.js\");\nvar mi_js_1 = require(\"./MmlNodes/mi.js\");\nexports.DATAMJX = 'data-mjx-';\nvar toEntity = function (c) { return '&#x' + c.codePointAt(0).toString(16).toUpperCase() + ';'; };\nexports.toEntity = toEntity;\nvar SerializedMmlVisitor = (function (_super) {\n    __extends(SerializedMmlVisitor, _super);\n    function SerializedMmlVisitor() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    SerializedMmlVisitor.prototype.visitTree = function (node) {\n        return this.visitNode(node, '');\n    };\n    SerializedMmlVisitor.prototype.visitTextNode = function (node, _space) {\n        return this.quoteHTML(node.getText());\n    };\n    SerializedMmlVisitor.prototype.visitXMLNode = function (node, space) {\n        return space + node.getSerializedXML();\n    };\n    SerializedMmlVisitor.prototype.visitInferredMrowNode = function (node, space) {\n        var e_1, _a;\n        var mml = [];\n        try {\n            for (var _b = __values(node.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                mml.push(this.visitNode(child, space));\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return mml.join('\\n');\n    };\n    SerializedMmlVisitor.prototype.visitTeXAtomNode = function (node, space) {\n        var children = this.childNodeMml(node, space + '  ', '\\n');\n        var mml = space + '<mrow' + this.getAttributes(node) + '>' +\n            (children.match(/\\S/) ? '\\n' + children + space : '') + '</mrow>';\n        return mml;\n    };\n    SerializedMmlVisitor.prototype.visitAnnotationNode = function (node, space) {\n        return space + '<annotation' + this.getAttributes(node) + '>'\n            + this.childNodeMml(node, '', '')\n            + '</annotation>';\n    };\n    SerializedMmlVisitor.prototype.visitDefault = function (node, space) {\n        var kind = node.kind;\n        var _a = __read((node.isToken || node.childNodes.length === 0 ? ['', ''] : ['\\n', space]), 2), nl = _a[0], endspace = _a[1];\n        var children = this.childNodeMml(node, space + '  ', nl);\n        return space + '<' + kind + this.getAttributes(node) + '>'\n            + (children.match(/\\S/) ? nl + children + endspace : '')\n            + '</' + kind + '>';\n    };\n    SerializedMmlVisitor.prototype.childNodeMml = function (node, space, nl) {\n        var e_2, _a;\n        var mml = '';\n        try {\n            for (var _b = __values(node.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var child = _c.value;\n                mml += this.visitNode(child, space) + nl;\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return mml;\n    };\n    SerializedMmlVisitor.prototype.getAttributes = function (node) {\n        var e_3, _a;\n        var attr = [];\n        var defaults = this.constructor.defaultAttributes[node.kind] || {};\n        var attributes = Object.assign({}, defaults, this.getDataAttributes(node), node.attributes.getAllAttributes());\n        var variants = this.constructor.variants;\n        if (attributes.hasOwnProperty('mathvariant') && variants.hasOwnProperty(attributes.mathvariant)) {\n            attributes.mathvariant = variants[attributes.mathvariant];\n        }\n        try {\n            for (var _b = __values(Object.keys(attributes)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var name_1 = _c.value;\n                var value = String(attributes[name_1]);\n                if (value === undefined)\n                    continue;\n                attr.push(name_1 + '=\"' + this.quoteHTML(value) + '\"');\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        return attr.length ? ' ' + attr.join(' ') : '';\n    };\n    SerializedMmlVisitor.prototype.getDataAttributes = function (node) {\n        var data = {};\n        var variant = node.attributes.getExplicit('mathvariant');\n        var variants = this.constructor.variants;\n        variant && variants.hasOwnProperty(variant) && this.setDataAttribute(data, 'variant', variant);\n        node.getProperty('variantForm') && this.setDataAttribute(data, 'alternate', '1');\n        node.getProperty('pseudoscript') && this.setDataAttribute(data, 'pseudoscript', 'true');\n        node.getProperty('autoOP') === false && this.setDataAttribute(data, 'auto-op', 'false');\n        var scriptalign = node.getProperty('scriptalign');\n        scriptalign && this.setDataAttribute(data, 'script-align', scriptalign);\n        var texclass = node.getProperty('texClass');\n        if (texclass !== undefined) {\n            var setclass = true;\n            if (texclass === MmlNode_js_1.TEXCLASS.OP && node.isKind('mi')) {\n                var name_2 = node.getText();\n                setclass = !(name_2.length > 1 && name_2.match(mi_js_1.MmlMi.operatorName));\n            }\n            setclass && this.setDataAttribute(data, 'texclass', texclass < 0 ? 'NONE' : MmlNode_js_1.TEXCLASSNAMES[texclass]);\n        }\n        node.getProperty('scriptlevel') && node.getProperty('useHeight') === false &&\n            this.setDataAttribute(data, 'smallmatrix', 'true');\n        return data;\n    };\n    SerializedMmlVisitor.prototype.setDataAttribute = function (data, name, value) {\n        data[exports.DATAMJX + name] = value;\n    };\n    SerializedMmlVisitor.prototype.quoteHTML = function (value) {\n        return value\n            .replace(/&/g, '&amp;')\n            .replace(/</g, '&lt;').replace(/>/g, '&gt;')\n            .replace(/\\\"/g, '&quot;')\n            .replace(/[\\uD800-\\uDBFF]./g, exports.toEntity)\n            .replace(/[\\u0080-\\uD7FF\\uE000-\\uFFFF]/g, exports.toEntity);\n    };\n    SerializedMmlVisitor.variants = {\n        '-tex-calligraphic': 'script',\n        '-tex-bold-calligraphic': 'bold-script',\n        '-tex-oldstyle': 'normal',\n        '-tex-bold-oldstyle': 'bold',\n        '-tex-mathit': 'italic'\n    };\n    SerializedMmlVisitor.defaultAttributes = {\n        math: {\n            xmlns: 'http://www.w3.org/1998/Math/MathML'\n        }\n    };\n    return SerializedMmlVisitor;\n}(MmlVisitor_js_1.MmlVisitor));\nexports.SerializedMmlVisitor = SerializedMmlVisitor;\n//# sourceMappingURL=SerializedMmlVisitor.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractVisitor = void 0;\nvar Node_js_1 = require(\"./Node.js\");\nvar AbstractVisitor = (function () {\n    function AbstractVisitor(factory) {\n        var e_1, _a;\n        this.nodeHandlers = new Map();\n        try {\n            for (var _b = __values(factory.getKinds()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var kind = _c.value;\n                var method = this[AbstractVisitor.methodName(kind)];\n                if (method) {\n                    this.nodeHandlers.set(kind, method);\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    }\n    AbstractVisitor.methodName = function (kind) {\n        return 'visit' + (kind.charAt(0).toUpperCase() + kind.substr(1)).replace(/[^a-z0-9_]/ig, '_') + 'Node';\n    };\n    AbstractVisitor.prototype.visitTree = function (tree) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        return this.visitNode.apply(this, __spreadArray([tree], __read(args), false));\n    };\n    AbstractVisitor.prototype.visitNode = function (node) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        var handler = this.nodeHandlers.get(node.kind) || this.visitDefault;\n        return handler.call.apply(handler, __spreadArray([this, node], __read(args), false));\n    };\n    AbstractVisitor.prototype.visitDefault = function (node) {\n        var e_2, _a;\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        if (node instanceof Node_js_1.AbstractNode) {\n            try {\n                for (var _b = __values(node.childNodes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var child = _c.value;\n                    this.visitNode.apply(this, __spreadArray([child], __read(args), false));\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n        }\n    };\n    AbstractVisitor.prototype.setNodeHandler = function (kind, handler) {\n        this.nodeHandlers.set(kind, handler);\n    };\n    AbstractVisitor.prototype.removeNodeHandler = function (kind) {\n        this.nodeHandlers.delete(kind);\n    };\n    return AbstractVisitor;\n}());\nexports.AbstractVisitor = AbstractVisitor;\n//# sourceMappingURL=Visitor.js.map"], "names": [], "sourceRoot": ""}