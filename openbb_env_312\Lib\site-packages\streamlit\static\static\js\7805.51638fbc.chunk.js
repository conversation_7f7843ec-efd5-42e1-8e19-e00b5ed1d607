(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[7805],{32508:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>u});var a=i(66845),n=i(18157),r=i(91487),s=i(23849),c=i(62622);const o=(0,i(1515).Z)("div",{target:"e1uym6o70"})((t=>{let{theme:e,isFullScreen:i}=t;return{"& *":{fontFamily:e.genericFonts.bodyFont,fontSize:"9.6px"},"& svg":{maxWidth:"100%",width:i?"100%":"auto",height:i?"100%":"auto"},width:i?"100%":"auto",height:i?"100%":"auto"}}),"");var h=i(40864);const u=(0,c.Z)((function(t){let{element:e,isFullScreen:i}=t;const c="graphviz-chart-".concat(e.elementId);return(0,a.useEffect)((()=>{try{if((0,r.graphviz)("#".concat(c)).zoom(!1).fit(!0).scale(1).engine(e.engine).renderDot(e.spec),i||e.useContainerWidth){const t=(0,n.Ys)("#".concat(c," > svg")).node();t.removeAttribute("width"),t.removeAttribute("height")}}catch(t){(0,s.H)(t)}}),[c,e.engine,e.spec,e.useContainerWidth,i]),(0,h.jsx)(o,{className:"graphviz stGraphVizChart","data-testid":"stGraphVizChart",id:c,isFullScreen:i})}))},47318:()=>{},57516:()=>{},38728:()=>{}}]);