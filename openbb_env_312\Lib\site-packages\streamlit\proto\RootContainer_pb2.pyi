"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2024)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""
import builtins
import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _RootContainer:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _RootContainerEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_RootContainer.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    MAIN: _RootContainer.ValueType  # 0
    SIDEBAR: _RootContainer.ValueType  # 1
    EVENT: _RootContainer.ValueType  # 2
    BOTTOM: _RootContainer.ValueType  # 3

class RootContainer(_RootContainer, metaclass=_RootContainerEnumTypeWrapper):
    """Constants for our "RootContainers" - the top-level containers
    that `st.foo` and `st.sidebar.foo` write to. Each value corresponds
    to the first index in any `delta_path` that uses these containers.
    (This enum is not used in any protobuf messages; we declare it here so
    that the values remain synced between Python and the frontend.)
    """

MAIN: RootContainer.ValueType  # 0
SIDEBAR: RootContainer.ValueType  # 1
EVENT: RootContainer.ValueType  # 2
BOTTOM: RootContainer.ValueType  # 3
global___RootContainer = RootContainer
