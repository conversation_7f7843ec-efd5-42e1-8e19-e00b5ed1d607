interactions:
- request:
    body: null
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
    method: GET
    uri: https://www.wsj.com/market-data/mutualfunds-etfs/etfmovers?id=%7B%22application%22%3A%22WSJ%22%2C%22etfMover%22%3A%22most_active%22%2C%22count%22%3A25%7D&type=mdc_etfmovers
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA7VbbXPbNhL+KxrNzX2pTQMgCZL5Fsd24hu7cSqldnLsdHgya+tqST6Kbpxm8t8P
        WIqyBIDAgmxnbqYXUra8+zz7gsWz38bz2/Gr8bd8XDw+PsxnRT1fLfPxq3x8PflXPj7Ix2X92+Xq
        j7KCh4vVuv61mNXzP0p4OVs9LWvxhsXfxwfj+utjKX7Z4nb2q/iphfyptXh8W9TF+NW38Xy5rqun
        Rbms1+NX//42/s/DU3lXFev1m/tiuSwfxI8e/Tm/WxRLdvS/p1Vdro8YYVGUpiQ7mq0Wj6v1vC7F
        L4Rvrb6Kz3+ciH8+FOv6qprPxHenAWUHY/Eb6vl6Lb40Ohg/ltVMfKX8jjvxiUMaZKF4Kj+/fUYC
        yg/Gy2Ih//yrajW5L6pyPfr4UFeF+Odocr+q6tGHDx+kkfPZ72UlPjdp/70o13WxeBSP5J97SJJD
        QqeMvCLyfwGJ0h/g/7046PR5Bt88rYrb8vbsaXkr3v2xeniS30/DjEVJlqUH499W1aKo6/J2Y91Y
        mjdWn7dWjMGMvdf7po/B9t0P/Lz5zrH40oBdildPlYThvq4f16+Ojr58+RJ8Wf83EM4/WhTV72V9
        KMFs0REgH32cHIEjvh/gAA2TOMpixpCAUhKwzI4opUEaK4jSgGRbQE/mVfksaD06KeYPX0fTycXr
        0fHTw8OI3YwapHdhFa8vELAmceIFK+cJCRmLDKiCjd2wSltsqIL9RlQ5D5K+oIIbsKASThgPaYSN
        0izIqCNMmQgcNUyFockW1fkmSBn5YfSpLKrRtCqL9VP1dXS8Wt6OTqdne6heTDGxypkXqFEWMUGE
        2BSq0kZLrEpbrLEqHWBENcqCqDeqwg1oUOM4DPGgxnEQcjuoRAGUdIXopFzMZwLFp1m9qppYDU2x
        Onl/g4lVxjxhDUlCI2qKVbCyG1ZBIWJDVX3/AmoY0N75V3oBjSrJoljQFolqEgckdOTfgKuRSgNG
        bfVUqaRTXCUNM18URXQagxOssmVcRq0ZV1jchSLpHZo+VVSAmHISpkgUGQsSR8IVvYPaFhGAHhWh
        Mv92RegElXipH7YkC9OUMwO2YKs1QkN7hFJzixSRIBsQoRMPbDNCeUSweTcSKSlygSvjUQlR2Slv
        wJ1cnfw0mvzzahQTIiunqKVP63oPx6tPf0OiDbMwoky2anqiBbNsMaq0whqO+zG8xVF0umFvHIUX
        0DCGLCI8DbGJNkgcfa5o7xOun1wI74zRDaCW6LxCRqcnrKnAlVNiyrzCTOsJhthPMNIHZljTAeF5
        5RWenIszHbYtikQxcQYn0zPvy/HlbL4slrN58SCS7kM5q8V/IOlC0Cpt7s3FGQJPmoR+eHJKBZdD
        A57SPGLNttazi7TdDCfv3w9JJ2DRjJIkIxHl2EIaUHeUZoYgfcm1b6tiKb6mPb08b4YLP/588noT
        twqm4g0G08wT0zBKaGrscaWR9hi1Zl7pAXOTG4ZB3BdU6QV0iEbCtpDE2NQrjsmuqVEDoQYrjal2
        IJ0fr56fR/8YvZvf3Y8+zcuH29GbVfW4qoq6NJ5N3316i0nC1BNgSuIwTrkpCUuDhyFMOsKW9g9b
        6QZ02GYkpTzBjgVpkMkRkWMuGJkC96UDnh7+VD7LmdH5Ug42SxGy5yfnbdBOi+qurA2xiznZiGbB
        C1vGeRYybhwmBZkjeK3tL7jBCC0bMEwCN6CjN42TOI6wZ5soFSY7S2ysYsuC9CV0JaLrGZxLm9ZX
        1NhqLmL5fBdM5Lw3jP2wjJI4Swk1FVcwrRtNaYO9usYdWEZB2hdLr2NqyhOSRNgeWFCMpC4kQ3XW
        u9cstUn4zb3omkYXMigP3xSPalye3Zz/DX0vi1gaxeZTqTRtSJ8Umke8AsnepxnpBPyEN6MZTyl6
        bC/6N9eI1xCV4hkPI9ZZUyFQa3mFNHorEXBU1osPqNbJ89TKxMGOcG6K2MZua/7l9vzbHbOs/9hX
        +sFj/CDafXxHTCimJ04Nw3x9lv/T03pdyosZ0swhdrE8v77EYOnZJTGasjQ0t8FgmQVLYYF12Atm
        m7Gk/fOv9AN+BiFCVtAVG7U0CN1Q0tR6vnkZ9zYHm80UYm/s8A6TfAn3Q5KkUWSeJYFdw/pdmpqB
        JP2BFF7A19E0CzlLsDGZBNQxEZSDFMMtuF5ILydvzkfHVfHn/EGNyNPrz6gi6hmRooGKKTMNj8Au
        K47UWkWl0eaTqcCxd3KVbkADmXAeJiF2tpukQZQ45/ZqPBJA34Tj6euzUw3Fs9cYFFO/Gkkz0Qkl
        zFQjwShbK6RgrA/ozbFIs/6xKJ2Any5wUSFDdFJ1ZFQWhGokCjJGnUPdRSEKpGxpm7Hus0nC8BkF
        qa8wJc3SMCaJKcFm6r2nAmlkhVS6oAvS/vdpnz0gpVEcsRgblhELuANVcabXr9P0WVETlYuyupsv
        70aXYMxai9BTTOcjzlqecKYkDs3nTmmfNUIdl9ysQ2WUDojQU3zbE0s4swx7WHHJFuIg0vVFKdfA
        PJ7Xs9V8qV+enR+fY9QnIfENyDAjNDRdcIfcGpDyz7chKC3uQrB3nQQv4AtlFooago1IyuEs6eh4
        mHYIIaAycgn/TlZfwv0G9uT9NSrD+oYk4yIojWM9rpwL9RbWmmLB+i5I+9+HSj+gZ7aUxlxwHNv7
        iIBwCU+I3vpIvUfXTcvFSiRZ60ULSk1EIz9UecoiAaypFZJBb0u0SWwDlSozpRdMef9EC17Aa8RE
        oKboy9AwDhzzvUNDOyTP0QmiHwKZmLEf+hHVD8We6ZfLSbU5/Qo7LbiCQdZo7e6IBtyLSjd8/0UC
        Ih7WUx+B685nI/FodHU5Oj2ZjpIjQkVgC7qM74v1/RiE5PNbkIp/y/N9UXkuH+eNsDyX6vF8R1ze
        vtwVmLcfakTmeSMzB9m5hKfRo+9KzeGVtFu8kn/Hi9w8l3rzXGM0/AqE5PxF6l59hZ/5OIFHW2bn
        G+V5vuV2Lsmd77M7b9Xn+S6/81Z/ngPD4ffbJOiNA4Da8NnJy7MNSPDY2vHvO1FnObxveJ7vKtJz
        hdHw49Jy+AET2+ETYJ/yEcUxeSNO3//Qz+1fkG8E6vBaUB8e+YvUc5nXsDTQheoYGjR6dScPGs26
        ygNQre/QwC1cV8kgVdtIMmzj248MWx27kQtgvp0M0kgXF8A9nVwAWfsAKjRO8qCCLm9HZQRQubtT
        AijdtZQAWvcdLrjl7hoXLqbYvNDec/lRYat+N6cFab4jL0gjnXlB+qeTCyCGH8IF6SQfKmiieAwV
        Gm28kwpEowGxpAObSF4rEu9vsHlhq+H0JEOrmTeSARxgJ4Oc+Lu4oH9mlwpSQj+kQoCPfLigSekx
        XGgU9e4KIVX1eoFg1NEnGDqEKb5DaCX23tg3Snsj9mCwqyYwNVXoNUE4xIY9GZQGfLsDXYCPwb7R
        4bvzgNTia6mgoQ0yG+xJfg3ZYIIuDbQXI1p9vpER4AZnNlA5Y8gGtLthBLn+sGww8WSEKttHVYZG
        vY+gBES+mg7gzLGlhFnGr6F/9envLQVbVb+5FIDFrnygHSkM6Ks5Ywd9EPkPQV/6yAd8TeyPKgVS
        8+9uEEH3bzgzkr0zI0r8r3PBIxP0I0O7C2CuDcIDzrMjcZ8dpYu6yZAOTAVX3qlAXRHAsAE2BTCJ
        gJlqw97B0bUyoNLg5uIMyYLt6oAnC9oNAiMLYInAWQ+cp0bpmm4S8GHdIbjIgwP6YgGqQZASDExG
        yIwJYa8aoBYNVCaIt1gmZP2Y0O4dmDsD8Wvd+cBZG2AFoZsKcg1hABXARz7pQFtHQBUH2ErAcIHo
        CaHdTTAMEFDrCSot3n16iy0TtB8t2m0Fc5mQvvhreKF+aJcXdFiKACf5pAhtiQE1YYRdBsykOTIn
        ib1TBHapwZAnsGfK7XKDHyO2Ow7mQWOQIRKF8wgBXuokBBs4aGyc5JMptNUHVOPQbEAgWodYZwTs
        QewQonsVQqWAx71DuxLhyYB2M8LcNIDVdg5I49xdQ2xhgFyUGMAA77GCtjCB6hpgbwKBf6jfOait
        o2WBQiXA2c05lgD9zg7bfQpzryCt/iu6xrD7qgHWKwbgDy7yumlQ1yxQJaHZtsD0CoYM0O5cWHoF
        xNqFSo6LD+hGst+UYbuF0XERJV3irBDcXSHs+YENu34AL/kNmZTlDFR+aHY0MPxIjVdRxpsodVlD
        ZcD59SWWAf16xu3uhjk9gNEOBsgVDgQD0u7JM6xyDGAAeMlr0qSudKAyBGx2YAhAU/fJsmPFQxsu
        vcOWh3bVwxP/duPDnAGkyX/NmYGm3fCTYfBLH3n1B+oiCCr+YR8ENWg0gU+NDYKyGKKCf3r9Gd0c
        9Iv+dk/EHP3SZCf6Wn4wTha7JwmwNjIAfXCSD/za+ghqkgBbJJhbJz32ScMcM/rtOomG/dlrLPZp
        r9q/3S4xTwukva7GUGOH6XqpO+5h2WQI8tJFXjMkdekElfYROR8Elzrs0R7syCUUlQjTz2gi9JSm
        tTsp5hKQ6ZoCjQiRkwjSQzYiDLt5/uxJBG1VBTUigI0VzOWC6eLZOEfsXF3RssEptg/crrD4kmCz
        yWKeE0jTndkAIT1hFnViOjAbnPo1gfqCC4YEGAkS7Lro98wpN1Fgb+FFa/+Pz7H6s+3iiy/um/0X
        I+6wAmPHXdrlwl06xIb7oPrf+MirAVDXYlBlgDfnfnf/xwzHP9LoE93iZFiT0Q4BJ++v0TWgZ/hv
        tmbMNYBr53fTMcBZBMA5NiIM0xuAl3zuDrRlGlQnKHdqENIzYmoEQd/VfbtoWK4xXBpcYC8N2iUb
        Ty60uzbmxlCmGlcpSGIXFag2d9xlAh9WChofeSlS1RUcVC2QmzgYbbKhOWz2cVDd4d5KjtYd/oju
        DuN+BaLd0DEXCLmk48wMKIGyrT8cqDsAJ33/BeDbW9vpdJYi7N/7vGF5R/z27+Pv/weQ9t+CCGIA
        AA==
    headers:
      Cache-Control:
      - public, max-age=120, no-cache=Set-Cookie
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Security-Policy:
      - 'default-src ''self'' cdn.privacy-mgmt.com;script-src ''self'' *.wsj.net *.wsj.com
        ''unsafe-inline'' ''unsafe-eval'';script-src-elem * ''unsafe-inline'';manifest-src
        ''self'' *.wsj.com;media-src * data: blob: https:;worker-src * ''unsafe-inline''
        ''unsafe-eval'' blob: data:;frame-src * ''unsafe-inline'';connect-src * ''unsafe-inline''
        ''unsafe-eval'';form-action * ''unsafe-inline'';frame-ancestors *;script-src-attr
        ''unsafe-inline'';object-src ''self'' ''unsafe-inline'';img-src * data: blob:
        https:;font-src ''self'' * ''unsafe-inline'';upgrade-insecure-requests;base-uri
        ''self'';style-src ''self'' https: ''unsafe-inline'''
      Content-Type:
      - application/json; charset=utf-8
      Cross-Origin-Resource-Policy:
      - same-site
      Date:
      - Mon, 01 Jul 2024 21:23:38 GMT
      ETag:
      - W/"6208-aSWJ479odA/1qJ7uf5aiu1h8BA8"
      GC-Versions:
      - 2.2.363|0.4.1243|4.1.2
      Origin-Agent-Cluster:
      - ?1
      Referrer-Policy:
      - strict-origin-when-cross-origin,unsafe-url
      Strict-Transport-Security:
      - max-age=63072000; includeSubDomains; preload
      Transfer-Encoding:
      - chunked
      Via:
      - 1.1 0b68d76a38a9449f14204cc8596730e4.cloudfront.net (CloudFront)
      X-Amz-Cf-Id:
      - e_JfNEAUxhkA94toGkPacLzzbwcEA-QvyNbUXFc9FjDjhYCFpyR9aw==
      X-Amz-Cf-Pop:
      - YVR52-P1
      X-Cache:
      - Miss from cloudfront
      X-Content-Type-Options:
      - nosniff
      X-DNS-Prefetch-Control:
      - 'off'
      X-Download-Options:
      - noopen
      X-Frame-Options:
      - SAMEORIGIN
      X-Permitted-Cross-Domain-Policies:
      - none
      X-XSS-Protection:
      - 1; mode=block
    status:
      code: 200
      message: OK
version: 1
