## OpenBB-CN (A股/港股/期货)

### 快速开始
1) 准备环境
```
python -m venv .venv
./.venv/Scripts/activate
pip install -r requirements.txt
```

2) 配置 `.env`
```
TUSHARE_TOKEN=你的token
DATABASE_URL=postgresql+psycopg2://openbb:openbb@localhost:5432/openbb_cn
APP_HOST=0.0.0.0
APP_PORT=6900
```
可前往 Tushare 获取 Token：[`https://tushare.pro/user/token`](https://tushare.pro/user/token)

3) 启动数据库（可选 Docker）
```
docker compose up -d db
```

4) 启动 API
```
uvicorn openbb_cn.api.app:app --host 0.0.0.0 --port 6900 --reload
```

5) Python SDK 示例
```
from openbb_cn.sdk.obb import obb
df = obb.equity.price.historical("600519.SH", start="20240101", end="20240630")
print(df.tail())
```

### 生产部署（Docker）
```
docker compose up -d --build
```

### 说明
- 首个 Provider：Tushare（合规API）。
- 支持 PostgreSQL 缓存/快照。
- 默认端口 6900。

FROM python:3.12-slim

WORKDIR /app

COPY requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

COPY . /app

EXPOSE 6900

CMD ["uvicorn", "openbb_cn.api.app:app", "--host", "0.0.0.0", "--port", "6900"]

version: "3.9"
services:
  db:
    image: postgres:16
    environment:
      POSTGRES_DB: openbb_cn
      POSTGRES_USER: openbb
      POSTGRES_PASSWORD: openbb
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data

  api:
    build: .
    env_file: .env
    environment:
      - DATABASE_URL=postgresql+psycopg2://openbb:openbb@db:5432/openbb_cn
    depends_on:
      - db
    ports:
      - "6900:6900"
    command: ["uvicorn", "openbb_cn.api.app:app", "--host", "0.0.0.0", "--port", "6900"]

volumes:
  db_data:

import typer
from .config import get_settings
from .api.app import create_app
import uvicorn


app = typer.Typer(help="OpenBB-CN CLI")


@app.command()
def api(host: str | None = None, port: int | None = None, reload: bool = True):
    settings = get_settings()
    uvicorn.run(
        "openbb_cn.api.app:app",
        host=host or settings.app_host,
        port=port or settings.app_port,
        reload=reload,
    )

from __future__ import annotations

from fastapi import FastAPI, Query
from pydantic import BaseModel
from ..sdk.obb import obb
from ..config import get_settings
from ..storage.db import init_db


class PriceResponse(BaseModel):
    symbol: str
    rows: int


def create_app() -> FastAPI:
    settings = get_settings()
    init_db()
    app = FastAPI(title="OpenBB-CN API", version="0.1.0")

    @app.get("/equity/price/historical", response_model=PriceResponse)
    def historical(
        symbol: str = Query(..., description="ts_code, 如 600519.SH/000001.SZ"),
        start: str | None = Query(None, description="开始日期 YYYYMMDD"),
        end: str | None = Query(None, description="结束日期 YYYYMMDD"),
    ):
        df = obb.equity.price.historical(symbol=symbol, start=start, end=end)
        return PriceResponse(symbol=symbol, rows=len(df))

    return app


app = create_app()


from __future__ import annotations

from dataclasses import dataclass
from typing import Optional
import pandas as pd

from ..providers.tushare_provider import TushareProvider
from ..storage.db import db_session
from ..storage.schema import PriceDaily


@dataclass
class EquityPrice:
    def historical(self, symbol: str, start: Optional[str] = None, end: Optional[str] = None) -> pd.DataFrame:
        provider = TushareProvider()
        df = provider.daily(symbol=symbol, start=start, end=end)

        if not df.empty:
            with db_session() as session:
                for _, row in df.iterrows():
                    session.merge(
                        PriceDaily(
                            symbol=symbol,
                            trade_date=row.trade_date,
                            open=float(row.open) if row.open is not None else None,
                            high=float(row.high) if row.high is not None else None,
                            low=float(row.low) if row.low is not None else None,
                            close=float(row.close) if row.close is not None else None,
                            volume=float(row.volume) if row.volume is not None else None,
                            provider=row.provider,
                        )
                    )
        return df


@dataclass
class Equity:
    price: EquityPrice = EquityPrice()


@dataclass
class OBB_CN:
    equity: Equity = Equity()


obb = OBB_CN()


from __future__ import annotations

import os
import pandas as pd
import tushare as ts
from datetime import date
from typing import Optional


class TushareProvider:
    def __init__(self, token: Optional[str] = None):
        self._token = token or os.getenv("TUSHARE_TOKEN")
        if not self._token:
            raise RuntimeError("Tushare token not set. Set TUSHARE_TOKEN in environment or .env")
        ts.set_token(self._token)
        self.pro = ts.pro_api()

    def daily(self, symbol: str, start: Optional[str] = None, end: Optional[str] = None) -> pd.DataFrame:
        # Tushare 使用 ts_code，如 600519.SH
        df = self.pro.daily(ts_code=symbol, start_date=start, end_date=end)
        if df is None or df.empty:
            return pd.DataFrame()
        df = df.rename(
            columns={
                "trade_date": "trade_date",
                "open": "open",
                "high": "high",
                "low": "low",
                "close": "close",
                "vol": "volume",
            }
        )
        df["provider"] = "tushare"
        # 统一日期格式
        df["trade_date"] = pd.to_datetime(df["trade_date"]).dt.date
        df = df[["trade_date", "open", "high", "low", "close", "volume", "provider"]]
        return df.sort_values("trade_date")


from __future__ import annotations

from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import String, Integer, Date, DateTime, Float, Index
from datetime import datetime, date


class Base(DeclarativeBase):
    pass


class PriceDaily(Base):
    __tablename__ = "price_daily"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    symbol: Mapped[str] = mapped_column(String(32), index=True)
    trade_date: Mapped[date] = mapped_column(Date, index=True)
    open: Mapped[float | None] = mapped_column(Float)
    high: Mapped[float | None] = mapped_column(Float)
    low: Mapped[float | None] = mapped_column(Float)
    close: Mapped[float | None] = mapped_column(Float)
    volume: Mapped[float | None] = mapped_column(Float)
    provider: Mapped[str] = mapped_column(String(32))
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index("ix_price_daily_symbol_date", "symbol", "trade_date", unique=True),
    )

from __future__ import annotations

from sqlalchemy import create_engine, text
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
from .schema import Base
from ..config import get_settings


def create_db_engine() -> Engine:
    url = get_settings().database_url
    engine = create_engine(url, pool_pre_ping=True)
    return engine


engine: Engine | None = None
SessionLocal = None


def init_db() -> None:
    global engine, SessionLocal
    engine = create_db_engine()
    Base.metadata.create_all(bind=engine)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@contextmanager
def db_session():
    if SessionLocal is None:
        init_db()
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()

from pydantic import BaseModel
from functools import lru_cache
import os
from dotenv import load_dotenv


class AppConfig(BaseModel):
    app_host: str = "0.0.0.0"
    app_port: int = 6900
    database_url: str = "postgresql+psycopg2://openbb:openbb@localhost:5432/openbb_cn"
    tushare_token: str | None = None


@lru_cache(maxsize=1)
def get_settings() -> AppConfig:
    load_dotenv()
    return AppConfig(
        app_host=os.getenv("APP_HOST", "0.0.0.0"),
        app_port=int(os.getenv("APP_PORT", "6900")),
        database_url=os.getenv(
            "DATABASE_URL",
            "postgresql+psycopg2://openbb:openbb@localhost:5432/openbb_cn",
        ),
        tushare_token=os.getenv("TUSHARE_TOKEN"),
    )

__all__ = ["api", "sdk", "providers", "storage", "config"]

[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "openbb_cn"
version = "0.1.0"
description = "OpenBB-CN: 中国化的 OpenBB (A股/港股/期货)，含 API/SDK/Provider/CLI"
readme = "README.md"
requires-python = ">=3.10"
dependencies = []

[project.scripts]
openbb-cn = "openbb_cn.cli:app"

TUSHARE_TOKEN=967c1a5085b6cfceb171ea390aee91783909d33e7c1274c937b750c6
# PostgreSQL 连接串（docker-compose 默认）
DATABASE_URL=postgresql+psycopg2://openbb:openbb@localhost:5432/openbb_cn
APP_HOST=0.0.0.0
APP_PORT=6900

