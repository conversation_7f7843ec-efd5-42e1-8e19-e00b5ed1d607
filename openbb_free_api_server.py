#!/usr/bin/env python3
"""
OpenBB 免费API补充服务器
使用免费API补充OpenBB缺失的功能
支持: Yahoo Finance, Alpha Vantage, NewsAPI, FRED等
"""

from fastapi import FastAPI, HTTPException, Query
from fastapi.responses import JSONResponse
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import time
from typing import Optional, List

app = FastAPI(
    title="OpenBB 免费API补充服务器",
    description="使用免费API补充OpenBB缺失的功能",
    version="1.0.0"
)

# API密钥配置 - 请替换为真实密钥
API_KEYS = {
    "alpha_vantage": "demo",  # https://www.alphavantage.co/support/#api-key
    "finnhub": "demo",        # https://finnhub.io/
    "newsapi": "demo",        # https://newsapi.org/
    "fred": "demo"            # https://fred.stlouisfed.org/docs/api/api_key.html
}

# 请求缓存 (简单内存缓存)
cache = {}
CACHE_DURATION = 300  # 5分钟缓存


def get_cached_or_fetch(cache_key: str, fetch_func):
    """获取缓存数据或重新获取"""
    now = time.time()

    if cache_key in cache:
        data, timestamp = cache[cache_key]
        if now - timestamp < CACHE_DURATION:
            return data

    # 重新获取数据
    data = fetch_func()
    cache[cache_key] = (data, now)
    return data


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "OpenBB 免费API补充服务器",
        "version": "1.0.0",
        "endpoints": [
            "/api/v1/equity/price/historical",
            "/api/v1/equity/price/quote",
            "/api/v1/equity/fundamental/income",
            "/api/v1/equity/fundamental/balance",
            "/api/v1/equity/fundamental/cash",
            "/api/v1/news/company",
            "/api/v1/economy/gdp",
            "/api/v1/crypto/price/historical",
            "/api/v1/technical/sma"
        ]
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "cache_size": len(cache)
    }


@app.get("/api/v1/equity/price/historical")
async def get_historical_data(
    symbol: str = Query(..., description="股票代码"),
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    provider: str = Query("yahoo", description="数据提供商: yahoo, alpha_vantage")
):
    """获取股票历史数据"""
    cache_key = f"hist_{symbol}_{start_date}_{end_date}_{provider}"

    def fetch_data():
        if provider == "yahoo":
            return _get_yahoo_historical(symbol, start_date, end_date)
        elif provider == "alpha_vantage":
            return _get_alpha_vantage_historical(symbol, start_date, end_date)
        else:
            raise HTTPException(status_code=400, detail="不支持的数据提供商")

    try:
        data = get_cached_or_fetch(cache_key, fetch_data)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取历史数据失败: {str(e)}")


@app.get("/api/v1/equity/price/quote")
async def get_quote_data(
    symbol: str = Query(..., description="股票代码，多个用逗号分隔"),
    provider: str = Query("yahoo", description="数据提供商: yahoo, alpha_vantage")
):
    """获取股票实时报价"""
    symbols = [s.strip() for s in symbol.split(',')]
    cache_key = f"quote_{symbol}_{provider}"

    def fetch_data():
        if provider == "yahoo":
            return _get_yahoo_quotes(symbols)
        elif provider == "alpha_vantage":
            return _get_alpha_vantage_quotes(symbols)
        else:
            raise HTTPException(status_code=400, detail="不支持的数据提供商")

    try:
        data = get_cached_or_fetch(cache_key, fetch_data)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取报价失败: {str(e)}")


@app.get("/api/v1/equity/fundamental/income")
async def get_income_statement(
    symbol: str = Query(..., description="股票代码"),
    period: str = Query("annual", description="报告期: annual, quarterly"),
    limit: int = Query(5, description="返回条数"),
    provider: str = Query("alpha_vantage", description="数据提供商")
):
    """获取利润表数据"""
    cache_key = f"income_{symbol}_{period}_{provider}"

    def fetch_data():
        if provider == "alpha_vantage":
            return _get_alpha_vantage_income(symbol, period, limit)
        else:
            raise HTTPException(status_code=400, detail="不支持的数据提供商")

    try:
        data = get_cached_or_fetch(cache_key, fetch_data)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取财务数据失败: {str(e)}")


@app.get("/api/v1/equity/fundamental/balance")
async def get_balance_sheet(
    symbol: str = Query(..., description="股票代码"),
    period: str = Query("annual", description="报告期: annual, quarterly"),
    limit: int = Query(5, description="返回条数"),
    provider: str = Query("alpha_vantage", description="数据提供商")
):
    """获取资产负债表数据"""
    cache_key = f"balance_{symbol}_{period}_{provider}"

    def fetch_data():
        if provider == "alpha_vantage":
            return _get_alpha_vantage_balance(symbol, period, limit)
        else:
            raise HTTPException(status_code=400, detail="不支持的数据提供商")

    try:
        data = get_cached_or_fetch(cache_key, fetch_data)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取财务数据失败: {str(e)}")


@app.get("/api/v1/equity/fundamental/cash")
async def get_cash_flow(
    symbol: str = Query(..., description="股票代码"),
    period: str = Query("annual", description="报告期: annual, quarterly"),
    limit: int = Query(5, description="返回条数"),
    provider: str = Query("alpha_vantage", description="数据提供商")
):
    """获取现金流量表数据"""
    cache_key = f"cash_{symbol}_{period}_{provider}"

    def fetch_data():
        if provider == "alpha_vantage":
            return _get_alpha_vantage_cash_flow(symbol, period, limit)
        else:
            raise HTTPException(status_code=400, detail="不支持的数据提供商")

    try:
        data = get_cached_or_fetch(cache_key, fetch_data)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取财务数据失败: {str(e)}")


@app.get("/api/v1/news/company")
async def get_company_news(
    symbol: Optional[str] = Query(None, description="股票代码"),
    limit: int = Query(10, description="返回条数"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    provider: str = Query("newsapi", description="数据提供商: newsapi, alpha_vantage")
):
    """获取公司新闻"""
    cache_key = f"news_{symbol}_{limit}_{provider}"

    def fetch_data():
        if provider == "newsapi":
            return _get_newsapi_data(symbol, limit, start_date, end_date)
        elif provider == "alpha_vantage":
            return _get_alpha_vantage_news(symbol, limit)
        else:
            raise HTTPException(status_code=400, detail="不支持的数据提供商")

    try:
        data = get_cached_or_fetch(cache_key, fetch_data)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取新闻失败: {str(e)}")


@app.get("/api/v1/economy/gdp")
async def get_gdp_data(
    country: str = Query("US", description="国家代码"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    provider: str = Query("fred", description="数据提供商: fred")
):
    """获取GDP数据"""
    cache_key = f"gdp_{country}_{start_date}_{end_date}_{provider}"

    def fetch_data():
        if provider == "fred":
            return _get_fred_gdp(country, start_date, end_date)
        else:
            raise HTTPException(status_code=400, detail="不支持的数据提供商")

    try:
        data = get_cached_or_fetch(cache_key, fetch_data)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取GDP数据失败: {str(e)}")


@app.get("/api/v1/crypto/price/historical")
async def get_crypto_historical(
    symbol: str = Query(..., description="加密货币代码，如BTC-USD"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    provider: str = Query("yahoo", description="数据提供商: yahoo, alpha_vantage")
):
    """获取加密货币历史数据"""
    cache_key = f"crypto_{symbol}_{start_date}_{end_date}_{provider}"

    def fetch_data():
        if provider == "yahoo":
            return _get_yahoo_crypto(symbol, start_date, end_date)
        elif provider == "alpha_vantage":
            return _get_alpha_vantage_crypto(symbol, start_date, end_date)
        else:
            raise HTTPException(status_code=400, detail="不支持的数据提供商")

    try:
        data = get_cached_or_fetch(cache_key, fetch_data)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取加密货币数据失败: {str(e)}")


@app.get("/api/v1/technical/sma")
async def get_sma_indicator(
    symbol: str = Query(..., description="股票代码"),
    window: int = Query(20, description="移动平均窗口"),
    provider: str = Query("alpha_vantage", description="数据提供商")
):
    """获取简单移动平均线"""
    cache_key = f"sma_{symbol}_{window}_{provider}"

    def fetch_data():
        if provider == "alpha_vantage":
            return _get_alpha_vantage_sma(symbol, window)
        else:
            raise HTTPException(status_code=400, detail="不支持的数据提供商")

    try:
        data = get_cached_or_fetch(cache_key, fetch_data)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取技术指标失败: {str(e)}")


# ============================================================================
# 数据获取实现函数
# ============================================================================

def _get_yahoo_historical(symbol: str, start_date: str, end_date: str):
    """使用Yahoo Finance获取历史数据"""
    try:
        import yfinance as yf

        # 设置默认日期
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
        if not end_date:
            end_date = datetime.now().strftime("%Y-%m-%d")

        ticker = yf.Ticker(symbol)
        data = ticker.history(start=start_date, end=end_date)

        if data.empty:
            return {"results": [], "provider": "yahoo_finance", "message": "无数据"}

        results = []
        for date, row in data.iterrows():
            results.append({
                "date": date.strftime("%Y-%m-%d"),
                "open": float(row["Open"]),
                "high": float(row["High"]),
                "low": float(row["Low"]),
                "close": float(row["Close"]),
                "volume": int(row["Volume"]),
                "adj_close": float(row.get("Adj Close", row["Close"]))
            })

        return {
            "results": results,
            "provider": "yahoo_finance",
            "symbol": symbol,
            "count": len(results)
        }

    except ImportError:
        raise HTTPException(status_code=500, detail="yfinance包未安装")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Yahoo Finance错误: {str(e)}")


def _get_yahoo_quotes(symbols: List[str]):
    """使用Yahoo Finance获取实时报价"""
    try:
        import yfinance as yf

        results = []
        for symbol in symbols:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period="2d")

            if not hist.empty:
                current_price = float(hist['Close'].iloc[-1])
                prev_close = float(hist['Close'].iloc[-2]) if len(hist) > 1 else current_price
                change = current_price - prev_close
                change_percent = (change / prev_close) * 100 if prev_close != 0 else 0

                results.append({
                    "symbol": symbol,
                    "price": current_price,
                    "change": change,
                    "change_percent": change_percent,
                    "volume": int(hist['Volume'].iloc[-1]),
                    "market_cap": info.get('marketCap'),
                    "pe_ratio": info.get('trailingPE'),
                    "name": info.get('longName', symbol)
                })

        return {
            "results": results,
            "provider": "yahoo_finance",
            "count": len(results)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Yahoo Finance错误: {str(e)}")


def _get_alpha_vantage_historical(symbol: str, start_date: str, end_date: str):
    """使用Alpha Vantage获取历史数据"""
    try:
        url = "https://www.alphavantage.co/query"
        params = {
            "function": "TIME_SERIES_DAILY",
            "symbol": symbol,
            "apikey": API_KEYS["alpha_vantage"],
            "outputsize": "full"
        }

        response = requests.get(url, params=params, timeout=10)
        data = response.json()

        if "Error Message" in data:
            raise HTTPException(status_code=404, detail="股票代码未找到")

        if "Note" in data:
            raise HTTPException(status_code=429, detail="API调用频率限制")

        time_series = data.get("Time Series (Daily)", {})

        results = []
        for date_str, values in time_series.items():
            date_obj = datetime.strptime(date_str, "%Y-%m-%d")

            # 过滤日期范围
            if start_date and date_obj < datetime.strptime(start_date, "%Y-%m-%d"):
                continue
            if end_date and date_obj > datetime.strptime(end_date, "%Y-%m-%d"):
                continue

            results.append({
                "date": date_str,
                "open": float(values["1. open"]),
                "high": float(values["2. high"]),
                "low": float(values["3. low"]),
                "close": float(values["4. close"]),
                "volume": int(values["5. volume"]),
                "adj_close": float(values["5. adjusted close"]) if "5. adjusted close" in values else float(values["4. close"])
            })

        # 按日期排序
        results.sort(key=lambda x: x["date"])

        return {
            "results": results,
            "provider": "alpha_vantage",
            "symbol": symbol,
            "count": len(results)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Alpha Vantage错误: {str(e)}")


def _get_alpha_vantage_quotes(symbols: List[str]):
    """使用Alpha Vantage获取实时报价"""
    try:
        results = []

        for symbol in symbols:
            url = "https://www.alphavantage.co/query"
            params = {
                "function": "GLOBAL_QUOTE",
                "symbol": symbol,
                "apikey": API_KEYS["alpha_vantage"]
            }

            response = requests.get(url, params=params, timeout=10)
            data = response.json()

            if "Global Quote" in data:
                quote = data["Global Quote"]

                results.append({
                    "symbol": quote.get("01. symbol", symbol),
                    "price": float(quote.get("05. price", 0)),
                    "change": float(quote.get("09. change", 0)),
                    "change_percent": float(quote.get("10. change percent", "0%").replace("%", "")),
                    "volume": int(quote.get("06. volume", 0)),
                    "market_cap": None,  # Alpha Vantage不提供市值
                    "pe_ratio": None,    # Alpha Vantage不提供PE比率
                    "name": symbol
                })

            # 避免API频率限制
            time.sleep(0.2)

        return {
            "results": results,
            "provider": "alpha_vantage",
            "count": len(results)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Alpha Vantage错误: {str(e)}")


def _get_alpha_vantage_income(symbol: str, period: str, limit: int):
    """使用Alpha Vantage获取利润表"""
    try:
        url = "https://www.alphavantage.co/query"
        params = {
            "function": "INCOME_STATEMENT",
            "symbol": symbol,
            "apikey": API_KEYS["alpha_vantage"]
        }

        response = requests.get(url, params=params, timeout=10)
        data = response.json()

        if "Error Message" in data:
            raise HTTPException(status_code=404, detail="股票代码未找到")

        # 选择年度或季度报告
        reports_key = "annualReports" if period == "annual" else "quarterlyReports"
        reports = data.get(reports_key, [])

        results = []
        for report in reports[:limit]:
            results.append({
                "symbol": symbol,
                "date": report.get("fiscalDateEnding"),
                "revenue": float(report.get("totalRevenue", 0)) if report.get("totalRevenue") != "None" else 0,
                "operating_income": float(report.get("operatingIncome", 0)) if report.get("operatingIncome") != "None" else 0,
                "net_income": float(report.get("netIncome", 0)) if report.get("netIncome") != "None" else 0,
                "eps": float(report.get("reportedEPS", 0)) if report.get("reportedEPS") != "None" else 0
            })

        return {
            "results": results,
            "provider": "alpha_vantage",
            "symbol": symbol,
            "period": period,
            "count": len(results)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Alpha Vantage错误: {str(e)}")


def _get_alpha_vantage_balance(symbol: str, period: str, limit: int):
    """使用Alpha Vantage获取资产负债表"""
    try:
        url = "https://www.alphavantage.co/query"
        params = {
            "function": "BALANCE_SHEET",
            "symbol": symbol,
            "apikey": API_KEYS["alpha_vantage"]
        }

        response = requests.get(url, params=params, timeout=10)
        data = response.json()

        if "Error Message" in data:
            raise HTTPException(status_code=404, detail="股票代码未找到")

        reports_key = "annualReports" if period == "annual" else "quarterlyReports"
        reports = data.get(reports_key, [])

        results = []
        for report in reports[:limit]:
            results.append({
                "symbol": symbol,
                "date": report.get("fiscalDateEnding"),
                "total_assets": float(report.get("totalAssets", 0)) if report.get("totalAssets") != "None" else 0,
                "total_liabilities": float(report.get("totalLiabilities", 0)) if report.get("totalLiabilities") != "None" else 0,
                "shareholders_equity": float(report.get("totalShareholderEquity", 0)) if report.get("totalShareholderEquity") != "None" else 0,
                "current_assets": float(report.get("totalCurrentAssets", 0)) if report.get("totalCurrentAssets") != "None" else 0,
                "current_liabilities": float(report.get("totalCurrentLiabilities", 0)) if report.get("totalCurrentLiabilities") != "None" else 0
            })

        return {
            "results": results,
            "provider": "alpha_vantage",
            "symbol": symbol,
            "period": period,
            "count": len(results)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Alpha Vantage错误: {str(e)}")


def _get_alpha_vantage_cash_flow(symbol: str, period: str, limit: int):
    """使用Alpha Vantage获取现金流量表"""
    try:
        url = "https://www.alphavantage.co/query"
        params = {
            "function": "CASH_FLOW",
            "symbol": symbol,
            "apikey": API_KEYS["alpha_vantage"]
        }

        response = requests.get(url, params=params, timeout=10)
        data = response.json()

        if "Error Message" in data:
            raise HTTPException(status_code=404, detail="股票代码未找到")

        reports_key = "annualReports" if period == "annual" else "quarterlyReports"
        reports = data.get(reports_key, [])

        results = []
        for report in reports[:limit]:
            results.append({
                "symbol": symbol,
                "date": report.get("fiscalDateEnding"),
                "operating_cash_flow": float(report.get("operatingCashflow", 0)) if report.get("operatingCashflow") != "None" else 0,
                "investing_cash_flow": float(report.get("cashflowFromInvestment", 0)) if report.get("cashflowFromInvestment") != "None" else 0,
                "financing_cash_flow": float(report.get("cashflowFromFinancing", 0)) if report.get("cashflowFromFinancing") != "None" else 0,
                "net_cash_flow": float(report.get("changeInCashAndCashEquivalents", 0)) if report.get("changeInCashAndCashEquivalents") != "None" else 0
            })

        return {
            "results": results,
            "provider": "alpha_vantage",
            "symbol": symbol,
            "period": period,
            "count": len(results)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Alpha Vantage错误: {str(e)}")


def _get_newsapi_data(symbol: str, limit: int, start_date: str, end_date: str):
    """使用NewsAPI获取新闻"""
    try:
        url = "https://newsapi.org/v2/everything"

        # 构建查询词
        query = f"{symbol} stock" if symbol else "stock market finance"

        params = {
            "q": query,
            "language": "en",
            "sortBy": "publishedAt",
            "pageSize": min(limit, 100),  # NewsAPI限制
            "apiKey": API_KEYS["newsapi"]
        }

        if start_date:
            params["from"] = start_date
        if end_date:
            params["to"] = end_date

        response = requests.get(url, params=params, timeout=10)
        data = response.json()

        if data.get("status") != "ok":
            raise HTTPException(status_code=400, detail=data.get("message", "NewsAPI错误"))

        results = []
        for article in data.get("articles", []):
            results.append({
                "title": article.get("title"),
                "content": article.get("description"),
                "url": article.get("url"),
                "published_date": article.get("publishedAt"),
                "source": article.get("source", {}).get("name"),
                "author": article.get("author")
            })

        return {
            "results": results,
            "provider": "newsapi",
            "query": query,
            "count": len(results)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"NewsAPI错误: {str(e)}")


def _get_alpha_vantage_news(symbol: str, limit: int):
    """使用Alpha Vantage获取新闻"""
    try:
        url = "https://www.alphavantage.co/query"
        params = {
            "function": "NEWS_SENTIMENT",
            "tickers": symbol,
            "limit": limit,
            "apikey": API_KEYS["alpha_vantage"]
        }

        response = requests.get(url, params=params, timeout=10)
        data = response.json()

        if "Error Message" in data:
            raise HTTPException(status_code=404, detail="获取新闻失败")

        results = []
        for article in data.get("feed", []):
            results.append({
                "title": article.get("title"),
                "content": article.get("summary"),
                "url": article.get("url"),
                "published_date": article.get("time_published"),
                "source": article.get("source"),
                "sentiment": article.get("overall_sentiment_label")
            })

        return {
            "results": results,
            "provider": "alpha_vantage",
            "symbol": symbol,
            "count": len(results)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Alpha Vantage错误: {str(e)}")


def _get_fred_gdp(country: str, start_date: str, end_date: str):
    """使用FRED获取GDP数据"""
    try:
        # FRED系列ID映射
        series_map = {
            "US": "GDP",
            "CN": "CHNGDPNQDSMEI",  # 中国GDP
            "JP": "JPNRGDPEXP",     # 日本GDP
            "DE": "CLVMNACSCAB1GQDE", # 德国GDP
            "UK": "CLVMNACSCAB1GQUK"  # 英国GDP
        }

        series_id = series_map.get(country.upper(), "GDP")

        url = "https://api.stlouisfed.org/fred/series/observations"
        params = {
            "series_id": series_id,
            "api_key": API_KEYS["fred"],
            "file_type": "json"
        }

        if start_date:
            params["observation_start"] = start_date
        if end_date:
            params["observation_end"] = end_date

        response = requests.get(url, params=params, timeout=10)
        data = response.json()

        if "error_message" in data:
            raise HTTPException(status_code=400, detail=data["error_message"])

        results = []
        for obs in data.get("observations", []):
            if obs["value"] != ".":  # FRED用"."表示无数据
                results.append({
                    "date": obs["date"],
                    "value": float(obs["value"]),
                    "country": country.upper()
                })

        return {
            "results": results,
            "provider": "fred",
            "country": country,
            "series_id": series_id,
            "count": len(results)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"FRED错误: {str(e)}")


def _get_yahoo_crypto(symbol: str, start_date: str, end_date: str):
    """使用Yahoo Finance获取加密货币数据"""
    try:
        import yfinance as yf

        # 确保加密货币代码格式正确
        if not symbol.endswith("-USD"):
            symbol = f"{symbol}-USD"

        ticker = yf.Ticker(symbol)
        data = ticker.history(start=start_date, end=end_date)

        if data.empty:
            return {"results": [], "provider": "yahoo_finance", "message": "无数据"}

        results = []
        for date, row in data.iterrows():
            results.append({
                "date": date.strftime("%Y-%m-%d"),
                "open": float(row["Open"]),
                "high": float(row["High"]),
                "low": float(row["Low"]),
                "close": float(row["Close"]),
                "volume": int(row["Volume"])
            })

        return {
            "results": results,
            "provider": "yahoo_finance",
            "symbol": symbol,
            "count": len(results)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Yahoo Finance错误: {str(e)}")


def _get_alpha_vantage_crypto(symbol: str, start_date: str, end_date: str):
    """使用Alpha Vantage获取加密货币数据"""
    try:
        # 提取基础货币代码
        base_symbol = symbol.split("-")[0] if "-" in symbol else symbol

        url = "https://www.alphavantage.co/query"
        params = {
            "function": "DIGITAL_CURRENCY_DAILY",
            "symbol": base_symbol,
            "market": "USD",
            "apikey": API_KEYS["alpha_vantage"]
        }

        response = requests.get(url, params=params, timeout=10)
        data = response.json()

        if "Error Message" in data:
            raise HTTPException(status_code=404, detail="加密货币代码未找到")

        time_series = data.get("Time Series (Digital Currency Daily)", {})

        results = []
        for date_str, values in time_series.items():
            date_obj = datetime.strptime(date_str, "%Y-%m-%d")

            # 过滤日期范围
            if start_date and date_obj < datetime.strptime(start_date, "%Y-%m-%d"):
                continue
            if end_date and date_obj > datetime.strptime(end_date, "%Y-%m-%d"):
                continue

            results.append({
                "date": date_str,
                "open": float(values["1a. open (USD)"]),
                "high": float(values["2a. high (USD)"]),
                "low": float(values["3a. low (USD)"]),
                "close": float(values["4a. close (USD)"]),
                "volume": float(values["5. volume"])
            })

        # 按日期排序
        results.sort(key=lambda x: x["date"])

        return {
            "results": results,
            "provider": "alpha_vantage",
            "symbol": symbol,
            "count": len(results)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Alpha Vantage错误: {str(e)}")


def _get_alpha_vantage_sma(symbol: str, window: int):
    """使用Alpha Vantage获取简单移动平均线"""
    try:
        url = "https://www.alphavantage.co/query"
        params = {
            "function": "SMA",
            "symbol": symbol,
            "interval": "daily",
            "time_period": window,
            "series_type": "close",
            "apikey": API_KEYS["alpha_vantage"]
        }

        response = requests.get(url, params=params, timeout=10)
        data = response.json()

        if "Error Message" in data:
            raise HTTPException(status_code=404, detail="股票代码未找到")

        technical_analysis = data.get("Technical Analysis: SMA", {})

        results = []
        for date_str, values in technical_analysis.items():
            results.append({
                "date": date_str,
                "sma": float(values["SMA"])
            })

        # 按日期排序
        results.sort(key=lambda x: x["date"])

        return {
            "results": results,
            "provider": "alpha_vantage",
            "symbol": symbol,
            "window": window,
            "count": len(results)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Alpha Vantage错误: {str(e)}")


# ============================================================================
# 启动服务器
# ============================================================================

if __name__ == "__main__":
    import uvicorn

    print("🚀 启动OpenBB免费API补充服务器")
    print("=" * 50)
    print("📋 服务信息:")
    print("- 地址: http://127.0.0.1:6902")
    print("- 文档: http://127.0.0.1:6902/docs")
    print("- 健康检查: http://127.0.0.1:6902/health")
    print()
    print("🔑 支持的免费API:")
    print("- Yahoo Finance (无需密钥)")
    print("- Alpha Vantage (需要密钥)")
    print("- NewsAPI (需要密钥)")
    print("- FRED (需要密钥)")
    print()
    print("💡 配置API密钥以获得更好的数据质量")
    print("=" * 50)

    uvicorn.run(
        app,
        host="127.0.0.1",
        port=6902,
        log_level="info"
    )
