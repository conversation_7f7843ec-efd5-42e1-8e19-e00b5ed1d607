#!/usr/bin/env python3
"""
股票列表数据 - 备用搜索数据源
当Tushare权限不足时使用
"""

import pandas as pd

# 常用股票列表
STOCK_DATA = [
    # 银行股
    {"ts_code": "000001.SZ", "name": "平安银行", "industry": "银行", "area": "广东"},
    {"ts_code": "600036.SH", "name": "招商银行", "industry": "银行", "area": "广东"},
    {"ts_code": "601166.SH", "name": "兴业银行", "industry": "银行", "area": "福建"},
    {"ts_code": "600000.SH", "name": "浦发银行", "industry": "银行", "area": "上海"},
    {"ts_code": "601328.SH", "name": "交通银行", "industry": "银行", "area": "上海"},
    {"ts_code": "601398.SH", "name": "工商银行", "industry": "银行", "area": "北京"},
    {"ts_code": "601939.SH", "name": "建设银行", "industry": "银行", "area": "北京"},
    {"ts_code": "601988.SH", "name": "中国银行", "industry": "银行", "area": "北京"},
    
    # 白酒股
    {"ts_code": "600519.SH", "name": "贵州茅台", "industry": "白酒", "area": "贵州"},
    {"ts_code": "000858.SZ", "name": "五粮液", "industry": "白酒", "area": "四川"},
    {"ts_code": "000568.SZ", "name": "泸州老窖", "industry": "白酒", "area": "四川"},
    {"ts_code": "002304.SZ", "name": "洋河股份", "industry": "白酒", "area": "江苏"},
    {"ts_code": "600809.SH", "name": "山西汾酒", "industry": "白酒", "area": "山西"},
    {"ts_code": "000596.SZ", "name": "古井贡酒", "industry": "白酒", "area": "安徽"},
    
    # 科技股
    {"ts_code": "000002.SZ", "name": "万科A", "industry": "房地产", "area": "广东"},
    {"ts_code": "000858.SZ", "name": "五粮液", "industry": "白酒", "area": "四川"},
    {"ts_code": "002415.SZ", "name": "海康威视", "industry": "安防设备", "area": "浙江"},
    {"ts_code": "300059.SZ", "name": "东方财富", "industry": "互联网金融", "area": "上海"},
    {"ts_code": "300750.SZ", "name": "宁德时代", "industry": "电池", "area": "福建"},
    
    # 钢铁股
    {"ts_code": "600507.SH", "name": "方大特钢", "industry": "钢铁", "area": "江西"},
    {"ts_code": "000709.SZ", "name": "河钢股份", "industry": "钢铁", "area": "河北"},
    {"ts_code": "600019.SH", "name": "宝钢股份", "industry": "钢铁", "area": "上海"},
    {"ts_code": "000825.SZ", "name": "太钢不锈", "industry": "钢铁", "area": "山西"},
    
    # 汽车股
    {"ts_code": "002594.SZ", "name": "比亚迪", "industry": "汽车制造", "area": "广东"},
    {"ts_code": "600104.SH", "name": "上汽集团", "industry": "汽车制造", "area": "上海"},
    {"ts_code": "000625.SZ", "name": "长安汽车", "industry": "汽车制造", "area": "重庆"},
    
    # 医药股
    {"ts_code": "000661.SZ", "name": "长春高新", "industry": "生物制药", "area": "吉林"},
    {"ts_code": "300015.SZ", "name": "爱尔眼科", "industry": "医疗服务", "area": "湖南"},
    {"ts_code": "600276.SH", "name": "恒瑞医药", "industry": "化学制药", "area": "江苏"},
    
    # 保险股
    {"ts_code": "601318.SH", "name": "中国平安", "industry": "保险", "area": "广东"},
    {"ts_code": "601601.SH", "name": "中国太保", "industry": "保险", "area": "上海"},
    {"ts_code": "601628.SH", "name": "中国人寿", "industry": "保险", "area": "北京"},
    
    # 券商股
    {"ts_code": "000166.SZ", "name": "申万宏源", "industry": "证券", "area": "上海"},
    {"ts_code": "600030.SH", "name": "中信证券", "industry": "证券", "area": "北京"},
    {"ts_code": "000776.SZ", "name": "广发证券", "industry": "证券", "area": "广东"},
    
    # 地产股
    {"ts_code": "000002.SZ", "name": "万科A", "industry": "房地产", "area": "广东"},
    {"ts_code": "000069.SZ", "name": "华侨城A", "industry": "房地产", "area": "广东"},
    {"ts_code": "600048.SH", "name": "保利发展", "industry": "房地产", "area": "广东"},
    
    # 家电股
    {"ts_code": "000333.SZ", "name": "美的集团", "industry": "家用电器", "area": "广东"},
    {"ts_code": "000651.SZ", "name": "格力电器", "industry": "家用电器", "area": "广东"},
    {"ts_code": "002032.SZ", "name": "苏泊尔", "industry": "家用电器", "area": "浙江"},
    
    # 食品饮料
    {"ts_code": "600887.SH", "name": "伊利股份", "industry": "乳制品", "area": "内蒙古"},
    {"ts_code": "000895.SZ", "name": "双汇发展", "industry": "肉制品", "area": "河南"},
    {"ts_code": "600600.SH", "name": "青岛啤酒", "industry": "啤酒", "area": "山东"},
    
    # 石油化工
    {"ts_code": "600028.SH", "name": "中国石化", "industry": "石油化工", "area": "北京"},
    {"ts_code": "601857.SH", "name": "中国石油", "industry": "石油开采", "area": "北京"},
    {"ts_code": "600346.SH", "name": "恒力石化", "industry": "石油化工", "area": "江苏"},
]


def get_stock_dataframe() -> pd.DataFrame:
    """获取股票数据DataFrame"""
    df = pd.DataFrame(STOCK_DATA)
    df["list_date"] = "20100101"  # 默认上市日期
    return df


def search_stocks(query: str, limit: int = 20) -> pd.DataFrame:
    """
    搜索股票
    
    Args:
        query: 搜索关键词
        limit: 返回结果数量限制
        
    Returns:
        匹配的股票DataFrame
    """
    df = get_stock_dataframe()
    
    if not query:
        return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])
    
    query = str(query).strip()
    results = []
    
    # 1. 精确代码匹配（6位数字）
    if query.isdigit() and len(query) == 6:
        # 尝试匹配完整代码
        for suffix in ['.SH', '.SZ']:
            code_with_suffix = query + suffix
            exact_match = df[df["ts_code"] == code_with_suffix]
            if not exact_match.empty:
                results.append(exact_match)
    
    # 2. 完整代码匹配
    elif '.' in query:
        exact_match = df[df["ts_code"].str.upper() == query.upper()]
        if not exact_match.empty:
            results.append(exact_match)
    
    # 3. 代码模糊匹配
    code_fuzzy = df[df["ts_code"].str.contains(query, case=False, na=False)]
    if not code_fuzzy.empty:
        results.append(code_fuzzy)
    
    # 4. 名称匹配
    name_match = df[df["name"].str.contains(query, na=False)]
    if not name_match.empty:
        results.append(name_match)
    
    # 5. 行业匹配
    if len(query) >= 2:
        industry_match = df[df["industry"].str.contains(query, na=False)]
        if not industry_match.empty:
            results.append(industry_match)
    
    # 合并结果并去重
    if results:
        combined = pd.concat(results, ignore_index=True)
        combined = combined.drop_duplicates(subset=['ts_code'], keep='first')
        
        # 排序：精确匹配优先
        def sort_key(row):
            ts_code = row['ts_code']
            name = row['name']
            
            # 精确匹配得分最高
            if (query.isdigit() and len(query) == 6 and ts_code.startswith(query)) or \
               (ts_code.upper() == query.upper()) or (name == query):
                return (0, ts_code)
            # 名称匹配次之
            elif name.startswith(query):
                return (1, ts_code)
            # 其他匹配最后
            else:
                return (2, ts_code)
        
        combined['sort_key'] = combined.apply(sort_key, axis=1)
        combined = combined.sort_values('sort_key').drop('sort_key', axis=1)
        
        return combined.head(limit)
    
    return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])


if __name__ == "__main__":
    # 测试搜索功能
    test_queries = ["600507", "平安银行", "000001", "茅台", "钢铁"]
    
    for query in test_queries:
        print(f"\n搜索: {query}")
        result = search_stocks(query, 3)
        if not result.empty:
            for _, row in result.iterrows():
                print(f"  {row['ts_code']} {row['name']} {row['industry']}")
        else:
            print("  无结果")
