#!/usr/bin/env python3
"""
测试改进后的股票搜索功能
验证去除后缀和自动加载功能
"""

import requests
import time


def test_search_api():
    """测试搜索API"""
    print("🔍 测试搜索API功能")
    print("=" * 50)
    
    test_cases = [
        ("平安银行", "中文名称搜索"),
        ("000001", "6位代码搜索"),
        ("贵州茅台", "热门股票名称"),
        ("600519", "上海股票代码"),
        ("五粮液", "消费股搜索"),
        ("000858", "深圳股票代码"),
    ]
    
    for query, desc in test_cases:
        print(f"\n📊 测试: {query} ({desc})")
        
        try:
            response = requests.get(
                f"http://127.0.0.1:6900/search",
                params={"query": query},
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                if data:
                    print(f"✅ 搜索成功，找到 {len(data)} 个结果")
                    for i, item in enumerate(data[:3]):  # 显示前3个结果
                        ts_code = item.get('ts_code', '')
                        name = item.get('name', '')
                        # 显示去除后缀的代码
                        display_code = ts_code.replace('.SH', '').replace('.SZ', '')
                        print(f"  {i+1}. {display_code} {name}")
                else:
                    print("❌ 未找到结果")
            else:
                print(f"❌ API调用失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        time.sleep(0.5)  # 避免请求过快


def test_historical_data():
    """测试历史数据API"""
    print("\n📈 测试历史数据API")
    print("=" * 50)
    
    test_symbols = ["000001.SZ", "600519.SH", "000858.SZ"]
    
    for symbol in test_symbols:
        display_code = symbol.replace('.SH', '').replace('.SZ', '')
        print(f"\n📊 测试股票: {display_code} (实际代码: {symbol})")
        
        try:
            response = requests.get(
                f"http://127.0.0.1:6900/equity/price/historical/data",
                params={"symbol": symbol},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data:
                    print(f"✅ 获取到 {len(data)} 条历史数据")
                    latest = data[-1] if data else None
                    if latest:
                        print(f"  最新数据: {latest.get('trade_date')} 收盘价: {latest.get('close')}")
                else:
                    print("❌ 无历史数据")
            else:
                print(f"❌ API调用失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        time.sleep(0.5)


def check_ui_features():
    """检查UI功能"""
    print("\n🌐 UI功能检查")
    print("=" * 50)
    
    urls = [
        ("http://127.0.0.1:6900/", "首页"),
        ("http://127.0.0.1:6900/ui", "股票搜索界面"),
        ("http://127.0.0.1:6900/docs", "API文档"),
    ]
    
    for url, name in urls:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: 可访问")
            else:
                print(f"❌ {name}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {name}: 连接失败 - {e}")


def show_usage_guide():
    """显示使用指南"""
    print("\n📋 改进功能使用指南")
    print("=" * 60)
    
    print("🎉 主要改进:")
    print("1. ✅ 去除股票代码后缀显示 (.SH/.SZ)")
    print("2. ✅ 删除加载按钮，搜索即自动加载")
    print("3. ✅ 支持6位代码直接搜索")
    print("4. ✅ 实时搜索，输入即反馈")
    print("5. ✅ 智能搜索排序和匹配")
    
    print("\n🔍 搜索功能:")
    print("- 输入股票名称: 平安银行、贵州茅台、五粮液")
    print("- 输入6位代码: 000001、600519、000858")
    print("- 系统自动匹配交易所(.SH/.SZ)")
    print("- 搜索结果自动加载第一个匹配项")
    
    print("\n💡 界面优化:")
    print("- 搜索框提示: 无需输入.SH/.SZ后缀")
    print("- 实时搜索: 输入2个字符即开始搜索")
    print("- 智能防抖: 避免频繁请求")
    print("- 状态提示: 搜索中、加载中、错误提示")
    
    print("\n🧪 测试建议:")
    test_examples = [
        ("平安银行", "中文名称 → 自动匹配000001"),
        ("000001", "6位代码 → 自动添加.SZ"),
        ("贵州茅台", "热门股票 → 自动匹配600519"),
        ("600519", "上海代码 → 自动添加.SH"),
        ("五粮液", "消费股 → 自动匹配000858"),
        ("000858", "深圳代码 → 自动添加.SZ"),
    ]
    
    for example, desc in test_examples:
        print(f"  📝 {example:<8} - {desc}")
    
    print("\n🌐 访问地址:")
    print("- 📊 股票搜索: http://127.0.0.1:6900/ui")
    print("- 🏠 首页: http://127.0.0.1:6900/")
    print("- 📋 API文档: http://127.0.0.1:6900/docs")


def main():
    """主函数"""
    print("🚀 改进功能测试工具")
    print("=" * 60)
    
    # 1. 检查UI可访问性
    check_ui_features()
    
    # 2. 测试搜索API
    test_search_api()
    
    # 3. 测试历史数据
    test_historical_data()
    
    # 4. 显示使用指南
    show_usage_guide()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("\n💡 下一步:")
    print("1. 打开浏览器访问: http://127.0.0.1:6900/ui")
    print("2. 尝试输入股票名称或6位代码")
    print("3. 观察自动搜索和加载效果")
    print("4. 验证显示的代码已去除.SH/.SZ后缀")
    
    print("\n🔧 如果遇到问题:")
    print("- 确保服务正在运行 (端口6900)")
    print("- 检查网络连接")
    print("- 查看浏览器控制台错误信息")
    print("- 尝试刷新页面 (Ctrl+F5)")


if __name__ == "__main__":
    main()
