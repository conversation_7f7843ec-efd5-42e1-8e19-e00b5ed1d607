#!/usr/bin/env python3
"""
OpenBB创新技术指标测试演示
完整测试所有5个创新指标的功能和效果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import requests
from datetime import datetime, timedelta
from 创新技术指标 import InnovativeIndicators

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


def generate_sample_data(days=365):
    """生成样本数据用于测试"""
    print("📊 生成样本股票数据...")
    
    dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
    
    # 生成模拟股价数据
    np.random.seed(42)
    returns = np.random.normal(0.001, 0.02, days)
    
    # 添加趋势和周期性
    trend = np.linspace(0, 0.3, days)
    cycle = 0.1 * np.sin(np.linspace(0, 4*np.pi, days))
    
    adjusted_returns = returns + trend/days + cycle/days
    
    prices = 100 * np.cumprod(1 + adjusted_returns)
    
    # 生成OHLC数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        volatility = 0.02
        high = close * (1 + np.random.uniform(0, volatility))
        low = close * (1 - np.random.uniform(0, volatility))
        
        if i == 0:
            open_price = close
        else:
            open_price = prices[i-1] * (1 + np.random.uniform(-0.01, 0.01))
        
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        volume = np.random.randint(1000000, 10000000)
        
        data.append({
            'date': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('date', inplace=True)
    
    print(f"✅ 生成了 {len(df)} 天的样本数据")
    return df


def test_all_indicators():
    """测试所有创新指标"""
    print("🚀 OpenBB创新技术指标测试")
    print("=" * 60)
    
    # 生成测试数据
    data = generate_sample_data(365)
    
    # 初始化指标计算器
    indicators_calc = InnovativeIndicators(data)
    
    # 计算所有指标
    print("\n🔄 计算所有创新指标...")
    indicators = indicators_calc.calculate_all_indicators()
    
    # 生成交易信号
    print("\n📊 生成交易信号...")
    signals = indicators_calc.generate_signals(indicators)
    
    # 显示指标统计
    print("\n📈 指标统计信息:")
    print("=" * 60)
    
    for name, indicator in indicators.items():
        if len(indicator) > 0:
            print(f"\n{name}:")
            print(f"  当前值: {indicator.iloc[-1]:.2f}")
            print(f"  最大值: {indicator.max():.2f}")
            print(f"  最小值: {indicator.min():.2f}")
            print(f"  平均值: {indicator.mean():.2f}")
            print(f"  标准差: {indicator.std():.2f}")
    
    # 显示信号统计
    print("\n📊 交易信号统计:")
    print("=" * 60)
    
    signal_counts = signals['Final_Signal'].value_counts()
    print(f"买入信号: {signal_counts.get('BUY', 0)} 次")
    print(f"卖出信号: {signal_counts.get('SELL', 0)} 次")
    print(f"持有信号: {signal_counts.get('HOLD', 0)} 次")
    
    print(f"\n最新综合信号: {signals['Final_Signal'].iloc[-1]}")
    print(f"信号强度: {signals['Composite_Signal'].iloc[-1]:.3f}")
    
    # 绘制图表
    print("\n📊 生成可视化图表...")
    fig = indicators_calc.plot_indicators(indicators)
    plt.savefig('创新指标分析图.png', dpi=300, bbox_inches='tight')
    print("✅ 图表已保存为 '创新指标分析图.png'")
    
    # 回测分析
    print("\n📈 简单回测分析...")
    backtest_results = simple_backtest(data, signals)
    
    return indicators, signals, backtest_results


def simple_backtest(price_data, signals, initial_capital=100000):
    """简单回测分析"""
    
    portfolio_value = []
    position = 0  # 0: 空仓, 1: 持仓
    cash = initial_capital
    shares = 0
    
    for i, (date, signal) in enumerate(signals['Final_Signal'].items()):
        price = price_data.loc[date, 'close']
        
        if signal == 'BUY' and position == 0:
            # 买入
            shares = cash / price
            cash = 0
            position = 1
        elif signal == 'SELL' and position == 1:
            # 卖出
            cash = shares * price
            shares = 0
            position = 0
        
        # 计算当前组合价值
        current_value = cash + shares * price
        portfolio_value.append(current_value)
    
    # 计算收益率
    total_return = (portfolio_value[-1] - initial_capital) / initial_capital * 100
    buy_hold_return = (price_data['close'].iloc[-1] - price_data['close'].iloc[0]) / price_data['close'].iloc[0] * 100
    
    print(f"策略总收益率: {total_return:.2f}%")
    print(f"买入持有收益率: {buy_hold_return:.2f}%")
    print(f"超额收益: {total_return - buy_hold_return:.2f}%")
    
    return {
        'strategy_return': total_return,
        'buy_hold_return': buy_hold_return,
        'excess_return': total_return - buy_hold_return,
        'portfolio_values': portfolio_value
    }


def test_real_stock_data():
    """测试真实股票数据"""
    print("\n🔍 测试真实股票数据...")
    
    # 尝试获取真实数据
    try:
        response = requests.get(
            "http://127.0.0.1:6901/api/v1/astock/equity/price/historical",
            params={
                "symbol": "000001",
                "start_date": (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d"),
                "end_date": datetime.now().strftime("%Y-%m-%d")
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            if 'results' in data and data['results']:
                df = pd.DataFrame(data['results'])
                df['date'] = pd.to_datetime(df['date'])
                df.set_index('date', inplace=True)
                
                print("✅ 成功获取000001(平安银行)真实数据")
                
                # 计算指标
                indicators_calc = InnovativeIndicators(df)
                indicators = indicators_calc.calculate_all_indicators()
                signals = indicators_calc.generate_signals(indicators)
                
                print(f"最新价格: ¥{df['close'].iloc[-1]:.2f}")
                print(f"最新信号: {signals['Final_Signal'].iloc[-1]}")
                
                return df, indicators, signals
        
    except Exception as e:
        print(f"⚠️ 无法获取真实数据: {e}")
    
    print("使用模拟数据进行测试")
    return None, None, None


def generate_indicator_report(indicators, signals):
    """生成指标分析报告"""
    print("\n📋 生成创新指标分析报告...")
    
    report = []
    report.append("# OpenBB创新技术指标分析报告")
    report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 指标概述
    report.append("## 指标概述")
    report.append("")
    
    indicator_descriptions = {
        'DMFI': '动态资金流强度指标 - 结合波动率、时间衰减和市场情绪的资金流指标',
        'AISI': '自适应智能情绪指标 - 使用机器学习动态调整权重的市场情绪指标',
        'MVCI': '多维度价值收敛指标 - 结合价格、成交量、波动率的多维度收敛分析',
        'TAFI': '时间自适应趋势指标 - 根据市场状态自动调整计算周期的趋势指标',
        'FMSI': '基本面-技术面综合指标 - 将基本面因子与技术面指标有机结合'
    }
    
    for name, desc in indicator_descriptions.items():
        if name in indicators:
            current_value = indicators[name].iloc[-1]
            report.append(f"### {name}")
            report.append(f"- 描述: {desc}")
            report.append(f"- 当前值: {current_value:.2f}")
            report.append("")
    
    # 交易信号
    report.append("## 交易信号分析")
    report.append("")
    
    latest_signal = signals['Final_Signal'].iloc[-1]
    composite_score = signals['Composite_Signal'].iloc[-1]
    
    report.append(f"- 最新信号: {latest_signal}")
    report.append(f"- 信号强度: {composite_score:.3f}")
    report.append("")
    
    signal_counts = signals['Final_Signal'].value_counts()
    report.append("### 历史信号统计")
    report.append(f"- 买入信号: {signal_counts.get('BUY', 0)} 次")
    report.append(f"- 卖出信号: {signal_counts.get('SELL', 0)} 次")
    report.append(f"- 持有信号: {signal_counts.get('HOLD', 0)} 次")
    
    # 保存报告
    with open('创新指标分析报告.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("✅ 报告已保存为 '创新指标分析报告.md'")


def main():
    """主函数"""
    print("🚀 OpenBB创新技术指标完整测试")
    print("=" * 80)
    
    # 1. 测试所有指标
    indicators, signals, backtest = test_all_indicators()
    
    # 2. 测试真实数据
    real_data, real_indicators, real_signals = test_real_stock_data()
    
    # 3. 生成报告
    if indicators is not None:
        generate_indicator_report(indicators, signals)
    
    print("\n🎉 测试完成!")
    print("\n📁 生成的文件:")
    print("- 创新指标分析图.png (可视化图表)")
    print("- 创新指标分析报告.md (分析报告)")
    
    print("\n💡 使用建议:")
    print("1. 查看生成的图表了解指标表现")
    print("2. 阅读分析报告了解详细信息")
    print("3. 在实际交易中结合多个指标综合判断")
    print("4. 注意风险管理，设置止损点")
    
    print("\n⚠️ 风险提示:")
    print("这些指标仅供参考，不构成投资建议")
    print("请结合基本面分析和风险管理进行投资决策")


if __name__ == "__main__":
    main()
