#!/usr/bin/env python3
"""
OpenBB Platform 演示脚本
展示OpenBB的核心功能和架构，不依赖外部API
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta


def demo_openbb_core():
    """演示OpenBB核心功能"""
    print("🔧 OpenBB Core 功能演示")
    print("=" * 40)
    
    try:
        from openbb_core.app.static.app_factory import create_app
        
        # 创建应用实例
        app = create_app()
        print("✅ OpenBB 应用创建成功")
        
        # 检查应用属性
        print(f"   应用类型: {type(app).__name__}")
        
        # 列出可用的方法和属性
        methods = [method for method in dir(app) if not method.startswith('_')]
        print(f"   可用方法数量: {len(methods)}")
        print(f"   主要方法: {', '.join(methods[:5])}...")
        
        return app
        
    except Exception as e:
        print(f"❌ OpenBB Core 演示失败: {e}")
        return None


def demo_data_structures():
    """演示数据结构和处理"""
    print("\n📊 数据结构演示")
    print("=" * 40)
    
    # 创建模拟股票数据
    dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='D')
    
    # 模拟价格数据
    np.random.seed(42)  # 确保结果可重现
    base_price = 150.0
    returns = np.random.normal(0.001, 0.02, len(dates))  # 日收益率
    
    prices = [base_price]
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # 创建OHLCV数据
    data = pd.DataFrame({
        'Date': dates,
        'Open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
        'High': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'Low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'Close': prices,
        'Volume': np.random.randint(1000000, 5000000, len(dates))
    })
    
    data.set_index('Date', inplace=True)
    
    print("✅ 模拟股票数据创建成功")
    print(f"   数据期间: {data.index[0].strftime('%Y-%m-%d')} 到 {data.index[-1].strftime('%Y-%m-%d')}")
    print(f"   数据点数: {len(data)}")
    print(f"   列名: {', '.join(data.columns)}")
    
    # 显示前几行数据
    print("\n📋 数据预览:")
    print(data.head().round(2))
    
    return data


def demo_technical_analysis(data):
    """演示技术分析功能"""
    print("\n📈 技术分析演示")
    print("=" * 40)
    
    # 计算移动平均线
    data['MA_5'] = data['Close'].rolling(window=5).mean()
    data['MA_10'] = data['Close'].rolling(window=10).mean()
    data['MA_20'] = data['Close'].rolling(window=20).mean()
    
    # 计算收益率
    data['Returns'] = data['Close'].pct_change()
    
    # 计算波动率 (20日滚动标准差)
    data['Volatility'] = data['Returns'].rolling(window=20).std()
    
    # 计算简单的RSI指标
    def calculate_rsi(prices, window=14):
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    data['RSI'] = calculate_rsi(data['Close'])
    
    print("✅ 技术指标计算完成")
    print("   - 移动平均线 (5日, 10日, 20日)")
    print("   - 日收益率")
    print("   - 波动率 (20日)")
    print("   - RSI指标 (14日)")
    
    # 显示最新的技术指标
    latest = data.iloc[-1]
    print(f"\n📊 最新技术指标 ({latest.name.strftime('%Y-%m-%d')}):")
    print(f"   收盘价: ${latest['Close']:.2f}")
    print(f"   MA5: ${latest['MA_5']:.2f}")
    print(f"   MA10: ${latest['MA_10']:.2f}")
    print(f"   MA20: ${latest['MA_20']:.2f}")
    print(f"   RSI: {latest['RSI']:.1f}")
    print(f"   波动率: {latest['Volatility']*100:.2f}%")


def demo_risk_analysis(data):
    """演示风险分析功能"""
    print("\n⚠️ 风险分析演示")
    print("=" * 40)
    
    returns = data['Returns'].dropna()
    
    # 基本风险指标
    annual_return = returns.mean() * 252  # 年化收益率
    annual_volatility = returns.std() * np.sqrt(252)  # 年化波动率
    sharpe_ratio = annual_return / annual_volatility if annual_volatility > 0 else 0
    
    # VaR计算 (Value at Risk)
    var_95 = returns.quantile(0.05)  # 95% VaR
    var_99 = returns.quantile(0.01)  # 99% VaR
    
    # 最大回撤
    cumulative_returns = (1 + returns).cumprod()
    rolling_max = cumulative_returns.expanding().max()
    drawdown = (cumulative_returns - rolling_max) / rolling_max
    max_drawdown = drawdown.min()
    
    print("✅ 风险指标计算完成")
    print(f"   年化收益率: {annual_return*100:.2f}%")
    print(f"   年化波动率: {annual_volatility*100:.2f}%")
    print(f"   夏普比率: {sharpe_ratio:.2f}")
    print(f"   95% VaR: {var_95*100:.2f}%")
    print(f"   99% VaR: {var_99*100:.2f}%")
    print(f"   最大回撤: {max_drawdown*100:.2f}%")


def demo_data_export(data):
    """演示数据导出功能"""
    print("\n💾 数据导出演示")
    print("=" * 40)
    
    try:
        # 导出为CSV
        csv_filename = "demo_stock_data.csv"
        data.to_csv(csv_filename)
        print(f"✅ 数据已导出为CSV: {csv_filename}")
        
        # 导出为Excel (如果openpyxl可用)
        try:
            excel_filename = "demo_stock_data.xlsx"
            data.to_excel(excel_filename)
            print(f"✅ 数据已导出为Excel: {excel_filename}")
        except ImportError:
            print("⚠️ Excel导出需要openpyxl库")
        
        # 导出为JSON
        json_filename = "demo_stock_data.json"
        data.to_json(json_filename, date_format='iso')
        print(f"✅ 数据已导出为JSON: {json_filename}")
        
    except Exception as e:
        print(f"❌ 数据导出失败: {e}")


def demo_openbb_extensions():
    """演示OpenBB扩展"""
    print("\n🔌 OpenBB 扩展演示")
    print("=" * 40)
    
    extensions = [
        ("openbb-core", "核心平台功能"),
        ("openbb-yfinance", "Yahoo Finance数据提供商"),
        ("openbb-equity", "股票数据扩展"),
        ("openbb-economy", "经济数据扩展"),
        ("openbb-news", "新闻数据扩展"),
    ]
    
    print("✅ 已安装的扩展:")
    for ext_name, description in extensions:
        try:
            __import__(ext_name.replace('-', '_'))
            print(f"   ✅ {ext_name}: {description}")
        except ImportError:
            print(f"   ❌ {ext_name}: 未安装")
    
    print("\n📦 可选扩展:")
    optional_extensions = [
        ("openbb-crypto", "加密货币数据"),
        ("openbb-technical", "技术分析工具"),
        ("openbb-charting", "图表可视化"),
        ("openbb-quantitative", "量化分析"),
        ("openbb-econometrics", "计量经济学"),
    ]
    
    for ext_name, description in optional_extensions:
        print(f"   📦 {ext_name}: {description}")


def main():
    """主演示函数"""
    print("🚀 OpenBB Platform 功能演示")
    print("=" * 60)
    print("本演示展示OpenBB的核心功能，不依赖外部API")
    print("=" * 60)
    
    # 1. 核心功能演示
    app = demo_openbb_core()
    
    # 2. 数据结构演示
    data = demo_data_structures()
    
    if data is not None:
        # 3. 技术分析演示
        demo_technical_analysis(data)
        
        # 4. 风险分析演示
        demo_risk_analysis(data)
        
        # 5. 数据导出演示
        demo_data_export(data)
    
    # 6. 扩展演示
    demo_openbb_extensions()
    
    print(f"\n{'='*60}")
    print("🎉 演示完成！")
    print("\n💡 下一步:")
    print("1. 查看生成的数据文件")
    print("2. 阅读 openbb_setup_guide.md")
    print("3. 尝试连接真实的数据源")
    print("4. 探索更多OpenBB功能")


if __name__ == "__main__":
    main()
