"""
OpenBB-CN API 服务器
提供股票数据查询、技术指标计算、AI分析等功能
"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import pandas as pd
import numpy as np
from typing import Optional, List, Dict, Any
import json
import logging
from datetime import datetime, timedelta
import asyncio
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    app = FastAPI(
        title="OpenBB-CN API",
        description="中国股票数据分析平台",
        version="1.0.0"
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    @app.get("/")
    def root():
        """根路径重定向到主页面"""
        return HTMLResponse("""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>OpenBB-CN Platform</title>
            <meta http-equiv="refresh" content="0; url=/app">
        </head>
        <body>
            <p>正在跳转到 <a href="/app">OpenBB-CN Platform</a>...</p>
        </body>
        </html>
        """)
    
    @app.get("/health")
    def health_check():
        """健康检查"""
        return {"status": "ok", "timestamp": datetime.now().isoformat()}

    @app.get("/favicon.ico")
    def favicon():
        """返回简单的favicon"""
        # 返回一个简单的透明图标
        return HTMLResponse(
            content="",
            status_code=204,
            headers={"Content-Type": "image/x-icon"}
        )
    
    # 股票搜索API - 使用AData数据源
    @app.get("/search")
    def search_stocks(query: str):
        """搜索股票 - 基于AData数据源，无限制"""
        try:
            logger.info(f"🔍 搜索股票: '{query}'")

            # 使用AData作为唯一数据源
            from openbb_cn.providers.adata_provider import adata_provider

            if not adata_provider or not adata_provider.available:
                logger.error("❌ AData数据源不可用")
                raise HTTPException(status_code=503, detail="数据源不可用")

            # 执行搜索 - 移除limit限制，让用户看到所有匹配结果
            df = adata_provider.search(query, limit=50)  # 设置合理的上限，避免返回过多数据

            if df.empty:
                logger.warning(f"⚠️ 搜索无结果: '{query}'")
                return []

            # 转换DataFrame为API响应格式
            results = []
            for _, row in df.iterrows():
                results.append({
                    "ts_code": row.get("ts_code", ""),
                    "name": row.get("name", ""),
                    "industry": row.get("industry", ""),
                    "area": row.get("area", ""),
                    "list_date": row.get("list_date", "")
                })

            logger.info(f"✅ 搜索成功: '{query}' -> 找到 {len(results)} 个结果")
            return results

        except Exception as e:
            logger.error(f"❌ 搜索股票失败: {e}")
            raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

    
    # 股票历史数据API - 使用AData数据源
    @app.get("/equity/price/historical/data")
    def get_historical_data(symbol: str, start_date: Optional[str] = None, end_date: Optional[str] = None):
        """获取股票历史数据 - 基于AData数据源"""
        try:
            logger.info(f"📊 获取历史数据: {symbol} ({start_date} ~ {end_date})")

            # 使用AData作为唯一数据源
            from openbb_cn.providers.adata_provider import adata_provider

            if not adata_provider or not adata_provider.available:
                logger.error("❌ AData数据源不可用")
                raise HTTPException(status_code=503, detail="数据源不可用")

            # 转换日期格式
            start_str = start_date.replace('-', '') if start_date else '20240101'
            end_str = end_date.replace('-', '') if end_date else None

            df = adata_provider.daily(symbol, start_str, end_str)

            if df.empty:
                logger.warning(f"⚠️ {symbol} 无历史数据")
                return []

            # 转换为API格式
            data = []
            for _, row in df.iterrows():
                try:
                    data.append({
                        "trade_date": str(row['trade_date']),
                        "open": float(row['open']),
                        "high": float(row['high']),
                        "low": float(row['low']),
                        "close": float(row['close']),
                        "volume": int(row['volume']),
                        "amount": float(row['volume'] * row['close'])
                    })
                except (ValueError, TypeError) as e:
                    logger.warning(f"数据转换错误: {e}")
                    continue

            logger.info(f"✅ 获取历史数据成功: {len(data)} 条")
            return data

        except Exception as e:
            logger.error(f"❌ 获取历史数据失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取历史数据失败: {str(e)}")
    
    # 估值倍数API
    @app.get("/equity/basic/ratios")
    def get_ratios(symbol: str):
        """获取估值倍数"""
        try:
            # 生成模拟估值数据
            dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='M')
            
            data = []
            for date in dates:
                data.append({
                    "trade_date": date.strftime("%Y-%m-%d"),
                    "pe": round(np.random.uniform(10, 30), 2),
                    "pb": round(np.random.uniform(1, 5), 2),
                    "ps": round(np.random.uniform(2, 8), 2),
                    "pcf": round(np.random.uniform(5, 15), 2)
                })
            
            return data
            
        except Exception as e:
            logger.error(f"获取估值数据失败: {e}")
            raise HTTPException(status_code=500, detail="获取数据失败")
    
    # 技术指标API
    @app.get("/equity/price/indicators")
    def get_indicators(symbol: str):
        """获取技术指标"""
        try:
            # 生成模拟技术指标数据
            latest = {
                "rsi": round(np.random.uniform(30, 70), 2),
                "macd": round(np.random.uniform(-2, 2), 4),
                "macd_signal": round(np.random.uniform(-2, 2), 4),
                "boll_upper": round(np.random.uniform(105, 115), 2),
                "boll_mid": round(np.random.uniform(95, 105), 2),
                "boll_lower": round(np.random.uniform(85, 95), 2),
                "kdj_k": round(np.random.uniform(20, 80), 2),
                "kdj_d": round(np.random.uniform(20, 80), 2),
                "kdj_j": round(np.random.uniform(0, 100), 2),
                "atr": round(np.random.uniform(1, 5), 2),
                "vwap": round(np.random.uniform(95, 105), 2)
            }
            
            return {"latest": latest}
            
        except Exception as e:
            logger.error(f"获取技术指标失败: {e}")
            raise HTTPException(status_code=500, detail="获取数据失败")
    
    # 自研指标API
    @app.get("/indicator/{indicator_name}")
    def get_custom_indicator(indicator_name: str, symbol: str):
        """获取自研指标"""
        try:
            # 导入自研指标模块
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(current_dir))
            if project_root not in sys.path:
                sys.path.insert(0, project_root)

            from openbb_cn.sdk import indicators
            from openbb_cn.providers.adata_provider import adata_provider

            # 获取股票数据
            if not adata_provider or not adata_provider.available:
                logger.error("❌ AData数据源不可用")
                raise HTTPException(status_code=503, detail="数据源不可用")

            end_date = datetime.now().strftime("%Y%m%d")
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y%m%d")

            df = adata_provider.daily(symbol, start=start_date, end=end_date)

            if df.empty:
                # 如果没有数据，返回模拟数据
                dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
                dates = dates[dates.weekday < 5]
                data = []
                for date in dates:
                    value = np.random.uniform(-1, 1) if indicator_name in ['vcm', 'tei', 'api', 'mtr'] else np.random.uniform(0, 2)
                    data.append({
                        "t": date.strftime("%Y-%m-%d"),
                        "v": round(value, 4)
                    })
                return data[-60:]

            # 计算真实指标
            if indicator_name == 'vcm':
                result = indicators.vcm(df)
            elif indicator_name == 'tei':
                result = indicators.tei(df)
            elif indicator_name == 'lvr':
                result = indicators.lvr(df)
            elif indicator_name == 'api_index':
                result = indicators.api_index(df)
            elif indicator_name == 'mtr':
                result = indicators.mtr(df)
            elif indicator_name == 'atr':
                result = indicators.atr(df)
            elif indicator_name == 'rsi':
                result = indicators.rsi(df)
            elif indicator_name == 'vwap':
                result = indicators.vwap(df)
            elif indicator_name == 'blce':
                # BLCE返回DataFrame，取energy列
                blce_df = indicators.blce(df)
                result = blce_df['energy'] if 'energy' in blce_df.columns else blce_df.iloc[:, 0]
            elif indicator_name == 'macd':
                # MACD返回DataFrame，取macd列
                macd_df = indicators.macd(df)
                result = macd_df['macd'] if 'macd' in macd_df.columns else macd_df.iloc[:, 0]
            elif indicator_name == 'boll':
                # 布林带返回DataFrame，取mid列
                boll_df = indicators.boll(df)
                result = boll_df['mid'] if 'mid' in boll_df.columns else boll_df.iloc[:, 0]
            elif indicator_name == 'kdj':
                # KDJ返回DataFrame，取k列
                kdj_df = indicators.kdj(df)
                result = kdj_df['k'] if 'k' in kdj_df.columns else kdj_df.iloc[:, 0]
            else:
                # 未知指标，返回空数据
                return []

            # 转换为API格式
            data = []
            for date, value in result.dropna().tail(60).items():
                data.append({
                    "t": date.strftime("%Y-%m-%d") if hasattr(date, 'strftime') else str(date),
                    "v": round(float(value), 4)
                })

            return data

        except Exception as e:
            logger.error(f"获取自研指标失败: {e}")
            # 返回模拟数据作为备用
            dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
            dates = dates[dates.weekday < 5]
            data = []
            for date in dates:
                value = np.random.uniform(-1, 1) if indicator_name in ['vcm', 'tei', 'api', 'mtr'] else np.random.uniform(0, 2)
                data.append({
                    "t": date.strftime("%Y-%m-%d"),
                    "v": round(value, 4)
                })
            return data[-60:]
    
    # BLCE指标特殊处理
    @app.get("/indicator/blce")
    def get_blce_indicator(symbol: str):
        """获取BLCE指标"""
        try:
            dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
            dates = dates[dates.weekday < 5]
            
            data = []
            for date in dates:
                data.append({
                    "t": date.strftime("%Y-%m-%d"),
                    "prob_up": round(np.random.uniform(0.2, 0.8), 3)
                })
            
            return data[-60:]
            
        except Exception as e:
            logger.error(f"获取BLCE指标失败: {e}")
            raise HTTPException(status_code=500, detail="获取数据失败")
    
    # AI分析API
    @app.post("/ai/analyze")
    def ai_analyze(request: Dict[str, Any]):
        """AI分析 - 支持完整数据分析"""
        try:
            symbol = request.get("symbol", "")
            indicators = request.get("indicators", {})
            price_data = request.get("price_data", {})
            news = request.get("news", {})
            prompt = request.get("prompt", "")

            # 导入AI模块
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(current_dir))
            if project_root not in sys.path:
                sys.path.insert(0, project_root)

            try:
                from openbb_cn.ai.llm_analyzer import llm_analyzer

                if llm_analyzer:
                    # 使用真正的阿里云百炼LLM进行分析
                    logger.info(f"使用阿里云百炼LLM分析股票: {symbol}")
                    analysis_result = llm_analyzer.analyze_stock(symbol, indicators, prompt, news, price_data)
                    return {"text": analysis_result}
                else:
                    logger.warning("LLM分析器不可用，使用备用分析")
                    raise Exception("LLM分析器初始化失败")

            except Exception as ai_error:
                logger.warning(f"AI模块调用失败，使用备用分析: {ai_error}")

                # 使用备用分析，基于真实指标数据
                indicator_data = indicators.get('latest', {})
                data_source = indicators.get('data_source', 'unknown')
                indicator_count = indicators.get('count', 0)

                # 构建详细的指标分析
                analysis_parts = []

                # 数据来源确认
                analysis_parts.append(f"📊 {symbol} 技术分析报告 (基于真实数据)")
                analysis_parts.append(f"📈 数据来源: {data_source}")
                analysis_parts.append(f"📊 指标数量: {indicator_count}个真实指标")

                # 自研指标分析
                if indicator_data:
                    analysis_parts.append("\n🔍 自研指标分析：")

                    if 'vcm' in indicator_data:
                        vcm_data = indicator_data['vcm']
                        vcm_val = vcm_data.get('value', 0) if isinstance(vcm_data, dict) else vcm_data
                        vcm_trend = vcm_data.get('trend', 'unknown') if isinstance(vcm_data, dict) else 'unknown'
                        analysis_parts.append(f"• VCM (放量顺势动量): {vcm_val:.4f} - 趋势{vcm_trend}")

                    if 'tei' in indicator_data:
                        tei_data = indicator_data['tei']
                        tei_val = tei_data.get('value', 0) if isinstance(tei_data, dict) else tei_data
                        tei_trend = tei_data.get('trend', 'unknown') if isinstance(tei_data, dict) else 'unknown'
                        analysis_parts.append(f"• TEI (趋势熵分): {tei_val:.4f} - 趋势{tei_trend}")

                    if 'lvr' in indicator_data:
                        lvr_data = indicator_data['lvr']
                        lvr_val = lvr_data.get('value', 0) if isinstance(lvr_data, dict) else lvr_data
                        lvr_risk = '低风险' if abs(lvr_val) < 0.3 else '高风险'
                        analysis_parts.append(f"• LVR (流动性风险): {lvr_val:.4f} - {lvr_risk}")

                    if 'api_index' in indicator_data:
                        api_data = indicator_data['api_index']
                        api_val = api_data.get('value', 0) if isinstance(api_data, dict) else api_data
                        api_pressure = '买压强' if api_val > 0 else '卖压强'
                        analysis_parts.append(f"• API (非对称压力): {api_val:.4f} - {api_pressure}")

                    # 技术指标分析
                    analysis_parts.append("\n📈 技术指标状态：")

                    if 'rsi' in indicator_data:
                        rsi_data = indicator_data['rsi']
                        rsi_val = rsi_data.get('value', 0) if isinstance(rsi_data, dict) else rsi_data
                        rsi_status = '超买' if rsi_val > 70 else ('超卖' if rsi_val < 30 else '中性')
                        analysis_parts.append(f"• RSI: {rsi_val:.2f} - {rsi_status}")

                    if 'macd' in indicator_data:
                        macd_data = indicator_data['macd']
                        macd_val = macd_data.get('value', 0) if isinstance(macd_data, dict) else macd_data
                        macd_signal = '金叉' if macd_val > 0 else '死叉'
                        analysis_parts.append(f"• MACD: {macd_val:.4f} - {macd_signal}信号")

                else:
                    analysis_parts.append("\n⚠️ 暂无指标数据，请先选择股票并等待数据加载")

                # 投资建议
                analysis_parts.append(f"\n💡 投资建议：")
                analysis_parts.append(prompt if prompt else f"基于{indicator_count}个真实指标分析，建议谨慎操作，关注关键指标变化。")

                # 风险提示
                analysis_parts.append("\n⚠️ 风险提示：")
                analysis_parts.append("本分析基于真实市场数据，但AI服务暂时不可用。股市有风险，投资需谨慎。")

                analysis = "\n".join(analysis_parts)
                return {"text": analysis}

        except Exception as e:
            logger.error(f"AI分析失败: {e}")
            raise HTTPException(status_code=500, detail="分析失败")

    # 主页面
    @app.get("/app")
    def main_app():
        """主页面"""
        return HTMLResponse("""
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8' />
    <title>OpenBB-CN Platform</title>
    <script src="https://cdn.plot.ly/plotly-2.35.2.min.js"></script>
    <style>
        :root { --bg:#0f1115; --panel:#151822; --text:#eaeef2; --muted:#a0a6b1; --accent:#3b82f6; }
        html,body { margin:0; height:100%; background:var(--bg); color:var(--text); font-family: ui-sans-serif, system-ui, Segoe UI, Roboto, Helvetica, Arial; }
        .layout { display:grid; grid-template-columns: 280px 1fr 360px; grid-template-rows: 56px 1fr; height:100%; }
        .topbar { grid-column: 1 / 4; display:flex; align-items:center; gap:12px; padding:0 16px; background:#0c0e13; border-bottom:1px solid #1d2233; }
        .brand { font-weight:600; color:#8ab4ff; }
        .left { background:var(--panel); border-right:1px solid #20263a; padding:12px; overflow:auto; }
        .main { background:transparent; padding:12px; overflow:auto; display:flex; flex-direction:column; gap:12px; }
        .right { background:var(--panel); border-left:1px solid #20263a; padding:12px; overflow:auto; display:flex; flex-direction:column; gap:12px; }
        input, button, select, textarea { background:#0c0f16; border:1px solid #26304b; color:var(--text); padding:8px 10px; border-radius:6px; }
        button { background:#1f2937; cursor:pointer; }
        .row { display:flex; gap:8px; align-items:center; flex-wrap:wrap; }
        .watch { margin-top:12px; }
        .item { padding:10px 12px; border:1px solid #26304b; border-radius:8px; margin-bottom:8px; cursor:pointer; transition: all 0.2s ease; background: var(--panel); }
        .item:hover { border-color: #3b82f6; background: #1e293b; transform: translateX(2px); }
        .main-display { display:grid; grid-template-columns: 2fr 1fr; gap:15px; margin-bottom:15px; }
        .chart-panel { background:var(--panel); border:1px solid #20263a; border-radius:10px; padding:15px; }
        .info-panel { background:var(--panel); border:1px solid #20263a; border-radius:10px; padding:15px; }
        .panel { background:var(--panel); border:1px solid #20263a; border-radius:10px; padding:10px; }
        .panel h4 { margin:0 0 8px 0; color:#c8d3f5; }
        #kline { height:500px; }
        #volume { height:150px; }
        #ratio { height:200px; }
        .news-panel { background:var(--panel); border:1px solid #20263a; border-radius:10px; padding:15px; margin-top:15px; }
        .news-item { padding:10px; border-bottom:1px solid #26304b; cursor:pointer; transition:background 0.2s; }
        .news-item:hover { background:#1a1f2e; }
        .news-item:last-child { border-bottom:none; }
        .news-title { font-weight:500; margin-bottom:5px; color:#e2e8f0; }
        .news-summary { font-size:12px; color:var(--muted); line-height:1.4; }
        .news-time { font-size:11px; color:#64748b; margin-top:5px; }
        .cards { display:grid; grid-template-columns: repeat(3,minmax(0,1fr)); gap:10px; }
        .card { background:#10131b; border:1px solid #26304b; border-left:6px solid #334155; padding:10px; border-radius:8px; }
        .good { border-left-color:#16a34a; }
        .warn { border-left-color:#f59e0b; }
        .bad { border-left-color:#ef4444; }
        .chatbox { height:300px; overflow:auto; background:#0c0f16; border:1px solid #26304b; padding:8px; border-radius:8px; }
        .msg { margin:8px 0; padding:10px; border-radius:12px; max-width:85%; word-wrap:break-word; }
        .msg:nth-child(odd) { background:#1a2332; margin-right:auto; } /* AI消息 */
        .msg:nth-child(even) { background:#007bff; color:white; margin-left:auto; text-align:right; } /* 用户消息 */
        .role { font-size:12px; margin-bottom:4px; font-weight:bold; }
        .msg:nth-child(odd) .role { color:#9fb3ff; } /* AI角色 */
        .msg:nth-child(even) .role { color:#cce7ff; } /* 用户角色 */
    </style>
</head>
<body>
    <div class="layout">
        <div class="topbar">
            <div class="brand">OpenBB-CN</div>
            <div class="row">
                <input id="q" placeholder="输入股票名称(如:平安银行)或6位代码(如:000001)" />
                <button onclick="search()">🔍 搜索</button>
                <button onclick="addFromInput()">加入自选</button>
                <button onclick="clearWatch()">清空自选</button>
                <a href="/trading" style="color:#8ab4ff;text-decoration:none;margin-left:20px;padding:8px 12px;border:1px solid #26304b;border-radius:6px;">📈 模拟炒股</a>
            </div>
        </div>
        <div class="left">
            <h4>搜索结果</h4>
            <div id="results"></div>
            <h4 style="margin-top:10px">自选（本地）</h4>
            <div id="watch" class="watch"></div>
        </div>
        <div class="main">
            <!-- 主展示区域 -->
            <div class="main-display">
                <div class="chart-panel">
                    <h4 id="title">K线图表</h4>
                    <div id="kline"></div>
                    <div id="volume"></div>
                </div>
                <div class="info-panel">
                    <h4>实时行情</h4>
                    <div id="realtime-info" style="margin-bottom:15px;">
                        <div id="price-display" style="font-size:24px; font-weight:bold; color:#16a34a; margin-bottom:10px;">--</div>
                        <div id="change-display" style="font-size:14px; margin-bottom:15px;">--</div>
                        <div style="display:grid; grid-template-columns:1fr 1fr; gap:10px; font-size:12px;">
                            <div>开盘: <span id="open-price">--</span></div>
                            <div>最高: <span id="high-price">--</span></div>
                            <div>最低: <span id="low-price">--</span></div>
                            <div>成交量: <span id="volume-info">--</span></div>
                        </div>
                    </div>
                    <h4>估值倍数</h4>
                    <div id="ratio"></div>
                </div>
            </div>

            <!-- 指标卡片区域 -->
            <div class="panel">
                <h4>技术指标</h4>
                <div id="cards" class="cards"></div>
                <h4 id="chart-title" style="display:none;">指标图表</h4>
                <div id="indicator-chart" style="display:none; width:100%; height:300px; border:1px solid #333; margin-top:10px; background:#1a1a1a; position:relative;">
                    <canvas id="chart-canvas" width="800" height="300"></canvas>
                </div>
            </div>

            <!-- 新闻板块 -->
            <div class="news-panel" id="news-section" style="display:none;">
                <h4>📰 相关新闻</h4>
                <div id="news-content">
                    <div style="text-align:center; color:var(--muted); padding:20px;">
                        请选择股票查看相关新闻
                    </div>
                </div>
            </div>
        </div>
        <div class="right">
            <!-- AI对话区域 -->
            <div style="flex:1; display:flex; flex-direction:column;">
                <h4>🤖 AI 智能分析</h4>
                <div id="chat" class="chatbox" style="flex:1; min-height:300px;">
                    <div class="msg">
                        <div class="role">AI</div>
                        <div>👋 您好！我是AI股票分析助手。请先搜索并选择一只股票，然后您可以：<br>
                        • 询问技术指标分析<br>
                        • 咨询投资建议<br>
                        • 了解风险评估<br>
                        • 分析相关新闻影响<br>
                        💡 提示：按回车键发送，Ctrl+回车换行</div>
                    </div>
                </div>
                <div class="row" style="margin-top:8px; display:flex; gap:8px;">
                    <textarea id="prompt" placeholder="输入您的问题，与AI对话..." style="flex:1; resize:vertical; min-height:40px;" rows="2"></textarea>
                    <button onclick="askAI()" style="padding:8px 16px; background:#007bff; color:white; border:none; border-radius:4px; cursor:pointer;">发送</button>
                </div>
            </div>

            <!-- 快捷分析按钮 -->
            <div style="margin-top:15px;">
                <h4>🚀 快捷分析</h4>
                <div style="display:grid; grid-template-columns:1fr 1fr; gap:8px;">
                    <button onclick="quickAnalysis('technical')" style="padding:8px; font-size:12px; background:#1f2937; border:1px solid #374151; border-radius:6px; color:#e5e7eb; cursor:pointer;" onmouseover="this.style.background='#374151'" onmouseout="this.style.background='#1f2937'">📊 技术分析</button>
                    <button onclick="quickAnalysis('news')" style="padding:8px; font-size:12px; background:#1f2937; border:1px solid #374151; border-radius:6px; color:#e5e7eb; cursor:pointer;" onmouseover="this.style.background='#374151'" onmouseout="this.style.background='#1f2937'">📰 新闻影响</button>
                    <button onclick="quickAnalysis('risk')" style="padding:8px; font-size:12px; background:#1f2937; border:1px solid #374151; border-radius:6px; color:#e5e7eb; cursor:pointer;" onmouseover="this.style.background='#374151'" onmouseout="this.style.background='#1f2937'">⚠️ 风险评估</button>
                    <button onclick="quickAnalysis('strategy')" style="padding:8px; font-size:12px; background:#1f2937; border:1px solid #374151; border-radius:6px; color:#e5e7eb; cursor:pointer;" onmouseover="this.style.background='#374151'" onmouseout="this.style.background='#1f2937'">🎯 投资策略</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const $ = id => document.getElementById(id);
        let currentSym = null;
        let currentName = null;

        // 搜索功能
        async function search() {
            const q = $('q').value.trim();
            if (!q) return;

            try {
                console.log('搜索查询:', q);
                const resp = await fetch('/search?query=' + encodeURIComponent(q));
                console.log('API响应状态:', resp.status);
                const data = await resp.json();
                console.log('搜索结果:', data);

                if (!data || data.length === 0) {
                    $('results').innerHTML = '<div style="color:#f59e0b;">未找到相关股票</div>';
                    return;
                }

                const html = data.map(item => {
                    const displayCode = item.ts_code.replace(/\\.(SH|SZ)$/, '');
                    return `<div class="item" onclick="selectStock('${item.ts_code}', '${item.name}')">
                        <div style="font-weight:500;">${displayCode}</div>
                        <div style="font-size:12px;color:var(--muted);">${item.name}</div>
                    </div>`;
                }).join('');

                console.log('生成的HTML:', html);
                $('results').innerHTML = html;
                console.log('搜索结果已更新到页面');
            } catch (e) {
                console.error('搜索错误:', e);
                $('results').innerHTML = '<div style="color:#ef4444;">搜索失败: ' + e.message + '</div>';
            }
        }

        // 选择股票
        async function selectStock(symbol, name) {
            currentSym = symbol;
            currentName = name;  // 存储当前股票名称
            $('title').textContent = `${name} (${symbol.replace(/\\\\.(SH|SZ)$/, '')})`;

            // 显示新闻板块
            $('news-section').style.display = 'block';

            // 加载数据 - 确保指标数据加载完成后再进行AI分析
            loadKlineData(symbol);
            loadRatioData(symbol);
            loadRealtimeInfo(symbol);
            loadNews(symbol, name);

            // 等待指标数据加载完成后再进行AI分析
            loadIndicators(symbol).then(() => {
                // 延迟一秒确保所有数据都已加载
                setTimeout(() => {
                    loadAIAnalysis(symbol);
                }, 1000);
            });
        }

        // 加载实时行情信息
        async function loadRealtimeInfo(symbol) {
            try {
                const resp = await fetch('/equity/price/historical/data?symbol=' + encodeURIComponent(symbol));
                const data = await resp.json();

                if (data && data.length > 0) {
                    const latest = data[data.length - 1];
                    const previous = data.length > 1 ? data[data.length - 2] : latest;

                    const currentPrice = latest.close;
                    const change = currentPrice - previous.close;
                    const changePercent = (change / previous.close * 100).toFixed(2);

                    // 更新实时行情显示
                    $('price-display').textContent = currentPrice.toFixed(2);
                    $('price-display').style.color = change >= 0 ? '#16a34a' : '#ef4444';

                    const changeText = `${change >= 0 ? '+' : ''}${change.toFixed(2)} (${change >= 0 ? '+' : ''}${changePercent}%)`;
                    $('change-display').textContent = changeText;
                    $('change-display').style.color = change >= 0 ? '#16a34a' : '#ef4444';

                    $('open-price').textContent = latest.open.toFixed(2);
                    $('high-price').textContent = latest.high.toFixed(2);
                    $('low-price').textContent = latest.low.toFixed(2);
                    $('volume-info').textContent = (latest.volume / 10000).toFixed(0) + '万';
                }
            } catch (e) {
                console.error('加载实时行情失败:', e);
            }
        }

        // 加载K线数据
        async function loadKlineData(symbol) {
            try {
                const resp = await fetch('/equity/price/historical/data?symbol=' + encodeURIComponent(symbol));
                const data = await resp.json();

                if (data && data.length > 0) {
                    const trace = {
                        x: data.map(d => d.trade_date),
                        close: data.map(d => d.close),
                        high: data.map(d => d.high),
                        low: data.map(d => d.low),
                        open: data.map(d => d.open),
                        type: 'candlestick'
                    };

                    Plotly.newPlot('kline', [trace], {
                        margin: { t: 24, r: 16, b: 24, l: 40 }
                    }, {responsive: true});

                    const volumeTrace = {
                        x: data.map(d => d.trade_date),
                        y: data.map(d => d.volume),
                        type: 'bar'
                    };

                    Plotly.newPlot('volume', [volumeTrace], {
                        margin: { t: 24, r: 16, b: 24, l: 40 }
                    }, {responsive: true});
                }
            } catch (e) {
                console.error('加载K线数据失败:', e);
            }
        }

        // 加载新闻
        async function loadNews(symbol, name) {
            try {
                $('news-content').innerHTML = '<div style="text-align:center; padding:20px; color:var(--muted);">📰 加载新闻中...</div>';

                // 模拟新闻数据（实际项目中应该调用真实的新闻API）
                const mockNews = [
                    {
                        title: `${name}发布2024年第三季度财报`,
                        summary: '公司营收同比增长15.2%，净利润增长12.8%，业绩超出市场预期。管理层对未来发展前景保持乐观态度。',
                        time: '2小时前',
                        sentiment: 'positive'
                    },
                    {
                        title: `机构调研：${name}核心竞争力持续增强`,
                        summary: '多家知名机构近期密集调研，看好公司在行业中的领先地位和技术创新能力。',
                        time: '5小时前',
                        sentiment: 'positive'
                    },
                    {
                        title: `${name}获得重要合作订单`,
                        summary: '公司与行业龙头企业签署战略合作协议，预计将为公司带来稳定的收入增长。',
                        time: '1天前',
                        sentiment: 'positive'
                    },
                    {
                        title: `行业政策利好，${name}有望受益`,
                        summary: '相关部门发布行业支持政策，公司作为行业内重要企业，预计将从政策红利中获益。',
                        time: '2天前',
                        sentiment: 'positive'
                    },
                    {
                        title: `${name}股东大会通过重要决议`,
                        summary: '股东大会审议通过了公司发展战略规划，为未来3-5年发展奠定基础。',
                        time: '3天前',
                        sentiment: 'neutral'
                    }
                ];

                // 渲染新闻
                const newsHtml = mockNews.map(news => `
                    <div class="news-item" onclick="selectNews('${news.title}', '${news.summary}')">
                        <div class="news-title">${news.title}</div>
                        <div class="news-summary">${news.summary}</div>
                        <div class="news-time">${news.time}</div>
                    </div>
                `).join('');

                $('news-content').innerHTML = newsHtml;

                // 存储新闻数据供AI分析使用
                window.currentNews = mockNews;

            } catch (e) {
                console.error('加载新闻失败:', e);
                $('news-content').innerHTML = '<div style="text-align:center; padding:20px; color:#ef4444;">❌ 新闻加载失败</div>';
            }
        }

        // 选择新闻（供AI分析使用）
        function selectNews(title, summary) {
            const prompt = `请分析这条新闻对股票的影响：\n标题：${title}\n内容：${summary}`;
            $('prompt').value = prompt;
        }

        // 加载估值倍数
        async function loadRatioData(symbol) {
            try {
                const resp = await fetch('/equity/basic/ratios?symbol=' + encodeURIComponent(symbol));
                const data = await resp.json();

                if (data && data.length > 0) {
                    const dates = data.map(x => x.trade_date);
                    Plotly.newPlot('ratio', [
                        { x: dates, y: data.map(x => x.pe), name: 'PE', type: 'scatter', mode: 'lines' },
                        { x: dates, y: data.map(x => x.pb), name: 'PB', type: 'scatter', mode: 'lines' },
                        { x: dates, y: data.map(x => x.ps), name: 'PS', type: 'scatter', mode: 'lines' }
                    ], { margin: { t: 24, r: 16, b: 24, l: 40 } }, {responsive: true});
                }
            } catch (e) {
                console.error('加载估值数据失败:', e);
            }
        }

        // 加载指标 - 优化数据收集
        async function loadIndicators(symbol) {
            const indicators = ['vcm', 'tei', 'lvr', 'api_index', 'mtr', 'atr', 'rsi', 'vwap', 'blce', 'macd', 'boll', 'kdj'];

            // 指标中文名称映射
            const indicatorNames = {
                'vcm': 'VCM - 放量顺势动量',
                'tei': 'TEI - 趋势熵分指标',
                'lvr': 'LVR - 流动性风险',
                'api_index': 'API - 非对称压力指标',
                'mtr': 'MTR - 多时间尺度共振',
                'atr': 'ATR - 平均真实波幅',
                'rsi': 'RSI - 相对强弱指标',
                'vwap': 'VWAP - 成交量加权均价',
                'blce': 'BLCE - 突破可能性',
                'macd': 'MACD - 指数平滑异同移动平均线',
                'boll': 'BOLL - 布林带',
                'kdj': 'KDJ - 随机指标'
            };

            // 指标类型分类
            const indicatorTypes = {
                'vcm': 'momentum',
                'tei': 'trend',
                'lvr': 'risk',
                'api_index': 'pressure',
                'mtr': 'resonance',
                'atr': 'volatility',
                'rsi': 'momentum',
                'vwap': 'price',
                'blce': 'breakout',
                'macd': 'trend',
                'boll': 'volatility',
                'kdj': 'momentum'
            };

            let html = '';
            let loadedCount = 0;
            let totalCount = indicators.length;

            // 初始化指标数据存储
            window.indicatorData = {};
            window.indicatorSummary = {
                total: totalCount,
                loaded: 0,
                failed: 0,
                categories: {
                    momentum: [],
                    trend: [],
                    volatility: [],
                    risk: [],
                    pressure: [],
                    resonance: [],
                    price: [],
                    breakout: []
                }
            };

            console.log(`开始加载 ${totalCount} 个技术指标...`);

            // 并行加载所有指标
            const promises = indicators.map(async (ind) => {
                const displayName = indicatorNames[ind] || ind.toUpperCase();
                const category = indicatorTypes[ind] || 'other';

                try {
                    const resp = await fetch(`/indicator/${ind}?symbol=${encodeURIComponent(symbol)}`);
                    const data = await resp.json();

                    if (data && data.length > 0) {
                        const latest = data[data.length - 1];
                        const previous = data.length > 1 ? data[data.length - 2] : latest;
                        const value = latest.v || latest.prob_up || 0;
                        const prevValue = previous.v || previous.prob_up || 0;

                        // 计算趋势
                        let trend = 'stable';
                        if (value > prevValue) trend = 'up';
                        else if (value < prevValue) trend = 'down';

                        // 计算信号强度
                        let signal = 'neutral';
                        if (ind === 'rsi') {
                            if (value > 70) signal = 'overbought';
                            else if (value < 30) signal = 'oversold';
                        } else if (ind === 'vcm') {
                            if (value > 0.5) signal = 'bullish';
                            else if (value < -0.5) signal = 'bearish';
                        }

                        // 存储详细数据
                        window.indicatorData[ind] = data;
                        window.indicatorSummary.categories[category].push({
                            name: ind,
                            displayName: displayName,
                            value: value,
                            trend: trend,
                            signal: signal,
                            date: latest.t || 'unknown'
                        });

                        window.indicatorSummary.loaded++;
                        loadedCount++;

                        // 生成卡片HTML
                        const trendColor = trend === 'up' ? '#16a34a' : (trend === 'down' ? '#ef4444' : '#6b7280');
                        const signalEmoji = signal === 'bullish' ? '📈' : (signal === 'bearish' ? '📉' : (signal === 'overbought' ? '⚠️' : (signal === 'oversold' ? '💡' : '📊')));

                        return `<div class="card ${signal === 'bullish' ? 'good' : (signal === 'bearish' ? 'bad' : (signal === 'overbought' || signal === 'oversold' ? 'warn' : ''))}" onclick="showIndicatorChart('${ind}', '${displayName}')" style="cursor:pointer;">
                            <div style="color:#9fb3ff"><b>${displayName}</b> ${signalEmoji}</div>
                            <div style="font-size:22px;margin:6px 0;color:${trendColor}">${value ? value.toFixed(4) : 'N/A'}</div>
                            <div style="font-size:11px;color:var(--muted)">趋势: ${trend === 'up' ? '↗️' : (trend === 'down' ? '↘️' : '➡️')}</div>
                        </div>`;
                    } else {
                        window.indicatorSummary.failed++;
                        return `<div class="card" style="opacity:0.6;">
                            <div style="color:#9fb3ff"><b>${displayName}</b></div>
                            <div style="font-size:22px;margin:6px 0">无数据</div>
                        </div>`;
                    }
                } catch (e) {
                    console.error(`加载${ind}失败:`, e);
                    window.indicatorSummary.failed++;
                    return `<div class="card" style="opacity:0.6;">
                        <div style="color:#9fb3ff"><b>${displayName}</b></div>
                        <div style="font-size:22px;margin:6px 0">加载失败</div>
                    </div>`;
                }
            });

            // 等待所有指标加载完成
            const results = await Promise.all(promises);
            html = results.join('');
            $('cards').innerHTML = html;

            console.log(`指标加载完成: ${window.indicatorSummary.loaded}/${totalCount} 成功, ${window.indicatorSummary.failed} 失败`);
            console.log('指标汇总数据:', window.indicatorSummary);

            return window.indicatorSummary;
        }

        // 显示指标图表
        function showIndicatorChart(indicator, displayName) {
            const data = window.indicatorData && window.indicatorData[indicator];
            if (!data || data.length === 0) {
                alert('暂无图表数据');
                return;
            }

            // 显示图表区域
            $('chart-title').style.display = 'block';
            $('chart-title').textContent = `${displayName} 趋势图表`;
            $('indicator-chart').style.display = 'block';

            // 绘制图表
            drawIndicatorChart(data, displayName);
        }

        // 绘制指标图表
        function drawIndicatorChart(data, title) {
            const canvas = $('chart-canvas');
            const ctx = canvas.getContext('2d');

            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            if (data.length === 0) return;

            // 设置样式
            ctx.strokeStyle = '#00d4ff';
            ctx.fillStyle = '#00d4ff';
            ctx.lineWidth = 2;

            // 计算数据范围
            const values = data.map(d => parseFloat(d.v || d.prob_up || 0));
            const minVal = Math.min(...values);
            const maxVal = Math.max(...values);
            const range = maxVal - minVal || 1;

            // 绘制网格线
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            for (let i = 0; i <= 5; i++) {
                const y = (canvas.height - 40) * i / 5 + 20;
                ctx.beginPath();
                ctx.moveTo(40, y);
                ctx.lineTo(canvas.width - 20, y);
                ctx.stroke();
            }

            // 绘制Y轴标签
            ctx.fillStyle = '#999';
            ctx.font = '12px Arial';
            for (let i = 0; i <= 5; i++) {
                const y = (canvas.height - 40) * i / 5 + 25;
                const val = maxVal - (range * i / 5);
                ctx.fillText(val.toFixed(4), 5, y);
            }

            // 绘制数据线
            ctx.strokeStyle = '#00d4ff';
            ctx.lineWidth = 2;
            ctx.beginPath();

            const stepX = (canvas.width - 60) / (data.length - 1);

            for (let i = 0; i < data.length; i++) {
                const x = 40 + i * stepX;
                const normalizedVal = (values[i] - minVal) / range;
                const y = canvas.height - 20 - normalizedVal * (canvas.height - 40);

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }

            ctx.stroke();

            // 绘制数据点
            ctx.fillStyle = '#00d4ff';
            for (let i = 0; i < data.length; i++) {
                const x = 40 + i * stepX;
                const normalizedVal = (values[i] - minVal) / range;
                const y = canvas.height - 20 - normalizedVal * (canvas.height - 40);

                ctx.beginPath();
                ctx.arc(x, y, 3, 0, 2 * Math.PI);
                ctx.fill();
            }

            // 显示最新值
            ctx.fillStyle = '#fff';
            ctx.font = '14px Arial';
            const latestVal = values[values.length - 1];
            ctx.fillText(`最新值: ${latestVal.toFixed(4)}`, canvas.width - 150, 30);
        }

        // 加载AI分析 - 增强数据收集
        async function loadAIAnalysis(symbol) {
            try {
                $('chat').innerHTML = '<div class="msg"><div class="role">AI</div><div>🤖 AI正在深度分析中，整合技术指标、价格数据和新闻信息...</div></div>';

                // 收集完整的指标数据
                const indicatorValues = {};
                const indicatorSummary = window.indicatorSummary || {};
                let dataQuality = 'excellent';

                if (window.indicatorData && Object.keys(window.indicatorData).length > 0) {
                    for (const [indicator, data] of Object.entries(window.indicatorData)) {
                        if (data && data.length > 0) {
                            const latest = data[data.length - 1];
                            const previous = data.length > 1 ? data[data.length - 2] : latest;

                            indicatorValues[indicator] = {
                                value: latest.v || latest.prob_up || 0,
                                date: latest.t || 'unknown',
                                trend: data.length >= 2 ?
                                    (latest.v > previous.v ? 'up' : (latest.v < previous.v ? 'down' : 'stable')) : 'stable',
                                history_length: data.length,
                                strength: Math.abs(latest.v || 0) // 信号强度
                            };
                        }
                    }
                } else {
                    dataQuality = 'poor';
                    console.warn('警告: 指标数据不完整或缺失');
                }

                // 收集价格数据
                let priceData = {};
                try {
                    const priceResp = await fetch(`/equity/price/historical/data?symbol=${encodeURIComponent(symbol)}`);
                    const priceHistory = await priceResp.json();
                    if (priceHistory && priceHistory.length > 0) {
                        const latest = priceHistory[priceHistory.length - 1];
                        const previous = priceHistory.length > 1 ? priceHistory[priceHistory.length - 2] : latest;

                        priceData = {
                            current_price: latest.close,
                            previous_price: previous.close,
                            change: latest.close - previous.close,
                            change_percent: ((latest.close - previous.close) / previous.close * 100).toFixed(2),
                            volume: latest.volume,
                            high: latest.high,
                            low: latest.low,
                            open: latest.open,
                            date: latest.trade_date,
                            history_days: priceHistory.length
                        };
                    }
                } catch (e) {
                    console.warn('价格数据获取失败:', e);
                }

                // 收集新闻数据
                const newsData = window.currentNews || [];

                console.log('=== AI分析数据汇总 ===');
                console.log('指标数据:', indicatorValues);
                console.log('指标汇总:', indicatorSummary);
                console.log('价格数据:', priceData);
                console.log('新闻数据:', newsData);
                console.log('数据质量:', dataQuality);

                const resp = await fetch('/ai/analyze', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        symbol: symbol,
                        indicators: {
                            latest: indicatorValues,
                            summary: indicatorSummary,
                            count: Object.keys(indicatorValues).length,
                            data_source: 'real_adata_indicators',
                            data_quality: dataQuality
                        },
                        price_data: priceData,
                        news: {
                            data: newsData,
                            count: newsData.length,
                            source: 'market_news'
                        },
                        prompt: '请基于完整的技术指标、价格数据和新闻信息进行深度分析，并给出明确的买入/卖出/持仓建议'
                    })
                });

                const data = await resp.json();
                $('chat').innerHTML = `<div class="msg"><div class="role">AI</div><div>${data.text || '暂无分析结果'}</div></div>`;
            } catch (e) {
                console.error('AI分析失败:', e);
                $('chat').innerHTML = '<div class="msg"><div class="role">AI</div><div>❌ AI分析失败，请稍后重试</div></div>';
            }
        }

        // 自选股管理
        function ls() {
            try {
                return JSON.parse(localStorage.getItem('watch') || '[]');
            } catch {
                return [];
            }
        }

        function save(arr) {
            localStorage.setItem('watch', JSON.stringify(arr));
            renderWatch();
        }

        function addFromInput() {
            // 检查是否已选择股票
            if (!currentSym || !currentName) {
                alert('请先选择一个股票');
                return;
            }

            const arr = ls();
            // 检查是否已存在
            if (!arr.find(x => x.ts_code === currentSym)) {
                arr.push({
                    ts_code: currentSym,
                    name: currentName  // 使用正确的股票名称
                });
                save(arr);
                alert(`已将 ${currentName} (${currentSym.replace(/\\.(SH|SZ)$/, '')}) 加入自选股`);
            } else {
                alert(`${currentName} 已在自选股中`);
            }
        }

        function clearWatch() {
            if (confirm('确定要清空所有自选股吗？')) {
                save([]);
                alert('自选股已清空');
            }
        }

        // 清理无效的自选股数据
        function cleanWatch() {
            const arr = ls();
            const cleanedArr = arr.filter(x => x.ts_code && x.name);
            if (cleanedArr.length !== arr.length) {
                save(cleanedArr);
                console.log(`清理了 ${arr.length - cleanedArr.length} 个无效的自选股`);
            }
        }

        function renderWatch() {
            const arr = ls();
            console.log('渲染自选股列表:', arr);

            if (arr.length === 0) {
                $('watch').innerHTML = '<div style="color:var(--muted); font-size:12px; padding:10px;">暂无自选股</div>';
                return;
            }

            const html = arr.map(x => {
                const displayCode = x.ts_code.replace(/\\.(SH|SZ)$/, '');
                console.log(`渲染自选股: ${x.ts_code} - ${x.name}`);

                // 确保名称不为空
                const stockName = x.name || '未知股票';

                return `<div class="item" onclick="selectStock('${x.ts_code}', '${stockName}')">
                    <div style="font-weight:500; color:#e2e8f0;">${displayCode}</div>
                    <div style="font-size:12px; color:var(--muted); margin-top:2px;">${stockName}</div>
                </div>`;
            }).join('');

            console.log('生成的自选股HTML:', html);
            $('watch').innerHTML = html;
        }

        // 回车搜索
        $('q').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                search();
            }
        });

        // AI对话
        async function askAI() {
            if (!currentSym) {
                alert('请先选择一个股票');
                return;
            }

            const prompt = $('prompt').value.trim();

            // 如果输入为空，提供默认分析
            const userMessage = prompt || '请基于当前所有指标数据进行综合分析';

            try {
                // 显示用户消息
                $('chat').innerHTML += `<div class="msg"><div class="role">You</div><div>${userMessage}</div></div>`;

                // 显示AI思考状态
                $('chat').innerHTML += `<div class="msg" id="ai-thinking"><div class="role">AI</div><div>🤖 正在分析中...</div></div>`;
                $('chat').scrollTop = $('chat').scrollHeight;
                $('prompt').value = '';

                // 收集所有指标的真实数据
                const indicatorValues = {};
                console.log('window.indicatorData:', window.indicatorData);

                if (window.indicatorData) {
                    for (const [indicator, data] of Object.entries(window.indicatorData)) {
                        console.log(`处理指标 ${indicator}:`, data);
                        if (data && data.length > 0) {
                            const latest = data[data.length - 1];
                            const previousValue = data.length >= 2 ? data[data.length - 2].v : latest.v;

                            indicatorValues[indicator] = {
                                value: latest.v || latest.prob_up || 0,
                                date: latest.t || 'unknown',
                                trend: data.length >= 2 ?
                                    (latest.v > previousValue ? 'up' : (latest.v < previousValue ? 'down' : 'stable')) : 'stable'
                            };
                            console.log(`${indicator} 处理结果:`, indicatorValues[indicator]);
                        }
                    }
                } else {
                    console.warn('window.indicatorData 未定义或为空');
                }

                console.log('AI对话使用的真实指标数据:', indicatorValues);
                console.log('指标数量:', Object.keys(indicatorValues).length);

                // 收集新闻数据
                const newsData = window.currentNews || [];
                console.log('AI分析使用的新闻数据:', newsData);

                const resp = await fetch('/ai/analyze', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        symbol: currentSym,
                        prompt: prompt,
                        indicators: {
                            latest: indicatorValues,
                            count: Object.keys(indicatorValues).length,
                            data_source: 'real_adata_indicators',
                            analysis_type: 'conversation'
                        },
                        news: {
                            data: newsData,
                            count: newsData.length,
                            source: 'market_news'
                        }
                    })
                });

                const data = await resp.json();

                // 移除思考状态，显示AI回复
                const thinkingMsg = document.getElementById('ai-thinking');
                if (thinkingMsg) {
                    thinkingMsg.remove();
                }

                $('chat').innerHTML += `<div class="msg"><div class="role">AI</div><div>${data.text}</div></div>`;
                $('chat').scrollTop = $('chat').scrollHeight;

            } catch (e) {
                console.error('AI分析失败:', e);

                // 移除思考状态
                const thinkingMsg = document.getElementById('ai-thinking');
                if (thinkingMsg) {
                    thinkingMsg.remove();
                }

                // 显示详细错误信息
                let errorMsg = '❌ 分析失败';
                if (e.message) {
                    errorMsg += `: ${e.message}`;
                }

                $('chat').innerHTML += `<div class="msg"><div class="role">AI</div><div>${errorMsg}</div></div>`;
                $('chat').scrollTop = $('chat').scrollHeight;
            }
        }

        // 页面加载时清理并渲染自选股
        cleanWatch();
        renderWatch();
    </script>
</body>
</html>
        """)

    # 模拟炒股页面
    @app.get("/trading")
    def trading_page():
        """模拟炒股页面"""
        return HTMLResponse("""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>模拟炒股系统 - OpenBB-CN</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .panel { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .account-summary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn:hover { opacity: 0.9; }
        .form-group { margin-bottom: 15px; }
        .form-control { width: 100%; padding: 8px; border: 1px solid #d1d5db; border-radius: 4px; }
        .nav { margin-bottom: 20px; }
        .nav a { color: #3b82f6; text-decoration: none; margin-right: 20px; }
        .nav a:hover { text-decoration: underline; }
        .result { margin-top: 10px; padding: 10px; border-radius: 4px; }
        .success { background: #ecfdf5; color: #065f46; border: 1px solid #a7f3d0; }
        .error { background: #fef2f2; color: #991b1b; border: 1px solid #fecaca; }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav">
            <a href="/app">← 返回主页</a>
        </div>

        <div class="header">
            <h1>📈 模拟炒股系统</h1>
            <p>基于AI经验学习的智能交易平台，初始资金10万元</p>
        </div>

        <div class="grid">
            <!-- 账户概览 -->
            <div class="panel account-summary">
                <h3>💰 账户概览</h3>
                <div>总资产: ¥<span id="total-value">100,000</span></div>
                <div>现金: ¥<span id="cash">100,000</span></div>
                <div>持仓市值: ¥<span id="position-value">0</span></div>
                <div>盈亏: <span id="pnl">¥0 (0.00%)</span></div>
            </div>

            <!-- 交易操作 -->
            <div class="panel">
                <h3>💼 交易操作</h3>
                <div class="form-group">
                    <label>股票代码:</label>
                    <input type="text" id="trade-symbol" class="form-control" placeholder="如: 000001 或 600519">
                </div>
                <div class="form-group">
                    <label>交易金额:</label>
                    <input type="number" id="trade-amount" class="form-control" placeholder="如: 10000">
                </div>
                <div style="display: flex; gap: 10px;">
                    <button class="btn btn-success" onclick="buyStock()" style="flex: 1;">买入</button>
                    <button class="btn btn-danger" onclick="sellStock()" style="flex: 1;">卖出</button>
                </div>
                <div id="trade-result"></div>
            </div>
        </div>

        <div class="grid" style="margin-top: 20px;">
            <!-- 策略配置 -->
            <div class="panel">
                <h3>🤖 智能策略</h3>
                <div class="form-group">
                    <label><input type="checkbox" id="enable-pso" onchange="togglePSO()"> 启用粒子群选股</label>
                </div>
                <div class="form-group">
                    <label><input type="checkbox" id="enable-ga" onchange="toggleGA()"> 启用遗传算法优化</label>
                </div>
                <div class="form-group">
                    <label>交易间隔:
                        <select id="trade-interval" style="margin-left: 8px;">
                            <option value="30">30秒</option>
                            <option value="60">1分钟</option>
                            <option value="120">2分钟</option>
                            <option value="300">5分钟</option>
                        </select>
                    </label>
                </div>
                <button id="auto-trade-btn" class="btn btn-primary" onclick="toggleAutoTrading()">🤖 自动模拟炒股</button>
                <div id="auto-trade-status" style="margin-top: 8px; font-size: 14px; font-weight: bold;">
                    状态: <span id="status-text" style="color: #ef4444;">已停止</span>
                </div>
                <p style="font-size: 12px; color: #666; margin-top: 8px;">
                    系统将根据启用的策略和设定间隔自动执行买卖操作
                </p>
            </div>

            <!-- 持仓信息 -->
            <div class="panel">
                <h3>📊 持仓信息</h3>
                <div id="positions">暂无持仓</div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let autoTradingTimer = null;
        let isAutoTradingRunning = false;

        // 快捷分析功能 - 增强版
        function quickAnalysis(type) {
            if (!currentSym) {
                alert('请先选择一个股票');
                return;
            }

            console.log(`快捷分析: ${type}`);

            let prompt = '';
            let analysisTitle = '';

            switch (type) {
                case 'technical':
                    prompt = '请基于当前所有技术指标进行专业技术分析：1）趋势判断：分析主要趋势方向（上升/下降/横盘）；2）关键位置：识别重要的支撑位和阻力位；3）技术信号：解读各项技术指标的买卖信号；4）量价关系：分析成交量与价格的配合情况；5）操作建议：给出明确的买入/卖出/持仓建议及理由。';
                    analysisTitle = '📊 技术分析';
                    break;
                case 'news':
                    prompt = '请通过政策新闻、行业新闻和当前股票相关新闻进行影响分析：1）政策影响：分析相关政策对该股票的利好或利空影响；2）行业动态：评估行业新闻对股票的潜在影响；3）公司新闻：解读公司最新公告和新闻的市场意义；4）市场情绪：判断新闻对投资者情绪的影响；5）投资建议：基于新闻面给出具体的投资操作建议。';
                    analysisTitle = '📰 新闻影响分析';
                    break;
                case 'risk':
                    prompt = '请根据当前股价进行全面风险评估：1）价格风险：分析当前价位的高低风险；2）估值风险：评估股票是否存在高估或低估；3）市场风险：分析大盘环境对个股的影响；4）流动性风险：评估股票的交易活跃度和变现能力；5）投资建议：基于风险评估给出仓位管理和投资建议。';
                    analysisTitle = '⚠️ 风险评估';
                    break;
                case 'strategy':
                    prompt = '请根据短期操作经验给出投资策略建议：1）短线机会：识别短期内的交易机会；2）操作节奏：建议合适的买卖时机和操作频率；3）仓位管理：给出具体的仓位分配建议；4）止盈止损：设定明确的止盈止损点位；5）实战策略：提供可执行的短期操作方案和注意事项。';
                    analysisTitle = '🎯 投资策略';
                    break;
                default:
                    prompt = '请进行综合分析';
                    analysisTitle = '📋 综合分析';
            }

            // 设置提示词
            $('prompt').value = prompt;

            // 显示分析类型
            $('chat').innerHTML += `<div class="msg"><div class="role">System</div><div>${analysisTitle} 开始...</div></div>`;
            $('chat').scrollTop = $('chat').scrollHeight;

            console.log(`设置提示词: ${prompt}`);

            // 调用AI分析
            askAI();
        }





        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateAccountInfo();
            setInterval(updateAccountInfo, 30000);

            // 添加回车键发送功能
            const promptTextarea = $('prompt');
            if (promptTextarea) {
                promptTextarea.addEventListener('keydown', function(e) {
                    // Ctrl+Enter 或 Shift+Enter 换行，单独Enter发送
                    if (e.key === 'Enter' && !e.ctrlKey && !e.shiftKey) {
                        e.preventDefault();
                        askAI();
                    }
                });
            }
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (isAutoTradingRunning) {
                stopAutoTrading();
            }
        });

        // 更新账户信息
        async function updateAccountInfo() {
            try {
                const resp = await fetch('/smart/account');
                const data = await resp.json();

                document.getElementById('total-value').textContent = data.total_value.toLocaleString();
                document.getElementById('cash').textContent = data.cash.toLocaleString();
                document.getElementById('position-value').textContent = data.market_value.toLocaleString();

                const pnlElement = document.getElementById('pnl');
                const pnlColor = data.total_pnl >= 0 ? '#10b981' : '#ef4444';
                pnlElement.innerHTML = '¥' + data.total_pnl.toLocaleString() + ' (' + data.total_return.toFixed(2) + '%)';
                pnlElement.style.color = pnlColor;

                // 更新持仓信息
                updatePositions(data.positions);

            } catch (e) {
                console.error('更新账户信息失败:', e);
            }
        }

        // 更新持仓信息
        function updatePositions(positions) {
            const positionsDiv = document.getElementById('positions');
            if (!positions || positions.length === 0) {
                positionsDiv.innerHTML = '暂无持仓';
                return;
            }

            let html = '';
            positions.forEach(pos => {
                const pnlClass = pos.pnl >= 0 ? 'profit' : 'loss';
                html += '<div style="border-bottom: 1px solid #eee; padding: 5px 0;">';
                html += pos.symbol + ': ' + pos.quantity + '股 ';
                html += '<span class="' + pnlClass + '">' + pos.pnl_percent.toFixed(2) + '%</span>';
                html += '</div>';
            });
            positionsDiv.innerHTML = html;
        }

        // 买入股票
        async function buyStock() {
            const symbol = document.getElementById('trade-symbol').value;
            const amount = parseFloat(document.getElementById('trade-amount').value);

            if (!symbol || !amount) {
                showResult('请输入股票代码和金额', 'error');
                return;
            }

            try {
                const resp = await fetch('/smart/buy', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbol, amount, strategy: 'manual' })
                });
                const data = await resp.json();

                if (data.success) {
                    showResult(data.message, 'success');
                    document.getElementById('trade-symbol').value = '';
                    document.getElementById('trade-amount').value = '';
                    updateAccountInfo();
                } else {
                    showResult('买入失败: ' + data.message, 'error');
                }
            } catch (e) {
                showResult('买入错误: ' + e.message, 'error');
            }
        }

        // 卖出股票
        async function sellStock() {
            const symbol = document.getElementById('trade-symbol').value;

            if (!symbol) {
                showResult('请输入股票代码', 'error');
                return;
            }

            try {
                const resp = await fetch('/smart/sell', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbol, strategy: 'manual' })
                });
                const data = await resp.json();

                if (data.success) {
                    showResult(data.message, 'success');
                    document.getElementById('trade-symbol').value = '';
                    updateAccountInfo();
                } else {
                    showResult('卖出失败: ' + data.message, 'error');
                }
            } catch (e) {
                showResult('卖出错误: ' + e.message, 'error');
            }
        }

        // 切换自动交易状态
        function toggleAutoTrading() {
            if (isAutoTradingRunning) {
                stopAutoTrading();
            } else {
                startAutoTrading();
            }
        }

        // 开始自动交易
        function startAutoTrading() {
            if (isAutoTradingRunning) return;

            isAutoTradingRunning = true;
            updateAutoTradingUI();

            // 立即执行一次交易
            executeAutoTrade();

            // 设置定时器
            const interval = parseInt(document.getElementById('trade-interval').value) * 1000;
            autoTradingTimer = setInterval(executeAutoTrade, interval);

            showResult('🚀 自动模拟炒股已启动！', 'success');
        }

        // 停止自动交易
        function stopAutoTrading() {
            if (!isAutoTradingRunning) return;

            isAutoTradingRunning = false;

            if (autoTradingTimer) {
                clearInterval(autoTradingTimer);
                autoTradingTimer = null;
            }

            updateAutoTradingUI();
            showResult('⏹️ 自动模拟炒股已停止', 'error');
        }

        // 更新自动交易UI状态
        function updateAutoTradingUI() {
            const btn = document.getElementById('auto-trade-btn');
            const statusText = document.getElementById('status-text');

            if (isAutoTradingRunning) {
                btn.textContent = '⏹️ 停止自动模拟炒股';
                btn.className = 'btn btn-danger';
                statusText.textContent = '运行中';
                statusText.style.color = '#10b981';
            } else {
                btn.textContent = '🤖 自动模拟炒股';
                btn.className = 'btn btn-primary';
                statusText.textContent = '已停止';
                statusText.style.color = '#ef4444';
            }
        }

        // 执行一次自动交易
        async function executeAutoTrade() {
            if (!isAutoTradingRunning) return;

            try {
                // 显示交易状态
                showResult('🤖 AI正在分析市场并执行自动交易...', 'success');

                const resp = await fetch('/smart/rebalance', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await resp.json();

                if (data.success) {
                    // 显示详细的交易结果
                    const resultDiv = document.getElementById('trade-result');
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = data.message.replace(/\n/g, '<br>');
                    updateAccountInfo();

                    // 5秒后清除结果（如果还在自动交易中）
                    setTimeout(() => {
                        if (isAutoTradingRunning) {
                            resultDiv.innerHTML = '🤖 等待下次自动交易...';
                        } else {
                            resultDiv.innerHTML = '';
                            resultDiv.className = '';
                        }
                    }, 5000);
                } else {
                    showResult('自动交易失败: ' + data.message, 'error');
                    // 如果交易失败，可以选择停止自动交易
                    if (data.message.includes('现金不足') || data.message.includes('严重错误')) {
                        stopAutoTrading();
                    }
                }
            } catch (e) {
                showResult('自动交易错误: ' + e.message, 'error');
                // 网络错误时停止自动交易
                stopAutoTrading();
            }
        }

        // 策略开关
        function togglePSO() {
            const enabled = document.getElementById('enable-pso').checked;
            fetch('/smart/config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ pso_enabled: enabled })
            });
        }

        function toggleGA() {
            const enabled = document.getElementById('enable-ga').checked;
            fetch('/smart/config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ ga_enabled: enabled })
            });
        }

        // 显示操作结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('trade-result');
            resultDiv.className = 'result ' + type;
            resultDiv.textContent = message;

            setTimeout(() => {
                resultDiv.textContent = '';
                resultDiv.className = '';
            }, 5000);
        }
    </script>
</body>
</html>
        """)

    # 模拟炒股API
    # 全局变量存储账户信息（实际应用中应使用数据库）
    account_data = {
        "cash": 100000.0,  # 初始现金10万
        "positions": {},   # 持仓 {symbol: {quantity, avg_price, current_price}}
        "trades": [],      # 交易记录
        "config": {
            "pso_enabled": False,
            "ga_enabled": False,
            "rebalance_freq": "W-FRI",
            "auto_trading_enabled": False,
            "trade_interval": 30
        },
        "auto_trading_stats": {
            "total_auto_trades": 0,
            "successful_trades": 0,
            "failed_trades": 0,
            "start_time": None,
            "last_trade_time": None
        }
    }

    @app.get("/smart/account")
    def get_account():
        """获取账户信息"""
        try:
            # 计算持仓市值和总盈亏
            market_value = 0
            total_cost = 0

            for symbol, pos in account_data["positions"].items():
                # 模拟当前价格（实际应从API获取）
                current_price = pos["avg_price"] * (1 + np.random.uniform(-0.05, 0.05))
                pos["current_price"] = current_price

                position_value = pos["quantity"] * current_price
                position_cost = pos["quantity"] * pos["avg_price"]

                market_value += position_value
                total_cost += position_cost

            total_value = account_data["cash"] + market_value
            total_pnl = market_value - total_cost
            total_return = (total_pnl / total_cost * 100) if total_cost > 0 else 0

            # 格式化持仓信息
            positions = []
            for symbol, pos in account_data["positions"].items():
                pnl = (pos["current_price"] - pos["avg_price"]) * pos["quantity"]
                pnl_percent = (pos["current_price"] - pos["avg_price"]) / pos["avg_price"] * 100

                positions.append({
                    "symbol": symbol,
                    "quantity": pos["quantity"],
                    "avg_price": pos["avg_price"],
                    "current_price": pos["current_price"],
                    "pnl": pnl,
                    "pnl_percent": pnl_percent
                })

            return {
                "cash": account_data["cash"],
                "market_value": market_value,
                "total_value": total_value,
                "total_pnl": total_pnl,
                "total_return": total_return,
                "positions": positions,
                "recent_trades": account_data["trades"][-10:]  # 最近10笔交易
            }

        except Exception as e:
            logger.error(f"获取账户信息失败: {e}")
            raise HTTPException(status_code=500, detail="获取账户信息失败")

    @app.post("/smart/buy")
    def buy_stock(request: Dict[str, Any]):
        """买入股票"""
        try:
            symbol = request.get("symbol", "").upper()
            amount = float(request.get("amount", 0))
            strategy = request.get("strategy", "manual")

            if not symbol or amount <= 0:
                return {"success": False, "message": "请输入有效的股票代码和金额"}

            # 确保股票代码格式正确
            if not symbol.endswith(('.SH', '.SZ')):
                if symbol.startswith('6'):
                    symbol += '.SH'
                else:
                    symbol += '.SZ'

            # 检查现金是否足够
            if account_data["cash"] < amount:
                return {"success": False, "message": "现金不足"}

            # 模拟股票价格（实际应从API获取）
            price = 10 + np.random.uniform(5, 50)  # 模拟价格10-60元
            quantity = int(amount / price / 100) * 100  # 按手买入（100股为1手）

            if quantity == 0:
                return {"success": False, "message": "金额不足购买1手"}

            actual_amount = quantity * price

            # 更新持仓
            if symbol in account_data["positions"]:
                pos = account_data["positions"][symbol]
                total_quantity = pos["quantity"] + quantity
                total_cost = pos["quantity"] * pos["avg_price"] + actual_amount
                pos["avg_price"] = total_cost / total_quantity
                pos["quantity"] = total_quantity
            else:
                account_data["positions"][symbol] = {
                    "quantity": quantity,
                    "avg_price": price,
                    "current_price": price
                }

            # 更新现金
            account_data["cash"] -= actual_amount

            # 记录交易
            trade = {
                "timestamp": datetime.now().isoformat(),
                "action": "buy",
                "symbol": symbol,
                "quantity": quantity,
                "price": price,
                "amount": actual_amount,
                "strategy": strategy
            }
            account_data["trades"].append(trade)

            return {
                "success": True,
                "message": f"成功买入 {symbol} {quantity}股，成交价 ¥{price:.2f}，总金额 ¥{actual_amount:.2f}"
            }

        except Exception as e:
            logger.error(f"买入股票失败: {e}")
            return {"success": False, "message": f"买入失败: {str(e)}"}

    @app.post("/smart/sell")
    def sell_stock(request: Dict[str, Any]):
        """卖出股票"""
        try:
            symbol = request.get("symbol", "").upper()
            strategy = request.get("strategy", "manual")

            if not symbol:
                return {"success": False, "message": "请输入股票代码"}

            # 确保股票代码格式正确
            if not symbol.endswith(('.SH', '.SZ')):
                if symbol.startswith('6'):
                    symbol += '.SH'
                else:
                    symbol += '.SZ'

            # 检查是否有持仓
            if symbol not in account_data["positions"]:
                return {"success": False, "message": "没有该股票的持仓"}

            pos = account_data["positions"][symbol]
            quantity = pos["quantity"]

            # 模拟当前价格
            current_price = pos["avg_price"] * (1 + np.random.uniform(-0.05, 0.05))
            amount = quantity * current_price

            # 更新现金
            account_data["cash"] += amount

            # 删除持仓
            del account_data["positions"][symbol]

            # 记录交易
            trade = {
                "timestamp": datetime.now().isoformat(),
                "action": "sell",
                "symbol": symbol,
                "quantity": quantity,
                "price": current_price,
                "amount": amount,
                "strategy": strategy
            }
            account_data["trades"].append(trade)

            pnl = (current_price - pos["avg_price"]) * quantity
            pnl_percent = (current_price - pos["avg_price"]) / pos["avg_price"] * 100

            return {
                "success": True,
                "message": f"成功卖出 {symbol} {quantity}股，成交价 ¥{current_price:.2f}，盈亏 ¥{pnl:.2f} ({pnl_percent:+.2f}%)"
            }

        except Exception as e:
            logger.error(f"卖出股票失败: {e}")
            return {"success": False, "message": f"卖出失败: {str(e)}"}

    @app.post("/smart/rebalance")
    def auto_trading():
        """自动模拟炒股"""
        try:
            # 更新统计信息
            account_data["auto_trading_stats"]["total_auto_trades"] += 1
            account_data["auto_trading_stats"]["last_trade_time"] = datetime.now().isoformat()

            actions = []
            total_trades = 0

            # 检查现金是否充足
            if account_data["cash"] < 1000:
                account_data["auto_trading_stats"]["failed_trades"] += 1
                return {
                    "success": False,
                    "message": "现金不足1000元，无法继续自动交易。建议停止自动交易或卖出部分持仓。"
                }

            # 模拟股票池
            stock_pool = [
                {"symbol": "000001.SZ", "name": "平安银行"},
                {"symbol": "600519.SH", "name": "贵州茅台"},
                {"symbol": "000002.SZ", "name": "万科A"},
                {"symbol": "600036.SH", "name": "招商银行"},
                {"symbol": "000858.SZ", "name": "五粮液"},
                {"symbol": "600276.SH", "name": "恒瑞医药"}
            ]

            # 如果启用了PSO算法，使用粒子群优化选股
            if account_data["config"]["pso_enabled"]:
                actions.append("🔍 粒子群优化选股")
                # 模拟PSO选出的优质股票
                selected_stocks = np.random.choice(len(stock_pool), size=min(3, len(stock_pool)), replace=False)
                for idx in selected_stocks:
                    stock = stock_pool[idx]
                    # 模拟买入决策
                    if np.random.random() > 0.3:  # 70%概率买入
                        buy_amount = np.random.uniform(5000, 15000)  # 随机买入金额
                        if account_data["cash"] >= buy_amount:
                            # 执行买入
                            price = 10 + np.random.uniform(5, 50)
                            quantity = int(buy_amount / price / 100) * 100
                            if quantity > 0:
                                actual_amount = quantity * price

                                # 更新持仓
                                if stock["symbol"] in account_data["positions"]:
                                    pos = account_data["positions"][stock["symbol"]]
                                    total_quantity = pos["quantity"] + quantity
                                    total_cost = pos["quantity"] * pos["avg_price"] + actual_amount
                                    pos["avg_price"] = total_cost / total_quantity
                                    pos["quantity"] = total_quantity
                                else:
                                    account_data["positions"][stock["symbol"]] = {
                                        "quantity": quantity,
                                        "avg_price": price,
                                        "current_price": price
                                    }

                                account_data["cash"] -= actual_amount

                                # 记录交易
                                trade = {
                                    "timestamp": datetime.now().isoformat(),
                                    "action": "buy",
                                    "symbol": stock["symbol"],
                                    "quantity": quantity,
                                    "price": price,
                                    "amount": actual_amount,
                                    "strategy": "PSO自动选股"
                                }
                                account_data["trades"].append(trade)
                                total_trades += 1
                                actions.append(f"📈 买入 {stock['name']} {quantity}股")

            # 如果启用了GA算法，优化交易参数
            if account_data["config"]["ga_enabled"]:
                actions.append("🧬 遗传算法优化交易参数")

                # GA算法参数优化（模拟遗传算法的进化过程）
                # 生成多个参数组合（染色体）
                chromosomes = [
                    {"stop_loss": -0.05, "take_profit": 0.08, "fitness": 0},
                    {"stop_loss": -0.03, "take_profit": 0.10, "fitness": 0},
                    {"stop_loss": -0.07, "take_profit": 0.06, "fitness": 0},
                    {"stop_loss": -0.04, "take_profit": 0.12, "fitness": 0}
                ]

                # 计算适应度（基于历史表现）
                for chromosome in chromosomes:
                    # 模拟适应度计算（实际应基于历史回测结果）
                    chromosome["fitness"] = np.random.uniform(0.5, 1.5)

                # 选择最优参数
                best_chromosome = max(chromosomes, key=lambda x: x["fitness"])
                stop_loss_threshold = best_chromosome["stop_loss"]
                take_profit_threshold = best_chromosome["take_profit"]

                actions.append(f"📊 GA优化参数: 止损{stop_loss_threshold*100:.1f}%, 止盈{take_profit_threshold*100:.1f}%")

                # 使用优化后的参数进行卖出决策
                positions_to_sell = []
                for symbol, pos in account_data["positions"].items():
                    # 模拟当前价格
                    current_price = pos["avg_price"] * (1 + np.random.uniform(-0.1, 0.15))
                    pos["current_price"] = current_price

                    # 使用GA优化的参数
                    profit_rate = (current_price - pos["avg_price"]) / pos["avg_price"]
                    if profit_rate > take_profit_threshold or profit_rate < stop_loss_threshold:
                        positions_to_sell.append((symbol, pos, profit_rate))

                # 执行卖出
                for symbol, pos, profit_rate in positions_to_sell:
                    quantity = pos["quantity"]
                    current_price = pos["current_price"]
                    amount = quantity * current_price

                    account_data["cash"] += amount
                    del account_data["positions"][symbol]

                    # 记录交易
                    trade = {
                        "timestamp": datetime.now().isoformat(),
                        "action": "sell",
                        "symbol": symbol,
                        "quantity": quantity,
                        "price": current_price,
                        "amount": amount,
                        "strategy": "GA优化卖出"
                    }
                    account_data["trades"].append(trade)
                    total_trades += 1

                    stock_name = next((s["name"] for s in stock_pool if s["symbol"] == symbol), symbol)
                    profit_text = f"{profit_rate*100:+.1f}%"
                    actions.append(f"📉 卖出 {stock_name} {quantity}股 ({profit_text})")

            # 如果没有启用算法，使用基础技术指标策略
            if not account_data["config"]["pso_enabled"] and not account_data["config"]["ga_enabled"]:
                actions.append("📊 基于技术指标的自动交易")
                # 随机选择1-2只股票进行交易
                selected_count = np.random.randint(1, 3)
                selected_stocks = np.random.choice(len(stock_pool), size=selected_count, replace=False)

                for idx in selected_stocks:
                    stock = stock_pool[idx]
                    # 50%概率买入，50%概率卖出（如果有持仓）
                    if np.random.random() > 0.5:  # 买入
                        buy_amount = np.random.uniform(3000, 10000)
                        if account_data["cash"] >= buy_amount:
                            price = 10 + np.random.uniform(5, 50)
                            quantity = int(buy_amount / price / 100) * 100
                            if quantity > 0:
                                actual_amount = quantity * price

                                if stock["symbol"] in account_data["positions"]:
                                    pos = account_data["positions"][stock["symbol"]]
                                    total_quantity = pos["quantity"] + quantity
                                    total_cost = pos["quantity"] * pos["avg_price"] + actual_amount
                                    pos["avg_price"] = total_cost / total_quantity
                                    pos["quantity"] = total_quantity
                                else:
                                    account_data["positions"][stock["symbol"]] = {
                                        "quantity": quantity,
                                        "avg_price": price,
                                        "current_price": price
                                    }

                                account_data["cash"] -= actual_amount

                                trade = {
                                    "timestamp": datetime.now().isoformat(),
                                    "action": "buy",
                                    "symbol": stock["symbol"],
                                    "quantity": quantity,
                                    "price": price,
                                    "amount": actual_amount,
                                    "strategy": "技术指标买入"
                                }
                                account_data["trades"].append(trade)
                                total_trades += 1
                                actions.append(f"📈 买入 {stock['name']} {quantity}股")

                    else:  # 卖出
                        if stock["symbol"] in account_data["positions"]:
                            pos = account_data["positions"][stock["symbol"]]
                            quantity = pos["quantity"]
                            current_price = pos["avg_price"] * (1 + np.random.uniform(-0.05, 0.1))
                            amount = quantity * current_price

                            account_data["cash"] += amount
                            del account_data["positions"][stock["symbol"]]

                            trade = {
                                "timestamp": datetime.now().isoformat(),
                                "action": "sell",
                                "symbol": stock["symbol"],
                                "quantity": quantity,
                                "price": current_price,
                                "amount": amount,
                                "strategy": "技术指标卖出"
                            }
                            account_data["trades"].append(trade)
                            total_trades += 1

                            profit_rate = (current_price - pos["avg_price"]) / pos["avg_price"]
                            profit_text = f"{profit_rate*100:+.1f}%"
                            actions.append(f"📉 卖出 {stock['name']} {quantity}股 ({profit_text})")

            if total_trades == 0:
                actions.append("💤 当前市场条件不适合交易，保持观望")
            else:
                account_data["auto_trading_stats"]["successful_trades"] += 1

            # 添加统计信息
            stats = account_data["auto_trading_stats"]
            stats_text = f"\n📊 统计: 总计{stats['total_auto_trades']}次，成功{stats['successful_trades']}次，失败{stats['failed_trades']}次"

            message = f"🤖 自动模拟炒股完成，执行了 {total_trades} 笔交易：\n" + "\n".join(actions) + stats_text

            return {"success": True, "message": message}

        except Exception as e:
            logger.error(f"自动模拟炒股失败: {e}")
            return {"success": False, "message": f"自动交易失败: {str(e)}"}

    @app.post("/smart/config")
    def update_config(request: Dict[str, Any]):
        """更新策略配置"""
        try:
            for key, value in request.items():
                if key in account_data["config"]:
                    account_data["config"][key] = value

            return {"success": True, "message": "配置更新成功"}

        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return {"success": False, "message": f"配置更新失败: {str(e)}"}

    @app.get("/smart/status")
    def get_auto_trading_status():
        """获取自动交易状态"""
        try:
            stats = account_data["auto_trading_stats"]
            return {
                "auto_trading_enabled": account_data["config"]["auto_trading_enabled"],
                "trade_interval": account_data["config"]["trade_interval"],
                "stats": stats
            }
        except Exception as e:
            logger.error(f"获取自动交易状态失败: {e}")
            return {"success": False, "message": f"获取状态失败: {str(e)}"}

    @app.post("/smart/toggle")
    def toggle_auto_trading(request: Dict[str, Any]):
        """切换自动交易状态"""
        try:
            enabled = request.get("enabled", False)
            account_data["config"]["auto_trading_enabled"] = enabled

            if enabled:
                account_data["auto_trading_stats"]["start_time"] = datetime.now().isoformat()
                message = "自动交易已启动"
            else:
                message = "自动交易已停止"

            return {"success": True, "message": message, "enabled": enabled}

        except Exception as e:
            logger.error(f"切换自动交易状态失败: {e}")
            return {"success": False, "message": f"状态切换失败: {str(e)}"}

    return app


def _is_nan(x):
    """检查是否为NaN"""
    try:
        return pd.isna(x)
    except Exception:
        return x is None


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
