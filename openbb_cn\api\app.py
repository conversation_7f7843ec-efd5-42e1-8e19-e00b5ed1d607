from __future__ import annotations

from fastapi import FastAPI, Query
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from ..sdk.obb import obb
from ..config import get_settings
from ..storage.db import init_db
from ..providers.tushare_provider import TushareProvider


class PriceResponse(BaseModel):
    symbol: str
    rows: int


class PriceBar(BaseModel):
    trade_date: str
    open: float | None
    high: float | None
    low: float | None
    close: float | None
    volume: float | None
    provider: str


def create_app() -> FastAPI:
    settings = get_settings()
    init_db()
    app = FastAPI(title="OpenBB-CN API", version="0.1.0")

    @app.get("/", response_class=HTMLResponse)
    def root():
        return """
<!doctype html>
<html>
  <head>
    <meta charset='utf-8' />
    <title>OpenBB-CN UI</title>
    <script src=\"https://cdn.plot.ly/plotly-2.35.2.min.js\"></script>
    <style>
      body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial; margin: 24px; }
      .row { display: flex; gap: 12px; align-items: center; flex-wrap: wrap; }
      input { padding: 6px 10px; }
      button { padding: 6px 12px; }
      #chart { width: 100%; max-width: 1100px; height: 520px; }
      #vol { width: 100%; max-width: 1100px; height: 160px; margin-top: 8px; }
    </style>
  </head>
  <body>
    <h2>OpenBB-CN 简易行情页面</h2>
    <div class=\"row\">
      <label>Symbol(ts_code)：</label>
      <input id=\"sym\" value=\"600519.SH\" />
      <label>Start(YYYYMMDD)：</label>
      <input id=\"start\" placeholder=\"20240101\" />
      <label>End(YYYYMMDD)：</label>
      <input id=\"end\" placeholder=\"20240630\" />
      <button onclick=\"loadData()\">查询</button>
    </div>
    <div id=\"chart\"></div>
    <div id=\"vol\"></div>
    <script>
      async function loadData(){
        const s = document.getElementById('sym').value.trim();
        const st = document.getElementById('start').value.trim();
        const ed = document.getElementById('end').value.trim();
        const qs = new URLSearchParams({ symbol: s });
        if(st) qs.append('start', st);
        if(ed) qs.append('end', ed);
        const resp = await fetch('/equity/price/historical/data?' + qs.toString());
        const rows = await resp.json();
        const dates = rows.map(r => r.trade_date);
        const open = rows.map(r => r.open);
        const high = rows.map(r => r.high);
        const low = rows.map(r => r.low);
        const close = rows.map(r => r.close);
        const vol = rows.map(r => r.volume);
        const candle = [{
          x: dates, open, high, low, close, type: 'candlestick', name: s,
        }];
        const layout = { margin: { t: 24, r: 16, b: 24, l: 40 } };
        Plotly.newPlot('chart', candle, layout, {responsive: true});
        Plotly.newPlot('vol', [{ x: dates, y: vol, type: 'bar', name: 'volume' }], { margin: { t: 10 } }, {responsive:true});
      }
      loadData();
    </script>
  </body>
</html>
"""

    @app.get("/workspace", response_class=HTMLResponse)
    def workspace():
        return """
<!doctype html>
<html>
  <head>
    <meta charset='utf-8' />
    <title>OpenBB-CN Workspace</title>
    <script src=\"https://cdn.plot.ly/plotly-2.35.2.min.js\"></script>
    <style>
      body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial; margin: 0; }
      .wrap { display: grid; grid-template-columns: 360px 1fr; height: 100vh; }
      .left { border-right: 1px solid #eee; padding: 16px; overflow: auto; }
      .right { padding: 16px; overflow: auto; }
      input, button { padding: 6px 10px; }
      .stock { cursor: pointer; padding: 6px; border-bottom: 1px dashed #eee; }
      #chart { width: 100%; height: 420px; }
      #vol { width: 100%; height: 140px; }
      textarea { width: 100%; height: 140px; }
      pre { white-space: pre-wrap; background: #fafafa; padding: 8px; border: 1px solid #eee; }
    </style>
  </head>
  <body>
    <div class=\"wrap\">
      <div class=\"left\">
        <h3>股票搜索</h3>
        <div>
          <input id=\"q\" placeholder=\"输入代码或名称\" />
          <button onclick=\"search()\">搜索</button>
        </div>
        <div id=\"list\"></div>
      </div>
      <div class=\"right\">
        <h3 id=\"title\">AI 分析</h3>
        <div id=\"chart\"></div>
        <div id=\"vol\"></div>
        <h4>估值倍数</h4>
        <div id=\"ratio\" style=\"width:100%;height:260px;\"></div>
        <h4>指标</h4>
        <pre id=\"ind\"></pre>
        <h4>AI 结论</h4>
        <pre id=\"ai\"></pre>
      </div>
    </div>
    <script>
      async function search(){
        const q = document.getElementById('q').value.trim();
        const resp = await fetch('/search?query=' + encodeURIComponent(q));
        const data = await resp.json();
        const list = document.getElementById('list');
        list.innerHTML = '';
        data.forEach(item => {
          const div = document.createElement('div');
          div.className = 'stock';
          div.textContent = `${item.ts_code} ${item.name} ${item.industry||''}`;
          div.onclick = () => load(item.ts_code, item.name);
          list.appendChild(div);
        });
      }
      async function load(sym, name){
        document.getElementById('title').textContent = `AI 分析 - ${sym} ${name||''}`;
        const resp = await fetch('/equity/price/historical/data?symbol=' + encodeURIComponent(sym));
        const rows = await resp.json();
        const dates = rows.map(r => r.trade_date);
        const open = rows.map(r => r.open);
        const high = rows.map(r => r.high);
        const low = rows.map(r => r.low);
        const close = rows.map(r => r.close);
        const vol = rows.map(r => r.volume);
        Plotly.newPlot('chart', [{ x: dates, open, high, low, close, type: 'candlestick', name: sym }], { margin: { t: 24, r: 16, b: 24, l: 40 } }, {responsive: true});
        Plotly.newPlot('vol', [{ x: dates, y: vol, type: 'bar', name: 'volume' }], { margin: { t: 10 } }, {responsive:true});
        // 估值倍数
        const r = await fetch('/equity/basic/ratios?symbol=' + encodeURIComponent(sym));
        const rr = await r.json();
        const rdates = rr.map(x=>x.trade_date);
        Plotly.newPlot('ratio', [
          { x: rdates, y: rr.map(x=>x.pe), name: 'PE', type: 'scatter', mode: 'lines' },
          { x: rdates, y: rr.map(x=>x.pb), name: 'PB', type: 'scatter', mode: 'lines' },
          { x: rdates, y: rr.map(x=>x.ps), name: 'PS', type: 'scatter', mode: 'lines' },
        ], { margin: { t: 10 }, legend: { orientation: 'h' } }, {responsive:true});
        const indResp = await fetch('/equity/price/indicators?symbol=' + encodeURIComponent(sym));
        const ind = await indResp.json();
        document.getElementById('ind').textContent = JSON.stringify(ind, null, 2);
        const aiResp = await fetch('/ai/analyze', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ symbol: sym, indicators: ind }) });
        const ai = await aiResp.json();
        document.getElementById('ai').textContent = ai.text || JSON.stringify(ai);
      }
    </script>
  </body>
</html>
"""

    @app.get("/equity/price/historical", response_model=PriceResponse)
    def historical(
        symbol: str = Query(..., description="ts_code, 如 600519.SH/000001.SZ"),
        start: str | None = Query(None, description="开始日期 YYYYMMDD"),
        end: str | None = Query(None, description="结束日期 YYYYMMDD"),
    ):
        df = obb.equity.price.historical(symbol=symbol, start=start, end=end)
        return PriceResponse(symbol=symbol, rows=len(df))

    @app.get("/equity/price/historical/data", response_model=list[PriceBar])
    def historical_data(
        symbol: str = Query(..., description="ts_code, 如 600519.SH/000001.SZ"),
        start: str | None = Query(None, description="开始日期 YYYYMMDD"),
        end: str | None = Query(None, description="结束日期 YYYYMMDD"),
    ):
        df = obb.equity.price.historical(symbol=symbol, start=start, end=end)
        records = [
            PriceBar(
                trade_date=str(row.trade_date),
                open=(None if pd_isna(row.open) else float(row.open)),
                high=(None if pd_isna(row.high) else float(row.high)),
                low=(None if pd_isna(row.low) else float(row.low)),
                close=(None if pd_isna(row.close) else float(row.close)),
                volume=(None if pd_isna(row.volume) else float(row.volume)),
                provider=row.provider,
            )
            for row in df.itertuples(index=False)
        ]
        return records

    @app.get("/equity/basic/ratios")
    def basic_ratios(symbol: str, start: str | None = None, end: str | None = None):
        prov = TushareProvider()
        df = prov.daily_basic(symbol=symbol, start=start, end=end)
        return [
            {
                "trade_date": str(r.trade_date),
                "pe": (None if pd_isna(r.pe) else float(r.pe)),
                "pb": (None if pd_isna(r.pb) else float(r.pb)),
                "ps": (None if pd_isna(r.ps) else float(r.ps)),
            }
            for r in df.itertuples(index=False)
        ]

    @app.get("/search")
    def search(query: str, limit: int = 20):
        prov = TushareProvider()
        df = prov.search(query=query, limit=limit)
        return df.to_dict(orient="records")

    @app.get("/equity/price/indicators")
    def indicators(symbol: str):
        import pandas as pd
        df = obb.equity.price.historical(symbol=symbol)
        if df.empty:
            return {"symbol": symbol, "size": 0}
        s = df.tail(120).copy()
        s = s.reset_index(drop=True)
        s["ma5"] = s["close"].rolling(5).mean()
        s["ma20"] = s["close"].rolling(20).mean()
        s["ma60"] = s["close"].rolling(60).mean()
        # 简易动量
        s["ret5"] = s["close"].pct_change(5)
        s["ret20"] = s["close"].pct_change(20)
        latest = s.iloc[-1].to_dict()
        return {
            "symbol": symbol,
            "size": int(len(df)),
            "latest": {
                k: (None if pd_isna(v) else float(v))
                for k, v in {
                    "close": latest.get("close"),
                    "ma5": latest.get("ma5"),
                    "ma20": latest.get("ma20"),
                    "ma60": latest.get("ma60"),
                    "ret5": latest.get("ret5"),
                    "ret20": latest.get("ret20"),
                }.items()
            },
        }

    @app.post("/ai/analyze")
    def ai_analyze(payload: dict):
        symbol = payload.get("symbol")
        indicators = payload.get("indicators", {})
        settings = get_settings()
        api_key = settings.bailian_api_key
        base_url = settings.bailian_base_url
        model = settings.bailian_model
        if api_key:
            try:
                import requests
                sys_prompt = (
                    "你是专业的A股/港股/期货分析师。基于提供的指标输出结构化观点：风险、趋势、估值、交易建议。"
                )
                user_content = {
                    "symbol": symbol,
                    "indicators": indicators,
                }
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json",
                }
                body = {
                    "model": model,
                    "messages": [
                        {"role": "system", "content": sys_prompt},
                        {"role": "user", "content": str(user_content)},
                    ],
                }
                resp = requests.post(f"{base_url}/chat/completions", headers=headers, json=body, timeout=30)
                data = resp.json()
                text = data.get("choices", [{}])[0].get("message", {}).get("content", "")
                return {"symbol": symbol, "text": text or "", "indicators": indicators}
            except Exception as e:
                return {"symbol": symbol, "text": f"AI调用失败：{e}", "indicators": indicators}
        # 无 Key 回退本地规则
        latest = indicators.get("latest") or {}
        ma5 = latest.get("ma5")
        ma20 = latest.get("ma20")
        close = latest.get("close")
        ret5 = latest.get("ret5")
        ret20 = latest.get("ret20")
        signals = []
        if ma5 and ma20 and close:
            if ma5 > ma20 and close > ma5:
                signals.append("短期趋势偏强（MA5>MA20 且收盘价在MA5之上）")
            elif ma5 < ma20 and close < ma5:
                signals.append("短期趋势偏弱（MA5<MA20 且收盘价在MA5之下）")
        if ret5 is not None:
            signals.append(f"近5日涨跌幅：{round(ret5*100,2)}%")
        if ret20 is not None:
            signals.append(f"近20日涨跌幅：{round(ret20*100,2)}%")
        text = f"【{symbol}】基于简易指标的自动分析：\n- " + "\n- ".join(signals or ["数据不足，无法生成结论"]) + "\n(未配置大模型Key，已使用本地规则)"
        return {"symbol": symbol, "text": text, "indicators": indicators}

    return app


app = create_app()


def pd_isna(x):
    try:
        import pandas as pd
        return pd.isna(x)
    except Exception:
        return x is None


