"""
OpenBB-CN API 服务器
提供股票数据查询、技术指标计算、AI分析等功能
"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import pandas as pd
import numpy as np
from typing import Optional, List, Dict, Any
import json
import logging
from datetime import datetime, timedelta
import asyncio
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    app = FastAPI(
        title="OpenBB-CN API",
        description="中国股票数据分析平台",
        version="1.0.0"
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    @app.get("/")
    def root():
        """根路径重定向到主页面"""
        return HTMLResponse("""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>OpenBB-CN Platform</title>
            <meta http-equiv="refresh" content="0; url=/app">
        </head>
        <body>
            <p>正在跳转到 <a href="/app">OpenBB-CN Platform</a>...</p>
        </body>
        </html>
        """)
    
    @app.get("/health")
    def health_check():
        """健康检查"""
        return {"status": "ok", "timestamp": datetime.now().isoformat()}
    
    # 股票搜索API
    @app.get("/search")
    def search_stocks(query: str):
        """搜索股票"""
        try:
            # 模拟股票搜索结果
            mock_results = [
                {"ts_code": "000001.SZ", "name": "平安银行", "industry": "银行"},
                {"ts_code": "600519.SH", "name": "贵州茅台", "industry": "白酒"},
                {"ts_code": "000002.SZ", "name": "万科A", "industry": "房地产"},
            ]
            
            # 简单的模糊匹配
            results = []
            for stock in mock_results:
                if (query.lower() in stock["name"].lower() or 
                    query in stock["ts_code"] or
                    query.replace(".", "") in stock["ts_code"].replace(".", "")):
                    results.append(stock)
            
            return results[:10]  # 最多返回10个结果
            
        except Exception as e:
            logger.error(f"搜索股票失败: {e}")
            raise HTTPException(status_code=500, detail="搜索失败")
    
    # 股票历史数据API
    @app.get("/equity/price/historical/data")
    def get_historical_data(symbol: str, start_date: Optional[str] = None, end_date: Optional[str] = None):
        """获取股票历史数据"""
        try:
            # 生成模拟数据
            dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
            dates = dates[dates.weekday < 5]  # 只保留工作日
            
            # 生成随机价格数据
            np.random.seed(42)
            base_price = 100
            prices = []
            current_price = base_price
            
            for _ in range(len(dates)):
                change = np.random.normal(0, 0.02)  # 2%的日波动
                current_price *= (1 + change)
                prices.append(current_price)
            
            data = []
            for i, date in enumerate(dates):
                price = prices[i]
                high = price * (1 + abs(np.random.normal(0, 0.01)))
                low = price * (1 - abs(np.random.normal(0, 0.01)))
                open_price = prices[i-1] if i > 0 else price
                volume = np.random.randint(1000000, 10000000)
                
                data.append({
                    "trade_date": date.strftime("%Y-%m-%d"),
                    "open": round(open_price, 2),
                    "high": round(high, 2),
                    "low": round(low, 2),
                    "close": round(price, 2),
                    "volume": volume,
                    "amount": round(volume * price, 2)
                })
            
            return data[-252:]  # 返回最近一年的数据
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            raise HTTPException(status_code=500, detail="获取数据失败")
    
    # 估值倍数API
    @app.get("/equity/basic/ratios")
    def get_ratios(symbol: str):
        """获取估值倍数"""
        try:
            # 生成模拟估值数据
            dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='M')
            
            data = []
            for date in dates:
                data.append({
                    "trade_date": date.strftime("%Y-%m-%d"),
                    "pe": round(np.random.uniform(10, 30), 2),
                    "pb": round(np.random.uniform(1, 5), 2),
                    "ps": round(np.random.uniform(2, 8), 2),
                    "pcf": round(np.random.uniform(5, 15), 2)
                })
            
            return data
            
        except Exception as e:
            logger.error(f"获取估值数据失败: {e}")
            raise HTTPException(status_code=500, detail="获取数据失败")
    
    # 技术指标API
    @app.get("/equity/price/indicators")
    def get_indicators(symbol: str):
        """获取技术指标"""
        try:
            # 生成模拟技术指标数据
            latest = {
                "rsi": round(np.random.uniform(30, 70), 2),
                "macd": round(np.random.uniform(-2, 2), 4),
                "macd_signal": round(np.random.uniform(-2, 2), 4),
                "boll_upper": round(np.random.uniform(105, 115), 2),
                "boll_mid": round(np.random.uniform(95, 105), 2),
                "boll_lower": round(np.random.uniform(85, 95), 2),
                "kdj_k": round(np.random.uniform(20, 80), 2),
                "kdj_d": round(np.random.uniform(20, 80), 2),
                "kdj_j": round(np.random.uniform(0, 100), 2),
                "atr": round(np.random.uniform(1, 5), 2),
                "vwap": round(np.random.uniform(95, 105), 2)
            }
            
            return {"latest": latest}
            
        except Exception as e:
            logger.error(f"获取技术指标失败: {e}")
            raise HTTPException(status_code=500, detail="获取数据失败")
    
    # 自研指标API
    @app.get("/indicator/{indicator_name}")
    def get_custom_indicator(indicator_name: str, symbol: str):
        """获取自研指标"""
        try:
            # 生成模拟指标数据
            dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
            dates = dates[dates.weekday < 5]  # 只保留工作日
            
            data = []
            for date in dates:
                value = np.random.uniform(-1, 1) if indicator_name in ['vcm', 'tei', 'api', 'mtr'] else np.random.uniform(0, 2)
                data.append({
                    "t": date.strftime("%Y-%m-%d"),
                    "v": round(value, 4)
                })
            
            return data[-60:]  # 返回最近60天的数据
            
        except Exception as e:
            logger.error(f"获取自研指标失败: {e}")
            raise HTTPException(status_code=500, detail="获取数据失败")
    
    # BLCE指标特殊处理
    @app.get("/indicator/blce")
    def get_blce_indicator(symbol: str):
        """获取BLCE指标"""
        try:
            dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
            dates = dates[dates.weekday < 5]
            
            data = []
            for date in dates:
                data.append({
                    "t": date.strftime("%Y-%m-%d"),
                    "prob_up": round(np.random.uniform(0.2, 0.8), 3)
                })
            
            return data[-60:]
            
        except Exception as e:
            logger.error(f"获取BLCE指标失败: {e}")
            raise HTTPException(status_code=500, detail="获取数据失败")
    
    # AI分析API
    @app.post("/ai/analyze")
    def ai_analyze(request: Dict[str, Any]):
        """AI分析"""
        try:
            symbol = request.get("symbol", "")
            indicators = request.get("indicators", {})
            prompt = request.get("prompt", "")
            
            # 模拟AI分析结果
            analysis = f"""
            📊 {symbol} 技术分析报告
            
            🔍 技术指标概览：
            • RSI: {indicators.get('latest', {}).get('rsi', 'N/A')} - 处于中性区间
            • MACD: {indicators.get('latest', {}).get('macd', 'N/A')} - 趋势信号
            • 布林带: 价格在中轨附近波动
            
            📈 自研指标分析：
            • VCM: 放量顺势动量指标显示市场情绪
            • TEI: 趋势熵分指标反映价格稳定性
            • LVR: 流动性风险处于可控范围
            
            💡 投资建议：
            基于当前技术指标，建议保持观望态度，等待更明确的趋势信号。
            
            ⚠️ 风险提示：
            股市有风险，投资需谨慎。本分析仅供参考，不构成投资建议。
            """
            
            return {"text": analysis.strip()}

        except Exception as e:
            logger.error(f"AI分析失败: {e}")
            raise HTTPException(status_code=500, detail="分析失败")

    # 主页面
    @app.get("/app")
    def main_app():
        """主页面"""
        return HTMLResponse("""
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8' />
    <title>OpenBB-CN Platform</title>
    <script src="https://cdn.plot.ly/plotly-2.35.2.min.js"></script>
    <style>
        :root { --bg:#0f1115; --panel:#151822; --text:#eaeef2; --muted:#a0a6b1; --accent:#3b82f6; }
        html,body { margin:0; height:100%; background:var(--bg); color:var(--text); font-family: ui-sans-serif, system-ui, Segoe UI, Roboto, Helvetica, Arial; }
        .layout { display:grid; grid-template-columns: 300px 1fr 380px; grid-template-rows: 56px 1fr; height:100%; }
        .topbar { grid-column: 1 / 4; display:flex; align-items:center; gap:12px; padding:0 16px; background:#0c0e13; border-bottom:1px solid #1d2233; }
        .brand { font-weight:600; color:#8ab4ff; }
        .left { background:var(--panel); border-right:1px solid #20263a; padding:12px; overflow:auto; }
        .main { background:transparent; padding:12px; overflow:auto; }
        .right { background:var(--panel); border-left:1px solid #20263a; padding:12px; overflow:auto; }
        input, button, select, textarea { background:#0c0f16; border:1px solid #26304b; color:var(--text); padding:8px 10px; border-radius:6px; }
        button { background:#1f2937; cursor:pointer; }
        .row { display:flex; gap:8px; align-items:center; flex-wrap:wrap; }
        .watch { margin-top:12px; }
        .item { padding:10px 12px; border:1px solid #26304b; border-radius:8px; margin-bottom:8px; cursor:pointer; transition: all 0.2s ease; background: var(--panel); }
        .item:hover { border-color: #3b82f6; background: #1e293b; transform: translateX(2px); }
        .grid { display:grid; grid-template-columns: 1fr 1fr; gap:12px; }
        .panel { background:var(--panel); border:1px solid #20263a; border-radius:10px; padding:10px; }
        .panel h4 { margin:0 0 8px 0; color:#c8d3f5; }
        #kline { height:400px; }
        #volume { height:130px; }
        #ratio { height:220px; }
        .cards { display:grid; grid-template-columns: repeat(3,minmax(0,1fr)); gap:10px; }
        .card { background:#10131b; border:1px solid #26304b; border-left:6px solid #334155; padding:10px; border-radius:8px; }
        .good { border-left-color:#16a34a; }
        .warn { border-left-color:#f59e0b; }
        .bad { border-left-color:#ef4444; }
        .chatbox { height:260px; overflow:auto; background:#0c0f16; border:1px solid #26304b; padding:8px; border-radius:8px; }
        .msg { margin:6px 0; }
        .role { font-size:12px; color:var(--muted); }
    </style>
</head>
<body>
    <div class="layout">
        <div class="topbar">
            <div class="brand">OpenBB-CN</div>
            <div class="row">
                <input id="q" placeholder="输入股票名称(如:平安银行)或6位代码(如:000001)" />
                <button onclick="search()">🔍 搜索</button>
                <button onclick="addFromInput()">加入自选</button>
                <button onclick="clearWatch()">清空自选</button>
                <a href="/trading" style="color:#8ab4ff;text-decoration:none;margin-left:20px;padding:8px 12px;border:1px solid #26304b;border-radius:6px;">📈 模拟炒股</a>
            </div>
        </div>
        <div class="left">
            <h4>搜索结果</h4>
            <div id="results"></div>
            <h4 style="margin-top:10px">自选（本地）</h4>
            <div id="watch" class="watch"></div>
        </div>
        <div class="main">
            <div class="grid">
                <div class="panel">
                    <h4 id="title">K线</h4>
                    <div id="kline"></div>
                    <div id="volume"></div>
                </div>
                <div class="panel">
                    <h4>估值倍数</h4>
                    <div id="ratio"></div>
                    <h4>指标卡片</h4>
                    <div id="cards" class="cards"></div>
                </div>
            </div>
        </div>
        <div class="right">
            <h4>AI 分析</h4>
            <div id="chat" class="chatbox"></div>
            <div class="row" style="margin-top:8px">
                <textarea id="prompt" placeholder="想问AI的问题（可留空，默认生成结构化结论）" style="width:100%"></textarea>
            </div>
            <div class="row" style="margin-top:8px">
                <button onclick="askAI()">生成分析</button>
            </div>
        </div>
    </div>

    <script>
        const $ = id => document.getElementById(id);
        let currentSym = null;

        // 搜索功能
        async function search() {
            const q = $('q').value.trim();
            if (!q) return;

            try {
                const resp = await fetch('/search?query=' + encodeURIComponent(q));
                const data = await resp.json();

                const html = data.map(item => {
                    const displayCode = item.ts_code.replace(/\\.(SH|SZ)$/, '');
                    return `<div class="item" onclick="selectStock('${item.ts_code}', '${item.name}')">
                        <div style="font-weight:500;">${displayCode}</div>
                        <div style="font-size:12px;color:var(--muted);">${item.name}</div>
                    </div>`;
                }).join('');

                $('results').innerHTML = html;
            } catch (e) {
                $('results').innerHTML = '<div style="color:#ef4444;">搜索失败</div>';
            }
        }

        // 选择股票
        async function selectStock(symbol, name) {
            currentSym = symbol;
            $('title').textContent = `${name} (${symbol.replace(/\\.(SH|SZ)$/, '')})`;

            // 加载数据
            loadKlineData(symbol);
            loadRatioData(symbol);
            loadIndicators(symbol);
            loadAIAnalysis(symbol);
        }

        // 加载K线数据
        async function loadKlineData(symbol) {
            try {
                const resp = await fetch('/equity/price/historical/data?symbol=' + encodeURIComponent(symbol));
                const data = await resp.json();

                if (data && data.length > 0) {
                    const trace = {
                        x: data.map(d => d.trade_date),
                        close: data.map(d => d.close),
                        high: data.map(d => d.high),
                        low: data.map(d => d.low),
                        open: data.map(d => d.open),
                        type: 'candlestick'
                    };

                    Plotly.newPlot('kline', [trace], {
                        margin: { t: 24, r: 16, b: 24, l: 40 }
                    }, {responsive: true});

                    const volumeTrace = {
                        x: data.map(d => d.trade_date),
                        y: data.map(d => d.volume),
                        type: 'bar'
                    };

                    Plotly.newPlot('volume', [volumeTrace], {
                        margin: { t: 24, r: 16, b: 24, l: 40 }
                    }, {responsive: true});
                }
            } catch (e) {
                console.error('加载K线数据失败:', e);
            }
        }

        // 加载估值倍数
        async function loadRatioData(symbol) {
            try {
                const resp = await fetch('/equity/basic/ratios?symbol=' + encodeURIComponent(symbol));
                const data = await resp.json();

                if (data && data.length > 0) {
                    const dates = data.map(x => x.trade_date);
                    Plotly.newPlot('ratio', [
                        { x: dates, y: data.map(x => x.pe), name: 'PE', type: 'scatter', mode: 'lines' },
                        { x: dates, y: data.map(x => x.pb), name: 'PB', type: 'scatter', mode: 'lines' },
                        { x: dates, y: data.map(x => x.ps), name: 'PS', type: 'scatter', mode: 'lines' }
                    ], { margin: { t: 24, r: 16, b: 24, l: 40 } }, {responsive: true});
                }
            } catch (e) {
                console.error('加载估值数据失败:', e);
            }
        }

        // 加载指标
        async function loadIndicators(symbol) {
            const indicators = ['vcm', 'tei', 'lvr', 'api', 'mtr'];
            let html = '';

            for (const ind of indicators) {
                try {
                    const resp = await fetch(`/indicator/${ind}?symbol=${encodeURIComponent(symbol)}`);
                    const data = await resp.json();

                    if (data && data.length > 0) {
                        const latest = data[data.length - 1];
                        const value = latest.v || latest.prob_up;
                        html += `<div class="card">
                            <div style="color:#9fb3ff"><b>${ind.toUpperCase()}</b></div>
                            <div style="font-size:22px;margin:6px 0">${value ? value.toFixed(4) : 'N/A'}</div>
                        </div>`;
                    }
                } catch (e) {
                    html += `<div class="card">
                        <div style="color:#9fb3ff"><b>${ind.toUpperCase()}</b></div>
                        <div style="font-size:22px;margin:6px 0">加载失败</div>
                    </div>`;
                }
            }

            $('cards').innerHTML = html;
        }

        // 加载AI分析
        async function loadAIAnalysis(symbol) {
            try {
                $('chat').innerHTML = '<div class="msg"><div class="role">AI</div><div>🤖 分析中...</div></div>';

                const resp = await fetch('/ai/analyze', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbol })
                });

                const data = await resp.json();
                $('chat').innerHTML = `<div class="msg"><div class="role">AI</div><div>${data.text || '暂无分析结果'}</div></div>`;
            } catch (e) {
                $('chat').innerHTML = '<div class="msg"><div class="role">AI</div><div>❌ AI分析失败</div></div>';
            }
        }

        // 自选股管理
        function ls() {
            try {
                return JSON.parse(localStorage.getItem('watch') || '[]');
            } catch {
                return [];
            }
        }

        function save(arr) {
            localStorage.setItem('watch', JSON.stringify(arr));
            renderWatch();
        }

        function addFromInput() {
            const q = $('q').value.trim();
            if (!q) return;

            const arr = ls();
            if (!arr.find(x => x.ts_code === currentSym)) {
                arr.push({ ts_code: currentSym, name: q });
                save(arr);
            }
        }

        function clearWatch() {
            save([]);
        }

        function renderWatch() {
            const arr = ls();
            const html = arr.map(x => {
                const displayCode = x.ts_code.replace(/\\.(SH|SZ)$/, '');
                return `<div class="item" onclick="selectStock('${x.ts_code}', '${x.name}')">
                    <div style="font-weight:500;">${displayCode}</div>
                    <div style="font-size:12px;color:var(--muted);">${x.name}</div>
                </div>`;
            }).join('');
            $('watch').innerHTML = html;
        }

        // 回车搜索
        $('q').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                search();
            }
        });

        // AI分析
        async function askAI() {
            if (!currentSym) {
                alert('请先选择一个股票');
                return;
            }

            const prompt = $('prompt').value.trim();

            try {
                const resp = await fetch('/ai/analyze', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbol: currentSym, prompt })
                });

                const data = await resp.json();
                $('chat').innerHTML += `<div class="msg"><div class="role">You</div><div>${prompt || '生成分析'}</div></div>`;
                $('chat').innerHTML += `<div class="msg"><div class="role">AI</div><div>${data.text}</div></div>`;
                $('chat').scrollTop = $('chat').scrollHeight;
                $('prompt').value = '';
            } catch (e) {
                $('chat').innerHTML += '<div class="msg"><div class="role">AI</div><div>❌ 分析失败</div></div>';
            }
        }

        // 页面加载时渲染自选股
        renderWatch();
    </script>
</body>
</html>
        """)

    # 模拟炒股页面
    @app.get("/trading")
    def trading_page():
        """模拟炒股页面"""
        return HTMLResponse("""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>模拟炒股系统 - OpenBB-CN</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .panel { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .account-summary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn:hover { opacity: 0.9; }
        .form-group { margin-bottom: 15px; }
        .form-control { width: 100%; padding: 8px; border: 1px solid #d1d5db; border-radius: 4px; }
        .nav { margin-bottom: 20px; }
        .nav a { color: #3b82f6; text-decoration: none; margin-right: 20px; }
        .nav a:hover { text-decoration: underline; }
        .result { margin-top: 10px; padding: 10px; border-radius: 4px; }
        .success { background: #ecfdf5; color: #065f46; border: 1px solid #a7f3d0; }
        .error { background: #fef2f2; color: #991b1b; border: 1px solid #fecaca; }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav">
            <a href="/app">← 返回主页</a>
        </div>

        <div class="header">
            <h1>📈 模拟炒股系统</h1>
            <p>基于AI经验学习的智能交易平台，初始资金10万元</p>
        </div>

        <div class="grid">
            <!-- 账户概览 -->
            <div class="panel account-summary">
                <h3>💰 账户概览</h3>
                <div>总资产: ¥<span id="total-value">100,000</span></div>
                <div>现金: ¥<span id="cash">100,000</span></div>
                <div>持仓市值: ¥<span id="position-value">0</span></div>
                <div>盈亏: <span id="pnl">¥0 (0.00%)</span></div>
            </div>

            <!-- 交易操作 -->
            <div class="panel">
                <h3>💼 交易操作</h3>
                <div class="form-group">
                    <label>股票代码:</label>
                    <input type="text" id="trade-symbol" class="form-control" placeholder="如: 000001 或 600519">
                </div>
                <div class="form-group">
                    <label>交易金额:</label>
                    <input type="number" id="trade-amount" class="form-control" placeholder="如: 10000">
                </div>
                <div style="display: flex; gap: 10px;">
                    <button class="btn btn-success" onclick="buyStock()" style="flex: 1;">买入</button>
                    <button class="btn btn-danger" onclick="sellStock()" style="flex: 1;">卖出</button>
                </div>
                <div id="trade-result"></div>
            </div>
        </div>

        <div class="grid" style="margin-top: 20px;">
            <!-- 策略配置 -->
            <div class="panel">
                <h3>🤖 智能策略</h3>
                <div class="form-group">
                    <label><input type="checkbox" id="enable-pso" onchange="togglePSO()"> 启用粒子群选股</label>
                </div>
                <div class="form-group">
                    <label><input type="checkbox" id="enable-ga" onchange="toggleGA()"> 启用遗传算法优化</label>
                </div>
                <div class="form-group">
                    <label>交易间隔:
                        <select id="trade-interval" style="margin-left: 8px;">
                            <option value="30">30秒</option>
                            <option value="60">1分钟</option>
                            <option value="120">2分钟</option>
                            <option value="300">5分钟</option>
                        </select>
                    </label>
                </div>
                <button id="auto-trade-btn" class="btn btn-primary" onclick="toggleAutoTrading()">🤖 自动模拟炒股</button>
                <button class="btn btn-secondary" onclick="testFunction()" style="margin-left: 10px;">🔧 测试功能</button>
                <div id="auto-trade-status" style="margin-top: 8px; font-size: 14px; font-weight: bold;">
                    状态: <span id="status-text" style="color: #ef4444;">已停止</span>
                </div>
                <p style="font-size: 12px; color: #666; margin-top: 8px;">
                    系统将根据启用的策略和设定间隔自动执行买卖操作
                </p>
            </div>

            <!-- 持仓信息 -->
            <div class="panel">
                <h3>📊 持仓信息</h3>
                <div id="positions">暂无持仓</div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let autoTradingTimer = null;
        let isAutoTradingRunning = false;

        // 测试函数
        function testFunction() {
            console.log('测试函数被调用');
            alert('测试功能正常！JavaScript正在工作。');

            // 测试元素获取
            const btn = document.getElementById('auto-trade-btn');
            const statusText = document.getElementById('status-text');
            console.log('按钮元素:', btn);
            console.log('状态文本元素:', statusText);
            console.log('当前自动交易状态:', isAutoTradingRunning);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            updateAccountInfo();
            setInterval(updateAccountInfo, 30000);
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (isAutoTradingRunning) {
                stopAutoTrading();
            }
        });

        // 更新账户信息
        async function updateAccountInfo() {
            try {
                const resp = await fetch('/smart/account');
                const data = await resp.json();

                document.getElementById('total-value').textContent = data.total_value.toLocaleString();
                document.getElementById('cash').textContent = data.cash.toLocaleString();
                document.getElementById('position-value').textContent = data.market_value.toLocaleString();

                const pnlElement = document.getElementById('pnl');
                const pnlColor = data.total_pnl >= 0 ? '#10b981' : '#ef4444';
                pnlElement.innerHTML = '¥' + data.total_pnl.toLocaleString() + ' (' + data.total_return.toFixed(2) + '%)';
                pnlElement.style.color = pnlColor;

                // 更新持仓信息
                updatePositions(data.positions);

            } catch (e) {
                console.error('更新账户信息失败:', e);
            }
        }

        // 更新持仓信息
        function updatePositions(positions) {
            const positionsDiv = document.getElementById('positions');
            if (!positions || positions.length === 0) {
                positionsDiv.innerHTML = '暂无持仓';
                return;
            }

            let html = '';
            positions.forEach(pos => {
                const pnlClass = pos.pnl >= 0 ? 'profit' : 'loss';
                html += '<div style="border-bottom: 1px solid #eee; padding: 5px 0;">';
                html += pos.symbol + ': ' + pos.quantity + '股 ';
                html += '<span class="' + pnlClass + '">' + pos.pnl_percent.toFixed(2) + '%</span>';
                html += '</div>';
            });
            positionsDiv.innerHTML = html;
        }

        // 买入股票
        async function buyStock() {
            const symbol = document.getElementById('trade-symbol').value;
            const amount = parseFloat(document.getElementById('trade-amount').value);

            if (!symbol || !amount) {
                showResult('请输入股票代码和金额', 'error');
                return;
            }

            try {
                const resp = await fetch('/smart/buy', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbol, amount, strategy: 'manual' })
                });
                const data = await resp.json();

                if (data.success) {
                    showResult(data.message, 'success');
                    document.getElementById('trade-symbol').value = '';
                    document.getElementById('trade-amount').value = '';
                    updateAccountInfo();
                } else {
                    showResult('买入失败: ' + data.message, 'error');
                }
            } catch (e) {
                showResult('买入错误: ' + e.message, 'error');
            }
        }

        // 卖出股票
        async function sellStock() {
            const symbol = document.getElementById('trade-symbol').value;

            if (!symbol) {
                showResult('请输入股票代码', 'error');
                return;
            }

            try {
                const resp = await fetch('/smart/sell', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbol, strategy: 'manual' })
                });
                const data = await resp.json();

                if (data.success) {
                    showResult(data.message, 'success');
                    document.getElementById('trade-symbol').value = '';
                    updateAccountInfo();
                } else {
                    showResult('卖出失败: ' + data.message, 'error');
                }
            } catch (e) {
                showResult('卖出错误: ' + e.message, 'error');
            }
        }

        // 切换自动交易状态
        function toggleAutoTrading() {
            console.log('toggleAutoTrading 被调用, 当前状态:', isAutoTradingRunning);

            if (isAutoTradingRunning) {
                console.log('停止自动交易');
                stopAutoTrading();
            } else {
                console.log('开始自动交易');
                startAutoTrading();
            }
        }

        // 开始自动交易
        function startAutoTrading() {
            console.log('startAutoTrading 被调用');

            if (isAutoTradingRunning) {
                console.log('自动交易已在运行，跳过');
                return;
            }

            console.log('设置 isAutoTradingRunning = true');
            isAutoTradingRunning = true;

            console.log('更新UI状态');
            updateAutoTradingUI();

            console.log('立即执行一次交易');
            executeAutoTrade();

            // 设置定时器
            const interval = parseInt(document.getElementById('trade-interval').value) * 1000;
            console.log('设置定时器，间隔:', interval, 'ms');
            autoTradingTimer = setInterval(executeAutoTrade, interval);

            console.log('显示启动消息');
            showResult('🚀 自动模拟炒股已启动！', 'success');
        }

        // 停止自动交易
        function stopAutoTrading() {
            if (!isAutoTradingRunning) return;

            isAutoTradingRunning = false;

            if (autoTradingTimer) {
                clearInterval(autoTradingTimer);
                autoTradingTimer = null;
            }

            updateAutoTradingUI();
            showResult('⏹️ 自动模拟炒股已停止', 'error');
        }

        // 更新自动交易UI状态
        function updateAutoTradingUI() {
            console.log('updateAutoTradingUI 被调用, isAutoTradingRunning:', isAutoTradingRunning);

            const btn = document.getElementById('auto-trade-btn');
            const statusText = document.getElementById('status-text');

            console.log('按钮元素:', btn);
            console.log('状态文本元素:', statusText);

            if (isAutoTradingRunning) {
                console.log('更新UI为运行状态');
                btn.textContent = '⏹️ 停止自动模拟炒股';
                btn.className = 'btn btn-danger';
                statusText.textContent = '运行中';
                statusText.style.color = '#10b981';
            } else {
                console.log('更新UI为停止状态');
                btn.textContent = '🤖 自动模拟炒股';
                btn.className = 'btn btn-primary';
                statusText.textContent = '已停止';
                statusText.style.color = '#ef4444';
            }
        }

        // 执行一次自动交易
        async function executeAutoTrade() {
            console.log('executeAutoTrade 被调用, isAutoTradingRunning:', isAutoTradingRunning);

            if (!isAutoTradingRunning) {
                console.log('自动交易未运行，跳过执行');
                return;
            }

            try {
                console.log('开始执行自动交易');
                // 显示交易状态
                showResult('🤖 AI正在分析市场并执行自动交易...', 'success');

                console.log('发送API请求到 /smart/rebalance');
                const resp = await fetch('/smart/rebalance', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                console.log('API响应状态:', resp.status);
                const data = await resp.json();
                console.log('API响应数据:', data);

                if (data.success) {
                    // 显示详细的交易结果
                    const resultDiv = document.getElementById('trade-result');
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = data.message.replace(/\n/g, '<br>');
                    updateAccountInfo();

                    // 5秒后清除结果（如果还在自动交易中）
                    setTimeout(() => {
                        if (isAutoTradingRunning) {
                            resultDiv.innerHTML = '🤖 等待下次自动交易...';
                        } else {
                            resultDiv.innerHTML = '';
                            resultDiv.className = '';
                        }
                    }, 5000);
                } else {
                    showResult('自动交易失败: ' + data.message, 'error');
                    // 如果交易失败，可以选择停止自动交易
                    if (data.message.includes('现金不足') || data.message.includes('严重错误')) {
                        stopAutoTrading();
                    }
                }
            } catch (e) {
                showResult('自动交易错误: ' + e.message, 'error');
                // 网络错误时停止自动交易
                stopAutoTrading();
            }
        }

        // 策略开关
        function togglePSO() {
            const enabled = document.getElementById('enable-pso').checked;
            fetch('/smart/config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ pso_enabled: enabled })
            });
        }

        function toggleGA() {
            const enabled = document.getElementById('enable-ga').checked;
            fetch('/smart/config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ ga_enabled: enabled })
            });
        }

        // 显示操作结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('trade-result');
            resultDiv.className = 'result ' + type;
            resultDiv.textContent = message;

            setTimeout(() => {
                resultDiv.textContent = '';
                resultDiv.className = '';
            }, 5000);
        }
    </script>
</body>
</html>
        """)

    # 模拟炒股API
    # 全局变量存储账户信息（实际应用中应使用数据库）
    account_data = {
        "cash": 100000.0,  # 初始现金10万
        "positions": {},   # 持仓 {symbol: {quantity, avg_price, current_price}}
        "trades": [],      # 交易记录
        "config": {
            "pso_enabled": False,
            "ga_enabled": False,
            "rebalance_freq": "W-FRI",
            "auto_trading_enabled": False,
            "trade_interval": 30
        },
        "auto_trading_stats": {
            "total_auto_trades": 0,
            "successful_trades": 0,
            "failed_trades": 0,
            "start_time": None,
            "last_trade_time": None
        }
    }

    @app.get("/smart/account")
    def get_account():
        """获取账户信息"""
        try:
            # 计算持仓市值和总盈亏
            market_value = 0
            total_cost = 0

            for symbol, pos in account_data["positions"].items():
                # 模拟当前价格（实际应从API获取）
                current_price = pos["avg_price"] * (1 + np.random.uniform(-0.05, 0.05))
                pos["current_price"] = current_price

                position_value = pos["quantity"] * current_price
                position_cost = pos["quantity"] * pos["avg_price"]

                market_value += position_value
                total_cost += position_cost

            total_value = account_data["cash"] + market_value
            total_pnl = market_value - total_cost
            total_return = (total_pnl / total_cost * 100) if total_cost > 0 else 0

            # 格式化持仓信息
            positions = []
            for symbol, pos in account_data["positions"].items():
                pnl = (pos["current_price"] - pos["avg_price"]) * pos["quantity"]
                pnl_percent = (pos["current_price"] - pos["avg_price"]) / pos["avg_price"] * 100

                positions.append({
                    "symbol": symbol,
                    "quantity": pos["quantity"],
                    "avg_price": pos["avg_price"],
                    "current_price": pos["current_price"],
                    "pnl": pnl,
                    "pnl_percent": pnl_percent
                })

            return {
                "cash": account_data["cash"],
                "market_value": market_value,
                "total_value": total_value,
                "total_pnl": total_pnl,
                "total_return": total_return,
                "positions": positions,
                "recent_trades": account_data["trades"][-10:]  # 最近10笔交易
            }

        except Exception as e:
            logger.error(f"获取账户信息失败: {e}")
            raise HTTPException(status_code=500, detail="获取账户信息失败")

    @app.post("/smart/buy")
    def buy_stock(request: Dict[str, Any]):
        """买入股票"""
        try:
            symbol = request.get("symbol", "").upper()
            amount = float(request.get("amount", 0))
            strategy = request.get("strategy", "manual")

            if not symbol or amount <= 0:
                return {"success": False, "message": "请输入有效的股票代码和金额"}

            # 确保股票代码格式正确
            if not symbol.endswith(('.SH', '.SZ')):
                if symbol.startswith('6'):
                    symbol += '.SH'
                else:
                    symbol += '.SZ'

            # 检查现金是否足够
            if account_data["cash"] < amount:
                return {"success": False, "message": "现金不足"}

            # 模拟股票价格（实际应从API获取）
            price = 10 + np.random.uniform(5, 50)  # 模拟价格10-60元
            quantity = int(amount / price / 100) * 100  # 按手买入（100股为1手）

            if quantity == 0:
                return {"success": False, "message": "金额不足购买1手"}

            actual_amount = quantity * price

            # 更新持仓
            if symbol in account_data["positions"]:
                pos = account_data["positions"][symbol]
                total_quantity = pos["quantity"] + quantity
                total_cost = pos["quantity"] * pos["avg_price"] + actual_amount
                pos["avg_price"] = total_cost / total_quantity
                pos["quantity"] = total_quantity
            else:
                account_data["positions"][symbol] = {
                    "quantity": quantity,
                    "avg_price": price,
                    "current_price": price
                }

            # 更新现金
            account_data["cash"] -= actual_amount

            # 记录交易
            trade = {
                "timestamp": datetime.now().isoformat(),
                "action": "buy",
                "symbol": symbol,
                "quantity": quantity,
                "price": price,
                "amount": actual_amount,
                "strategy": strategy
            }
            account_data["trades"].append(trade)

            return {
                "success": True,
                "message": f"成功买入 {symbol} {quantity}股，成交价 ¥{price:.2f}，总金额 ¥{actual_amount:.2f}"
            }

        except Exception as e:
            logger.error(f"买入股票失败: {e}")
            return {"success": False, "message": f"买入失败: {str(e)}"}

    @app.post("/smart/sell")
    def sell_stock(request: Dict[str, Any]):
        """卖出股票"""
        try:
            symbol = request.get("symbol", "").upper()
            strategy = request.get("strategy", "manual")

            if not symbol:
                return {"success": False, "message": "请输入股票代码"}

            # 确保股票代码格式正确
            if not symbol.endswith(('.SH', '.SZ')):
                if symbol.startswith('6'):
                    symbol += '.SH'
                else:
                    symbol += '.SZ'

            # 检查是否有持仓
            if symbol not in account_data["positions"]:
                return {"success": False, "message": "没有该股票的持仓"}

            pos = account_data["positions"][symbol]
            quantity = pos["quantity"]

            # 模拟当前价格
            current_price = pos["avg_price"] * (1 + np.random.uniform(-0.05, 0.05))
            amount = quantity * current_price

            # 更新现金
            account_data["cash"] += amount

            # 删除持仓
            del account_data["positions"][symbol]

            # 记录交易
            trade = {
                "timestamp": datetime.now().isoformat(),
                "action": "sell",
                "symbol": symbol,
                "quantity": quantity,
                "price": current_price,
                "amount": amount,
                "strategy": strategy
            }
            account_data["trades"].append(trade)

            pnl = (current_price - pos["avg_price"]) * quantity
            pnl_percent = (current_price - pos["avg_price"]) / pos["avg_price"] * 100

            return {
                "success": True,
                "message": f"成功卖出 {symbol} {quantity}股，成交价 ¥{current_price:.2f}，盈亏 ¥{pnl:.2f} ({pnl_percent:+.2f}%)"
            }

        except Exception as e:
            logger.error(f"卖出股票失败: {e}")
            return {"success": False, "message": f"卖出失败: {str(e)}"}

    @app.post("/smart/rebalance")
    def auto_trading():
        """自动模拟炒股"""
        try:
            # 更新统计信息
            account_data["auto_trading_stats"]["total_auto_trades"] += 1
            account_data["auto_trading_stats"]["last_trade_time"] = datetime.now().isoformat()

            actions = []
            total_trades = 0

            # 检查现金是否充足
            if account_data["cash"] < 1000:
                account_data["auto_trading_stats"]["failed_trades"] += 1
                return {
                    "success": False,
                    "message": "现金不足1000元，无法继续自动交易。建议停止自动交易或卖出部分持仓。"
                }

            # 模拟股票池
            stock_pool = [
                {"symbol": "000001.SZ", "name": "平安银行"},
                {"symbol": "600519.SH", "name": "贵州茅台"},
                {"symbol": "000002.SZ", "name": "万科A"},
                {"symbol": "600036.SH", "name": "招商银行"},
                {"symbol": "000858.SZ", "name": "五粮液"},
                {"symbol": "600276.SH", "name": "恒瑞医药"}
            ]

            # 如果启用了PSO算法，使用粒子群优化选股
            if account_data["config"]["pso_enabled"]:
                actions.append("🔍 粒子群优化选股")
                # 模拟PSO选出的优质股票
                selected_stocks = np.random.choice(len(stock_pool), size=min(3, len(stock_pool)), replace=False)
                for idx in selected_stocks:
                    stock = stock_pool[idx]
                    # 模拟买入决策
                    if np.random.random() > 0.3:  # 70%概率买入
                        buy_amount = np.random.uniform(5000, 15000)  # 随机买入金额
                        if account_data["cash"] >= buy_amount:
                            # 执行买入
                            price = 10 + np.random.uniform(5, 50)
                            quantity = int(buy_amount / price / 100) * 100
                            if quantity > 0:
                                actual_amount = quantity * price

                                # 更新持仓
                                if stock["symbol"] in account_data["positions"]:
                                    pos = account_data["positions"][stock["symbol"]]
                                    total_quantity = pos["quantity"] + quantity
                                    total_cost = pos["quantity"] * pos["avg_price"] + actual_amount
                                    pos["avg_price"] = total_cost / total_quantity
                                    pos["quantity"] = total_quantity
                                else:
                                    account_data["positions"][stock["symbol"]] = {
                                        "quantity": quantity,
                                        "avg_price": price,
                                        "current_price": price
                                    }

                                account_data["cash"] -= actual_amount

                                # 记录交易
                                trade = {
                                    "timestamp": datetime.now().isoformat(),
                                    "action": "buy",
                                    "symbol": stock["symbol"],
                                    "quantity": quantity,
                                    "price": price,
                                    "amount": actual_amount,
                                    "strategy": "PSO自动选股"
                                }
                                account_data["trades"].append(trade)
                                total_trades += 1
                                actions.append(f"📈 买入 {stock['name']} {quantity}股")

            # 如果启用了GA算法，优化交易参数
            if account_data["config"]["ga_enabled"]:
                actions.append("🧬 遗传算法优化交易参数")

                # GA算法参数优化（模拟遗传算法的进化过程）
                # 生成多个参数组合（染色体）
                chromosomes = [
                    {"stop_loss": -0.05, "take_profit": 0.08, "fitness": 0},
                    {"stop_loss": -0.03, "take_profit": 0.10, "fitness": 0},
                    {"stop_loss": -0.07, "take_profit": 0.06, "fitness": 0},
                    {"stop_loss": -0.04, "take_profit": 0.12, "fitness": 0}
                ]

                # 计算适应度（基于历史表现）
                for chromosome in chromosomes:
                    # 模拟适应度计算（实际应基于历史回测结果）
                    chromosome["fitness"] = np.random.uniform(0.5, 1.5)

                # 选择最优参数
                best_chromosome = max(chromosomes, key=lambda x: x["fitness"])
                stop_loss_threshold = best_chromosome["stop_loss"]
                take_profit_threshold = best_chromosome["take_profit"]

                actions.append(f"📊 GA优化参数: 止损{stop_loss_threshold*100:.1f}%, 止盈{take_profit_threshold*100:.1f}%")

                # 使用优化后的参数进行卖出决策
                positions_to_sell = []
                for symbol, pos in account_data["positions"].items():
                    # 模拟当前价格
                    current_price = pos["avg_price"] * (1 + np.random.uniform(-0.1, 0.15))
                    pos["current_price"] = current_price

                    # 使用GA优化的参数
                    profit_rate = (current_price - pos["avg_price"]) / pos["avg_price"]
                    if profit_rate > take_profit_threshold or profit_rate < stop_loss_threshold:
                        positions_to_sell.append((symbol, pos, profit_rate))

                # 执行卖出
                for symbol, pos, profit_rate in positions_to_sell:
                    quantity = pos["quantity"]
                    current_price = pos["current_price"]
                    amount = quantity * current_price

                    account_data["cash"] += amount
                    del account_data["positions"][symbol]

                    # 记录交易
                    trade = {
                        "timestamp": datetime.now().isoformat(),
                        "action": "sell",
                        "symbol": symbol,
                        "quantity": quantity,
                        "price": current_price,
                        "amount": amount,
                        "strategy": "GA优化卖出"
                    }
                    account_data["trades"].append(trade)
                    total_trades += 1

                    stock_name = next((s["name"] for s in stock_pool if s["symbol"] == symbol), symbol)
                    profit_text = f"{profit_rate*100:+.1f}%"
                    actions.append(f"📉 卖出 {stock_name} {quantity}股 ({profit_text})")

            # 如果没有启用算法，使用基础技术指标策略
            if not account_data["config"]["pso_enabled"] and not account_data["config"]["ga_enabled"]:
                actions.append("📊 基于技术指标的自动交易")
                # 随机选择1-2只股票进行交易
                selected_count = np.random.randint(1, 3)
                selected_stocks = np.random.choice(len(stock_pool), size=selected_count, replace=False)

                for idx in selected_stocks:
                    stock = stock_pool[idx]
                    # 50%概率买入，50%概率卖出（如果有持仓）
                    if np.random.random() > 0.5:  # 买入
                        buy_amount = np.random.uniform(3000, 10000)
                        if account_data["cash"] >= buy_amount:
                            price = 10 + np.random.uniform(5, 50)
                            quantity = int(buy_amount / price / 100) * 100
                            if quantity > 0:
                                actual_amount = quantity * price

                                if stock["symbol"] in account_data["positions"]:
                                    pos = account_data["positions"][stock["symbol"]]
                                    total_quantity = pos["quantity"] + quantity
                                    total_cost = pos["quantity"] * pos["avg_price"] + actual_amount
                                    pos["avg_price"] = total_cost / total_quantity
                                    pos["quantity"] = total_quantity
                                else:
                                    account_data["positions"][stock["symbol"]] = {
                                        "quantity": quantity,
                                        "avg_price": price,
                                        "current_price": price
                                    }

                                account_data["cash"] -= actual_amount

                                trade = {
                                    "timestamp": datetime.now().isoformat(),
                                    "action": "buy",
                                    "symbol": stock["symbol"],
                                    "quantity": quantity,
                                    "price": price,
                                    "amount": actual_amount,
                                    "strategy": "技术指标买入"
                                }
                                account_data["trades"].append(trade)
                                total_trades += 1
                                actions.append(f"📈 买入 {stock['name']} {quantity}股")

                    else:  # 卖出
                        if stock["symbol"] in account_data["positions"]:
                            pos = account_data["positions"][stock["symbol"]]
                            quantity = pos["quantity"]
                            current_price = pos["avg_price"] * (1 + np.random.uniform(-0.05, 0.1))
                            amount = quantity * current_price

                            account_data["cash"] += amount
                            del account_data["positions"][stock["symbol"]]

                            trade = {
                                "timestamp": datetime.now().isoformat(),
                                "action": "sell",
                                "symbol": stock["symbol"],
                                "quantity": quantity,
                                "price": current_price,
                                "amount": amount,
                                "strategy": "技术指标卖出"
                            }
                            account_data["trades"].append(trade)
                            total_trades += 1

                            profit_rate = (current_price - pos["avg_price"]) / pos["avg_price"]
                            profit_text = f"{profit_rate*100:+.1f}%"
                            actions.append(f"📉 卖出 {stock['name']} {quantity}股 ({profit_text})")

            if total_trades == 0:
                actions.append("💤 当前市场条件不适合交易，保持观望")
            else:
                account_data["auto_trading_stats"]["successful_trades"] += 1

            # 添加统计信息
            stats = account_data["auto_trading_stats"]
            stats_text = f"\n📊 统计: 总计{stats['total_auto_trades']}次，成功{stats['successful_trades']}次，失败{stats['failed_trades']}次"

            message = f"🤖 自动模拟炒股完成，执行了 {total_trades} 笔交易：\n" + "\n".join(actions) + stats_text

            return {"success": True, "message": message}

        except Exception as e:
            logger.error(f"自动模拟炒股失败: {e}")
            return {"success": False, "message": f"自动交易失败: {str(e)}"}

    @app.post("/smart/config")
    def update_config(request: Dict[str, Any]):
        """更新策略配置"""
        try:
            for key, value in request.items():
                if key in account_data["config"]:
                    account_data["config"][key] = value

            return {"success": True, "message": "配置更新成功"}

        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return {"success": False, "message": f"配置更新失败: {str(e)}"}

    @app.get("/smart/status")
    def get_auto_trading_status():
        """获取自动交易状态"""
        try:
            stats = account_data["auto_trading_stats"]
            return {
                "auto_trading_enabled": account_data["config"]["auto_trading_enabled"],
                "trade_interval": account_data["config"]["trade_interval"],
                "stats": stats
            }
        except Exception as e:
            logger.error(f"获取自动交易状态失败: {e}")
            return {"success": False, "message": f"获取状态失败: {str(e)}"}

    @app.post("/smart/toggle")
    def toggle_auto_trading(request: Dict[str, Any]):
        """切换自动交易状态"""
        try:
            enabled = request.get("enabled", False)
            account_data["config"]["auto_trading_enabled"] = enabled

            if enabled:
                account_data["auto_trading_stats"]["start_time"] = datetime.now().isoformat()
                message = "自动交易已启动"
            else:
                message = "自动交易已停止"

            return {"success": True, "message": message, "enabled": enabled}

        except Exception as e:
            logger.error(f"切换自动交易状态失败: {e}")
            return {"success": False, "message": f"状态切换失败: {str(e)}"}

    return app


def _is_nan(x):
    """检查是否为NaN"""
    try:
        return pd.isna(x)
    except Exception:
        return x is None


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
