from __future__ import annotations

from fastapi import FastAPI, Query
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from ..sdk.obb import obb
from ..config import get_settings
from ..storage.db import init_db
from ..providers.tushare_provider import <PERSON><PERSON>reProvider
from ..sdk import indicators as IND
from ..quant.ga_pso import ga_optimize, pso_select
from ..sim.simulate import SimConfig, backtest


class PriceResponse(BaseModel):
    symbol: str
    rows: int


class PriceBar(BaseModel):
    trade_date: str
    open: float | None
    high: float | None
    low: float | None
    close: float | None
    volume: float | None
    provider: str


def create_app() -> FastAPI:
    settings = get_settings()
    init_db()
    app = FastAPI(title="OpenBB-CN API", version="0.1.0")

    @app.get("/healthz")
    def healthz():
        return {"status": "ok"}

    @app.get("/", response_class=HTMLResponse)
    def root():
        return """
<!doctype html>
<html>
  <head>
    <meta charset='utf-8' />
    <title>OpenBB-CN UI</title>
    <script src=\"https://cdn.plot.ly/plotly-2.35.2.min.js\"></script>
    <style>
      body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial; margin: 24px; }
      .row { display: flex; gap: 12px; align-items: center; flex-wrap: wrap; }
      input { padding: 6px 10px; }
      button { padding: 6px 12px; }
      #chart { width: 100%; max-width: 1100px; height: 520px; }
      #vol { width: 100%; max-width: 1100px; height: 160px; margin-top: 8px; }
    </style>
  </head>
  <body>
    <h2>OpenBB-CN 简易行情页面</h2>
    <div class=\"row\">
      <label>Symbol(ts_code)：</label>
      <input id=\"sym\" value=\"600519.SH\" />
      <label>Start(YYYYMMDD)：</label>
      <input id=\"start\" placeholder=\"20240101\" />
      <label>End(YYYYMMDD)：</label>
      <input id=\"end\" placeholder=\"20240630\" />
      <button onclick=\"loadData()\">查询</button>
    </div>
    <div id=\"chart\"></div>
    <div id=\"vol\"></div>
    <script>
      async function loadData(){
        const s = document.getElementById('sym').value.trim();
        const st = document.getElementById('start').value.trim();
        const ed = document.getElementById('end').value.trim();
        const qs = new URLSearchParams({ symbol: s });
        if(st) qs.append('start', st);
        if(ed) qs.append('end', ed);
        const resp = await fetch('/equity/price/historical/data?' + qs.toString());
        const rows = await resp.json();
        const dates = rows.map(r => r.trade_date);
        const open = rows.map(r => r.open);
        const high = rows.map(r => r.high);
        const low = rows.map(r => r.low);
        const close = rows.map(r => r.close);
        const vol = rows.map(r => r.volume);
        const candle = [{
          x: dates, open, high, low, close, type: 'candlestick', name: s,
        }];
        const layout = { margin: { t: 24, r: 16, b: 24, l: 40 } };
        Plotly.newPlot('chart', candle, layout, {responsive: true});
        Plotly.newPlot('vol', [{ x: dates, y: vol, type: 'bar', name: 'volume' }], { margin: { t: 10 } }, {responsive:true});
      }
      loadData();
    </script>
  </body>
</html>
"""

    @app.get("/workspace", response_class=HTMLResponse)
    def workspace():
        return """
<!doctype html>
<html>
  <head>
    <meta charset='utf-8' />
    <title>OpenBB-CN Workspace</title>
    <script src=\"https://cdn.plot.ly/plotly-2.35.2.min.js\"></script>
    <style>
      body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial; margin: 0; }
      .wrap { display: grid; grid-template-columns: 360px 1fr; height: 100vh; }
      .left { border-right: 1px solid #eee; padding: 16px; overflow: auto; }
      .right { padding: 16px; overflow: auto; }
      input, button { padding: 6px 10px; }
      .stock { cursor: pointer; padding: 6px; border-bottom: 1px dashed #eee; }
      #chart { width: 100%; height: 420px; }
      #vol { width: 100%; height: 140px; }
      textarea { width: 100%; height: 140px; }
      pre { white-space: pre-wrap; background: #fafafa; padding: 8px; border: 1px solid #eee; }
      .cards { display: grid; grid-template-columns: repeat(auto-fill,minmax(200px,1fr)); gap: 10px; margin: 12px 0; }
      .card { border: 1px solid #eee; border-left: 6px solid #ddd; padding: 8px; background: #fff; }
      .good { border-left-color: #12b886; }
      .warn { border-left-color: #fab005; }
      .bad { border-left-color: #fa5252; }
    </style>
  </head>
  <body>
    <div class=\"wrap\">
      <div class=\"left\">
        <h3>股票搜索</h3>
        <div>
          <input id=\"q\" placeholder=\"输入6位代码(如:000001)或名称(如:平安银行)\" />
          <button onclick=\"search()\">搜索</button>
        </div>
        <div id=\"list\"></div>
      </div>
      <div class=\"right\">
        <h3 id=\"title\">AI 分析</h3>
        <div id=\"chart\"></div>
        <div id=\"vol\"></div>
        <h4>估值倍数</h4>
        <div id=\"ratio\" style=\"width:100%;height:260px;\"></div>
        <h4>自研指标</h4>
        <div id=\"cards\" class=\"cards\"></div>
        <h4>指标</h4>
        <pre id=\"ind\"></pre>
        <h4>AI 结论</h4>
        <pre id=\"ai\"></pre>
      </div>
    </div>
    <script>
      async function search(){
        const q = document.getElementById('q').value.trim();
        if (!q) return;

        const resp = await fetch('/search?query=' + encodeURIComponent(q));
        const data = await resp.json();
        const list = document.getElementById('list');
        list.innerHTML = '';

        data.forEach((item, index) => {
          const div = document.createElement('div');
          div.className = 'stock';
          // 去除.SH/.SZ后缀用于显示
          const displayCode = item.ts_code.replace(/\.(SH|SZ)$/, '');
          div.textContent = `${displayCode} ${item.name} ${item.industry||''}`;
          div.onclick = () => load(item.ts_code, item.name);
          list.appendChild(div);
        });

        // 自动加载第一个结果
        if (data.length > 0) {
          load(data[0].ts_code, data[0].name);
        }
      }
      function lastNonNull(arr, key){
        for(let i=arr.length-1;i>=0;i--){
          const v = key?arr[i][key]:arr[i];
          if(v!==null && v!==undefined && !Number.isNaN(v)) return arr[i];
        }
        return null;
      }

      function cardHtml(title, value, hint, cls){
        return `<div class="card ${cls}"><div><b>${title}</b></div><div style="font-size:20px">${value}</div><div style="color:#666">${hint}</div></div>`
      }

      async function load(sym, name){
        // 去除.SH/.SZ后缀用于显示
        const displayCode = sym.replace(/\.(SH|SZ)$/, '');
        document.getElementById('title').textContent = `AI 分析 - ${displayCode} ${name||''}`;
        const resp = await fetch('/equity/price/historical/data?symbol=' + encodeURIComponent(sym));
        const rows = await resp.json();
        const dates = rows.map(r => r.trade_date);
        const open = rows.map(r => r.open);
        const high = rows.map(r => r.high);
        const low = rows.map(r => r.low);
        const close = rows.map(r => r.close);
        const vol = rows.map(r => r.volume);
        Plotly.newPlot('chart', [{ x: dates, open, high, low, close, type: 'candlestick', name: displayCode }], { margin: { t: 24, r: 16, b: 24, l: 40 } }, {responsive: true});
        Plotly.newPlot('vol', [{ x: dates, y: vol, type: 'bar', name: 'volume' }], { margin: { t: 10 } }, {responsive:true});
        // 估值倍数
        const r = await fetch('/equity/basic/ratios?symbol=' + encodeURIComponent(sym));
        const rr = await r.json();
        const rdates = rr.map(x=>x.trade_date);
        Plotly.newPlot('ratio', [
          { x: rdates, y: rr.map(x=>x.pe), name: 'PE', type: 'scatter', mode: 'lines' },
          { x: rdates, y: rr.map(x=>x.pb), name: 'PB', type: 'scatter', mode: 'lines' },
          { x: rdates, y: rr.map(x=>x.ps), name: 'PS', type: 'scatter', mode: 'lines' },
        ], { margin: { t: 10 }, legend: { orientation: 'h' } }, {responsive:true});
        const indResp = await fetch('/equity/price/indicators?symbol=' + encodeURIComponent(sym));
        const ind = await indResp.json();
        // 自研指标并行获取
        const [vcm, tei, lvr, apiI, blce, mtr] = await Promise.all([
          fetch('/indicator/vcm?symbol=' + encodeURIComponent(sym)).then(r=>r.json()),
          fetch('/indicator/tei?symbol=' + encodeURIComponent(sym)).then(r=>r.json()),
          fetch('/indicator/lvr?symbol=' + encodeURIComponent(sym)).then(r=>r.json()),
          fetch('/indicator/api?symbol=' + encodeURIComponent(sym)).then(r=>r.json()),
          fetch('/indicator/blce?symbol=' + encodeURIComponent(sym)).then(r=>r.json()),
          fetch('/indicator/mtr?symbol=' + encodeURIComponent(sym)).then(r=>r.json()),
        ]);

        const v_vcm = lastNonNull(vcm, 'v')?.v ?? null;
        const v_tei = lastNonNull(tei, 'v')?.v ?? null;
        const v_lvr = lastNonNull(lvr, 'v')?.v ?? null;
        const v_api = lastNonNull(apiI, 'v')?.v ?? null;
        const b_last = lastNonNull(blce);
        const v_prob = b_last? b_last.prob_up: null;
        const v_energy = b_last? b_last.energy: null;
        const v_bias = b_last? b_last.dir_bias: null;
        const v_mtr = lastNonNull(mtr, 'v')?.v ?? null;

        // 阈值提示
        const cards = [];
        const fmt = (x)=> x===null? '—' : (Math.abs(x)>1e4? x.toExponential(2) : (typeof x==='number'? x.toFixed(3) : x));
        const clsOf = (good,bad,val)=> val===null? '' : (val>=good? 'good' : (val<=bad? 'bad' : 'warn'));
        cards.push(cardHtml('VCM', fmt(v_vcm), '放量顺势动量，高于0.5看多，低于-0.5看空', clsOf(0.5,-0.5,v_vcm)));
        cards.push(cardHtml('TEI', fmt(v_tei), '趋势熵，>0.2稳态上行，<-0.2衰减', clsOf(0.2,-0.2,v_tei)));
        cards.push(cardHtml('LVR', fmt(v_lvr), '流动性真空风险，>1需警惕滑点/回撤', v_lvr!==null && v_lvr>1? 'bad' : 'good'));
        cards.push(cardHtml('API', fmt(v_api), '非对称压力，>0.1上行动力，<-0.1下行动力', clsOf(0.1,-0.1,v_api)));
        cards.push(cardHtml('BLCE↑', v_prob===null? '—' : (v_prob*100).toFixed(1)+'%', '收敛-爆发上行概率，>60%看突破上行', v_prob!==null && v_prob>0.6? 'good' : (v_prob!==null && v_prob<0.4? 'bad':'warn')));
        cards.push(cardHtml('MTR', fmt(v_mtr), '多尺度共振，>0.5多头共振，<-0.2空头', clsOf(0.5,-0.2,v_mtr)));
        document.getElementById('cards').innerHTML = cards.join('');

        // 注入AI上下文
        const custom = {
          vcm: v_vcm, tei: v_tei, lvr: v_lvr, api: v_api, mtr: v_mtr,
          blce_prob_up: v_prob, blce_energy: v_energy, blce_dir_bias: v_bias
        };
        const merged = { ...ind, custom };
        document.getElementById('ind').textContent = JSON.stringify(merged, null, 2);
        const aiResp = await fetch('/ai/analyze', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ symbol: sym, indicators: merged }) });
        const ai = await aiResp.json();
        document.getElementById('ai').textContent = ai.text || JSON.stringify(ai);
      }

      // 添加实时搜索功能
      let searchTimeout;
      const qInput = document.getElementById('q');
      if (qInput) {
        // 回车键立即搜索
        qInput.addEventListener('keydown', (e) => {
          if (e.key === 'Enter') {
            clearTimeout(searchTimeout);
            search();
          }
        });

        // 输入时延迟搜索
        qInput.addEventListener('input', (e) => {
          clearTimeout(searchTimeout);
          const query = e.target.value.trim();

          if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
              search();
            }, 500);
          }
        });
      }
    </script>
  </body>
</html>
"""

    @app.get("/equity/price/historical", response_model=PriceResponse)
    def historical(
        symbol: str = Query(..., description="ts_code, 如 600519.SH/000001.SZ"),
        start: str | None = Query(None, description="开始日期 YYYYMMDD"),
        end: str | None = Query(None, description="结束日期 YYYYMMDD"),
    ):
        df = obb.equity.price.historical(symbol=symbol, start=start, end=end)
        return PriceResponse(symbol=symbol, rows=len(df))

    @app.get("/equity/price/historical/data", response_model=list[PriceBar])
    def historical_data(
        symbol: str = Query(..., description="ts_code, 如 600519.SH/000001.SZ"),
        start: str | None = Query(None, description="开始日期 YYYYMMDD"),
        end: str | None = Query(None, description="结束日期 YYYYMMDD"),
    ):
        df = obb.equity.price.historical(symbol=symbol, start=start, end=end)
        records = [
            PriceBar(
                trade_date=str(row.trade_date),
                open=(None if pd_isna(row.open) else float(row.open)),
                high=(None if pd_isna(row.high) else float(row.high)),
                low=(None if pd_isna(row.low) else float(row.low)),
                close=(None if pd_isna(row.close) else float(row.close)),
                volume=(None if pd_isna(row.volume) else float(row.volume)),
                provider=row.provider,
            )
            for row in df.itertuples(index=False)
        ]
        return records

    @app.get("/equity/basic/ratios")
    def basic_ratios(symbol: str, start: str | None = None, end: str | None = None):
        prov = TushareProvider()
        df = prov.daily_basic(symbol=symbol, start=start, end=end)
        return [
            {
                "trade_date": str(r.trade_date),
                "pe": (None if pd_isna(r.pe) else float(r.pe)),
                "pb": (None if pd_isna(r.pb) else float(r.pb)),
                "ps": (None if pd_isna(r.ps) else float(r.ps)),
            }
            for r in df.itertuples(index=False)
        ]

    @app.get("/search")
    def search(query: str, limit: int = 20):
        prov = TushareProvider()
        df = prov.search(query=query, limit=limit)
        return df.to_dict(orient="records")

    @app.get("/equity/price/indicators")
    def indicators(symbol: str):
        import pandas as pd
        df = obb.equity.price.historical(symbol=symbol)
        if df.empty:
            return {"symbol": symbol, "size": 0}
        s = df.copy().reset_index(drop=True)
        out: dict[str, float | None] = {}
        # 均线/动量
        s["ma5"] = s["close"].rolling(5).mean()
        s["ma20"] = s["close"].rolling(20).mean()
        s["ma60"] = s["close"].rolling(60).mean()
        s["ret5"] = s["close"].pct_change(5)
        s["ret20"] = s["close"].pct_change(20)
        # 常用指标
        rsi = IND.rsi(s)
        macd = IND.macd(s)
        boll = IND.boll(s)
        kdj = IND.kdj(s)
        atr = IND.atr(s)
        vwap = IND.vwap(s)
        # 自研指标（服务器端也给出最新值，便于统一消费）
        vcm_s = IND.vcm(s)
        tei_s = IND.tei(s)
        lvr_s = IND.lvr(s)
        api_s = IND.api_index(s)
        blce_df = IND.blce(s)
        mtr_s = IND.mtr(s)
        latest = s.iloc[-1].to_dict()
        out.update({
            "close": latest.get("close"),
            "ma5": latest.get("ma5"),
            "ma20": latest.get("ma20"),
            "ma60": latest.get("ma60"),
            "ret5": latest.get("ret5"),
            "ret20": latest.get("ret20"),
            "rsi": rsi.iloc[-1] if len(rsi)>0 else None,
            "macd": macd["macd"].iloc[-1] if len(macd)>0 else None,
            "macd_signal": macd["signal"].iloc[-1] if len(macd)>0 else None,
            "boll_mid": boll["mid"].iloc[-1] if len(boll)>0 else None,
            "boll_upper": boll["upper"].iloc[-1] if len(boll)>0 else None,
            "boll_lower": boll["lower"].iloc[-1] if len(boll)>0 else None,
            "kdj_k": kdj["k"].iloc[-1] if len(kdj)>0 else None,
            "kdj_d": kdj["d"].iloc[-1] if len(kdj)>0 else None,
            "kdj_j": kdj["j"].iloc[-1] if len(kdj)>0 else None,
            "atr": atr.iloc[-1] if len(atr)>0 else None,
            "vwap": vwap.iloc[-1] if len(vwap)>0 else None,
            # 自研指标 latest
            "vcm": vcm_s.iloc[-1] if len(vcm_s)>0 else None,
            "tei": tei_s.iloc[-1] if len(tei_s)>0 else None,
            "lvr": lvr_s.iloc[-1] if len(lvr_s)>0 else None,
            "api_index": api_s.iloc[-1] if len(api_s)>0 else None,
            "blce_prob_up": (blce_df["prob_up"].iloc[-1] if len(blce_df)>0 else None),
            "blce_energy": (blce_df["energy"].iloc[-1] if len(blce_df)>0 else None),
            "blce_dir_bias": (blce_df["dir_bias"].iloc[-1] if len(blce_df)>0 else None),
            "mtr": mtr_s.iloc[-1] if len(mtr_s)>0 else None,
        })
        return {
            "symbol": symbol,
            "size": int(len(df)),
            "latest": { k: (None if pd_isna(v) else float(v)) for k, v in out.items() },
        }

    @app.post("/ai/analyze")
    def ai_analyze(payload: dict):
        symbol = payload.get("symbol")
        indicators = payload.get("indicators", {})
        user_prompt = payload.get("prompt") or "基于所给数据做简洁、结构化的中文分析。"
        temperature = payload.get("temperature")
        max_tokens = payload.get("max_tokens")
        top_p = payload.get("top_p")
        settings = get_settings()
        api_key = settings.bailian_api_key
        base_url = settings.bailian_base_url
        model = settings.bailian_model
        if api_key:
            try:
                import requests
                sys_prompt = (
                    "你是专业的A股/港股/期货分析师。请严格基于提供的数据（价格、估值、以及自研指标：VCM/TEI/LVR/API/BLCE/MTR）"
                    "进行中文分析，输出四段结构：\n1) 趋势与动量\n2) 估值与性价比\n3) 风险点（流动性/下行压力）\n4) 操作建议（仓位/止盈/触发条件）。"
                )
                user_content = {
                    "symbol": symbol,
                    "indicators": indicators,
                    "ask": user_prompt,
                }
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json",
                }
                body = {
                    "model": model,
                    "messages": [
                        {"role": "system", "content": sys_prompt},
                        {"role": "user", "content": str(user_content)},
                    ],
                }
                # 可选参数透传（对齐百炼 OpenAI 兼容接口）
                if isinstance(temperature, (int, float)):
                    body["temperature"] = float(temperature)
                if isinstance(max_tokens, int):
                    body["max_tokens"] = max_tokens
                if isinstance(top_p, (int, float)):
                    body["top_p"] = float(top_p)
                resp = requests.post(f"{base_url}/chat/completions", headers=headers, json=body, timeout=30)
                data = resp.json()
                text = data.get("choices", [{}])[0].get("message", {}).get("content", "")
                return {"symbol": symbol, "text": text or "", "indicators": indicators}
            except Exception as e:
                return {"symbol": symbol, "text": f"AI调用失败：{e}", "indicators": indicators}
        # 无 Key 回退本地规则
        latest = indicators.get("latest") or {}
        ma5 = latest.get("ma5")
        ma20 = latest.get("ma20")
        close = latest.get("close")
        ret5 = latest.get("ret5")
        ret20 = latest.get("ret20")
        signals = []
        if ma5 and ma20 and close:
            if ma5 > ma20 and close > ma5:
                signals.append("短期趋势偏强（MA5>MA20 且收盘价在MA5之上）")
            elif ma5 < ma20 and close < ma5:
                signals.append("短期趋势偏弱（MA5<MA20 且收盘价在MA5之下）")
        if ret5 is not None:
            signals.append(f"近5日涨跌幅：{round(ret5*100,2)}%")
        if ret20 is not None:
            signals.append(f"近20日涨跌幅：{round(ret20*100,2)}%")
        text = f"【{symbol}】基于简易指标的自动分析：\n- " + "\n- ".join(signals or ["数据不足，无法生成结论"]) + "\n(未配置大模型Key，已使用本地规则)"
        return {"symbol": symbol, "text": text, "indicators": indicators}

    # 自研指标接口
    @app.get("/indicator/vcm")
    def ind_vcm(symbol: str, window: int = 20, decay: float = 0.85):
        df = obb.equity.price.historical(symbol=symbol)
        s = IND.vcm(df, window=window, decay=decay).fillna(0)
        return [{"t": str(t), "v": float(v)} for t, v in zip(df["trade_date"], s)]

    @app.get("/indicator/tei")
    def ind_tei(symbol: str, window: int = 20):
        df = obb.equity.price.historical(symbol=symbol)
        s = IND.tei(df, window=window).fillna(0)
        return [{"t": str(t), "v": float(v)} for t, v in zip(df["trade_date"], s)]

    @app.get("/indicator/lvr")
    def ind_lvr(symbol: str, window: int = 20, atr_window: int = 14):
        df = obb.equity.price.historical(symbol=symbol)
        s = IND.lvr(df, window=window, atr_window=atr_window).fillna(0)
        return [{"t": str(t), "v": float(v)} for t, v in zip(df["trade_date"], s)]

    @app.get("/indicator/api")
    def ind_api(symbol: str, window: int = 20, gamma: float = 0.2):
        df = obb.equity.price.historical(symbol=symbol)
        s = IND.api_index(df, window=window, gamma=gamma).fillna(0)
        return [{"t": str(t), "v": float(v)} for t, v in zip(df["trade_date"], s)]

    @app.get("/indicator/blce")
    def ind_blce(symbol: str, window: int = 20, alpha: float = 0.6, beta: float = 0.8, atr_window: int = 14):
        df = obb.equity.price.historical(symbol=symbol)
        out = IND.blce(df, window=window, alpha=alpha, beta=beta, atr_window=atr_window).fillna(0)
        return [{"t": str(t), "energy": float(a), "dir_bias": float(b), "prob_up": float(c)} for t, a, b, c in zip(df["trade_date"], out["energy"], out["dir_bias"], out["prob_up"])]

    @app.get("/indicator/mtr")
    def ind_mtr(symbol: str):
        df = obb.equity.price.historical(symbol=symbol)
        s = IND.mtr(df).fillna(0)
        return [{"t": str(t), "v": float(v)} for t, v in zip(df["trade_date"], s)]

    # 量化优化：遗传算法与粒子群
    @app.get("/quant/ga")
    def quant_ga(symbol: str, start: str | None = None, end: str | None = None):
        return ga_optimize(symbol=symbol, start=start, end=end)

    @app.post("/quant/pso")
    def quant_pso(payload: dict):
        symbols = payload.get("symbols") or []
        start = payload.get("start")
        end = payload.get("end")
        top_n = int(payload.get("top_n") or 10)
        return pso_select(symbols=symbols, start=start, end=end, top_n=top_n)

    # 模拟炒股：基于PSO的自动选股+等权调仓回测
    @app.post("/sim/backtest")
    def sim_backtest(payload: dict):
        cfg = SimConfig(
            start=payload.get("start"),
            end=payload.get("end"),
            universe=payload.get("universe") or [],
            initial_cash=float(payload.get("initial_cash") or 1_000_000.0),
            top_n=int(payload.get("top_n") or 10),
            rebalance_freq=payload.get("rebalance_freq") or "W-FRI",
            fee_bps=float(payload.get("fee_bps") or 5.0),
        )
        return backtest(cfg)

    # 完整平台 UI（侧边栏/主工作区/AI对话）
    @app.get("/app", response_class=HTMLResponse)
    def platform_app():
        return """
<!doctype html>
<html>
  <head>
    <meta charset='utf-8' />
    <title>OpenBB-CN Platform</title>
    <script src=\"https://cdn.plot.ly/plotly-2.35.2.min.js\"></script>
    <style>
      :root { --bg:#0f1115; --panel:#151822; --text:#eaeef2; --muted:#a0a6b1; --accent:#3b82f6; }
      html,body { margin:0; height:100%; background:var(--bg); color:var(--text); font-family: ui-sans-serif, system-ui, Segoe UI, Roboto, Helvetica, Arial; }
      .layout { display:grid; grid-template-columns: 300px 1fr 380px; grid-template-rows: 56px 1fr; height:100%; }
      .topbar { grid-column: 1 / 4; display:flex; align-items:center; gap:12px; padding:0 16px; background:#0c0e13; border-bottom:1px solid #1d2233; }
      .brand { font-weight:600; color:#8ab4ff; }
      .left { background:var(--panel); border-right:1px solid #20263a; padding:12px; overflow:auto; }
      .main { background:transparent; padding:12px; overflow:auto; }
      .right { background:var(--panel); border-left:1px solid #20263a; padding:12px; overflow:auto; }
      input, button, select, textarea { background:#0c0f16; border:1px solid #26304b; color:var(--text); padding:8px 10px; border-radius:6px; }
      button { background:#1f2937; cursor:pointer; }
      .row { display:flex; gap:8px; align-items:center; flex-wrap:wrap; }
      .watch { margin-top:12px; }
      .item {
        padding:10px 12px;
        border:1px solid #26304b;
        border-radius:8px;
        margin-bottom:8px;
        cursor:pointer;
        transition: all 0.2s ease;
        background: var(--panel);
      }
      .item:hover {
        border-color: #3b82f6;
        background: #1e293b;
        transform: translateX(2px);
      }
      .item button {
        background: #374151;
        border: 1px solid #4b5563;
        border-radius: 4px;
        padding: 4px 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        color: #f3f4f6;
        font-size: 12px;
      }
      .item button:hover {
        background: #fbbf24;
        border-color: #f59e0b;
        color: #000;
      }
      .grid { display:grid; grid-template-columns: 1fr 1fr; gap:12px; }
      .panel { background:var(--panel); border:1px solid #20263a; border-radius:10px; padding:10px; }
      .panel h4 { margin:0 0 8px 0; color:#c8d3f5; }
      #kline { height:400px; }
      #volume { height:130px; }
      #ratio { height:220px; }
      #ind-mini { height:260px; }
      .cards { display:grid; grid-template-columns: repeat(3,minmax(0,1fr)); gap:10px; }
      .card { background:#10131b; border:1px solid #26304b; border-left:6px solid #334155; padding:10px; border-radius:8px; }
      .good { border-left-color:#16a34a; }
      .warn { border-left-color:#f59e0b; }
      .bad { border-left-color:#ef4444; }
      .chatbox { height:260px; overflow:auto; background:#0c0f16; border:1px solid #26304b; padding:8px; border-radius:8px; }
      .msg { margin:6px 0; }
      .role { font-size:12px; color:var(--muted); }
    </style>
  </head>
  <body>
    <div class=\"layout\">
      <div class=\"topbar\"> 
        <div class=\"brand\">OpenBB-CN</div> 
        <div class=\"row\">
          <input id=\"q\" placeholder=\"输入股票名称(如:平安银行)或6位代码(如:000001)\" />
          <button onclick=\"search()\">🔍 搜索</button>
          <button onclick=\"addFromInput()\">加入自选</button>
          <button onclick=\"clearWatch()\">清空自选</button>
        </div>
        <div style=\"font-size:12px;color:#666;margin-top:5px;\">
          💡 支持：股票名称、6位代码(无需.SH/.SZ后缀)，点击搜索按钮或回车搜索
        </div>
      </div>
      <div class=\"left\">
        <h4>搜索结果</h4>
        <div id=\"results\"></div>
        <h4 style=\"margin-top:10px\">自选（本地）</h4>
        <div id=\"watch\" class=\"watch\"></div>
      </div>
      <div class=\"main\">
        <div class=\"grid\"> 
          <div class=\"panel\">
            <h4 id=\"title\">K线</h4>
            <div id=\"kline\"></div>
            <div id=\"volume\"></div>
          </div>
          <div class=\"panel\">
            <h4>估值倍数</h4>
            <div id=\"ratio\"></div>
            <h4>指标卡片</h4>
            <div id=\"cards\" class=\"cards\"></div>
            <div style=\"margin-top:8px\"> 
              <select id=\"ti\"> 
                <option value=\"RSI\">RSI</option> 
                <option value=\"MACD\">MACD</option> 
                <option value=\"BOLL\">BOLL</option> 
                <option value=\"KDJ\">KDJ</option> 
                <option value=\"ATR\">ATR</option> 
                <option value=\"VWAP\">VWAP</option> 
              </select> 
              <button onclick=\"drawTi()\">绘制指标</button> 
            </div> 
            <div id=\"ti-mini\" style=\"height:160px;margin-top:6px\"></div> 
          </div>
          <div class=\"panel\" style=\"grid-column: 1 / 3;\">
            <h4>自研指标小图</h4>
            <div id=\"ind-mini\"></div>
          </div>
        </div>
      </div>
      <div class=\"right\">
        <h4>AI 分析</h4>
        <div id=\"chat\" class=\"chatbox\"></div>
        <div class=\"row\" style=\"margin-top:8px\">
          <textarea id=\"prompt\" placeholder=\"想问AI的问题（可留空，默认生成结构化结论）\" style=\"width:100%\"></textarea>
        </div>
        <div class=\"row\" style=\"margin-top:8px\">
          <button onclick=\"askAI()\">生成分析</button>
        </div>
      </div>
    </div>

    <script>
      const $ = (id)=> document.getElementById(id);
      function ls(){ try { return JSON.parse(localStorage.getItem('watch')||'[]'); } catch(e){ return []; } }
      function save(w){ localStorage.setItem('watch', JSON.stringify(w)); }
      function addWatch(ts_code, name){ const w = ls(); if(!w.find(x=>x.ts_code===ts_code)){ w.push({ts_code, name}); save(w); renderWatch(); } }
      function removeWatch(ts_code){ const w = ls().filter(x=>x.ts_code!==ts_code); save(w); renderWatch(); }
      function clearWatch(){ save([]); renderWatch(); }
      function renderWatch(){
        const w = ls();
        $('watch').innerHTML = w.map(x=>{
          // 去除.SH/.SZ后缀用于显示
          const displayCode = x.ts_code.replace(/\.(SH|SZ)$/, '');
          return `<div class=\"item\" onclick=\"load('${x.ts_code}','${x.name||''}')\">${displayCode} ${x.name||''} <span style='float:right;color:#6b7280' onclick=\"event.stopPropagation();removeWatch('${x.ts_code}')\">×</span></div>`;
        }).join('') || '<div style="color:#6b7280">暂无</div>';
      }

      async function search(){
        const q = $('q').value.trim();
        if (!q) {
          $('results').innerHTML = '<div style="color:#6b7280">请输入股票名称或代码</div>';
          return;
        }

        // 显示搜索状态
        $('results').innerHTML = '<div style="color:#3b82f6">🔍 搜索中...</div>';

        try {
          const data = await fetch('/search?query='+encodeURIComponent(q)).then(r=>r.json());

          if (data && data.length > 0) {
            // 显示搜索结果（去除.SH/.SZ后缀显示）
            $('results').innerHTML = data.map((x, index) => {
              const isFirst = index === 0;
              const badge = isFirst ? '<span style="background:#10b981;color:white;padding:2px 6px;border-radius:3px;font-size:10px;margin-left:5px;">自动加载</span>' : '';
              // 去除.SH/.SZ后缀用于显示
              const displayCode = x.ts_code.replace(/\.(SH|SZ)$/, '');
              return `<div class="item" onclick="load('${x.ts_code}','${x.name||''}')" style="${isFirst ? 'border-left:3px solid #10b981;' : ''}">${displayCode} ${x.name||''} ${badge} <button style='float:right' onclick="event.stopPropagation();addWatch('${x.ts_code}','${x.name||''}');load('${x.ts_code}','${x.name||''}')" title="加入自选并加载">⭐</button></div>`;
            }).join('');

            // 自动加载第一个匹配结果
            load(data[0].ts_code, data[0].name);

            // 显示加载提示
            setTimeout(() => {
              const displayCode = data[0].ts_code.replace(/\.(SH|SZ)$/, '');
              $('title').textContent = `📊 ${data[0].name || displayCode} - 自动加载`;
            }, 100);

          } else {
            $('results').innerHTML = '<div style="color:#ef4444">❌ 未找到匹配的股票</div>';
          }
        } catch (error) {
          $('results').innerHTML = '<div style="color:#ef4444">❌ 搜索失败，请重试</div>';
          console.error('搜索错误:', error);
        }
      }

      // 删除loadFromInput函数，功能已合并到search中

      async function addFromInput(){
        const s = $('q').value.trim();
        if(!s){ return; }

        // 如果包含.则直接添加
        if(s.includes('.')){
          addWatch(s, '');
          renderWatch();
          return;
        }

        // 否则搜索匹配
        const data = await fetch('/search?query='+encodeURIComponent(s)).then(r=>r.json());
        if (data && data.length > 0) {
          addWatch(data[0].ts_code, data[0].name||'');
          renderWatch();
        } else {
          alert('未找到匹配的股票');
        }
      }

      function last(arr, key){ for(let i=arr.length-1;i>=0;i--){ const v= key?arr[i][key]:arr[i]; if(v!=null && !Number.isNaN(v)) return arr[i]; } return null; }
      function clsOf(good,bad,val){ return val==null? '' : (val>=good? 'good' : (val<=bad? 'bad' : 'warn')); }
      const fmt = (x)=> x==null? '—' : (Math.abs(x)>1e4? Number(x).toExponential(2) : Number(x).toFixed(3));

      let currentSym = null;
      async function load(sym, name){
        currentSym = sym;
        // 去除.SH/.SZ后缀用于显示
        const displayCode = sym.replace(/\.(SH|SZ)$/, '');
        $('title').textContent = `K线 - ${displayCode} ${name||''}`;
        // K线/成交量
        const rows = await fetch('/equity/price/historical/data?symbol='+encodeURIComponent(sym)).then(r=>r.json());
        const d = rows.map(r=>r.trade_date), o=rows.map(r=>r.open), h=rows.map(r=>r.high), l=rows.map(r=>r.low), c=rows.map(r=>r.close), v=rows.map(r=>r.volume);
        Plotly.newPlot('kline', [{x:d, open:o, high:h, low:l, close:c, type:'candlestick', name:displayCode}], {margin:{t:24,r:16,b:24,l:40}, xaxis:{rangeslider:{visible:false}}}, {responsive:true});
        Plotly.newPlot('volume', [{x:d, y:v, type:'bar', name:'volume'}], {margin:{t:8}}, {responsive:true});

        // 估值倍数
        const r = await fetch('/equity/basic/ratios?symbol='+encodeURIComponent(sym)).then(r=>r.json());
        const rd = r.map(x=>x.trade_date);
        Plotly.newPlot('ratio', [
          {x:rd, y:r.map(x=>x.pe), name:'PE', type:'scatter', mode:'lines'},
          {x:rd, y:r.map(x=>x.pb), name:'PB', type:'scatter', mode:'lines'},
          {x:rd, y:r.map(x=>x.ps), name:'PS', type:'scatter', mode:'lines'},
        ], {margin:{t:8}, legend:{orientation:'h'}}, {responsive:true});

        // 指标并行
        const [vcm, tei, lvr, api, blce, mtr] = await Promise.all([
          fetch('/indicator/vcm?symbol='+encodeURIComponent(sym)).then(r=>r.json()),
          fetch('/indicator/tei?symbol='+encodeURIComponent(sym)).then(r=>r.json()),
          fetch('/indicator/lvr?symbol='+encodeURIComponent(sym)).then(r=>r.json()),
          fetch('/indicator/api?symbol='+encodeURIComponent(sym)).then(r=>r.json()),
          fetch('/indicator/blce?symbol='+encodeURIComponent(sym)).then(r=>r.json()),
          fetch('/indicator/mtr?symbol='+encodeURIComponent(sym)).then(r=>r.json()),
        ]);

        // 卡片与小图
        const v_vcm = last(vcm,'v')?.v ?? null;
        const v_tei = last(tei,'v')?.v ?? null;
        const v_lvr = last(lvr,'v')?.v ?? null;
        const v_api = last(api,'v')?.v ?? null;
        const b = last(blce);
        const v_prob = b? b.prob_up : null;
        const v_mtr = last(mtr,'v')?.v ?? null;
        const cards = [];
        cards.push(card('VCM', v_vcm, '放量顺势动量，>0.5看多，<-0.5看空', clsOf(0.5,-0.5,v_vcm)));
        cards.push(card('TEI', v_tei, '趋势熵分，>0.2稳态上行，<-0.2衰减', clsOf(0.2,-0.2,v_tei)));
        cards.push(card('LVR', v_lvr, '流动性真空风险，>1需警惕', v_lvr!=null && v_lvr>1? 'bad':'good'));
        cards.push(card('API', v_api, '非对称压力，>0.1上行，<-0.1下行', clsOf(0.1,-0.1,v_api)));
        cards.push(card('BLCE↑', v_prob==null? '—' : (v_prob*100).toFixed(1)+'%', '收敛→爆发上行概率', v_prob!=null && v_prob>0.6? 'good': (v_prob!=null && v_prob<0.4? 'bad':'warn')));
        cards.push(card('MTR', v_mtr, '多尺度共振，>0.5多头，<-0.2空头', clsOf(0.5,-0.2,v_mtr)));
        $('cards').innerHTML = cards.join('');

        // 指标小图
        Plotly.newPlot('ind-mini', [
          {x:vcm.map(x=>x.t), y:vcm.map(x=>x.v), name:'VCM', type:'scatter', mode:'lines'},
          {x:tei.map(x=>x.t), y:tei.map(x=>x.v), name:'TEI', type:'scatter', mode:'lines'},
          {x:lvr.map(x=>x.t), y:lvr.map(x=>x.v), name:'LVR', type:'scatter', mode:'lines'},
          {x:api.map(x=>x.t), y:api.map(x=>x.v), name:'API', type:'scatter', mode:'lines'},
          {x:mtr.map(x=>x.t), y:mtr.map(x=>x.v), name:'MTR', type:'scatter', mode:'lines'},
        ], {margin:{t:8}, legend:{orientation:'h'}}, {responsive:true});

        // AI 自动分析
        const indicators = await fetch('/equity/price/indicators?symbol='+encodeURIComponent(sym)).then(r=>r.json());
        const custom = { vcm:v_vcm, tei:v_tei, lvr:v_lvr, api:v_api, mtr:v_mtr, blce_prob_up:v_prob };
        const merged = { ...indicators, custom };
        await sendAI(sym, merged, $('prompt').value.trim());
      }

      async function drawTi(){
        const sym = currentSym; if(!sym){ return; }
        const ind = await fetch('/equity/price/indicators?symbol='+encodeURIComponent(sym)).then(r=>r.json());
        const L = ind.latest || {};
        const typ = $('ti').value;
        if(typ==='RSI'){
          Plotly.newPlot('ti-mini', [{x:[1],y:[L.rsi||null],type:'scatter',mode:'markers',name:'RSI'}], {margin:{t:8}, yaxis:{range:[0,100]}}, {responsive:true});
        } else if(typ==='MACD'){
          Plotly.newPlot('ti-mini', [
            {x:[1], y:[L.macd||null], name:'MACD', type:'bar'},
            {x:[1], y:[L.macd_signal||null], name:'Signal', type:'bar'},
          ], {margin:{t:8}, barmode:'group'}, {responsive:true});
        } else if(typ==='BOLL'){
          Plotly.newPlot('ti-mini', [
            {x:[1], y:[L.boll_upper||null], name:'Upper', type:'scatter', mode:'markers'},
            {x:[1], y:[L.boll_mid||null], name:'Mid', type:'scatter', mode:'markers'},
            {x:[1], y:[L.boll_lower||null], name:'Lower', type:'scatter', mode:'markers'},
          ], {margin:{t:8}}, {responsive:true});
        } else if(typ==='KDJ'){
          Plotly.newPlot('ti-mini', [
            {x:[1], y:[L.kdj_k||null], name:'K', type:'bar'},
            {x:[1], y:[L.kdj_d||null], name:'D', type:'bar'},
            {x:[1], y:[L.kdj_j||null], name:'J', type:'bar'},
          ], {margin:{t:8}, barmode:'group'}, {responsive:true});
        } else if(typ==='ATR'){
          Plotly.newPlot('ti-mini', [{x:[1], y:[L.atr||null], type:'bar', name:'ATR'}], {margin:{t:8}}, {responsive:true});
        } else if(typ==='VWAP'){
          Plotly.newPlot('ti-mini', [{x:[1], y:[L.vwap||null], type:'scatter', mode:'markers', name:'VWAP'}], {margin:{t:8}}, {responsive:true});
        }
      }

      function card(title, value, hint, cls){ return `<div class=\"card ${cls}\"><div style=\"color:#9fb3ff\"><b>${title}</b></div><div style=\"font-size:22px;margin:6px 0\">${fmt(value)}</div><div style=\"color:#9aa1ad\">${hint}</div></div>`; }

      async function sendAI(sym, indicators, prompt){
        const chat = $('chat');
        chat.innerHTML += `<div class=\"msg\"><div class=\"role\">You</div><div>${prompt||'生成结构化结论'}</div></div>`;
        const res = await fetch('/ai/analyze', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ symbol: sym, indicators, prompt }) });
        const data = await res.json();
        chat.innerHTML += `<div class=\"msg\"><div class=\"role\">AI</div><div>${(data.text||'')}</div></div>`;
        chat.scrollTop = chat.scrollHeight;
      }

      async function askAI(){
        const cur = ls()[0]; // 简化：取自选首个或最近
        if(!cur){ alert('请先在左侧选择或加入一个股票到自选'); return; }
        const indicators = await fetch('/equity/price/indicators?symbol='+encodeURIComponent(cur.ts_code)).then(r=>r.json());
        await sendAI(cur.ts_code, indicators, $('prompt').value.trim());
      }

      // 智能实时搜索功能
      let searchTimeout;
      let lastQuery = '';

      (function(){
        const inp = $('q');
        if (inp){
          // 回车键立即搜索
          inp.addEventListener('keydown', (e)=>{
            if (e.key === 'Enter'){
              clearTimeout(searchTimeout);
              search();
            }
          });

          // 输入时智能搜索（防抖）
          inp.addEventListener('input', (e)=>{
            clearTimeout(searchTimeout);
            const query = e.target.value.trim();

            // 避免重复搜索
            if (query === lastQuery) return;
            lastQuery = query;

            if (query.length === 0) {
              // 清空输入时清空结果
              $('results').innerHTML = '<div style="color:#6b7280">请输入股票名称或代码</div>';
              $('title').textContent = 'K线';
              return;
            }

            if (query.length >= 1) {  // 1个字符就开始搜索
              // 显示即时反馈
              $('results').innerHTML = '<div style="color:#6b7280">⌨️ 输入中...</div>';

              // 根据输入类型调整延迟
              let delay = 600;  // 默认延迟
              if (query.length >= 6 && query.match(/^\d+$/)) {
                delay = 300;  // 数字代码，快速搜索
              } else if (query.length >= 3) {
                delay = 400;  // 较长输入，中等延迟
              }

              searchTimeout = setTimeout(() => {
                search();
              }, delay);
            }
          });

          // 焦点事件
          inp.addEventListener('focus', () => {
            if (!inp.value.trim()) {
              $('results').innerHTML = '<div style="color:#6b7280">💡 支持股票名称、6位代码或完整代码</div>';
            }
          });
        }
      })();
      renderWatch();
    </script>
  </body>
</html>
"""

    return app


app = create_app()


def pd_isna(x):
    try:
        import pandas as pd
        return pd.isna(x)
    except Exception:
        return x is None


