"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[1792],{61792:(e,t,i)=>{i.r(t),i.d(t,{default:()=>n});i(66845);var s=i(81354),l=i(27446),a=i(9003),d=i(21e3),r=i(40864);const n=function(e){const{disabled:t,element:i,widgetMgr:n,width:h,fragmentId:u}=e,c={width:h},p="primary"===i.type?s.nW.PRIMARY:s.nW.SECONDARY,o=!i.help||h;return(0,r.jsx)("div",{className:"row-widget stButton","data-testid":"stButton",style:c,children:(0,r.jsx)(l.t,{help:i.help,children:(0,r.jsx)(a.Z<PERSON>,{kind:p,size:s.V5.SMALL,disabled:t,fluidWidth:!!i.useContainerWidth&&o,onClick:()=>n.setTriggerValue(i,{fromUi:!0},u),children:(0,r.jsx)(d.ZP,{source:i.label,allowHTML:!1,isLabel:!0,largerLabel:!0,disableLinks:!0})})})})}}}]);