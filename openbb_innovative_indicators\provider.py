"""OpenBB Innovative Indicators Provider Implementation."""

from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from warnings import warn

import pandas as pd
from openbb_core.provider.abstract.fetcher import Fetcher
from openbb_core.provider.standard_models.technical_analysis import (
    TechnicalAnalysisQueryParams,
)
from openbb_core.provider.utils.errors import EmptyDataError

from openbb_innovative_indicators.models import (
    InnovativeIndicatorsData,
    InnovativeIndicatorsQueryParams,
    TradingSignalsData,
    TradingSignalsQueryParams,
)
from openbb_innovative_indicators.utils.calculations import InnovativeCalculator
from openbb_innovative_indicators.utils.helpers import generate_signals


class InnovativeIndicatorsFetcher(
    Fetcher[InnovativeIndicatorsQueryParams, List[InnovativeIndicatorsData]]
):
    """Fetcher for innovative technical indicators."""

    @staticmethod
    def transform_query(params: Dict[str, Any]) -> InnovativeIndicatorsQueryParams:
        """Transform query parameters."""
        return InnovativeIndicatorsQueryParams(**params)

    @staticmethod
    def extract_data(
        query: InnovativeIndicatorsQueryParams,
        credentials: Optional[Dict[str, str]],
        **kwargs: Any,
    ) -> List[Dict[str, Any]]:
        """Extract data for innovative indicators calculation."""
        
        # Get historical price data (this would typically come from another provider)
        # For now, we'll assume the data is passed in kwargs or fetched from a standard provider
        
        if "price_data" not in kwargs:
            raise ValueError(
                "Price data must be provided in kwargs. "
                "This should be OHLCV data from a standard provider."
            )
        
        price_data = kwargs["price_data"]
        
        if not isinstance(price_data, pd.DataFrame):
            raise ValueError("Price data must be a pandas DataFrame")
        
        # Validate date range
        if query.start_date:
            price_data = price_data[price_data.index >= query.start_date]
        if query.end_date:
            price_data = price_data[price_data.index <= query.end_date]
        
        if price_data.empty:
            raise EmptyDataError("No data available for the specified date range.")
        
        # Initialize calculator
        calculator = InnovativeCalculator(price_data)
        
        # Calculate indicators based on query
        results = []
        
        if query.indicator in ["dmfi", "all"]:
            dmfi = calculator.calculate_dmfi(period=query.period)
        else:
            dmfi = pd.Series(index=price_data.index, dtype=float)
        
        if query.indicator in ["aisi", "all"]:
            aisi = calculator.calculate_aisi()
        else:
            aisi = pd.Series(index=price_data.index, dtype=float)
        
        if query.indicator in ["mvci", "all"]:
            mvci = calculator.calculate_mvci()
        else:
            mvci = pd.Series(index=price_data.index, dtype=float)
        
        if query.indicator in ["tafi", "all"]:
            tafi = calculator.calculate_tafi()
        else:
            tafi = pd.Series(index=price_data.index, dtype=float)
        
        if query.indicator in ["fmsi", "all"]:
            fmsi = calculator.calculate_fmsi()
        else:
            fmsi = pd.Series(index=price_data.index, dtype=float)
        
        # Generate trading signals
        indicators_dict = {
            "dmfi": dmfi,
            "aisi": aisi, 
            "mvci": mvci,
            "tafi": tafi,
            "fmsi": fmsi
        }
        
        signals_data = generate_signals(indicators_dict)
        
        # Combine all data
        for date in price_data.index:
            result = {
                "date": date.date(),
                "dmfi": dmfi.get(date) if not dmfi.empty else None,
                "aisi": aisi.get(date) if not aisi.empty else None,
                "mvci": mvci.get(date) if not mvci.empty else None,
                "tafi": tafi.get(date) if not tafi.empty else None,
                "fmsi": fmsi.get(date) if not fmsi.empty else None,
                "signal": signals_data["signal"].get(date) if "signal" in signals_data else None,
                "signal_strength": signals_data["strength"].get(date) if "strength" in signals_data else None,
            }
            results.append(result)
        
        return results

    @staticmethod
    def transform_data(
        query: InnovativeIndicatorsQueryParams,
        data: List[Dict[str, Any]],
        **kwargs: Any,
    ) -> List[InnovativeIndicatorsData]:
        """Transform data to InnovativeIndicatorsData model."""
        return [InnovativeIndicatorsData(**item) for item in data]


class TradingSignalsFetcher(
    Fetcher[TradingSignalsQueryParams, List[TradingSignalsData]]
):
    """Fetcher for trading signals based on innovative indicators."""

    @staticmethod
    def transform_query(params: Dict[str, Any]) -> TradingSignalsQueryParams:
        """Transform query parameters."""
        return TradingSignalsQueryParams(**params)

    @staticmethod
    def extract_data(
        query: TradingSignalsQueryParams,
        credentials: Optional[Dict[str, str]],
        **kwargs: Any,
    ) -> List[Dict[str, Any]]:
        """Extract trading signals data."""
        
        if "price_data" not in kwargs:
            raise ValueError("Price data must be provided in kwargs.")
        
        price_data = kwargs["price_data"]
        
        # Filter by date range
        if query.start_date:
            price_data = price_data[price_data.index >= query.start_date]
        if query.end_date:
            price_data = price_data[price_data.index <= query.end_date]
        
        if price_data.empty:
            raise EmptyDataError("No data available for the specified date range.")
        
        # Calculate all indicators
        calculator = InnovativeCalculator(price_data)
        
        indicators = {
            "dmfi": calculator.calculate_dmfi(),
            "aisi": calculator.calculate_aisi(),
            "mvci": calculator.calculate_mvci(),
            "tafi": calculator.calculate_tafi(),
            "fmsi": calculator.calculate_fmsi(),
        }
        
        # Generate detailed signals
        signals_data = generate_signals(indicators, threshold=query.threshold)
        
        results = []
        for date in price_data.index:
            if date in signals_data["signal"].index:
                result = {
                    "date": date.date(),
                    "signal": signals_data["signal"].get(date, "HOLD"),
                    "strength": signals_data["strength"].get(date, 0.0),
                    "confidence": signals_data["confidence"].get(date, 0.0),
                    "indicators_consensus": signals_data["consensus"].get(date, 0),
                    "price": price_data.loc[date, "close"],
                    "volume": price_data.loc[date, "volume"],
                }
                results.append(result)
        
        return results

    @staticmethod
    def transform_data(
        query: TradingSignalsQueryParams,
        data: List[Dict[str, Any]],
        **kwargs: Any,
    ) -> List[TradingSignalsData]:
        """Transform data to TradingSignalsData model."""
        return [TradingSignalsData(**item) for item in data]


# Provider configuration
innovative_indicators_provider = {
    "fetcher": InnovativeIndicatorsFetcher,
    "router_prefix": "/innovative_indicators",
}

trading_signals_provider = {
    "fetcher": TradingSignalsFetcher,
    "router_prefix": "/trading_signals",
}
