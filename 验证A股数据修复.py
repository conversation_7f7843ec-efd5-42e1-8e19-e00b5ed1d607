#!/usr/bin/env python3
"""
验证A股数据修复是否正确
"""

import requests
import sys

def test_astock_api_direct():
    """直接测试A股API"""
    print("🧪 直接测试A股API")
    print("=" * 50)
    
    try:
        # 测试报价API
        response = requests.get("http://127.0.0.1:6901/api/v1/astock/equity/price/quote?symbol=000001")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ A股API响应正常")
            print(f"📊 数据格式: {type(data)}")
            print(f"📊 包含字段: {list(data.keys())}")
            
            if 'results' in data and data['results']:
                stock = data['results'][0]
                print(f"📈 股票数据: {stock['name']} ({stock['symbol']})")
                print(f"💰 价格: ¥{stock['price']:.2f}")
                print(f"📊 涨跌: {stock['change']:+.2f} ({stock['change_percent']:+.2f}%)")
                print(f"📊 成交量: {stock['volume']:,}")
                return True
            else:
                print("❌ API返回数据为空")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_client_integration():
    """测试客户端集成"""
    print("\n🧪 测试客户端集成")
    print("=" * 50)
    
    try:
        # 模拟客户端调用
        from OpenBB完整客户端 import OpenBBClient
        
        client = OpenBBClient()
        
        # 测试A股报价
        quote_data = client.get_stock_quote("000001", market="CN")
        
        print(f"📊 客户端返回格式: {type(quote_data)}")
        
        if isinstance(quote_data, dict) and 'results' in quote_data:
            stock = quote_data['results'][0]
            print("✅ 客户端数据格式正确")
            print(f"📈 股票: {stock['name']} ({stock['symbol']})")
            print(f"💰 价格: ¥{stock['price']:.2f}")
            print(f"📊 涨跌: {stock['change']:+.2f} ({stock['change_percent']:+.2f}%)")
            return True
        else:
            print(f"❌ 客户端数据格式错误: {quote_data}")
            return False
            
    except Exception as e:
        print(f"❌ 客户端测试失败: {e}")
        return False

def test_currency_display():
    """测试货币显示"""
    print("\n🧪 测试货币显示逻辑")
    print("=" * 50)
    
    # 模拟客户端显示逻辑
    test_data = {
        'results': [{
            'symbol': '000001',
            'name': '平安银行',
            'price': 13.27,
            'change': 0.42,
            'change_percent': 3.25,
            'volume': 34531686
        }],
        'provider_used': 'mock_data',
        'count': 1
    }
    
    # 模拟A股市场检查
    market = "🇨🇳 A股 (Tushare/AKShare/东方财富)"
    
    if "🇨🇳 A股" in market:
        if isinstance(test_data, dict) and 'results' in test_data and test_data['results']:
            quote = test_data['results'][0]
            currency = "¥"
            price = quote.get('price', 0)
            change = quote.get('change', 0)
            change_pct = quote.get('change_percent', 0)
            volume = quote.get('volume', 0)
            name = quote.get('name', 'unknown')
            
            print("✅ 货币显示逻辑正确")
            print(f"💰 货币符号: {currency}")
            print(f"📈 股票: {name}")
            print(f"💰 当前价格: {currency}{price:.2f}")
            print(f"📊 涨跌: {currency}{change:+.2f}")
            print(f"📊 涨跌幅: {change_pct:+.2f}%")
            
            # 格式化成交量
            if volume > 100000000:
                volume_str = f"{volume/100000000:.1f}亿"
            elif volume > 10000:
                volume_str = f"{volume/10000:.1f}万"
            else:
                volume_str = f"{volume:,.0f}"
            
            print(f"📊 成交量: {volume_str}")
            return True
        else:
            print("❌ 数据格式错误")
            return False
    else:
        print("❌ 市场检查失败")
        return False

def test_multiple_stocks():
    """测试多只股票"""
    print("\n🧪 测试多只A股股票")
    print("=" * 50)
    
    stocks = ["000001", "600519", "000858", "600036", "002415"]
    
    try:
        symbols_str = ",".join(stocks)
        response = requests.get(f"http://127.0.0.1:6901/api/v1/astock/equity/price/quote?symbol={symbols_str}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ 成功获取 {data['count']} 只股票数据")
            print()
            print("📈 A股实时报价 (修复后):")
            print("-" * 80)
            print(f"{'代码':<8} {'名称':<12} {'价格':<12} {'涨跌额':<10} {'涨跌幅':<10} {'成交量':<15}")
            print("-" * 80)
            
            all_correct = True
            
            for stock in data['results']:
                symbol = stock['symbol']
                name = stock['name']
                price = stock['price']
                change = stock['change']
                change_pct = stock['change_percent']
                volume = stock['volume']
                
                # 检查数据合理性
                if price <= 0:
                    print(f"❌ {name}: 价格异常 ¥{price:.2f}")
                    all_correct = False
                    continue
                
                # 格式化成交量
                if volume > 100000000:
                    volume_str = f"{volume/100000000:.1f}亿"
                elif volume > 10000:
                    volume_str = f"{volume/10000:.1f}万"
                else:
                    volume_str = f"{volume:,}"
                
                print(f"{symbol:<8} {name:<12} ¥{price:<11.2f} {change:+.2f}     {change_pct:+.2f}%     {volume_str:<15}")
            
            print("-" * 80)
            
            if all_correct:
                print("✅ 所有股票数据格式正确，价格合理，货币符号为人民币")
                return True
            else:
                print("❌ 部分股票数据异常")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🇨🇳 A股数据修复验证")
    print("=" * 60)
    
    tests = [
        ("A股API直接测试", test_astock_api_direct),
        ("客户端集成测试", test_client_integration),
        ("货币显示测试", test_currency_display),
        ("多股票测试", test_multiple_stocks)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！A股数据显示已修复")
        print("\n💡 现在您可以在Streamlit客户端中:")
        print("1. 选择 '🇨🇳 A股' 市场")
        print("2. 输入股票代码: 000001, 600519, 000858等")
        print("3. 查看正确的人民币价格: ¥XX.XX")
        print("4. 所有金额都以人民币显示")
    else:
        print("⚠️ 部分测试失败，需要进一步修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
