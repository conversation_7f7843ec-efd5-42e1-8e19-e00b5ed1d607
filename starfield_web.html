
<!-- 动态星空背景 - HTML5 Canvas版本 -->
<canvas id="starfield" style="position: fixed; top: 0; left: 0; z-index: -1;"></canvas>

<script>
// 星空背景JavaScript代码
class StarField {
    constructor() {
        this.canvas = document.getElementById('starfield');
        this.ctx = this.canvas.getContext('2d');
        this.resize();

        this.stars = [];
        this.shootingStars = [];

        this.initStars();
        this.initShootingStars();

        window.addEventListener('resize', () => this.resize());
        this.animate();
    }

    resize() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
    }

    initStars() {
        for (let i = 0; i < 200; i++) {
            this.stars.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                size: Math.random() * 2 + 1,
                brightness: Math.random() * 0.7 + 0.3,
                twinkleSpeed: Math.random() * 0.06 + 0.02,
                twinklePhase: Math.random() * Math.PI * 2,
                color: ['#ffffff', '#ffffcc', '#ccddff'][Math.floor(Math.random() * 3)]
            });
        }
    }

    initShootingStars() {
        for (let i = 0; i < 3; i++) {
            this.shootingStars.push(this.createShootingStar());
        }
    }

    createShootingStar() {
        return {
            x: Math.random() * this.canvas.width,
            y: Math.random() * this.canvas.height * 0.5,
            speedX: Math.random() * 5 + 3,
            speedY: Math.random() * 2 + 1,
            length: Math.random() * 40 + 20,
            life: Math.random() * 60 + 60,
            maxLife: 120
        };
    }

    update() {
        // 更新星星闪烁
        this.stars.forEach(star => {
            star.twinklePhase += star.twinkleSpeed;
        });

        // 更新流星
        this.shootingStars.forEach(star => {
            star.x += star.speedX;
            star.y += star.speedY;
            star.life--;

            if (star.x > this.canvas.width + 100 ||
                star.y > this.canvas.height + 100 ||
                star.life <= 0) {
                Object.assign(star, this.createShootingStar());
            }
        });
    }

    draw() {
        // 清空画布并绘制渐变背景
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#001122');
        gradient.addColorStop(1, '#000000');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制星星
        this.stars.forEach(star => {
            const brightness = star.brightness * (0.5 + 0.5 * Math.sin(star.twinklePhase));
            this.ctx.globalAlpha = brightness;
            this.ctx.fillStyle = star.color;
            this.ctx.beginPath();
            this.ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
            this.ctx.fill();
        });

        // 绘制流星
        this.shootingStars.forEach(star => {
            if (star.life > 0) {
                const alpha = star.life / star.maxLife;
                this.ctx.globalAlpha = alpha;
                this.ctx.strokeStyle = '#ffffff';
                this.ctx.lineWidth = 2;
                this.ctx.beginPath();
                this.ctx.moveTo(star.x, star.y);
                this.ctx.lineTo(star.x - star.speedX * 10, star.y - star.speedY * 10);
                this.ctx.stroke();
            }
        });

        this.ctx.globalAlpha = 1;
    }

    animate() {
        this.update();
        this.draw();
        requestAnimationFrame(() => this.animate());
    }
}

// 启动星空背景
document.addEventListener('DOMContentLoaded', () => {
    new StarField();
});
</script>
