{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Historical Prices With the OpenBB Platform\n", "\n", "This notebook demonstrates some of the ways to approach loading historical price data using the OpenBB Platform.  The action is in the Equity module; but first, we need to initialize the notebook with the import statements block.\n", "\n", "## Import Statements"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "import pandas as pd\n", "from openbb import obb"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## The Equity Module\n", "\n", "Historical market prices typically come in the form of OHLC+V - open, high, low, close, volume.  There may be additional fields returned by a provider, but those are the expected columns.  Granularity and amount of historical data will vary by provider and subscription status.  Visit their websites to understand what your entitlements are.\n", "\n", "### openbb.equity.price.historical()\n", "\n", "- This endpoint has the most number of providers out of any function. At the time of writing, choices are:\n", "\n", "['alpha_vantage', 'cboe', 'fmp', 'intrinio', 'polygon', 'tiingo', 'yfinance']\n", "\n", "- Common parameters have been standardized across all souces, `start_date`, `end_date`, `interval`.\n", "\n", "- The default interval will be `1d`.\n", "\n", "- The depth of historical data and choices for granularity will vary by provider and subscription status.  Refer to the website and documentation of each source understand your specific entitlements.\n", "\n", "- For demonstration purposes, we will use the `openbb-yfinance` data extension."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>split_ratio</th>\n", "      <th>dividend</th>\n", "      <th>capital_gains</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-08-22</th>\n", "      <td>441.179993</td>\n", "      <td>441.179993</td>\n", "      <td>437.570007</td>\n", "      <td>438.149994</td>\n", "      <td>65062900</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  open        high         low       close    volume  \\\n", "date                                                                   \n", "2023-08-22  441.179993  441.179993  437.570007  438.149994  65062900   \n", "\n", "            split_ratio  dividend  capital_gains  \n", "date                                              \n", "2023-08-22          0.0       0.0            0.0  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df_daily = obb.equity.price.historical(symbol=\"spy\", provider=\"yfinance\")\n", "df_daily.to_df().head(1)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["To load the entire history available from a source, pick a starting date well beyond what it might be. For example, `1900-01-01`"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>split_ratio</th>\n", "      <th>dividend</th>\n", "      <th>capital_gains</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1993-01-29</th>\n", "      <td>43.96875</td>\n", "      <td>43.96875</td>\n", "      <td>43.75</td>\n", "      <td>43.9375</td>\n", "      <td>1003200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                open      high    low    close   volume  split_ratio  \\\n", "date                                                                   \n", "1993-01-29  43.96875  43.96875  43.75  43.9375  1003200          0.0   \n", "\n", "            dividend  capital_gains  \n", "date                                 \n", "1993-01-29       0.0            0.0  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df_daily = obb.equity.price.historical(\n", "    symbol=\"spy\", start_date=\"1990-01-01\", provider=\"yfinance\"\n", ").to_df()\n", "df_daily.head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Intervals\n", "\n", "The intervals are entered according to this pattern:\n", "\n", "- `1m` = One Minute\n", "- `1h` = One Hour\n", "- `1d` = One Day\n", "- `1W` = One Week\n", "- `1M` = One Month\n", "\n", "The date for monthly value is the first or last, depending on the provider.  This can be easily resampled from daily data."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>split_ratio</th>\n", "      <th>dividend</th>\n", "      <th>capital_gains</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-07-01</th>\n", "      <td>545.630005</td>\n", "      <td>565.159973</td>\n", "      <td>537.450012</td>\n", "      <td>550.809998</td>\n", "      <td>1038465500</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-01</th>\n", "      <td>552.570007</td>\n", "      <td>563.150024</td>\n", "      <td>510.269989</td>\n", "      <td>556.570007</td>\n", "      <td>954486073</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  open        high         low       close      volume  \\\n", "date                                                                     \n", "2024-07-01  545.630005  565.159973  537.450012  550.809998  1038465500   \n", "2024-08-01  552.570007  563.150024  510.269989  556.570007   954486073   \n", "\n", "            split_ratio  dividend  capital_gains  \n", "date                                              \n", "2024-07-01          0.0       0.0            0.0  \n", "2024-08-01          0.0       0.0            0.0  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_monthly = obb.equity.price.historical(\n", "    \"spy\", start_date=\"1990-01-01\", interval=\"1M\", provider=\"yfinance\"\n", ").to_df()\n", "df_monthly.tail(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Resample a Time Series\n", "\n", "`yfinance` returns the monthly data for the first day of each month.  Let's resample it to take from the last, using the daily information captured in the previous cells."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1993-01-31</th>\n", "      <td>43.968750</td>\n", "      <td>43.968750</td>\n", "      <td>43.750000</td>\n", "      <td>43.937500</td>\n", "      <td>1003200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1993-02-28</th>\n", "      <td>43.968750</td>\n", "      <td>45.125000</td>\n", "      <td>42.812500</td>\n", "      <td>44.406250</td>\n", "      <td>5417600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1993-03-31</th>\n", "      <td>44.562500</td>\n", "      <td>45.843750</td>\n", "      <td>44.218750</td>\n", "      <td>45.187500</td>\n", "      <td>3019200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1993-04-30</th>\n", "      <td>45.250000</td>\n", "      <td>45.250000</td>\n", "      <td>43.281250</td>\n", "      <td>44.031250</td>\n", "      <td>2697200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1993-05-31</th>\n", "      <td>44.093750</td>\n", "      <td>45.656250</td>\n", "      <td>43.843750</td>\n", "      <td>45.218750</td>\n", "      <td>1808000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-04-30</th>\n", "      <td>523.830017</td>\n", "      <td>524.380005</td>\n", "      <td>493.859985</td>\n", "      <td>501.980011</td>\n", "      <td>1592974000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-05-31</th>\n", "      <td>501.380005</td>\n", "      <td>533.070007</td>\n", "      <td>499.549988</td>\n", "      <td>527.369995</td>\n", "      <td>1153264400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-06-30</th>\n", "      <td>529.020020</td>\n", "      <td>550.280029</td>\n", "      <td>522.599976</td>\n", "      <td>544.219971</td>\n", "      <td>888923200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-07-31</th>\n", "      <td>545.630005</td>\n", "      <td>565.159973</td>\n", "      <td>537.450012</td>\n", "      <td>550.809998</td>\n", "      <td>1038465500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-31</th>\n", "      <td>552.570007</td>\n", "      <td>563.150024</td>\n", "      <td>510.269989</td>\n", "      <td>556.565002</td>\n", "      <td>954484078</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>380 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                  open        high         low       close      volume\n", "date                                                                  \n", "1993-01-31   43.968750   43.968750   43.750000   43.937500     1003200\n", "1993-02-28   43.968750   45.125000   42.812500   44.406250     5417600\n", "1993-03-31   44.562500   45.843750   44.218750   45.187500     3019200\n", "1993-04-30   45.250000   45.250000   43.281250   44.031250     2697200\n", "1993-05-31   44.093750   45.656250   43.843750   45.218750     1808000\n", "...                ...         ...         ...         ...         ...\n", "2024-04-30  523.830017  524.380005  493.859985  501.980011  1592974000\n", "2024-05-31  501.380005  533.070007  499.549988  527.369995  1153264400\n", "2024-06-30  529.020020  550.280029  522.599976  544.219971   888923200\n", "2024-07-31  545.630005  565.159973  537.450012  550.809998  1038465500\n", "2024-08-31  552.570007  563.150024  510.269989  556.565002   954484078\n", "\n", "[380 rows x 5 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_daily.index = pd.to_datetime(df_daily.index)\n", "(\n", "    df_daily[[\"open\", \"high\", \"low\", \"close\", \"volume\"]]\n", "    .resample(\"ME\")\n", "    .agg(\n", "        {\"open\": \"first\", \"high\": \"max\", \"low\": \"min\", \"close\": \"last\", \"volume\": \"sum\"}\n", "    )\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The block below packs an object with most intervals."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['one', 'five', 'fifteen', 'thirty', 'sixty', 'daily', 'weekly', 'monthly'])"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>split_ratio</th>\n", "      <th>dividend</th>\n", "      <th>capital_gains</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-08-12</th>\n", "      <td>534.210022</td>\n", "      <td>555.02002</td>\n", "      <td>530.950012</td>\n", "      <td>554.309998</td>\n", "      <td>242599600</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-19</th>\n", "      <td>554.72998</td>\n", "      <td>563.150024</td>\n", "      <td>553.859985</td>\n", "      <td>557.031006</td>\n", "      <td>142159243</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  open        high         low       close     volume  \\\n", "date                                                                    \n", "2024-08-12  534.210022   555.02002  530.950012  554.309998  242599600   \n", "2024-08-19   554.72998  563.150024  553.859985  557.031006  142159243   \n", "\n", "            split_ratio  dividend  capital_gains  \n", "date                                              \n", "2024-08-12            0       0.0              0  \n", "2024-08-19            0       0.0              0  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>split_ratio</th>\n", "      <th>dividend</th>\n", "      <th>capital_gains</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-08-16 09:30:00</th>\n", "      <td>551.419983</td>\n", "      <td>551.929993</td>\n", "      <td>551.289978</td>\n", "      <td>551.349976</td>\n", "      <td>1881026</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16 09:31:00</th>\n", "      <td>551.349976</td>\n", "      <td>551.77002</td>\n", "      <td>551.26001</td>\n", "      <td>551.630005</td>\n", "      <td>230595</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           open        high         low       close   volume  \\\n", "date                                                                           \n", "2024-08-16 09:30:00  551.419983  551.929993  551.289978  551.349976  1881026   \n", "2024-08-16 09:31:00  551.349976   551.77002   551.26001  551.630005   230595   \n", "\n", "                     split_ratio  dividend  capital_gains  \n", "date                                                       \n", "2024-08-16 09:30:00            0         0              0  \n", "2024-08-16 09:31:00            0         0              0  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["class HistoricalPrices:\n", "    def __init__(self, symbol, start_date, end_date, provider, **kwargs) -> None:\n", "        self.one: pd.DataFrame = (\n", "            obb.equity.price.historical(\n", "                symbol=symbol,\n", "                start_date=start_date,\n", "                end_date=end_date,\n", "                interval=\"1m\",\n", "                provider=provider,\n", "                **kwargs\n", "            )\n", "            .to_df()\n", "            .convert_dtypes()\n", "        )\n", "        self.five: pd.DataFrame = (\n", "            obb.equity.price.historical(\n", "                symbol=symbol,\n", "                start_date=start_date,\n", "                end_date=end_date,\n", "                interval=\"5m\",\n", "                provider=provider,\n", "                **kwargs\n", "            )\n", "            .to_df()\n", "            .convert_dtypes()\n", "        )\n", "        self.fifteen: pd.DataFrame = (\n", "            obb.equity.price.historical(\n", "                symbol=symbol,\n", "                start_date=start_date,\n", "                end_date=end_date,\n", "                interval=\"15m\",\n", "                provider=provider,\n", "                **kwargs\n", "            )\n", "            .to_df()\n", "            .convert_dtypes()\n", "        )\n", "        self.thirty: pd.DataFrame = (\n", "            obb.equity.price.historical(\n", "                symbol=symbol,\n", "                start_date=start_date,\n", "                end_date=end_date,\n", "                interval=\"30m\",\n", "                provider=provider,\n", "                **kwargs\n", "            )\n", "            .to_df()\n", "            .convert_dtypes()\n", "        )\n", "        self.sixty: pd.DataFrame = (\n", "            obb.equity.price.historical(\n", "                symbol=symbol,\n", "                start_date=start_date,\n", "                end_date=end_date,\n", "                interval=\"60m\",\n", "                provider=provider,\n", "                **kwargs\n", "            )\n", "            .to_df()\n", "            .convert_dtypes()\n", "        )\n", "        self.daily: pd.DataFrame = (\n", "            obb.equity.price.historical(\n", "                symbol=symbol,\n", "                start_date=start_date,\n", "                end_date=end_date,\n", "                interval=\"1d\",\n", "                provider=provider,\n", "                **kwargs\n", "            )\n", "            .to_df()\n", "            .convert_dtypes()\n", "        )\n", "        self.weekly: pd.DataFrame = (\n", "            obb.equity.price.historical(\n", "                symbol=symbol,\n", "                start_date=start_date,\n", "                end_date=end_date,\n", "                interval=\"1W\",\n", "                provider=provider,\n", "                **kwargs\n", "            )\n", "            .to_df()\n", "            .convert_dtypes()\n", "        )\n", "        self.monthly: pd.DataFrame = (\n", "            obb.equity.price.historical(\n", "                symbol=symbol,\n", "                start_date=start_date,\n", "                end_date=end_date,\n", "                interval=\"1M\",\n", "                provider=provider,\n", "                **kwargs\n", "            )\n", "            .to_df()\n", "            .convert_dtypes()\n", "        )\n", "\n", "\n", "def load_historical(\n", "    symbol: str = \"\", start_date=None, end_date=None, provider=None, **kwargs\n", ") -> HistoricalPrices:\n", "\n", "    if symbol == \"\":\n", "        display(\"Please enter a ticker symbol\")\n", "    if provider is None:\n", "        provider = \"yfinance\"\n", "    prices = HistoricalPrices(symbol, start_date, end_date, provider, **kwargs)\n", "\n", "    return prices\n", "\n", "\n", "prices = load_historical(\"spy\")\n", "display(prices.__dict__.keys())\n", "display(prices.weekly.tail(2))\n", "\n", "display(prices.one.head(2))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["To demonstrate the difference between sources, let's compare values for daily volume from several sources."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>AV Volume</th>\n", "      <th>FMP Volume</th>\n", "      <th>Intrinio Volume</th>\n", "      <th>Yahoo Volume</th>\n", "      <th>Polygon Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-08-09</th>\n", "      <td>45619558</td>\n", "      <td>45619558.0</td>\n", "      <td>45619558.0</td>\n", "      <td>45619600.0</td>\n", "      <td>45425963.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-12</th>\n", "      <td>42542069</td>\n", "      <td>42542069.0</td>\n", "      <td>42542069.0</td>\n", "      <td>42542100.0</td>\n", "      <td>42533175.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-13</th>\n", "      <td>52333073</td>\n", "      <td>52333073.0</td>\n", "      <td>52333073.0</td>\n", "      <td>52333100.0</td>\n", "      <td>50110167.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-14</th>\n", "      <td>42446929</td>\n", "      <td>42446929.0</td>\n", "      <td>42446929.0</td>\n", "      <td>42446900.0</td>\n", "      <td>42362522.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-15</th>\n", "      <td>60846812</td>\n", "      <td>60846812.0</td>\n", "      <td>60846812.0</td>\n", "      <td>60846800.0</td>\n", "      <td>60762738.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16</th>\n", "      <td>44430728</td>\n", "      <td>44430728.0</td>\n", "      <td>44430728.0</td>\n", "      <td>44430700.0</td>\n", "      <td>44368969.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-19</th>\n", "      <td>39121793</td>\n", "      <td>39121793.0</td>\n", "      <td>39121793.0</td>\n", "      <td>39121800.0</td>\n", "      <td>38648958.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-20</th>\n", "      <td>33732264</td>\n", "      <td>33732264.0</td>\n", "      <td>33732264.0</td>\n", "      <td>33732300.0</td>\n", "      <td>33693989.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-21</th>\n", "      <td>41514600</td>\n", "      <td>38682509.0</td>\n", "      <td>41514600.0</td>\n", "      <td>41467000.0</td>\n", "      <td>41532360.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            AV Volume  FMP Volume  Intrinio Volume  Yahoo Volume  \\\n", "date                                                               \n", "2024-08-09   45619558  45619558.0       45619558.0    45619600.0   \n", "2024-08-12   42542069  42542069.0       42542069.0    42542100.0   \n", "2024-08-13   52333073  52333073.0       52333073.0    52333100.0   \n", "2024-08-14   42446929  42446929.0       42446929.0    42446900.0   \n", "2024-08-15   60846812  60846812.0       60846812.0    60846800.0   \n", "2024-08-16   44430728  44430728.0       44430728.0    44430700.0   \n", "2024-08-19   39121793  39121793.0       39121793.0    39121800.0   \n", "2024-08-20   33732264  33732264.0       33732264.0    33732300.0   \n", "2024-08-21   41514600  38682509.0       41514600.0    41467000.0   \n", "\n", "            Polygon Volume  \n", "date                        \n", "2024-08-09      45425963.0  \n", "2024-08-12      42533175.0  \n", "2024-08-13      50110167.0  \n", "2024-08-14      42362522.0  \n", "2024-08-15      60762738.0  \n", "2024-08-16      44368969.0  \n", "2024-08-19      38648958.0  \n", "2024-08-20      33693989.0  \n", "2024-08-21      41532360.0  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Collect the data\n", "\n", "yahoo = obb.equity.price.historical(\"spy\", provider=\"yfinance\").to_df()\n", "alphavantage = obb.equity.price.historical(\"spy\", provider=\"alpha_vantage\").to_df()\n", "intrinio = obb.equity.price.historical(\"spy\", provider=\"intrinio\").to_df()\n", "fmp = obb.equity.price.historical(\"spy\", provider=\"fmp\").to_df()\n", "polygon = obb.equity.price.historical(\"spy\", provider=\"polygon\").to_df()\n", "\n", "# Make a new DataFrame with just the volume columns\n", "compare = pd.DataFrame()\n", "compare[\"AV Volume\"] = alphavantage[\"volume\"].tail(10)\n", "compare[\"FMP Volume\"] = fmp[\"volume\"].tail(10)\n", "compare[\"Intrinio Volume\"] = intrinio[\"volume\"].tail(10)\n", "compare[\"Yahoo Volume\"] = yahoo[\"volume\"].tail(10)\n", "compare[\"Polygon Volume\"] = polygon[\"volume\"].tail(10)\n", "\n", "compare.dropna(how=\"any\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Other Types of Symbols\n", "\n", "Other types of assets and ticker symbols can be loaded from `obb.equity.price.historical()`, below are some examples but not an exhaustive list.\n", "\n", "### Share Classes\n", "\n", "Some sources use `-` as the distinction between a share class, e.g., `BRK-A` and `BRK-B`. Other formats include:\n", "\n", "- A period: `BRK.A`\n", "- A slash: `BRK/A`\n", "- No separator, the share class becomes the fourth or fifth letter.\n", "\n", "```python\n", "obb.equity.price.historical(\"brk.b\", provider=\"polygon\")\n", "```\n", "\n", "```python\n", "obb.equity.price.historical(\"brk-b\", provider=\"fmp\")\n", "```\n", "\n", "While some providers handle the different formats on their end, others do not. This is something to consider when no results are returned from one source. Some may even use a combination, or accept multiple variations. Sometimes there is no real logic behind the additional characters, `GOOGL` vs. `GOOG`. These are known unknown variables of ticker symbology, what's good for one source may return errors from another. \n", "\n", "### Regional Identifiers\n", "\n", "With providers supporting market data from multiple jurisdictions, the most common method for requesting data outside of US-listings is to append a suffix to the ticker symbol (e.g., `RELIANCE.NS` for Indian equities). Formats may be unique to a provider, so it is best to review the source's documentation for an overview of their specific conventions. [This page](https://help.yahoo.com/kb/SLN2310.html) on Yahoo describes how they format symbols, which many others follow to some degree.\n", "\n", "### Indexes\n", "\n", "Sources will have their own treatment of these symbols, some examples are:\n", "\n", "- YahooFinance/FMP/CBOE: ^RUT\n", "- Polygon: I:NDX\n", "\n", "### Currencies\n", "\n", "FX symbols face the same dilemna as share classes, there are several variations of the same symbol.\n", "\n", "- YahooFinance: `EURUSD=X`\n", "- Polygon: `C:EURUSD`\n", "- AlphaVantage/FMP: `EURUSD`\n", "\n", "**The symbol prefixes are handled internally when `obb.currency.price.historical()` is used to enter a pair with no extra characters.**\n", "\n", "### Crypto\n", "\n", "Similar, but different to FX tickers.\n", "\n", "- YahooFinance: `BTC-USD`\n", "- Polygon: `X:BTCUSD`\n", "- AlphaVantage/FMP: `BTCUSD`\n", "\n", "**The symbol prefixes are handled internally when `obb.crypto.price.historical()` is used to enter a pair with no extra characters and placing the fiat currency second.**\n", "\n", "### Futures\n", "\n", "Historical prices for active contracts, and the continuation chart, can be fetched via `yfinance`.\n", "\n", "- Continuous front-month: `CL=F`\n", "- December 2023 contract: `CLZ24.NYM`\n", "- March 2024 contract: `CLH24.NYM`\n", "\n", "Individual contracts will require knowing which of the CME venues the future is listed on. `[\"NYM\", \"NYB\", \"CME\", \"CBT\"]`.\n", "\n", "### Options\n", "\n", "Individual options contracts are also loadable from `openbb.equity.price.historical()`.\n", "\n", "- YahooFinance: `SPY241220P00400000`\n", "- Polygon: `O:SPY241220P00400000`"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["These examples represent only a few methods for fetching historical price data.  Explore the contents of each module to find more!"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>split_ratio</th>\n", "      <th>dividend</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-08-22</th>\n", "      <td>25.10</td>\n", "      <td>25.100000</td>\n", "      <td>25.10</td>\n", "      <td>25.100000</td>\n", "      <td>11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-23</th>\n", "      <td>25.00</td>\n", "      <td>25.000000</td>\n", "      <td>24.50</td>\n", "      <td>24.500000</td>\n", "      <td>2</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-24</th>\n", "      <td>25.00</td>\n", "      <td>25.200001</td>\n", "      <td>25.00</td>\n", "      <td>25.200001</td>\n", "      <td>2</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-25</th>\n", "      <td>25.35</td>\n", "      <td>25.350000</td>\n", "      <td>24.18</td>\n", "      <td>24.549999</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-29</th>\n", "      <td>24.00</td>\n", "      <td>24.700001</td>\n", "      <td>22.50</td>\n", "      <td>23.910000</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16</th>\n", "      <td>5.95</td>\n", "      <td>6.100000</td>\n", "      <td>5.95</td>\n", "      <td>5.990000</td>\n", "      <td>4</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-19</th>\n", "      <td>5.92</td>\n", "      <td>5.920000</td>\n", "      <td>5.71</td>\n", "      <td>5.710000</td>\n", "      <td>40</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-20</th>\n", "      <td>5.73</td>\n", "      <td>6.240000</td>\n", "      <td>5.73</td>\n", "      <td>6.240000</td>\n", "      <td>42</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-21</th>\n", "      <td>6.28</td>\n", "      <td>6.660000</td>\n", "      <td>6.28</td>\n", "      <td>6.440000</td>\n", "      <td>276</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-22</th>\n", "      <td>6.29</td>\n", "      <td>6.660000</td>\n", "      <td>6.29</td>\n", "      <td>6.660000</td>\n", "      <td>4</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>234 rows × 7 columns</p>\n", "</div>"], "text/plain": ["             open       high    low      close  volume  split_ratio  dividend\n", "date                                                                         \n", "2023-08-22  25.10  25.100000  25.10  25.100000      11          0.0       0.0\n", "2023-08-23  25.00  25.000000  24.50  24.500000       2          0.0       0.0\n", "2023-08-24  25.00  25.200001  25.00  25.200001       2          0.0       0.0\n", "2023-08-25  25.35  25.350000  24.18  24.549999       0          0.0       0.0\n", "2023-08-29  24.00  24.700001  22.50  23.910000       0          0.0       0.0\n", "...           ...        ...    ...        ...     ...          ...       ...\n", "2024-08-16   5.95   6.100000   5.95   5.990000       4          0.0       0.0\n", "2024-08-19   5.92   5.920000   5.71   5.710000      40          0.0       0.0\n", "2024-08-20   5.73   6.240000   5.73   6.240000      42          0.0       0.0\n", "2024-08-21   6.28   6.660000   6.28   6.440000     276          0.0       0.0\n", "2024-08-22   6.29   6.660000   6.29   6.660000       4          0.0       0.0\n", "\n", "[234 rows x 7 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.equity.price.historical(\"SPY251219P00400000\", provider=\"yfinance\").to_df()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1978-01-03</th>\n", "      <td>94.74</td>\n", "      <td>95.15</td>\n", "      <td>93.49</td>\n", "      <td>93.82</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1978-01-04</th>\n", "      <td>93.16</td>\n", "      <td>94.10</td>\n", "      <td>92.57</td>\n", "      <td>93.52</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1978-01-05</th>\n", "      <td>94.18</td>\n", "      <td>94.53</td>\n", "      <td>92.51</td>\n", "      <td>92.74</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1978-01-06</th>\n", "      <td>92.06</td>\n", "      <td>92.66</td>\n", "      <td>91.05</td>\n", "      <td>91.62</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1978-01-09</th>\n", "      <td>90.82</td>\n", "      <td>91.48</td>\n", "      <td>89.97</td>\n", "      <td>90.64</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-15</th>\n", "      <td>5501.13</td>\n", "      <td>5546.23</td>\n", "      <td>5501.13</td>\n", "      <td>5543.22</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16</th>\n", "      <td>5530.50</td>\n", "      <td>5561.98</td>\n", "      <td>5525.17</td>\n", "      <td>5554.25</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-19</th>\n", "      <td>5557.23</td>\n", "      <td>5608.30</td>\n", "      <td>5550.74</td>\n", "      <td>5608.25</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-20</th>\n", "      <td>5602.88</td>\n", "      <td>5620.51</td>\n", "      <td>5585.50</td>\n", "      <td>5597.12</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-21</th>\n", "      <td>5603.09</td>\n", "      <td>5632.68</td>\n", "      <td>5591.57</td>\n", "      <td>5620.85</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>11509 rows × 5 columns</p>\n", "</div>"], "text/plain": ["               open     high      low    close  volume\n", "date                                                  \n", "1978-01-03    94.74    95.15    93.49    93.82       0\n", "1978-01-04    93.16    94.10    92.57    93.52       0\n", "1978-01-05    94.18    94.53    92.51    92.74       0\n", "1978-01-06    92.06    92.66    91.05    91.62       0\n", "1978-01-09    90.82    91.48    89.97    90.64       0\n", "...             ...      ...      ...      ...     ...\n", "2024-08-15  5501.13  5546.23  5501.13  5543.22       0\n", "2024-08-16  5530.50  5561.98  5525.17  5554.25       0\n", "2024-08-19  5557.23  5608.30  5550.74  5608.25       0\n", "2024-08-20  5602.88  5620.51  5585.50  5597.12       0\n", "2024-08-21  5603.09  5632.68  5591.57  5620.85       0\n", "\n", "[11509 rows x 5 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.equity.price.historical(\"SPX\", provider=\"cboe\").to_df()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>vwap</th>\n", "      <th>adj_close</th>\n", "      <th>unadjusted_volume</th>\n", "      <th>change</th>\n", "      <th>change_percent</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-08-22</th>\n", "      <td>4415.33008</td>\n", "      <td>4418.58984</td>\n", "      <td>4382.77002</td>\n", "      <td>4387.54980</td>\n", "      <td>3522760000</td>\n", "      <td>4396.30</td>\n", "      <td>4387.54980</td>\n", "      <td>3.522760e+09</td>\n", "      <td>-27.78028</td>\n", "      <td>-0.006292</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-23</th>\n", "      <td>4396.43994</td>\n", "      <td>4443.18018</td>\n", "      <td>4396.43994</td>\n", "      <td>4436.00977</td>\n", "      <td>3837270000</td>\n", "      <td>4425.21</td>\n", "      <td>4436.00977</td>\n", "      <td>3.837270e+09</td>\n", "      <td>39.56983</td>\n", "      <td>0.009000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-24</th>\n", "      <td>4455.16016</td>\n", "      <td>4458.29980</td>\n", "      <td>4375.54980</td>\n", "      <td>4376.31006</td>\n", "      <td>3723470000</td>\n", "      <td>4403.39</td>\n", "      <td>4376.31006</td>\n", "      <td>3.723470e+09</td>\n", "      <td>-78.85010</td>\n", "      <td>-0.017700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-25</th>\n", "      <td>4389.37988</td>\n", "      <td>4418.45996</td>\n", "      <td>4356.29004</td>\n", "      <td>4405.70996</td>\n", "      <td>3296180000</td>\n", "      <td>4393.49</td>\n", "      <td>4405.70996</td>\n", "      <td>3.296180e+09</td>\n", "      <td>16.33008</td>\n", "      <td>0.003720</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-28</th>\n", "      <td>4426.02979</td>\n", "      <td>4439.56006</td>\n", "      <td>4414.97998</td>\n", "      <td>4433.31006</td>\n", "      <td>2957230000</td>\n", "      <td>4429.28</td>\n", "      <td>4433.31006</td>\n", "      <td>2.957230e+09</td>\n", "      <td>7.28027</td>\n", "      <td>0.001645</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16</th>\n", "      <td>5530.50000</td>\n", "      <td>5561.97998</td>\n", "      <td>5525.16992</td>\n", "      <td>5554.25000</td>\n", "      <td>3357690000</td>\n", "      <td>5542.97</td>\n", "      <td>5554.25000</td>\n", "      <td>3.357690e+09</td>\n", "      <td>23.75000</td>\n", "      <td>0.004294</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-19</th>\n", "      <td>5557.22998</td>\n", "      <td>5608.29981</td>\n", "      <td>5550.74023</td>\n", "      <td>5608.25000</td>\n", "      <td>3222050000</td>\n", "      <td>5581.13</td>\n", "      <td>5608.25000</td>\n", "      <td>3.222050e+09</td>\n", "      <td>51.02002</td>\n", "      <td>0.009181</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-20</th>\n", "      <td>5602.87988</td>\n", "      <td>5620.50977</td>\n", "      <td>5585.50000</td>\n", "      <td>5597.12012</td>\n", "      <td>2994420000</td>\n", "      <td>5601.50</td>\n", "      <td>5597.12012</td>\n", "      <td>2.994420e+09</td>\n", "      <td>-5.75976</td>\n", "      <td>-0.001028</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-21</th>\n", "      <td>5603.08984</td>\n", "      <td>5632.68018</td>\n", "      <td>5591.56982</td>\n", "      <td>5620.85010</td>\n", "      <td>1982137065</td>\n", "      <td>5612.05</td>\n", "      <td>5620.85010</td>\n", "      <td>1.982137e+09</td>\n", "      <td>17.76026</td>\n", "      <td>0.003170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-22</th>\n", "      <td>5637.77000</td>\n", "      <td>5643.22000</td>\n", "      <td>5563.54000</td>\n", "      <td>5577.27000</td>\n", "      <td>1218069912</td>\n", "      <td>5594.68</td>\n", "      <td>5577.27000</td>\n", "      <td>1.218070e+09</td>\n", "      <td>-60.50000</td>\n", "      <td>-0.010731</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>253 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                  open        high         low       close      volume  \\\n", "date                                                                     \n", "2023-08-22  4415.33008  4418.58984  4382.77002  4387.54980  3522760000   \n", "2023-08-23  4396.43994  4443.18018  4396.43994  4436.00977  3837270000   \n", "2023-08-24  4455.16016  4458.29980  4375.54980  4376.31006  3723470000   \n", "2023-08-25  4389.37988  4418.45996  4356.29004  4405.70996  3296180000   \n", "2023-08-28  4426.02979  4439.56006  4414.97998  4433.31006  2957230000   \n", "...                ...         ...         ...         ...         ...   \n", "2024-08-16  5530.50000  5561.97998  5525.16992  5554.25000  3357690000   \n", "2024-08-19  5557.22998  5608.29981  5550.74023  5608.25000  3222050000   \n", "2024-08-20  5602.87988  5620.50977  5585.50000  5597.12012  2994420000   \n", "2024-08-21  5603.08984  5632.68018  5591.56982  5620.85010  1982137065   \n", "2024-08-22  5637.77000  5643.22000  5563.54000  5577.27000  1218069912   \n", "\n", "               vwap   adj_close  unadjusted_volume    change  change_percent  \n", "date                                                                          \n", "2023-08-22  4396.30  4387.54980       3.522760e+09 -27.78028       -0.006292  \n", "2023-08-23  4425.21  4436.00977       3.837270e+09  39.56983        0.009000  \n", "2023-08-24  4403.39  4376.31006       3.723470e+09 -78.85010       -0.017700  \n", "2023-08-25  4393.49  4405.70996       3.296180e+09  16.33008        0.003720  \n", "2023-08-28  4429.28  4433.31006       2.957230e+09   7.28027        0.001645  \n", "...             ...         ...                ...       ...             ...  \n", "2024-08-16  5542.97  5554.25000       3.357690e+09  23.75000        0.004294  \n", "2024-08-19  5581.13  5608.25000       3.222050e+09  51.02002        0.009181  \n", "2024-08-20  5601.50  5597.12012       2.994420e+09  -5.75976       -0.001028  \n", "2024-08-21  5612.05  5620.85010       1.982137e+09  17.76026        0.003170  \n", "2024-08-22  5594.68  5577.27000       1.218070e+09 -60.50000       -0.010731  \n", "\n", "[253 rows x 10 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.equity.price.historical(\"^SPX\", provider=\"fmp\").to_df()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>split_ratio</th>\n", "      <th>dividend</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-08-22</th>\n", "      <td>71.419998</td>\n", "      <td>71.949997</td>\n", "      <td>71.029999</td>\n", "      <td>71.160004</td>\n", "      <td>5342</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-23</th>\n", "      <td>71.120003</td>\n", "      <td>71.320000</td>\n", "      <td>69.709999</td>\n", "      <td>70.730003</td>\n", "      <td>5139</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-24</th>\n", "      <td>70.459999</td>\n", "      <td>70.860001</td>\n", "      <td>69.870003</td>\n", "      <td>70.190002</td>\n", "      <td>7594</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-25</th>\n", "      <td>70.050003</td>\n", "      <td>70.889999</td>\n", "      <td>69.470001</td>\n", "      <td>70.680000</td>\n", "      <td>9328</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-28</th>\n", "      <td>70.690002</td>\n", "      <td>71.190002</td>\n", "      <td>70.239998</td>\n", "      <td>70.480003</td>\n", "      <td>6234</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16</th>\n", "      <td>70.940002</td>\n", "      <td>70.989998</td>\n", "      <td>69.440002</td>\n", "      <td>70.019997</td>\n", "      <td>38165</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-19</th>\n", "      <td>70.099998</td>\n", "      <td>70.400002</td>\n", "      <td>68.870003</td>\n", "      <td>69.029999</td>\n", "      <td>29067</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-20</th>\n", "      <td>69.150002</td>\n", "      <td>69.250000</td>\n", "      <td>68.309998</td>\n", "      <td>68.389999</td>\n", "      <td>29827</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-21</th>\n", "      <td>68.389999</td>\n", "      <td>68.959999</td>\n", "      <td>67.370003</td>\n", "      <td>67.629997</td>\n", "      <td>29827</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-22</th>\n", "      <td>67.730003</td>\n", "      <td>68.650002</td>\n", "      <td>67.430000</td>\n", "      <td>68.220001</td>\n", "      <td>33722</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>254 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                 open       high        low      close  volume  split_ratio  \\\n", "date                                                                          \n", "2023-08-22  71.419998  71.949997  71.029999  71.160004    5342          0.0   \n", "2023-08-23  71.120003  71.320000  69.709999  70.730003    5139          0.0   \n", "2023-08-24  70.459999  70.860001  69.870003  70.190002    7594          0.0   \n", "2023-08-25  70.050003  70.889999  69.470001  70.680000    9328          0.0   \n", "2023-08-28  70.690002  71.190002  70.239998  70.480003    6234          0.0   \n", "...               ...        ...        ...        ...     ...          ...   \n", "2024-08-16  70.940002  70.989998  69.440002  70.019997   38165          0.0   \n", "2024-08-19  70.099998  70.400002  68.870003  69.029999   29067          0.0   \n", "2024-08-20  69.150002  69.250000  68.309998  68.389999   29827          0.0   \n", "2024-08-21  68.389999  68.959999  67.370003  67.629997   29827          0.0   \n", "2024-08-22  67.730003  68.650002  67.430000  68.220001   33722          0.0   \n", "\n", "            dividend  \n", "date                  \n", "2023-08-22       0.0  \n", "2023-08-23       0.0  \n", "2023-08-24       0.0  \n", "2023-08-25       0.0  \n", "2023-08-28       0.0  \n", "...              ...  \n", "2024-08-16       0.0  \n", "2024-08-19       0.0  \n", "2024-08-20       0.0  \n", "2024-08-21       0.0  \n", "2024-08-22       0.0  \n", "\n", "[254 rows x 7 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.equity.price.historical(\"CLZ25.NYM\", provider=\"yfinance\").to_df()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>vwap</th>\n", "      <th>adj_close</th>\n", "      <th>unadjusted_volume</th>\n", "      <th>change</th>\n", "      <th>change_percent</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-08-22</th>\n", "      <td>80.80</td>\n", "      <td>80.99</td>\n", "      <td>80.10</td>\n", "      <td>80.35</td>\n", "      <td>287489</td>\n", "      <td>80.48</td>\n", "      <td>80.35</td>\n", "      <td>287489.0</td>\n", "      <td>-0.45</td>\n", "      <td>-0.005569</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-23</th>\n", "      <td>79.64</td>\n", "      <td>79.91</td>\n", "      <td>77.62</td>\n", "      <td>78.89</td>\n", "      <td>378146</td>\n", "      <td>78.81</td>\n", "      <td>78.89</td>\n", "      <td>378146.0</td>\n", "      <td>-0.75</td>\n", "      <td>-0.009417</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-24</th>\n", "      <td>78.57</td>\n", "      <td>79.28</td>\n", "      <td>77.59</td>\n", "      <td>79.05</td>\n", "      <td>349230</td>\n", "      <td>78.64</td>\n", "      <td>79.05</td>\n", "      <td>349230.0</td>\n", "      <td>0.48</td>\n", "      <td>0.006109</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-25</th>\n", "      <td>78.88</td>\n", "      <td>80.45</td>\n", "      <td>78.14</td>\n", "      <td>79.83</td>\n", "      <td>411409</td>\n", "      <td>79.47</td>\n", "      <td>79.83</td>\n", "      <td>411409.0</td>\n", "      <td>0.95</td>\n", "      <td>0.012000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-28</th>\n", "      <td>80.15</td>\n", "      <td>80.87</td>\n", "      <td>79.61</td>\n", "      <td>80.10</td>\n", "      <td>246584</td>\n", "      <td>80.19</td>\n", "      <td>80.10</td>\n", "      <td>246584.0</td>\n", "      <td>-0.05</td>\n", "      <td>-0.000624</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-18</th>\n", "      <td>76.58</td>\n", "      <td>76.71</td>\n", "      <td>76.48</td>\n", "      <td>76.71</td>\n", "      <td>175</td>\n", "      <td>76.62</td>\n", "      <td>76.71</td>\n", "      <td>175.0</td>\n", "      <td>0.13</td>\n", "      <td>0.001698</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-19</th>\n", "      <td>76.58</td>\n", "      <td>76.87</td>\n", "      <td>74.17</td>\n", "      <td>74.37</td>\n", "      <td>118172</td>\n", "      <td>75.50</td>\n", "      <td>74.37</td>\n", "      <td>118172.0</td>\n", "      <td>-2.21</td>\n", "      <td>-0.028900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-20</th>\n", "      <td>74.34</td>\n", "      <td>75.03</td>\n", "      <td>73.50</td>\n", "      <td>74.04</td>\n", "      <td>118172</td>\n", "      <td>74.23</td>\n", "      <td>74.04</td>\n", "      <td>118172.0</td>\n", "      <td>-0.30</td>\n", "      <td>-0.004036</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-21</th>\n", "      <td>73.12</td>\n", "      <td>74.16</td>\n", "      <td>71.46</td>\n", "      <td>71.93</td>\n", "      <td>361850</td>\n", "      <td>72.67</td>\n", "      <td>71.93</td>\n", "      <td>361850.0</td>\n", "      <td>-1.19</td>\n", "      <td>-0.016300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-22</th>\n", "      <td>71.93</td>\n", "      <td>73.52</td>\n", "      <td>71.58</td>\n", "      <td>73.00</td>\n", "      <td>28663</td>\n", "      <td>72.70</td>\n", "      <td>73.00</td>\n", "      <td>28663.0</td>\n", "      <td>1.07</td>\n", "      <td>0.014876</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>266 rows × 10 columns</p>\n", "</div>"], "text/plain": ["             open   high    low  close  volume   vwap  adj_close  \\\n", "date                                                               \n", "2023-08-22  80.80  80.99  80.10  80.35  287489  80.48      80.35   \n", "2023-08-23  79.64  79.91  77.62  78.89  378146  78.81      78.89   \n", "2023-08-24  78.57  79.28  77.59  79.05  349230  78.64      79.05   \n", "2023-08-25  78.88  80.45  78.14  79.83  411409  79.47      79.83   \n", "2023-08-28  80.15  80.87  79.61  80.10  246584  80.19      80.10   \n", "...           ...    ...    ...    ...     ...    ...        ...   \n", "2024-08-18  76.58  76.71  76.48  76.71     175  76.62      76.71   \n", "2024-08-19  76.58  76.87  74.17  74.37  118172  75.50      74.37   \n", "2024-08-20  74.34  75.03  73.50  74.04  118172  74.23      74.04   \n", "2024-08-21  73.12  74.16  71.46  71.93  361850  72.67      71.93   \n", "2024-08-22  71.93  73.52  71.58  73.00   28663  72.70      73.00   \n", "\n", "            unadjusted_volume  change  change_percent  \n", "date                                                   \n", "2023-08-22           287489.0   -0.45       -0.005569  \n", "2023-08-23           378146.0   -0.75       -0.009417  \n", "2023-08-24           349230.0    0.48        0.006109  \n", "2023-08-25           411409.0    0.95        0.012000  \n", "2023-08-28           246584.0   -0.05       -0.000624  \n", "...                       ...     ...             ...  \n", "2024-08-18              175.0    0.13        0.001698  \n", "2024-08-19           118172.0   -2.21       -0.028900  \n", "2024-08-20           118172.0   -0.30       -0.004036  \n", "2024-08-21           361850.0   -1.19       -0.016300  \n", "2024-08-22            28663.0    1.07        0.014876  \n", "\n", "[266 rows x 10 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.equity.price.historical(\"CL=F\", provider=\"fmp\").to_df()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>split_ratio</th>\n", "      <th>dividend</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-08-22</th>\n", "      <td>146.238007</td>\n", "      <td>146.389999</td>\n", "      <td>145.501999</td>\n", "      <td>146.238007</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-23</th>\n", "      <td>145.763000</td>\n", "      <td>145.813004</td>\n", "      <td>144.580002</td>\n", "      <td>145.763000</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-24</th>\n", "      <td>144.673004</td>\n", "      <td>145.947006</td>\n", "      <td>144.621002</td>\n", "      <td>144.673004</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-25</th>\n", "      <td>146.067001</td>\n", "      <td>146.604996</td>\n", "      <td>145.733994</td>\n", "      <td>146.067001</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-28</th>\n", "      <td>146.531006</td>\n", "      <td>146.716003</td>\n", "      <td>146.278000</td>\n", "      <td>146.531006</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16</th>\n", "      <td>149.222000</td>\n", "      <td>149.229996</td>\n", "      <td>147.639008</td>\n", "      <td>149.222000</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-19</th>\n", "      <td>147.955994</td>\n", "      <td>147.959000</td>\n", "      <td>145.220993</td>\n", "      <td>147.955994</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-20</th>\n", "      <td>146.699005</td>\n", "      <td>147.319000</td>\n", "      <td>145.533997</td>\n", "      <td>146.699005</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-21</th>\n", "      <td>145.347000</td>\n", "      <td>146.339005</td>\n", "      <td>144.981003</td>\n", "      <td>145.347000</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-22</th>\n", "      <td>145.117996</td>\n", "      <td>146.524994</td>\n", "      <td>144.839996</td>\n", "      <td>146.292999</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>262 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                  open        high         low       close  volume  \\\n", "date                                                                 \n", "2023-08-22  146.238007  146.389999  145.501999  146.238007       0   \n", "2023-08-23  145.763000  145.813004  144.580002  145.763000       0   \n", "2023-08-24  144.673004  145.947006  144.621002  144.673004       0   \n", "2023-08-25  146.067001  146.604996  145.733994  146.067001       0   \n", "2023-08-28  146.531006  146.716003  146.278000  146.531006       0   \n", "...                ...         ...         ...         ...     ...   \n", "2024-08-16  149.222000  149.229996  147.639008  149.222000       0   \n", "2024-08-19  147.955994  147.959000  145.220993  147.955994       0   \n", "2024-08-20  146.699005  147.319000  145.533997  146.699005       0   \n", "2024-08-21  145.347000  146.339005  144.981003  145.347000       0   \n", "2024-08-22  145.117996  146.524994  144.839996  146.292999       0   \n", "\n", "            split_ratio  dividend  \n", "date                               \n", "2023-08-22          0.0       0.0  \n", "2023-08-23          0.0       0.0  \n", "2023-08-24          0.0       0.0  \n", "2023-08-25          0.0       0.0  \n", "2023-08-28          0.0       0.0  \n", "...                 ...       ...  \n", "2024-08-16          0.0       0.0  \n", "2024-08-19          0.0       0.0  \n", "2024-08-20          0.0       0.0  \n", "2024-08-21          0.0       0.0  \n", "2024-08-22          0.0       0.0  \n", "\n", "[262 rows x 7 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.equity.price.historical(\"usdjpy=x\", provider=\"yfinance\").to_df()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-08-22</th>\n", "      <td>146.238007</td>\n", "      <td>146.389999</td>\n", "      <td>145.501999</td>\n", "      <td>146.238007</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-23</th>\n", "      <td>145.763000</td>\n", "      <td>145.813004</td>\n", "      <td>144.580002</td>\n", "      <td>145.763000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-24</th>\n", "      <td>144.673004</td>\n", "      <td>145.947006</td>\n", "      <td>144.621002</td>\n", "      <td>144.673004</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-25</th>\n", "      <td>146.067001</td>\n", "      <td>146.604996</td>\n", "      <td>145.733994</td>\n", "      <td>146.067001</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-28</th>\n", "      <td>146.531006</td>\n", "      <td>146.716003</td>\n", "      <td>146.278000</td>\n", "      <td>146.531006</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16</th>\n", "      <td>149.222000</td>\n", "      <td>149.229996</td>\n", "      <td>147.639008</td>\n", "      <td>149.222000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-19</th>\n", "      <td>147.955994</td>\n", "      <td>147.959000</td>\n", "      <td>145.220993</td>\n", "      <td>147.955994</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-20</th>\n", "      <td>146.699005</td>\n", "      <td>147.319000</td>\n", "      <td>145.533997</td>\n", "      <td>146.699005</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-21</th>\n", "      <td>145.347000</td>\n", "      <td>146.339005</td>\n", "      <td>144.981003</td>\n", "      <td>145.347000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-22</th>\n", "      <td>145.117996</td>\n", "      <td>146.524994</td>\n", "      <td>144.839996</td>\n", "      <td>146.287003</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>262 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                  open        high         low       close  volume\n", "date                                                              \n", "2023-08-22  146.238007  146.389999  145.501999  146.238007     0.0\n", "2023-08-23  145.763000  145.813004  144.580002  145.763000     0.0\n", "2023-08-24  144.673004  145.947006  144.621002  144.673004     0.0\n", "2023-08-25  146.067001  146.604996  145.733994  146.067001     0.0\n", "2023-08-28  146.531006  146.716003  146.278000  146.531006     0.0\n", "...                ...         ...         ...         ...     ...\n", "2024-08-16  149.222000  149.229996  147.639008  149.222000     0.0\n", "2024-08-19  147.955994  147.959000  145.220993  147.955994     0.0\n", "2024-08-20  146.699005  147.319000  145.533997  146.699005     0.0\n", "2024-08-21  145.347000  146.339005  144.981003  145.347000     0.0\n", "2024-08-22  145.117996  146.524994  144.839996  146.287003     0.0\n", "\n", "[262 rows x 5 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.currency.price.historical(\"usdjpy\", provider=\"yfinance\").to_df()"]}], "metadata": {"kernelspec": {"display_name": "obb", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}