[tool.poetry]
name = "openbb"
version = "4.4.4"
description = "Investment research for everyone, anywhere."
authors = ["OpenBB Team <<EMAIL>>"]
license = "AGPL-3.0-only"
readme = "README.md"
packages = [{ include = "openbb" }]

[tool.poetry.dependencies]
python = ">=3.9.21,<3.13"
openbb-core = "^1.4.7"
openbb-platform-api = "^1.1.9"

openbb-benzinga = "^1.4.1"
openbb-bls = "^1.1.2"
openbb-cftc = "^1.1.1"
openbb-econdb = "^1.3.1"
openbb-federal-reserve = "^1.4.2"
openbb-fmp = "^1.4.2"
openbb-fred = "^1.4.4"
openbb-imf = "^1.1.1"
openbb-intrinio = "^1.4.1"
openbb-oecd = "^1.4.1"
openbb-polygon = "^1.4.1"
openbb-sec = "^1.4.3"
openbb-tiingo = "^1.4.1"
openbb-tradingeconomics = "^1.4.1"
openbb-us-eia = "^1.1.1"
openbb-yfinance = "^1.4.6"

openbb-commodity = "^1.3.1"
openbb-crypto = "^1.4.1"
openbb-currency = "^1.4.1"
openbb-derivatives = "^1.4.1"
openbb-economy = "^1.4.2"
openbb-equity = "^1.4.1"
openbb-etf = "^1.4.1"
openbb-fixedincome = "^1.4.3"
openbb-index = "^1.4.1"
openbb-news = "^1.4.1"
openbb-regulators = "^1.4.2"

# Community dependencies
openbb-alpha-vantage = { version = "^1.4.1", optional = true }
openbb-biztoc = { version = "^1.4.2", optional = true }
openbb-cboe = { version = "^1.4.1", optional = true }
openbb-deribit = { version = "^1.0.1", optional = true }
openbb-ecb = { version = "^1.4.2", optional = true }
openbb-famafrench = { version = "^1.0.0", optional = true }
openbb-finra = { version = "^1.4.1", optional = true }
openbb-finviz = { version = "^1.3.1", optional = true }
openbb-government-us = { version = "^1.4.1", optional = true }
openbb-multpl = { version = "^1.1.1", optional = true }
openbb-nasdaq = { version = "^1.4.1", optional = true }
openbb-seeking-alpha = { version = "^1.4.1", optional = true }
openbb-stockgrid = { version = "^1.4.1", optional = true }
openbb-tmx = { version = "^1.3.2", optional = true }
openbb-tradier = { version = "^1.3.1", optional = true }
openbb-wsj = { version = "^1.4.1", optional = true }

openbb-charting = { version = "^2.3.4", optional = true }
openbb-econometrics = { version = "^1.5.3", optional = true }
openbb-quantitative = { version = "^1.4.3", optional = true }
openbb-technical = { version = "^1.4.3", optional = true }
openbb-mcp-server = { version = "^0.0.1", optional = true, markers = "python_version >= '3.10'" }

[tool.poetry.extras]
alpha_vantage = ["openbb-alpha-vantage"]
biztoc = ["openbb-biztoc"]
cboe = ["openbb-cboe"]
charting = ["openbb-charting"]
deribit = ["openbb-deribit"]
ecb = ["openbb-ecb"]
econometrics = ["openbb-econometrics"]
famafrench = ["openbb-famafrench"]
finra = ["openbb-finra"]
finviz = ["openbb-finviz"]
government_us = ["openbb-government-us"]
mcp_server = ["openbb-mcp-server"]
nasdaq = ["openbb-nasdaq"]
multpl = ["openbb-multpl"]
quantitative = ["openbb-quantitative"]
seeking_alpha = ["openbb-seeking-alpha"]
stockgrid = ["openbb-stockgrid"]
technical = ["openbb-technical"]
tmx = ["openbb-tmx"]
tradier = ["openbb-tradier"]
wsj = ["openbb-wsj"]


all = [
    "openbb-alpha-vantage",
    "openbb-biztoc",
    "openbb-cboe",
    "openbb-charting",
    "openbb-deribit",
    "openbb-ecb",
    "openbb-econometrics",
    "openbb-famafrench",
    "openbb-finra",
    "openbb-finviz",
    "openbb-government-us",
    "openbb-mcp-server",
    "openbb-multpl",
    "openbb-nasdaq",
    "openbb-quantitative",
    "openbb-seeking-alpha",
    "openbb-stockgrid",
    "openbb-technical",
    "openbb-tmx",
    "openbb-tradier",
    "openbb-wsj",
]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
