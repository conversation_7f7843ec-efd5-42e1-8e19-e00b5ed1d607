#!/usr/bin/env python3
"""
OpenBB Platform 测试脚本
用于验证OpenBB安装是否成功并展示基本功能
"""

import sys
from datetime import datetime, timedelta


def test_openbb_import():
    """测试OpenBB导入"""
    try:
        from openbb_core.app.static.app_factory import create_app
        print("✅ OpenBB Core 导入成功")
        return True
    except ImportError as e:
        print(f"❌ OpenBB Core 导入失败: {e}")
        return False


def test_basic_functionality():
    """测试基本功能"""
    try:
        # 尝试创建基本的OpenBB应用
        from openbb_core.app.static.app_factory import create_app
        
        # 创建应用实例
        app = create_app()
        print("✅ OpenBB 应用创建成功")
        
        # 检查可用的提供商
        if hasattr(app, 'providers'):
            providers = getattr(app, 'providers', [])
            print(f"✅ 可用的数据提供商: {len(providers) if providers else 0}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False


def test_yfinance_provider():
    """测试Yahoo Finance数据提供商"""
    try:
        import yfinance as yf
        
        # 测试获取简单的股票数据
        ticker = yf.Ticker("AAPL")
        info = ticker.info
        
        if info and 'symbol' in info:
            print(f"✅ Yahoo Finance 测试成功 - 获取到 {info.get('symbol', 'AAPL')} 数据")
            return True
        else:
            print("⚠️ Yahoo Finance 数据获取为空")
            return False
            
    except Exception as e:
        print(f"❌ Yahoo Finance 测试失败: {e}")
        return False


def test_data_fetching():
    """测试数据获取功能"""
    try:
        import yfinance as yf
        import pandas as pd
        
        # 获取苹果股票最近5天的数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        ticker = yf.Ticker("AAPL")
        data = ticker.history(
            start=start_date.strftime("%Y-%m-%d"),
            end=end_date.strftime("%Y-%m-%d")
        )
        
        if not data.empty:
            print(f"✅ 数据获取成功 - 获取到 {len(data)} 条AAPL股票数据")
            print(f"   最新收盘价: ${data['Close'].iloc[-1]:.2f}")
            return True
        else:
            print("⚠️ 数据获取为空")
            return False
            
    except Exception as e:
        print(f"❌ 数据获取测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试 OpenBB Platform 安装...")
    print("=" * 50)
    
    tests = [
        ("OpenBB Core 导入", test_openbb_import),
        ("基本功能", test_basic_functionality),
        ("Yahoo Finance 提供商", test_yfinance_provider),
        ("数据获取功能", test_data_fetching),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        if test_func():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！OpenBB Platform 安装成功！")
        print("\n📖 使用示例:")
        print("```python")
        print("import yfinance as yf")
        print("ticker = yf.Ticker('AAPL')")
        print("data = ticker.history(period='1mo')")
        print("print(data.head())")
        print("```")
    else:
        print("⚠️ 部分测试失败，请检查安装")
        
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
