"""Core calculation engine for innovative technical indicators."""

import pandas as pd
import numpy as np
from typing import Dict, Optional, Tuple
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import warnings

warnings.filterwarnings('ignore')


class InnovativeCalculator:
    """Core calculator for innovative technical indicators."""
    
    def __init__(self, data: pd.DataFrame):
        """
        Initialize calculator with OHLCV data.
        
        Args:
            data: DataFrame with columns ['open', 'high', 'low', 'close', 'volume']
        """
        self.data = data.copy()
        self._validate_data()
        self._prepare_data()
    
    def _validate_data(self) -> None:
        """Validate input data structure."""
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in self.data.columns]
        
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        if len(self.data) < 50:
            raise ValueError("Insufficient data points. Minimum 50 required.")
    
    def _prepare_data(self) -> None:
        """Prepare data for calculations."""
        # Ensure datetime index
        if not isinstance(self.data.index, pd.DatetimeIndex):
            self.data.index = pd.to_datetime(self.data.index)
        
        # Sort by date
        self.data = self.data.sort_index()
        
        # Fill any missing values
        self.data = self.data.fillna(method='ffill').fillna(method='bfill')
        
        # Calculate basic indicators
        self.data['returns'] = self.data['close'].pct_change()
        self.data['atr'] = self._calculate_atr()
        self.data['rsi'] = self._calculate_rsi()
    
    def calculate_dmfi(self, period: int = 20, lambda_decay: float = 10) -> pd.Series:
        """
        Calculate Dynamic Money Flow Intensity (DMFI).
        
        Combines traditional money flow with volatility adjustment,
        time decay, and market sentiment.
        """
        high = self.data['high']
        low = self.data['low']
        close = self.data['close']
        volume = self.data['volume']
        
        # 1. Money Flow Volume
        hl_diff = (high - low).replace(0, 0.0001)
        mfv = volume * ((close - low) - (high - close)) / hl_diff
        
        # 2. Volatility Ratio
        atr_sma = self.data['atr'].rolling(50).mean()
        vr = (self.data['atr'] / atr_sma).fillna(1)
        
        # 3. Time Decay
        td = pd.Series(
            np.exp(-np.arange(len(self.data)) / lambda_decay),
            index=self.data.index
        )
        
        # 4. Market Sentiment
        sma_20 = close.rolling(20).mean()
        ms = ((close / sma_20 - 1) * 100).fillna(0)
        
        # 5. Calculate DMFI
        dmfi_raw = mfv * vr * td * (1 + ms / 100)
        dmfi = dmfi_raw.rolling(period).sum()
        
        # Normalize to [-100, 100]
        return self._normalize_indicator(dmfi)
    
    def calculate_aisi(self, ml_period: int = 60, retrain_freq: int = 20) -> pd.Series:
        """
        Calculate Adaptive Intelligent Sentiment Index (AISI).
        
        Uses machine learning to dynamically adjust feature weights
        based on predictive performance.
        """
        close = self.data['close']
        volume = self.data['volume']
        
        # Feature engineering
        features = pd.DataFrame({
            'returns': self.data['returns'],
            'volatility': self.data['returns'].rolling(20).std(),
            'rsi': self.data['rsi'],
            'volume_ratio': volume / volume.rolling(20).mean(),
            'price_position': self._calculate_price_position(),
            'momentum': close / close.shift(10) - 1
        }).fillna(0)
        
        # Target: future 5-day return
        target = (close.shift(-5) / close - 1).fillna(0)
        
        aisi_values = []
        scaler = StandardScaler()
        weights = np.array([1/6] * 6)  # Initial equal weights
        
        for i in range(ml_period, len(self.data)):
            # Retrain model periodically
            if i % retrain_freq == 0:
                train_features = features.iloc[i-ml_period:i]
                train_target = target.iloc[i-ml_period:i]
                
                # Remove invalid data
                valid_idx = ~(train_target.isna() | train_features.isna().any(axis=1))
                if valid_idx.sum() > 10:
                    train_features_clean = train_features[valid_idx]
                    train_target_clean = train_target[valid_idx]
                    
                    try:
                        scaler.fit(train_features_clean)
                        train_scaled = scaler.transform(train_features_clean)
                        
                        model = RandomForestRegressor(
                            n_estimators=50, 
                            random_state=42,
                            max_depth=5
                        )
                        model.fit(train_scaled, train_target_clean)
                        weights = model.feature_importances_
                    except Exception:
                        pass  # Keep previous weights
            
            # Calculate AISI value
            current_features = features.iloc[i].values
            try:
                current_scaled = scaler.transform(current_features.reshape(1, -1))[0]
                aisi_value = np.sum(current_scaled * weights) * 100
            except Exception:
                aisi_value = 0
            
            aisi_values.append(aisi_value)
        
        # Create full series
        aisi_series = pd.Series(index=self.data.index, dtype=float)
        aisi_series.iloc[ml_period:] = aisi_values
        aisi_series = aisi_series.fillna(0)
        
        return self._normalize_indicator(aisi_series)
    
    def calculate_mvci(self, short_period: int = 10, long_period: int = 30) -> pd.Series:
        """
        Calculate Multi-Dimensional Value Convergence Index (MVCI).
        
        Analyzes convergence across price, volume, volatility, and position dimensions.
        """
        close = self.data['close']
        volume = self.data['volume']
        high = self.data['high']
        low = self.data['low']
        
        # 1. Price convergence
        sma_short = close.rolling(short_period).mean()
        sma_long = close.rolling(long_period).mean()
        price_conv = ((sma_short - sma_long) / sma_long * 100).fillna(0)
        
        # 2. Volume convergence
        volume_sma = volume.rolling(20).mean()
        volume_conv = ((volume - volume_sma) / volume_sma * 100).fillna(0)
        
        # 3. Volatility convergence
        atr_sma = self.data['atr'].rolling(long_period).mean()
        vol_conv = ((self.data['atr'] - atr_sma) / atr_sma * 100).fillna(0)
        
        # 4. Position convergence
        highest = high.rolling(long_period).max()
        lowest = low.rolling(long_period).min()
        price_pos = ((close - lowest) / (highest - lowest) * 100).fillna(50)
        pos_conv = price_pos - 50
        
        # 5. Dynamic weighting based on volatility
        recent_vol = self.data['returns'].rolling(10).std()
        vol_weight = (1 / (1 + recent_vol * 100)).fillna(1)
        
        # 6. Calculate MVCI
        mvci = (
            price_conv * 0.4 * vol_weight +
            volume_conv * 0.3 +
            vol_conv * 0.2 +
            pos_conv * 0.1
        )
        
        return mvci.rolling(5).mean().fillna(0)
    
    def calculate_tafi(self, base_period: int = 20) -> pd.Series:
        """
        Calculate Time-Adaptive Trend Following Index (TAFI).
        
        Automatically adjusts calculation period based on market efficiency.
        """
        close = self.data['close']
        volume = self.data['volume']
        
        # Calculate market efficiency
        volatility = self.data['returns'].rolling(20).std()
        vol_sma = volatility.rolling(60).mean()
        market_efficiency = (1 - volatility / vol_sma).fillna(0.5).clip(0.1, 2.0)
        
        # Adaptive period
        adaptive_period = (base_period * market_efficiency).round().astype(int).clip(5, 60)
        
        tafi_values = []
        
        for i in range(len(self.data)):
            if i < base_period:
                tafi_values.append(0)
                continue
            
            period = adaptive_period.iloc[i]
            start_idx = max(0, i - period + 1)
            
            # Exponential weights
            weights = np.exp(np.linspace(-1, 0, period))
            weights = weights / weights.sum()
            
            # Price and volume windows
            price_window = close.iloc[start_idx:i+1]
            volume_window = volume.iloc[start_idx:i+1]
            
            if len(price_window) == period:
                # Calculate signals
                price_trend = np.sum(price_window.values * weights)
                volume_trend = np.sum(volume_window.values * weights)
                
                current_price = close.iloc[i]
                current_volume = volume.iloc[i]
                
                price_signal = (current_price - price_trend) / price_trend * 100
                volume_signal = (current_volume - volume_trend) / volume_trend * 100
                
                tafi_value = price_signal * 0.7 + volume_signal * 0.3
            else:
                tafi_value = 0
            
            tafi_values.append(tafi_value)
        
        tafi_series = pd.Series(tafi_values, index=self.data.index)
        return self._normalize_indicator(tafi_series.rolling(3).mean())
    
    def calculate_fmsi(self, fundamental_weight: float = 0.3) -> pd.Series:
        """
        Calculate Fundamental-Technical Momentum Synthesis Index (FMSI).
        
        Combines technical indicators with fundamental proxies derived from price behavior.
        """
        close = self.data['close']
        volume = self.data['volume']
        
        # Technical analysis
        price_momentum = (close / close.shift(20) - 1).fillna(0)
        rsi_normalized = (self.data['rsi'] - 50) / 50
        volume_momentum = (volume / volume.rolling(20).mean() - 1).fillna(0)
        
        volatility = self.data['returns'].rolling(20).std()
        vol_adjustment = (1 / (1 + volatility * 10)).fillna(1)
        
        technical_score = (
            price_momentum * 0.4 +
            rsi_normalized * 0.3 +
            volume_momentum * 0.3
        ) * vol_adjustment
        
        # Fundamental proxies
        price_to_ma = close / close.rolling(252).mean()
        value_reversion = (1 / price_to_ma).fillna(1)
        
        price_growth = (close.rolling(60).mean() / close.rolling(252).mean()).fillna(1)
        growth_momentum = (price_growth - 1) * 2
        
        price_stability = 1 / (self.data['returns'].rolling(60).std() + 0.01)
        quality_score = ((price_stability - price_stability.rolling(252).mean()) / 
                        price_stability.rolling(252).std()).fillna(0)
        
        fundamental_score = (
            value_reversion * 0.4 +
            growth_momentum * 0.4 +
            quality_score * 0.2
        )
        
        # Combine scores
        fmsi = (
            technical_score * (1 - fundamental_weight) +
            fundamental_score * fundamental_weight
        )
        
        return self._normalize_indicator(fmsi)
    
    def _calculate_atr(self, period: int = 14) -> pd.Series:
        """Calculate Average True Range."""
        high = self.data['high']
        low = self.data['low']
        close = self.data['close']
        
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr.rolling(period).mean()
    
    def _calculate_rsi(self, period: int = 14) -> pd.Series:
        """Calculate Relative Strength Index."""
        delta = self.data['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        avg_gain = gain.rolling(period).mean()
        avg_loss = loss.rolling(period).mean()
        
        rs = avg_gain / avg_loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_price_position(self, period: int = 20) -> pd.Series:
        """Calculate price position within recent range."""
        high = self.data['high']
        low = self.data['low']
        close = self.data['close']
        
        highest = high.rolling(period).max()
        lowest = low.rolling(period).min()
        
        return ((close - lowest) / (highest - lowest)).fillna(0.5)
    
    def _normalize_indicator(self, series: pd.Series, window: int = 252) -> pd.Series:
        """Normalize indicator to [-100, 100] range."""
        rolling_mean = series.rolling(window).mean()
        rolling_std = series.rolling(window).std()
        
        normalized = ((series - rolling_mean) / rolling_std * 20).fillna(0)
        return normalized.clip(-100, 100)
