#!/usr/bin/env python3
"""
检查OpenBB API参数需求
分析缺失的参数并找到免费API补充
"""

import requests
import json
from datetime import datetime, timedelta


class OpenBBAPIAnalyzer:
    """OpenBB API参数分析器"""
    
    def __init__(self):
        self.us_api = "http://127.0.0.1:6900"
        self.astock_api = "http://127.0.0.1:6901"
        
        # OpenBB标准API参数需求
        self.required_endpoints = {
            "equity_historical": {
                "endpoint": "/api/v1/equity/price/historical",
                "required_params": ["symbol"],
                "optional_params": ["start_date", "end_date", "interval", "provider"],
                "response_fields": ["date", "open", "high", "low", "close", "volume", "adj_close"]
            },
            "equity_quote": {
                "endpoint": "/api/v1/equity/price/quote", 
                "required_params": ["symbol"],
                "optional_params": ["provider"],
                "response_fields": ["symbol", "price", "change", "change_percent", "volume", "market_cap", "pe_ratio"]
            },
            "equity_fundamental_income": {
                "endpoint": "/api/v1/equity/fundamental/income",
                "required_params": ["symbol"],
                "optional_params": ["period", "limit", "provider"],
                "response_fields": ["symbol", "date", "revenue", "operating_income", "net_income", "eps"]
            },
            "equity_fundamental_balance": {
                "endpoint": "/api/v1/equity/fundamental/balance",
                "required_params": ["symbol"],
                "optional_params": ["period", "limit", "provider"],
                "response_fields": ["symbol", "date", "total_assets", "total_liabilities", "shareholders_equity"]
            },
            "equity_fundamental_cash": {
                "endpoint": "/api/v1/equity/fundamental/cash",
                "required_params": ["symbol"],
                "optional_params": ["period", "limit", "provider"],
                "response_fields": ["symbol", "date", "operating_cash_flow", "investing_cash_flow", "financing_cash_flow"]
            },
            "news_company": {
                "endpoint": "/api/v1/news/company",
                "required_params": [],
                "optional_params": ["symbol", "limit", "start_date", "end_date", "provider"],
                "response_fields": ["title", "content", "url", "published_date", "source"]
            },
            "economy_gdp": {
                "endpoint": "/api/v1/economy/gdp",
                "required_params": [],
                "optional_params": ["country", "start_date", "end_date", "provider"],
                "response_fields": ["date", "value", "country"]
            },
            "crypto_historical": {
                "endpoint": "/api/v1/crypto/price/historical",
                "required_params": ["symbol"],
                "optional_params": ["start_date", "end_date", "interval", "provider"],
                "response_fields": ["date", "open", "high", "low", "close", "volume"]
            },
            "technical_sma": {
                "endpoint": "/api/v1/technical/sma",
                "required_params": ["symbol"],
                "optional_params": ["window", "provider"],
                "response_fields": ["date", "sma"]
            },
            "derivatives_options": {
                "endpoint": "/api/v1/derivatives/options/chains",
                "required_params": ["symbol"],
                "optional_params": ["expiration", "provider"],
                "response_fields": ["strike", "call_price", "put_price", "volume", "open_interest"]
            }
        }
        
        # 免费API资源
        self.free_apis = {
            "alpha_vantage": {
                "base_url": "https://www.alphavantage.co/query",
                "key_required": True,
                "free_key": "demo",
                "endpoints": ["TIME_SERIES_DAILY", "GLOBAL_QUOTE", "OVERVIEW", "INCOME_STATEMENT", "BALANCE_SHEET", "CASH_FLOW", "NEWS_SENTIMENT"]
            },
            "yahoo_finance": {
                "base_url": "https://query1.finance.yahoo.com/v8/finance/chart",
                "key_required": False,
                "endpoints": ["chart", "quote", "fundamentals"]
            },
            "finnhub": {
                "base_url": "https://finnhub.io/api/v1",
                "key_required": True,
                "free_key": "demo",
                "endpoints": ["quote", "candle", "company-news", "financials"]
            },
            "polygon": {
                "base_url": "https://api.polygon.io/v2",
                "key_required": True,
                "free_key": "demo",
                "endpoints": ["aggs", "trades", "quotes", "tickers"]
            },
            "newsapi": {
                "base_url": "https://newsapi.org/v2",
                "key_required": True,
                "free_key": "demo",
                "endpoints": ["everything", "top-headlines"]
            },
            "fred": {
                "base_url": "https://api.stlouisfed.org/fred",
                "key_required": True,
                "free_key": "demo",
                "endpoints": ["series/observations"]
            }
        }
    
    def check_current_apis(self):
        """检查当前API状态"""
        print("🔍 检查当前OpenBB API状态")
        print("=" * 60)
        
        results = {}
        
        for endpoint_name, config in self.required_endpoints.items():
            print(f"\n📊 检查: {endpoint_name}")
            print(f"端点: {config['endpoint']}")
            
            # 检查美股API
            us_status = self._test_endpoint(self.us_api, config)
            
            # 检查A股API (如果是A股相关端点)
            astock_status = None
            if "astock" in config['endpoint'] or endpoint_name.startswith("equity"):
                astock_endpoint = config['endpoint'].replace("/api/v1/", "/api/v1/astock/")
                astock_status = self._test_endpoint(self.astock_api, {**config, "endpoint": astock_endpoint})
            
            results[endpoint_name] = {
                "us_api": us_status,
                "astock_api": astock_status,
                "required_params": config["required_params"],
                "response_fields": config["response_fields"]
            }
            
            # 显示结果
            if us_status["available"]:
                print(f"✅ 美股API: 可用")
            else:
                print(f"❌ 美股API: {us_status['error']}")
            
            if astock_status:
                if astock_status["available"]:
                    print(f"✅ A股API: 可用")
                else:
                    print(f"❌ A股API: {astock_status['error']}")
        
        return results
    
    def _test_endpoint(self, base_url, config):
        """测试单个端点"""
        try:
            # 构造测试参数
            test_params = {}
            if "symbol" in config["required_params"]:
                test_params["symbol"] = "AAPL" if "6900" in base_url else "000001"
            
            # 发送请求
            response = requests.get(
                f"{base_url}{config['endpoint']}",
                params=test_params,
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "available": True,
                    "status_code": 200,
                    "has_results": "results" in data,
                    "response_keys": list(data.keys()) if isinstance(data, dict) else []
                }
            else:
                return {
                    "available": False,
                    "status_code": response.status_code,
                    "error": f"HTTP {response.status_code}"
                }
                
        except requests.exceptions.ConnectionError:
            return {
                "available": False,
                "error": "连接失败"
            }
        except requests.exceptions.Timeout:
            return {
                "available": False,
                "error": "请求超时"
            }
        except Exception as e:
            return {
                "available": False,
                "error": str(e)
            }
    
    def identify_missing_apis(self, current_status):
        """识别缺失的API"""
        print("\n🔍 分析缺失的API功能")
        print("=" * 60)
        
        missing_apis = []
        
        for endpoint_name, status in current_status.items():
            us_available = status["us_api"]["available"]
            astock_available = status["astock_api"]["available"] if status["astock_api"] else False
            
            if not us_available and not astock_available:
                missing_apis.append({
                    "endpoint": endpoint_name,
                    "description": self._get_endpoint_description(endpoint_name),
                    "priority": self._get_priority(endpoint_name),
                    "free_alternatives": self._find_free_alternatives(endpoint_name)
                })
        
        # 按优先级排序
        missing_apis.sort(key=lambda x: x["priority"])
        
        print(f"发现 {len(missing_apis)} 个缺失的API:")
        for api in missing_apis:
            print(f"\n❌ {api['endpoint']}")
            print(f"   描述: {api['description']}")
            print(f"   优先级: {api['priority']}")
            print(f"   免费替代: {', '.join(api['free_alternatives'])}")
        
        return missing_apis
    
    def _get_endpoint_description(self, endpoint_name):
        """获取端点描述"""
        descriptions = {
            "equity_historical": "股票历史价格数据",
            "equity_quote": "股票实时报价",
            "equity_fundamental_income": "财务报表-利润表",
            "equity_fundamental_balance": "财务报表-资产负债表", 
            "equity_fundamental_cash": "财务报表-现金流量表",
            "news_company": "公司新闻数据",
            "economy_gdp": "经济数据-GDP",
            "crypto_historical": "加密货币历史数据",
            "technical_sma": "技术指标-移动平均线",
            "derivatives_options": "期权数据"
        }
        return descriptions.get(endpoint_name, "未知功能")
    
    def _get_priority(self, endpoint_name):
        """获取优先级 (1=最高, 5=最低)"""
        priorities = {
            "equity_historical": 1,
            "equity_quote": 1,
            "equity_fundamental_income": 2,
            "news_company": 2,
            "equity_fundamental_balance": 3,
            "equity_fundamental_cash": 3,
            "economy_gdp": 3,
            "crypto_historical": 4,
            "technical_sma": 4,
            "derivatives_options": 5
        }
        return priorities.get(endpoint_name, 5)
    
    def _find_free_alternatives(self, endpoint_name):
        """找到免费替代API"""
        alternatives = {
            "equity_historical": ["yahoo_finance", "alpha_vantage", "finnhub"],
            "equity_quote": ["yahoo_finance", "alpha_vantage", "finnhub"],
            "equity_fundamental_income": ["alpha_vantage", "finnhub"],
            "equity_fundamental_balance": ["alpha_vantage", "finnhub"],
            "equity_fundamental_cash": ["alpha_vantage", "finnhub"],
            "news_company": ["newsapi", "finnhub", "alpha_vantage"],
            "economy_gdp": ["fred", "alpha_vantage"],
            "crypto_historical": ["alpha_vantage", "polygon"],
            "technical_sma": ["alpha_vantage"],
            "derivatives_options": ["polygon", "alpha_vantage"]
        }
        return alternatives.get(endpoint_name, [])
    
    def generate_free_api_implementations(self, missing_apis):
        """生成免费API实现方案"""
        print("\n🔧 生成免费API实现方案")
        print("=" * 60)
        
        implementations = {}
        
        for api in missing_apis:
            endpoint_name = api["endpoint"]
            alternatives = api["free_alternatives"]
            
            if alternatives:
                primary_api = alternatives[0]  # 选择第一个作为主要实现
                
                implementation = {
                    "endpoint": endpoint_name,
                    "primary_api": primary_api,
                    "backup_apis": alternatives[1:],
                    "implementation_code": self._generate_implementation_code(endpoint_name, primary_api),
                    "api_info": self.free_apis.get(primary_api, {})
                }
                
                implementations[endpoint_name] = implementation
                
                print(f"\n📝 {endpoint_name}")
                print(f"   主要API: {primary_api}")
                print(f"   备用API: {', '.join(alternatives[1:])}")
                print(f"   需要密钥: {'是' if implementation['api_info'].get('key_required') else '否'}")
        
        return implementations
    
    def _generate_implementation_code(self, endpoint_name, api_name):
        """生成实现代码"""
        if api_name == "yahoo_finance" and endpoint_name == "equity_historical":
            return """
def get_yahoo_historical(symbol, start_date, end_date):
    url = f"https://query1.finance.yahoo.com/v8/finance/chart/{symbol}"
    params = {
        "period1": int(pd.to_datetime(start_date).timestamp()),
        "period2": int(pd.to_datetime(end_date).timestamp()),
        "interval": "1d"
    }
    response = requests.get(url, params=params)
    data = response.json()
    return process_yahoo_data(data)
"""
        elif api_name == "alpha_vantage" and endpoint_name == "equity_quote":
            return """
def get_alpha_vantage_quote(symbol, api_key="demo"):
    url = "https://www.alphavantage.co/query"
    params = {
        "function": "GLOBAL_QUOTE",
        "symbol": symbol,
        "apikey": api_key
    }
    response = requests.get(url, params=params)
    data = response.json()
    return process_alpha_vantage_quote(data)
"""
        else:
            return f"# {endpoint_name} 使用 {api_name} 的实现代码"
    
    def create_missing_api_server(self, implementations):
        """创建缺失API的服务器代码"""
        print("\n🏗️ 创建免费API服务器")
        print("=" * 60)
        
        server_code = '''#!/usr/bin/env python3
"""
OpenBB 免费API补充服务器
使用免费API补充OpenBB缺失的功能
"""

from fastapi import FastAPI, HTTPException, Query
import requests
import pandas as pd
from datetime import datetime
import json

app = FastAPI(title="OpenBB 免费API补充", version="1.0.0")

# API密钥配置
API_KEYS = {
    "alpha_vantage": "demo",  # 替换为真实密钥
    "finnhub": "demo",        # 替换为真实密钥
    "newsapi": "demo"         # 替换为真实密钥
}

'''
        
        for endpoint_name, impl in implementations.items():
            if impl["primary_api"] == "yahoo_finance":
                server_code += self._generate_yahoo_endpoint(endpoint_name)
            elif impl["primary_api"] == "alpha_vantage":
                server_code += self._generate_alpha_vantage_endpoint(endpoint_name)
            elif impl["primary_api"] == "finnhub":
                server_code += self._generate_finnhub_endpoint(endpoint_name)
        
        server_code += '''
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=6902)
'''
        
        return server_code
    
    def _generate_yahoo_endpoint(self, endpoint_name):
        """生成Yahoo Finance端点代码"""
        if endpoint_name == "equity_historical":
            return '''
@app.get("/api/v1/equity/price/historical")
async def get_historical_yahoo(symbol: str, start_date: str = None, end_date: str = None):
    """使用Yahoo Finance获取历史数据"""
    try:
        import yfinance as yf
        ticker = yf.Ticker(symbol)
        data = ticker.history(start=start_date, end=end_date)
        
        results = []
        for date, row in data.iterrows():
            results.append({
                "date": date.strftime("%Y-%m-%d"),
                "open": float(row["Open"]),
                "high": float(row["High"]),
                "low": float(row["Low"]),
                "close": float(row["Close"]),
                "volume": int(row["Volume"])
            })
        
        return {"results": results, "provider": "yahoo_finance"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

'''
        return ""
    
    def _generate_alpha_vantage_endpoint(self, endpoint_name):
        """生成Alpha Vantage端点代码"""
        if endpoint_name == "equity_quote":
            return '''
@app.get("/api/v1/equity/price/quote")
async def get_quote_alpha_vantage(symbol: str):
    """使用Alpha Vantage获取实时报价"""
    try:
        url = "https://www.alphavantage.co/query"
        params = {
            "function": "GLOBAL_QUOTE",
            "symbol": symbol,
            "apikey": API_KEYS["alpha_vantage"]
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        if "Global Quote" in data:
            quote = data["Global Quote"]
            result = {
                "symbol": quote.get("01. symbol"),
                "price": float(quote.get("05. price", 0)),
                "change": float(quote.get("09. change", 0)),
                "change_percent": quote.get("10. change percent", "0%").replace("%", "")
            }
            return {"results": [result], "provider": "alpha_vantage"}
        else:
            raise HTTPException(status_code=404, detail="数据未找到")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

'''
        return ""
    
    def _generate_finnhub_endpoint(self, endpoint_name):
        """生成Finnhub端点代码"""
        return f"# Finnhub {endpoint_name} 实现\n\n"


def main():
    """主函数"""
    analyzer = OpenBBAPIAnalyzer()
    
    # 1. 检查当前API状态
    current_status = analyzer.check_current_apis()
    
    # 2. 识别缺失的API
    missing_apis = analyzer.identify_missing_apis(current_status)
    
    # 3. 生成免费API实现方案
    if missing_apis:
        implementations = analyzer.generate_free_api_implementations(missing_apis)
        
        # 4. 创建补充服务器代码
        server_code = analyzer.create_missing_api_server(implementations)
        
        # 保存服务器代码
        with open("openbb_free_api_server.py", "w", encoding="utf-8") as f:
            f.write(server_code)
        
        print(f"\n✅ 已生成免费API服务器代码: openbb_free_api_server.py")
        print(f"📊 补充了 {len(implementations)} 个缺失的API")
        
        # 生成使用说明
        print(f"\n📋 使用说明:")
        print(f"1. 安装依赖: pip install yfinance")
        print(f"2. 配置API密钥 (可选)")
        print(f"3. 启动服务器: python openbb_free_api_server.py")
        print(f"4. 服务地址: http://127.0.0.1:6902")
    else:
        print("\n✅ 所有必需的API都已可用!")


if __name__ == "__main__":
    main()
