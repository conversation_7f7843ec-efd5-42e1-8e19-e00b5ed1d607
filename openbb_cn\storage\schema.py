from __future__ import annotations

from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import String, Integer, Date, DateTime, Float, Index, UniqueConstraint, Text, Boolean
from datetime import datetime, date


class Base(DeclarativeBase):
    pass


class PriceDaily(Base):
    __tablename__ = "price_daily"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    symbol: Mapped[str] = mapped_column(String(32), index=True)
    trade_date: Mapped[date] = mapped_column(Date, index=True)
    open: Mapped[float | None] = mapped_column(Float)
    high: Mapped[float | None] = mapped_column(Float)
    low: Mapped[float | None] = mapped_column(Float)
    close: Mapped[float | None] = mapped_column(Float)
    volume: Mapped[float | None] = mapped_column(Float)
    provider: Mapped[str] = mapped_column(String(32))
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index("ix_price_daily_symbol_date", "symbol", "trade_date", unique=True),
    )


class User(Base):
    __tablename__ = "users"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    username: Mapped[str] = mapped_column(String(64), unique=True, index=True)
    password_hash: Mapped[str] = mapped_column(String(256))
    password_salt: Mapped[str] = mapped_column(String(64))
    role: Mapped[str] = mapped_column(String(16), default="user")  # 'admin' or 'user'
    active: Mapped[bool] = mapped_column(Boolean, default=True)
    scopes: Mapped[str | None] = mapped_column(String(256), default="prices:read,indicators:read,ai:analyze")
    symbols: Mapped[str | None] = mapped_column(Text, default=None)  # 逗号分隔白名单，空=不限
    markets: Mapped[str | None] = mapped_column(String(64), default=None)  # 例：A,HK,FUT，空=不限
    rate_limit: Mapped[int | None] = mapped_column(Integer, default=None)  # 每分钟次数，空=不限
    expires_at: Mapped[datetime | None] = mapped_column(DateTime, default=None)
    api_key: Mapped[str | None] = mapped_column(String(64), unique=True, default=None)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        UniqueConstraint("username", name="uq_users_username"),
    )

