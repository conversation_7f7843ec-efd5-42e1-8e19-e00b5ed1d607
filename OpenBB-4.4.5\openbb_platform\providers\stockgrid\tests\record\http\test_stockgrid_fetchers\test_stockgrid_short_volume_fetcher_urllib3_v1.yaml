interactions:
- request:
    body: null
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
    method: GET
    uri: https://www.stockgrid.io/get_dark_pool_individual_data?ticker=AAPL
  response:
    body:
      string: !!binary |
        H4sIALYag2YA/5WdS6/synXf5/kYB9CstVHvh2bOOAiSsSAI19YNLPjKMiTZCCDou+f/W1X7nNpk
        MU0K0D3N3uzi4qr1ftXfv/3x3//wx//64x/+86dffv+Hn/7yb7//jz//+Rf9569//Nsf//zv+upv
        P337zd+/6d+f//rtN7/9FlyIv3b11y5+e/24yOtFWS/qcuHdeuHXi7BerEv7tF58Wa2tF325COtz
        wvqcsK4WVqjDCnVYnxPW58Qfq7Vfuy8XYb2I60VaL+p60daLvlwsqGorqtqKEF3k9aKsF+tz/Pqc
        sK4WVqjDCnVYnxPW54Qvq61QxxXqBVV9RVVfyaWv5NJX7PQVO33FQV/Jpa/k0lfs9BU7fcVBX8ml
        r+TSV+z0FTt9xUFfyaWv5NJX7PQFO94tFMJFXC/SepHXi7JerKv9oBAu/HqxPsevz/Hraj8ohIu2
        XqzPCetzwrpaWKEOK9RhfU5YnxPX1X5QiPcLhXAR1ou4XpT1oq4Xbb3oy8WCKr8ixC/kwkVeL9bn
        +PU5YV0trFCHFeqwLh2+LLACGlZAF+yEFSFhpZCwUkhYERJWhIQVIWGlkLBSSFgRElaEhBUhYaWQ
        sFJIWBESVoSEFSFhJYqwIiSsCAnfEZKEgO90MC7iepHWi7xetPViXe2TDsaFXy/W53xu/bio68W6
        tF+XDusCYQU0rICGFdCwPufLa8cV0PgD0B9EMS7CepHXi7Je1PWirRd9ufDraj6uF2m9WJ/j1+cE
        t16sgIZ16bAuHb4ssAIaVkAX7MQVB3Glg7jiIK44iCsO4oqDuNJBXHEQVxzEFQdxxUFciSKuRBFX
        hMQVIXFFSFyJIq4IiStC4oqQtOIgrXSQVi5JK3bSip204iCtdJBWLkkrdtKKnbTiIK10kFaWSSt2
        0oqdtOIgrUSRVpZJK3bSip20EkVaWSav2MkrdvKKnbxSSF4pJK/YySt28oqdvFJIXikkr9jJK3by
        ip28UkheKSSv2MkrdvKKnbwSRV4Rkr8iZJEhZcVBWSmkrBRSVuyUFTtlxUFZKaSsFFJW7JQVO2XF
        QVkppKwIKStCyvraZSWKshJFWVmmgJ3fvb794c+//PLTX37/h//47tbIkQnVfcT8KrV/5PbyPrWP
        nl7V949WX6H5j9Jev04tftSX3qqkj5xegs75j+b1IeX84fWnEGP88HyTS/yIfGi6R0sK56V+9KIP
        jXuClvPJfwT+1Grlcb+OOdeP3Lm5u4+ue6LL4cMnWyePBWtPH4lvenMfKeue4PqHFg4hxY+kDylV
        /+G1TPYhfUTBlV0bP0qFhau9S/go3FObvas+6Ff6dfE9GqAlugBSfl1Kbx9Ozy49tI+oH9XQsj2h
        6m+Gm/Z5c8vRjw+t548UX7/upWpB4c2B1goGXRMyoh7qfenV4Og5hY9Q+Z2Q6fid15tq6R71l8ot
        PukthJWWnBbShx6EHscHrw88rDUvxHOPcPhRgcML1szCtQhE1tOre/2lpz7Q05OABxrnovsImU81
        Ct8RCLOIghfyvrGk51OvhnJ9FbSHwW6L5cPxnevazmCvWdlYe1830O59dHF8ciVkA1B/ze2j2SJe
        66axiDYzNj7FUj46r19Dhf66nmrE0VpMH46/RF8Nsa3X8pE8by9oDJsp6I0SaKhCQzTEZMN4S0nk
        zd7V2OwJrWl/+aaX7G3B3nr5KME2z9UBpEs5fXS7S8/3oK91w1p3ydlTu9PD4lg6GH60tLM3a0nk
        Ba/U2gK/rg660y1F/GTUn5q+gQKT176UConrdeCZkAWFYVlUP8hW3CRktGq/7V648i9fdL9IJvjQ
        oVOfRB5N/7I1Sf/6HlhGVxCCj0JABdlFCIBbtdXto8BlqSYjrACld+NoNtU4Oo1tS3wAtRn+NUap
        AC4wa89jr/QmqRiNeZAM5D40luTxrG3skLy4p9t3zRXjNZ+qeLYAZgLh4xNywfZFW994NS0HB0Ho
        ep2UjSt9TJJVNdoz3HxGhEWKPUPyCg7zQq+21Gg8isjaIF4t56N9AjyoqggmRE4NcBk7xCa6jISJ
        3b7JQZvHDuWqF878qTUtCXqi+Bf+yV7feDZapMiPioOzIAFxXQaB2Q+iL00iLCBpfBqsoZ3pJn5b
        LkP89lDy5BDhH+bqGYoe9BttRyRo4rjbp6jfGX/mJIpD1PjixIyGxeqn/PJVlDkQ0F0oQ3D1Kgp3
        SHoHY7GzwYFtCELfucG0wUn6GUKFqEHmQaydTXoFybMwFIgYZ1Cg6Fo45pcZETBInWdVI70sCuD5
        Uit17KyoSdrD7pM4Kx8NBRCdIK721K7tCPzWtFXgr5Ipwd5W0BVxi31XJUkdoIQosmQ50WCe6q2K
        yFjNFUgBrusN6EBO19uwrNRaDaZbhES9fgKJGWKPhmwJjvFdFE4MYCG4D+EkuutDckjwZfcpNCWm
        vUm+JC3hTDVU+6ahCfhQmzgumEbSDiLuS4mDcyXnIhC2JAxAh13y2OiwtzzulZiXRoeWJGSDrSN5
        HgyHSDdvNOklef2HwVaA3CDqMNEQgoIp2XeB9e1T8f5jrO8nhbncymSshpbu9qqyI/xgNqhpKB69
        hz1fBOht1zsamnWF38HrNU2uyzkMo0Lsl+1nIkbtLXRRdHMzApVc6Eb5fpBRK0PJ8QA0gmwe6PaH
        SfTvP//t9//151/+808/D4soCl/auiS6EW1InMl6QFCUyAMl44Nxc5JAwSZBlpcc67ihNtegQeCO
        NWgnm145SVzbpvogqtIrJyNb8dVHhzLFLNJ2bE1kR4vhqUga5G4oEBFJH2MRhSIp55H5EQ6MkKB+
        Y1I6vMR1ohE+iRJBpLhCZpy4ARKE2MW9oo4gze/NFBHgTmIkDFGfu58aIcl8yMVkiPixozuBI8WK
        0tMduWcpa9tk0SfGRjQOlIkUTTQWSU0JVZBWtWHuk1j0KsnktYRVFqrMoJHADrY7xbQK38H+3Zvc
        lSCCOQfDBNQ3+JdhIJyYBJDYhDOCkUYUJZuhKNZuQyaLIN2QwTWKD6cekHlS2hD7qUvuSdCidGW4
        CTewidgtmWGg3xefBw70CaFfTRvnMjWIJJSgMNGQXIpSf7aF1SG20ajat47UA/Ig8ymBL7F/MhFe
        xX5AaPrRmVAa5lDGiqymCKTru6FD1IRVk8x0lqR1tiEpCcRkYkVfYsDwbeghafdRJUnkJoTwsWCT
        JlRP1GuHYejJwvbcygNkBDvj/RiF8oQUFuYdSrIZOUq/ZDP4oSM/9EvUksXUmyyzYeu7iDr0hjYB
        5cyRkBQtab5KFVaSGUZZBipywTyOIh1tH2WPBpCJ0EOEZFOpHrsTNYUSLQkdl8zYCwUYMLOkWrC4
        ZAxJUrthTsnBEW9LyLMSElQmUSzaeqmgKNYsY4cRUKJ9sbMXL0taOHin61a4NTptie0/oh1xKL6T
        Rpt813MY2xxkA2I3NHFbsF/Ulx5vOghsZqwQVm4dKWZiXc9D0qLTzfOB5EUMRSgCGcKaXpt90a57
        7Ax+VKQ5pkGvzdTDTbWbAT033kU4BTItOAxI8gS3R5YSLkM2rSlxXLGq+Lk2q0w7NYmghwYTvQe8
        AuNICUIjbMGJOoO7g5ZCBTozrrSfmDVoyNwDFjoGsYQqGiqixGQZleEl6rsw5IrcMSQ3BJmlgqTj
        cQx77jh3UFzouGMiHuEPWWogdCQtojhr54QCGInHZ9t44SLzc+G/uWjcL4GRJD1R+F02ehweqZSE
        3hpDR8Iza0flCslEQf/Ul1Cr9bSL0kxC7nSTtCVpmAR6R6luM1i0DUkEjCnc0fZmUWrH+jB25AtL
        bIh4EbOyRFBuej9tuZGBdGvGkwBJTXal9gR5Ke4oYvA4JAhmKtZXFvS8SB66T09FPTfJEBxc+ygH
        Mpr1pBfJ1dSIWFiyfPj1RTTO67BpXdaXljDv3svRkjxg1yT7tN+DmmQWpoYc4fX1Sa7sEO0BJ7TY
        JnUJ5M8N0eppmBaSSfhnw8yWgSsLkTsQyDUNI0loxwHww4MTug0Sefqy2Mwf8VjG01TVfrCb5rD5
        htVkZqtYyyMF/HBPUx2uWNU+pCH+UOzwOo6aiLsNB0H0RJChmLMoT6qZrdhwubxhEqnTgc5CAdLe
        +dNnKbL8hr+DJpLV6oBUhDV8p6Lt1O9Yo8Igw6+RJu1DP4nWYBWT4rlLQ067upjBZjYm2DVdp437
        dFqSw8r0g1pdHwaEh27j0KCDjMwyT1mGzRDEkvISo7JFXCkSfcJqk31nMAfsKFkXwcybEvMw/+Vi
        1TSkrfZB2i3ZLfI6Cy5IGX6bDAJ7G1FNnb6tTIhUzddBIUULK0kTSFd2Q0EjojAcZGnV1IcXL/Vg
        igrqlmRoGAYiHMxsyEHbWwcwWTpjmITZ+AHBSagHC8t2X7+SojFG7b3w1rLmUjWN3FJCbMILMv1D
        MHehS5Q2c2sEotBrcQb9MSMCJCG1TegdiZeuPZNi0u6Akgq51dxZsplQzkPK4VjpDcHAMJjMYdZu
        xu/Wc8e6ChWD9EtwTvubKiC8hHAxrrTFSyaR3i7wXUW5NvYxCYKebf9EiMQ5HKaHbEIJXWfPiMZ5
        zvwR7ZUcUmcUL66JYXyfq4RKtM8JUyCM+7WKhLrdI5Jn75p9FpeJksZn2UPZFDYRBLEyFpb9Vq7H
        5/pFYGIWuCE1cip5fpaCH+v7LDoObXyuLgSIzo3YgTS3rRnQRq4MeJp2LbQ8P+uN47hHz+z6n33W
        PoqcjLrRgtX3z89S3tnukcEotTzWj9pD2WwGp+zTrA21++UyexN1zgwp7EHDodwtGZG2IxgzIxrB
        Z71WwVJ3BAkkarMzXElZyJJr47M8bRxq2y7XsJJtfbwJSW97rkw9GeVuPAsth8XqTDd24wg+E+kN
        PYxnaXGZV3Z/kR4Kdd4vayB6N38rQnZ5rKn129gL2eBaf6yTMlZVGOs0mal10I+eJQ3b5mf5xdrh
        8VlyNs33xRDFibb3EkR14KdkuYXyigZ5ogUHLUmkd4tqDLKVlhzwC2L57IMmBYyMo0EPRMxyG7Qn
        q1LqtY37pYtDGjgpTmzV5mfiAnXQjIRQMW/evi+Enu23bLs8r4GrXHncfK9QRrQA3Mo7wd61fUlY
        jgP/0q8SSXHQgKCug35gBDlEg37kQwa8XbtHSt8NGpCpq73oAwbZ1zI2+vgs89EP2siSJVpq4rYW
        iGzQT5NhFfOAv/KWAx7pP4cINnqTJR/nOjjjLde5jyLLNumTcE2fn0uBrQZsrWDE2Wcu6niu/MHU
        2+BTiWV99pMHJc79gAeXNuaBQ8l+ccD8nKo8gmJrNnkWxGCN1TyOhj7KM63jncTxrZoEQ0QQ2ZGs
        k1kpE5SfV0wjI1acSqEK+MU65jTrNyXFgXoJv5oH9WpjnOScPbyR7hiyJgDIlImR0FGbsMoaT4P3
        5H6LffyUg4JhylDJHd1RhkyRPJSFNmSEq2L6MmgoIqnH92KNJllSBq0bbxjeK+pBP7HPchNcmJ9l
        RntMlAG9VEywdSq2Txu0W424hlyreFxp0nrVy+ZBZ8T3ypTXepZYYMiCKiHrp/wSa0rnze9FoWiE
        wYeRSE2ZfCXXYdKQNiKUyRuNwOGn3MkmpsfGiiTcoK0g01wqe+CtSEq5qVeaZEof9CqxJCkbjIZw
        yVx0n3iWGB/8L5DFrG58lrwNc1+iyE/uwXguwj5O+Z66DP8Bm7ZEyO1D1jf0a8pTjusJ4x2xwbS9
        Uz62VsNYRxIdgZGnPJUbO4g34134T/kiDeaGbBV7Cl1D1oiEJYu7wdw6JtqAubNkGHJTSBb7tan0
        ZPtFN99GLygFMkW2eUUll88LX3GE5kUTXsduSkE0qXFjG5E4yjTXeYHCa3PpmABmXiSJWDdVnpel
        lJoLU0+LGsT2AzZpDSP8wRmyP8pUyfqLaKV9LqANqG1EMyUXJEHy0HBEAGGzyX5VWsHnT5ukthHW
        M6g7Ecf0yaUy9/38iwzLNGlAMkGuipt4k6krKmvzArX6CY5Ul9Rab59IlH8xhZLLcgjckObQASi1
        z8T48pBorUf550PrS/QgcAeHFvIDwwqREhAoAxUE/4SYobFka3Qcdj4HLPyxlUYevU4pry2dloqs
        B0ntoWkEsnA/OE6OtozSXqbIlP0+3lO6WnKrflohEm1tahR24NPyAOEDfrlzocb5PfmVEKdWkyRs
        470EjXbTDcqHV3vun/CLEIa2kAn1qVnh7O771EDa3smtWsdpG8Y6Uea1r3P9wM5MDZ3Ztrm+zMch
        zX9NBNDyxYP7Qpjbr+8F0rRmbBunhSe5JkNtSAMZsZ8UL+dJymRYNkEv36YlhIeEFzQpHANofPY8
        eXJ0bp/GLb5sGHJfJOYnAqV6hHG+/t0/Xmsl8V//9c9/+RFa/vu3L4FmrHsikqheOXbxFQitO9xT
        oJLLajHiQGSCGCqJEpwjeW/yUXFuI3m0aAkQ2XhSJa9CDGVEavCfzLnGnws4HZ6ki7NUPLZ3wrnF
        9sOZIU2mv2XCIpbKs5uLPBmIp/KFhLf8NEIWIhWp2Rc4+vC4OPxJjh6xL2/BE+iGvBJeNxmhggcm
        J0vochZBkB9QCZlELDpfzW3DDbfAkSeaxPtJM8m/qmbEN/mTRFEdpQqEViMSp1gqnygz32QCiaxg
        tjYp1Qyb4UuLBu01iP7rkyUpzDofQUxilBb2ymhaYjBBxt2Hx8mMeOnBIo3yEj4iAVZJLOHLMlhS
        Ph+NVSRzBCO+H5BlghUR+yQRQJG61hPwNeHYmIZ33oQ/20gy2xZfL9I63uK2lqUWZrr+QoDa7DD2
        VkqdEGe1WLmo2/IgZBKaUQz6OFhqC88iE6tA72rVbMUTRJPZg2KJHKOeRIkFAaYoHUzkLBBX6LxB
        wjRNhrhMgshyihbhJyCGyrbsDdm5PoLAFnkg8BGJFAcittWIemRYeUlctkJaRAaDPGgDrJHKd+aM
        NzKfIAhN2PirVHwZW4xWjkRzikXxiXpFSNTi7jKD9CxvIXyhSOSPyK0WCEJA8PQi6pUt+5L0EzkH
        ETzBAjACKzjRdSeeBVmTrmuwhMlAHFi9U3iRYBEyXh5LQN4THJE+ukjXUzHTtHtiyPghmW15eel9
        3asXKN1i0Fq1ekv3a3vyIHi9e7MNECE0wtMkVEfMj9IEAnayYdxHZNvlnriPnkc2lh2wyhntEt94
        8kgiE+KdjsAIqQzLPwQi7tFojCVZiJR+tYB7sWypiRdRSEgj2lvbINpKnjqNYKYeNMAJFuyPSPlg
        Ib2aiKvkWeGTR65NoqHCiVaSULuFlqntEUlIQ4g2LD2G8RqIYAcynEYHps2iVTo4LSvpAn/LhHo1
        bWq35LvFiY2YSXxYwiEF6mq0BdQv1PKiTkL0+kpmX8DjuE/icUszCPHB8vnkxXK3XUlsZoQwBK/F
        NWs0gSfPTuJ25PpFCn1shejRWcpEhBVMujX5LKFYWi+TqQNLPpM7GFiVmOC9BKq4kgxlkp9h8lsE
        gEnUBsEimZvhPIw4ox81DybVG4VWY0v0k5At3EUNQg5WfyLCy24klkR5ZKIzFShGCgSitU4yj1BS
        jgBhoRSsWYQykzC1girCdNVZBk86tfvxZAnGwKv0WkZSPltwE1Imtmixy0LCzwSSHP1i4etEDqLa
        /eifTnYpk45wxfI8xMGtVqvHGUEJJLCRc/LHtfMW95ZAzeC76lrESfUONmVG7iPcUrTKMhFgse0k
        jxWNsEVPfcSvkXImkvCRR+Q3WT6vW/7MyiGE/DjC7x/Z8iWUMXQrAkFfW/A7wlLJ9B2eptUYoAK1
        dy8S4ML9S26zYArDsNUCVrmg17FtkhBMQ4d1K/NgVRGlFBSolpaiLCmPVLNWMQEenT1dzoSUAHK5
        U3Ng24WzEEdpHMRrZoMZ9mVU0pkW8+xatVQztpqJdkvv64cUs1VoJZByTzxaiGXXLFlPNQhqnrRy
        eGEHWERY8qSY3E0IG5nsr0hhWbUaDguxacMK1pR8MP1MMg6DpKFOIslr4c4EuaemLr2s3MyEZzLD
        SAjraKhmURFRbiRW+tW4+i1WuDisk8QiN4l6ohQiiaQqdQPoTsksQvb1VaBYeEHEQ7K/CD7sD9Ew
        oVLhX4/HcZF0e0lKCIeV8iqS8DKdyZlRI6kdaeh/WcSCVupAcv9lWekoYdyqlTJRdkXgF6Xc8abk
        FbzMNUX6i8qk4Tt/cqThpU4cIRTkYiAwLdHjsc/0CnonikP0Qyw1QgeSH9V+IBEgmSJXFQGhtylk
        qvTAYuVUgr2y2112D9lXSkb0gg6dKy1fiW5Ls75qx3QGh0SwqkyBBlumysuIxAWYpCA1fd5qziBK
        UqieYjTBJI8kGe02K5QsvF5FAaBFyUc5rSS3S58qOVChi41BFVgFoihWH8QTjV8Q1xMQZCixE7Dj
        RtmXg4wjZqIMD1BCWs1QQrqQ5VzEZhNWR+K/kPJ15IpSsLcTMos0SKcUpFougLJHPaDwmmLXRi5Y
        NpgIpJByFjVRoKTtE0qobNK7yQWJZixWElJdW18K5pVulvhtJk2bZV1EDLJlpZAo1SOQ38kH4LZq
        K8m74naIx3U7fiQgBgoxtJONhK78Gy0pAZbF+fjnpv+oNJGJqufjiYuAhW0pY0n3V9beCnXQmdQZ
        VileIylHchhIAGxokBPNEhPCEIoZei6AZ/TVYWL7qeVwpHlzjmbXieTgCbGUJ2ZpwryTyEdPdkK2
        0iJiN4GMpWEJVns/OdNUCMlV1DvAB+abwzW9W+5friuSR78hOIShpm+GWIAUS8IwIzwgm/klE4+K
        KjKyoEIfCuavnGvgojJXsBb2vcF8cBOyWRLTj63ElJYnLJuP2mYAKyIXCBkPhf3r0IMgL+gP7WNs
        GRmJIclqwqxFTouWTUNgaYspQgiYipg/XssQJzOBDK9S6YgVY5l8MZSl16W2bGObdl/yyooAcrUE
        VYI9SE/VkaIP4zuLRDUEFaiLlHmKRD2Z4EacBhUnBwYKl5zpmEhCjhavVAoLn7htzfjBUYgrBdit
        /Ad/0JH7wwINZluVzG2elLb4LBuZe0QdilkMl1Gz2H7CczfWMlbQS0lKAJxuleTziP1iRU8FBFKN
        B2uQLXSy1ahDB+vVrHNB3czrkYuRYbFc4AfJS8i4IRFxALyl1RHmoueO/cx7G92bc0plQIWlEase
        EktSJ0XE1y0WmU0dZNO08lbFiJTAE+JES1r0BF+1EZREozeqXbtYkwCvWUJyxrRxnaIOSS8t3cZb
        yhYVHqip18MKxS3gtlQr2xSK8A54uuSZ0VjGQ+aVMsFZjLZMoZ4E3StDADKuXslixFIJUlrk2kVk
        5tpA8oncTXdkJOEHAczrYz7zXQBzTTRYWT0J340KCKnqF4FCGQcSYFa/qedVjOTE5iDt0IKVou5Y
        odcwFDZFUVT0CSWUn4F3gSG9SiuBpHy0lYgWRTNtRElUbKI+k3STcUiR22vuUIOUJBBfJJI+0CA4
        ntoJKy7GxK4U1uOY5W5VFk02vav2vo3AtiG5NQSC/pRwuNB7PXWjVUulYiZaXX6kWM5V5EAz8UGJ
        hZ5K6jiTB6ZIBZ9M0lFGhMgXO0E0VgiodlLB2gTqweVqGImFQA2RNUJg9pnYQthQf4rFL+oI7FQR
        JsjUBIuFUFCK7sZFxDPIfBLc2fxI2SQuGa10aLY205QEb9ADRP+lngQ0hn2NB6PoV7KKiFWHKvLJ
        UIOQWc2yZ9uR8fQVeEqaKEGRjsHDxMUnrE2cY1RCy6Itycog0bLCZCNkE7h2xFSsnUPSw8qBM8+i
        u4F/A0aJRSvkaWPpYwmbyVvsPgrhJDuwIuzfKH/W/pUefwlDlLm9KE3xGI7oWMktarwxTclpSWS+
        iFzm7u3x4gu2RySRyMNTo1Xh58qvKQUB+Ni0aqJAPvCjht6nkh3rKGGjV8OQnI7OtegCqxWf3x4u
        UuDZ8sAqTxEPdLu9QGS0fkjacRsqmuIDJARuegHh0ZzT1OaqVKlEW1VEY68UW7Tl4Xz+LtIUCsVw
        ITV7CVECKCF/yr+Bt4gEkpxB6VuxZSvLSO8h5xL+HMBrw6T6xm1UkFIz2uxlC/atXQMWgTVnbyNT
        zXZOhnPgbTJ6nX+JDhEry87WC5g/SCZP4Sx2iqBKRrGJ+JhEgr5OMAS9EkQ0zI1qA7WIZWwYBC3Q
        9mZkhdlrRCl/BiTIVva8BeFuyFI0bP82RC6lUfIADblJO0wRN+oR+hqCRHvDOlTPJPteast4A9+b
        r/Uc/g0NnItpG99jY+nlYaU8wMV2hQkIzwFNgEqJ04n8aLjIzbZESq7xfSHsgNfPfSlZECdRGDSQ
        04kj8hK+27+CFTFP9FU/ixY2oOFEjoYEzEcgFkJGAjuTsI/kK3+XSLDbMwo0Wfw48y+BKes/0KsD
        BP9CvlKhsDdRKoiciJYZd7JxoA9JUnvVIgyh0iPv2ogYelBRIFfoAk0OT8GJUL/0kz21OduxOl8u
        EHvDQRIBmriR8ALqDl3RhZIlMSLFbcJZNgLKxuk4+8SLEIfRajIjNb+sUgZzWHNUIMKjf3lnYiCi
        B8QVRp61VkAX7ru4aliulB7KVDUooE+8A9nIyJ3xO28x2Yiy6wYsGRf+TOTYxA4WNo8lhoavKR/U
        vocLaFmRtsHpj2HcbjuFfsqs4gCah+PssKG4qhEh2PmHQuiXVZzqXWhaa8mAoPpCStWi62x3tLvF
        KdlAbcbGju2jM8WkCR0G0bbPlWpCSO5xsPu62D61ub1o/G6CtoRB2niW/Cu8NMjAhBLRdvjQEStI
        JjLzUA+e4BTxQLGGkXAMmC7E04waGq+YRuzHIsWIefq0kuGVUguTGsQpIpZ5MhmXscgoHMywPS8b
        DErfwDdBrdbGPoBwieRejWiDEWtiX0zmSqAb7cfM9wInDlGePcRHrRtqRa+RvIEt7yC8rBlADB+p
        chxEn2Rmm7hB+yIkMesI5hMywqj2Q2bThsQ1hplhDzMapsn6HeKL2Br9bmh4dK6UgpT43/78t59+
        WRJFVKyYw+mbs/4PIhHYw8hweYUYEFQZ0yNg3pDl8D7QAMFirJatgfFFsQVDA6NCf/Vddr6ZYd6K
        myNyKlIqIXH+IgZEwIfKNStYSvyCUBFpA28JW9ILEiFElDCEKAq25IowQfOiVcxRjFBw5wOox7OU
        TUVBqG5D9oldUEHUO5mqDN4IksJo68vDXsrypfULfQrEz73oQWjgNfBPK5nJauW0jQJ2iRTfzIcS
        u1MMhjZOvJA9jewW+cgM+orFaM0kIxoSqFS0ALGndq+TbgDzGbcPXpOqwguSbicHJA4iy9AaWMYE
        J/NqfpP2wDxv5BTuQYQh8K0bDOusoo7HFnzkhJImHy78iAMiTqfDds7WCMBGWn8WfBF6Jn1CqblV
        dcNy1Hl6yyqJlcQI4IK0TkdHvaiWKvM+6mn0bD1XFt1H4DX0CPyCQGRQRIiRCzk0ewZYke7pBGZk
        H42AS7dqXe2kSA4RhKVa6cZFqXirjseMgEi1L4Y0qu0LIQdRJXFIQySBJMcnC9toNRpuyVFQP0rv
        LKU4VjZLZbOzTA51xcEECNyW4U1eAWmbrEehN9OY2olqLYhiPqhNEgHplDHzwCsx/YTQJ95PCERu
        B8E2iI0uvxABKBFuppaf8JVoXJ8sEmeAEIlDgogiRTqZZFKDinscQS6Tk/qOmvph60eEEPkdSRCi
        qqSfYF9vzdeegn/gTNTdAQAp6TzomRIWBAmhQPaaoE2q1hUKC1K/KyYR8MUakuE3C1AQoqGmDqGG
        Bxatk1EbSn+aca9V3UISkT4JonfkHgkNV7CIsUfQpZMixnrUwjSfsHDG4czR9oIMuUXoiC9YVMsT
        yYvGbrSvEqCzHLdFL6n/YGejo0GEREcI2WSaSLfCMWx7Jo+djQCo9SgwVjH/m5CWLCYpAdovKqLP
        fCc9HisFWreYBPQqNdEspxN57AeGO2WmCDJ7bTyWmKwlQ3RIS4ZrpLQbCEB8RURutqqngZRiXUPB
        tsfQiSfsKr+kThK9Ey0gjBkjrjAeb+w2iUmjlGoNtpUNQOWAMKqZB2LFhFjLcEI2uxaeJBqExgjB
        eiEI01QiBpHtKUTNMXt9pNENr8Bj/Evnsj34kZhksmS1ZRG+D8TTmt47WEs2pnVIVt1rrS0Uz5kV
        EZsFX6UBKW75MNTSV0x0gKrbYIQsg4FAK8La8YNkLG5FAyaOSfloJXBmUU+kcRgBuvHUZAopI93R
        DGNhXCgLEaRiMhDvnJ3qjuIJe0eL9pNzddSG4FhQ12MN6zAXP0BFkdWEV4uFv4qJ+ToieSJxS1bx
        aWSV0QtiKfPk/Wj39jialFmRPpCMxZPmqdnEfEIuE1VOxeQjuSmidRbmRw0GSqQsVgL9eos1U2yA
        JE/AZ137Bil2KJFCSq21MtKqhzBSzo3uxgztS72AeMRVpkpPbCbkY0TgNQSkO1ltay7CYELtF4vI
        SQySeAxpCiQiltTKNYsv0c1KsgWpar251lZOwrPzyZNRL4gmSn9GPFnyCPai+9Cb34Ji96ZerL8K
        qwRNQjegAzsQBXtBaZJwjHAh/E9GlV0plvSXwYJJiGR2gGnayArdeGnioFYikxJ9riRgZJRRl6B3
        6CZK9X8ZVXR04KMGa/jCTkLMaXvkD0f6F7QKljPNHCSdtI0WjdQvqNeTAEkGFE5DRNN4ohzRJKN1
        6KCjaNON/5/in9//7ad//uXnOUHwJ9l0f//2L7/8+a/6Bu2q7bTJgt9+M8Y/fU5z+9NPf/m3n/+m
        b//763+//ue3z+jOz//35z/9x4/MV5WRRHHSISFm2RPTPKeoEJJFouDb3/74L//281+0/j/90//6
        H9+OdqdZIY6SrH+8FnCJgZ7BTbfBpRuK1qYTvOR4XL+AV3LyDbhmKqGkj+C6DXbzbXB7G/0CJ+wi
        Uq14cANty+/BxZ4LLPAFXGvjP4Hb7mMXz6xu4RX7nvE+4EWrv4MXq9NaA77A20mJnODtt+HNliY4
        g0tKr/ULcEldvQMX09gq2g/UkPoJXBu2cxO9ItByIlG+p3DwTCgD3iCh+5Z6ZcBblf4XeAn+nOH1
        t+GVyLJC2hO4VuycLsCVZHwHLl5GPVEvKvkMbrgNrmRt3okGcrglX4Cb6nvs4gpZpewXcK1P9QTu
        fdmQR/nVGVwXY7Iq/TO4FPa/BRd/7Uy8NhvmBG65ryhkoW15jXCLu+A16ej3tCun0vzZL+Bau+YJ
        3Hofu92dBYAEMj0NZU8LkSzsO2hxfAOve4C2bEj3vuDVkvW042LAFOnduYA2vCcFnHPrtDmQwsZm
        CPfFLkm9jRij+GH0zGygbW91hMUPGLxzFGIyq74A+zkF9iYh5FGEfkRtsC6TC3vBvyVbi3GcNRqz
        Qk7APrHGRk/OEVg5jy2eFPOn+n1LtRaGsdr6A2pLOUN73xjLPqWNfqDUZbR97FDb3pqOFioya+Og
        Hro/Q/tA3uYt1VKP489aeSqz9NZWsHCWda0dTLGjrfA5iPgmtCn2k30o3MrJd+GkNga0hBDf4rbF
        aB7fF2htkNkJ2vsSgYaWDd1S2dTrSWvcZzLCgkQ9j9DGM7T+gR3GdIaN6sWarBdsFju21BtwiV1a
        i8zBym1naO9bYTZbaeNEUCTWLj2e9za5xVcJtn6Bljjpmc38fXkrO6FthAKVbN5d4va9KiMGTCzg
        SArtrB0euL+pEmPaEK5EW97bYIw+eU+4sYlNj96vTSY6QftEKOStwM2UZ154k/mGCUYs3bTkQYT5
        DW7vm2AU+JztWHx1+rX7XiwwLOE9vPhLNEMf3LN2Vr4PvN9UtpRr5ZWxXchc6tbfylwtbPMHjt7k
        Wfs+cH4Z7LKzFWrL7sqX7O+JwZIn/YTcwOSmI7QPfN82RtJskEsdxFUk5IZCI8dDAPZo2rgztA/s
        MEZsbHDLDJ+rsE18qx8sDWVFYwdY05luw30zrPjR+njSD5Jt4Ur5vvcjLVVGRefReTirhwdeLzU2
        O0vBBWv62wLr8ntgyeZR5PYFWItin6G9L3CbTW4504FjnsJeIsRU3wtcMo7WKvkFWhtBd4L2vsCl
        qWpHCCG5MW5k56G/t8ctK2oVhwefN5xxG+9LhLQP11CEHq+gdek9JZC5tQz4Adpaz9Det8JyiLto
        DZXytV54ZnSJvYOW9HKsR9UbT4Hcz8M9bkLLsK8tblO58B4itb1vcUtW8uT0UuR4hvaJi97qBlrr
        OfDlSoKF91Fn8vQUUB8Dd+Vo1/gnTrrcB192uozOiOyvhNh7N92qCWwUwMF/CPEM730hRjtO2WUh
        6N8Ykwe2cdz3YdzqmfR4cCDoYT2De1+KNbfxwSzqTFDgKtDo30edKcwI8aB9WziriHn8yV1Wi3kD
        Lo0wPV7Rwh1bQdI8+zO06ah+/RNHvdR0dhlBbuthjPzZBZ3je+RS4WJzi76AW07hMP/EUc826W0n
        GJjIc+GekU1/q38lBW3+wxdo08lR9098SVrUdp5vFom0vZ+eKUV776czn+cgxVo9W43+USaVXPzG
        ErPerDEDYwfvDXOBeiaagL/C206p1M/Tou46EHEX0LcGMrNSd+Cm99il5qrHgynW+jkK4h956j2d
        t1yeeu9pTIrbeTv5RvhOqtLGEh2gPaYfPs/buptVtwmUZ3D1sHqOjwzchveerxWvuWNgYUxlPoL7
        wJe0iTYbaJnzcU5fD2jjjUgu9SzmU3+BllaqM7T3bbFqZS0bBSEtcBFVyHcIlyLAemQz4TaeCfdB
        ErVSDb3zfHMMdR9VwO96L8RCDzbB5UC4Gzv3gesrLVk2Ipf+0E0hw6SE/N7OpQIoH61yhMKZzR74
        ZyVs8rokfZJzF1HyLIPyPSV4LXw0bJiLegb2vl3D9PMNamm0de2SEN6jlqJHO3/mgNq4IYT7do1U
        2TlcK7Klk7NdEALN3G+h7Snb4K0j2Z4F2APXlxl+G2eSnuV0hdtW3otbanvj0X/odm7BV2jDE2cy
        +XoOLkO2zIvbO+o5+veOOvXH1AgecXtM/H+e3Hg3rNDPuUfSfI6RjhdM9r6ezWqkbfzcF9zSmnqG
        9oFVI4dvV1VBy3a7sGqS/Mz3zk4RMPlICja8/QTufbOm+NB3ibNEjedFFKTdyPNRbG5TGL9AS8/6
        GdoH8TDGqm4It7bdHz69hztpvjKmyn2BNp9NxvDEkexhk+InKelau4iMciTEe2gzY8HciXD90dkJ
        T7KSnLyzrV4KY3T83mS84z1E59Mxc9bTDrkPspIUGW+kAsMdmrsoB2o3fDPaH9KxeKnXU63V5xGx
        d8tA6jm6LApJVGFf5VBvVGFSu1yPMYVux2OcoH1ghKXa0q5WIRV/VQfCdPgbZYLdzmY6IvcUbQxP
        HEmG92yiYQzzCOGqxuZ9Dab1uuQUT1LhFF4KT5KSlVN1Nrgt4ukraG+k060fJx1TUb1wxNoJ2gdm
        WM47dcZYlOwucpK31EPNaaMe2tlSeJSSLGcNi80YbcbRRfzjPWopdM7HUnKG25wF7gM3MnAC1S4X
        1eVIXvmRPt3I98YQrXL+oB/OEuyBGwkGz0BhKdTgLvzIVO+YuDaW+chldmTUCdz7do1UVt0VvjOu
        p55LLoYIize8HZrY6JU7Kd8NuA/iNQxQ39kKmFFX+Z10wyQnS983tkLekO6TtoLqdvVWjD4qV/rM
        h/e2Av2A+Zj0tZ7CM7gPitkY2n+GlvlM7SLoHLN7b4gRVT1VKtDC9UXkpu9Htt8kXebK72J38s3C
        2YOfyH0bsLHGynrMndns0jO0D3Jn0aZMbmxyCeOrSuf0Putr3Z90XR5zUV9rRtP3U+9vgls4R2GD
        3BhKPbeeTLlwoySIFtUzLfiDUT7AvW/aVMJF25A+DTwX+RJ3o46cRtp81BEcEFzO4D6oeu/1XC5u
        Ve9uY61PaPt7M5dmX+u2PUDrN4z2wLbh6KRdqaBLPl546qneKLMh0hjOmTPfT9A+SJ11O3NyW2fT
        zvXwnzb5neqK7v2x0whK2ED7IEYetskdJtnFy76oGwqCxu58KnFlmtUZ2gfOZK5nh1xWbg0pXrUU
        MHvhhm3T0imHGulYPUH7IP3vc9h5kzYVMFxVZ9f3UXJrkS/HOv0WDobugPe+cWMHQ+2EmBOVXKCX
        DOuNTB+HWxxMx9a26H1g3BSbXLmpBpEwblfVIAxMf6eBMRjs9ItDNHcjdB+4aFKSu7g+UyBju1AR
        dwKOTERIx1YjmY7tLBie+BEcYbqtIaZfO1+VuZb3BQvMbfDxmOzLOw38oHDUixzO8SR0Wq+bWoYB
        brvjpWVKns8u8Nkae5A+yzns6slt7GfNFy5wLne6TOhYOpKuHdR6AveBecNZxDtLN6dxjNAu5Hgj
        ycOQjngKi4VDaH9Ae1/uphJ3/joDVOM52T4p4X2nug0SOUfF7PTrE7QPCoOi39JtQFxc0G2+kVNn
        2AmTpk/FIGdb7EECTUgI+5I2pkOfiwk/bcf3hjkvdYqWy+3ZgPugkTq5ncHAvNx67vcbhHvDoWRs
        DPN8j5ZjOyqIR+m+EvquF9Vm+m5K3Qa0/j2b2Wybdgw5ciLzUYaFJ/4vdVy7CKlNHt7ETqcQe2vd
        2ACecOzra/Vs3TxK+Hk7oX3ro4VYLmYWRKZ+ve1MjrI/j+X6JNbP4D5I+I0J2+eAriTVWboNYO+V
        g1gj9Tn3e3TXHyX8RKNpl4lg0HT3V17Pe+PG2DSdisTaIZs6wH3g/3KS3ga5mNXpghIYofg+KZXK
        qdu3HVvlBrT3NYRkazgXJ5A4kb11kaDMd1o3mFt1iu63Y/eZgfukD7GM41yPIVJRbryosUg3TBub
        rcXxFkcpdlJojzJ+VBztaoOYf34Fro0pfW83ZjuE6ajPdsi9bzjWEnfdXExp9xfljci99xpNIqye
        XLS402gPMn4yQcbRwUfKLdJ0F32Tsd5oVGeSmp1kcHCA41mKPXHYHTOxttF9l6/C5eX9HBMb9xaP
        Gcq2cSgfpdE4X3ij0BjLX85NKIPRbkR0bSRd2kQXNjL3STWm307a4OyAq6Rf8jegZWyeP3bGtHQo
        axvQPhBiwe/SUhxwkC78Mw4jf6/PCs2jR1uBA0LO0D5opi5x16jOKQyuXVDCjZQf0wc5h+YUK9+Q
        7YN5NruMtM1dYHTeRZbnjjYLPcTjgBiCYmc77EnGz+Xt6CjOs7hskXLvfQgb49iOYRCOi9pQwoOg
        GJ3fu6Jn+i4ue3judFKHHI+jjRhZcNZmD3xfiUW3a5ezo0HKxbSg9H4KgE2mdPU456ofxq4I3PjE
        QdNP8r4VIvfWzh2gn1b5+5l9jO+x8u9Do/pRncUnObTgWtqleeyQlXKeKfWrmUR7Cy2TRZneeGz0
        9EfSjU8ctMD54Dv/l2M6NsnAAe77Tk8bf9qOPUd2suYZ3AfGQuwbR8yqWPJGLw9o/XtasCGt6Rh9
        HqHYI7QPxBhHLWwHXfVcLgr28YbeQsu811MzBE2/R6kbnzhoWsFdTOFJ7iIUEm/Uitm02xKOEyHC
        WY7FZ5NiOIZlYzhWomIXDiXn9rwP55Z2qsms8dCXOsC9b9z04M5DQGwGlo8X0wU5rPUttIwNTscO
        A+YWbKB90N1X3DahWhjff2Hc+PQ+iMdo49iOOiJu+OxJc1+U9bwjXOGvnotbBm77e2ht/HLu5+Eg
        5SzEnjhojBDcWjch16tIyA1gOb7qpM7iYQjPAPaBUOAYkJ03SR7/YpJJvGHnmrWQTiNoj2MmB7gP
        8n2+ntsebDQXRwtdoPZGZZudoH7UD7V9lKPDE584kxxXsh0tWO203T207zukTD2cpjb6s3cWn/iS
        vtZNbMbURtzVlv5qzm18S7k2tDyVIykwz/oM74NQeUjbaC5xkHRRTCyL8H15EJ5+PjoRHCt3VmcP
        0n0yXzbWLJJYtHBlLKR+Y3Sjc3QJnUyberZzn9SQyqraeZSc3HeV5YnZ3ehQlpo8j+yL5yhTfOJR
        tsbRIRtOK7lfpSE4XuStrRA5x/xIC56zOE/QPqmyiHVLC52R/BczR+ONMgCG/dejVc6CR2MhPcqh
        VX+u/KDdhDEJF/W55c6AwdBdPk7cKO0w+GxA+8AQ289k4jzJK2c9hhuBG45NCEfCFZ+Vo1hIT0pI
        s+87ocuhl+XS+X0/E8KOdoilnHF7tBbSE++Xw6M3Co3z+TbJH4OWaUs38up1VKYfcJuPbJaeOL8l
        biaG0ewVUrqadxXfD/y2IzLasU2KYz42lPAgf+Y2I0tssFTc1JtPaN8PirFjPMpxyAJ0e7QW0qPR
        K34zhIuy8lL8xbgNub7vuYyjRuwwiS/Q1o96poQH9aMN3t+5O/IgLrsR79QsSB2eOj3rJn2WHnm+
        Ke9HHHGm7aYga8L73lO3Q1v6KchUOCb5BO99iSvm3wzQN4uyxU3/8q9mpfZ7y5GjZdLJWAiHASED
        3idBMbcta7PzgcsFflO7EX/mAJxybLDnZKwN9T6IirUSdv2edopxuyjcTzckmR3Tw2FER0l2dNfT
        s0mpEjfbrgjvNoPcJvW+d3u65Y/iSTTsmO1Bo7KA2sX2U5Kluxdkqb03xey0I44v+gpt3qH2QWyf
        MqDdsJjWU7ioYEn5RiSEE5n8scG+HGfQGrgPXDSqcDd8xqTnywYOfyOxzmC6eGzg4HC8M7D3LbFG
        WHFDtV5cfZGnThyN9L5+hcNi0kkmuLMl9qDYtca+HfUc5QidKWRC+z5Qbodv1eN0Xzv66gztg1pX
        ZqfvJEKtm9KLAW2/MbsAIze1M7Rnd+fJmNS4b99InEVyAWyNN1BbGLVwPtbmrHufZPtC6LthV3Ye
        vb8C19X3Vc8ctBaOIqFidJ7gfVLrKjFzHpxO1Iax0hcyod4gXE6Dy8fGow0p5Eeeb+vbxK+QW64q
        FsqdkwBSCvV0EkA8++n5iefLxJ1dMWaQl30pb+udhrkcmjv2/bZ4NmvyE9c3cPTMJmgTeukxXc16
        ru/1g539505Vbf4cEsuPkpOyt7ap35JDMX9wA6/cmbdWOQcUtuPhci2cqrTzo+QkQyx3ZQCpustW
        2uzae6ORHn1/HKDMGcsbeJ8cGtTOVTWExEqoF8WuKd8YDcFJj/HUm5oOYzcGtA/G1TPRaCMYSNte
        n9t3o5RJDh4H3h45zR2VRH7i/hLP3pWJMfP2YpBFdOGOjZtaO828KufAfn5UPRrrdh4ALVnpKpXK
        +fFvxZiso3QaQlsPI7oGuA8G1ot7d8jNetyFAxFvddLK2ojHI0KY2ne0G/OjeTFlM5ieNHXP+ap6
        Jd05m4tjIU61TP3sQOQnnm+kI2RDCrnLJL84zzOWGyE8DnHtO3DPUuGB5ysUnpv4GDnn6mXYhuO2
        3w9wFI2dRiL6s+ebn2QnU6/buX3JXyakRJA3jPKW7Hjd07C2swx7kJ6UG5t2uVQJoHB5susNU4xZ
        Wv3UiuhOvmR+dHgjJxFsCIHTdi7mUsd44+Qzhq+WYyciPVJngfvA8+3e7SRY7WlXjTVRm97HcpmH
        Eo7zchk3uYH2gefb6rkonzi5XJ4rkXAjYcLRzOF40ALAbgjhvlXDpNnd7LNSOVL4wvG90eSJVeSO
        x9r04yEWA9onTTGUJ++cyVq8u5w8eqMuiHOuT4FcZnme4X3gTJaad2OZpBlavmCz1OqNplQk42n4
        WThPi8lP+ia9k6W/PX9F9tlFGZPcoBtDBl108RjVx1A62jXliXtGm+du6DdnVl+Rgrtz6oaXCAvH
        MUfprB4E7X2pILB2vQXSrj1dzWoLNwrgOXg9nIbQ5rOhUB71TXq/HShW5Wdv5tgPIZZvHO7L6fCn
        czyF3FO9Qnnk+TJJfFcB4DlI8EJDlBu5SY6wP5250Y/nHA1w75thvbedqSD9QOr2orXe3Tmdi6kd
        R++M+WdHk7w8O8BRSmKD3UCN9iaiM+F9C25KrtfQv2ajgtu0IpYn2Umq/3bgyjQkH32VTX1/Vhsz
        MKI7+GeW1Tjz2pPsJANjNmGmxMjnqwGkco3fMltnrro/1OFxYGY4muXlkfsr7bXr3wh24PrFgZ60
        Rb6NMzVZdNkd8RsOZ7sOeB9I3hK3YzcCo0mvonj5zlSx5pjvdAC3nAsXyqPsJDS24zYxcnAXw6Rk
        ZryfcFOppM3pTA4b9N63IGkG2GV6fO09hHNYZ8Bb3k/2jLLOSzgoYsz100iA8sSr1E82BxBbIC62
        TRhqSrP3fqW2redaj/AejyYe8D6YZhG2x2/gF+6OWJ7ofT9yMPRKIemB22RRnea2lSfeD1Mbz5kH
        +96lq1LHdKcYXuQktySdyGEjfR9k/pqMvV0neNJeXgxoyndmoYkhfTv0VqMrTiZ6eZL6Q+zviCG4
        kq8yEtm/H1seYpBT0Y7gplMvWnlU9Fq2Exdknad2cc5Nen/4FaOfszsYZVI+566I8sQTZgDn7jwp
        12O5GNsW241eKfSMRcj/8Tst8S//+vOffvr2m9/+/dv/+ePPv/xBvxov8Pr2y0///PMv3695tc87
        5gN+3DK/WO/5AvSPO798fXX/ry5+8KvNL74i7fi7r3/98g4rUpY3Wb9e75979uPO+cU/fveP17f/
        +Msf/+Xnv377zd9tu/Xht2PwrKvDf/x+kdeLsl7U5cJM4e8Xfr0I68W6tNkf3y++rNbWi75chPU5
        YX1OWFcLK9RhhTqszwnrc+KP1drIH3+/COtFXC/SelHXi7Ze9OViQVVbUdVWhLQR7/9+UdaL9Tl+
        fU5YVwsr1GGFOqzPCetzwpfVVqjjCvWCqr6iqq/k0ldy6St2+oqdvuKgr+TSV3LpK3b6ip2+4qCv
        5NJXcukrdvqKnb7ioK/k0ldy6St2+oId7xYK4SKuF2m9yOtFWS/W1X5QCBd+vVif49fn+HW1HxTy
        eZ7494v1OWF9TlhXCyvUYYU6rM8J63PiutoPCvk8Bvb7RVgv4npR1ou6XrT1oi8XC6r8ihC/kMvn
        qX3fL9bn+PU5YV0trFCHFeqwLh2+LLACGlZAF+yEFSFhpZCwUkhYERJWhIQVIWGlkLBSSFgRElaE
        hBUhYaWQsFJIWBESVoSEFSFhJYqwIiSsCAnfEZK+Dwz/fhHXi7Re5PWirRfrap90kL6P8f1+sT7n
        c+vT9wmv3y/Wpf26dFgXCCugYQU0rICG9TlfXjuugMYfgP4givR9nNz3i7xelPWirhdtvejLhV9X
        83G9SOvF+hy/Pie49WIFNKxLh3Xp8GWBFdCwArpgJ644iCsdxBUHccVBXHEQVxzElQ7iioO44iCu
        OIgrDuJKFHElirgiJK4IiStC4koUcUVIXBESV4SkFQdppYO0cklasZNW7KQVB2mlg7RySVqxk1bs
        pBUHaaWDtLJMWrGTVuykFQdpJYq0skxasZNW7KSVKNLKMnnFTl6xk1fs5JVC8kohecVOXrGTV+zk
        lULySiF5xU5esZNX7OSVQvJKIXnFTl6xk1fs5JUo8oqQ/BUhiwwpKw7KSiFlpZCyYqes2CkrDspK
        IWWlkLJip6zYKSsOykohZUVIWRFS1tcuK1GUlSjKyjLmlf7uhxfzWxt+W152mmi0f5p/2Umo7WUT
        s73947jqH7Xa33Iat/QXAf4+/qn8nAEyFvQfa3VutFkXdsxMeI05xlyEYLc3bix0h3FVXLYf5GY/
        93ocBzvoAdVmGjJRp9mF57vKmaL8U7v9iRfRFYvRt8PfSBfx38QdVrLPHSHawj7YjXEsaa/qbGWm
        5ulvjADN9k+r9v48ADS8KOnr3tbKZQDUDIRYxpL20ODtoTXZP87/ANkiR3xpcAmH4wf2HjYGgD7a
        zlqEJewfsK0vbc1oKK1UQXJRiv16ApQGuibWYv/EGsOT/A9MNoqluN/nH6jJA4eBookxmoL/pmSP
        1o+LpZ+IYbJ/1XLqNnyljtcY22GYL4a7caT9PCp+HsE+zzYfZ4aPs7jnIdfz7Oh5KPM87XieIjyP
        553n3s4DZedJreMI1Hm26Dy0c56GOY+ZnOc3znMR54GD8yS/eUTePHpunuk2D0ubh5DN073msVnz
        QKp50tM4QWmeTDRP/Jkn6cwTaubRL/NMlXlWyTwDZJ6tMQ+tmKdBzGMW5vkF82CAOW9/zrGf4+Hn
        3PU5zvyTvWz89pxrPQdGz0nMY8DxHB08R/LOUbdjguwczTpHns5ZonNG5xx+OYdKzmmNcwrinC44
        x/bNeXhz0Nwc4TZno82ZY3OY1xySNWZPzZlOc1bSmEE0Z/vMmTlzFs0c8vJJojaVZM77+M5ANqBi
        jn6YExXmpII5AWB21s+W9dkKPnusZ/Py7Aqe7bazj3U2iM7Oy9nSODsFZwPe7Gsbwf7ZhzUanGbj
        0OjIma0us4dkVOSProfZTPDjK0AbVeWzWHsUQc/a4lm0O6thZ5npLN+cdZGz4HAW8s0KuVl6Nmq6
        ZrHUrEKa9T2zcGbWo8xCj1lAMSsTZsp/ptJninqmfmdKdWYqZwJw5tVmvmqmgWZ2ZSYtZjJgxthn
        8HoEhUe49Xf/+Md/+38hGAzJju0AAA==
    headers:
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Length:
      - '16780'
      Content-Type:
      - application/json
      Date:
      - Mon, 01 Jul 2024 21:08:06 GMT
      Nel:
      - '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}'
      Report-To:
      - '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1719868086&sid=************************************&s=1SIdGH%2BXs6rNp34hLwsJ3YEyj62JCQqSNwyZlq8Z3Xw%3D"}]}'
      Reporting-Endpoints:
      - heroku-nel=https://nel.heroku.com/reports?ts=1719868086&sid=************************************&s=1SIdGH%2BXs6rNp34hLwsJ3YEyj62JCQqSNwyZlq8Z3Xw%3D
      Server:
      - Cowboy
      Set-Cookie:
      - XSRF-TOKEN=IjcyOTAyYzY3OTA4NjYyYTEzMDQ0YmU2NmQxYmMyM2RmZTgzZGJmYzUi.ZoMatg.J7zJ3ThmZOajtPPKk08CtOGYR5U;
        Secure; Path=/; SameSite=None
      - session=8da15354-9d29-4a9f-87d0-776cbe524863; Expires=Tue, 02 Jul 2024 05:08:06
        GMT; HttpOnly; Path=/
      Vary:
      - Accept-Encoding
      Via:
      - 1.1 vegur
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
    status:
      code: 200
      message: OK
version: 1
