from __future__ import annotations

from sqlalchemy import create_engine
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
from .schema import Base
from ..config import get_settings


def create_db_engine() -> Engine:
    url = get_settings().database_url
    engine = create_engine(url, pool_pre_ping=True)
    return engine


engine: Engine | None = None
SessionLocal = None


def init_db() -> None:
    global engine, SessionLocal
    engine = create_db_engine()
    Base.metadata.create_all(bind=engine)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@contextmanager
def db_session():
    if SessionLocal is None:
        init_db()
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()

