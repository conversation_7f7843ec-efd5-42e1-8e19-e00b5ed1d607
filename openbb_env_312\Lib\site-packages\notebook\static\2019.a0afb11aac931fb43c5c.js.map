{"version": 3, "file": "2019.a0afb11aac931fb43c5c.js?v=a0afb11aac931fb43c5c", "mappings": ";;;;;;;;;;;;AAI8B;;AAE9B;AAC4B;AAC5B,wCAAwC,qEAAM;AAC9C;AACA;AACA,qBAAqB,oDAAM;AAC3B;AACA,6CAA6C,oDAAM,mDAAmD,oDAAM;AAC5G,kCAAkC,GAAG;AACrC;AACA,CAAC;;AAED;AACA,0CAA0C,qEAAM;AAChD;AACA,UAAU,sBAAsB;AAChC,EAAE,+EAAgB;AAClB;AACA;AACA,EAAE,8DAAG,8BAA8B,SAAS,gBAAgB,QAAQ;AACpE,CAAC;AACD,qDAAqD,qEAAM;AAC3D,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C,YAAY,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO;AAC1D,CAAC;;AAKC;;;;;;;;;;;;;;;;;;;;;ACxC4B;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAaA;;AAE9B;AACA;AACA,0BAA0B,qEAAM;AAChC,sBAAsB,gBAAgB,KAAK;AAC3C;AACA,GAAG;AACH;AACA,2BAA2B,qEAAM;AACjC,KAAK;AACL,UAAU;AACV,gBAAgB,uiDAAuiD;AACvjD,kBAAkB,8jCAA8jC;AAChlC;AACA,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,cAAc,8CAA8C,IAAI,QAAQ,IAAI,yDAAyD,IAAI,WAAW,IAAI,aAAa,IAAI,aAAa,oBAAoB,+CAA+C,IAAI,WAAW,IAAI,gRAAgR,oCAAoC,WAAW,IAAI,YAAY,IAAI,gRAAgR,IAAI,gRAAgR,IAAI,gRAAgR,IAAI,gRAAgR,IAAI,gRAAgR,IAAI,gRAAgR,IAAI,gRAAgR,IAAI,gRAAgR,IAAI,gRAAgR,IAAI,kCAAkC,IAAI,kCAAkC,IAAI,uCAAuC,wEAAwE,2CAA2C,IAAI,2CAA2C,IAAI,2CAA2C,4IAA4I,WAAW,IAAI,WAAW,IAAI,WAAW,IAAI,YAAY,IAAI,YAAY,IAAI,YAAY,IAAI,YAAY,IAAI,YAAY,IAAI,YAAY,IAAI,YAAY,IAAI,0BAA0B,sCAAsC,0BAA0B,sCAAsC,uEAAuE,IAAI,uEAAuE,IAAI,2CAA2C,IAAI,2HAA2H,sCAAsC,2HAA2H,IAAI,oDAAoD,IAAI,YAAY,IAAI,2CAA2C,IAAI,YAAY,IAAI,2CAA2C,IAAI,aAAa,2HAA2H,cAAc,oBAAoB,SAAS,qBAAqB,SAAS,KAAK,yCAAyC,oBAAoB,mGAAmG,gNAAgN,SAAS,qBAAqB,SAAS,KAAK,8DAA8D,IAAI,uBAAuB,IAAI,4CAA4C,IAAI,uBAAuB,IAAI,2BAA2B,IAAI,2BAA2B,IAAI,2GAA2G,wEAAwE,cAAc,IAAI,cAAc,IAAI,cAAc,IAAI,cAAc,IAAI,8DAA8D,qBAAqB,aAAa,qBAAqB,cAAc,IAAI,cAAc,IAAI,4CAA4C,qBAAqB,aAAa,sDAAsD,mGAAmG,KAAK,2BAA2B,IAAI,qCAAqC,IAAI,mDAAmD,IAAI,iEAAiE,qBAAqB,8DAA8D,IAAI,qCAAqC,IAAI,qCAAqC,qBAAqB,4CAA4C,IAAI,aAAa,IAAI,aAAa,IAAI,YAAY,IAAI,YAAY,IAAI,aAAa,IAAI,YAAY,IAAI,YAAY,IAAI,YAAY,IAAI,aAAa,IAAI,YAAY,IAAI,YAAY,IAAI,YAAY,IAAI,YAAY,qBAAqB,aAAa,IAAI,YAAY,IAAI,YAAY,IAAI,aAAa,IAAI,YAAY,IAAI,YAAY,qBAAqB,8DAA8D,IAAI,8DAA8D,IAAI,8DAA8D,IAAI,8DAA8D,IAAI,4CAA4C,IAAI,4CAA4C;AACz9N,sBAAsB,4UAA4U;AAClW,gCAAgC,qEAAM;AACtC;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,KAAK;AACL,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ,iEAAiE;AACjE;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA,OAAO;AACP;AACA,8BAA8B,qEAAM;AACpC;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA,OAAO;AACP;AACA,iCAAiC,qEAAM;AACvC;AACA;AACA,OAAO;AACP;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,oCAAoC,qEAAM;AAC1C;AACA;AACA;AACA,OAAO;AACP;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,gBAAgB;AAChB;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,OAAO;AACP;AACA,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA,OAAO;AACP;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP,4DAA4D;AAC5D,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,iCAAiC,qEAAM;AACvC;AACA,OAAO;AACP;AACA,sCAAsC,qEAAM;AAC5C;AACA,OAAO;AACP,iBAAiB,0BAA0B;AAC3C,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,gCAAgC,6HAA6H,gBAAgB,eAAe,wPAAwP,YAAY,YAAY,EAAE,0nBAA0nB,8MAA8M;AACtxC,oBAAoB,yBAAyB,iDAAiD,iBAAiB,8CAA8C,iBAAiB,8CAA8C,aAAa,+EAA+E,iBAAiB,2CAA2C,aAAa,2CAA2C,cAAc,mDAAmD,eAAe;AAC5f;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,sEAAW;AAClC,uBAAuB,sEAAW;AAClC,6BAA6B,4EAAiB;AAC9C,6BAA6B,4EAAiB;AAC9C,2BAA2B,0EAAe;AAC1C,2BAA2B,0EAAe;AAC1C,qCAAqC,qEAAM,OAAO,yEAAS;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qEAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM,8DAAG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oEAAK;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,yEAAS;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,aAAa,GAAG,aAAa,GAAG,QAAQ;AACvD;AACA;AACA,oBAAoB,IAAI,EAAE,cAAc,IAAI,IAAI;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,uBAAuB;AACpC;AACA;;AAEA;AACA,gCAAgC,qEAAM;;AAEtC;AACA,YAAY;AACZ,cAAc;AACd;;AAEA;AACA,cAAc;AACd;;AAEA;AACA,mBAAmB;AACnB,iBAAiB;AACjB;;AAEA;AACA,YAAY;AACZ;AACA,cAAc;AACd,oBAAoB;AACpB;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,YAAY;AACZ;AACA;;AAEA;AACA,cAAc;AACd,oBAAoB;AACpB;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,mBAAmB;AACnB,aAAa;AACb;AACA;AACA,YAAY;AACZ,aAAa;AACb;AACA;AACA,wBAAwB;AACxB;;AAEA;AACA;;AAEA;AACA;AACA,uEAAQ;AACR;AACA,CAAC;AACD,2BAA2B,qEAAM;AACjC,EAAE,8DAAG;AACL,EAAE,8DAAG;AACL,UAAU,qCAAqC,EAAE,yEAAS;AAC1D;AACA,cAAc,+EAAiB;AAC/B;AACA,gCAAgC,2FAA4B;AAC5D;AACA;AACA;AACA;AACA,QAAQ,qEAAM;AACd;AACA,EAAE,wEAAa;AACf;AACA;AACA;AACA;AACA;AACA,EAAE,iFAAmB;AACrB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AAGE", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/requirementDiagram-KVF5MWMF.mjs"], "sourcesContent": ["import {\n  __name,\n  configureSvgSize,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/insertElementsForSize.js\nimport { select } from \"d3\";\nvar getDiagramElement = /* @__PURE__ */ __name((id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  return svg;\n}, \"getDiagramElement\");\n\n// src/rendering-util/setupViewPortForSVG.ts\nvar setupViewPortForSVG = /* @__PURE__ */ __name((svg, padding, cssDiagram, useMaxWidth) => {\n  svg.attr(\"class\", cssDiagram);\n  const { width, height, x, y } = calculateDimensionsWithPadding(svg, padding);\n  configureSvgSize(svg, height, width, useMaxWidth);\n  const viewBox = createViewBox(x, y, width, height, padding);\n  svg.attr(\"viewBox\", viewBox);\n  log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);\n}, \"setupViewPortForSVG\");\nvar calculateDimensionsWithPadding = /* @__PURE__ */ __name((svg, padding) => {\n  const bounds = svg.node()?.getBBox() || { width: 0, height: 0, x: 0, y: 0 };\n  return {\n    width: bounds.width + padding * 2,\n    height: bounds.height + padding * 2,\n    x: bounds.x,\n    y: bounds.y\n  };\n}, \"calculateDimensionsWithPadding\");\nvar createViewBox = /* @__PURE__ */ __name((x, y, width, height, padding) => {\n  return `${x - padding} ${y - padding} ${width} ${height}`;\n}, \"createViewBox\");\n\nexport {\n  getDiagramElement,\n  setupViewPortForSVG\n};\n", "import {\n  getDiagramElement,\n  setupViewPortForSVG\n} from \"./chunk-RZ5BOZE2.mjs\";\nimport {\n  getRegisteredLayoutAlgorithm,\n  render\n} from \"./chunk-TYCBKAJE.mjs\";\nimport \"./chunk-IIMUDSI4.mjs\";\nimport \"./chunk-VV3M67IP.mjs\";\nimport \"./chunk-HRU6DDCH.mjs\";\nimport \"./chunk-K557N5IZ.mjs\";\nimport \"./chunk-H2D2JQ3I.mjs\";\nimport \"./chunk-C3MQ5ANM.mjs\";\nimport {\n  utils_default\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __export,\n  __name,\n  clear,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/requirement/parser/requirementDiagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 3], $V1 = [1, 4], $V2 = [1, 5], $V3 = [1, 6], $V4 = [5, 6, 8, 9, 11, 13, 21, 22, 23, 24, 41, 42, 43, 44, 45, 46, 54, 72, 74, 77, 89, 90], $V5 = [1, 22], $V6 = [2, 7], $V7 = [1, 26], $V8 = [1, 27], $V9 = [1, 28], $Va = [1, 29], $Vb = [1, 33], $Vc = [1, 34], $Vd = [1, 35], $Ve = [1, 36], $Vf = [1, 37], $Vg = [1, 38], $Vh = [1, 24], $Vi = [1, 31], $Vj = [1, 32], $Vk = [1, 30], $Vl = [1, 39], $Vm = [1, 40], $Vn = [5, 8, 9, 11, 13, 21, 22, 23, 24, 41, 42, 43, 44, 45, 46, 54, 72, 74, 77, 89, 90], $Vo = [1, 61], $Vp = [89, 90], $Vq = [5, 8, 9, 11, 13, 21, 22, 23, 24, 27, 29, 41, 42, 43, 44, 45, 46, 54, 61, 63, 72, 74, 75, 76, 77, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90], $Vr = [27, 29], $Vs = [1, 70], $Vt = [1, 71], $Vu = [1, 72], $Vv = [1, 73], $Vw = [1, 74], $Vx = [1, 75], $Vy = [1, 76], $Vz = [1, 83], $VA = [1, 80], $VB = [1, 84], $VC = [1, 85], $VD = [1, 86], $VE = [1, 87], $VF = [1, 88], $VG = [1, 89], $VH = [1, 90], $VI = [1, 91], $VJ = [1, 92], $VK = [5, 8, 9, 11, 13, 21, 22, 23, 24, 27, 41, 42, 43, 44, 45, 46, 54, 72, 74, 75, 76, 77, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90], $VL = [63, 64], $VM = [1, 101], $VN = [5, 8, 9, 11, 13, 21, 22, 23, 24, 41, 42, 43, 44, 45, 46, 54, 72, 74, 76, 77, 89, 90], $VO = [5, 8, 9, 11, 13, 21, 22, 23, 24, 41, 42, 43, 44, 45, 46, 54, 72, 74, 75, 76, 77, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90], $VP = [1, 110], $VQ = [1, 106], $VR = [1, 107], $VS = [1, 108], $VT = [1, 109], $VU = [1, 111], $VV = [1, 116], $VW = [1, 117], $VX = [1, 114], $VY = [1, 115];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"directive\": 4, \"NEWLINE\": 5, \"RD\": 6, \"diagram\": 7, \"EOF\": 8, \"acc_title\": 9, \"acc_title_value\": 10, \"acc_descr\": 11, \"acc_descr_value\": 12, \"acc_descr_multiline_value\": 13, \"requirementDef\": 14, \"elementDef\": 15, \"relationshipDef\": 16, \"direction\": 17, \"styleStatement\": 18, \"classDefStatement\": 19, \"classStatement\": 20, \"direction_tb\": 21, \"direction_bt\": 22, \"direction_rl\": 23, \"direction_lr\": 24, \"requirementType\": 25, \"requirementName\": 26, \"STRUCT_START\": 27, \"requirementBody\": 28, \"STYLE_SEPARATOR\": 29, \"idList\": 30, \"ID\": 31, \"COLONSEP\": 32, \"id\": 33, \"TEXT\": 34, \"text\": 35, \"RISK\": 36, \"riskLevel\": 37, \"VERIFYMTHD\": 38, \"verifyType\": 39, \"STRUCT_STOP\": 40, \"REQUIREMENT\": 41, \"FUNCTIONAL_REQUIREMENT\": 42, \"INTERFACE_REQUIREMENT\": 43, \"PERFORMANCE_REQUIREMENT\": 44, \"PHYSICAL_REQUIREMENT\": 45, \"DESIGN_CONSTRAINT\": 46, \"LOW_RISK\": 47, \"MED_RISK\": 48, \"HIGH_RISK\": 49, \"VERIFY_ANALYSIS\": 50, \"VERIFY_DEMONSTRATION\": 51, \"VERIFY_INSPECTION\": 52, \"VERIFY_TEST\": 53, \"ELEMENT\": 54, \"elementName\": 55, \"elementBody\": 56, \"TYPE\": 57, \"type\": 58, \"DOCREF\": 59, \"ref\": 60, \"END_ARROW_L\": 61, \"relationship\": 62, \"LINE\": 63, \"END_ARROW_R\": 64, \"CONTAINS\": 65, \"COPIES\": 66, \"DERIVES\": 67, \"SATISFIES\": 68, \"VERIFIES\": 69, \"REFINES\": 70, \"TRACES\": 71, \"CLASSDEF\": 72, \"stylesOpt\": 73, \"CLASS\": 74, \"ALPHA\": 75, \"COMMA\": 76, \"STYLE\": 77, \"style\": 78, \"styleComponent\": 79, \"NUM\": 80, \"COLON\": 81, \"UNIT\": 82, \"SPACE\": 83, \"BRKT\": 84, \"PCT\": 85, \"MINUS\": 86, \"LABEL\": 87, \"SEMICOLON\": 88, \"unqString\": 89, \"qString\": 90, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 5: \"NEWLINE\", 6: \"RD\", 8: \"EOF\", 9: \"acc_title\", 10: \"acc_title_value\", 11: \"acc_descr\", 12: \"acc_descr_value\", 13: \"acc_descr_multiline_value\", 21: \"direction_tb\", 22: \"direction_bt\", 23: \"direction_rl\", 24: \"direction_lr\", 27: \"STRUCT_START\", 29: \"STYLE_SEPARATOR\", 31: \"ID\", 32: \"COLONSEP\", 34: \"TEXT\", 36: \"RISK\", 38: \"VERIFYMTHD\", 40: \"STRUCT_STOP\", 41: \"REQUIREMENT\", 42: \"FUNCTIONAL_REQUIREMENT\", 43: \"INTERFACE_REQUIREMENT\", 44: \"PERFORMANCE_REQUIREMENT\", 45: \"PHYSICAL_REQUIREMENT\", 46: \"DESIGN_CONSTRAINT\", 47: \"LOW_RISK\", 48: \"MED_RISK\", 49: \"HIGH_RISK\", 50: \"VERIFY_ANALYSIS\", 51: \"VERIFY_DEMONSTRATION\", 52: \"VERIFY_INSPECTION\", 53: \"VERIFY_TEST\", 54: \"ELEMENT\", 57: \"TYPE\", 59: \"DOCREF\", 61: \"END_ARROW_L\", 63: \"LINE\", 64: \"END_ARROW_R\", 65: \"CONTAINS\", 66: \"COPIES\", 67: \"DERIVES\", 68: \"SATISFIES\", 69: \"VERIFIES\", 70: \"REFINES\", 71: \"TRACES\", 72: \"CLASSDEF\", 74: \"CLASS\", 75: \"ALPHA\", 76: \"COMMA\", 77: \"STYLE\", 80: \"NUM\", 81: \"COLON\", 82: \"UNIT\", 83: \"SPACE\", 84: \"BRKT\", 85: \"PCT\", 86: \"MINUS\", 87: \"LABEL\", 88: \"SEMICOLON\", 89: \"unqString\", 90: \"qString\" },\n    productions_: [0, [3, 3], [3, 2], [3, 4], [4, 2], [4, 2], [4, 1], [7, 0], [7, 2], [7, 2], [7, 2], [7, 2], [7, 2], [7, 2], [7, 2], [7, 2], [7, 2], [17, 1], [17, 1], [17, 1], [17, 1], [14, 5], [14, 7], [28, 5], [28, 5], [28, 5], [28, 5], [28, 2], [28, 1], [25, 1], [25, 1], [25, 1], [25, 1], [25, 1], [25, 1], [37, 1], [37, 1], [37, 1], [39, 1], [39, 1], [39, 1], [39, 1], [15, 5], [15, 7], [56, 5], [56, 5], [56, 2], [56, 1], [16, 5], [16, 5], [62, 1], [62, 1], [62, 1], [62, 1], [62, 1], [62, 1], [62, 1], [19, 3], [20, 3], [20, 3], [30, 1], [30, 3], [30, 1], [30, 3], [18, 3], [73, 1], [73, 3], [78, 1], [78, 2], [79, 1], [79, 1], [79, 1], [79, 1], [79, 1], [79, 1], [79, 1], [79, 1], [79, 1], [79, 1], [26, 1], [26, 1], [33, 1], [33, 1], [35, 1], [35, 1], [55, 1], [55, 1], [58, 1], [58, 1], [60, 1], [60, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 4:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 5:\n        case 6:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 7:\n          this.$ = [];\n          break;\n        case 17:\n          yy.setDirection(\"TB\");\n          break;\n        case 18:\n          yy.setDirection(\"BT\");\n          break;\n        case 19:\n          yy.setDirection(\"RL\");\n          break;\n        case 20:\n          yy.setDirection(\"LR\");\n          break;\n        case 21:\n          yy.addRequirement($$[$0 - 3], $$[$0 - 4]);\n          break;\n        case 22:\n          yy.addRequirement($$[$0 - 5], $$[$0 - 6]);\n          yy.setClass([$$[$0 - 5]], $$[$0 - 3]);\n          break;\n        case 23:\n          yy.setNewReqId($$[$0 - 2]);\n          break;\n        case 24:\n          yy.setNewReqText($$[$0 - 2]);\n          break;\n        case 25:\n          yy.setNewReqRisk($$[$0 - 2]);\n          break;\n        case 26:\n          yy.setNewReqVerifyMethod($$[$0 - 2]);\n          break;\n        case 29:\n          this.$ = yy.RequirementType.REQUIREMENT;\n          break;\n        case 30:\n          this.$ = yy.RequirementType.FUNCTIONAL_REQUIREMENT;\n          break;\n        case 31:\n          this.$ = yy.RequirementType.INTERFACE_REQUIREMENT;\n          break;\n        case 32:\n          this.$ = yy.RequirementType.PERFORMANCE_REQUIREMENT;\n          break;\n        case 33:\n          this.$ = yy.RequirementType.PHYSICAL_REQUIREMENT;\n          break;\n        case 34:\n          this.$ = yy.RequirementType.DESIGN_CONSTRAINT;\n          break;\n        case 35:\n          this.$ = yy.RiskLevel.LOW_RISK;\n          break;\n        case 36:\n          this.$ = yy.RiskLevel.MED_RISK;\n          break;\n        case 37:\n          this.$ = yy.RiskLevel.HIGH_RISK;\n          break;\n        case 38:\n          this.$ = yy.VerifyType.VERIFY_ANALYSIS;\n          break;\n        case 39:\n          this.$ = yy.VerifyType.VERIFY_DEMONSTRATION;\n          break;\n        case 40:\n          this.$ = yy.VerifyType.VERIFY_INSPECTION;\n          break;\n        case 41:\n          this.$ = yy.VerifyType.VERIFY_TEST;\n          break;\n        case 42:\n          yy.addElement($$[$0 - 3]);\n          break;\n        case 43:\n          yy.addElement($$[$0 - 5]);\n          yy.setClass([$$[$0 - 5]], $$[$0 - 3]);\n          break;\n        case 44:\n          yy.setNewElementType($$[$0 - 2]);\n          break;\n        case 45:\n          yy.setNewElementDocRef($$[$0 - 2]);\n          break;\n        case 48:\n          yy.addRelationship($$[$0 - 2], $$[$0], $$[$0 - 4]);\n          break;\n        case 49:\n          yy.addRelationship($$[$0 - 2], $$[$0 - 4], $$[$0]);\n          break;\n        case 50:\n          this.$ = yy.Relationships.CONTAINS;\n          break;\n        case 51:\n          this.$ = yy.Relationships.COPIES;\n          break;\n        case 52:\n          this.$ = yy.Relationships.DERIVES;\n          break;\n        case 53:\n          this.$ = yy.Relationships.SATISFIES;\n          break;\n        case 54:\n          this.$ = yy.Relationships.VERIFIES;\n          break;\n        case 55:\n          this.$ = yy.Relationships.REFINES;\n          break;\n        case 56:\n          this.$ = yy.Relationships.TRACES;\n          break;\n        case 57:\n          this.$ = $$[$0 - 2];\n          yy.defineClass($$[$0 - 1], $$[$0]);\n          break;\n        case 58:\n          yy.setClass($$[$0 - 1], $$[$0]);\n          break;\n        case 59:\n          yy.setClass([$$[$0 - 2]], $$[$0]);\n          break;\n        case 60:\n        case 62:\n          this.$ = [$$[$0]];\n          break;\n        case 61:\n        case 63:\n          this.$ = $$[$0 - 2].concat([$$[$0]]);\n          break;\n        case 64:\n          this.$ = $$[$0 - 2];\n          yy.setCssStyle($$[$0 - 1], $$[$0]);\n          break;\n        case 65:\n          this.$ = [$$[$0]];\n          break;\n        case 66:\n          $$[$0 - 2].push($$[$0]);\n          this.$ = $$[$0 - 2];\n          break;\n        case 68:\n          this.$ = $$[$0 - 1] + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: 2, 6: $V0, 9: $V1, 11: $V2, 13: $V3 }, { 1: [3] }, { 3: 8, 4: 2, 5: [1, 7], 6: $V0, 9: $V1, 11: $V2, 13: $V3 }, { 5: [1, 9] }, { 10: [1, 10] }, { 12: [1, 11] }, o($V4, [2, 6]), { 3: 12, 4: 2, 6: $V0, 9: $V1, 11: $V2, 13: $V3 }, { 1: [2, 2] }, { 4: 17, 5: $V5, 7: 13, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, o($V4, [2, 4]), o($V4, [2, 5]), { 1: [2, 1] }, { 8: [1, 41] }, { 4: 17, 5: $V5, 7: 42, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 4: 17, 5: $V5, 7: 43, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 4: 17, 5: $V5, 7: 44, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 4: 17, 5: $V5, 7: 45, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 4: 17, 5: $V5, 7: 46, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 4: 17, 5: $V5, 7: 47, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 4: 17, 5: $V5, 7: 48, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 4: 17, 5: $V5, 7: 49, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 4: 17, 5: $V5, 7: 50, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 26: 51, 89: [1, 52], 90: [1, 53] }, { 55: 54, 89: [1, 55], 90: [1, 56] }, { 29: [1, 59], 61: [1, 57], 63: [1, 58] }, o($Vn, [2, 17]), o($Vn, [2, 18]), o($Vn, [2, 19]), o($Vn, [2, 20]), { 30: 60, 33: 62, 75: $Vo, 89: $Vl, 90: $Vm }, { 30: 63, 33: 62, 75: $Vo, 89: $Vl, 90: $Vm }, { 30: 64, 33: 62, 75: $Vo, 89: $Vl, 90: $Vm }, o($Vp, [2, 29]), o($Vp, [2, 30]), o($Vp, [2, 31]), o($Vp, [2, 32]), o($Vp, [2, 33]), o($Vp, [2, 34]), o($Vq, [2, 81]), o($Vq, [2, 82]), { 1: [2, 3] }, { 8: [2, 8] }, { 8: [2, 9] }, { 8: [2, 10] }, { 8: [2, 11] }, { 8: [2, 12] }, { 8: [2, 13] }, { 8: [2, 14] }, { 8: [2, 15] }, { 8: [2, 16] }, { 27: [1, 65], 29: [1, 66] }, o($Vr, [2, 79]), o($Vr, [2, 80]), { 27: [1, 67], 29: [1, 68] }, o($Vr, [2, 85]), o($Vr, [2, 86]), { 62: 69, 65: $Vs, 66: $Vt, 67: $Vu, 68: $Vv, 69: $Vw, 70: $Vx, 71: $Vy }, { 62: 77, 65: $Vs, 66: $Vt, 67: $Vu, 68: $Vv, 69: $Vw, 70: $Vx, 71: $Vy }, { 30: 78, 33: 62, 75: $Vo, 89: $Vl, 90: $Vm }, { 73: 79, 75: $Vz, 76: $VA, 78: 81, 79: 82, 80: $VB, 81: $VC, 82: $VD, 83: $VE, 84: $VF, 85: $VG, 86: $VH, 87: $VI, 88: $VJ }, o($VK, [2, 60]), o($VK, [2, 62]), { 73: 93, 75: $Vz, 76: $VA, 78: 81, 79: 82, 80: $VB, 81: $VC, 82: $VD, 83: $VE, 84: $VF, 85: $VG, 86: $VH, 87: $VI, 88: $VJ }, { 30: 94, 33: 62, 75: $Vo, 76: $VA, 89: $Vl, 90: $Vm }, { 5: [1, 95] }, { 30: 96, 33: 62, 75: $Vo, 89: $Vl, 90: $Vm }, { 5: [1, 97] }, { 30: 98, 33: 62, 75: $Vo, 89: $Vl, 90: $Vm }, { 63: [1, 99] }, o($VL, [2, 50]), o($VL, [2, 51]), o($VL, [2, 52]), o($VL, [2, 53]), o($VL, [2, 54]), o($VL, [2, 55]), o($VL, [2, 56]), { 64: [1, 100] }, o($Vn, [2, 59], { 76: $VA }), o($Vn, [2, 64], { 76: $VM }), { 33: 103, 75: [1, 102], 89: $Vl, 90: $Vm }, o($VN, [2, 65], { 79: 104, 75: $Vz, 80: $VB, 81: $VC, 82: $VD, 83: $VE, 84: $VF, 85: $VG, 86: $VH, 87: $VI, 88: $VJ }), o($VO, [2, 67]), o($VO, [2, 69]), o($VO, [2, 70]), o($VO, [2, 71]), o($VO, [2, 72]), o($VO, [2, 73]), o($VO, [2, 74]), o($VO, [2, 75]), o($VO, [2, 76]), o($VO, [2, 77]), o($VO, [2, 78]), o($Vn, [2, 57], { 76: $VM }), o($Vn, [2, 58], { 76: $VA }), { 5: $VP, 28: 105, 31: $VQ, 34: $VR, 36: $VS, 38: $VT, 40: $VU }, { 27: [1, 112], 76: $VA }, { 5: $VV, 40: $VW, 56: 113, 57: $VX, 59: $VY }, { 27: [1, 118], 76: $VA }, { 33: 119, 89: $Vl, 90: $Vm }, { 33: 120, 89: $Vl, 90: $Vm }, { 75: $Vz, 78: 121, 79: 82, 80: $VB, 81: $VC, 82: $VD, 83: $VE, 84: $VF, 85: $VG, 86: $VH, 87: $VI, 88: $VJ }, o($VK, [2, 61]), o($VK, [2, 63]), o($VO, [2, 68]), o($Vn, [2, 21]), { 32: [1, 122] }, { 32: [1, 123] }, { 32: [1, 124] }, { 32: [1, 125] }, { 5: $VP, 28: 126, 31: $VQ, 34: $VR, 36: $VS, 38: $VT, 40: $VU }, o($Vn, [2, 28]), { 5: [1, 127] }, o($Vn, [2, 42]), { 32: [1, 128] }, { 32: [1, 129] }, { 5: $VV, 40: $VW, 56: 130, 57: $VX, 59: $VY }, o($Vn, [2, 47]), { 5: [1, 131] }, o($Vn, [2, 48]), o($Vn, [2, 49]), o($VN, [2, 66], { 79: 104, 75: $Vz, 80: $VB, 81: $VC, 82: $VD, 83: $VE, 84: $VF, 85: $VG, 86: $VH, 87: $VI, 88: $VJ }), { 33: 132, 89: $Vl, 90: $Vm }, { 35: 133, 89: [1, 134], 90: [1, 135] }, { 37: 136, 47: [1, 137], 48: [1, 138], 49: [1, 139] }, { 39: 140, 50: [1, 141], 51: [1, 142], 52: [1, 143], 53: [1, 144] }, o($Vn, [2, 27]), { 5: $VP, 28: 145, 31: $VQ, 34: $VR, 36: $VS, 38: $VT, 40: $VU }, { 58: 146, 89: [1, 147], 90: [1, 148] }, { 60: 149, 89: [1, 150], 90: [1, 151] }, o($Vn, [2, 46]), { 5: $VV, 40: $VW, 56: 152, 57: $VX, 59: $VY }, { 5: [1, 153] }, { 5: [1, 154] }, { 5: [2, 83] }, { 5: [2, 84] }, { 5: [1, 155] }, { 5: [2, 35] }, { 5: [2, 36] }, { 5: [2, 37] }, { 5: [1, 156] }, { 5: [2, 38] }, { 5: [2, 39] }, { 5: [2, 40] }, { 5: [2, 41] }, o($Vn, [2, 22]), { 5: [1, 157] }, { 5: [2, 87] }, { 5: [2, 88] }, { 5: [1, 158] }, { 5: [2, 89] }, { 5: [2, 90] }, o($Vn, [2, 43]), { 5: $VP, 28: 159, 31: $VQ, 34: $VR, 36: $VS, 38: $VT, 40: $VU }, { 5: $VP, 28: 160, 31: $VQ, 34: $VR, 36: $VS, 38: $VT, 40: $VU }, { 5: $VP, 28: 161, 31: $VQ, 34: $VR, 36: $VS, 38: $VT, 40: $VU }, { 5: $VP, 28: 162, 31: $VQ, 34: $VR, 36: $VS, 38: $VT, 40: $VU }, { 5: $VV, 40: $VW, 56: 163, 57: $VX, 59: $VY }, { 5: $VV, 40: $VW, 56: 164, 57: $VX, 59: $VY }, o($Vn, [2, 23]), o($Vn, [2, 24]), o($Vn, [2, 25]), o($Vn, [2, 26]), o($Vn, [2, 44]), o($Vn, [2, 45])],\n    defaultActions: { 8: [2, 2], 12: [2, 1], 41: [2, 3], 42: [2, 8], 43: [2, 9], 44: [2, 10], 45: [2, 11], 46: [2, 12], 47: [2, 13], 48: [2, 14], 49: [2, 15], 50: [2, 16], 134: [2, 83], 135: [2, 84], 137: [2, 35], 138: [2, 36], 139: [2, 37], 141: [2, 38], 142: [2, 39], 143: [2, 40], 144: [2, 41], 147: [2, 87], 148: [2, 88], 150: [2, 89], 151: [2, 90] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return \"title\";\n            break;\n          case 1:\n            this.begin(\"acc_title\");\n            return 9;\n            break;\n          case 2:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 3:\n            this.begin(\"acc_descr\");\n            return 11;\n            break;\n          case 4:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 5:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 6:\n            this.popState();\n            break;\n          case 7:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 8:\n            return 21;\n            break;\n          case 9:\n            return 22;\n            break;\n          case 10:\n            return 23;\n            break;\n          case 11:\n            return 24;\n            break;\n          case 12:\n            return 5;\n            break;\n          case 13:\n            break;\n          case 14:\n            break;\n          case 15:\n            break;\n          case 16:\n            return 8;\n            break;\n          case 17:\n            return 6;\n            break;\n          case 18:\n            return 27;\n            break;\n          case 19:\n            return 40;\n            break;\n          case 20:\n            return 29;\n            break;\n          case 21:\n            return 32;\n            break;\n          case 22:\n            return 31;\n            break;\n          case 23:\n            return 34;\n            break;\n          case 24:\n            return 36;\n            break;\n          case 25:\n            return 38;\n            break;\n          case 26:\n            return 41;\n            break;\n          case 27:\n            return 42;\n            break;\n          case 28:\n            return 43;\n            break;\n          case 29:\n            return 44;\n            break;\n          case 30:\n            return 45;\n            break;\n          case 31:\n            return 46;\n            break;\n          case 32:\n            return 47;\n            break;\n          case 33:\n            return 48;\n            break;\n          case 34:\n            return 49;\n            break;\n          case 35:\n            return 50;\n            break;\n          case 36:\n            return 51;\n            break;\n          case 37:\n            return 52;\n            break;\n          case 38:\n            return 53;\n            break;\n          case 39:\n            return 54;\n            break;\n          case 40:\n            return 65;\n            break;\n          case 41:\n            return 66;\n            break;\n          case 42:\n            return 67;\n            break;\n          case 43:\n            return 68;\n            break;\n          case 44:\n            return 69;\n            break;\n          case 45:\n            return 70;\n            break;\n          case 46:\n            return 71;\n            break;\n          case 47:\n            return 57;\n            break;\n          case 48:\n            return 59;\n            break;\n          case 49:\n            this.begin(\"style\");\n            return 77;\n            break;\n          case 50:\n            return 75;\n            break;\n          case 51:\n            return 81;\n            break;\n          case 52:\n            return 88;\n            break;\n          case 53:\n            return \"PERCENT\";\n            break;\n          case 54:\n            return 86;\n            break;\n          case 55:\n            return 84;\n            break;\n          case 56:\n            break;\n          case 57:\n            this.begin(\"string\");\n            break;\n          case 58:\n            this.popState();\n            break;\n          case 59:\n            this.begin(\"style\");\n            return 72;\n            break;\n          case 60:\n            this.begin(\"style\");\n            return 74;\n            break;\n          case 61:\n            return 61;\n            break;\n          case 62:\n            return 64;\n            break;\n          case 63:\n            return 63;\n            break;\n          case 64:\n            this.begin(\"string\");\n            break;\n          case 65:\n            this.popState();\n            break;\n          case 66:\n            return \"qString\";\n            break;\n          case 67:\n            yy_.yytext = yy_.yytext.trim();\n            return 89;\n            break;\n          case 68:\n            return 75;\n            break;\n          case 69:\n            return 80;\n            break;\n          case 70:\n            return 76;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:title\\s[^#\\n;]+)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:.*direction\\s+TB[^\\n]*)/i, /^(?:.*direction\\s+BT[^\\n]*)/i, /^(?:.*direction\\s+RL[^\\n]*)/i, /^(?:.*direction\\s+LR[^\\n]*)/i, /^(?:(\\r?\\n)+)/i, /^(?:\\s+)/i, /^(?:#[^\\n]*)/i, /^(?:%[^\\n]*)/i, /^(?:$)/i, /^(?:requirementDiagram\\b)/i, /^(?:\\{)/i, /^(?:\\})/i, /^(?::{3})/i, /^(?::)/i, /^(?:id\\b)/i, /^(?:text\\b)/i, /^(?:risk\\b)/i, /^(?:verifyMethod\\b)/i, /^(?:requirement\\b)/i, /^(?:functionalRequirement\\b)/i, /^(?:interfaceRequirement\\b)/i, /^(?:performanceRequirement\\b)/i, /^(?:physicalRequirement\\b)/i, /^(?:designConstraint\\b)/i, /^(?:low\\b)/i, /^(?:medium\\b)/i, /^(?:high\\b)/i, /^(?:analysis\\b)/i, /^(?:demonstration\\b)/i, /^(?:inspection\\b)/i, /^(?:test\\b)/i, /^(?:element\\b)/i, /^(?:contains\\b)/i, /^(?:copies\\b)/i, /^(?:derives\\b)/i, /^(?:satisfies\\b)/i, /^(?:verifies\\b)/i, /^(?:refines\\b)/i, /^(?:traces\\b)/i, /^(?:type\\b)/i, /^(?:docref\\b)/i, /^(?:style\\b)/i, /^(?:\\w+)/i, /^(?::)/i, /^(?:;)/i, /^(?:%)/i, /^(?:-)/i, /^(?:#)/i, /^(?: )/i, /^(?:[\"])/i, /^(?:\\n)/i, /^(?:classDef\\b)/i, /^(?:class\\b)/i, /^(?:<-)/i, /^(?:->)/i, /^(?:-)/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:[\\w][^:,\\r\\n\\{\\<\\>\\-\\=]*)/i, /^(?:\\w+)/i, /^(?:[0-9]+)/i, /^(?:,)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [6, 7, 68, 69, 70], \"inclusive\": false }, \"acc_descr\": { \"rules\": [4, 68, 69, 70], \"inclusive\": false }, \"acc_title\": { \"rules\": [2, 68, 69, 70], \"inclusive\": false }, \"style\": { \"rules\": [50, 51, 52, 53, 54, 55, 56, 57, 58, 68, 69, 70], \"inclusive\": false }, \"unqString\": { \"rules\": [68, 69, 70], \"inclusive\": false }, \"token\": { \"rules\": [68, 69, 70], \"inclusive\": false }, \"string\": { \"rules\": [65, 66, 68, 69, 70], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 3, 5, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 59, 60, 61, 62, 63, 64, 67, 68, 69, 70], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar requirementDiagram_default = parser;\n\n// src/diagrams/requirement/requirementDb.ts\nvar RequirementDB = class {\n  constructor() {\n    this.relations = [];\n    this.latestRequirement = this.getInitialRequirement();\n    this.requirements = /* @__PURE__ */ new Map();\n    this.latestElement = this.getInitialElement();\n    this.elements = /* @__PURE__ */ new Map();\n    this.classes = /* @__PURE__ */ new Map();\n    this.direction = \"TB\";\n    this.RequirementType = {\n      REQUIREMENT: \"Requirement\",\n      FUNCTIONAL_REQUIREMENT: \"Functional Requirement\",\n      INTERFACE_REQUIREMENT: \"Interface Requirement\",\n      PERFORMANCE_REQUIREMENT: \"Performance Requirement\",\n      PHYSICAL_REQUIREMENT: \"Physical Requirement\",\n      DESIGN_CONSTRAINT: \"Design Constraint\"\n    };\n    this.RiskLevel = {\n      LOW_RISK: \"Low\",\n      MED_RISK: \"Medium\",\n      HIGH_RISK: \"High\"\n    };\n    this.VerifyType = {\n      VERIFY_ANALYSIS: \"Analysis\",\n      VERIFY_DEMONSTRATION: \"Demonstration\",\n      VERIFY_INSPECTION: \"Inspection\",\n      VERIFY_TEST: \"Test\"\n    };\n    this.Relationships = {\n      CONTAINS: \"contains\",\n      COPIES: \"copies\",\n      DERIVES: \"derives\",\n      SATISFIES: \"satisfies\",\n      VERIFIES: \"verifies\",\n      REFINES: \"refines\",\n      TRACES: \"traces\"\n    };\n    this.setAccTitle = setAccTitle;\n    this.getAccTitle = getAccTitle;\n    this.setAccDescription = setAccDescription;\n    this.getAccDescription = getAccDescription;\n    this.setDiagramTitle = setDiagramTitle;\n    this.getDiagramTitle = getDiagramTitle;\n    this.getConfig = /* @__PURE__ */ __name(() => getConfig().requirement, \"getConfig\");\n    this.clear();\n    this.setDirection = this.setDirection.bind(this);\n    this.addRequirement = this.addRequirement.bind(this);\n    this.setNewReqId = this.setNewReqId.bind(this);\n    this.setNewReqRisk = this.setNewReqRisk.bind(this);\n    this.setNewReqText = this.setNewReqText.bind(this);\n    this.setNewReqVerifyMethod = this.setNewReqVerifyMethod.bind(this);\n    this.addElement = this.addElement.bind(this);\n    this.setNewElementType = this.setNewElementType.bind(this);\n    this.setNewElementDocRef = this.setNewElementDocRef.bind(this);\n    this.addRelationship = this.addRelationship.bind(this);\n    this.setCssStyle = this.setCssStyle.bind(this);\n    this.setClass = this.setClass.bind(this);\n    this.defineClass = this.defineClass.bind(this);\n    this.setAccTitle = this.setAccTitle.bind(this);\n    this.setAccDescription = this.setAccDescription.bind(this);\n  }\n  static {\n    __name(this, \"RequirementDB\");\n  }\n  getDirection() {\n    return this.direction;\n  }\n  setDirection(dir) {\n    this.direction = dir;\n  }\n  resetLatestRequirement() {\n    this.latestRequirement = this.getInitialRequirement();\n  }\n  resetLatestElement() {\n    this.latestElement = this.getInitialElement();\n  }\n  getInitialRequirement() {\n    return {\n      requirementId: \"\",\n      text: \"\",\n      risk: \"\",\n      verifyMethod: \"\",\n      name: \"\",\n      type: \"\",\n      cssStyles: [],\n      classes: [\"default\"]\n    };\n  }\n  getInitialElement() {\n    return {\n      name: \"\",\n      type: \"\",\n      docRef: \"\",\n      cssStyles: [],\n      classes: [\"default\"]\n    };\n  }\n  addRequirement(name, type) {\n    if (!this.requirements.has(name)) {\n      this.requirements.set(name, {\n        name,\n        type,\n        requirementId: this.latestRequirement.requirementId,\n        text: this.latestRequirement.text,\n        risk: this.latestRequirement.risk,\n        verifyMethod: this.latestRequirement.verifyMethod,\n        cssStyles: [],\n        classes: [\"default\"]\n      });\n    }\n    this.resetLatestRequirement();\n    return this.requirements.get(name);\n  }\n  getRequirements() {\n    return this.requirements;\n  }\n  setNewReqId(id) {\n    if (this.latestRequirement !== void 0) {\n      this.latestRequirement.requirementId = id;\n    }\n  }\n  setNewReqText(text) {\n    if (this.latestRequirement !== void 0) {\n      this.latestRequirement.text = text;\n    }\n  }\n  setNewReqRisk(risk) {\n    if (this.latestRequirement !== void 0) {\n      this.latestRequirement.risk = risk;\n    }\n  }\n  setNewReqVerifyMethod(verifyMethod) {\n    if (this.latestRequirement !== void 0) {\n      this.latestRequirement.verifyMethod = verifyMethod;\n    }\n  }\n  addElement(name) {\n    if (!this.elements.has(name)) {\n      this.elements.set(name, {\n        name,\n        type: this.latestElement.type,\n        docRef: this.latestElement.docRef,\n        cssStyles: [],\n        classes: [\"default\"]\n      });\n      log.info(\"Added new element: \", name);\n    }\n    this.resetLatestElement();\n    return this.elements.get(name);\n  }\n  getElements() {\n    return this.elements;\n  }\n  setNewElementType(type) {\n    if (this.latestElement !== void 0) {\n      this.latestElement.type = type;\n    }\n  }\n  setNewElementDocRef(docRef) {\n    if (this.latestElement !== void 0) {\n      this.latestElement.docRef = docRef;\n    }\n  }\n  addRelationship(type, src, dst) {\n    this.relations.push({\n      type,\n      src,\n      dst\n    });\n  }\n  getRelationships() {\n    return this.relations;\n  }\n  clear() {\n    this.relations = [];\n    this.resetLatestRequirement();\n    this.requirements = /* @__PURE__ */ new Map();\n    this.resetLatestElement();\n    this.elements = /* @__PURE__ */ new Map();\n    this.classes = /* @__PURE__ */ new Map();\n    clear();\n  }\n  setCssStyle(ids, styles) {\n    for (const id of ids) {\n      const node = this.requirements.get(id) ?? this.elements.get(id);\n      if (!styles || !node) {\n        return;\n      }\n      for (const s of styles) {\n        if (s.includes(\",\")) {\n          node.cssStyles.push(...s.split(\",\"));\n        } else {\n          node.cssStyles.push(s);\n        }\n      }\n    }\n  }\n  setClass(ids, classNames) {\n    for (const id of ids) {\n      const node = this.requirements.get(id) ?? this.elements.get(id);\n      if (node) {\n        for (const _class of classNames) {\n          node.classes.push(_class);\n          const styles = this.classes.get(_class)?.styles;\n          if (styles) {\n            node.cssStyles.push(...styles);\n          }\n        }\n      }\n    }\n  }\n  defineClass(ids, style) {\n    for (const id of ids) {\n      let styleClass = this.classes.get(id);\n      if (styleClass === void 0) {\n        styleClass = { id, styles: [], textStyles: [] };\n        this.classes.set(id, styleClass);\n      }\n      if (style) {\n        style.forEach(function(s) {\n          if (/color/.exec(s)) {\n            const newStyle = s.replace(\"fill\", \"bgFill\");\n            styleClass.textStyles.push(newStyle);\n          }\n          styleClass.styles.push(s);\n        });\n      }\n      this.requirements.forEach((value) => {\n        if (value.classes.includes(id)) {\n          value.cssStyles.push(...style.flatMap((s) => s.split(\",\")));\n        }\n      });\n      this.elements.forEach((value) => {\n        if (value.classes.includes(id)) {\n          value.cssStyles.push(...style.flatMap((s) => s.split(\",\")));\n        }\n      });\n    }\n  }\n  getClasses() {\n    return this.classes;\n  }\n  getData() {\n    const config = getConfig();\n    const nodes = [];\n    const edges = [];\n    for (const requirement of this.requirements.values()) {\n      const node = requirement;\n      node.id = requirement.name;\n      node.cssStyles = requirement.cssStyles;\n      node.cssClasses = requirement.classes.join(\" \");\n      node.shape = \"requirementBox\";\n      node.look = config.look;\n      nodes.push(node);\n    }\n    for (const element of this.elements.values()) {\n      const node = element;\n      node.shape = \"requirementBox\";\n      node.look = config.look;\n      node.id = element.name;\n      node.cssStyles = element.cssStyles;\n      node.cssClasses = element.classes.join(\" \");\n      nodes.push(node);\n    }\n    for (const relation of this.relations) {\n      let counter = 0;\n      const isContains = relation.type === this.Relationships.CONTAINS;\n      const edge = {\n        id: `${relation.src}-${relation.dst}-${counter}`,\n        start: this.requirements.get(relation.src)?.name ?? this.elements.get(relation.src)?.name,\n        end: this.requirements.get(relation.dst)?.name ?? this.elements.get(relation.dst)?.name,\n        label: `&lt;&lt;${relation.type}&gt;&gt;`,\n        classes: \"relationshipLine\",\n        style: [\"fill:none\", isContains ? \"\" : \"stroke-dasharray: 10,7\"],\n        labelpos: \"c\",\n        thickness: \"normal\",\n        type: \"normal\",\n        pattern: isContains ? \"normal\" : \"dashed\",\n        arrowTypeStart: isContains ? \"requirement_contains\" : \"\",\n        arrowTypeEnd: isContains ? \"\" : \"requirement_arrow\",\n        look: config.look\n      };\n      edges.push(edge);\n      counter++;\n    }\n    return { nodes, edges, other: {}, config, direction: this.getDirection() };\n  }\n};\n\n// src/diagrams/requirement/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `\n\n  marker {\n    fill: ${options.relationColor};\n    stroke: ${options.relationColor};\n  }\n\n  marker.cross {\n    stroke: ${options.lineColor};\n  }\n\n  svg {\n    font-family: ${options.fontFamily};\n    font-size: ${options.fontSize};\n  }\n\n  .reqBox {\n    fill: ${options.requirementBackground};\n    fill-opacity: 1.0;\n    stroke: ${options.requirementBorderColor};\n    stroke-width: ${options.requirementBorderSize};\n  }\n  \n  .reqTitle, .reqLabel{\n    fill:  ${options.requirementTextColor};\n  }\n  .reqLabelBox {\n    fill: ${options.relationLabelBackground};\n    fill-opacity: 1.0;\n  }\n\n  .req-title-line {\n    stroke: ${options.requirementBorderColor};\n    stroke-width: ${options.requirementBorderSize};\n  }\n  .relationshipLine {\n    stroke: ${options.relationColor};\n    stroke-width: 1;\n  }\n  .relationshipLabel {\n    fill: ${options.relationLabelColor};\n  }\n  .divider {\n    stroke: ${options.nodeBorder};\n    stroke-width: 1;\n  }\n  .label {\n    font-family: ${options.fontFamily};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n  .label text,span {\n    fill: ${options.nodeTextColor || options.textColor};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n  .labelBkg {\n    background-color: ${options.edgeLabelBackground};\n  }\n\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/requirement/requirementRenderer.ts\nvar requirementRenderer_exports = {};\n__export(requirementRenderer_exports, {\n  draw: () => draw\n});\nvar draw = /* @__PURE__ */ __name(async function(text, id, _version, diag) {\n  log.info(\"REF0:\");\n  log.info(\"Drawing requirement diagram (unified)\", id);\n  const { securityLevel, state: conf, layout } = getConfig();\n  const data4Layout = diag.db.getData();\n  const svg = getDiagramElement(id, securityLevel);\n  data4Layout.type = diag.type;\n  data4Layout.layoutAlgorithm = getRegisteredLayoutAlgorithm(layout);\n  data4Layout.nodeSpacing = conf?.nodeSpacing ?? 50;\n  data4Layout.rankSpacing = conf?.rankSpacing ?? 50;\n  data4Layout.markers = [\"requirement_contains\", \"requirement_arrow\"];\n  data4Layout.diagramId = id;\n  await render(data4Layout, svg);\n  const padding = 8;\n  utils_default.insertTitle(\n    svg,\n    \"requirementDiagramTitleText\",\n    conf?.titleTopMargin ?? 25,\n    diag.db.getDiagramTitle()\n  );\n  setupViewPortForSVG(svg, padding, \"requirementDiagram\", conf?.useMaxWidth ?? true);\n}, \"draw\");\n\n// src/diagrams/requirement/requirementDiagram.ts\nvar diagram = {\n  parser: requirementDiagram_default,\n  get db() {\n    return new RequirementDB();\n  },\n  renderer: requirementRenderer_exports,\n  styles: styles_default\n};\nexport {\n  diagram\n};\n"], "names": [], "sourceRoot": ""}