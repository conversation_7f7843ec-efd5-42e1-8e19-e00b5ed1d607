"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[9330],{69330:(e,t,r)=>{r.r(t),r.d(t,{default:()=>a});var n=r(66845),u=r(40864);function a(e){let{element:t,width:r,endpoints:a,elementMgr:s}=e;const i=(0,n.useRef)(null),{startTime:d,endTime:c,loop:l,autoplay:o}=t,m=(0,n.useMemo)((()=>{if(!t.id)return!0;const e=s.getElementState(t.id,"preventAutoplay");return e||s.setElementState(t.id,"preventAutoplay",!0),null!==e&&void 0!==e&&e}),[t.id,s]);(0,n.useEffect)((()=>{i.current&&(i.current.currentTime=d)}),[d]),(0,n.useEffect)((()=>{const e=i.current,r=()=>{e&&(e.currentTime=t.startTime)};return e&&e.addEventListener("loadedmetadata",r),()=>{e&&e.removeEventListener("loadedmetadata",r)}}),[t]),(0,n.useEffect)((()=>{const e=i.current;if(!e)return;let t=!1;const r=()=>{c>0&&e.currentTime>=c&&(l?(e.currentTime=d||0,e.play()):t||(t=!0,e.pause()))};return c>0&&e.addEventListener("timeupdate",r),()=>{e&&c>0&&e.removeEventListener("timeupdate",r)}}),[c,l,d]),(0,n.useEffect)((()=>{const e=i.current;if(!e)return;const t=()=>{l&&(e.currentTime=d||0,e.play())};return e.addEventListener("ended",t),()=>{e&&e.removeEventListener("ended",t)}}),[l,d]);const p=a.buildMediaURL(t.url);return(0,u.jsx)("audio",{"data-testid":"stAudio",id:"audio",ref:i,controls:!0,autoPlay:o&&!m,src:p,className:"stAudio",style:{width:r}})}}}]);