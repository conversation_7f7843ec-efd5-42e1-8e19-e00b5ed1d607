# 🚀 OpenBB API 完整使用说明

## 📋 概述

您现在拥有一个完整的OpenBB API生态系统，包含：

1. **🇺🇸 美股API服务器** (端口6900) - OpenBB官方API
2. **🇨🇳 A股API服务器** (端口6901) - 集成Tushare Pro/AKShare/东方财富
3. **🆓 免费API服务器** (端口6902) - 补充缺失功能的免费API
4. **📱 Streamlit客户端** (端口8502) - 统一的Web界面

## 🌐 API服务器状态

### 1. 美股API服务器 (端口6900)
- **状态**: ✅ 运行中
- **地址**: http://127.0.0.1:6900
- **文档**: http://127.0.0.1:6900/docs
- **功能**: OpenBB官方API，支持美股数据

### 2. A股API服务器 (端口6901)
- **状态**: ✅ 运行中
- **地址**: http://127.0.0.1:6901
- **文档**: http://127.0.0.1:6901/docs
- **功能**: A股专用API，多数据源集成

### 3. 免费API服务器 (端口6902)
- **状态**: ✅ 运行中
- **地址**: http://127.0.0.1:6902
- **文档**: http://127.0.0.1:6902/docs
- **功能**: 补充缺失的API功能

### 4. Streamlit客户端 (端口8502)
- **状态**: ✅ 运行中
- **地址**: http://localhost:8502
- **功能**: 统一的Web界面

## 📊 可用的API端点

### 🇺🇸 美股API (端口6900)
```
GET /api/v1/equity/price/quote              # 美股实时报价
GET /api/v1/equity/fundamental/income       # 美股财务报表
```

### 🇨🇳 A股API (端口6901)
```
GET /api/v1/astock/equity/price/historical  # A股历史数据
GET /api/v1/astock/equity/price/quote       # A股实时报价
GET /api/v1/astock/market/indices           # A股市场指数
GET /api/v1/astock/equity/fundamental/income # A股财务报表
GET /api/v1/astock/providers                # 可用数据源
```

### 🆓 免费API (端口6902)
```
GET /api/v1/equity/price/historical         # 股票历史数据 (Yahoo Finance)
GET /api/v1/equity/price/quote              # 股票实时报价 (Yahoo Finance)
GET /api/v1/equity/fundamental/income       # 财务报表-利润表 (Alpha Vantage)
GET /api/v1/equity/fundamental/balance      # 财务报表-资产负债表 (Alpha Vantage)
GET /api/v1/equity/fundamental/cash         # 财务报表-现金流量表 (Alpha Vantage)
GET /api/v1/news/company                    # 公司新闻 (NewsAPI)
GET /api/v1/economy/gdp                     # 经济数据-GDP (FRED)
GET /api/v1/crypto/price/historical         # 加密货币历史数据 (Yahoo Finance)
GET /api/v1/technical/sma                   # 技术指标-移动平均线 (Alpha Vantage)
```

## 🔧 API调用示例

### 1. 获取美股报价
```bash
curl "http://127.0.0.1:6902/api/v1/equity/price/quote?symbol=AAPL"
```

### 2. 获取A股历史数据
```bash
curl "http://127.0.0.1:6901/api/v1/astock/equity/price/historical?symbol=000001&start_date=2024-01-01&end_date=2024-12-31"
```

### 3. 获取A股实时报价
```bash
curl "http://127.0.0.1:6901/api/v1/astock/equity/price/quote?symbol=000001,600519"
```

### 4. 获取公司新闻
```bash
curl "http://127.0.0.1:6902/api/v1/news/company?symbol=AAPL&limit=5"
```

### 5. 获取GDP数据
```bash
curl "http://127.0.0.1:6902/api/v1/economy/gdp?country=US&start_date=2020-01-01"
```

## 🔑 API密钥配置

### 免费API密钥获取

1. **Alpha Vantage** (推荐)
   - 网址: https://www.alphavantage.co/support/#api-key
   - 免费额度: 25次/天
   - 支持: 股票、财务、技术指标

2. **NewsAPI**
   - 网址: https://newsapi.org/
   - 免费额度: 1000次/月
   - 支持: 新闻数据

3. **FRED (Federal Reserve Economic Data)**
   - 网址: https://fred.stlouisfed.org/docs/api/api_key.html
   - 免费额度: 无限制
   - 支持: 经济数据

4. **Finnhub**
   - 网址: https://finnhub.io/
   - 免费额度: 60次/分钟
   - 支持: 股票、财务数据

### 配置密钥

编辑 `openbb_free_api_server.py` 文件中的 `API_KEYS` 字典：

```python
API_KEYS = {
    "alpha_vantage": "YOUR_ALPHA_VANTAGE_KEY",
    "finnhub": "YOUR_FINNHUB_KEY", 
    "newsapi": "YOUR_NEWSAPI_KEY",
    "fred": "YOUR_FRED_KEY"
}
```

## 🚀 启动服务

### 方法1: 使用批处理文件
```bash
# 启动A股API服务器
启动A股API服务器.bat

# 启动免费API服务器  
启动免费API服务器.bat

# 启动美股API服务器
启动API服务器.bat

# 启动Streamlit客户端
启动客户端.bat
```

### 方法2: 手动启动
```bash
# 激活环境
call openbb_env_312\Scripts\activate.bat

# 启动各个服务器
python openbb_astock_api.py --port 6901
python openbb_free_api_server.py
python -m streamlit run OpenBB完整客户端.py --server.port 8502
```

## 📱 客户端使用

1. 打开浏览器访问: http://localhost:8502
2. 在侧边栏选择市场: 🇺🇸 美股 / 🇨🇳 A股 / 🌍 全球市场
3. 选择功能模块: 股票分析、投资组合、新闻资讯等
4. 输入股票代码进行分析

### 股票代码格式
- **美股**: AAPL, MSFT, GOOGL
- **A股**: 000001, 600519, 000858 (6位数字)

## 🔍 故障排除

### 1. API连接失败
- 检查服务器是否启动
- 确认端口没有被占用
- 检查防火墙设置

### 2. 数据获取失败
- 检查网络连接
- 验证API密钥是否正确
- 确认API额度是否用完

### 3. A股数据包安装问题
```bash
# 重新安装A股数据包
pip install tushare akshare efinance
```

### 4. SSL证书问题
```bash
# 更新证书
pip install --upgrade certifi
```

## 📈 数据源优先级

### A股数据源
1. **Tushare Pro** (优先) - 专业数据，需要token
2. **AKShare** (备选) - 开源免费
3. **东方财富** (备选) - 实时行情

### 美股数据源
1. **Yahoo Finance** (优先) - 免费，无需密钥
2. **Alpha Vantage** (备选) - 专业数据
3. **Finnhub** (备选) - 实时数据

## 🎯 扩展功能

### 添加新的数据源
1. 在相应的API服务器中添加新的获取函数
2. 更新路由处理
3. 重启服务器

### 自定义指标
1. 在免费API服务器中添加计算函数
2. 创建新的API端点
3. 在客户端中集成显示

## 📞 技术支持

如果遇到问题，请检查：
1. 所有服务器是否正常启动
2. API密钥是否正确配置
3. 网络连接是否正常
4. 日志输出中的错误信息

---

**🎉 恭喜！您现在拥有一个功能完整的OpenBB API生态系统！**
