"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[9865],{19865:(e,t,i)=>{i.r(t),i.d(t,{default:()=>u});var r=i(66845),n=i(17664),o=i(84256),a=i(3263),l=i(67930);const d=(0,i(74559).z)("div")({name:"DataGridOverlayEditorStyle",class:"gdg-d19meir1",propsAsIs:!1,vars:{"d19meir1-0":[e=>e.targetY,"px"],"d19meir1-1":[e=>e.targetX,"px"],"d19meir1-2":[e=>e.targetWidth,"px"],"d19meir1-3":[e=>e.targetHeight,"px"],"d19meir1-4":[e=>e.targetY+10,"px"],"d19meir1-5":[e=>Math.max(0,(e.targetHeight-28)/2),"px"]}});function s(){const[e,t]=function(){const[e,t]=r.useState();return[null!==e&&void 0!==e?e:void 0,t]}(),[i,n]=r.useState(0),[o,a]=r.useState(!0);r.useLayoutEffect((()=>{if(void 0===e)return;if(!("IntersectionObserver"in window))return;const t=new IntersectionObserver((e=>{0!==e.length&&a(e[0].isIntersecting)}),{threshold:1});return t.observe(e),()=>t.disconnect()}),[e]),r.useEffect((()=>{if(o||void 0===e)return;let t;const i=()=>{const{right:r}=e.getBoundingClientRect();n((e=>Math.min(e+window.innerWidth-r-10,0))),t=requestAnimationFrame(i)};return t=requestAnimationFrame(i),()=>{void 0!==t&&cancelAnimationFrame(t)}}),[e,o]);return{ref:t,style:r.useMemo((()=>({transform:"translateX(".concat(i,"px)")})),[i])}}const u=e=>{var t,i;const{target:u,content:c,onFinishEditing:v,forceEditMode:g,initialValue:m,imageEditorOverride:p,markdownDivCreateNode:f,highlight:h,className:y,theme:b,id:E,cell:k,bloom:C,validateCell:w,getCellRenderer:x,provideEditor:D,isOutsideClick:O}=e,[P,S]=r.useState(g?c:void 0),N=r.useRef(null!==P&&void 0!==P?P:c);N.current=null!==P&&void 0!==P?P:c;const[M,R]=r.useState((()=>void 0===w||!((0,l.T9)(c)&&!1===(null===w||void 0===w?void 0:w(k,c,N.current))))),F=r.useCallback(((e,t)=>{v(M?e:void 0,t)}),[M,v]),I=r.useCallback((e=>{if(void 0!==w&&void 0!==e&&(0,l.T9)(e)){const t=w(k,e,N.current);!1===t?R(!1):"object"===typeof t?(e=t,R(!0)):R(!0)}S(e)}),[k,w]),T=r.useRef(!1),A=r.useRef(void 0),H=r.useCallback((()=>{F(P,[0,0]),T.current=!0}),[P,F]),_=r.useCallback(((e,t)=>{var i;F(e,null!==(i=null!==t&&void 0!==t?t:A.current)&&void 0!==i?i:[0,0]),T.current=!0}),[F]),K=r.useCallback((async e=>{let t=!1;"Escape"===e.key?(e.stopPropagation(),e.preventDefault(),A.current=[0,0]):"Enter"!==e.key||e.shiftKey?"Tab"===e.key&&(e.stopPropagation(),e.preventDefault(),A.current=[e.shiftKey?-1:1,0],t=!0):(e.stopPropagation(),e.preventDefault(),A.current=[0,1],t=!0),window.setTimeout((()=>{T.current||void 0===A.current||(F(t?P:void 0,A.current),T.current=!0)}),0)}),[F,P]),V=null!==P&&void 0!==P?P:c,[W,X]=r.useMemo((()=>{var e,t;if((0,l.rs)(c))return[];const i=null===D||void 0===D?void 0:D(c);return void 0!==i?[i,!1]:[null===(e=x(c))||void 0===e||null===(t=e.provideEditor)||void 0===t?void 0:t.call(e,c),!1]}),[c,x,D]),{ref:Y,style:q}=s();let B,G,j=!0,z=!0;if(void 0!==W){j=!0!==W.disablePadding,z=!0!==W.disableStyling;const e=(0,l.DP)(W);e&&(G=W.styleOverride);const t=e?W.editor:W;B=r.createElement(t,{isHighlighted:h,onChange:I,value:V,initialValue:m,onFinishedEditing:_,validatedSelection:(0,l.T9)(V)?V.selectionRange:void 0,forceEditMode:g,target:u,imageEditorOverride:p,markdownDivCreateNode:f,isValid:M,theme:b})}G={...G,...q};const L=document.getElementById("portal");if(null===L)return console.error('Cannot open Data Grid overlay editor, because portal not found.  Please add `<div id="portal" />` as the last child of your `<body>`.'),null;let Z=z?"gdg-style":"gdg-unstyle";M||(Z+=" gdg-invalid"),j&&(Z+=" gdg-pad");const J=null!==(t=null===C||void 0===C?void 0:C[0])&&void 0!==t?t:1,Q=null!==(i=null===C||void 0===C?void 0:C[1])&&void 0!==i?i:1;return(0,n.createPortal)(r.createElement(a.Ni.Provider,{value:b},r.createElement(o.Z,{style:(0,a.be)(b),className:y,onClickOutside:H,isOutsideClick:O},r.createElement(d,{ref:Y,id:E,className:Z,style:G,as:!0===X?"label":void 0,targetX:u.x-J,targetY:u.y-Q,targetWidth:u.width+2*J,targetHeight:u.height+2*Q},r.createElement("div",{className:"gdg-clip-region",onKeyDown:K},B)))),L)}}}]);