{"version": 3, "file": "2425.6472013fa9798ece0dc9.js?v=6472013fa9798ece0dc9", "mappings": ";;;;;AAAA;AACA,IAAI,IAAyD;AAC7D,2BAA2B,mBAAO,CAAC,KAAW;AAC9C,MAAM,EAK+C;AACrD,CAAC;AACD,yBAAyB;AACzB;AACA;;AAEA;AACA;;;;AAIA;;AAEA;AACA,qFAAqF,aAAa;AAClG;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;;AAEA,OAAO;;AAEP;AACA,0CAA0C,+BAAmB;;;;AAI7D,mCAAmC,iCAAiC,eAAe,eAAe,gBAAgB,oBAAoB,MAAM,0CAA0C,+BAA+B,aAAa,qBAAqB,uCAAuC,cAAc,WAAW,YAAY,UAAU,MAAM,2CAA2C,UAAU,sBAAsB,eAAe,2BAA2B,0BAA0B,cAAc,2CAA2C,gCAAgC,OAAO,mFAAmF;;AAErpB;AACA;AACA;;AAEA,iBAAiB,+BAAmB;;AAEpC;;AAEA;AACA;AACA;AACA,kBAAkB,kBAAkB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP,sBAAsB,0BAA0B;AAChD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,IAAI;;AAEJ;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,oFAAoF,gEAAgE;AACpJ;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,kBAAkB,OAAO;AACzB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;;AAEA,OAAO;;AAEP;AACA,0CAA0C,gCAAmB;;;;AAI7D;AACA;AACA;;AAEA,UAAU,gCAAmB;AAC7B,iBAAiB,gCAAmB;AACpC,eAAe,gCAAmB;AAClC,aAAa,gCAAmB;AAChC,iBAAiB,gCAAmB;AACpC,sBAAsB,gCAAmB;AACzC,wBAAwB,gCAAmB;AAC3C,oBAAoB,gCAAmB;;AAEvC;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA,oBAAoB,UAAU;AAC9B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,oDAAoD,kEAAkE;AACtH,qDAAqD,kEAAkE;AACvH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,oBAAoB,kBAAkB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kIAAkI;AAClI,4GAA4G;AAC5G;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,8DAA8D;;AAE9D;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iFAAiF;AACjF;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA,mBAAmB;;AAEnB,OAAO;;AAEP;AACA,0CAA0C,gCAAmB;;;;AAI7D,iCAAiC,2CAA2C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD,+DAA+D,yDAAyD,qEAAqE,6DAA6D,wBAAwB;;AAEjjB,kDAAkD,0CAA0C;;AAE5F;AACA;AACA;;AAEA,aAAa,gCAAmB;AAChC,UAAU,gCAAmB;;AAE7B,eAAe,gCAAmB;AAClC;;AAEA,gBAAgB,gCAAmB;AACnC;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,OAAO,yBAAyB,eAAe,GAAG,IAAI;AACtD;AACA;AACA,MAAM;AACN;AACA;AACA,OAAO,kCAAkC,GAAG,iCAAiC;AAC7E;;AAEA;AACA,4BAA4B;AAC5B,2BAA2B;AAC3B,CAAC;;AAED;AACA;AACA;;AAEA,4BAA4B;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,kCAAkC,8EAA8E;AAChH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gFAAgF;AAChF,YAAY;AACZ,oFAAoF;AACpF;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,8EAA8E;AAClH,WAAW;;AAEX;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA,eAAe;AACf;AACA;AACA,wCAAwC,kFAAkF;AAC1H;AACA;AACA,4DAA4D,QAAQ;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0FAA0F;AAC1F,aAAa;AACb,YAAY;AACZ;AACA,8FAA8F;AAC9F,aAAa;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,2MAA2M;AACvP,sBAAsB;AACtB;AACA,4CAA4C,oGAAoG;AAChJ;AACA,oBAAoB;AACpB;AACA,4CAA4C,uLAAuL;AACnO;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA,4CAA4C,gGAAgG;AAC5I,sBAAsB;AACtB;AACA,8CAA8C,gNAAgN;AAC9P;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA,eAAe;AACf,cAAc;AACd;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,eAAe;AACf;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,WAAW;AACX,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;;AAEA;AACA;AACA;AACA,WAAW;AACX;AACA,QAAQ;AACR;AACA;AACA;AACA,GAAG;;AAEH;AACA,CAAC;;AAED;;AAEA,OAAO;;AAEP;AACA,0CAA0C,gCAAmB;;;;AAI7D;AACA;AACA;;AAEA,UAAU,gCAAmB;AAC7B,aAAa,gCAAmB;AAChC,UAAU,gCAAmB;;AAE7B;AACA;;AAEA;AACA;AACA;AACA;;AAEA,8BAA8B;AAC9B,+BAA+B;AAC/B,kCAAkC;AAClC,iCAAiC;AACjC;AACA;;AAEA,0BAA0B;AAC1B;AACA,cAAc;AACd,gBAAgB;AAChB,gBAAgB;;AAEhB,4BAA4B;AAC5B;;AAEA;AACA;;AAEA;AACA,2CAA2C;AAC3C;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,sBAAsB,WAAW;AACjC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;;AAEA;AACA;AACA,mBAAmB;AACnB,mBAAmB;AACnB;AACA;AACA;AACA;;AAEA,sBAAsB;AACtB;;AAEA,oBAAoB,cAAc;AAClC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,uBAAuB,uBAAuB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,wBAAwB,gBAAgB;AACxC;AACA;;AAEA,wBAAwB,gBAAgB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA,sBAAsB,gBAAgB;AACtC;AACA;AACA,MAAM;AACN;AACA;;AAEA,wBAAwB,gBAAgB;AACxC;AACA;;AAEA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;;AAEA;AACA,sBAAsB,gBAAgB;AACtC,sBAAsB,gBAAgB;AACtC;AACA;AACA;;AAEA;AACA,sBAAsB,kBAAkB;AACxC;AACA;;AAEA,sBAAsB,kBAAkB;AACxC,uBAAuB,iBAAiB;AACxC;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA,oBAAoB,gBAAgB;AACpC;AACA,sBAAsB,gBAAgB;AACtC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,oBAAoB,cAAc;AAClC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA,wBAAwB,gBAAgB;AACxC;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,uBAAuB,iBAAiB;AACxC;AACA;;AAEA;AACA;AACA;AACA;;AAEA,yBAAyB,iBAAiB;AAC1C;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,uBAAuB,iBAAiB;AACxC;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA,kBAAkB,kBAAkB;AACpC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,sEAAsE,gEAAgE;AACtI;;AAEA;AACA;;AAEA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA,qBAAqB,yBAAyB;AAC9C;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,8EAA8E;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;;AAEH;AACA;AACA;;AAEA,iFAAiF;;AAEjF;AACA;AACA,gGAAgG;AAChG;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA;AACA,oFAAoF;;AAEpF;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;;AAEA;AACA,wEAAwE,mEAAmE;AAC3I;;AAEA;AACA;;AAEA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,uBAAuB,iBAAiB;AACxC;AACA;AACA,uBAAuB,mBAAmB;AAC1C;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA,yBAAyB;AACzB,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP,yBAAyB;AACzB;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,uBAAuB;AACvB;AACA;AACA;;AAEA,mBAAmB;;AAEnB,OAAO;;AAEP;AACA,0CAA0C,gCAAmB;;;;AAI7D,WAAW,gCAAmB;;AAE9B;AACA;AACA;AACA;AACA,IAAI;;AAEJ,sCAAsC;AACtC;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;;AAEP;AACA;;AAEA;;AAEA,OAAO;;AAEP,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,mBAAmB,gCAAmB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAiE,gCAAmB;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,0BAAmB,GAAG,gCAAmB;AACvD;AACA,iBAAiB,0BAAmB;AACpC,UAAU;AACV;AACA,CAAC;;;;;;;AC5gDD;AACA,IAAI,IAAyD;AAC7D,2BAA2B,mBAAO,CAAC,KAAa;AAChD,MAAM,EAK2C;AACjD,CAAC;AACD,yBAAyB;AACzB;AACA;;AAEA;AACA,0CAA0C,8BAAmB;;;;AAI7D;;AAEA,sBAAsB,8BAAmB;AACzC,yBAAyB,8BAAmB;AAC5C,oBAAoB,8BAAmB;AACvC,qBAAqB,8BAAmB;AACxC,4BAA4B,8BAAmB;AAC/C,sBAAsB,8BAAmB;AACzC,oBAAoB,8BAAmB;AACvC,6BAA6B,8BAAmB;;AAEhD;;AAEA,OAAO;;AAEP;AACA,0CAA0C,+BAAmB;;;;AAI7D,wBAAwB,+BAAmB;;AAE3C;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD;AACpD;AACA;AACA;;AAEA;;AAEA,OAAO;;AAEP;AACA,0CAA0C,+BAAmB;;;;AAI7D,mBAAmB,+BAAmB;;AAEtC;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;;AAEP;AACA,0CAA0C,+BAAmB;;;;AAI7D,aAAa,+BAAmB;;AAEhC;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;;AAEP;AACA,0CAA0C,+BAAmB;;;;AAI7D,oBAAoB,+BAAmB;;AAEvC;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;;AAEP;AACA,0CAA0C,+BAAmB;;;;AAI7D,eAAe,+BAAmB;AAClC,uBAAuB,+BAAmB;AAC1C,gBAAgB,+BAAmB;AACnC,eAAe,+BAAmB;AAClC,eAAe,+BAAmB;AAClC,oBAAoB,+BAAmB;AACvC,wBAAwB,+BAAmB;AAC3C,wBAAwB,+BAAmB;AAC3C,sBAAsB,+BAAmB;AACzC,YAAY,+BAAmB;AAC/B,aAAa,+BAAmB;AAChC,iBAAiB,+BAAmB;AACpC,aAAa,+BAAmB;AAChC,cAAc,+BAAmB;AACjC,gBAAgB,+BAAmB;AACnC,aAAa,+BAAmB;AAChC,gBAAgB,+BAAmB;AACnC,iBAAiB,+BAAmB;;AAEpC;AACA;;AAEA,uBAAuB;AACvB,yBAAyB;AACzB;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA,0HAA0H;AAC1H,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mKAAmK;AACnK;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,gBAAgB;AAChB;;AAEA;AACA;AACA;AACA,kBAAkB,qBAAqB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB,mBAAmB;AACrC;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,kBAAkB,mBAAmB;AACrC;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,kBAAkB,qBAAqB;AACvC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,oBAAoB,kBAAkB;AACtC;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA,oBAAoB,qBAAqB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA,wBAAwB,8BAA8B;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,wBAAwB,gCAAgC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;;AAEA;;AAEA;AACA;AACA,mCAAmC,2BAA2B;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;;AAEA,4EAA4E,yCAAyC;AACrH,6EAA6E,uCAAuC;AACpH,UAAU;AACV;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,yEAAyE,2CAA2C;AACpH,4EAA4E,qCAAqC;AACjH;AACA,OAAO;AACP,MAAM;AACN,4CAA4C;AAC5C,0CAA0C;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA,SAAS;;AAET,iBAAiB;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA,sBAAsB,kCAAkC;AACxD;AACA,wBAAwB,qCAAqC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,qCAAqC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,oCAAoC;AAC1D;AACA,wBAAwB,uCAAuC;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,uCAAuC;AAC/D;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,aAAa;AACb,YAAY;AACZ;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,aAAa;AACb,YAAY;AACZ;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN,sBAAsB,wCAAwC;AAC9D;AACA;AACA,0BAA0B,sBAAsB;AAChD;AACA;AACA;AACA,eAAe;AACf,cAAc;AACd;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,0BAA0B,sBAAsB;AAChD;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA,0BAA0B,sBAAsB;AAChD;AACA;AACA;AACA,eAAe;AACf,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA,sBAAsB,sCAAsC;AAC5D;AACA;AACA,0BAA0B,sBAAsB;AAChD;AACA;AACA;AACA,eAAe;AACf,cAAc;AACd;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,0BAA0B,sBAAsB;AAChD;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA,0BAA0B,sBAAsB;AAChD;AACA;AACA;AACA,eAAe;AACf,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,cAAc,UAAU;AACxB;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,cAAc,kBAAkB;AAChC;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;;AAEA;AACA;;AAEA;AACA;AACA;AACA,wBAAwB,qBAAqB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB,mBAAmB;AACrC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB,iBAAiB;AACnC;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;;AAEA,2BAA2B,2BAA2B;AACtD;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,kBAAkB,iBAAiB;AACnC;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,0BAA0B;AAC1B,2BAA2B;;AAE3B,uBAAuB;AACvB;;AAEA;AACA,kBAAkB,qBAAqB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB,uBAAuB;AACzC,8BAA8B;AAC9B,oCAAoC;;AAEpC;;AAEA,kEAAkE;AAClE;;AAEA;AACA;AACA;AACA,qDAAqD;AACrD,mEAAmE;;AAEnE,0DAA0D;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,sBAAsB,mCAAmC;AACzD;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,kBAAkB,+BAA+B;;AAEjD;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,+CAA+C;;AAE/C;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA,8CAA8C,QAAQ;AACtD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,+CAA+C;AAC/C;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,wCAAwC;;AAExC;AACA,kBAAkB,qBAAqB;AACvC;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB,kBAAkB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,qBAAqB;AACvC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB,qBAAqB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,kBAAkB,8BAA8B;AAChD;AACA;AACA;;AAEA,oBAAoB,gBAAgB;AACpC;;AAEA,wBAAwB;AACxB,wBAAwB;;AAExB;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,kBAAkB,kBAAkB;AACpC;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,kBAAkB,kBAAkB;AACpC;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,kBAAkB,8BAA8B;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,kBAAkB,8BAA8B;;AAEhD;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,oBAAoB,gBAAgB;AACpC;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,oBAAoB,qBAAqB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,kCAAkC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB,8BAA8B;AAChD;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,+BAA+B,kBAAkB;AACjD;AACA;AACA;AACA;AACA,+BAA+B,kBAAkB;AACjD;AACA;AACA;AACA;AACA,+BAA+B,kBAAkB;AACjD;AACA;AACA;AACA;AACA,+BAA+B,kBAAkB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,2BAA2B;AAC/C;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ;AACR;AACA,QAAQ;AACR;AACA,QAAQ;AACR;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;;AAEP;AACA,0CAA0C,gCAAmB;;;;AAI7D,mBAAmB,gCAAmB;AACtC,YAAY,gCAAmB;;AAE/B;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,kBAAkB,kBAAkB;AACpC;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,OAAO;;AAEP;AACA,0CAA0C,gCAAmB;;;;AAI7D,mCAAmC,0BAA0B,0CAA0C,gBAAgB,OAAO,oBAAoB,eAAe,OAAO;;AAExK,oBAAoB,gCAAmB;AACvC,iBAAiB,gCAAmB;AACpC,aAAa,gCAAmB;AAChC,UAAU,gCAAmB;;AAE7B;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,kBAAkB,qBAAqB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,KAAK;AACL;;AAEA;;AAEA;AACA;AACA,aAAa;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL,aAAa;AACb;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,6DAA6D,gEAAgE;AAC7H;;AAEA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,OAAO;AACP,KAAK;;AAEL,iCAAiC;AACjC,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,UAAU;AACV;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA,OAAO;AACP;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,yDAAyD;AACzD;AACA;AACA;AACA;AACA,eAAe;AACf;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,oEAAoE,mEAAmE;AACvI;;AAEA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,cAAc;AACd;AACA;AACA,eAAe;AACf;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,sEAAsE,mEAAmE;AACzI;;AAEA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA,sEAAsE,mEAAmE;AACzI;;AAEA;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,KAAK;;AAEL;AACA,uBAAuB,uBAAuB;AAC9C;AACA;AACA;AACA,MAAM;AACN,wBAAwB,wBAAwB;AAChD;AACA;AACA,MAAM;AACN,wBAAwB,wBAAwB;AAChD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA,2CAA2C,2DAA2D;AACtG,OAAO;AACP,KAAK;;AAEL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;;AAEL;AACA;AACA,yCAAyC,2DAA2D;AACpG,OAAO;AACP,KAAK;;AAEL;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,yBAAyB;AACzB,yBAAyB;AACzB,sCAAsC;AACtC,8BAA8B;AAC9B;AACA,uBAAuB;AACvB,iCAAiC;AACjC,uBAAuB;;AAEvB;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,oEAAoE;AAC9G,UAAU;AACV,sCAAsC,oEAAoE;AAC1G;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,yCAAyC,mEAAmE;AAC5G,UAAU;AACV,qCAAqC,mEAAmE;AACxG;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA,2GAA2G;;AAE3G;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA,4BAA4B,4BAA4B;AACxD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA,2GAA2G;;AAE3G;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA,4BAA4B,8BAA8B;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;AACA,wBAAwB,yBAAyB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,mDAAmD,kCAAkC;AACrF,cAAc;AACd;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,mDAAmD,kCAAkC;AACrF;AACA,WAAW;AACX,SAAS;;AAET;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,kEAAkE;AAClE,kEAAkE;;AAElE;AACA,wBAAwB,oCAAoC;AAC5D;AACA;AACA;;AAEA;AACA,uGAAuG;AACvG,2CAA2C;AAC3C,yFAAyF;;AAEzF;AACA,wBAAwB,wBAAwB;AAChD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA,gCAAgC;AAChC;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA,OAAO;;AAEP;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA,OAAO;AACP;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,WAAW;AACX;AACA,yGAAyG;;AAEzG;AACA;AACA,WAAW;AACX;;AAEA,0BAA0B,qBAAqB;AAC/C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,WAAW;AACX;AACA,yGAAyG;;AAEzG;AACA;AACA,WAAW;AACX;;AAEA,2BAA2B,sBAAsB;AACjD;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;;AAEA,+BAA+B,iCAAiC;AAChE;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;;AAEA,+BAA+B,mCAAmC;AAClE;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B,gBAAgB;AAChB;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,+BAA+B;AAC/B,gBAAgB;AAChB;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;;AAEA;AACA;AACA;;AAEA;AACA,uEAAuE,mEAAmE;AAC1I;;AAEA;AACA;;AAEA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;;AAET;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb,YAAY;AACZ;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,yFAAyF,mEAAmE;AAC5J;;AAEA;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb,YAAY;AACZ;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,uFAAuF,mEAAmE;AAC1J;;AAEA;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA,qBAAqB,wBAAwB;AAC7C;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;;AAEP;AACA;;AAEA;;AAEA,OAAO;;AAEP,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,mBAAmB,iCAAmB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAiE,iCAAmB;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,0BAAmB,GAAG,iCAAmB;AACvD;AACA,iBAAiB,0BAAmB;AACpC,UAAU;AACV;AACA,CAAC;;;;;;;AC7oGD;AACA,IAAI,IAAyD;AAC7D;AACA,MAAM,EAK2B;AACjC,CAAC;AACD,qCAAqC;AACrC;AACA;AACA;AACA;AACA,mBAAmB,8BAAmB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0EAA0E,8BAAmB;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,8BAAmB;AAC7B;AACA;AACA,UAAU,8BAAmB;AAC7B;AACA;AACA,UAAU,8BAAmB,uBAAuB;AACpD;AACA;AACA,UAAU,8BAAmB;AAC7B,eAAe,8BAAmB;AAClC;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,UAAU,8BAAmB;AAC7B;AACA,oCAAoC,4BAA4B;AAChE,0CAA0C;AAC1C,WAAW,8BAAmB;AAC9B;AACA;AACA;AACA;AACA,UAAU,8BAAmB,kCAAkC;AAC/D;AACA;AACA,UAAU,8BAAmB;AAC7B;AACA;AACA,iBAAiB,8BAAmB,CAAC,8BAAmB;AACxD,UAAU;AACV;AACA;AACA;AACA;;AAEA;;;AAGA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA,iCAAiC,+BAAmB;;AAEpD;;;AAGA,mBAAmB,+BAAmB;AACtC,gBAAgB,+BAAmB;AACnC,YAAY,+BAAmB;;AAE/B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA;;AAEA;;;AAGA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA,iCAAiC,+BAAmB;;AAEpD;;;AAGA,mBAAmB,+BAAmB;AACtC,cAAc,+BAAmB;AACjC,iBAAiB,+BAAmB;AACpC,sBAAsB,+BAAmB;AACzC,iBAAiB,+BAAmB;AACpC,aAAa,+BAAmB;;AAEhC;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,qGAAqG;AACrG;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,oBAAoB,kBAAkB;AACtC;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA,oBAAoB,kBAAkB;AACtC;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA,iCAAiC,gCAAmB;;AAEpD;;;AAGA,sBAAsB,gCAAmB;;AAEzC;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA,iCAAiC,gCAAmB;;AAEpD;;;AAGA,mBAAmB,gCAAmB;AACtC,cAAc,gCAAmB;AACjC,sBAAsB,gCAAmB;AACzC,oBAAoB,gCAAmB;AACvC,YAAY,gCAAmB;AAC/B,YAAY,gCAAmB;AAC/B,iBAAiB,gCAAmB;AACpC,YAAY,gCAAmB;AAC/B,iBAAiB,gCAAmB;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,OAAO;AAC3B;;AAEA;AACA;AACA,QAAQ;AACR;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,kBAAkB,OAAO;AACzB;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,kBAAkB,OAAO;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,kBAAkB,OAAO;AACzB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,kBAAkB,OAAO;AACzB;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA,oBAAoB,UAAU;AAC9B;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA,iCAAiC,gCAAmB;;AAEpD;;;AAGA;AACA,YAAY,gCAAmB;;AAE/B;AACA,WAAW,gCAAmB,KAAK;AACnC;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA,oBAAoB,OAAO;AAC3B;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA,oBAAoB,OAAO;AAC3B;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,oBAAoB,OAAO;AAC3B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,oBAAoB,mBAAmB;AACvC;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,kBAAkB,OAAO;AACzB;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;;AAEN;AACA,IAAI;;AAEJ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,kBAAkB,OAAO;AACzB;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,kBAAkB,OAAO;AACzB;;AAEA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB,0BAA0B;AAC5C;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA,iCAAiC,gCAAmB;;AAEpD;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY,gCAAmB;;AAE/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;;AAEA;AACA;AACA,0BAA0B,KAAK;;AAE/B;AACA;AACA,0BAA0B,KAAK;;AAE/B;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA,IAAI;AACJ,oCAAoC;AACpC,IAAI;AACJ,4BAA4B;AAC5B;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAI;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA;;AAEA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA;;AAEA;;;AAGA;;AAEA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA;;AAEA;;;AAGA,iCAAiC,2CAA2C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD,+DAA+D,yDAAyD,qEAAqE,6DAA6D,wBAAwB;;AAEjjB,kDAAkD,0CAA0C;;AAE5F;AACA,WAAW;AACX;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,CAAC;;AAED;;AAEA,OAAO;AACP;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA;;AAEA;;;AAGA,qGAAqG,qBAAqB,mBAAmB;;AAE7I;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA,iCAAiC,gCAAmB;;AAEpD;;;AAGA,mCAAmC,0BAA0B,0CAA0C,gBAAgB,OAAO,oBAAoB,eAAe,OAAO;;AAExK,sBAAsB,gCAAmB;AACzC,oBAAoB,gCAAmB;AACvC,YAAY,gCAAmB;AAC/B,YAAY,gCAAmB;AAC/B,aAAa,gCAAmB;AAChC,aAAa,gCAAmB;AAChC,gBAAgB,gCAAmB;AACnC,cAAc,gCAAmB;;AAEjC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,qBAAqB;AACzC;AACA;AACA;;AAEA;AACA;AACA;AACA,oBAAoB,kBAAkB;AACtC;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,sBAAsB,kBAAkB;AACxC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,sBAAsB,kBAAkB;AACxC;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA,oBAAoB,kBAAkB;AACtC;AACA;;AAEA;AACA;AACA,QAAQ;AACR;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,kBAAkB,qBAAqB;AACvC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,sBAAsB,0BAA0B;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,kBAAkB,4BAA4B;AAC9C;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB,kBAAkB;AACpC;;AAEA;AACA;;AAEA,sBAAsB,iBAAiB;AACvC;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA,IAAI;AACJ;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,kBAAkB,iBAAiB;AACnC;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,oBAAoB,iBAAiB;AACrC;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA,iCAAiC,gCAAmB;;AAEpD;;;AAGA,aAAa,gCAAmB;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA,iCAAiC,gCAAmB;;AAEpD;;;AAGA,mCAAmC,0BAA0B,0CAA0C,gBAAgB,OAAO,oBAAoB,eAAe,OAAO;;AAExK,aAAa,gCAAmB;AAChC,wBAAwB,gCAAmB;AAC3C,sBAAsB,gCAAmB;AACzC,gBAAgB,gCAAmB;AACnC,YAAY,gCAAmB;;AAE/B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB,qBAAqB;AACvC;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,kBAAkB,mBAAmB;AACrC;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,gBAAgB,mBAAmB;AACnC;AACA;AACA;AACA;AACA,IAAI;AACJ,gBAAgB,mBAAmB;AACnC;;AAEA,sBAAsB,mBAAmB;AACzC;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,kBAAkB,mBAAmB;AACrC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,kBAAkB,mBAAmB;AACrC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,kBAAkB,qBAAqB;AACvC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA,kBAAkB,WAAW;AAC7B;AACA;;AAEA,kBAAkB,WAAW;AAC7B,oBAAoB,WAAW;AAC/B;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,uBAAuB,cAAc;AACrC,yBAAyB,cAAc;AACvC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,cAAc,mBAAmB;AACjC;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,mCAAmC,uBAAuB;AAC1D,qCAAqC,uBAAuB;AAC5D;AACA,0BAA0B,uBAAuB;AACjD;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,cAAc,8BAA8B;AAC5C;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA,iCAAiC,iCAAmB;;AAEpD;;;AAGA,YAAY,iCAAmB;AAC/B,wBAAwB,iCAAmB;;AAE3C;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA,iCAAiC,iCAAmB;;AAEpD;;;AAGA,YAAY,iCAAmB;AAC/B,wBAAwB,iCAAmB;;AAE3C;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA,iCAAiC,iCAAmB;;AAEpD;;;AAGA,wBAAwB,iCAAmB;;AAE3C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA,iCAAiC,iCAAmB;;AAEpD;;;AAGA,wBAAwB,iCAAmB;;AAE3C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,kBAAkB,YAAY;AAC9B;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,kBAAkB,OAAO;AACzB;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA;;AAEA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB,mBAAmB;AACrC;AACA,oBAAoB,sBAAsB;AAC1C;AACA,sBAAsB,sBAAsB;AAC5C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB,qBAAqB;AACvC;AACA,oBAAoB,kBAAkB;AACtC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB,kBAAkB;AACpC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB,mBAAmB;AACrC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB,mBAAmB;AACrC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB,kBAAkB;AACpC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB,kBAAkB;AACpC;AACA;;AAEA;;AAEA,mBAAmB,mBAAmB;AACtC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB,iBAAiB;AACnC;AACA,oBAAoB,cAAc;AAClC;AACA;AACA;AACA;AACA;AACA,oBAAoB,kBAAkB;AACtC;AACA,qBAAqB,iBAAiB;AACtC;AACA;AACA;AACA;AACA;AACA,oBAAoB,gBAAgB;AACpC;AACA,sBAAsB,mBAAmB;AACzC;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA,iCAAiC,iCAAmB;;AAEpD;;;AAGA,iCAAiC,2CAA2C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD,+DAA+D,yDAAyD,qEAAqE,6DAA6D,wBAAwB;;AAEjjB,kDAAkD,0CAA0C;;AAE5F;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAiB,iCAAmB;;AAEpC;AACA;AACA;;AAEA;;AAEA;AACA,uDAAuD;;AAEvD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,iBAAiB;AACjB;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA,KAAK;AACL;AACA;AACA,iFAAiF;AACjF;AACA,KAAK;AACL;AACA;AACA,iFAAiF;AACjF;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA,CAAC;;AAED;;AAEA,OAAO;AACP;AACA;;AAEA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,mDAAmD;;AAEnD;AACA,8CAA8C;;AAE9C;AACA;AACA;AACA;AACA,gCAAgC;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C;AAC3C,sEAAsE;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA,kEAAkE;AAClE;AACA;AACA;AACA;AACA;AACA;;AAEA,cAAc,OAAO;;AAErB;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,wBAAwB,aAAa;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,wBAAwB,aAAa;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,kBAAkB,wBAAwB;AAC1C;AACA;AACA,sBAAsB,YAAY;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,aAAa;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,YAAY;AACpC;AACA;AACA,OAAO;AACP;AACA,0BAA0B,cAAc;AACxC;AACA;AACA;AACA;AACA,0BAA0B,cAAc;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,wBAAwB,cAAc;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,cAAc;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,cAAc;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,8BAA8B,cAAc;AAC5C;AACA;AACA;AACA,6BAA6B,aAAa;AAC1C,gCAAgC,cAAc;AAC9C;AACA;AACA;AACA;AACA;AACA,8BAA8B,cAAc;AAC5C;AACA,gCAAgC,cAAc;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,eAAe;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,UAAU;AAClC,yBAAyB,eAAe;AACxC;AACA;AACA;AACA;AACA;AACA,2BAA2B,SAAS;AACpC;AACA,+BAA+B,UAAU;AACzC;AACA,8BAA8B,eAAe;AAC7C;AACA;AACA;AACA,8BAA8B,eAAe;AAC7C;AACA;AACA;AACA,4BAA4B,eAAe;AAC3C;AACA;AACA;AACA,2BAA2B,eAAe;AAC1C;AACA;AACA,QAAQ;AACR,2BAA2B,eAAe;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,UAAU;AACzC;AACA;AACA,OAAO;AACP,gCAAgC,UAAU;AAC1C;AACA,mCAAmC,eAAe;AAClD;AACA;AACA;AACA,mCAAmC,eAAe;AAClD;AACA;AACA;AACA;AACA,yBAAyB,eAAe;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,WAAW;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,uBAAuB,WAAW;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,YAAY;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,eAAe;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,SAAS;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,eAAe;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,aAAa;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,eAAe;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,eAAe;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,YAAY;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,eAAe;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,eAAe;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA;;AAEA;;;AAGA,iCAAiC,2CAA2C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD,+DAA+D,yDAAyD,qEAAqE,6DAA6D,wBAAwB;;AAEjjB,kDAAkD,0CAA0C;;AAE5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,wBAAwB,eAAe;AACvC;;AAEA,4BAA4B,eAAe;AAC3C;AACA;AACA;;AAEA;AACA;AACA,yBAAyB,gBAAgB;AACzC;;AAEA,6BAA6B,gBAAgB;AAC7C;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;;AAEA,KAAK;AACL;AACA;AACA;AACA,4BAA4B,eAAe;AAC3C;AACA;AACA;;AAEA;AACA,4BAA4B,eAAe;AAC3C;AACA;AACA;;AAEA;AACA,8BAA8B,iBAAiB;AAC/C,kCAAkC,iBAAiB;AACnD;AACA;AACA,mIAAmI;;AAEnI;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,KAAK;AACL;AACA;AACA;;AAEA,uCAAuC;AACvC;AACA;AACA,aAAa;;AAEb;AACA;AACA;;AAEA;AACA,+CAA+C;AAC/C;AACA;AACA,qBAAqB;AACrB;AACA;AACA,+CAA+C;AAC/C;AACA;AACA,qBAAqB;AACrB;AACA;AACA,+CAA+C;AAC/C;AACA;AACA,qBAAqB;AACrB;;AAEA,yFAAyF;AACzF;AACA,iBAAiB;;AAEjB;AACA;;AAEA;AACA;;AAEA;;AAEA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA,CAAC;;AAED;;AAEA,OAAO;AACP;AACA,iCAAiC,iCAAmB;;AAEpD;;;AAGA;AACA;AACA;;AAEA,sBAAsB,iCAAmB;AACzC,+BAA+B,iCAAmB;AAClD,0BAA0B,iCAAmB;AAC7C,0BAA0B,iCAAmB;AAC7C,wBAAwB,iCAAmB;AAC3C,qBAAqB,iCAAmB;AACxC,qBAAqB,iCAAmB;AACxC,uBAAuB,iCAAmB;AAC1C,mBAAmB,iCAAmB;AACtC,qBAAqB,iCAAmB;AACxC,mBAAmB,iCAAmB;AACtC,oBAAoB,iCAAmB;AACvC,wBAAwB,iCAAmB;AAC3C,wBAAwB,iCAAmB;AAC3C,uBAAuB,iCAAmB;AAC1C,+BAA+B,iCAAmB;AAClD,uBAAuB,iCAAmB;AAC1C,wBAAwB,iCAAmB;AAC3C,0BAA0B,iCAAmB;AAC7C,oBAAoB,iCAAmB;AACvC,mBAAmB,iCAAmB;AACtC,2BAA2B,iCAAmB;AAC9C,mBAAmB,iCAAmB;AACtC,oBAAoB,iCAAmB;AACvC,6BAA6B,iCAAmB;AAChD,6BAA6B,iCAAmB;AAChD,oBAAoB,iCAAmB;AACvC,iBAAiB,iCAAmB;;AAEpC;;AAEA,OAAO;AACP;AACA;;AAEA;;;AAGA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA,sCAAsC,QAAQ;AAC9C;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB,2BAA2B;AAC7C;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO;AACP;AACA,CAAC;;;;;;;;;;;;;;;;;;;;;;;ACzmK6B;AAGA;AAGA;AAGA;AACA;AAGA;AAcA;;AAE9B;AAC2C;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,qEAAM,eAAe,MAAM,GAAG,WAAW,IAAI,OAAO;AACzE,qBAAqB,qEAAM,iBAAiB,WAAW,EAAE,MAAM,KAAK,MAAM,GAAG,MAAM;AACnF,qBAAqB,qEAAM,mBAAmB,MAAM,KAAK,UAAU,GAAG,MAAM;AAC5E,qBAAqB,qEAAM,eAAe,UAAU,KAAK,MAAM,GAAG,OAAO,IAAI,MAAM;AACnF;AACA;AACA,qBAAqB,qEAAM;AAC3B,qBAAqB,qEAAM;AAC3B,qBAAqB,qEAAM;AAC3B,qBAAqB,qEAAM;AAC3B;AACA,uDAAuD,qEAAM;AAC7D;AACA;AACA,IAAI;AACJ;AACA;AACA,CAAC;AACD,8CAA8C,qEAAM;AACpD;AACA;AACA,CAAC;AACD,+CAA+C,qEAAM;AACrD;AACA;AACA,CAAC;AACD,+CAA+C,qEAAM;AACrD;AACA;AACA,CAAC;AACD,gDAAgD,qEAAM;AACtD;AACA;AACA;AACA,CAAC;AACD,2CAA2C,qEAAM;AACjD;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,uDAAuD,qEAAM;AAC7D;AACA,CAAC;AACD,mDAAmD,qEAAM;AACzD,kBAAkB,UAAU,EAAE,UAAU;AACxC;AACA,CAAC;AACD,+DAA+D,qEAAM;AACrE;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,IAAI;AACJ;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,CAAC;AACD,wDAAwD,qEAAM;AAC9D;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA,CAAC;AACD,wDAAwD,qEAAM;AAC9D;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,CAAC;AACD,4CAA4C,qEAAM;AAClD;AACA;AACA,CAAC;AACD,6CAA6C,qEAAM;AACnD;AACA;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC;AACA,CAAC;;AAED;AACA,kCAAkC,gFAAqB;AACvD,gBAAgB,yEAAe;AAC/B,WAAW;AACX,YAAY;AACZ;AACA,mBAAmB;AACnB;AACA;AACA;AACA,CAAC;AACD,6BAA6B,qEAAM;AACnC;AACA,EAAE,oEAAK;AACP,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,yBAAyB,GAAG,iCAAiC,gCAAgC;AAC7F;AACA;AACA;AACA;AACA,sCAAsC,GAAG;AACzC;AACA;AACA;AACA,wBAAwB,GAAG;AAC3B;AACA;AACA;AACA,sCAAsC,GAAG;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kCAAkC,qEAAM;AACxC,kCAAkC,qEAAM,YAAY,gBAAgB;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mCAAmC,qEAAM;AACzC,+BAA+B,qEAAM;AACrC,8BAA8B,qEAAM;AACpC,+BAA+B,qEAAM,YAAY,6BAA6B;AAC9E;AACA;AACA,uBAAuB,GAAG,iCAAiC,gCAAgC;AAC3F;AACA;AACA;AACA;AACA,oCAAoC,GAAG;AACvC;AACA;AACA;AACA,sBAAsB,GAAG;AACzB;AACA;AACA;AACA,oCAAoC,GAAG;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gCAAgC,qEAAM;AACtC;AACA,CAAC;AACD,8BAA8B,qEAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,4DAA4D,MAAM,IAAI,MAAM,2BAA2B,OAAO;AAC9G;AACA;AACA;AACA;AACA,6DAA6D,MAAM,IAAI,MAAM,2BAA2B,OAAO;AAC/G;AACA;AACA;AACA;AACA,2BAA2B,MAAM;AACjC;AACA;AACA;AACA;AACA,4BAA4B,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,MAAM;AACjC;AACA;AACA;AACA;AACA,4BAA4B,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,+BAA+B,qEAAM;AACrC,wCAAwC,qEAAM;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAI;AACX;AACA,KAAK,IAAI;AACT;AACA,sBAAsB;AACtB;AACA,8CAA8C,kBAAkB;AAChE;AACA;AACA,gCAAgC,qEAAM;AACtC,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,sCAAsC,qEAAM;AAC5C;AACA,CAAC;AACD,qCAAqC,qEAAM;AAC3C;AACA;AACA,iBAAiB;AACjB,iBAAiB;AACjB,aAAa;AACb,aAAa;AACb,mBAAmB;AACnB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,yEAAS;AACxB;AACA;AACA;AACA;AACA;AACA,qEAAM;;AAEN;AACA,iCAAiC,qEAAM;AACvC,EAAE,8EAAgB;AAClB;AACA,iDAAiD,6BAA6B;AAC9E,mDAAmD,8BAA8B;AACjF;AACA,CAAC;AACD;AACA,yBAAyB,qEAAM;AAC/B,sBAAsB,mEAAK;AAC3B,IAAI,8DAAG;AACP;AACA,GAAG;AACH;;AAEA;AACA,gCAAgC,qEAAM;AACtC;AACA,oBAAoB;AACpB,cAAc;AACd;AACA;;AAEA;AACA,YAAY;AACZ;;AAEA;AACA;AACA,cAAc;AACd,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACkC;AACE;AACR;;AAE5B;AACA,+BAA+B,qEAAM;AACrC,gEAAgE,kBAAkB,KAAK,KAAK;AAC5F,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6GAA6G,cAAc,uBAAuB,kBAAkB,uGAAuG,cAAc,uBAAuB,kBAAkB,uGAAuG,cAAc,uBAAuB,kBAAkB,yFAAyF,cAAc,uBAAuB,kBAAkB,kEAAkE,cAAc,uBAAuB,kBAAkB,kEAAkE,cAAc,uBAAuB,kBAAkB;AACl2B;AACA,KAAK;AACL;AACA;AACA,yFAAyF,cAAc,uBAAuB,kBAAkB,oEAAoE,cAAc,uBAAuB,kBAAkB,oEAAoE,cAAc,uBAAuB,kBAAkB,yHAAyH,kBAAkB,sHAAsH,cAAc,sBAAsB,6HAA6H,kBAAkB,sHAAsH,cAAc,sBAAsB,6HAA6H,kBAAkB,sHAAsH,cAAc,sBAAsB,+DAA+D,cAAc,sBAAsB,wDAAwD,cAAc,sBAAsB,wDAAwD,cAAc,sBAAsB,+DAA+D,cAAc,sBAAsB,wDAAwD,cAAc,sBAAsB,wDAAwD,cAAc,sBAAsB,+DAA+D,cAAc,sBAAsB,wDAAwD,cAAc,sBAAsB,wDAAwD,cAAc,sBAAsB;AAC9kE;AACA,KAAK;AACL;AACA;AACA,qFAAqF,cAAc,uBAAuB,kBAAkB,mEAAmE,cAAc,uBAAuB,kBAAkB,mEAAmE,cAAc,uBAAuB,kBAAkB,mEAAmE,cAAc,uBAAuB,kBAAkB,mEAAmE,cAAc,uBAAuB,kBAAkB,qEAAqE,cAAc,uBAAuB,kBAAkB,mEAAmE,cAAc,uBAAuB,kBAAkB,8JAA8J,kBAAkB;AAC1hC;AACA,KAAK;AACL;AACA;AACA,6DAA6D,cAAc,uBAAuB,kBAAkB,gEAAgE,cAAc,uBAAuB,kBAAkB,gEAAgE,cAAc,uBAAuB,kBAAkB,6EAA6E,cAAc,uBAAuB,kBAAkB,4EAA4E,cAAc,uBAAuB,kBAAkB,sEAAsE,cAAc,uBAAuB,kBAAkB,sEAAsE,cAAc,uBAAuB,kBAAkB;AACn2B;AACA,KAAK;AACL;AACA;AACA,yVAAyV,cAAc,uBAAuB,kBAAkB;AAChZ;AACA,KAAK;AACL,aAAa,sEAAW;AACxB;AACA;AACA;AACA;AACA;;AAEA;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR,YAAY,uBAAuB;AACnC,cAAc,mBAAmB;AACjC,YAAY,mBAAmB;AAC/B;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA,wCAAwC,OAAO,GAAG,QAAQ,IAAI,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,MAAM;AAC7F;AACA;AACA;AACA,8HAA8H,OAAO,GAAG,OAAO;AAC/I;AACA;AACA;AACA;AACA,8HAA8H,OAAO,GAAG,OAAO;AAC/I;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA,YAAY;AACZ;AACA;AACA;AACA,gBAAgB,yEAAU;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,YAAY,yEAAS;AACrB;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA,qFAAqF,gBAAgB;AACrG;AACA;AACA;AACA;AACA,4BAA4B,KAAK,IAAI,2BAA2B;AAChE,4BAA4B,sBAAsB,IAAI,uBAAuB;AAC7E,yBAAyB,gBAAgB,OAAO,oBAAoB;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,iCAAiC,qEAAM;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,eAAe;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,MAAM,yEAAU,cAAc,uFAAuF,EAAE;AACzI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,yEAAU;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,YAAY,yEAAS;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA;AACA;AACA,YAAY,yEAAU;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,QAAQ,yEAAS;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,MAAM,yEAAU,iBAAiB,6EAA6E,EAAE;AAC9H;AACA,MAAM;AACN;AACA,cAAc,MAAM,yEAAU,YAAY,6EAA6E,EAAE;AACzH;AACA;AACA;AACA,gGAAgG,SAAS,GAAG;AAC5G;AACA;AACA;AACA,mDAAmD,uCAAuC;AAC1F,MAAM;AACN;AACA;AACA,cAAc,UAAU,GAAG,WAAW,cAAc,UAAU,YAAY,UAAU;AACpF;AACA;AACA;AACA,YAAY,gBAAgB;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,gBAAgB;AAC5B;AACA;AACA;AACA,GAAG;AACH,CAAC;;AAED;AACA,gFAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,0DAAS,KAAK,wDAAK;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL,GAAG;AACH;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL,GAAG;AACH;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL,GAAG;AACH;AACA,qEAAM;AACN;AACA;AACA,YAAY,4EAA4E;AACxF;AACA;AACA,aAAa,MAAM,GAAG,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA,qEAAM;AACN;AACA,4CAA4C,qEAAM;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,oBAAoB;AAC5C,8BAA8B,gBAAgB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,cAAc;AACd,8BAA8B,IAAI,GAAG,MAAM;AAC3C;AACA,8BAA8B,IAAI,GAAG,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,8BAA8B,aAAa;AAC3C;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA,mCAAmC,qEAAM,aAAa,OAAO,GAAG,OAAO;AACvE,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA,WAAW;AACX;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,qEAAM;AACN,uEAAuE,8BAA8B;AACrG;AACA,qBAAqB,qDAAM;AAC3B,eAAe,8DAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,4BAA4B,2BAA2B;AACvD;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,wBAAwB,0BAA0B;AAClD;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,kBAAkB;AAClC,gBAAgB,kBAAkB;AAClC;AACA;AACA,OAAO;AACP;AACA;AACA,gBAAgB,kBAAkB;AAClC,gBAAgB,kBAAkB;AAClC;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,gBAAgB,eAAe;AAC/B,gBAAgB,eAAe;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ;AACA;AACA;AACA,kBAAkB,eAAe;AACjC,kBAAkB,eAAe;AACjC;AACA;AACA;AACA,oBAAoB,YAAY;AAChC;AACA,oBAAoB,qBAAqB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,MAAM,8DAAG;AACT;AACA,KAAK;AACL,GAAG;AACH;AACA,qEAAM;AACN,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,8EAAgB;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gFAAiB;AACnB,CAAC;AACD,iBAAiB;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACA;AAGE;;;;;;;;;;;;;ACxmC4B;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;;AAIJ;;;;;;;;;;;;;AClB4B;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qEAAM;AACV;AACA;AACA;AACA;AACA;;AAIE", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/cytoscape-fcose/cytoscape-fcose.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/cytoscape-fcose/node_modules/cose-base/cose-base.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/cytoscape-fcose/node_modules/layout-base/layout-base.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/architectureDiagram-IEHRJDOE.mjs", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/chunk-4BMEZGHF.mjs", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/chunk-XZIHB7SX.mjs"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"cose-base\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"cose-base\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"cytoscapeFcose\"] = factory(require(\"cose-base\"));\n\telse\n\t\troot[\"cytoscapeFcose\"] = factory(root[\"coseBase\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__140__) {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 658:\n/***/ ((module) => {\n\n\n\n// Simple, internal Object.assign() polyfill for options objects etc.\n\nmodule.exports = Object.assign != null ? Object.assign.bind(Object) : function (tgt) {\n  for (var _len = arguments.length, srcs = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    srcs[_key - 1] = arguments[_key];\n  }\n\n  srcs.forEach(function (src) {\n    Object.keys(src).forEach(function (k) {\n      return tgt[k] = src[k];\n    });\n  });\n\n  return tgt;\n};\n\n/***/ }),\n\n/***/ 548:\n/***/ ((module, __unused_webpack_exports, __webpack_require__) => {\n\n\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\n/*\n * Auxiliary functions\n */\n\nvar LinkedList = __webpack_require__(140).layoutBase.LinkedList;\n\nvar auxiliary = {};\n\n// get the top most nodes\nauxiliary.getTopMostNodes = function (nodes) {\n  var nodesMap = {};\n  for (var i = 0; i < nodes.length; i++) {\n    nodesMap[nodes[i].id()] = true;\n  }\n  var roots = nodes.filter(function (ele, i) {\n    if (typeof ele === \"number\") {\n      ele = i;\n    }\n    var parent = ele.parent()[0];\n    while (parent != null) {\n      if (nodesMap[parent.id()]) {\n        return false;\n      }\n      parent = parent.parent()[0];\n    }\n    return true;\n  });\n\n  return roots;\n};\n\n// find disconnected components and create dummy nodes that connect them\nauxiliary.connectComponents = function (cy, eles, topMostNodes, dummyNodes) {\n  var queue = new LinkedList();\n  var visited = new Set();\n  var visitedTopMostNodes = [];\n  var currentNeighbor = void 0;\n  var minDegreeNode = void 0;\n  var minDegree = void 0;\n\n  var isConnected = false;\n  var count = 1;\n  var nodesConnectedToDummy = [];\n  var components = [];\n\n  var _loop = function _loop() {\n    var cmpt = cy.collection();\n    components.push(cmpt);\n\n    var currentNode = topMostNodes[0];\n    var childrenOfCurrentNode = cy.collection();\n    childrenOfCurrentNode.merge(currentNode).merge(currentNode.descendants().intersection(eles));\n    visitedTopMostNodes.push(currentNode);\n\n    childrenOfCurrentNode.forEach(function (node) {\n      queue.push(node);\n      visited.add(node);\n      cmpt.merge(node);\n    });\n\n    var _loop2 = function _loop2() {\n      currentNode = queue.shift();\n\n      // Traverse all neighbors of this node\n      var neighborNodes = cy.collection();\n      currentNode.neighborhood().nodes().forEach(function (node) {\n        if (eles.intersection(currentNode.edgesWith(node)).length > 0) {\n          neighborNodes.merge(node);\n        }\n      });\n\n      for (var i = 0; i < neighborNodes.length; i++) {\n        var neighborNode = neighborNodes[i];\n        currentNeighbor = topMostNodes.intersection(neighborNode.union(neighborNode.ancestors()));\n        if (currentNeighbor != null && !visited.has(currentNeighbor[0])) {\n          var childrenOfNeighbor = currentNeighbor.union(currentNeighbor.descendants());\n\n          childrenOfNeighbor.forEach(function (node) {\n            queue.push(node);\n            visited.add(node);\n            cmpt.merge(node);\n            if (topMostNodes.has(node)) {\n              visitedTopMostNodes.push(node);\n            }\n          });\n        }\n      }\n    };\n\n    while (queue.length != 0) {\n      _loop2();\n    }\n\n    cmpt.forEach(function (node) {\n      eles.intersection(node.connectedEdges()).forEach(function (e) {\n        // connectedEdges() usually cached\n        if (cmpt.has(e.source()) && cmpt.has(e.target())) {\n          // has() is cheap\n          cmpt.merge(e);\n        }\n      });\n    });\n\n    if (visitedTopMostNodes.length == topMostNodes.length) {\n      isConnected = true;\n    }\n\n    if (!isConnected || isConnected && count > 1) {\n      minDegreeNode = visitedTopMostNodes[0];\n      minDegree = minDegreeNode.connectedEdges().length;\n      visitedTopMostNodes.forEach(function (node) {\n        if (node.connectedEdges().length < minDegree) {\n          minDegree = node.connectedEdges().length;\n          minDegreeNode = node;\n        }\n      });\n      nodesConnectedToDummy.push(minDegreeNode.id());\n      // TO DO: Check efficiency of this part\n      var temp = cy.collection();\n      temp.merge(visitedTopMostNodes[0]);\n      visitedTopMostNodes.forEach(function (node) {\n        temp.merge(node);\n      });\n      visitedTopMostNodes = [];\n      topMostNodes = topMostNodes.difference(temp);\n      count++;\n    }\n  };\n\n  do {\n    _loop();\n  } while (!isConnected);\n\n  if (dummyNodes) {\n    if (nodesConnectedToDummy.length > 0) {\n      dummyNodes.set('dummy' + (dummyNodes.size + 1), nodesConnectedToDummy);\n    }\n  }\n  return components;\n};\n\n// relocates componentResult to originalCenter if there is no fixedNodeConstraint\nauxiliary.relocateComponent = function (originalCenter, componentResult, options) {\n  if (!options.fixedNodeConstraint) {\n    var minXCoord = Number.POSITIVE_INFINITY;\n    var maxXCoord = Number.NEGATIVE_INFINITY;\n    var minYCoord = Number.POSITIVE_INFINITY;\n    var maxYCoord = Number.NEGATIVE_INFINITY;\n    if (options.quality == \"draft\") {\n      // calculate current bounding box\n      var _iteratorNormalCompletion = true;\n      var _didIteratorError = false;\n      var _iteratorError = undefined;\n\n      try {\n        for (var _iterator = componentResult.nodeIndexes[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n          var _ref = _step.value;\n\n          var _ref2 = _slicedToArray(_ref, 2);\n\n          var key = _ref2[0];\n          var value = _ref2[1];\n\n          var cyNode = options.cy.getElementById(key);\n          if (cyNode) {\n            var nodeBB = cyNode.boundingBox();\n            var leftX = componentResult.xCoords[value] - nodeBB.w / 2;\n            var rightX = componentResult.xCoords[value] + nodeBB.w / 2;\n            var topY = componentResult.yCoords[value] - nodeBB.h / 2;\n            var bottomY = componentResult.yCoords[value] + nodeBB.h / 2;\n\n            if (leftX < minXCoord) minXCoord = leftX;\n            if (rightX > maxXCoord) maxXCoord = rightX;\n            if (topY < minYCoord) minYCoord = topY;\n            if (bottomY > maxYCoord) maxYCoord = bottomY;\n          }\n        }\n        // find difference between current and original center\n      } catch (err) {\n        _didIteratorError = true;\n        _iteratorError = err;\n      } finally {\n        try {\n          if (!_iteratorNormalCompletion && _iterator.return) {\n            _iterator.return();\n          }\n        } finally {\n          if (_didIteratorError) {\n            throw _iteratorError;\n          }\n        }\n      }\n\n      var diffOnX = originalCenter.x - (maxXCoord + minXCoord) / 2;\n      var diffOnY = originalCenter.y - (maxYCoord + minYCoord) / 2;\n      // move component to original center\n      componentResult.xCoords = componentResult.xCoords.map(function (x) {\n        return x + diffOnX;\n      });\n      componentResult.yCoords = componentResult.yCoords.map(function (y) {\n        return y + diffOnY;\n      });\n    } else {\n      // calculate current bounding box\n      Object.keys(componentResult).forEach(function (item) {\n        var node = componentResult[item];\n        var leftX = node.getRect().x;\n        var rightX = node.getRect().x + node.getRect().width;\n        var topY = node.getRect().y;\n        var bottomY = node.getRect().y + node.getRect().height;\n\n        if (leftX < minXCoord) minXCoord = leftX;\n        if (rightX > maxXCoord) maxXCoord = rightX;\n        if (topY < minYCoord) minYCoord = topY;\n        if (bottomY > maxYCoord) maxYCoord = bottomY;\n      });\n      // find difference between current and original center\n      var _diffOnX = originalCenter.x - (maxXCoord + minXCoord) / 2;\n      var _diffOnY = originalCenter.y - (maxYCoord + minYCoord) / 2;\n      // move component to original center\n      Object.keys(componentResult).forEach(function (item) {\n        var node = componentResult[item];\n        node.setCenter(node.getCenterX() + _diffOnX, node.getCenterY() + _diffOnY);\n      });\n    }\n  }\n};\n\nauxiliary.calcBoundingBox = function (parentNode, xCoords, yCoords, nodeIndexes) {\n  // calculate bounds\n  var left = Number.MAX_SAFE_INTEGER;\n  var right = Number.MIN_SAFE_INTEGER;\n  var top = Number.MAX_SAFE_INTEGER;\n  var bottom = Number.MIN_SAFE_INTEGER;\n  var nodeLeft = void 0;\n  var nodeRight = void 0;\n  var nodeTop = void 0;\n  var nodeBottom = void 0;\n\n  var nodes = parentNode.descendants().not(\":parent\");\n  var s = nodes.length;\n  for (var i = 0; i < s; i++) {\n    var node = nodes[i];\n\n    nodeLeft = xCoords[nodeIndexes.get(node.id())] - node.width() / 2;\n    nodeRight = xCoords[nodeIndexes.get(node.id())] + node.width() / 2;\n    nodeTop = yCoords[nodeIndexes.get(node.id())] - node.height() / 2;\n    nodeBottom = yCoords[nodeIndexes.get(node.id())] + node.height() / 2;\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n\n    if (right < nodeRight) {\n      right = nodeRight;\n    }\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (bottom < nodeBottom) {\n      bottom = nodeBottom;\n    }\n  }\n\n  var boundingBox = {};\n  boundingBox.topLeftX = left;\n  boundingBox.topLeftY = top;\n  boundingBox.width = right - left;\n  boundingBox.height = bottom - top;\n  return boundingBox;\n};\n\n// This function finds and returns parent nodes whose all children are hidden\nauxiliary.calcParentsWithoutChildren = function (cy, eles) {\n  var parentsWithoutChildren = cy.collection();\n  eles.nodes(':parent').forEach(function (parent) {\n    var check = false;\n    parent.children().forEach(function (child) {\n      if (child.css('display') != 'none') {\n        check = true;\n      }\n    });\n    if (!check) {\n      parentsWithoutChildren.merge(parent);\n    }\n  });\n\n  return parentsWithoutChildren;\n};\n\nmodule.exports = auxiliary;\n\n/***/ }),\n\n/***/ 816:\n/***/ ((module, __unused_webpack_exports, __webpack_require__) => {\n\n\n\n/**\n  The implementation of the postprocessing part that applies CoSE layout over the spectral layout\n*/\n\nvar aux = __webpack_require__(548);\nvar CoSELayout = __webpack_require__(140).CoSELayout;\nvar CoSENode = __webpack_require__(140).CoSENode;\nvar PointD = __webpack_require__(140).layoutBase.PointD;\nvar DimensionD = __webpack_require__(140).layoutBase.DimensionD;\nvar LayoutConstants = __webpack_require__(140).layoutBase.LayoutConstants;\nvar FDLayoutConstants = __webpack_require__(140).layoutBase.FDLayoutConstants;\nvar CoSEConstants = __webpack_require__(140).CoSEConstants;\n\n// main function that cose layout is processed\nvar coseLayout = function coseLayout(options, spectralResult) {\n\n  var cy = options.cy;\n  var eles = options.eles;\n  var nodes = eles.nodes();\n  var edges = eles.edges();\n\n  var nodeIndexes = void 0;\n  var xCoords = void 0;\n  var yCoords = void 0;\n  var idToLNode = {};\n\n  if (options.randomize) {\n    nodeIndexes = spectralResult[\"nodeIndexes\"];\n    xCoords = spectralResult[\"xCoords\"];\n    yCoords = spectralResult[\"yCoords\"];\n  }\n\n  var isFn = function isFn(fn) {\n    return typeof fn === 'function';\n  };\n\n  var optFn = function optFn(opt, ele) {\n    if (isFn(opt)) {\n      return opt(ele);\n    } else {\n      return opt;\n    }\n  };\n\n  /**** Postprocessing functions ****/\n\n  var parentsWithoutChildren = aux.calcParentsWithoutChildren(cy, eles);\n\n  // transfer cytoscape nodes to cose nodes\n  var processChildrenList = function processChildrenList(parent, children, layout, options) {\n    var size = children.length;\n    for (var i = 0; i < size; i++) {\n      var theChild = children[i];\n      var children_of_children = null;\n      if (theChild.intersection(parentsWithoutChildren).length == 0) {\n        children_of_children = theChild.children();\n      }\n      var theNode = void 0;\n\n      var dimensions = theChild.layoutDimensions({\n        nodeDimensionsIncludeLabels: options.nodeDimensionsIncludeLabels\n      });\n\n      if (theChild.outerWidth() != null && theChild.outerHeight() != null) {\n        if (options.randomize) {\n          if (!theChild.isParent()) {\n            theNode = parent.add(new CoSENode(layout.graphManager, new PointD(xCoords[nodeIndexes.get(theChild.id())] - dimensions.w / 2, yCoords[nodeIndexes.get(theChild.id())] - dimensions.h / 2), new DimensionD(parseFloat(dimensions.w), parseFloat(dimensions.h))));\n          } else {\n            var parentInfo = aux.calcBoundingBox(theChild, xCoords, yCoords, nodeIndexes);\n            if (theChild.intersection(parentsWithoutChildren).length == 0) {\n              theNode = parent.add(new CoSENode(layout.graphManager, new PointD(parentInfo.topLeftX, parentInfo.topLeftY), new DimensionD(parentInfo.width, parentInfo.height)));\n            } else {\n              // for the parentsWithoutChildren\n              theNode = parent.add(new CoSENode(layout.graphManager, new PointD(parentInfo.topLeftX, parentInfo.topLeftY), new DimensionD(parseFloat(dimensions.w), parseFloat(dimensions.h))));\n            }\n          }\n        } else {\n          theNode = parent.add(new CoSENode(layout.graphManager, new PointD(theChild.position('x') - dimensions.w / 2, theChild.position('y') - dimensions.h / 2), new DimensionD(parseFloat(dimensions.w), parseFloat(dimensions.h))));\n        }\n      } else {\n        theNode = parent.add(new CoSENode(this.graphManager));\n      }\n      // Attach id to the layout node and repulsion value\n      theNode.id = theChild.data(\"id\");\n      theNode.nodeRepulsion = optFn(options.nodeRepulsion, theChild);\n      // Attach the paddings of cy node to layout node\n      theNode.paddingLeft = parseInt(theChild.css('padding'));\n      theNode.paddingTop = parseInt(theChild.css('padding'));\n      theNode.paddingRight = parseInt(theChild.css('padding'));\n      theNode.paddingBottom = parseInt(theChild.css('padding'));\n\n      //Attach the label properties to both compound and simple nodes if labels will be included in node dimensions\n      //These properties will be used while updating bounds of compounds during iterations or tiling\n      //and will be used for simple nodes while transferring final positions to cytoscape\n      if (options.nodeDimensionsIncludeLabels) {\n        theNode.labelWidth = theChild.boundingBox({ includeLabels: true, includeNodes: false, includeOverlays: false }).w;\n        theNode.labelHeight = theChild.boundingBox({ includeLabels: true, includeNodes: false, includeOverlays: false }).h;\n        theNode.labelPosVertical = theChild.css(\"text-valign\");\n        theNode.labelPosHorizontal = theChild.css(\"text-halign\");\n      }\n\n      // Map the layout node\n      idToLNode[theChild.data(\"id\")] = theNode;\n\n      if (isNaN(theNode.rect.x)) {\n        theNode.rect.x = 0;\n      }\n\n      if (isNaN(theNode.rect.y)) {\n        theNode.rect.y = 0;\n      }\n\n      if (children_of_children != null && children_of_children.length > 0) {\n        var theNewGraph = void 0;\n        theNewGraph = layout.getGraphManager().add(layout.newGraph(), theNode);\n        processChildrenList(theNewGraph, children_of_children, layout, options);\n      }\n    }\n  };\n\n  // transfer cytoscape edges to cose edges\n  var processEdges = function processEdges(layout, gm, edges) {\n    var idealLengthTotal = 0;\n    var edgeCount = 0;\n    for (var i = 0; i < edges.length; i++) {\n      var edge = edges[i];\n      var sourceNode = idToLNode[edge.data(\"source\")];\n      var targetNode = idToLNode[edge.data(\"target\")];\n      if (sourceNode && targetNode && sourceNode !== targetNode && sourceNode.getEdgesBetween(targetNode).length == 0) {\n        var e1 = gm.add(layout.newEdge(), sourceNode, targetNode);\n        e1.id = edge.id();\n        e1.idealLength = optFn(options.idealEdgeLength, edge);\n        e1.edgeElasticity = optFn(options.edgeElasticity, edge);\n        idealLengthTotal += e1.idealLength;\n        edgeCount++;\n      }\n    }\n    // we need to update the ideal edge length constant with the avg. ideal length value after processing edges\n    // in case there is no edge, use other options\n    if (options.idealEdgeLength != null) {\n      if (edgeCount > 0) CoSEConstants.DEFAULT_EDGE_LENGTH = FDLayoutConstants.DEFAULT_EDGE_LENGTH = idealLengthTotal / edgeCount;else if (!isFn(options.idealEdgeLength)) // in case there is no edge, but option gives a value to use\n        CoSEConstants.DEFAULT_EDGE_LENGTH = FDLayoutConstants.DEFAULT_EDGE_LENGTH = options.idealEdgeLength;else // in case there is no edge and we cannot get a value from option (because it's a function)\n        CoSEConstants.DEFAULT_EDGE_LENGTH = FDLayoutConstants.DEFAULT_EDGE_LENGTH = 50;\n      // we need to update these constant values based on the ideal edge length constant\n      CoSEConstants.MIN_REPULSION_DIST = FDLayoutConstants.MIN_REPULSION_DIST = FDLayoutConstants.DEFAULT_EDGE_LENGTH / 10.0;\n      CoSEConstants.DEFAULT_RADIAL_SEPARATION = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\n    }\n  };\n\n  // transfer cytoscape constraints to cose layout\n  var processConstraints = function processConstraints(layout, options) {\n    // get nodes to be fixed\n    if (options.fixedNodeConstraint) {\n      layout.constraints[\"fixedNodeConstraint\"] = options.fixedNodeConstraint;\n    }\n    // get nodes to be aligned\n    if (options.alignmentConstraint) {\n      layout.constraints[\"alignmentConstraint\"] = options.alignmentConstraint;\n    }\n    // get nodes to be relatively placed\n    if (options.relativePlacementConstraint) {\n      layout.constraints[\"relativePlacementConstraint\"] = options.relativePlacementConstraint;\n    }\n  };\n\n  /**** Apply postprocessing ****/\n  if (options.nestingFactor != null) CoSEConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR = FDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR = options.nestingFactor;\n  if (options.gravity != null) CoSEConstants.DEFAULT_GRAVITY_STRENGTH = FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH = options.gravity;\n  if (options.numIter != null) CoSEConstants.MAX_ITERATIONS = FDLayoutConstants.MAX_ITERATIONS = options.numIter;\n  if (options.gravityRange != null) CoSEConstants.DEFAULT_GRAVITY_RANGE_FACTOR = FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR = options.gravityRange;\n  if (options.gravityCompound != null) CoSEConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH = options.gravityCompound;\n  if (options.gravityRangeCompound != null) CoSEConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR = options.gravityRangeCompound;\n  if (options.initialEnergyOnIncremental != null) CoSEConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL = options.initialEnergyOnIncremental;\n\n  if (options.tilingCompareBy != null) CoSEConstants.TILING_COMPARE_BY = options.tilingCompareBy;\n\n  if (options.quality == 'proof') LayoutConstants.QUALITY = 2;else LayoutConstants.QUALITY = 0;\n\n  CoSEConstants.NODE_DIMENSIONS_INCLUDE_LABELS = FDLayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS = LayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS = options.nodeDimensionsIncludeLabels;\n  CoSEConstants.DEFAULT_INCREMENTAL = FDLayoutConstants.DEFAULT_INCREMENTAL = LayoutConstants.DEFAULT_INCREMENTAL = !options.randomize;\n  CoSEConstants.ANIMATE = FDLayoutConstants.ANIMATE = LayoutConstants.ANIMATE = options.animate;\n  CoSEConstants.TILE = options.tile;\n  CoSEConstants.TILING_PADDING_VERTICAL = typeof options.tilingPaddingVertical === 'function' ? options.tilingPaddingVertical.call() : options.tilingPaddingVertical;\n  CoSEConstants.TILING_PADDING_HORIZONTAL = typeof options.tilingPaddingHorizontal === 'function' ? options.tilingPaddingHorizontal.call() : options.tilingPaddingHorizontal;\n\n  CoSEConstants.DEFAULT_INCREMENTAL = FDLayoutConstants.DEFAULT_INCREMENTAL = LayoutConstants.DEFAULT_INCREMENTAL = true;\n  CoSEConstants.PURE_INCREMENTAL = !options.randomize;\n  LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES = options.uniformNodeDimensions;\n\n  // This part is for debug/demo purpose\n  if (options.step == \"transformed\") {\n    CoSEConstants.TRANSFORM_ON_CONSTRAINT_HANDLING = true;\n    CoSEConstants.ENFORCE_CONSTRAINTS = false;\n    CoSEConstants.APPLY_LAYOUT = false;\n  }\n  if (options.step == \"enforced\") {\n    CoSEConstants.TRANSFORM_ON_CONSTRAINT_HANDLING = false;\n    CoSEConstants.ENFORCE_CONSTRAINTS = true;\n    CoSEConstants.APPLY_LAYOUT = false;\n  }\n  if (options.step == \"cose\") {\n    CoSEConstants.TRANSFORM_ON_CONSTRAINT_HANDLING = false;\n    CoSEConstants.ENFORCE_CONSTRAINTS = false;\n    CoSEConstants.APPLY_LAYOUT = true;\n  }\n  if (options.step == \"all\") {\n    if (options.randomize) CoSEConstants.TRANSFORM_ON_CONSTRAINT_HANDLING = true;else CoSEConstants.TRANSFORM_ON_CONSTRAINT_HANDLING = false;\n    CoSEConstants.ENFORCE_CONSTRAINTS = true;\n    CoSEConstants.APPLY_LAYOUT = true;\n  }\n\n  if (options.fixedNodeConstraint || options.alignmentConstraint || options.relativePlacementConstraint) {\n    CoSEConstants.TREE_REDUCTION_ON_INCREMENTAL = false;\n  } else {\n    CoSEConstants.TREE_REDUCTION_ON_INCREMENTAL = true;\n  }\n\n  var coseLayout = new CoSELayout();\n  var gm = coseLayout.newGraphManager();\n\n  processChildrenList(gm.addRoot(), aux.getTopMostNodes(nodes), coseLayout, options);\n  processEdges(coseLayout, gm, edges);\n  processConstraints(coseLayout, options);\n\n  coseLayout.runLayout();\n\n  return idToLNode;\n};\n\nmodule.exports = { coseLayout: coseLayout };\n\n/***/ }),\n\n/***/ 212:\n/***/ ((module, __unused_webpack_exports, __webpack_require__) => {\n\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n  The implementation of the fcose layout algorithm\n*/\n\nvar assign = __webpack_require__(658);\nvar aux = __webpack_require__(548);\n\nvar _require = __webpack_require__(657),\n    spectralLayout = _require.spectralLayout;\n\nvar _require2 = __webpack_require__(816),\n    coseLayout = _require2.coseLayout;\n\nvar defaults = Object.freeze({\n\n  // 'draft', 'default' or 'proof' \n  // - 'draft' only applies spectral layout \n  // - 'default' improves the quality with subsequent CoSE layout (fast cooling rate)\n  // - 'proof' improves the quality with subsequent CoSE layout (slow cooling rate) \n  quality: \"default\",\n  // Use random node positions at beginning of layout\n  // if this is set to false, then quality option must be \"proof\"\n  randomize: true,\n  // Whether or not to animate the layout\n  animate: true,\n  // Duration of animation in ms, if enabled\n  animationDuration: 1000,\n  // Easing of animation, if enabled\n  animationEasing: undefined,\n  // Fit the viewport to the repositioned nodes\n  fit: true,\n  // Padding around layout\n  padding: 30,\n  // Whether to include labels in node dimensions. Valid in \"proof\" quality\n  nodeDimensionsIncludeLabels: false,\n  // Whether or not simple nodes (non-compound nodes) are of uniform dimensions\n  uniformNodeDimensions: false,\n  // Whether to pack disconnected components - valid only if randomize: true\n  packComponents: true,\n  // Layout step - all, transformed, enforced, cose - for debug purpose only\n  step: \"all\",\n\n  /* spectral layout options */\n\n  // False for random, true for greedy\n  samplingType: true,\n  // Sample size to construct distance matrix\n  sampleSize: 25,\n  // Separation amount between nodes\n  nodeSeparation: 75,\n  // Power iteration tolerance\n  piTol: 0.0000001,\n\n  /* CoSE layout options */\n\n  // Node repulsion (non overlapping) multiplier\n  nodeRepulsion: function nodeRepulsion(node) {\n    return 4500;\n  },\n  // Ideal edge (non nested) length\n  idealEdgeLength: function idealEdgeLength(edge) {\n    return 50;\n  },\n  // Divisor to compute edge forces\n  edgeElasticity: function edgeElasticity(edge) {\n    return 0.45;\n  },\n  // Nesting factor (multiplier) to compute ideal edge length for nested edges\n  nestingFactor: 0.1,\n  // Gravity force (constant)\n  gravity: 0.25,\n  // Maximum number of iterations to perform\n  numIter: 2500,\n  // For enabling tiling\n  tile: true,\n  // The function that specifies the criteria for comparing nodes while sorting them during tiling operation.\n  // Takes the node id as a parameter and the default tiling operation is perfomed when this option is not set.\n  tilingCompareBy: undefined,\n  // Represents the amount of the vertical space to put between the zero degree members during the tiling operation(can also be a function)\n  tilingPaddingVertical: 10,\n  // Represents the amount of the horizontal space to put between the zero degree members during the tiling operation(can also be a function)\n  tilingPaddingHorizontal: 10,\n  // Gravity range (constant) for compounds\n  gravityRangeCompound: 1.5,\n  // Gravity force (constant) for compounds\n  gravityCompound: 1.0,\n  // Gravity range (constant)\n  gravityRange: 3.8,\n  // Initial cooling factor for incremental layout  \n  initialEnergyOnIncremental: 0.3,\n\n  /* constraint options */\n\n  // Fix required nodes to predefined positions\n  // [{nodeId: 'n1', position: {x: 100, y: 200}, {...}]\n  fixedNodeConstraint: undefined,\n  // Align required nodes in vertical/horizontal direction\n  // {vertical: [['n1', 'n2')], ['n3', 'n4']], horizontal: ['n2', 'n4']}\n  alignmentConstraint: undefined,\n  // Place two nodes relatively in vertical/horizontal direction \n  // [{top: 'n1', bottom: 'n2', gap: 100}, {left: 'n3', right: 'n4', gap: 75}]\n  relativePlacementConstraint: undefined,\n\n  /* layout event callbacks */\n  ready: function ready() {}, // on layoutready\n  stop: function stop() {} // on layoutstop\n});\n\nvar Layout = function () {\n  function Layout(options) {\n    _classCallCheck(this, Layout);\n\n    this.options = assign({}, defaults, options);\n  }\n\n  _createClass(Layout, [{\n    key: 'run',\n    value: function run() {\n      var layout = this;\n      var options = this.options;\n      var cy = options.cy;\n      var eles = options.eles;\n\n      var spectralResult = [];\n      var xCoords = void 0;\n      var yCoords = void 0;\n      var coseResult = [];\n      var components = void 0;\n      var componentCenters = [];\n\n      // basic validity check for constraint inputs \n      if (options.fixedNodeConstraint && (!Array.isArray(options.fixedNodeConstraint) || options.fixedNodeConstraint.length == 0)) {\n        options.fixedNodeConstraint = undefined;\n      }\n\n      if (options.alignmentConstraint) {\n        if (options.alignmentConstraint.vertical && (!Array.isArray(options.alignmentConstraint.vertical) || options.alignmentConstraint.vertical.length == 0)) {\n          options.alignmentConstraint.vertical = undefined;\n        }\n        if (options.alignmentConstraint.horizontal && (!Array.isArray(options.alignmentConstraint.horizontal) || options.alignmentConstraint.horizontal.length == 0)) {\n          options.alignmentConstraint.horizontal = undefined;\n        }\n      }\n\n      if (options.relativePlacementConstraint && (!Array.isArray(options.relativePlacementConstraint) || options.relativePlacementConstraint.length == 0)) {\n        options.relativePlacementConstraint = undefined;\n      }\n\n      // if any constraint exists, set some options\n      var constraintExist = options.fixedNodeConstraint || options.alignmentConstraint || options.relativePlacementConstraint;\n      if (constraintExist) {\n        // constraints work with these options\n        options.tile = false;\n        options.packComponents = false;\n      }\n\n      // decide component packing is enabled or not\n      var layUtil = void 0;\n      var packingEnabled = false;\n      if (cy.layoutUtilities && options.packComponents) {\n        layUtil = cy.layoutUtilities(\"get\");\n        if (!layUtil) layUtil = cy.layoutUtilities();\n        packingEnabled = true;\n      }\n\n      if (eles.nodes().length > 0) {\n        // if packing is not enabled, perform layout on the whole graph\n        if (!packingEnabled) {\n          // store component center\n          var boundingBox = options.eles.boundingBox();\n          componentCenters.push({ x: boundingBox.x1 + boundingBox.w / 2, y: boundingBox.y1 + boundingBox.h / 2 });\n          // apply spectral layout\n          if (options.randomize) {\n            var result = spectralLayout(options);\n            spectralResult.push(result);\n          }\n          // apply cose layout as postprocessing\n          if (options.quality == \"default\" || options.quality == \"proof\") {\n            coseResult.push(coseLayout(options, spectralResult[0]));\n            aux.relocateComponent(componentCenters[0], coseResult[0], options); // relocate center to original position\n          } else {\n            aux.relocateComponent(componentCenters[0], spectralResult[0], options); // relocate center to original position\n          }\n        } else {\n          // packing is enabled\n          var topMostNodes = aux.getTopMostNodes(options.eles.nodes());\n          components = aux.connectComponents(cy, options.eles, topMostNodes);\n          // store component centers\n          components.forEach(function (component) {\n            var boundingBox = component.boundingBox();\n            componentCenters.push({ x: boundingBox.x1 + boundingBox.w / 2, y: boundingBox.y1 + boundingBox.h / 2 });\n          });\n\n          //send each component to spectral layout if randomized\n          if (options.randomize) {\n            components.forEach(function (component) {\n              options.eles = component;\n              spectralResult.push(spectralLayout(options));\n            });\n          }\n\n          if (options.quality == \"default\" || options.quality == \"proof\") {\n            var toBeTiledNodes = cy.collection();\n            if (options.tile) {\n              // behave nodes to be tiled as one component\n              var nodeIndexes = new Map();\n              var _xCoords = [];\n              var _yCoords = [];\n              var count = 0;\n              var tempSpectralResult = { nodeIndexes: nodeIndexes, xCoords: _xCoords, yCoords: _yCoords };\n              var indexesToBeDeleted = [];\n              components.forEach(function (component, index) {\n                if (component.edges().length == 0) {\n                  component.nodes().forEach(function (node, i) {\n                    toBeTiledNodes.merge(component.nodes()[i]);\n                    if (!node.isParent()) {\n                      tempSpectralResult.nodeIndexes.set(component.nodes()[i].id(), count++);\n                      tempSpectralResult.xCoords.push(component.nodes()[0].position().x);\n                      tempSpectralResult.yCoords.push(component.nodes()[0].position().y);\n                    }\n                  });\n                  indexesToBeDeleted.push(index);\n                }\n              });\n              if (toBeTiledNodes.length > 1) {\n                var _boundingBox = toBeTiledNodes.boundingBox();\n                componentCenters.push({ x: _boundingBox.x1 + _boundingBox.w / 2, y: _boundingBox.y1 + _boundingBox.h / 2 });\n                components.push(toBeTiledNodes);\n                spectralResult.push(tempSpectralResult);\n                for (var i = indexesToBeDeleted.length - 1; i >= 0; i--) {\n                  components.splice(indexesToBeDeleted[i], 1);\n                  spectralResult.splice(indexesToBeDeleted[i], 1);\n                  componentCenters.splice(indexesToBeDeleted[i], 1);\n                };\n              }\n            }\n            components.forEach(function (component, index) {\n              // send each component to cose layout\n              options.eles = component;\n              coseResult.push(coseLayout(options, spectralResult[index]));\n              aux.relocateComponent(componentCenters[index], coseResult[index], options); // relocate center to original position\n            });\n          } else {\n            components.forEach(function (component, index) {\n              aux.relocateComponent(componentCenters[index], spectralResult[index], options); // relocate center to original position\n            });\n          }\n\n          // packing\n          var componentsEvaluated = new Set();\n          if (components.length > 1) {\n            var subgraphs = [];\n            var hiddenEles = eles.filter(function (ele) {\n              return ele.css('display') == 'none';\n            });\n            components.forEach(function (component, index) {\n              var nodeIndexes = void 0;\n              if (options.quality == \"draft\") {\n                nodeIndexes = spectralResult[index].nodeIndexes;\n              }\n\n              if (component.nodes().not(hiddenEles).length > 0) {\n                var subgraph = {};\n                subgraph.edges = [];\n                subgraph.nodes = [];\n                var nodeIndex = void 0;\n                component.nodes().not(hiddenEles).forEach(function (node) {\n                  if (options.quality == \"draft\") {\n                    if (!node.isParent()) {\n                      nodeIndex = nodeIndexes.get(node.id());\n                      subgraph.nodes.push({ x: spectralResult[index].xCoords[nodeIndex] - node.boundingbox().w / 2, y: spectralResult[index].yCoords[nodeIndex] - node.boundingbox().h / 2, width: node.boundingbox().w, height: node.boundingbox().h });\n                    } else {\n                      var parentInfo = aux.calcBoundingBox(node, spectralResult[index].xCoords, spectralResult[index].yCoords, nodeIndexes);\n                      subgraph.nodes.push({ x: parentInfo.topLeftX, y: parentInfo.topLeftY, width: parentInfo.width, height: parentInfo.height });\n                    }\n                  } else {\n                    if (coseResult[index][node.id()]) {\n                      subgraph.nodes.push({ x: coseResult[index][node.id()].getLeft(), y: coseResult[index][node.id()].getTop(), width: coseResult[index][node.id()].getWidth(), height: coseResult[index][node.id()].getHeight() });\n                    }\n                  }\n                });\n                component.edges().forEach(function (edge) {\n                  var source = edge.source();\n                  var target = edge.target();\n                  if (source.css(\"display\") != \"none\" && target.css(\"display\") != \"none\") {\n                    if (options.quality == \"draft\") {\n                      var sourceNodeIndex = nodeIndexes.get(source.id());\n                      var targetNodeIndex = nodeIndexes.get(target.id());\n                      var sourceCenter = [];\n                      var targetCenter = [];\n                      if (source.isParent()) {\n                        var parentInfo = aux.calcBoundingBox(source, spectralResult[index].xCoords, spectralResult[index].yCoords, nodeIndexes);\n                        sourceCenter.push(parentInfo.topLeftX + parentInfo.width / 2);\n                        sourceCenter.push(parentInfo.topLeftY + parentInfo.height / 2);\n                      } else {\n                        sourceCenter.push(spectralResult[index].xCoords[sourceNodeIndex]);\n                        sourceCenter.push(spectralResult[index].yCoords[sourceNodeIndex]);\n                      }\n                      if (target.isParent()) {\n                        var _parentInfo = aux.calcBoundingBox(target, spectralResult[index].xCoords, spectralResult[index].yCoords, nodeIndexes);\n                        targetCenter.push(_parentInfo.topLeftX + _parentInfo.width / 2);\n                        targetCenter.push(_parentInfo.topLeftY + _parentInfo.height / 2);\n                      } else {\n                        targetCenter.push(spectralResult[index].xCoords[targetNodeIndex]);\n                        targetCenter.push(spectralResult[index].yCoords[targetNodeIndex]);\n                      }\n                      subgraph.edges.push({ startX: sourceCenter[0], startY: sourceCenter[1], endX: targetCenter[0], endY: targetCenter[1] });\n                    } else {\n                      if (coseResult[index][source.id()] && coseResult[index][target.id()]) {\n                        subgraph.edges.push({ startX: coseResult[index][source.id()].getCenterX(), startY: coseResult[index][source.id()].getCenterY(), endX: coseResult[index][target.id()].getCenterX(), endY: coseResult[index][target.id()].getCenterY() });\n                      }\n                    }\n                  }\n                });\n                if (subgraph.nodes.length > 0) {\n                  subgraphs.push(subgraph);\n                  componentsEvaluated.add(index);\n                }\n              }\n            });\n            var shiftResult = layUtil.packComponents(subgraphs, options.randomize).shifts;\n            if (options.quality == \"draft\") {\n              spectralResult.forEach(function (result, index) {\n                var newXCoords = result.xCoords.map(function (x) {\n                  return x + shiftResult[index].dx;\n                });\n                var newYCoords = result.yCoords.map(function (y) {\n                  return y + shiftResult[index].dy;\n                });\n                result.xCoords = newXCoords;\n                result.yCoords = newYCoords;\n              });\n            } else {\n              var _count = 0;\n              componentsEvaluated.forEach(function (index) {\n                Object.keys(coseResult[index]).forEach(function (item) {\n                  var nodeRectangle = coseResult[index][item];\n                  nodeRectangle.setCenter(nodeRectangle.getCenterX() + shiftResult[_count].dx, nodeRectangle.getCenterY() + shiftResult[_count].dy);\n                });\n                _count++;\n              });\n            }\n          }\n        }\n      }\n\n      // get each element's calculated position\n      var getPositions = function getPositions(ele, i) {\n        if (options.quality == \"default\" || options.quality == \"proof\") {\n          if (typeof ele === \"number\") {\n            ele = i;\n          }\n          var pos = void 0;\n          var node = void 0;\n          var theId = ele.data('id');\n          coseResult.forEach(function (result) {\n            if (theId in result) {\n              pos = { x: result[theId].getRect().getCenterX(), y: result[theId].getRect().getCenterY() };\n              node = result[theId];\n            }\n          });\n          if (options.nodeDimensionsIncludeLabels) {\n            if (node.labelWidth) {\n              if (node.labelPosHorizontal == \"left\") {\n                pos.x += node.labelWidth / 2;\n              } else if (node.labelPosHorizontal == \"right\") {\n                pos.x -= node.labelWidth / 2;\n              }\n            }\n            if (node.labelHeight) {\n              if (node.labelPosVertical == \"top\") {\n                pos.y += node.labelHeight / 2;\n              } else if (node.labelPosVertical == \"bottom\") {\n                pos.y -= node.labelHeight / 2;\n              }\n            }\n          }\n          if (pos == undefined) pos = { x: ele.position(\"x\"), y: ele.position(\"y\") };\n          return {\n            x: pos.x,\n            y: pos.y\n          };\n        } else {\n          var _pos = void 0;\n          spectralResult.forEach(function (result) {\n            var index = result.nodeIndexes.get(ele.id());\n            if (index != undefined) {\n              _pos = { x: result.xCoords[index], y: result.yCoords[index] };\n            }\n          });\n          if (_pos == undefined) _pos = { x: ele.position(\"x\"), y: ele.position(\"y\") };\n          return {\n            x: _pos.x,\n            y: _pos.y\n          };\n        }\n      };\n\n      // quality = \"draft\" and randomize = false are contradictive so in that case positions don't change\n      if (options.quality == \"default\" || options.quality == \"proof\" || options.randomize) {\n        // transfer calculated positions to nodes (positions of only simple nodes are evaluated, compounds are positioned automatically)\n        var parentsWithoutChildren = aux.calcParentsWithoutChildren(cy, eles);\n        var _hiddenEles = eles.filter(function (ele) {\n          return ele.css('display') == 'none';\n        });\n        options.eles = eles.not(_hiddenEles);\n\n        eles.nodes().not(\":parent\").not(_hiddenEles).layoutPositions(layout, options, getPositions);\n\n        if (parentsWithoutChildren.length > 0) {\n          parentsWithoutChildren.forEach(function (ele) {\n            ele.position(getPositions(ele));\n          });\n        }\n      } else {\n        console.log(\"If randomize option is set to false, then quality option must be 'default' or 'proof'.\");\n      }\n    }\n  }]);\n\n  return Layout;\n}();\n\nmodule.exports = Layout;\n\n/***/ }),\n\n/***/ 657:\n/***/ ((module, __unused_webpack_exports, __webpack_require__) => {\n\n\n\n/**\n  The implementation of the spectral layout that is the first part of the fcose layout algorithm\n*/\n\nvar aux = __webpack_require__(548);\nvar Matrix = __webpack_require__(140).layoutBase.Matrix;\nvar SVD = __webpack_require__(140).layoutBase.SVD;\n\n// main function that spectral layout is processed\nvar spectralLayout = function spectralLayout(options) {\n\n  var cy = options.cy;\n  var eles = options.eles;\n  var nodes = eles.nodes();\n  var parentNodes = eles.nodes(\":parent\");\n\n  var dummyNodes = new Map(); // map to keep dummy nodes and their neighbors\n  var nodeIndexes = new Map(); // map to keep indexes to nodes\n  var parentChildMap = new Map(); // mapping btw. compound and its representative node \n  var allNodesNeighborhood = []; // array to keep neighborhood of all nodes\n  var xCoords = [];\n  var yCoords = [];\n\n  var samplesColumn = []; // sampled vertices\n  var minDistancesColumn = [];\n  var C = []; // column sampling matrix\n  var PHI = []; // intersection of column and row sampling matrices \n  var INV = []; // inverse of PHI \n\n  var firstSample = void 0; // the first sampled node\n  var nodeSize = void 0;\n\n  var infinity = 100000000;\n  var small = 0.000000001;\n\n  var piTol = options.piTol;\n  var samplingType = options.samplingType; // false for random, true for greedy\n  var nodeSeparation = options.nodeSeparation;\n  var sampleSize = void 0;\n\n  /**** Spectral-preprocessing functions ****/\n\n  /**** Spectral layout functions ****/\n\n  // determine which columns to be sampled\n  var randomSampleCR = function randomSampleCR() {\n    var sample = 0;\n    var count = 0;\n    var flag = false;\n\n    while (count < sampleSize) {\n      sample = Math.floor(Math.random() * nodeSize);\n\n      flag = false;\n      for (var i = 0; i < count; i++) {\n        if (samplesColumn[i] == sample) {\n          flag = true;\n          break;\n        }\n      }\n\n      if (!flag) {\n        samplesColumn[count] = sample;\n        count++;\n      } else {\n        continue;\n      }\n    }\n  };\n\n  // takes the index of the node(pivot) to initiate BFS as a parameter\n  var BFS = function BFS(pivot, index, samplingMethod) {\n    var path = []; // the front of the path\n    var front = 0; // the back of the path\n    var back = 0;\n    var current = 0;\n    var temp = void 0;\n    var distance = [];\n\n    var max_dist = 0; // the furthest node to be returned\n    var max_ind = 1;\n\n    for (var i = 0; i < nodeSize; i++) {\n      distance[i] = infinity;\n    }\n\n    path[back] = pivot;\n    distance[pivot] = 0;\n\n    while (back >= front) {\n      current = path[front++];\n      var neighbors = allNodesNeighborhood[current];\n      for (var _i = 0; _i < neighbors.length; _i++) {\n        temp = nodeIndexes.get(neighbors[_i]);\n        if (distance[temp] == infinity) {\n          distance[temp] = distance[current] + 1;\n          path[++back] = temp;\n        }\n      }\n      C[current][index] = distance[current] * nodeSeparation;\n    }\n\n    if (samplingMethod) {\n      for (var _i2 = 0; _i2 < nodeSize; _i2++) {\n        if (C[_i2][index] < minDistancesColumn[_i2]) minDistancesColumn[_i2] = C[_i2][index];\n      }\n\n      for (var _i3 = 0; _i3 < nodeSize; _i3++) {\n        if (minDistancesColumn[_i3] > max_dist) {\n          max_dist = minDistancesColumn[_i3];\n          max_ind = _i3;\n        }\n      }\n    }\n    return max_ind;\n  };\n\n  // apply BFS to all nodes or selected samples\n  var allBFS = function allBFS(samplingMethod) {\n\n    var sample = void 0;\n\n    if (!samplingMethod) {\n      randomSampleCR();\n\n      // call BFS\n      for (var i = 0; i < sampleSize; i++) {\n        BFS(samplesColumn[i], i, samplingMethod, false);\n      }\n    } else {\n      sample = Math.floor(Math.random() * nodeSize);\n      firstSample = sample;\n\n      for (var _i4 = 0; _i4 < nodeSize; _i4++) {\n        minDistancesColumn[_i4] = infinity;\n      }\n\n      for (var _i5 = 0; _i5 < sampleSize; _i5++) {\n        samplesColumn[_i5] = sample;\n        sample = BFS(sample, _i5, samplingMethod);\n      }\n    }\n\n    // form the squared distances for C\n    for (var _i6 = 0; _i6 < nodeSize; _i6++) {\n      for (var j = 0; j < sampleSize; j++) {\n        C[_i6][j] *= C[_i6][j];\n      }\n    }\n\n    // form PHI\n    for (var _i7 = 0; _i7 < sampleSize; _i7++) {\n      PHI[_i7] = [];\n    }\n\n    for (var _i8 = 0; _i8 < sampleSize; _i8++) {\n      for (var _j = 0; _j < sampleSize; _j++) {\n        PHI[_i8][_j] = C[samplesColumn[_j]][_i8];\n      }\n    }\n  };\n\n  // perform the SVD algorithm and apply a regularization step\n  var sample = function sample() {\n\n    var SVDResult = SVD.svd(PHI);\n\n    var a_q = SVDResult.S;\n    var a_u = SVDResult.U;\n    var a_v = SVDResult.V;\n\n    var max_s = a_q[0] * a_q[0] * a_q[0];\n\n    var a_Sig = [];\n\n    //  regularization\n    for (var i = 0; i < sampleSize; i++) {\n      a_Sig[i] = [];\n      for (var j = 0; j < sampleSize; j++) {\n        a_Sig[i][j] = 0;\n        if (i == j) {\n          a_Sig[i][j] = a_q[i] / (a_q[i] * a_q[i] + max_s / (a_q[i] * a_q[i]));\n        }\n      }\n    }\n\n    INV = Matrix.multMat(Matrix.multMat(a_v, a_Sig), Matrix.transpose(a_u));\n  };\n\n  // calculate final coordinates \n  var powerIteration = function powerIteration() {\n    // two largest eigenvalues\n    var theta1 = void 0;\n    var theta2 = void 0;\n\n    // initial guesses for eigenvectors\n    var Y1 = [];\n    var Y2 = [];\n\n    var V1 = [];\n    var V2 = [];\n\n    for (var i = 0; i < nodeSize; i++) {\n      Y1[i] = Math.random();\n      Y2[i] = Math.random();\n    }\n\n    Y1 = Matrix.normalize(Y1);\n    Y2 = Matrix.normalize(Y2);\n\n    var count = 0;\n    // to keep track of the improvement ratio in power iteration\n    var current = small;\n    var previous = small;\n\n    var temp = void 0;\n\n    while (true) {\n      count++;\n\n      for (var _i9 = 0; _i9 < nodeSize; _i9++) {\n        V1[_i9] = Y1[_i9];\n      }\n\n      Y1 = Matrix.multGamma(Matrix.multL(Matrix.multGamma(V1), C, INV));\n      theta1 = Matrix.dotProduct(V1, Y1);\n      Y1 = Matrix.normalize(Y1);\n\n      current = Matrix.dotProduct(V1, Y1);\n\n      temp = Math.abs(current / previous);\n\n      if (temp <= 1 + piTol && temp >= 1) {\n        break;\n      }\n\n      previous = current;\n    }\n\n    for (var _i10 = 0; _i10 < nodeSize; _i10++) {\n      V1[_i10] = Y1[_i10];\n    }\n\n    count = 0;\n    previous = small;\n    while (true) {\n      count++;\n\n      for (var _i11 = 0; _i11 < nodeSize; _i11++) {\n        V2[_i11] = Y2[_i11];\n      }\n\n      V2 = Matrix.minusOp(V2, Matrix.multCons(V1, Matrix.dotProduct(V1, V2)));\n      Y2 = Matrix.multGamma(Matrix.multL(Matrix.multGamma(V2), C, INV));\n      theta2 = Matrix.dotProduct(V2, Y2);\n      Y2 = Matrix.normalize(Y2);\n\n      current = Matrix.dotProduct(V2, Y2);\n\n      temp = Math.abs(current / previous);\n\n      if (temp <= 1 + piTol && temp >= 1) {\n        break;\n      }\n\n      previous = current;\n    }\n\n    for (var _i12 = 0; _i12 < nodeSize; _i12++) {\n      V2[_i12] = Y2[_i12];\n    }\n\n    // theta1 now contains dominant eigenvalue\n    // theta2 now contains the second-largest eigenvalue\n    // V1 now contains theta1's eigenvector\n    // V2 now contains theta2's eigenvector\n\n    //populate the two vectors\n    xCoords = Matrix.multCons(V1, Math.sqrt(Math.abs(theta1)));\n    yCoords = Matrix.multCons(V2, Math.sqrt(Math.abs(theta2)));\n  };\n\n  /**** Preparation for spectral layout (Preprocessing) ****/\n\n  // connect disconnected components (first top level, then inside of each compound node)\n  aux.connectComponents(cy, eles, aux.getTopMostNodes(nodes), dummyNodes);\n\n  parentNodes.forEach(function (ele) {\n    aux.connectComponents(cy, eles, aux.getTopMostNodes(ele.descendants().intersection(eles)), dummyNodes);\n  });\n\n  // assign indexes to nodes (first real, then dummy nodes)\n  var index = 0;\n  for (var i = 0; i < nodes.length; i++) {\n    if (!nodes[i].isParent()) {\n      nodeIndexes.set(nodes[i].id(), index++);\n    }\n  }\n\n  var _iteratorNormalCompletion = true;\n  var _didIteratorError = false;\n  var _iteratorError = undefined;\n\n  try {\n    for (var _iterator = dummyNodes.keys()[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n      var key = _step.value;\n\n      nodeIndexes.set(key, index++);\n    }\n\n    // instantiate the neighborhood matrix\n  } catch (err) {\n    _didIteratorError = true;\n    _iteratorError = err;\n  } finally {\n    try {\n      if (!_iteratorNormalCompletion && _iterator.return) {\n        _iterator.return();\n      }\n    } finally {\n      if (_didIteratorError) {\n        throw _iteratorError;\n      }\n    }\n  }\n\n  for (var _i13 = 0; _i13 < nodeIndexes.size; _i13++) {\n    allNodesNeighborhood[_i13] = [];\n  }\n\n  // form a parent-child map to keep representative node of each compound node  \n  parentNodes.forEach(function (ele) {\n    var children = ele.children().intersection(eles);\n\n    //      let random = 0;\n    while (children.nodes(\":childless\").length == 0) {\n      //        random = Math.floor(Math.random() * children.nodes().length); // if all children are compound then proceed randomly\n      children = children.nodes()[0].children().intersection(eles);\n    }\n    //  select the representative node - we can apply different methods here\n    //      random = Math.floor(Math.random() * children.nodes(\":childless\").length);\n    var index = 0;\n    var min = children.nodes(\":childless\")[0].connectedEdges().length;\n    children.nodes(\":childless\").forEach(function (ele2, i) {\n      if (ele2.connectedEdges().length < min) {\n        min = ele2.connectedEdges().length;\n        index = i;\n      }\n    });\n    parentChildMap.set(ele.id(), children.nodes(\":childless\")[index].id());\n  });\n\n  // add neighborhood relations (first real, then dummy nodes)\n  nodes.forEach(function (ele) {\n    var eleIndex = void 0;\n\n    if (ele.isParent()) eleIndex = nodeIndexes.get(parentChildMap.get(ele.id()));else eleIndex = nodeIndexes.get(ele.id());\n\n    ele.neighborhood().nodes().forEach(function (node) {\n      if (eles.intersection(ele.edgesWith(node)).length > 0) {\n        if (node.isParent()) allNodesNeighborhood[eleIndex].push(parentChildMap.get(node.id()));else allNodesNeighborhood[eleIndex].push(node.id());\n      }\n    });\n  });\n\n  var _loop = function _loop(_key) {\n    var eleIndex = nodeIndexes.get(_key);\n    var disconnectedId = void 0;\n    dummyNodes.get(_key).forEach(function (id) {\n      if (cy.getElementById(id).isParent()) disconnectedId = parentChildMap.get(id);else disconnectedId = id;\n\n      allNodesNeighborhood[eleIndex].push(disconnectedId);\n      allNodesNeighborhood[nodeIndexes.get(disconnectedId)].push(_key);\n    });\n  };\n\n  var _iteratorNormalCompletion2 = true;\n  var _didIteratorError2 = false;\n  var _iteratorError2 = undefined;\n\n  try {\n    for (var _iterator2 = dummyNodes.keys()[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n      var _key = _step2.value;\n\n      _loop(_key);\n    }\n\n    // nodeSize now only considers the size of transformed graph\n  } catch (err) {\n    _didIteratorError2 = true;\n    _iteratorError2 = err;\n  } finally {\n    try {\n      if (!_iteratorNormalCompletion2 && _iterator2.return) {\n        _iterator2.return();\n      }\n    } finally {\n      if (_didIteratorError2) {\n        throw _iteratorError2;\n      }\n    }\n  }\n\n  nodeSize = nodeIndexes.size;\n\n  var spectralResult = void 0;\n\n  // If number of nodes in transformed graph is 1 or 2, either SVD or powerIteration causes problem\n  // So skip spectral and layout the graph with cose\n  if (nodeSize > 2) {\n    // if # of nodes in transformed graph is smaller than sample size,\n    // then use # of nodes as sample size\n    sampleSize = nodeSize < options.sampleSize ? nodeSize : options.sampleSize;\n\n    // instantiates the partial matrices that will be used in spectral layout\n    for (var _i14 = 0; _i14 < nodeSize; _i14++) {\n      C[_i14] = [];\n    }\n    for (var _i15 = 0; _i15 < sampleSize; _i15++) {\n      INV[_i15] = [];\n    }\n\n    /**** Apply spectral layout ****/\n\n    if (options.quality == \"draft\" || options.step == \"all\") {\n      allBFS(samplingType);\n      sample();\n      powerIteration();\n\n      spectralResult = { nodeIndexes: nodeIndexes, xCoords: xCoords, yCoords: yCoords };\n    } else {\n      nodeIndexes.forEach(function (value, key) {\n        xCoords.push(cy.getElementById(key).position(\"x\"));\n        yCoords.push(cy.getElementById(key).position(\"y\"));\n      });\n      spectralResult = { nodeIndexes: nodeIndexes, xCoords: xCoords, yCoords: yCoords };\n    }\n    return spectralResult;\n  } else {\n    var iterator = nodeIndexes.keys();\n    var firstNode = cy.getElementById(iterator.next().value);\n    var firstNodePos = firstNode.position();\n    var firstNodeWidth = firstNode.outerWidth();\n    xCoords.push(firstNodePos.x);\n    yCoords.push(firstNodePos.y);\n    if (nodeSize == 2) {\n      var secondNode = cy.getElementById(iterator.next().value);\n      var secondNodeWidth = secondNode.outerWidth();\n      xCoords.push(firstNodePos.x + firstNodeWidth / 2 + secondNodeWidth / 2 + options.idealEdgeLength);\n      yCoords.push(firstNodePos.y);\n    }\n\n    spectralResult = { nodeIndexes: nodeIndexes, xCoords: xCoords, yCoords: yCoords };\n    return spectralResult;\n  }\n};\n\nmodule.exports = { spectralLayout: spectralLayout };\n\n/***/ }),\n\n/***/ 579:\n/***/ ((module, __unused_webpack_exports, __webpack_require__) => {\n\n\n\nvar impl = __webpack_require__(212);\n\n// registers the extension on a cytoscape lib ref\nvar register = function register(cytoscape) {\n  if (!cytoscape) {\n    return;\n  } // can't register if cytoscape unspecified\n\n  cytoscape('layout', 'fcose', impl); // register with cytoscape.js\n};\n\nif (typeof cytoscape !== 'undefined') {\n  // expose to global cytoscape (i.e. window.cytoscape)\n  register(cytoscape);\n}\n\nmodule.exports = register;\n\n/***/ }),\n\n/***/ 140:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__140__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t\n/******/ \t// startup\n/******/ \t// Load entry module and return exports\n/******/ \t// This entry module is referenced by other modules so it can't be inlined\n/******/ \tvar __webpack_exports__ = __webpack_require__(579);\n/******/ \t\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"layout-base\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"layout-base\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"coseBase\"] = factory(require(\"layout-base\"));\n\telse\n\t\troot[\"coseBase\"] = factory(root[\"layoutBase\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__551__) {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 45:\n/***/ ((module, __unused_webpack_exports, __webpack_require__) => {\n\n\n\nvar coseBase = {};\n\ncoseBase.layoutBase = __webpack_require__(551);\ncoseBase.CoSEConstants = __webpack_require__(806);\ncoseBase.CoSEEdge = __webpack_require__(767);\ncoseBase.CoSEGraph = __webpack_require__(880);\ncoseBase.CoSEGraphManager = __webpack_require__(578);\ncoseBase.CoSELayout = __webpack_require__(765);\ncoseBase.CoSENode = __webpack_require__(991);\ncoseBase.ConstraintHandler = __webpack_require__(902);\n\nmodule.exports = coseBase;\n\n/***/ }),\n\n/***/ 806:\n/***/ ((module, __unused_webpack_exports, __webpack_require__) => {\n\n\n\nvar FDLayoutConstants = __webpack_require__(551).FDLayoutConstants;\n\nfunction CoSEConstants() {}\n\n//CoSEConstants inherits static props in FDLayoutConstants\nfor (var prop in FDLayoutConstants) {\n  CoSEConstants[prop] = FDLayoutConstants[prop];\n}\n\nCoSEConstants.DEFAULT_USE_MULTI_LEVEL_SCALING = false;\nCoSEConstants.DEFAULT_RADIAL_SEPARATION = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\nCoSEConstants.DEFAULT_COMPONENT_SEPERATION = 60;\nCoSEConstants.TILE = true;\nCoSEConstants.TILING_PADDING_VERTICAL = 10;\nCoSEConstants.TILING_PADDING_HORIZONTAL = 10;\nCoSEConstants.TRANSFORM_ON_CONSTRAINT_HANDLING = true;\nCoSEConstants.ENFORCE_CONSTRAINTS = true;\nCoSEConstants.APPLY_LAYOUT = true;\nCoSEConstants.RELAX_MOVEMENT_ON_CONSTRAINTS = true;\nCoSEConstants.TREE_REDUCTION_ON_INCREMENTAL = true; // this should be set to false if there will be a constraint\n// This constant is for differentiating whether actual layout algorithm that uses cose-base wants to apply only incremental layout or \n// an incremental layout on top of a randomized layout. If it is only incremental layout, then this constant should be true.\nCoSEConstants.PURE_INCREMENTAL = CoSEConstants.DEFAULT_INCREMENTAL;\n\nmodule.exports = CoSEConstants;\n\n/***/ }),\n\n/***/ 767:\n/***/ ((module, __unused_webpack_exports, __webpack_require__) => {\n\n\n\nvar FDLayoutEdge = __webpack_require__(551).FDLayoutEdge;\n\nfunction CoSEEdge(source, target, vEdge) {\n  FDLayoutEdge.call(this, source, target, vEdge);\n}\n\nCoSEEdge.prototype = Object.create(FDLayoutEdge.prototype);\nfor (var prop in FDLayoutEdge) {\n  CoSEEdge[prop] = FDLayoutEdge[prop];\n}\n\nmodule.exports = CoSEEdge;\n\n/***/ }),\n\n/***/ 880:\n/***/ ((module, __unused_webpack_exports, __webpack_require__) => {\n\n\n\nvar LGraph = __webpack_require__(551).LGraph;\n\nfunction CoSEGraph(parent, graphMgr, vGraph) {\n  LGraph.call(this, parent, graphMgr, vGraph);\n}\n\nCoSEGraph.prototype = Object.create(LGraph.prototype);\nfor (var prop in LGraph) {\n  CoSEGraph[prop] = LGraph[prop];\n}\n\nmodule.exports = CoSEGraph;\n\n/***/ }),\n\n/***/ 578:\n/***/ ((module, __unused_webpack_exports, __webpack_require__) => {\n\n\n\nvar LGraphManager = __webpack_require__(551).LGraphManager;\n\nfunction CoSEGraphManager(layout) {\n  LGraphManager.call(this, layout);\n}\n\nCoSEGraphManager.prototype = Object.create(LGraphManager.prototype);\nfor (var prop in LGraphManager) {\n  CoSEGraphManager[prop] = LGraphManager[prop];\n}\n\nmodule.exports = CoSEGraphManager;\n\n/***/ }),\n\n/***/ 765:\n/***/ ((module, __unused_webpack_exports, __webpack_require__) => {\n\n\n\nvar FDLayout = __webpack_require__(551).FDLayout;\nvar CoSEGraphManager = __webpack_require__(578);\nvar CoSEGraph = __webpack_require__(880);\nvar CoSENode = __webpack_require__(991);\nvar CoSEEdge = __webpack_require__(767);\nvar CoSEConstants = __webpack_require__(806);\nvar ConstraintHandler = __webpack_require__(902);\nvar FDLayoutConstants = __webpack_require__(551).FDLayoutConstants;\nvar LayoutConstants = __webpack_require__(551).LayoutConstants;\nvar Point = __webpack_require__(551).Point;\nvar PointD = __webpack_require__(551).PointD;\nvar DimensionD = __webpack_require__(551).DimensionD;\nvar Layout = __webpack_require__(551).Layout;\nvar Integer = __webpack_require__(551).Integer;\nvar IGeometry = __webpack_require__(551).IGeometry;\nvar LGraph = __webpack_require__(551).LGraph;\nvar Transform = __webpack_require__(551).Transform;\nvar LinkedList = __webpack_require__(551).LinkedList;\n\nfunction CoSELayout() {\n  FDLayout.call(this);\n\n  this.toBeTiled = {}; // Memorize if a node is to be tiled or is tiled\n  this.constraints = {}; // keep layout constraints\n}\n\nCoSELayout.prototype = Object.create(FDLayout.prototype);\n\nfor (var prop in FDLayout) {\n  CoSELayout[prop] = FDLayout[prop];\n}\n\nCoSELayout.prototype.newGraphManager = function () {\n  var gm = new CoSEGraphManager(this);\n  this.graphManager = gm;\n  return gm;\n};\n\nCoSELayout.prototype.newGraph = function (vGraph) {\n  return new CoSEGraph(null, this.graphManager, vGraph);\n};\n\nCoSELayout.prototype.newNode = function (vNode) {\n  return new CoSENode(this.graphManager, vNode);\n};\n\nCoSELayout.prototype.newEdge = function (vEdge) {\n  return new CoSEEdge(null, null, vEdge);\n};\n\nCoSELayout.prototype.initParameters = function () {\n  FDLayout.prototype.initParameters.call(this, arguments);\n  if (!this.isSubLayout) {\n    if (CoSEConstants.DEFAULT_EDGE_LENGTH < 10) {\n      this.idealEdgeLength = 10;\n    } else {\n      this.idealEdgeLength = CoSEConstants.DEFAULT_EDGE_LENGTH;\n    }\n\n    this.useSmartIdealEdgeLengthCalculation = CoSEConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION;\n    this.gravityConstant = FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH;\n    this.compoundGravityConstant = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH;\n    this.gravityRangeFactor = FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR;\n    this.compoundGravityRangeFactor = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR;\n\n    // variables for tree reduction support\n    this.prunedNodesAll = [];\n    this.growTreeIterations = 0;\n    this.afterGrowthIterations = 0;\n    this.isTreeGrowing = false;\n    this.isGrowthFinished = false;\n  }\n};\n\n// This method is used to set CoSE related parameters used by spring embedder.\nCoSELayout.prototype.initSpringEmbedder = function () {\n  FDLayout.prototype.initSpringEmbedder.call(this);\n\n  // variables for cooling\n  this.coolingCycle = 0;\n  this.maxCoolingCycle = this.maxIterations / FDLayoutConstants.CONVERGENCE_CHECK_PERIOD;\n  this.finalTemperature = 0.04;\n  this.coolingAdjuster = 1;\n};\n\nCoSELayout.prototype.layout = function () {\n  var createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n  if (createBendsAsNeeded) {\n    this.createBendpoints();\n    this.graphManager.resetAllEdges();\n  }\n\n  this.level = 0;\n  return this.classicLayout();\n};\n\nCoSELayout.prototype.classicLayout = function () {\n  this.nodesWithGravity = this.calculateNodesToApplyGravitationTo();\n  this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity);\n  this.calcNoOfChildrenForAllNodes();\n  this.graphManager.calcLowestCommonAncestors();\n  this.graphManager.calcInclusionTreeDepths();\n  this.graphManager.getRoot().calcEstimatedSize();\n  this.calcIdealEdgeLengths();\n\n  if (!this.incremental) {\n    var forest = this.getFlatForest();\n\n    // The graph associated with this layout is flat and a forest\n    if (forest.length > 0) {\n      this.positionNodesRadially(forest);\n    }\n    // The graph associated with this layout is not flat or a forest\n    else {\n        // Reduce the trees when incremental mode is not enabled and graph is not a forest \n        this.reduceTrees();\n        // Update nodes that gravity will be applied\n        this.graphManager.resetAllNodesToApplyGravitation();\n        var allNodes = new Set(this.getAllNodes());\n        var intersection = this.nodesWithGravity.filter(function (x) {\n          return allNodes.has(x);\n        });\n        this.graphManager.setAllNodesToApplyGravitation(intersection);\n\n        this.positionNodesRandomly();\n      }\n  } else {\n    if (CoSEConstants.TREE_REDUCTION_ON_INCREMENTAL) {\n      // Reduce the trees in incremental mode if only this constant is set to true \n      this.reduceTrees();\n      // Update nodes that gravity will be applied\n      this.graphManager.resetAllNodesToApplyGravitation();\n      var allNodes = new Set(this.getAllNodes());\n      var intersection = this.nodesWithGravity.filter(function (x) {\n        return allNodes.has(x);\n      });\n      this.graphManager.setAllNodesToApplyGravitation(intersection);\n    }\n  }\n\n  if (Object.keys(this.constraints).length > 0) {\n    ConstraintHandler.handleConstraints(this);\n    this.initConstraintVariables();\n  }\n\n  this.initSpringEmbedder();\n  if (CoSEConstants.APPLY_LAYOUT) {\n    this.runSpringEmbedder();\n  }\n\n  return true;\n};\n\nCoSELayout.prototype.tick = function () {\n  this.totalIterations++;\n\n  if (this.totalIterations === this.maxIterations && !this.isTreeGrowing && !this.isGrowthFinished) {\n    if (this.prunedNodesAll.length > 0) {\n      this.isTreeGrowing = true;\n    } else {\n      return true;\n    }\n  }\n\n  if (this.totalIterations % FDLayoutConstants.CONVERGENCE_CHECK_PERIOD == 0 && !this.isTreeGrowing && !this.isGrowthFinished) {\n    if (this.isConverged()) {\n      if (this.prunedNodesAll.length > 0) {\n        this.isTreeGrowing = true;\n      } else {\n        return true;\n      }\n    }\n\n    this.coolingCycle++;\n\n    if (this.layoutQuality == 0) {\n      // quality - \"draft\"\n      this.coolingAdjuster = this.coolingCycle;\n    } else if (this.layoutQuality == 1) {\n      // quality - \"default\"\n      this.coolingAdjuster = this.coolingCycle / 3;\n    }\n\n    // cooling schedule is based on http://www.btluke.com/simanf1.html -> cooling schedule 3\n    this.coolingFactor = Math.max(this.initialCoolingFactor - Math.pow(this.coolingCycle, Math.log(100 * (this.initialCoolingFactor - this.finalTemperature)) / Math.log(this.maxCoolingCycle)) / 100 * this.coolingAdjuster, this.finalTemperature);\n    this.animationPeriod = Math.ceil(this.initialAnimationPeriod * Math.sqrt(this.coolingFactor));\n  }\n  // Operations while tree is growing again \n  if (this.isTreeGrowing) {\n    if (this.growTreeIterations % 10 == 0) {\n      if (this.prunedNodesAll.length > 0) {\n        this.graphManager.updateBounds();\n        this.updateGrid();\n        this.growTree(this.prunedNodesAll);\n        // Update nodes that gravity will be applied\n        this.graphManager.resetAllNodesToApplyGravitation();\n        var allNodes = new Set(this.getAllNodes());\n        var intersection = this.nodesWithGravity.filter(function (x) {\n          return allNodes.has(x);\n        });\n        this.graphManager.setAllNodesToApplyGravitation(intersection);\n\n        this.graphManager.updateBounds();\n        this.updateGrid();\n        if (CoSEConstants.PURE_INCREMENTAL) this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL / 2;else this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n      } else {\n        this.isTreeGrowing = false;\n        this.isGrowthFinished = true;\n      }\n    }\n    this.growTreeIterations++;\n  }\n  // Operations after growth is finished\n  if (this.isGrowthFinished) {\n    if (this.isConverged()) {\n      return true;\n    }\n    if (this.afterGrowthIterations % 10 == 0) {\n      this.graphManager.updateBounds();\n      this.updateGrid();\n    }\n    if (CoSEConstants.PURE_INCREMENTAL) this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL / 2 * ((100 - this.afterGrowthIterations) / 100);else this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL * ((100 - this.afterGrowthIterations) / 100);\n    this.afterGrowthIterations++;\n  }\n\n  var gridUpdateAllowed = !this.isTreeGrowing && !this.isGrowthFinished;\n  var forceToNodeSurroundingUpdate = this.growTreeIterations % 10 == 1 && this.isTreeGrowing || this.afterGrowthIterations % 10 == 1 && this.isGrowthFinished;\n\n  this.totalDisplacement = 0;\n  this.graphManager.updateBounds();\n  this.calcSpringForces();\n  this.calcRepulsionForces(gridUpdateAllowed, forceToNodeSurroundingUpdate);\n  this.calcGravitationalForces();\n  this.moveNodes();\n  this.animate();\n\n  return false; // Layout is not ended yet return false\n};\n\nCoSELayout.prototype.getPositionsData = function () {\n  var allNodes = this.graphManager.getAllNodes();\n  var pData = {};\n  for (var i = 0; i < allNodes.length; i++) {\n    var rect = allNodes[i].rect;\n    var id = allNodes[i].id;\n    pData[id] = {\n      id: id,\n      x: rect.getCenterX(),\n      y: rect.getCenterY(),\n      w: rect.width,\n      h: rect.height\n    };\n  }\n\n  return pData;\n};\n\nCoSELayout.prototype.runSpringEmbedder = function () {\n  this.initialAnimationPeriod = 25;\n  this.animationPeriod = this.initialAnimationPeriod;\n  var layoutEnded = false;\n\n  // If aminate option is 'during' signal that layout is supposed to start iterating\n  if (FDLayoutConstants.ANIMATE === 'during') {\n    this.emit('layoutstarted');\n  } else {\n    // If aminate option is 'during' tick() function will be called on index.js\n    while (!layoutEnded) {\n      layoutEnded = this.tick();\n    }\n\n    this.graphManager.updateBounds();\n  }\n};\n\n// overrides moveNodes method in FDLayout\nCoSELayout.prototype.moveNodes = function () {\n  var lNodes = this.getAllNodes();\n  var node;\n\n  // calculate displacement for each node \n  for (var i = 0; i < lNodes.length; i++) {\n    node = lNodes[i];\n    node.calculateDisplacement();\n  }\n\n  if (Object.keys(this.constraints).length > 0) {\n    this.updateDisplacements();\n  }\n\n  // move each node\n  for (var i = 0; i < lNodes.length; i++) {\n    node = lNodes[i];\n    node.move();\n  }\n};\n\n// constraint related methods: initConstraintVariables and updateDisplacements\n\n// initialize constraint related variables\nCoSELayout.prototype.initConstraintVariables = function () {\n  var self = this;\n  this.idToNodeMap = new Map();\n  this.fixedNodeSet = new Set();\n\n  var allNodes = this.graphManager.getAllNodes();\n\n  // fill idToNodeMap\n  for (var i = 0; i < allNodes.length; i++) {\n    var node = allNodes[i];\n    this.idToNodeMap.set(node.id, node);\n  }\n\n  // calculate fixed node weight for given compound node\n  var calculateCompoundWeight = function calculateCompoundWeight(compoundNode) {\n    var nodes = compoundNode.getChild().getNodes();\n    var node;\n    var fixedNodeWeight = 0;\n    for (var i = 0; i < nodes.length; i++) {\n      node = nodes[i];\n      if (node.getChild() == null) {\n        if (self.fixedNodeSet.has(node.id)) {\n          fixedNodeWeight += 100;\n        }\n      } else {\n        fixedNodeWeight += calculateCompoundWeight(node);\n      }\n    }\n    return fixedNodeWeight;\n  };\n\n  if (this.constraints.fixedNodeConstraint) {\n    // fill fixedNodeSet\n    this.constraints.fixedNodeConstraint.forEach(function (nodeData) {\n      self.fixedNodeSet.add(nodeData.nodeId);\n    });\n\n    // assign fixed node weights to compounds if they contain fixed nodes\n    var allNodes = this.graphManager.getAllNodes();\n    var node;\n\n    for (var i = 0; i < allNodes.length; i++) {\n      node = allNodes[i];\n      if (node.getChild() != null) {\n        var fixedNodeWeight = calculateCompoundWeight(node);\n        if (fixedNodeWeight > 0) {\n          node.fixedNodeWeight = fixedNodeWeight;\n        }\n      }\n    }\n  }\n\n  if (this.constraints.relativePlacementConstraint) {\n    var nodeToDummyForVerticalAlignment = new Map();\n    var nodeToDummyForHorizontalAlignment = new Map();\n    this.dummyToNodeForVerticalAlignment = new Map();\n    this.dummyToNodeForHorizontalAlignment = new Map();\n    this.fixedNodesOnHorizontal = new Set();\n    this.fixedNodesOnVertical = new Set();\n\n    // fill maps and sets\n    this.fixedNodeSet.forEach(function (nodeId) {\n      self.fixedNodesOnHorizontal.add(nodeId);\n      self.fixedNodesOnVertical.add(nodeId);\n    });\n\n    if (this.constraints.alignmentConstraint) {\n      if (this.constraints.alignmentConstraint.vertical) {\n        var verticalAlignment = this.constraints.alignmentConstraint.vertical;\n        for (var i = 0; i < verticalAlignment.length; i++) {\n          this.dummyToNodeForVerticalAlignment.set(\"dummy\" + i, []);\n          verticalAlignment[i].forEach(function (nodeId) {\n            nodeToDummyForVerticalAlignment.set(nodeId, \"dummy\" + i);\n            self.dummyToNodeForVerticalAlignment.get(\"dummy\" + i).push(nodeId);\n            if (self.fixedNodeSet.has(nodeId)) {\n              self.fixedNodesOnHorizontal.add(\"dummy\" + i);\n            }\n          });\n        }\n      }\n      if (this.constraints.alignmentConstraint.horizontal) {\n        var horizontalAlignment = this.constraints.alignmentConstraint.horizontal;\n        for (var i = 0; i < horizontalAlignment.length; i++) {\n          this.dummyToNodeForHorizontalAlignment.set(\"dummy\" + i, []);\n          horizontalAlignment[i].forEach(function (nodeId) {\n            nodeToDummyForHorizontalAlignment.set(nodeId, \"dummy\" + i);\n            self.dummyToNodeForHorizontalAlignment.get(\"dummy\" + i).push(nodeId);\n            if (self.fixedNodeSet.has(nodeId)) {\n              self.fixedNodesOnVertical.add(\"dummy\" + i);\n            }\n          });\n        }\n      }\n    }\n\n    if (CoSEConstants.RELAX_MOVEMENT_ON_CONSTRAINTS) {\n\n      this.shuffle = function (array) {\n        var j, x, i;\n        for (i = array.length - 1; i >= 2 * array.length / 3; i--) {\n          j = Math.floor(Math.random() * (i + 1));\n          x = array[i];\n          array[i] = array[j];\n          array[j] = x;\n        }\n        return array;\n      };\n\n      this.nodesInRelativeHorizontal = [];\n      this.nodesInRelativeVertical = [];\n      this.nodeToRelativeConstraintMapHorizontal = new Map();\n      this.nodeToRelativeConstraintMapVertical = new Map();\n      this.nodeToTempPositionMapHorizontal = new Map();\n      this.nodeToTempPositionMapVertical = new Map();\n\n      // fill arrays and maps\n      this.constraints.relativePlacementConstraint.forEach(function (constraint) {\n        if (constraint.left) {\n          var nodeIdLeft = nodeToDummyForVerticalAlignment.has(constraint.left) ? nodeToDummyForVerticalAlignment.get(constraint.left) : constraint.left;\n          var nodeIdRight = nodeToDummyForVerticalAlignment.has(constraint.right) ? nodeToDummyForVerticalAlignment.get(constraint.right) : constraint.right;\n\n          if (!self.nodesInRelativeHorizontal.includes(nodeIdLeft)) {\n            self.nodesInRelativeHorizontal.push(nodeIdLeft);\n            self.nodeToRelativeConstraintMapHorizontal.set(nodeIdLeft, []);\n            if (self.dummyToNodeForVerticalAlignment.has(nodeIdLeft)) {\n              self.nodeToTempPositionMapHorizontal.set(nodeIdLeft, self.idToNodeMap.get(self.dummyToNodeForVerticalAlignment.get(nodeIdLeft)[0]).getCenterX());\n            } else {\n              self.nodeToTempPositionMapHorizontal.set(nodeIdLeft, self.idToNodeMap.get(nodeIdLeft).getCenterX());\n            }\n          }\n          if (!self.nodesInRelativeHorizontal.includes(nodeIdRight)) {\n            self.nodesInRelativeHorizontal.push(nodeIdRight);\n            self.nodeToRelativeConstraintMapHorizontal.set(nodeIdRight, []);\n            if (self.dummyToNodeForVerticalAlignment.has(nodeIdRight)) {\n              self.nodeToTempPositionMapHorizontal.set(nodeIdRight, self.idToNodeMap.get(self.dummyToNodeForVerticalAlignment.get(nodeIdRight)[0]).getCenterX());\n            } else {\n              self.nodeToTempPositionMapHorizontal.set(nodeIdRight, self.idToNodeMap.get(nodeIdRight).getCenterX());\n            }\n          }\n\n          self.nodeToRelativeConstraintMapHorizontal.get(nodeIdLeft).push({ right: nodeIdRight, gap: constraint.gap });\n          self.nodeToRelativeConstraintMapHorizontal.get(nodeIdRight).push({ left: nodeIdLeft, gap: constraint.gap });\n        } else {\n          var nodeIdTop = nodeToDummyForHorizontalAlignment.has(constraint.top) ? nodeToDummyForHorizontalAlignment.get(constraint.top) : constraint.top;\n          var nodeIdBottom = nodeToDummyForHorizontalAlignment.has(constraint.bottom) ? nodeToDummyForHorizontalAlignment.get(constraint.bottom) : constraint.bottom;\n\n          if (!self.nodesInRelativeVertical.includes(nodeIdTop)) {\n            self.nodesInRelativeVertical.push(nodeIdTop);\n            self.nodeToRelativeConstraintMapVertical.set(nodeIdTop, []);\n            if (self.dummyToNodeForHorizontalAlignment.has(nodeIdTop)) {\n              self.nodeToTempPositionMapVertical.set(nodeIdTop, self.idToNodeMap.get(self.dummyToNodeForHorizontalAlignment.get(nodeIdTop)[0]).getCenterY());\n            } else {\n              self.nodeToTempPositionMapVertical.set(nodeIdTop, self.idToNodeMap.get(nodeIdTop).getCenterY());\n            }\n          }\n          if (!self.nodesInRelativeVertical.includes(nodeIdBottom)) {\n            self.nodesInRelativeVertical.push(nodeIdBottom);\n            self.nodeToRelativeConstraintMapVertical.set(nodeIdBottom, []);\n            if (self.dummyToNodeForHorizontalAlignment.has(nodeIdBottom)) {\n              self.nodeToTempPositionMapVertical.set(nodeIdBottom, self.idToNodeMap.get(self.dummyToNodeForHorizontalAlignment.get(nodeIdBottom)[0]).getCenterY());\n            } else {\n              self.nodeToTempPositionMapVertical.set(nodeIdBottom, self.idToNodeMap.get(nodeIdBottom).getCenterY());\n            }\n          }\n          self.nodeToRelativeConstraintMapVertical.get(nodeIdTop).push({ bottom: nodeIdBottom, gap: constraint.gap });\n          self.nodeToRelativeConstraintMapVertical.get(nodeIdBottom).push({ top: nodeIdTop, gap: constraint.gap });\n        }\n      });\n    } else {\n      var subGraphOnHorizontal = new Map(); // subgraph from vertical RP constraints\n      var subGraphOnVertical = new Map(); // subgraph from vertical RP constraints\n\n      // construct subgraphs from relative placement constraints \n      this.constraints.relativePlacementConstraint.forEach(function (constraint) {\n        if (constraint.left) {\n          var left = nodeToDummyForVerticalAlignment.has(constraint.left) ? nodeToDummyForVerticalAlignment.get(constraint.left) : constraint.left;\n          var right = nodeToDummyForVerticalAlignment.has(constraint.right) ? nodeToDummyForVerticalAlignment.get(constraint.right) : constraint.right;\n          if (subGraphOnHorizontal.has(left)) {\n            subGraphOnHorizontal.get(left).push(right);\n          } else {\n            subGraphOnHorizontal.set(left, [right]);\n          }\n          if (subGraphOnHorizontal.has(right)) {\n            subGraphOnHorizontal.get(right).push(left);\n          } else {\n            subGraphOnHorizontal.set(right, [left]);\n          }\n        } else {\n          var top = nodeToDummyForHorizontalAlignment.has(constraint.top) ? nodeToDummyForHorizontalAlignment.get(constraint.top) : constraint.top;\n          var bottom = nodeToDummyForHorizontalAlignment.has(constraint.bottom) ? nodeToDummyForHorizontalAlignment.get(constraint.bottom) : constraint.bottom;\n          if (subGraphOnVertical.has(top)) {\n            subGraphOnVertical.get(top).push(bottom);\n          } else {\n            subGraphOnVertical.set(top, [bottom]);\n          }\n          if (subGraphOnVertical.has(bottom)) {\n            subGraphOnVertical.get(bottom).push(top);\n          } else {\n            subGraphOnVertical.set(bottom, [top]);\n          }\n        }\n      });\n\n      // function to construct components from a given graph \n      // also returns an array that keeps whether each component contains fixed node\n      var constructComponents = function constructComponents(graph, fixedNodes) {\n        var components = [];\n        var isFixed = [];\n        var queue = new LinkedList();\n        var visited = new Set();\n        var count = 0;\n\n        graph.forEach(function (value, key) {\n          if (!visited.has(key)) {\n            components[count] = [];\n            isFixed[count] = false;\n            var currentNode = key;\n            queue.push(currentNode);\n            visited.add(currentNode);\n            components[count].push(currentNode);\n\n            while (queue.length != 0) {\n              currentNode = queue.shift();\n              if (fixedNodes.has(currentNode)) {\n                isFixed[count] = true;\n              }\n              var neighbors = graph.get(currentNode);\n              neighbors.forEach(function (neighbor) {\n                if (!visited.has(neighbor)) {\n                  queue.push(neighbor);\n                  visited.add(neighbor);\n                  components[count].push(neighbor);\n                }\n              });\n            }\n            count++;\n          }\n        });\n\n        return { components: components, isFixed: isFixed };\n      };\n\n      var resultOnHorizontal = constructComponents(subGraphOnHorizontal, self.fixedNodesOnHorizontal);\n      this.componentsOnHorizontal = resultOnHorizontal.components;\n      this.fixedComponentsOnHorizontal = resultOnHorizontal.isFixed;\n      var resultOnVertical = constructComponents(subGraphOnVertical, self.fixedNodesOnVertical);\n      this.componentsOnVertical = resultOnVertical.components;\n      this.fixedComponentsOnVertical = resultOnVertical.isFixed;\n    }\n  }\n};\n\n// updates node displacements based on constraints\nCoSELayout.prototype.updateDisplacements = function () {\n  var self = this;\n  if (this.constraints.fixedNodeConstraint) {\n    this.constraints.fixedNodeConstraint.forEach(function (nodeData) {\n      var fixedNode = self.idToNodeMap.get(nodeData.nodeId);\n      fixedNode.displacementX = 0;\n      fixedNode.displacementY = 0;\n    });\n  }\n\n  if (this.constraints.alignmentConstraint) {\n    if (this.constraints.alignmentConstraint.vertical) {\n      var allVerticalAlignments = this.constraints.alignmentConstraint.vertical;\n      for (var i = 0; i < allVerticalAlignments.length; i++) {\n        var totalDisplacementX = 0;\n        for (var j = 0; j < allVerticalAlignments[i].length; j++) {\n          if (this.fixedNodeSet.has(allVerticalAlignments[i][j])) {\n            totalDisplacementX = 0;\n            break;\n          }\n          totalDisplacementX += this.idToNodeMap.get(allVerticalAlignments[i][j]).displacementX;\n        }\n        var averageDisplacementX = totalDisplacementX / allVerticalAlignments[i].length;\n        for (var j = 0; j < allVerticalAlignments[i].length; j++) {\n          this.idToNodeMap.get(allVerticalAlignments[i][j]).displacementX = averageDisplacementX;\n        }\n      }\n    }\n    if (this.constraints.alignmentConstraint.horizontal) {\n      var allHorizontalAlignments = this.constraints.alignmentConstraint.horizontal;\n      for (var i = 0; i < allHorizontalAlignments.length; i++) {\n        var totalDisplacementY = 0;\n        for (var j = 0; j < allHorizontalAlignments[i].length; j++) {\n          if (this.fixedNodeSet.has(allHorizontalAlignments[i][j])) {\n            totalDisplacementY = 0;\n            break;\n          }\n          totalDisplacementY += this.idToNodeMap.get(allHorizontalAlignments[i][j]).displacementY;\n        }\n        var averageDisplacementY = totalDisplacementY / allHorizontalAlignments[i].length;\n        for (var j = 0; j < allHorizontalAlignments[i].length; j++) {\n          this.idToNodeMap.get(allHorizontalAlignments[i][j]).displacementY = averageDisplacementY;\n        }\n      }\n    }\n  }\n\n  if (this.constraints.relativePlacementConstraint) {\n\n    if (CoSEConstants.RELAX_MOVEMENT_ON_CONSTRAINTS) {\n      // shuffle array to randomize node processing order\n      if (this.totalIterations % 10 == 0) {\n        this.shuffle(this.nodesInRelativeHorizontal);\n        this.shuffle(this.nodesInRelativeVertical);\n      }\n\n      this.nodesInRelativeHorizontal.forEach(function (nodeId) {\n        if (!self.fixedNodesOnHorizontal.has(nodeId)) {\n          var displacement = 0;\n          if (self.dummyToNodeForVerticalAlignment.has(nodeId)) {\n            displacement = self.idToNodeMap.get(self.dummyToNodeForVerticalAlignment.get(nodeId)[0]).displacementX;\n          } else {\n            displacement = self.idToNodeMap.get(nodeId).displacementX;\n          }\n          self.nodeToRelativeConstraintMapHorizontal.get(nodeId).forEach(function (constraint) {\n            if (constraint.right) {\n              var diff = self.nodeToTempPositionMapHorizontal.get(constraint.right) - self.nodeToTempPositionMapHorizontal.get(nodeId) - displacement;\n              if (diff < constraint.gap) {\n                displacement -= constraint.gap - diff;\n              }\n            } else {\n              var diff = self.nodeToTempPositionMapHorizontal.get(nodeId) - self.nodeToTempPositionMapHorizontal.get(constraint.left) + displacement;\n              if (diff < constraint.gap) {\n                displacement += constraint.gap - diff;\n              }\n            }\n          });\n          self.nodeToTempPositionMapHorizontal.set(nodeId, self.nodeToTempPositionMapHorizontal.get(nodeId) + displacement);\n          if (self.dummyToNodeForVerticalAlignment.has(nodeId)) {\n            self.dummyToNodeForVerticalAlignment.get(nodeId).forEach(function (nodeId) {\n              self.idToNodeMap.get(nodeId).displacementX = displacement;\n            });\n          } else {\n            self.idToNodeMap.get(nodeId).displacementX = displacement;\n          }\n        }\n      });\n\n      this.nodesInRelativeVertical.forEach(function (nodeId) {\n        if (!self.fixedNodesOnHorizontal.has(nodeId)) {\n          var displacement = 0;\n          if (self.dummyToNodeForHorizontalAlignment.has(nodeId)) {\n            displacement = self.idToNodeMap.get(self.dummyToNodeForHorizontalAlignment.get(nodeId)[0]).displacementY;\n          } else {\n            displacement = self.idToNodeMap.get(nodeId).displacementY;\n          }\n          self.nodeToRelativeConstraintMapVertical.get(nodeId).forEach(function (constraint) {\n            if (constraint.bottom) {\n              var diff = self.nodeToTempPositionMapVertical.get(constraint.bottom) - self.nodeToTempPositionMapVertical.get(nodeId) - displacement;\n              if (diff < constraint.gap) {\n                displacement -= constraint.gap - diff;\n              }\n            } else {\n              var diff = self.nodeToTempPositionMapVertical.get(nodeId) - self.nodeToTempPositionMapVertical.get(constraint.top) + displacement;\n              if (diff < constraint.gap) {\n                displacement += constraint.gap - diff;\n              }\n            }\n          });\n          self.nodeToTempPositionMapVertical.set(nodeId, self.nodeToTempPositionMapVertical.get(nodeId) + displacement);\n          if (self.dummyToNodeForHorizontalAlignment.has(nodeId)) {\n            self.dummyToNodeForHorizontalAlignment.get(nodeId).forEach(function (nodeId) {\n              self.idToNodeMap.get(nodeId).displacementY = displacement;\n            });\n          } else {\n            self.idToNodeMap.get(nodeId).displacementY = displacement;\n          }\n        }\n      });\n    } else {\n      for (var i = 0; i < this.componentsOnHorizontal.length; i++) {\n        var component = this.componentsOnHorizontal[i];\n        if (this.fixedComponentsOnHorizontal[i]) {\n          for (var j = 0; j < component.length; j++) {\n            if (this.dummyToNodeForVerticalAlignment.has(component[j])) {\n              this.dummyToNodeForVerticalAlignment.get(component[j]).forEach(function (nodeId) {\n                self.idToNodeMap.get(nodeId).displacementX = 0;\n              });\n            } else {\n              this.idToNodeMap.get(component[j]).displacementX = 0;\n            }\n          }\n        } else {\n          var sum = 0;\n          var count = 0;\n          for (var j = 0; j < component.length; j++) {\n            if (this.dummyToNodeForVerticalAlignment.has(component[j])) {\n              var actualNodes = this.dummyToNodeForVerticalAlignment.get(component[j]);\n              sum += actualNodes.length * this.idToNodeMap.get(actualNodes[0]).displacementX;\n              count += actualNodes.length;\n            } else {\n              sum += this.idToNodeMap.get(component[j]).displacementX;\n              count++;\n            }\n          }\n          var averageDisplacement = sum / count;\n          for (var j = 0; j < component.length; j++) {\n            if (this.dummyToNodeForVerticalAlignment.has(component[j])) {\n              this.dummyToNodeForVerticalAlignment.get(component[j]).forEach(function (nodeId) {\n                self.idToNodeMap.get(nodeId).displacementX = averageDisplacement;\n              });\n            } else {\n              this.idToNodeMap.get(component[j]).displacementX = averageDisplacement;\n            }\n          }\n        }\n      }\n\n      for (var i = 0; i < this.componentsOnVertical.length; i++) {\n        var component = this.componentsOnVertical[i];\n        if (this.fixedComponentsOnVertical[i]) {\n          for (var j = 0; j < component.length; j++) {\n            if (this.dummyToNodeForHorizontalAlignment.has(component[j])) {\n              this.dummyToNodeForHorizontalAlignment.get(component[j]).forEach(function (nodeId) {\n                self.idToNodeMap.get(nodeId).displacementY = 0;\n              });\n            } else {\n              this.idToNodeMap.get(component[j]).displacementY = 0;\n            }\n          }\n        } else {\n          var sum = 0;\n          var count = 0;\n          for (var j = 0; j < component.length; j++) {\n            if (this.dummyToNodeForHorizontalAlignment.has(component[j])) {\n              var actualNodes = this.dummyToNodeForHorizontalAlignment.get(component[j]);\n              sum += actualNodes.length * this.idToNodeMap.get(actualNodes[0]).displacementY;\n              count += actualNodes.length;\n            } else {\n              sum += this.idToNodeMap.get(component[j]).displacementY;\n              count++;\n            }\n          }\n          var averageDisplacement = sum / count;\n          for (var j = 0; j < component.length; j++) {\n            if (this.dummyToNodeForHorizontalAlignment.has(component[j])) {\n              this.dummyToNodeForHorizontalAlignment.get(component[j]).forEach(function (nodeId) {\n                self.idToNodeMap.get(nodeId).displacementY = averageDisplacement;\n              });\n            } else {\n              this.idToNodeMap.get(component[j]).displacementY = averageDisplacement;\n            }\n          }\n        }\n      }\n    }\n  }\n};\n\nCoSELayout.prototype.calculateNodesToApplyGravitationTo = function () {\n  var nodeList = [];\n  var graph;\n\n  var graphs = this.graphManager.getGraphs();\n  var size = graphs.length;\n  var i;\n  for (i = 0; i < size; i++) {\n    graph = graphs[i];\n\n    graph.updateConnected();\n\n    if (!graph.isConnected) {\n      nodeList = nodeList.concat(graph.getNodes());\n    }\n  }\n\n  return nodeList;\n};\n\nCoSELayout.prototype.createBendpoints = function () {\n  var edges = [];\n  edges = edges.concat(this.graphManager.getAllEdges());\n  var visited = new Set();\n  var i;\n  for (i = 0; i < edges.length; i++) {\n    var edge = edges[i];\n\n    if (!visited.has(edge)) {\n      var source = edge.getSource();\n      var target = edge.getTarget();\n\n      if (source == target) {\n        edge.getBendpoints().push(new PointD());\n        edge.getBendpoints().push(new PointD());\n        this.createDummyNodesForBendpoints(edge);\n        visited.add(edge);\n      } else {\n        var edgeList = [];\n\n        edgeList = edgeList.concat(source.getEdgeListToNode(target));\n        edgeList = edgeList.concat(target.getEdgeListToNode(source));\n\n        if (!visited.has(edgeList[0])) {\n          if (edgeList.length > 1) {\n            var k;\n            for (k = 0; k < edgeList.length; k++) {\n              var multiEdge = edgeList[k];\n              multiEdge.getBendpoints().push(new PointD());\n              this.createDummyNodesForBendpoints(multiEdge);\n            }\n          }\n          edgeList.forEach(function (edge) {\n            visited.add(edge);\n          });\n        }\n      }\n    }\n\n    if (visited.size == edges.length) {\n      break;\n    }\n  }\n};\n\nCoSELayout.prototype.positionNodesRadially = function (forest) {\n  // We tile the trees to a grid row by row; first tree starts at (0,0)\n  var currentStartingPoint = new Point(0, 0);\n  var numberOfColumns = Math.ceil(Math.sqrt(forest.length));\n  var height = 0;\n  var currentY = 0;\n  var currentX = 0;\n  var point = new PointD(0, 0);\n\n  for (var i = 0; i < forest.length; i++) {\n    if (i % numberOfColumns == 0) {\n      // Start of a new row, make the x coordinate 0, increment the\n      // y coordinate with the max height of the previous row\n      currentX = 0;\n      currentY = height;\n\n      if (i != 0) {\n        currentY += CoSEConstants.DEFAULT_COMPONENT_SEPERATION;\n      }\n\n      height = 0;\n    }\n\n    var tree = forest[i];\n\n    // Find the center of the tree\n    var centerNode = Layout.findCenterOfTree(tree);\n\n    // Set the staring point of the next tree\n    currentStartingPoint.x = currentX;\n    currentStartingPoint.y = currentY;\n\n    // Do a radial layout starting with the center\n    point = CoSELayout.radialLayout(tree, centerNode, currentStartingPoint);\n\n    if (point.y > height) {\n      height = Math.floor(point.y);\n    }\n\n    currentX = Math.floor(point.x + CoSEConstants.DEFAULT_COMPONENT_SEPERATION);\n  }\n\n  this.transform(new PointD(LayoutConstants.WORLD_CENTER_X - point.x / 2, LayoutConstants.WORLD_CENTER_Y - point.y / 2));\n};\n\nCoSELayout.radialLayout = function (tree, centerNode, startingPoint) {\n  var radialSep = Math.max(this.maxDiagonalInTree(tree), CoSEConstants.DEFAULT_RADIAL_SEPARATION);\n  CoSELayout.branchRadialLayout(centerNode, null, 0, 359, 0, radialSep);\n  var bounds = LGraph.calculateBounds(tree);\n\n  var transform = new Transform();\n  transform.setDeviceOrgX(bounds.getMinX());\n  transform.setDeviceOrgY(bounds.getMinY());\n  transform.setWorldOrgX(startingPoint.x);\n  transform.setWorldOrgY(startingPoint.y);\n\n  for (var i = 0; i < tree.length; i++) {\n    var node = tree[i];\n    node.transform(transform);\n  }\n\n  var bottomRight = new PointD(bounds.getMaxX(), bounds.getMaxY());\n\n  return transform.inverseTransformPoint(bottomRight);\n};\n\nCoSELayout.branchRadialLayout = function (node, parentOfNode, startAngle, endAngle, distance, radialSeparation) {\n  // First, position this node by finding its angle.\n  var halfInterval = (endAngle - startAngle + 1) / 2;\n\n  if (halfInterval < 0) {\n    halfInterval += 180;\n  }\n\n  var nodeAngle = (halfInterval + startAngle) % 360;\n  var teta = nodeAngle * IGeometry.TWO_PI / 360;\n\n  // Make polar to java cordinate conversion.\n  var cos_teta = Math.cos(teta);\n  var x_ = distance * Math.cos(teta);\n  var y_ = distance * Math.sin(teta);\n\n  node.setCenter(x_, y_);\n\n  // Traverse all neighbors of this node and recursively call this\n  // function.\n  var neighborEdges = [];\n  neighborEdges = neighborEdges.concat(node.getEdges());\n  var childCount = neighborEdges.length;\n\n  if (parentOfNode != null) {\n    childCount--;\n  }\n\n  var branchCount = 0;\n\n  var incEdgesCount = neighborEdges.length;\n  var startIndex;\n\n  var edges = node.getEdgesBetween(parentOfNode);\n\n  // If there are multiple edges, prune them until there remains only one\n  // edge.\n  while (edges.length > 1) {\n    //neighborEdges.remove(edges.remove(0));\n    var temp = edges[0];\n    edges.splice(0, 1);\n    var index = neighborEdges.indexOf(temp);\n    if (index >= 0) {\n      neighborEdges.splice(index, 1);\n    }\n    incEdgesCount--;\n    childCount--;\n  }\n\n  if (parentOfNode != null) {\n    //assert edges.length == 1;\n    startIndex = (neighborEdges.indexOf(edges[0]) + 1) % incEdgesCount;\n  } else {\n    startIndex = 0;\n  }\n\n  var stepAngle = Math.abs(endAngle - startAngle) / childCount;\n\n  for (var i = startIndex; branchCount != childCount; i = ++i % incEdgesCount) {\n    var currentNeighbor = neighborEdges[i].getOtherEnd(node);\n\n    // Don't back traverse to root node in current tree.\n    if (currentNeighbor == parentOfNode) {\n      continue;\n    }\n\n    var childStartAngle = (startAngle + branchCount * stepAngle) % 360;\n    var childEndAngle = (childStartAngle + stepAngle) % 360;\n\n    CoSELayout.branchRadialLayout(currentNeighbor, node, childStartAngle, childEndAngle, distance + radialSeparation, radialSeparation);\n\n    branchCount++;\n  }\n};\n\nCoSELayout.maxDiagonalInTree = function (tree) {\n  var maxDiagonal = Integer.MIN_VALUE;\n\n  for (var i = 0; i < tree.length; i++) {\n    var node = tree[i];\n    var diagonal = node.getDiagonal();\n\n    if (diagonal > maxDiagonal) {\n      maxDiagonal = diagonal;\n    }\n  }\n\n  return maxDiagonal;\n};\n\nCoSELayout.prototype.calcRepulsionRange = function () {\n  // formula is 2 x (level + 1) x idealEdgeLength\n  return 2 * (this.level + 1) * this.idealEdgeLength;\n};\n\n// Tiling methods\n\n// Group zero degree members whose parents are not to be tiled, create dummy parents where needed and fill memberGroups by their dummp parent id's\nCoSELayout.prototype.groupZeroDegreeMembers = function () {\n  var self = this;\n  // array of [parent_id x oneDegreeNode_id]\n  var tempMemberGroups = {}; // A temporary map of parent node and its zero degree members\n  this.memberGroups = {}; // A map of dummy parent node and its zero degree members whose parents are not to be tiled\n  this.idToDummyNode = {}; // A map of id to dummy node \n\n  var zeroDegree = []; // List of zero degree nodes whose parents are not to be tiled\n  var allNodes = this.graphManager.getAllNodes();\n\n  // Fill zero degree list\n  for (var i = 0; i < allNodes.length; i++) {\n    var node = allNodes[i];\n    var parent = node.getParent();\n    // If a node has zero degree and its parent is not to be tiled if exists add that node to zeroDegres list\n    if (this.getNodeDegreeWithChildren(node) === 0 && (parent.id == undefined || !this.getToBeTiled(parent))) {\n      zeroDegree.push(node);\n    }\n  }\n\n  // Create a map of parent node and its zero degree members\n  for (var i = 0; i < zeroDegree.length; i++) {\n    var node = zeroDegree[i]; // Zero degree node itself\n    var p_id = node.getParent().id; // Parent id\n\n    if (typeof tempMemberGroups[p_id] === \"undefined\") tempMemberGroups[p_id] = [];\n\n    tempMemberGroups[p_id] = tempMemberGroups[p_id].concat(node); // Push node to the list belongs to its parent in tempMemberGroups\n  }\n\n  // If there are at least two nodes at a level, create a dummy compound for them\n  Object.keys(tempMemberGroups).forEach(function (p_id) {\n    if (tempMemberGroups[p_id].length > 1) {\n      var dummyCompoundId = \"DummyCompound_\" + p_id; // The id of dummy compound which will be created soon\n      self.memberGroups[dummyCompoundId] = tempMemberGroups[p_id]; // Add dummy compound to memberGroups\n\n      var parent = tempMemberGroups[p_id][0].getParent(); // The parent of zero degree nodes will be the parent of new dummy compound\n\n      // Create a dummy compound with calculated id\n      var dummyCompound = new CoSENode(self.graphManager);\n      dummyCompound.id = dummyCompoundId;\n      dummyCompound.paddingLeft = parent.paddingLeft || 0;\n      dummyCompound.paddingRight = parent.paddingRight || 0;\n      dummyCompound.paddingBottom = parent.paddingBottom || 0;\n      dummyCompound.paddingTop = parent.paddingTop || 0;\n\n      self.idToDummyNode[dummyCompoundId] = dummyCompound;\n\n      var dummyParentGraph = self.getGraphManager().add(self.newGraph(), dummyCompound);\n      var parentGraph = parent.getChild();\n\n      // Add dummy compound to parent the graph\n      parentGraph.add(dummyCompound);\n\n      // For each zero degree node in this level remove it from its parent graph and add it to the graph of dummy parent\n      for (var i = 0; i < tempMemberGroups[p_id].length; i++) {\n        var node = tempMemberGroups[p_id][i];\n\n        parentGraph.remove(node);\n        dummyParentGraph.add(node);\n      }\n    }\n  });\n};\n\nCoSELayout.prototype.clearCompounds = function () {\n  var childGraphMap = {};\n  var idToNode = {};\n\n  // Get compound ordering by finding the inner one first\n  this.performDFSOnCompounds();\n\n  for (var i = 0; i < this.compoundOrder.length; i++) {\n\n    idToNode[this.compoundOrder[i].id] = this.compoundOrder[i];\n    childGraphMap[this.compoundOrder[i].id] = [].concat(this.compoundOrder[i].getChild().getNodes());\n\n    // Remove children of compounds\n    this.graphManager.remove(this.compoundOrder[i].getChild());\n    this.compoundOrder[i].child = null;\n  }\n\n  this.graphManager.resetAllNodes();\n\n  // Tile the removed children\n  this.tileCompoundMembers(childGraphMap, idToNode);\n};\n\nCoSELayout.prototype.clearZeroDegreeMembers = function () {\n  var self = this;\n  var tiledZeroDegreePack = this.tiledZeroDegreePack = [];\n\n  Object.keys(this.memberGroups).forEach(function (id) {\n    var compoundNode = self.idToDummyNode[id]; // Get the dummy compound\n\n    tiledZeroDegreePack[id] = self.tileNodes(self.memberGroups[id], compoundNode.paddingLeft + compoundNode.paddingRight);\n\n    // Set the width and height of the dummy compound as calculated\n    compoundNode.rect.width = tiledZeroDegreePack[id].width;\n    compoundNode.rect.height = tiledZeroDegreePack[id].height;\n    compoundNode.setCenter(tiledZeroDegreePack[id].centerX, tiledZeroDegreePack[id].centerY);\n\n    // compound left and top margings for labels\n    // when node labels are included, these values may be set to different values below and are used in tilingPostLayout,\n    // otherwise they stay as zero\n    compoundNode.labelMarginLeft = 0;\n    compoundNode.labelMarginTop = 0;\n\n    // Update compound bounds considering its label properties and set label margins for left and top\n    if (CoSEConstants.NODE_DIMENSIONS_INCLUDE_LABELS) {\n\n      var width = compoundNode.rect.width;\n      var height = compoundNode.rect.height;\n\n      if (compoundNode.labelWidth) {\n        if (compoundNode.labelPosHorizontal == \"left\") {\n          compoundNode.rect.x -= compoundNode.labelWidth;\n          compoundNode.setWidth(width + compoundNode.labelWidth);\n          compoundNode.labelMarginLeft = compoundNode.labelWidth;\n        } else if (compoundNode.labelPosHorizontal == \"center\" && compoundNode.labelWidth > width) {\n          compoundNode.rect.x -= (compoundNode.labelWidth - width) / 2;\n          compoundNode.setWidth(compoundNode.labelWidth);\n          compoundNode.labelMarginLeft = (compoundNode.labelWidth - width) / 2;\n        } else if (compoundNode.labelPosHorizontal == \"right\") {\n          compoundNode.setWidth(width + compoundNode.labelWidth);\n        }\n      }\n\n      if (compoundNode.labelHeight) {\n        if (compoundNode.labelPosVertical == \"top\") {\n          compoundNode.rect.y -= compoundNode.labelHeight;\n          compoundNode.setHeight(height + compoundNode.labelHeight);\n          compoundNode.labelMarginTop = compoundNode.labelHeight;\n        } else if (compoundNode.labelPosVertical == \"center\" && compoundNode.labelHeight > height) {\n          compoundNode.rect.y -= (compoundNode.labelHeight - height) / 2;\n          compoundNode.setHeight(compoundNode.labelHeight);\n          compoundNode.labelMarginTop = (compoundNode.labelHeight - height) / 2;\n        } else if (compoundNode.labelPosVertical == \"bottom\") {\n          compoundNode.setHeight(height + compoundNode.labelHeight);\n        }\n      }\n    }\n  });\n};\n\nCoSELayout.prototype.repopulateCompounds = function () {\n  for (var i = this.compoundOrder.length - 1; i >= 0; i--) {\n    var lCompoundNode = this.compoundOrder[i];\n    var id = lCompoundNode.id;\n    var horizontalMargin = lCompoundNode.paddingLeft;\n    var verticalMargin = lCompoundNode.paddingTop;\n    var labelMarginLeft = lCompoundNode.labelMarginLeft;\n    var labelMarginTop = lCompoundNode.labelMarginTop;\n\n    this.adjustLocations(this.tiledMemberPack[id], lCompoundNode.rect.x, lCompoundNode.rect.y, horizontalMargin, verticalMargin, labelMarginLeft, labelMarginTop);\n  }\n};\n\nCoSELayout.prototype.repopulateZeroDegreeMembers = function () {\n  var self = this;\n  var tiledPack = this.tiledZeroDegreePack;\n\n  Object.keys(tiledPack).forEach(function (id) {\n    var compoundNode = self.idToDummyNode[id]; // Get the dummy compound by its id\n    var horizontalMargin = compoundNode.paddingLeft;\n    var verticalMargin = compoundNode.paddingTop;\n    var labelMarginLeft = compoundNode.labelMarginLeft;\n    var labelMarginTop = compoundNode.labelMarginTop;\n\n    // Adjust the positions of nodes wrt its compound\n    self.adjustLocations(tiledPack[id], compoundNode.rect.x, compoundNode.rect.y, horizontalMargin, verticalMargin, labelMarginLeft, labelMarginTop);\n  });\n};\n\nCoSELayout.prototype.getToBeTiled = function (node) {\n  var id = node.id;\n  //firstly check the previous results\n  if (this.toBeTiled[id] != null) {\n    return this.toBeTiled[id];\n  }\n\n  //only compound nodes are to be tiled\n  var childGraph = node.getChild();\n  if (childGraph == null) {\n    this.toBeTiled[id] = false;\n    return false;\n  }\n\n  var children = childGraph.getNodes(); // Get the children nodes\n\n  //a compound node is not to be tiled if all of its compound children are not to be tiled\n  for (var i = 0; i < children.length; i++) {\n    var theChild = children[i];\n\n    if (this.getNodeDegree(theChild) > 0) {\n      this.toBeTiled[id] = false;\n      return false;\n    }\n\n    //pass the children not having the compound structure\n    if (theChild.getChild() == null) {\n      this.toBeTiled[theChild.id] = false;\n      continue;\n    }\n\n    if (!this.getToBeTiled(theChild)) {\n      this.toBeTiled[id] = false;\n      return false;\n    }\n  }\n  this.toBeTiled[id] = true;\n  return true;\n};\n\n// Get degree of a node depending of its edges and independent of its children\nCoSELayout.prototype.getNodeDegree = function (node) {\n  var id = node.id;\n  var edges = node.getEdges();\n  var degree = 0;\n\n  // For the edges connected\n  for (var i = 0; i < edges.length; i++) {\n    var edge = edges[i];\n    if (edge.getSource().id !== edge.getTarget().id) {\n      degree = degree + 1;\n    }\n  }\n  return degree;\n};\n\n// Get degree of a node with its children\nCoSELayout.prototype.getNodeDegreeWithChildren = function (node) {\n  var degree = this.getNodeDegree(node);\n  if (node.getChild() == null) {\n    return degree;\n  }\n  var children = node.getChild().getNodes();\n  for (var i = 0; i < children.length; i++) {\n    var child = children[i];\n    degree += this.getNodeDegreeWithChildren(child);\n  }\n  return degree;\n};\n\nCoSELayout.prototype.performDFSOnCompounds = function () {\n  this.compoundOrder = [];\n  this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes());\n};\n\nCoSELayout.prototype.fillCompexOrderByDFS = function (children) {\n  for (var i = 0; i < children.length; i++) {\n    var child = children[i];\n    if (child.getChild() != null) {\n      this.fillCompexOrderByDFS(child.getChild().getNodes());\n    }\n    if (this.getToBeTiled(child)) {\n      this.compoundOrder.push(child);\n    }\n  }\n};\n\n/**\n* This method places each zero degree member wrt given (x,y) coordinates (top left).\n*/\nCoSELayout.prototype.adjustLocations = function (organization, x, y, compoundHorizontalMargin, compoundVerticalMargin, compoundLabelMarginLeft, compoundLabelMarginTop) {\n  x += compoundHorizontalMargin + compoundLabelMarginLeft;\n  y += compoundVerticalMargin + compoundLabelMarginTop;\n\n  var left = x;\n\n  for (var i = 0; i < organization.rows.length; i++) {\n    var row = organization.rows[i];\n    x = left;\n    var maxHeight = 0;\n\n    for (var j = 0; j < row.length; j++) {\n      var lnode = row[j];\n\n      lnode.rect.x = x; // + lnode.rect.width / 2;\n      lnode.rect.y = y; // + lnode.rect.height / 2;\n\n      x += lnode.rect.width + organization.horizontalPadding;\n\n      if (lnode.rect.height > maxHeight) maxHeight = lnode.rect.height;\n    }\n\n    y += maxHeight + organization.verticalPadding;\n  }\n};\n\nCoSELayout.prototype.tileCompoundMembers = function (childGraphMap, idToNode) {\n  var self = this;\n  this.tiledMemberPack = [];\n\n  Object.keys(childGraphMap).forEach(function (id) {\n    // Get the compound node\n    var compoundNode = idToNode[id];\n\n    self.tiledMemberPack[id] = self.tileNodes(childGraphMap[id], compoundNode.paddingLeft + compoundNode.paddingRight);\n\n    compoundNode.rect.width = self.tiledMemberPack[id].width;\n    compoundNode.rect.height = self.tiledMemberPack[id].height;\n    compoundNode.setCenter(self.tiledMemberPack[id].centerX, self.tiledMemberPack[id].centerY);\n\n    // compound left and top margings for labels\n    // when node labels are included, these values may be set to different values below and are used in tilingPostLayout,\n    // otherwise they stay as zero\n    compoundNode.labelMarginLeft = 0;\n    compoundNode.labelMarginTop = 0;\n\n    // Update compound bounds considering its label properties and set label margins for left and top\n    if (CoSEConstants.NODE_DIMENSIONS_INCLUDE_LABELS) {\n\n      var width = compoundNode.rect.width;\n      var height = compoundNode.rect.height;\n\n      if (compoundNode.labelWidth) {\n        if (compoundNode.labelPosHorizontal == \"left\") {\n          compoundNode.rect.x -= compoundNode.labelWidth;\n          compoundNode.setWidth(width + compoundNode.labelWidth);\n          compoundNode.labelMarginLeft = compoundNode.labelWidth;\n        } else if (compoundNode.labelPosHorizontal == \"center\" && compoundNode.labelWidth > width) {\n          compoundNode.rect.x -= (compoundNode.labelWidth - width) / 2;\n          compoundNode.setWidth(compoundNode.labelWidth);\n          compoundNode.labelMarginLeft = (compoundNode.labelWidth - width) / 2;\n        } else if (compoundNode.labelPosHorizontal == \"right\") {\n          compoundNode.setWidth(width + compoundNode.labelWidth);\n        }\n      }\n\n      if (compoundNode.labelHeight) {\n        if (compoundNode.labelPosVertical == \"top\") {\n          compoundNode.rect.y -= compoundNode.labelHeight;\n          compoundNode.setHeight(height + compoundNode.labelHeight);\n          compoundNode.labelMarginTop = compoundNode.labelHeight;\n        } else if (compoundNode.labelPosVertical == \"center\" && compoundNode.labelHeight > height) {\n          compoundNode.rect.y -= (compoundNode.labelHeight - height) / 2;\n          compoundNode.setHeight(compoundNode.labelHeight);\n          compoundNode.labelMarginTop = (compoundNode.labelHeight - height) / 2;\n        } else if (compoundNode.labelPosVertical == \"bottom\") {\n          compoundNode.setHeight(height + compoundNode.labelHeight);\n        }\n      }\n    }\n  });\n};\n\nCoSELayout.prototype.tileNodes = function (nodes, minWidth) {\n  var horizontalOrg = this.tileNodesByFavoringDim(nodes, minWidth, true);\n  var verticalOrg = this.tileNodesByFavoringDim(nodes, minWidth, false);\n\n  var horizontalRatio = this.getOrgRatio(horizontalOrg);\n  var verticalRatio = this.getOrgRatio(verticalOrg);\n  var bestOrg;\n\n  // the best ratio is the one that is closer to 1 since the ratios are already normalized\n  // and the best organization is the one that has the best ratio\n  if (verticalRatio < horizontalRatio) {\n    bestOrg = verticalOrg;\n  } else {\n    bestOrg = horizontalOrg;\n  }\n\n  return bestOrg;\n};\n\n// get the width/height ratio of the organization that is normalized so that it will not be less than 1\nCoSELayout.prototype.getOrgRatio = function (organization) {\n  // get dimensions and calculate the initial ratio\n  var width = organization.width;\n  var height = organization.height;\n  var ratio = width / height;\n\n  // if the initial ratio is less then 1 then inverse it\n  if (ratio < 1) {\n    ratio = 1 / ratio;\n  }\n\n  // return the normalized ratio\n  return ratio;\n};\n\n/*\n * Calculates the ideal width for the rows. This method assumes that\n * each node has the same sizes and calculates the ideal row width that\n * approximates a square shaped complex accordingly. However, since nodes would\n * have different sizes some rows would have different sizes and the resulting\n * shape would not be an exact square.\n */\nCoSELayout.prototype.calcIdealRowWidth = function (members, favorHorizontalDim) {\n  // To approximate a square shaped complex we need to make complex width equal to complex height.\n  // To achieve this we need to solve the following equation system for hc:\n  // (x + bx) * hc - bx = (y + by) * vc - by, hc * vc = n\n  // where x is the avarage width of the nodes, y is the avarage height of nodes\n  // bx and by are the buffer sizes in horizontal and vertical dimensions accordingly,\n  // hc and vc are the number of rows in horizontal and vertical dimensions\n  // n is number of members.\n\n  var verticalPadding = CoSEConstants.TILING_PADDING_VERTICAL;\n  var horizontalPadding = CoSEConstants.TILING_PADDING_HORIZONTAL;\n\n  // number of members\n  var membersSize = members.length;\n\n  // sum of the width of all members\n  var totalWidth = 0;\n\n  // sum of the height of all members\n  var totalHeight = 0;\n\n  var maxWidth = 0;\n\n  // traverse all members to calculate total width and total height and get the maximum members width\n  members.forEach(function (node) {\n    totalWidth += node.getWidth();\n    totalHeight += node.getHeight();\n\n    if (node.getWidth() > maxWidth) {\n      maxWidth = node.getWidth();\n    }\n  });\n\n  // average width of the members\n  var averageWidth = totalWidth / membersSize;\n\n  // average height of the members\n  var averageHeight = totalHeight / membersSize;\n\n  // solving the initial equation system for the hc yields the following second degree equation:\n  // hc^2 * (x+bx) + hc * (by - bx) - n * (y + by) = 0\n\n  // the delta value to solve the equation above for hc\n  var delta = Math.pow(verticalPadding - horizontalPadding, 2) + 4 * (averageWidth + horizontalPadding) * (averageHeight + verticalPadding) * membersSize;\n\n  // solve the equation using delta value to calculate the horizontal count\n  // that represents the number of nodes in an ideal row\n  var horizontalCountDouble = (horizontalPadding - verticalPadding + Math.sqrt(delta)) / (2 * (averageWidth + horizontalPadding));\n  // round the calculated horizontal count up or down according to the favored dimension\n  var horizontalCount;\n\n  if (favorHorizontalDim) {\n    horizontalCount = Math.ceil(horizontalCountDouble);\n    // if horizontalCount count is not a float value then both of rounding to floor and ceil\n    // will yield the same values. Instead of repeating the same calculation try going up\n    // while favoring horizontal dimension in such cases\n    if (horizontalCount == horizontalCountDouble) {\n      horizontalCount++;\n    }\n  } else {\n    horizontalCount = Math.floor(horizontalCountDouble);\n  }\n\n  // ideal width to be calculated\n  var idealWidth = horizontalCount * (averageWidth + horizontalPadding) - horizontalPadding;\n\n  // if max width is bigger than calculated ideal width reset ideal width to it\n  if (maxWidth > idealWidth) {\n    idealWidth = maxWidth;\n  }\n\n  // add the left-right margins to the ideal row width\n  idealWidth += horizontalPadding * 2;\n\n  // return the ideal row width1\n  return idealWidth;\n};\n\nCoSELayout.prototype.tileNodesByFavoringDim = function (nodes, minWidth, favorHorizontalDim) {\n  var verticalPadding = CoSEConstants.TILING_PADDING_VERTICAL;\n  var horizontalPadding = CoSEConstants.TILING_PADDING_HORIZONTAL;\n  var tilingCompareBy = CoSEConstants.TILING_COMPARE_BY;\n  var organization = {\n    rows: [],\n    rowWidth: [],\n    rowHeight: [],\n    width: 0,\n    height: minWidth, // assume minHeight equals to minWidth\n    verticalPadding: verticalPadding,\n    horizontalPadding: horizontalPadding,\n    centerX: 0,\n    centerY: 0\n  };\n\n  if (tilingCompareBy) {\n    organization.idealRowWidth = this.calcIdealRowWidth(nodes, favorHorizontalDim);\n  }\n\n  var getNodeArea = function getNodeArea(n) {\n    return n.rect.width * n.rect.height;\n  };\n\n  var areaCompareFcn = function areaCompareFcn(n1, n2) {\n    return getNodeArea(n2) - getNodeArea(n1);\n  };\n\n  // Sort the nodes in descending order of their areas\n  nodes.sort(function (n1, n2) {\n    var cmpBy = areaCompareFcn;\n    if (organization.idealRowWidth) {\n      cmpBy = tilingCompareBy;\n      return cmpBy(n1.id, n2.id);\n    }\n    return cmpBy(n1, n2);\n  });\n\n  // Create the organization -> calculate compound center\n  var sumCenterX = 0;\n  var sumCenterY = 0;\n  for (var i = 0; i < nodes.length; i++) {\n    var lNode = nodes[i];\n\n    sumCenterX += lNode.getCenterX();\n    sumCenterY += lNode.getCenterY();\n  }\n\n  organization.centerX = sumCenterX / nodes.length;\n  organization.centerY = sumCenterY / nodes.length;\n\n  // Create the organization -> tile members\n  for (var i = 0; i < nodes.length; i++) {\n    var lNode = nodes[i];\n\n    if (organization.rows.length == 0) {\n      this.insertNodeToRow(organization, lNode, 0, minWidth);\n    } else if (this.canAddHorizontal(organization, lNode.rect.width, lNode.rect.height)) {\n      var rowIndex = organization.rows.length - 1;\n      if (!organization.idealRowWidth) {\n        rowIndex = this.getShortestRowIndex(organization);\n      }\n      this.insertNodeToRow(organization, lNode, rowIndex, minWidth);\n    } else {\n      this.insertNodeToRow(organization, lNode, organization.rows.length, minWidth);\n    }\n\n    this.shiftToLastRow(organization);\n  }\n\n  return organization;\n};\n\nCoSELayout.prototype.insertNodeToRow = function (organization, node, rowIndex, minWidth) {\n  var minCompoundSize = minWidth;\n\n  // Add new row if needed\n  if (rowIndex == organization.rows.length) {\n    var secondDimension = [];\n\n    organization.rows.push(secondDimension);\n    organization.rowWidth.push(minCompoundSize);\n    organization.rowHeight.push(0);\n  }\n\n  // Update row width\n  var w = organization.rowWidth[rowIndex] + node.rect.width;\n\n  if (organization.rows[rowIndex].length > 0) {\n    w += organization.horizontalPadding;\n  }\n\n  organization.rowWidth[rowIndex] = w;\n  // Update compound width\n  if (organization.width < w) {\n    organization.width = w;\n  }\n\n  // Update height\n  var h = node.rect.height;\n  if (rowIndex > 0) h += organization.verticalPadding;\n\n  var extraHeight = 0;\n  if (h > organization.rowHeight[rowIndex]) {\n    extraHeight = organization.rowHeight[rowIndex];\n    organization.rowHeight[rowIndex] = h;\n    extraHeight = organization.rowHeight[rowIndex] - extraHeight;\n  }\n\n  organization.height += extraHeight;\n\n  // Insert node\n  organization.rows[rowIndex].push(node);\n};\n\n//Scans the rows of an organization and returns the one with the min width\nCoSELayout.prototype.getShortestRowIndex = function (organization) {\n  var r = -1;\n  var min = Number.MAX_VALUE;\n\n  for (var i = 0; i < organization.rows.length; i++) {\n    if (organization.rowWidth[i] < min) {\n      r = i;\n      min = organization.rowWidth[i];\n    }\n  }\n  return r;\n};\n\n//Scans the rows of an organization and returns the one with the max width\nCoSELayout.prototype.getLongestRowIndex = function (organization) {\n  var r = -1;\n  var max = Number.MIN_VALUE;\n\n  for (var i = 0; i < organization.rows.length; i++) {\n\n    if (organization.rowWidth[i] > max) {\n      r = i;\n      max = organization.rowWidth[i];\n    }\n  }\n\n  return r;\n};\n\n/**\n* This method checks whether adding extra width to the organization violates\n* the aspect ratio(1) or not.\n*/\nCoSELayout.prototype.canAddHorizontal = function (organization, extraWidth, extraHeight) {\n\n  // if there is an ideal row width specified use it instead of checking the aspect ratio\n  if (organization.idealRowWidth) {\n    var lastRowIndex = organization.rows.length - 1;\n    var lastRowWidth = organization.rowWidth[lastRowIndex];\n\n    // check and return if ideal row width will be exceed if the node is added to the row\n    return lastRowWidth + extraWidth + organization.horizontalPadding <= organization.idealRowWidth;\n  }\n\n  var sri = this.getShortestRowIndex(organization);\n\n  if (sri < 0) {\n    return true;\n  }\n\n  var min = organization.rowWidth[sri];\n\n  if (min + organization.horizontalPadding + extraWidth <= organization.width) return true;\n\n  var hDiff = 0;\n\n  // Adding to an existing row\n  if (organization.rowHeight[sri] < extraHeight) {\n    if (sri > 0) hDiff = extraHeight + organization.verticalPadding - organization.rowHeight[sri];\n  }\n\n  var add_to_row_ratio;\n  if (organization.width - min >= extraWidth + organization.horizontalPadding) {\n    add_to_row_ratio = (organization.height + hDiff) / (min + extraWidth + organization.horizontalPadding);\n  } else {\n    add_to_row_ratio = (organization.height + hDiff) / organization.width;\n  }\n\n  // Adding a new row for this node\n  hDiff = extraHeight + organization.verticalPadding;\n  var add_new_row_ratio;\n  if (organization.width < extraWidth) {\n    add_new_row_ratio = (organization.height + hDiff) / extraWidth;\n  } else {\n    add_new_row_ratio = (organization.height + hDiff) / organization.width;\n  }\n\n  if (add_new_row_ratio < 1) add_new_row_ratio = 1 / add_new_row_ratio;\n\n  if (add_to_row_ratio < 1) add_to_row_ratio = 1 / add_to_row_ratio;\n\n  return add_to_row_ratio < add_new_row_ratio;\n};\n\n//If moving the last node from the longest row and adding it to the last\n//row makes the bounding box smaller, do it.\nCoSELayout.prototype.shiftToLastRow = function (organization) {\n  var longest = this.getLongestRowIndex(organization);\n  var last = organization.rowWidth.length - 1;\n  var row = organization.rows[longest];\n  var node = row[row.length - 1];\n\n  var diff = node.width + organization.horizontalPadding;\n\n  // Check if there is enough space on the last row\n  if (organization.width - organization.rowWidth[last] > diff && longest != last) {\n    // Remove the last element of the longest row\n    row.splice(-1, 1);\n\n    // Push it to the last row\n    organization.rows[last].push(node);\n\n    organization.rowWidth[longest] = organization.rowWidth[longest] - diff;\n    organization.rowWidth[last] = organization.rowWidth[last] + diff;\n    organization.width = organization.rowWidth[instance.getLongestRowIndex(organization)];\n\n    // Update heights of the organization\n    var maxHeight = Number.MIN_VALUE;\n    for (var i = 0; i < row.length; i++) {\n      if (row[i].height > maxHeight) maxHeight = row[i].height;\n    }\n    if (longest > 0) maxHeight += organization.verticalPadding;\n\n    var prevTotal = organization.rowHeight[longest] + organization.rowHeight[last];\n\n    organization.rowHeight[longest] = maxHeight;\n    if (organization.rowHeight[last] < node.height + organization.verticalPadding) organization.rowHeight[last] = node.height + organization.verticalPadding;\n\n    var finalTotal = organization.rowHeight[longest] + organization.rowHeight[last];\n    organization.height += finalTotal - prevTotal;\n\n    this.shiftToLastRow(organization);\n  }\n};\n\nCoSELayout.prototype.tilingPreLayout = function () {\n  if (CoSEConstants.TILE) {\n    // Find zero degree nodes and create a compound for each level\n    this.groupZeroDegreeMembers();\n    // Tile and clear children of each compound\n    this.clearCompounds();\n    // Separately tile and clear zero degree nodes for each level\n    this.clearZeroDegreeMembers();\n  }\n};\n\nCoSELayout.prototype.tilingPostLayout = function () {\n  if (CoSEConstants.TILE) {\n    this.repopulateZeroDegreeMembers();\n    this.repopulateCompounds();\n  }\n};\n\n// -----------------------------------------------------------------------------\n// Section: Tree Reduction methods\n// -----------------------------------------------------------------------------\n// Reduce trees \nCoSELayout.prototype.reduceTrees = function () {\n  var prunedNodesAll = [];\n  var containsLeaf = true;\n  var node;\n\n  while (containsLeaf) {\n    var allNodes = this.graphManager.getAllNodes();\n    var prunedNodesInStepTemp = [];\n    containsLeaf = false;\n\n    for (var i = 0; i < allNodes.length; i++) {\n      node = allNodes[i];\n      if (node.getEdges().length == 1 && !node.getEdges()[0].isInterGraph && node.getChild() == null) {\n        if (CoSEConstants.PURE_INCREMENTAL) {\n          var otherEnd = node.getEdges()[0].getOtherEnd(node);\n          var relativePosition = new DimensionD(node.getCenterX() - otherEnd.getCenterX(), node.getCenterY() - otherEnd.getCenterY());\n          prunedNodesInStepTemp.push([node, node.getEdges()[0], node.getOwner(), relativePosition]);\n        } else {\n          prunedNodesInStepTemp.push([node, node.getEdges()[0], node.getOwner()]);\n        }\n        containsLeaf = true;\n      }\n    }\n    if (containsLeaf == true) {\n      var prunedNodesInStep = [];\n      for (var j = 0; j < prunedNodesInStepTemp.length; j++) {\n        if (prunedNodesInStepTemp[j][0].getEdges().length == 1) {\n          prunedNodesInStep.push(prunedNodesInStepTemp[j]);\n          prunedNodesInStepTemp[j][0].getOwner().remove(prunedNodesInStepTemp[j][0]);\n        }\n      }\n      prunedNodesAll.push(prunedNodesInStep);\n      this.graphManager.resetAllNodes();\n      this.graphManager.resetAllEdges();\n    }\n  }\n  this.prunedNodesAll = prunedNodesAll;\n};\n\n// Grow tree one step \nCoSELayout.prototype.growTree = function (prunedNodesAll) {\n  var lengthOfPrunedNodesInStep = prunedNodesAll.length;\n  var prunedNodesInStep = prunedNodesAll[lengthOfPrunedNodesInStep - 1];\n\n  var nodeData;\n  for (var i = 0; i < prunedNodesInStep.length; i++) {\n    nodeData = prunedNodesInStep[i];\n\n    this.findPlaceforPrunedNode(nodeData);\n\n    nodeData[2].add(nodeData[0]);\n    nodeData[2].add(nodeData[1], nodeData[1].source, nodeData[1].target);\n  }\n\n  prunedNodesAll.splice(prunedNodesAll.length - 1, 1);\n  this.graphManager.resetAllNodes();\n  this.graphManager.resetAllEdges();\n};\n\n// Find an appropriate position to replace pruned node, this method can be improved\nCoSELayout.prototype.findPlaceforPrunedNode = function (nodeData) {\n\n  var gridForPrunedNode;\n  var nodeToConnect;\n  var prunedNode = nodeData[0];\n  if (prunedNode == nodeData[1].source) {\n    nodeToConnect = nodeData[1].target;\n  } else {\n    nodeToConnect = nodeData[1].source;\n  }\n\n  if (CoSEConstants.PURE_INCREMENTAL) {\n    prunedNode.setCenter(nodeToConnect.getCenterX() + nodeData[3].getWidth(), nodeToConnect.getCenterY() + nodeData[3].getHeight());\n  } else {\n    var startGridX = nodeToConnect.startX;\n    var finishGridX = nodeToConnect.finishX;\n    var startGridY = nodeToConnect.startY;\n    var finishGridY = nodeToConnect.finishY;\n\n    var upNodeCount = 0;\n    var downNodeCount = 0;\n    var rightNodeCount = 0;\n    var leftNodeCount = 0;\n    var controlRegions = [upNodeCount, rightNodeCount, downNodeCount, leftNodeCount];\n\n    if (startGridY > 0) {\n      for (var i = startGridX; i <= finishGridX; i++) {\n        controlRegions[0] += this.grid[i][startGridY - 1].length + this.grid[i][startGridY].length - 1;\n      }\n    }\n    if (finishGridX < this.grid.length - 1) {\n      for (var i = startGridY; i <= finishGridY; i++) {\n        controlRegions[1] += this.grid[finishGridX + 1][i].length + this.grid[finishGridX][i].length - 1;\n      }\n    }\n    if (finishGridY < this.grid[0].length - 1) {\n      for (var i = startGridX; i <= finishGridX; i++) {\n        controlRegions[2] += this.grid[i][finishGridY + 1].length + this.grid[i][finishGridY].length - 1;\n      }\n    }\n    if (startGridX > 0) {\n      for (var i = startGridY; i <= finishGridY; i++) {\n        controlRegions[3] += this.grid[startGridX - 1][i].length + this.grid[startGridX][i].length - 1;\n      }\n    }\n    var min = Integer.MAX_VALUE;\n    var minCount;\n    var minIndex;\n    for (var j = 0; j < controlRegions.length; j++) {\n      if (controlRegions[j] < min) {\n        min = controlRegions[j];\n        minCount = 1;\n        minIndex = j;\n      } else if (controlRegions[j] == min) {\n        minCount++;\n      }\n    }\n\n    if (minCount == 3 && min == 0) {\n      if (controlRegions[0] == 0 && controlRegions[1] == 0 && controlRegions[2] == 0) {\n        gridForPrunedNode = 1;\n      } else if (controlRegions[0] == 0 && controlRegions[1] == 0 && controlRegions[3] == 0) {\n        gridForPrunedNode = 0;\n      } else if (controlRegions[0] == 0 && controlRegions[2] == 0 && controlRegions[3] == 0) {\n        gridForPrunedNode = 3;\n      } else if (controlRegions[1] == 0 && controlRegions[2] == 0 && controlRegions[3] == 0) {\n        gridForPrunedNode = 2;\n      }\n    } else if (minCount == 2 && min == 0) {\n      var random = Math.floor(Math.random() * 2);\n      if (controlRegions[0] == 0 && controlRegions[1] == 0) {\n        ;\n        if (random == 0) {\n          gridForPrunedNode = 0;\n        } else {\n          gridForPrunedNode = 1;\n        }\n      } else if (controlRegions[0] == 0 && controlRegions[2] == 0) {\n        if (random == 0) {\n          gridForPrunedNode = 0;\n        } else {\n          gridForPrunedNode = 2;\n        }\n      } else if (controlRegions[0] == 0 && controlRegions[3] == 0) {\n        if (random == 0) {\n          gridForPrunedNode = 0;\n        } else {\n          gridForPrunedNode = 3;\n        }\n      } else if (controlRegions[1] == 0 && controlRegions[2] == 0) {\n        if (random == 0) {\n          gridForPrunedNode = 1;\n        } else {\n          gridForPrunedNode = 2;\n        }\n      } else if (controlRegions[1] == 0 && controlRegions[3] == 0) {\n        if (random == 0) {\n          gridForPrunedNode = 1;\n        } else {\n          gridForPrunedNode = 3;\n        }\n      } else {\n        if (random == 0) {\n          gridForPrunedNode = 2;\n        } else {\n          gridForPrunedNode = 3;\n        }\n      }\n    } else if (minCount == 4 && min == 0) {\n      var random = Math.floor(Math.random() * 4);\n      gridForPrunedNode = random;\n    } else {\n      gridForPrunedNode = minIndex;\n    }\n\n    if (gridForPrunedNode == 0) {\n      prunedNode.setCenter(nodeToConnect.getCenterX(), nodeToConnect.getCenterY() - nodeToConnect.getHeight() / 2 - FDLayoutConstants.DEFAULT_EDGE_LENGTH - prunedNode.getHeight() / 2);\n    } else if (gridForPrunedNode == 1) {\n      prunedNode.setCenter(nodeToConnect.getCenterX() + nodeToConnect.getWidth() / 2 + FDLayoutConstants.DEFAULT_EDGE_LENGTH + prunedNode.getWidth() / 2, nodeToConnect.getCenterY());\n    } else if (gridForPrunedNode == 2) {\n      prunedNode.setCenter(nodeToConnect.getCenterX(), nodeToConnect.getCenterY() + nodeToConnect.getHeight() / 2 + FDLayoutConstants.DEFAULT_EDGE_LENGTH + prunedNode.getHeight() / 2);\n    } else {\n      prunedNode.setCenter(nodeToConnect.getCenterX() - nodeToConnect.getWidth() / 2 - FDLayoutConstants.DEFAULT_EDGE_LENGTH - prunedNode.getWidth() / 2, nodeToConnect.getCenterY());\n    }\n  }\n};\n\nmodule.exports = CoSELayout;\n\n/***/ }),\n\n/***/ 991:\n/***/ ((module, __unused_webpack_exports, __webpack_require__) => {\n\n\n\nvar FDLayoutNode = __webpack_require__(551).FDLayoutNode;\nvar IMath = __webpack_require__(551).IMath;\n\nfunction CoSENode(gm, loc, size, vNode) {\n  FDLayoutNode.call(this, gm, loc, size, vNode);\n}\n\nCoSENode.prototype = Object.create(FDLayoutNode.prototype);\nfor (var prop in FDLayoutNode) {\n  CoSENode[prop] = FDLayoutNode[prop];\n}\n\nCoSENode.prototype.calculateDisplacement = function () {\n  var layout = this.graphManager.getLayout();\n  // this check is for compound nodes that contain fixed nodes\n  if (this.getChild() != null && this.fixedNodeWeight) {\n    this.displacementX += layout.coolingFactor * (this.springForceX + this.repulsionForceX + this.gravitationForceX) / this.fixedNodeWeight;\n    this.displacementY += layout.coolingFactor * (this.springForceY + this.repulsionForceY + this.gravitationForceY) / this.fixedNodeWeight;\n  } else {\n    this.displacementX += layout.coolingFactor * (this.springForceX + this.repulsionForceX + this.gravitationForceX) / this.noOfChildren;\n    this.displacementY += layout.coolingFactor * (this.springForceY + this.repulsionForceY + this.gravitationForceY) / this.noOfChildren;\n  }\n\n  if (Math.abs(this.displacementX) > layout.coolingFactor * layout.maxNodeDisplacement) {\n    this.displacementX = layout.coolingFactor * layout.maxNodeDisplacement * IMath.sign(this.displacementX);\n  }\n\n  if (Math.abs(this.displacementY) > layout.coolingFactor * layout.maxNodeDisplacement) {\n    this.displacementY = layout.coolingFactor * layout.maxNodeDisplacement * IMath.sign(this.displacementY);\n  }\n\n  // non-empty compound node, propogate movement to children as well\n  if (this.child && this.child.getNodes().length > 0) {\n    this.propogateDisplacementToChildren(this.displacementX, this.displacementY);\n  }\n};\n\nCoSENode.prototype.propogateDisplacementToChildren = function (dX, dY) {\n  var nodes = this.getChild().getNodes();\n  var node;\n  for (var i = 0; i < nodes.length; i++) {\n    node = nodes[i];\n    if (node.getChild() == null) {\n      node.displacementX += dX;\n      node.displacementY += dY;\n    } else {\n      node.propogateDisplacementToChildren(dX, dY);\n    }\n  }\n};\n\nCoSENode.prototype.move = function () {\n  var layout = this.graphManager.getLayout();\n\n  // a simple node or an empty compound node, move it\n  if (this.child == null || this.child.getNodes().length == 0) {\n    this.moveBy(this.displacementX, this.displacementY);\n\n    layout.totalDisplacement += Math.abs(this.displacementX) + Math.abs(this.displacementY);\n  }\n\n  this.springForceX = 0;\n  this.springForceY = 0;\n  this.repulsionForceX = 0;\n  this.repulsionForceY = 0;\n  this.gravitationForceX = 0;\n  this.gravitationForceY = 0;\n  this.displacementX = 0;\n  this.displacementY = 0;\n};\n\nCoSENode.prototype.setPred1 = function (pred1) {\n  this.pred1 = pred1;\n};\n\nCoSENode.prototype.getPred1 = function () {\n  return pred1;\n};\n\nCoSENode.prototype.getPred2 = function () {\n  return pred2;\n};\n\nCoSENode.prototype.setNext = function (next) {\n  this.next = next;\n};\n\nCoSENode.prototype.getNext = function () {\n  return next;\n};\n\nCoSENode.prototype.setProcessed = function (processed) {\n  this.processed = processed;\n};\n\nCoSENode.prototype.isProcessed = function () {\n  return processed;\n};\n\nmodule.exports = CoSENode;\n\n/***/ }),\n\n/***/ 902:\n/***/ ((module, __unused_webpack_exports, __webpack_require__) => {\n\n\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar CoSEConstants = __webpack_require__(806);\nvar LinkedList = __webpack_require__(551).LinkedList;\nvar Matrix = __webpack_require__(551).Matrix;\nvar SVD = __webpack_require__(551).SVD;\n\nfunction ConstraintHandler() {}\n\nConstraintHandler.handleConstraints = function (layout) {\n  //  let layout = this.graphManager.getLayout();\n\n  // get constraints from layout\n  var constraints = {};\n  constraints.fixedNodeConstraint = layout.constraints.fixedNodeConstraint;\n  constraints.alignmentConstraint = layout.constraints.alignmentConstraint;\n  constraints.relativePlacementConstraint = layout.constraints.relativePlacementConstraint;\n\n  var idToNodeMap = new Map();\n  var nodeIndexes = new Map();\n  var xCoords = [];\n  var yCoords = [];\n\n  var allNodes = layout.getAllNodes();\n  var index = 0;\n  // fill index map and coordinates\n  for (var i = 0; i < allNodes.length; i++) {\n    var node = allNodes[i];\n    if (node.getChild() == null) {\n      nodeIndexes.set(node.id, index++);\n      xCoords.push(node.getCenterX());\n      yCoords.push(node.getCenterY());\n      idToNodeMap.set(node.id, node);\n    }\n  }\n\n  // if there exists relative placement constraint without gap value, set it to default \n  if (constraints.relativePlacementConstraint) {\n    constraints.relativePlacementConstraint.forEach(function (constraint) {\n      if (!constraint.gap && constraint.gap != 0) {\n        if (constraint.left) {\n          constraint.gap = CoSEConstants.DEFAULT_EDGE_LENGTH + idToNodeMap.get(constraint.left).getWidth() / 2 + idToNodeMap.get(constraint.right).getWidth() / 2;\n        } else {\n          constraint.gap = CoSEConstants.DEFAULT_EDGE_LENGTH + idToNodeMap.get(constraint.top).getHeight() / 2 + idToNodeMap.get(constraint.bottom).getHeight() / 2;\n        }\n      }\n    });\n  }\n\n  /* auxiliary functions */\n\n  // calculate difference between two position objects\n  var calculatePositionDiff = function calculatePositionDiff(pos1, pos2) {\n    return { x: pos1.x - pos2.x, y: pos1.y - pos2.y };\n  };\n\n  // calculate average position of the nodes\n  var calculateAvgPosition = function calculateAvgPosition(nodeIdSet) {\n    var xPosSum = 0;\n    var yPosSum = 0;\n    nodeIdSet.forEach(function (nodeId) {\n      xPosSum += xCoords[nodeIndexes.get(nodeId)];\n      yPosSum += yCoords[nodeIndexes.get(nodeId)];\n    });\n\n    return { x: xPosSum / nodeIdSet.size, y: yPosSum / nodeIdSet.size };\n  };\n\n  // find an appropriate positioning for the nodes in a given graph according to relative placement constraints\n  // this function also takes the fixed nodes and alignment constraints into account\n  // graph: dag to be evaluated, direction: \"horizontal\" or \"vertical\", \n  // fixedNodes: set of fixed nodes to consider during evaluation, dummyPositions: appropriate coordinates of the dummy nodes  \n  var findAppropriatePositionForRelativePlacement = function findAppropriatePositionForRelativePlacement(graph, direction, fixedNodes, dummyPositions, componentSources) {\n\n    // find union of two sets\n    function setUnion(setA, setB) {\n      var union = new Set(setA);\n      var _iteratorNormalCompletion = true;\n      var _didIteratorError = false;\n      var _iteratorError = undefined;\n\n      try {\n        for (var _iterator = setB[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n          var elem = _step.value;\n\n          union.add(elem);\n        }\n      } catch (err) {\n        _didIteratorError = true;\n        _iteratorError = err;\n      } finally {\n        try {\n          if (!_iteratorNormalCompletion && _iterator.return) {\n            _iterator.return();\n          }\n        } finally {\n          if (_didIteratorError) {\n            throw _iteratorError;\n          }\n        }\n      }\n\n      return union;\n    }\n\n    // find indegree count for each node\n    var inDegrees = new Map();\n\n    graph.forEach(function (value, key) {\n      inDegrees.set(key, 0);\n    });\n    graph.forEach(function (value, key) {\n      value.forEach(function (adjacent) {\n        inDegrees.set(adjacent.id, inDegrees.get(adjacent.id) + 1);\n      });\n    });\n\n    var positionMap = new Map(); // keeps the position for each node\n    var pastMap = new Map(); // keeps the predecessors(past) of a node\n    var queue = new LinkedList();\n    inDegrees.forEach(function (value, key) {\n      if (value == 0) {\n        queue.push(key);\n        if (!fixedNodes) {\n          if (direction == \"horizontal\") {\n            positionMap.set(key, nodeIndexes.has(key) ? xCoords[nodeIndexes.get(key)] : dummyPositions.get(key));\n          } else {\n            positionMap.set(key, nodeIndexes.has(key) ? yCoords[nodeIndexes.get(key)] : dummyPositions.get(key));\n          }\n        }\n      } else {\n        positionMap.set(key, Number.NEGATIVE_INFINITY);\n      }\n      if (fixedNodes) {\n        pastMap.set(key, new Set([key]));\n      }\n    });\n\n    // align sources of each component in enforcement phase\n    if (fixedNodes) {\n      componentSources.forEach(function (component) {\n        var fixedIds = [];\n        component.forEach(function (nodeId) {\n          if (fixedNodes.has(nodeId)) {\n            fixedIds.push(nodeId);\n          }\n        });\n        if (fixedIds.length > 0) {\n          var position = 0;\n          fixedIds.forEach(function (fixedId) {\n            if (direction == \"horizontal\") {\n              positionMap.set(fixedId, nodeIndexes.has(fixedId) ? xCoords[nodeIndexes.get(fixedId)] : dummyPositions.get(fixedId));\n              position += positionMap.get(fixedId);\n            } else {\n              positionMap.set(fixedId, nodeIndexes.has(fixedId) ? yCoords[nodeIndexes.get(fixedId)] : dummyPositions.get(fixedId));\n              position += positionMap.get(fixedId);\n            }\n          });\n          position = position / fixedIds.length;\n          component.forEach(function (nodeId) {\n            if (!fixedNodes.has(nodeId)) {\n              positionMap.set(nodeId, position);\n            }\n          });\n        } else {\n          var _position = 0;\n          component.forEach(function (nodeId) {\n            if (direction == \"horizontal\") {\n              _position += nodeIndexes.has(nodeId) ? xCoords[nodeIndexes.get(nodeId)] : dummyPositions.get(nodeId);\n            } else {\n              _position += nodeIndexes.has(nodeId) ? yCoords[nodeIndexes.get(nodeId)] : dummyPositions.get(nodeId);\n            }\n          });\n          _position = _position / component.length;\n          component.forEach(function (nodeId) {\n            positionMap.set(nodeId, _position);\n          });\n        }\n      });\n    }\n\n    // calculate positions of the nodes\n\n    var _loop = function _loop() {\n      var currentNode = queue.shift();\n      var neighbors = graph.get(currentNode);\n      neighbors.forEach(function (neighbor) {\n        if (positionMap.get(neighbor.id) < positionMap.get(currentNode) + neighbor.gap) {\n          if (fixedNodes && fixedNodes.has(neighbor.id)) {\n            var fixedPosition = void 0;\n            if (direction == \"horizontal\") {\n              fixedPosition = nodeIndexes.has(neighbor.id) ? xCoords[nodeIndexes.get(neighbor.id)] : dummyPositions.get(neighbor.id);\n            } else {\n              fixedPosition = nodeIndexes.has(neighbor.id) ? yCoords[nodeIndexes.get(neighbor.id)] : dummyPositions.get(neighbor.id);\n            }\n            positionMap.set(neighbor.id, fixedPosition); // TODO: may do unnecessary work\n            if (fixedPosition < positionMap.get(currentNode) + neighbor.gap) {\n              var diff = positionMap.get(currentNode) + neighbor.gap - fixedPosition;\n              pastMap.get(currentNode).forEach(function (nodeId) {\n                positionMap.set(nodeId, positionMap.get(nodeId) - diff);\n              });\n            }\n          } else {\n            positionMap.set(neighbor.id, positionMap.get(currentNode) + neighbor.gap);\n          }\n        }\n        inDegrees.set(neighbor.id, inDegrees.get(neighbor.id) - 1);\n        if (inDegrees.get(neighbor.id) == 0) {\n          queue.push(neighbor.id);\n        }\n        if (fixedNodes) {\n          pastMap.set(neighbor.id, setUnion(pastMap.get(currentNode), pastMap.get(neighbor.id)));\n        }\n      });\n    };\n\n    while (queue.length != 0) {\n      _loop();\n    }\n\n    // readjust position of the nodes after enforcement\n    if (fixedNodes) {\n      // find indegree count for each node\n      var sinkNodes = new Set();\n\n      graph.forEach(function (value, key) {\n        if (value.length == 0) {\n          sinkNodes.add(key);\n        }\n      });\n\n      var _components = [];\n      pastMap.forEach(function (value, key) {\n        if (sinkNodes.has(key)) {\n          var isFixedComponent = false;\n          var _iteratorNormalCompletion2 = true;\n          var _didIteratorError2 = false;\n          var _iteratorError2 = undefined;\n\n          try {\n            for (var _iterator2 = value[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n              var nodeId = _step2.value;\n\n              if (fixedNodes.has(nodeId)) {\n                isFixedComponent = true;\n              }\n            }\n          } catch (err) {\n            _didIteratorError2 = true;\n            _iteratorError2 = err;\n          } finally {\n            try {\n              if (!_iteratorNormalCompletion2 && _iterator2.return) {\n                _iterator2.return();\n              }\n            } finally {\n              if (_didIteratorError2) {\n                throw _iteratorError2;\n              }\n            }\n          }\n\n          if (!isFixedComponent) {\n            var isExist = false;\n            var existAt = void 0;\n            _components.forEach(function (component, index) {\n              if (component.has([].concat(_toConsumableArray(value))[0])) {\n                isExist = true;\n                existAt = index;\n              }\n            });\n            if (!isExist) {\n              _components.push(new Set(value));\n            } else {\n              value.forEach(function (ele) {\n                _components[existAt].add(ele);\n              });\n            }\n          }\n        }\n      });\n\n      _components.forEach(function (component, index) {\n        var minBefore = Number.POSITIVE_INFINITY;\n        var minAfter = Number.POSITIVE_INFINITY;\n        var maxBefore = Number.NEGATIVE_INFINITY;\n        var maxAfter = Number.NEGATIVE_INFINITY;\n\n        var _iteratorNormalCompletion3 = true;\n        var _didIteratorError3 = false;\n        var _iteratorError3 = undefined;\n\n        try {\n          for (var _iterator3 = component[Symbol.iterator](), _step3; !(_iteratorNormalCompletion3 = (_step3 = _iterator3.next()).done); _iteratorNormalCompletion3 = true) {\n            var nodeId = _step3.value;\n\n            var posBefore = void 0;\n            if (direction == \"horizontal\") {\n              posBefore = nodeIndexes.has(nodeId) ? xCoords[nodeIndexes.get(nodeId)] : dummyPositions.get(nodeId);\n            } else {\n              posBefore = nodeIndexes.has(nodeId) ? yCoords[nodeIndexes.get(nodeId)] : dummyPositions.get(nodeId);\n            }\n            var posAfter = positionMap.get(nodeId);\n            if (posBefore < minBefore) {\n              minBefore = posBefore;\n            }\n            if (posBefore > maxBefore) {\n              maxBefore = posBefore;\n            }\n            if (posAfter < minAfter) {\n              minAfter = posAfter;\n            }\n            if (posAfter > maxAfter) {\n              maxAfter = posAfter;\n            }\n          }\n        } catch (err) {\n          _didIteratorError3 = true;\n          _iteratorError3 = err;\n        } finally {\n          try {\n            if (!_iteratorNormalCompletion3 && _iterator3.return) {\n              _iterator3.return();\n            }\n          } finally {\n            if (_didIteratorError3) {\n              throw _iteratorError3;\n            }\n          }\n        }\n\n        var diff = (minBefore + maxBefore) / 2 - (minAfter + maxAfter) / 2;\n\n        var _iteratorNormalCompletion4 = true;\n        var _didIteratorError4 = false;\n        var _iteratorError4 = undefined;\n\n        try {\n          for (var _iterator4 = component[Symbol.iterator](), _step4; !(_iteratorNormalCompletion4 = (_step4 = _iterator4.next()).done); _iteratorNormalCompletion4 = true) {\n            var _nodeId = _step4.value;\n\n            positionMap.set(_nodeId, positionMap.get(_nodeId) + diff);\n          }\n        } catch (err) {\n          _didIteratorError4 = true;\n          _iteratorError4 = err;\n        } finally {\n          try {\n            if (!_iteratorNormalCompletion4 && _iterator4.return) {\n              _iterator4.return();\n            }\n          } finally {\n            if (_didIteratorError4) {\n              throw _iteratorError4;\n            }\n          }\n        }\n      });\n    }\n\n    return positionMap;\n  };\n\n  // find transformation based on rel. placement constraints if there are both alignment and rel. placement constraints\n  // or if there are only rel. placement contraints where the largest component isn't sufficiently large\n  var applyReflectionForRelativePlacement = function applyReflectionForRelativePlacement(relativePlacementConstraints) {\n    // variables to count votes\n    var reflectOnY = 0,\n        notReflectOnY = 0;\n    var reflectOnX = 0,\n        notReflectOnX = 0;\n\n    relativePlacementConstraints.forEach(function (constraint) {\n      if (constraint.left) {\n        xCoords[nodeIndexes.get(constraint.left)] - xCoords[nodeIndexes.get(constraint.right)] >= 0 ? reflectOnY++ : notReflectOnY++;\n      } else {\n        yCoords[nodeIndexes.get(constraint.top)] - yCoords[nodeIndexes.get(constraint.bottom)] >= 0 ? reflectOnX++ : notReflectOnX++;\n      }\n    });\n\n    if (reflectOnY > notReflectOnY && reflectOnX > notReflectOnX) {\n      for (var _i = 0; _i < nodeIndexes.size; _i++) {\n        xCoords[_i] = -1 * xCoords[_i];\n        yCoords[_i] = -1 * yCoords[_i];\n      }\n    } else if (reflectOnY > notReflectOnY) {\n      for (var _i2 = 0; _i2 < nodeIndexes.size; _i2++) {\n        xCoords[_i2] = -1 * xCoords[_i2];\n      }\n    } else if (reflectOnX > notReflectOnX) {\n      for (var _i3 = 0; _i3 < nodeIndexes.size; _i3++) {\n        yCoords[_i3] = -1 * yCoords[_i3];\n      }\n    }\n  };\n\n  // find weakly connected components in undirected graph\n  var findComponents = function findComponents(graph) {\n    // find weakly connected components in dag\n    var components = [];\n    var queue = new LinkedList();\n    var visited = new Set();\n    var count = 0;\n\n    graph.forEach(function (value, key) {\n      if (!visited.has(key)) {\n        components[count] = [];\n        var _currentNode = key;\n        queue.push(_currentNode);\n        visited.add(_currentNode);\n        components[count].push(_currentNode);\n\n        while (queue.length != 0) {\n          _currentNode = queue.shift();\n          var neighbors = graph.get(_currentNode);\n          neighbors.forEach(function (neighbor) {\n            if (!visited.has(neighbor.id)) {\n              queue.push(neighbor.id);\n              visited.add(neighbor.id);\n              components[count].push(neighbor.id);\n            }\n          });\n        }\n        count++;\n      }\n    });\n    return components;\n  };\n\n  // return undirected version of given dag\n  var dagToUndirected = function dagToUndirected(dag) {\n    var undirected = new Map();\n\n    dag.forEach(function (value, key) {\n      undirected.set(key, []);\n    });\n\n    dag.forEach(function (value, key) {\n      value.forEach(function (adjacent) {\n        undirected.get(key).push(adjacent);\n        undirected.get(adjacent.id).push({ id: key, gap: adjacent.gap, direction: adjacent.direction });\n      });\n    });\n\n    return undirected;\n  };\n\n  // return reversed (directions inverted) version of given dag\n  var dagToReversed = function dagToReversed(dag) {\n    var reversed = new Map();\n\n    dag.forEach(function (value, key) {\n      reversed.set(key, []);\n    });\n\n    dag.forEach(function (value, key) {\n      value.forEach(function (adjacent) {\n        reversed.get(adjacent.id).push({ id: key, gap: adjacent.gap, direction: adjacent.direction });\n      });\n    });\n\n    return reversed;\n  };\n\n  /****  apply transformation to the initial draft layout to better align with constrained nodes ****/\n  // solve the Orthogonal Procrustean Problem to rotate and/or reflect initial draft layout\n  // here we follow the solution in Chapter 20.2 of Borg, I. & Groenen, P. (2005) Modern Multidimensional Scaling: Theory and Applications \n\n  /* construct source and target configurations */\n\n  var targetMatrix = []; // A - target configuration\n  var sourceMatrix = []; // B - source configuration \n  var standardTransformation = false; // false for no transformation, true for standart (Procrustes) transformation (rotation and/or reflection)\n  var reflectionType = false; // false/true for reflection check, 'reflectOnX', 'reflectOnY' or 'reflectOnBoth' for reflection type if necessary\n  var fixedNodes = new Set();\n  var dag = new Map(); // adjacency list to keep directed acyclic graph (dag) that consists of relative placement constraints\n  var dagUndirected = new Map(); // undirected version of the dag\n  var components = []; // weakly connected components\n\n  // fill fixedNodes collection to use later\n  if (constraints.fixedNodeConstraint) {\n    constraints.fixedNodeConstraint.forEach(function (nodeData) {\n      fixedNodes.add(nodeData.nodeId);\n    });\n  }\n\n  // construct dag from relative placement constraints \n  if (constraints.relativePlacementConstraint) {\n    // construct both directed and undirected version of the dag\n    constraints.relativePlacementConstraint.forEach(function (constraint) {\n      if (constraint.left) {\n        if (dag.has(constraint.left)) {\n          dag.get(constraint.left).push({ id: constraint.right, gap: constraint.gap, direction: \"horizontal\" });\n        } else {\n          dag.set(constraint.left, [{ id: constraint.right, gap: constraint.gap, direction: \"horizontal\" }]);\n        }\n        if (!dag.has(constraint.right)) {\n          dag.set(constraint.right, []);\n        }\n      } else {\n        if (dag.has(constraint.top)) {\n          dag.get(constraint.top).push({ id: constraint.bottom, gap: constraint.gap, direction: \"vertical\" });\n        } else {\n          dag.set(constraint.top, [{ id: constraint.bottom, gap: constraint.gap, direction: \"vertical\" }]);\n        }\n        if (!dag.has(constraint.bottom)) {\n          dag.set(constraint.bottom, []);\n        }\n      }\n    });\n\n    dagUndirected = dagToUndirected(dag);\n    components = findComponents(dagUndirected);\n  }\n\n  if (CoSEConstants.TRANSFORM_ON_CONSTRAINT_HANDLING) {\n    // first check fixed node constraint\n    if (constraints.fixedNodeConstraint && constraints.fixedNodeConstraint.length > 1) {\n      constraints.fixedNodeConstraint.forEach(function (nodeData, i) {\n        targetMatrix[i] = [nodeData.position.x, nodeData.position.y];\n        sourceMatrix[i] = [xCoords[nodeIndexes.get(nodeData.nodeId)], yCoords[nodeIndexes.get(nodeData.nodeId)]];\n      });\n      standardTransformation = true;\n    } else if (constraints.alignmentConstraint) {\n      (function () {\n        // then check alignment constraint\n        var count = 0;\n        if (constraints.alignmentConstraint.vertical) {\n          var verticalAlign = constraints.alignmentConstraint.vertical;\n\n          var _loop2 = function _loop2(_i4) {\n            var alignmentSet = new Set();\n            verticalAlign[_i4].forEach(function (nodeId) {\n              alignmentSet.add(nodeId);\n            });\n            var intersection = new Set([].concat(_toConsumableArray(alignmentSet)).filter(function (x) {\n              return fixedNodes.has(x);\n            }));\n            var xPos = void 0;\n            if (intersection.size > 0) xPos = xCoords[nodeIndexes.get(intersection.values().next().value)];else xPos = calculateAvgPosition(alignmentSet).x;\n\n            verticalAlign[_i4].forEach(function (nodeId) {\n              targetMatrix[count] = [xPos, yCoords[nodeIndexes.get(nodeId)]];\n              sourceMatrix[count] = [xCoords[nodeIndexes.get(nodeId)], yCoords[nodeIndexes.get(nodeId)]];\n              count++;\n            });\n          };\n\n          for (var _i4 = 0; _i4 < verticalAlign.length; _i4++) {\n            _loop2(_i4);\n          }\n          standardTransformation = true;\n        }\n        if (constraints.alignmentConstraint.horizontal) {\n          var horizontalAlign = constraints.alignmentConstraint.horizontal;\n\n          var _loop3 = function _loop3(_i5) {\n            var alignmentSet = new Set();\n            horizontalAlign[_i5].forEach(function (nodeId) {\n              alignmentSet.add(nodeId);\n            });\n            var intersection = new Set([].concat(_toConsumableArray(alignmentSet)).filter(function (x) {\n              return fixedNodes.has(x);\n            }));\n            var yPos = void 0;\n            if (intersection.size > 0) yPos = xCoords[nodeIndexes.get(intersection.values().next().value)];else yPos = calculateAvgPosition(alignmentSet).y;\n\n            horizontalAlign[_i5].forEach(function (nodeId) {\n              targetMatrix[count] = [xCoords[nodeIndexes.get(nodeId)], yPos];\n              sourceMatrix[count] = [xCoords[nodeIndexes.get(nodeId)], yCoords[nodeIndexes.get(nodeId)]];\n              count++;\n            });\n          };\n\n          for (var _i5 = 0; _i5 < horizontalAlign.length; _i5++) {\n            _loop3(_i5);\n          }\n          standardTransformation = true;\n        }\n        if (constraints.relativePlacementConstraint) {\n          reflectionType = true;\n        }\n      })();\n    } else if (constraints.relativePlacementConstraint) {\n      // finally check relative placement constraint\n      // find largest component in dag\n      var largestComponentSize = 0;\n      var largestComponentIndex = 0;\n      for (var _i6 = 0; _i6 < components.length; _i6++) {\n        if (components[_i6].length > largestComponentSize) {\n          largestComponentSize = components[_i6].length;\n          largestComponentIndex = _i6;\n        }\n      }\n      // if largest component isn't dominant, then take the votes for reflection\n      if (largestComponentSize < dagUndirected.size / 2) {\n        applyReflectionForRelativePlacement(constraints.relativePlacementConstraint);\n        standardTransformation = false;\n        reflectionType = false;\n      } else {\n        // use largest component for transformation\n        // construct horizontal and vertical subgraphs in the largest component\n        var subGraphOnHorizontal = new Map();\n        var subGraphOnVertical = new Map();\n        var constraintsInlargestComponent = [];\n\n        components[largestComponentIndex].forEach(function (nodeId) {\n          dag.get(nodeId).forEach(function (adjacent) {\n            if (adjacent.direction == \"horizontal\") {\n              if (subGraphOnHorizontal.has(nodeId)) {\n                subGraphOnHorizontal.get(nodeId).push(adjacent);\n              } else {\n                subGraphOnHorizontal.set(nodeId, [adjacent]);\n              }\n              if (!subGraphOnHorizontal.has(adjacent.id)) {\n                subGraphOnHorizontal.set(adjacent.id, []);\n              }\n              constraintsInlargestComponent.push({ left: nodeId, right: adjacent.id });\n            } else {\n              if (subGraphOnVertical.has(nodeId)) {\n                subGraphOnVertical.get(nodeId).push(adjacent);\n              } else {\n                subGraphOnVertical.set(nodeId, [adjacent]);\n              }\n              if (!subGraphOnVertical.has(adjacent.id)) {\n                subGraphOnVertical.set(adjacent.id, []);\n              }\n              constraintsInlargestComponent.push({ top: nodeId, bottom: adjacent.id });\n            }\n          });\n        });\n\n        applyReflectionForRelativePlacement(constraintsInlargestComponent);\n        reflectionType = false;\n\n        // calculate appropriate positioning for subgraphs\n        var positionMapHorizontal = findAppropriatePositionForRelativePlacement(subGraphOnHorizontal, \"horizontal\");\n        var positionMapVertical = findAppropriatePositionForRelativePlacement(subGraphOnVertical, \"vertical\");\n\n        // construct source and target configuration\n        components[largestComponentIndex].forEach(function (nodeId, i) {\n          sourceMatrix[i] = [xCoords[nodeIndexes.get(nodeId)], yCoords[nodeIndexes.get(nodeId)]];\n          targetMatrix[i] = [];\n          if (positionMapHorizontal.has(nodeId)) {\n            targetMatrix[i][0] = positionMapHorizontal.get(nodeId);\n          } else {\n            targetMatrix[i][0] = xCoords[nodeIndexes.get(nodeId)];\n          }\n          if (positionMapVertical.has(nodeId)) {\n            targetMatrix[i][1] = positionMapVertical.get(nodeId);\n          } else {\n            targetMatrix[i][1] = yCoords[nodeIndexes.get(nodeId)];\n          }\n        });\n\n        standardTransformation = true;\n      }\n    }\n\n    // if transformation is required, then calculate and apply transformation matrix\n    if (standardTransformation) {\n      /* calculate transformation matrix */\n      var transformationMatrix = void 0;\n      var targetMatrixTranspose = Matrix.transpose(targetMatrix); // A'\n      var sourceMatrixTranspose = Matrix.transpose(sourceMatrix); // B'\n\n      // centralize transpose matrices\n      for (var _i7 = 0; _i7 < targetMatrixTranspose.length; _i7++) {\n        targetMatrixTranspose[_i7] = Matrix.multGamma(targetMatrixTranspose[_i7]);\n        sourceMatrixTranspose[_i7] = Matrix.multGamma(sourceMatrixTranspose[_i7]);\n      }\n\n      // do actual calculation for transformation matrix\n      var tempMatrix = Matrix.multMat(targetMatrixTranspose, Matrix.transpose(sourceMatrixTranspose)); // tempMatrix = A'B\n      var SVDResult = SVD.svd(tempMatrix); // SVD(A'B) = USV', svd function returns U, S and V \n      transformationMatrix = Matrix.multMat(SVDResult.V, Matrix.transpose(SVDResult.U)); // transformationMatrix = T = VU'\n\n      /* apply found transformation matrix to obtain final draft layout */\n      for (var _i8 = 0; _i8 < nodeIndexes.size; _i8++) {\n        var temp1 = [xCoords[_i8], yCoords[_i8]];\n        var temp2 = [transformationMatrix[0][0], transformationMatrix[1][0]];\n        var temp3 = [transformationMatrix[0][1], transformationMatrix[1][1]];\n        xCoords[_i8] = Matrix.dotProduct(temp1, temp2);\n        yCoords[_i8] = Matrix.dotProduct(temp1, temp3);\n      }\n\n      // applied only both alignment and rel. placement constraints exist\n      if (reflectionType) {\n        applyReflectionForRelativePlacement(constraints.relativePlacementConstraint);\n      }\n    }\n  }\n\n  if (CoSEConstants.ENFORCE_CONSTRAINTS) {\n    /****  enforce constraints on the transformed draft layout ****/\n\n    /* first enforce fixed node constraint */\n\n    if (constraints.fixedNodeConstraint && constraints.fixedNodeConstraint.length > 0) {\n      var translationAmount = { x: 0, y: 0 };\n      constraints.fixedNodeConstraint.forEach(function (nodeData, i) {\n        var posInTheory = { x: xCoords[nodeIndexes.get(nodeData.nodeId)], y: yCoords[nodeIndexes.get(nodeData.nodeId)] };\n        var posDesired = nodeData.position;\n        var posDiff = calculatePositionDiff(posDesired, posInTheory);\n        translationAmount.x += posDiff.x;\n        translationAmount.y += posDiff.y;\n      });\n      translationAmount.x /= constraints.fixedNodeConstraint.length;\n      translationAmount.y /= constraints.fixedNodeConstraint.length;\n\n      xCoords.forEach(function (value, i) {\n        xCoords[i] += translationAmount.x;\n      });\n\n      yCoords.forEach(function (value, i) {\n        yCoords[i] += translationAmount.y;\n      });\n\n      constraints.fixedNodeConstraint.forEach(function (nodeData) {\n        xCoords[nodeIndexes.get(nodeData.nodeId)] = nodeData.position.x;\n        yCoords[nodeIndexes.get(nodeData.nodeId)] = nodeData.position.y;\n      });\n    }\n\n    /* then enforce alignment constraint */\n\n    if (constraints.alignmentConstraint) {\n      if (constraints.alignmentConstraint.vertical) {\n        var xAlign = constraints.alignmentConstraint.vertical;\n\n        var _loop4 = function _loop4(_i9) {\n          var alignmentSet = new Set();\n          xAlign[_i9].forEach(function (nodeId) {\n            alignmentSet.add(nodeId);\n          });\n          var intersection = new Set([].concat(_toConsumableArray(alignmentSet)).filter(function (x) {\n            return fixedNodes.has(x);\n          }));\n          var xPos = void 0;\n          if (intersection.size > 0) xPos = xCoords[nodeIndexes.get(intersection.values().next().value)];else xPos = calculateAvgPosition(alignmentSet).x;\n\n          alignmentSet.forEach(function (nodeId) {\n            if (!fixedNodes.has(nodeId)) xCoords[nodeIndexes.get(nodeId)] = xPos;\n          });\n        };\n\n        for (var _i9 = 0; _i9 < xAlign.length; _i9++) {\n          _loop4(_i9);\n        }\n      }\n      if (constraints.alignmentConstraint.horizontal) {\n        var yAlign = constraints.alignmentConstraint.horizontal;\n\n        var _loop5 = function _loop5(_i10) {\n          var alignmentSet = new Set();\n          yAlign[_i10].forEach(function (nodeId) {\n            alignmentSet.add(nodeId);\n          });\n          var intersection = new Set([].concat(_toConsumableArray(alignmentSet)).filter(function (x) {\n            return fixedNodes.has(x);\n          }));\n          var yPos = void 0;\n          if (intersection.size > 0) yPos = yCoords[nodeIndexes.get(intersection.values().next().value)];else yPos = calculateAvgPosition(alignmentSet).y;\n\n          alignmentSet.forEach(function (nodeId) {\n            if (!fixedNodes.has(nodeId)) yCoords[nodeIndexes.get(nodeId)] = yPos;\n          });\n        };\n\n        for (var _i10 = 0; _i10 < yAlign.length; _i10++) {\n          _loop5(_i10);\n        }\n      }\n    }\n\n    /* finally enforce relative placement constraint */\n\n    if (constraints.relativePlacementConstraint) {\n      (function () {\n        var nodeToDummyForVerticalAlignment = new Map();\n        var nodeToDummyForHorizontalAlignment = new Map();\n        var dummyToNodeForVerticalAlignment = new Map();\n        var dummyToNodeForHorizontalAlignment = new Map();\n        var dummyPositionsForVerticalAlignment = new Map();\n        var dummyPositionsForHorizontalAlignment = new Map();\n        var fixedNodesOnHorizontal = new Set();\n        var fixedNodesOnVertical = new Set();\n\n        // fill maps and sets      \n        fixedNodes.forEach(function (nodeId) {\n          fixedNodesOnHorizontal.add(nodeId);\n          fixedNodesOnVertical.add(nodeId);\n        });\n\n        if (constraints.alignmentConstraint) {\n          if (constraints.alignmentConstraint.vertical) {\n            var verticalAlignment = constraints.alignmentConstraint.vertical;\n\n            var _loop6 = function _loop6(_i11) {\n              dummyToNodeForVerticalAlignment.set(\"dummy\" + _i11, []);\n              verticalAlignment[_i11].forEach(function (nodeId) {\n                nodeToDummyForVerticalAlignment.set(nodeId, \"dummy\" + _i11);\n                dummyToNodeForVerticalAlignment.get(\"dummy\" + _i11).push(nodeId);\n                if (fixedNodes.has(nodeId)) {\n                  fixedNodesOnHorizontal.add(\"dummy\" + _i11);\n                }\n              });\n              dummyPositionsForVerticalAlignment.set(\"dummy\" + _i11, xCoords[nodeIndexes.get(verticalAlignment[_i11][0])]);\n            };\n\n            for (var _i11 = 0; _i11 < verticalAlignment.length; _i11++) {\n              _loop6(_i11);\n            }\n          }\n          if (constraints.alignmentConstraint.horizontal) {\n            var horizontalAlignment = constraints.alignmentConstraint.horizontal;\n\n            var _loop7 = function _loop7(_i12) {\n              dummyToNodeForHorizontalAlignment.set(\"dummy\" + _i12, []);\n              horizontalAlignment[_i12].forEach(function (nodeId) {\n                nodeToDummyForHorizontalAlignment.set(nodeId, \"dummy\" + _i12);\n                dummyToNodeForHorizontalAlignment.get(\"dummy\" + _i12).push(nodeId);\n                if (fixedNodes.has(nodeId)) {\n                  fixedNodesOnVertical.add(\"dummy\" + _i12);\n                }\n              });\n              dummyPositionsForHorizontalAlignment.set(\"dummy\" + _i12, yCoords[nodeIndexes.get(horizontalAlignment[_i12][0])]);\n            };\n\n            for (var _i12 = 0; _i12 < horizontalAlignment.length; _i12++) {\n              _loop7(_i12);\n            }\n          }\n        }\n\n        // construct horizontal and vertical dags (subgraphs) from overall dag\n        var dagOnHorizontal = new Map();\n        var dagOnVertical = new Map();\n\n        var _loop8 = function _loop8(nodeId) {\n          dag.get(nodeId).forEach(function (adjacent) {\n            var sourceId = void 0;\n            var targetNode = void 0;\n            if (adjacent[\"direction\"] == \"horizontal\") {\n              sourceId = nodeToDummyForVerticalAlignment.get(nodeId) ? nodeToDummyForVerticalAlignment.get(nodeId) : nodeId;\n              if (nodeToDummyForVerticalAlignment.get(adjacent.id)) {\n                targetNode = { id: nodeToDummyForVerticalAlignment.get(adjacent.id), gap: adjacent.gap, direction: adjacent.direction };\n              } else {\n                targetNode = adjacent;\n              }\n              if (dagOnHorizontal.has(sourceId)) {\n                dagOnHorizontal.get(sourceId).push(targetNode);\n              } else {\n                dagOnHorizontal.set(sourceId, [targetNode]);\n              }\n              if (!dagOnHorizontal.has(targetNode.id)) {\n                dagOnHorizontal.set(targetNode.id, []);\n              }\n            } else {\n              sourceId = nodeToDummyForHorizontalAlignment.get(nodeId) ? nodeToDummyForHorizontalAlignment.get(nodeId) : nodeId;\n              if (nodeToDummyForHorizontalAlignment.get(adjacent.id)) {\n                targetNode = { id: nodeToDummyForHorizontalAlignment.get(adjacent.id), gap: adjacent.gap, direction: adjacent.direction };\n              } else {\n                targetNode = adjacent;\n              }\n              if (dagOnVertical.has(sourceId)) {\n                dagOnVertical.get(sourceId).push(targetNode);\n              } else {\n                dagOnVertical.set(sourceId, [targetNode]);\n              }\n              if (!dagOnVertical.has(targetNode.id)) {\n                dagOnVertical.set(targetNode.id, []);\n              }\n            }\n          });\n        };\n\n        var _iteratorNormalCompletion5 = true;\n        var _didIteratorError5 = false;\n        var _iteratorError5 = undefined;\n\n        try {\n          for (var _iterator5 = dag.keys()[Symbol.iterator](), _step5; !(_iteratorNormalCompletion5 = (_step5 = _iterator5.next()).done); _iteratorNormalCompletion5 = true) {\n            var nodeId = _step5.value;\n\n            _loop8(nodeId);\n          }\n\n          // find source nodes of each component in horizontal and vertical dags\n        } catch (err) {\n          _didIteratorError5 = true;\n          _iteratorError5 = err;\n        } finally {\n          try {\n            if (!_iteratorNormalCompletion5 && _iterator5.return) {\n              _iterator5.return();\n            }\n          } finally {\n            if (_didIteratorError5) {\n              throw _iteratorError5;\n            }\n          }\n        }\n\n        var undirectedOnHorizontal = dagToUndirected(dagOnHorizontal);\n        var undirectedOnVertical = dagToUndirected(dagOnVertical);\n        var componentsOnHorizontal = findComponents(undirectedOnHorizontal);\n        var componentsOnVertical = findComponents(undirectedOnVertical);\n        var reversedDagOnHorizontal = dagToReversed(dagOnHorizontal);\n        var reversedDagOnVertical = dagToReversed(dagOnVertical);\n        var componentSourcesOnHorizontal = [];\n        var componentSourcesOnVertical = [];\n\n        componentsOnHorizontal.forEach(function (component, index) {\n          componentSourcesOnHorizontal[index] = [];\n          component.forEach(function (nodeId) {\n            if (reversedDagOnHorizontal.get(nodeId).length == 0) {\n              componentSourcesOnHorizontal[index].push(nodeId);\n            }\n          });\n        });\n\n        componentsOnVertical.forEach(function (component, index) {\n          componentSourcesOnVertical[index] = [];\n          component.forEach(function (nodeId) {\n            if (reversedDagOnVertical.get(nodeId).length == 0) {\n              componentSourcesOnVertical[index].push(nodeId);\n            }\n          });\n        });\n\n        // calculate appropriate positioning for subgraphs\n        var positionMapHorizontal = findAppropriatePositionForRelativePlacement(dagOnHorizontal, \"horizontal\", fixedNodesOnHorizontal, dummyPositionsForVerticalAlignment, componentSourcesOnHorizontal);\n        var positionMapVertical = findAppropriatePositionForRelativePlacement(dagOnVertical, \"vertical\", fixedNodesOnVertical, dummyPositionsForHorizontalAlignment, componentSourcesOnVertical);\n\n        // update positions of the nodes based on relative placement constraints\n\n        var _loop9 = function _loop9(key) {\n          if (dummyToNodeForVerticalAlignment.get(key)) {\n            dummyToNodeForVerticalAlignment.get(key).forEach(function (nodeId) {\n              xCoords[nodeIndexes.get(nodeId)] = positionMapHorizontal.get(key);\n            });\n          } else {\n            xCoords[nodeIndexes.get(key)] = positionMapHorizontal.get(key);\n          }\n        };\n\n        var _iteratorNormalCompletion6 = true;\n        var _didIteratorError6 = false;\n        var _iteratorError6 = undefined;\n\n        try {\n          for (var _iterator6 = positionMapHorizontal.keys()[Symbol.iterator](), _step6; !(_iteratorNormalCompletion6 = (_step6 = _iterator6.next()).done); _iteratorNormalCompletion6 = true) {\n            var key = _step6.value;\n\n            _loop9(key);\n          }\n        } catch (err) {\n          _didIteratorError6 = true;\n          _iteratorError6 = err;\n        } finally {\n          try {\n            if (!_iteratorNormalCompletion6 && _iterator6.return) {\n              _iterator6.return();\n            }\n          } finally {\n            if (_didIteratorError6) {\n              throw _iteratorError6;\n            }\n          }\n        }\n\n        var _loop10 = function _loop10(key) {\n          if (dummyToNodeForHorizontalAlignment.get(key)) {\n            dummyToNodeForHorizontalAlignment.get(key).forEach(function (nodeId) {\n              yCoords[nodeIndexes.get(nodeId)] = positionMapVertical.get(key);\n            });\n          } else {\n            yCoords[nodeIndexes.get(key)] = positionMapVertical.get(key);\n          }\n        };\n\n        var _iteratorNormalCompletion7 = true;\n        var _didIteratorError7 = false;\n        var _iteratorError7 = undefined;\n\n        try {\n          for (var _iterator7 = positionMapVertical.keys()[Symbol.iterator](), _step7; !(_iteratorNormalCompletion7 = (_step7 = _iterator7.next()).done); _iteratorNormalCompletion7 = true) {\n            var key = _step7.value;\n\n            _loop10(key);\n          }\n        } catch (err) {\n          _didIteratorError7 = true;\n          _iteratorError7 = err;\n        } finally {\n          try {\n            if (!_iteratorNormalCompletion7 && _iterator7.return) {\n              _iterator7.return();\n            }\n          } finally {\n            if (_didIteratorError7) {\n              throw _iteratorError7;\n            }\n          }\n        }\n      })();\n    }\n  }\n\n  // assign new coordinates to nodes after constraint handling\n  for (var _i13 = 0; _i13 < allNodes.length; _i13++) {\n    var _node = allNodes[_i13];\n    if (_node.getChild() == null) {\n      _node.setCenter(xCoords[nodeIndexes.get(_node.id)], yCoords[nodeIndexes.get(_node.id)]);\n    }\n  }\n};\n\nmodule.exports = ConstraintHandler;\n\n/***/ }),\n\n/***/ 551:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__551__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t\n/******/ \t// startup\n/******/ \t// Load entry module and return exports\n/******/ \t// This entry module is referenced by other modules so it can't be inlined\n/******/ \tvar __webpack_exports__ = __webpack_require__(45);\n/******/ \t\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"layoutBase\"] = factory();\n\telse\n\t\troot[\"layoutBase\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// identity function for calling harmony imports with the correct context\n/******/ \t__webpack_require__.i = function(value) { return value; };\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 28);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction LayoutConstants() {}\n\n/**\r\n * Layout Quality: 0:draft, 1:default, 2:proof\r\n */\nLayoutConstants.QUALITY = 1;\n\n/**\r\n * Default parameters\r\n */\nLayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED = false;\nLayoutConstants.DEFAULT_INCREMENTAL = false;\nLayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT = true;\nLayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT = false;\nLayoutConstants.DEFAULT_ANIMATION_PERIOD = 50;\nLayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES = false;\n\n// -----------------------------------------------------------------------------\n// Section: General other constants\n// -----------------------------------------------------------------------------\n/*\r\n * Margins of a graph to be applied on bouding rectangle of its contents. We\r\n * assume margins on all four sides to be uniform.\r\n */\nLayoutConstants.DEFAULT_GRAPH_MARGIN = 15;\n\n/*\r\n * Whether to consider labels in node dimensions or not\r\n */\nLayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS = false;\n\n/*\r\n * Default dimension of a non-compound node.\r\n */\nLayoutConstants.SIMPLE_NODE_SIZE = 40;\n\n/*\r\n * Default dimension of a non-compound node.\r\n */\nLayoutConstants.SIMPLE_NODE_HALF_SIZE = LayoutConstants.SIMPLE_NODE_SIZE / 2;\n\n/*\r\n * Empty compound node size. When a compound node is empty, its both\r\n * dimensions should be of this value.\r\n */\nLayoutConstants.EMPTY_COMPOUND_NODE_SIZE = 40;\n\n/*\r\n * Minimum length that an edge should take during layout\r\n */\nLayoutConstants.MIN_EDGE_LENGTH = 1;\n\n/*\r\n * World boundaries that layout operates on\r\n */\nLayoutConstants.WORLD_BOUNDARY = 1000000;\n\n/*\r\n * World boundaries that random positioning can be performed with\r\n */\nLayoutConstants.INITIAL_WORLD_BOUNDARY = LayoutConstants.WORLD_BOUNDARY / 1000;\n\n/*\r\n * Coordinates of the world center\r\n */\nLayoutConstants.WORLD_CENTER_X = 1200;\nLayoutConstants.WORLD_CENTER_Y = 900;\n\nmodule.exports = LayoutConstants;\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __webpack_require__(2);\nvar IGeometry = __webpack_require__(8);\nvar IMath = __webpack_require__(9);\n\nfunction LEdge(source, target, vEdge) {\n  LGraphObject.call(this, vEdge);\n\n  this.isOverlapingSourceAndTarget = false;\n  this.vGraphObject = vEdge;\n  this.bendpoints = [];\n  this.source = source;\n  this.target = target;\n}\n\nLEdge.prototype = Object.create(LGraphObject.prototype);\n\nfor (var prop in LGraphObject) {\n  LEdge[prop] = LGraphObject[prop];\n}\n\nLEdge.prototype.getSource = function () {\n  return this.source;\n};\n\nLEdge.prototype.getTarget = function () {\n  return this.target;\n};\n\nLEdge.prototype.isInterGraph = function () {\n  return this.isInterGraph;\n};\n\nLEdge.prototype.getLength = function () {\n  return this.length;\n};\n\nLEdge.prototype.isOverlapingSourceAndTarget = function () {\n  return this.isOverlapingSourceAndTarget;\n};\n\nLEdge.prototype.getBendpoints = function () {\n  return this.bendpoints;\n};\n\nLEdge.prototype.getLca = function () {\n  return this.lca;\n};\n\nLEdge.prototype.getSourceInLca = function () {\n  return this.sourceInLca;\n};\n\nLEdge.prototype.getTargetInLca = function () {\n  return this.targetInLca;\n};\n\nLEdge.prototype.getOtherEnd = function (node) {\n  if (this.source === node) {\n    return this.target;\n  } else if (this.target === node) {\n    return this.source;\n  } else {\n    throw \"Node is not incident with this edge\";\n  }\n};\n\nLEdge.prototype.getOtherEndInGraph = function (node, graph) {\n  var otherEnd = this.getOtherEnd(node);\n  var root = graph.getGraphManager().getRoot();\n\n  while (true) {\n    if (otherEnd.getOwner() == graph) {\n      return otherEnd;\n    }\n\n    if (otherEnd.getOwner() == root) {\n      break;\n    }\n\n    otherEnd = otherEnd.getOwner().getParent();\n  }\n\n  return null;\n};\n\nLEdge.prototype.updateLength = function () {\n  var clipPointCoordinates = new Array(4);\n\n  this.isOverlapingSourceAndTarget = IGeometry.getIntersection(this.target.getRect(), this.source.getRect(), clipPointCoordinates);\n\n  if (!this.isOverlapingSourceAndTarget) {\n    this.lengthX = clipPointCoordinates[0] - clipPointCoordinates[2];\n    this.lengthY = clipPointCoordinates[1] - clipPointCoordinates[3];\n\n    if (Math.abs(this.lengthX) < 1.0) {\n      this.lengthX = IMath.sign(this.lengthX);\n    }\n\n    if (Math.abs(this.lengthY) < 1.0) {\n      this.lengthY = IMath.sign(this.lengthY);\n    }\n\n    this.length = Math.sqrt(this.lengthX * this.lengthX + this.lengthY * this.lengthY);\n  }\n};\n\nLEdge.prototype.updateLengthSimple = function () {\n  this.lengthX = this.target.getCenterX() - this.source.getCenterX();\n  this.lengthY = this.target.getCenterY() - this.source.getCenterY();\n\n  if (Math.abs(this.lengthX) < 1.0) {\n    this.lengthX = IMath.sign(this.lengthX);\n  }\n\n  if (Math.abs(this.lengthY) < 1.0) {\n    this.lengthY = IMath.sign(this.lengthY);\n  }\n\n  this.length = Math.sqrt(this.lengthX * this.lengthX + this.lengthY * this.lengthY);\n};\n\nmodule.exports = LEdge;\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction LGraphObject(vGraphObject) {\n  this.vGraphObject = vGraphObject;\n}\n\nmodule.exports = LGraphObject;\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __webpack_require__(2);\nvar Integer = __webpack_require__(10);\nvar RectangleD = __webpack_require__(13);\nvar LayoutConstants = __webpack_require__(0);\nvar RandomSeed = __webpack_require__(16);\nvar PointD = __webpack_require__(5);\n\nfunction LNode(gm, loc, size, vNode) {\n  //Alternative constructor 1 : LNode(LGraphManager gm, Point loc, Dimension size, Object vNode)\n  if (size == null && vNode == null) {\n    vNode = loc;\n  }\n\n  LGraphObject.call(this, vNode);\n\n  //Alternative constructor 2 : LNode(Layout layout, Object vNode)\n  if (gm.graphManager != null) gm = gm.graphManager;\n\n  this.estimatedSize = Integer.MIN_VALUE;\n  this.inclusionTreeDepth = Integer.MAX_VALUE;\n  this.vGraphObject = vNode;\n  this.edges = [];\n  this.graphManager = gm;\n\n  if (size != null && loc != null) this.rect = new RectangleD(loc.x, loc.y, size.width, size.height);else this.rect = new RectangleD();\n}\n\nLNode.prototype = Object.create(LGraphObject.prototype);\nfor (var prop in LGraphObject) {\n  LNode[prop] = LGraphObject[prop];\n}\n\nLNode.prototype.getEdges = function () {\n  return this.edges;\n};\n\nLNode.prototype.getChild = function () {\n  return this.child;\n};\n\nLNode.prototype.getOwner = function () {\n  //  if (this.owner != null) {\n  //    if (!(this.owner == null || this.owner.getNodes().indexOf(this) > -1)) {\n  //      throw \"assert failed\";\n  //    }\n  //  }\n\n  return this.owner;\n};\n\nLNode.prototype.getWidth = function () {\n  return this.rect.width;\n};\n\nLNode.prototype.setWidth = function (width) {\n  this.rect.width = width;\n};\n\nLNode.prototype.getHeight = function () {\n  return this.rect.height;\n};\n\nLNode.prototype.setHeight = function (height) {\n  this.rect.height = height;\n};\n\nLNode.prototype.getCenterX = function () {\n  return this.rect.x + this.rect.width / 2;\n};\n\nLNode.prototype.getCenterY = function () {\n  return this.rect.y + this.rect.height / 2;\n};\n\nLNode.prototype.getCenter = function () {\n  return new PointD(this.rect.x + this.rect.width / 2, this.rect.y + this.rect.height / 2);\n};\n\nLNode.prototype.getLocation = function () {\n  return new PointD(this.rect.x, this.rect.y);\n};\n\nLNode.prototype.getRect = function () {\n  return this.rect;\n};\n\nLNode.prototype.getDiagonal = function () {\n  return Math.sqrt(this.rect.width * this.rect.width + this.rect.height * this.rect.height);\n};\n\n/**\n * This method returns half the diagonal length of this node.\n */\nLNode.prototype.getHalfTheDiagonal = function () {\n  return Math.sqrt(this.rect.height * this.rect.height + this.rect.width * this.rect.width) / 2;\n};\n\nLNode.prototype.setRect = function (upperLeft, dimension) {\n  this.rect.x = upperLeft.x;\n  this.rect.y = upperLeft.y;\n  this.rect.width = dimension.width;\n  this.rect.height = dimension.height;\n};\n\nLNode.prototype.setCenter = function (cx, cy) {\n  this.rect.x = cx - this.rect.width / 2;\n  this.rect.y = cy - this.rect.height / 2;\n};\n\nLNode.prototype.setLocation = function (x, y) {\n  this.rect.x = x;\n  this.rect.y = y;\n};\n\nLNode.prototype.moveBy = function (dx, dy) {\n  this.rect.x += dx;\n  this.rect.y += dy;\n};\n\nLNode.prototype.getEdgeListToNode = function (to) {\n  var edgeList = [];\n  var edge;\n  var self = this;\n\n  self.edges.forEach(function (edge) {\n\n    if (edge.target == to) {\n      if (edge.source != self) throw \"Incorrect edge source!\";\n\n      edgeList.push(edge);\n    }\n  });\n\n  return edgeList;\n};\n\nLNode.prototype.getEdgesBetween = function (other) {\n  var edgeList = [];\n  var edge;\n\n  var self = this;\n  self.edges.forEach(function (edge) {\n\n    if (!(edge.source == self || edge.target == self)) throw \"Incorrect edge source and/or target\";\n\n    if (edge.target == other || edge.source == other) {\n      edgeList.push(edge);\n    }\n  });\n\n  return edgeList;\n};\n\nLNode.prototype.getNeighborsList = function () {\n  var neighbors = new Set();\n\n  var self = this;\n  self.edges.forEach(function (edge) {\n\n    if (edge.source == self) {\n      neighbors.add(edge.target);\n    } else {\n      if (edge.target != self) {\n        throw \"Incorrect incidency!\";\n      }\n\n      neighbors.add(edge.source);\n    }\n  });\n\n  return neighbors;\n};\n\nLNode.prototype.withChildren = function () {\n  var withNeighborsList = new Set();\n  var childNode;\n  var children;\n\n  withNeighborsList.add(this);\n\n  if (this.child != null) {\n    var nodes = this.child.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      childNode = nodes[i];\n      children = childNode.withChildren();\n      children.forEach(function (node) {\n        withNeighborsList.add(node);\n      });\n    }\n  }\n\n  return withNeighborsList;\n};\n\nLNode.prototype.getNoOfChildren = function () {\n  var noOfChildren = 0;\n  var childNode;\n\n  if (this.child == null) {\n    noOfChildren = 1;\n  } else {\n    var nodes = this.child.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      childNode = nodes[i];\n\n      noOfChildren += childNode.getNoOfChildren();\n    }\n  }\n\n  if (noOfChildren == 0) {\n    noOfChildren = 1;\n  }\n  return noOfChildren;\n};\n\nLNode.prototype.getEstimatedSize = function () {\n  if (this.estimatedSize == Integer.MIN_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.estimatedSize;\n};\n\nLNode.prototype.calcEstimatedSize = function () {\n  if (this.child == null) {\n    return this.estimatedSize = (this.rect.width + this.rect.height) / 2;\n  } else {\n    this.estimatedSize = this.child.calcEstimatedSize();\n    this.rect.width = this.estimatedSize;\n    this.rect.height = this.estimatedSize;\n\n    return this.estimatedSize;\n  }\n};\n\nLNode.prototype.scatter = function () {\n  var randomCenterX;\n  var randomCenterY;\n\n  var minX = -LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  var maxX = LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  randomCenterX = LayoutConstants.WORLD_CENTER_X + RandomSeed.nextDouble() * (maxX - minX) + minX;\n\n  var minY = -LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  var maxY = LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  randomCenterY = LayoutConstants.WORLD_CENTER_Y + RandomSeed.nextDouble() * (maxY - minY) + minY;\n\n  this.rect.x = randomCenterX;\n  this.rect.y = randomCenterY;\n};\n\nLNode.prototype.updateBounds = function () {\n  if (this.getChild() == null) {\n    throw \"assert failed\";\n  }\n  if (this.getChild().getNodes().length != 0) {\n    // wrap the children nodes by re-arranging the boundaries\n    var childGraph = this.getChild();\n    childGraph.updateBounds(true);\n\n    this.rect.x = childGraph.getLeft();\n    this.rect.y = childGraph.getTop();\n\n    this.setWidth(childGraph.getRight() - childGraph.getLeft());\n    this.setHeight(childGraph.getBottom() - childGraph.getTop());\n\n    // Update compound bounds considering its label properties    \n    if (LayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS) {\n\n      var width = childGraph.getRight() - childGraph.getLeft();\n      var height = childGraph.getBottom() - childGraph.getTop();\n\n      if (this.labelWidth) {\n        if (this.labelPosHorizontal == \"left\") {\n          this.rect.x -= this.labelWidth;\n          this.setWidth(width + this.labelWidth);\n        } else if (this.labelPosHorizontal == \"center\" && this.labelWidth > width) {\n          this.rect.x -= (this.labelWidth - width) / 2;\n          this.setWidth(this.labelWidth);\n        } else if (this.labelPosHorizontal == \"right\") {\n          this.setWidth(width + this.labelWidth);\n        }\n      }\n\n      if (this.labelHeight) {\n        if (this.labelPosVertical == \"top\") {\n          this.rect.y -= this.labelHeight;\n          this.setHeight(height + this.labelHeight);\n        } else if (this.labelPosVertical == \"center\" && this.labelHeight > height) {\n          this.rect.y -= (this.labelHeight - height) / 2;\n          this.setHeight(this.labelHeight);\n        } else if (this.labelPosVertical == \"bottom\") {\n          this.setHeight(height + this.labelHeight);\n        }\n      }\n    }\n  }\n};\n\nLNode.prototype.getInclusionTreeDepth = function () {\n  if (this.inclusionTreeDepth == Integer.MAX_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.inclusionTreeDepth;\n};\n\nLNode.prototype.transform = function (trans) {\n  var left = this.rect.x;\n\n  if (left > LayoutConstants.WORLD_BOUNDARY) {\n    left = LayoutConstants.WORLD_BOUNDARY;\n  } else if (left < -LayoutConstants.WORLD_BOUNDARY) {\n    left = -LayoutConstants.WORLD_BOUNDARY;\n  }\n\n  var top = this.rect.y;\n\n  if (top > LayoutConstants.WORLD_BOUNDARY) {\n    top = LayoutConstants.WORLD_BOUNDARY;\n  } else if (top < -LayoutConstants.WORLD_BOUNDARY) {\n    top = -LayoutConstants.WORLD_BOUNDARY;\n  }\n\n  var leftTop = new PointD(left, top);\n  var vLeftTop = trans.inverseTransformPoint(leftTop);\n\n  this.setLocation(vLeftTop.x, vLeftTop.y);\n};\n\nLNode.prototype.getLeft = function () {\n  return this.rect.x;\n};\n\nLNode.prototype.getRight = function () {\n  return this.rect.x + this.rect.width;\n};\n\nLNode.prototype.getTop = function () {\n  return this.rect.y;\n};\n\nLNode.prototype.getBottom = function () {\n  return this.rect.y + this.rect.height;\n};\n\nLNode.prototype.getParent = function () {\n  if (this.owner == null) {\n    return null;\n  }\n\n  return this.owner.getParent();\n};\n\nmodule.exports = LNode;\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LayoutConstants = __webpack_require__(0);\n\nfunction FDLayoutConstants() {}\n\n//FDLayoutConstants inherits static props in LayoutConstants\nfor (var prop in LayoutConstants) {\n  FDLayoutConstants[prop] = LayoutConstants[prop];\n}\n\nFDLayoutConstants.MAX_ITERATIONS = 2500;\n\nFDLayoutConstants.DEFAULT_EDGE_LENGTH = 50;\nFDLayoutConstants.DEFAULT_SPRING_STRENGTH = 0.45;\nFDLayoutConstants.DEFAULT_REPULSION_STRENGTH = 4500.0;\nFDLayoutConstants.DEFAULT_GRAVITY_STRENGTH = 0.4;\nFDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH = 1.0;\nFDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR = 3.8;\nFDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR = 1.5;\nFDLayoutConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION = true;\nFDLayoutConstants.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION = true;\nFDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL = 0.3;\nFDLayoutConstants.COOLING_ADAPTATION_FACTOR = 0.33;\nFDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT = 1000;\nFDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT = 5000;\nFDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL = 100.0;\nFDLayoutConstants.MAX_NODE_DISPLACEMENT = FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL * 3;\nFDLayoutConstants.MIN_REPULSION_DIST = FDLayoutConstants.DEFAULT_EDGE_LENGTH / 10.0;\nFDLayoutConstants.CONVERGENCE_CHECK_PERIOD = 100;\nFDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR = 0.1;\nFDLayoutConstants.MIN_EDGE_LENGTH = 1;\nFDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD = 10;\n\nmodule.exports = FDLayoutConstants;\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction PointD(x, y) {\n  if (x == null && y == null) {\n    this.x = 0;\n    this.y = 0;\n  } else {\n    this.x = x;\n    this.y = y;\n  }\n}\n\nPointD.prototype.getX = function () {\n  return this.x;\n};\n\nPointD.prototype.getY = function () {\n  return this.y;\n};\n\nPointD.prototype.setX = function (x) {\n  this.x = x;\n};\n\nPointD.prototype.setY = function (y) {\n  this.y = y;\n};\n\nPointD.prototype.getDifference = function (pt) {\n  return new DimensionD(this.x - pt.x, this.y - pt.y);\n};\n\nPointD.prototype.getCopy = function () {\n  return new PointD(this.x, this.y);\n};\n\nPointD.prototype.translate = function (dim) {\n  this.x += dim.width;\n  this.y += dim.height;\n  return this;\n};\n\nmodule.exports = PointD;\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __webpack_require__(2);\nvar Integer = __webpack_require__(10);\nvar LayoutConstants = __webpack_require__(0);\nvar LGraphManager = __webpack_require__(7);\nvar LNode = __webpack_require__(3);\nvar LEdge = __webpack_require__(1);\nvar RectangleD = __webpack_require__(13);\nvar Point = __webpack_require__(12);\nvar LinkedList = __webpack_require__(11);\n\nfunction LGraph(parent, obj2, vGraph) {\n  LGraphObject.call(this, vGraph);\n  this.estimatedSize = Integer.MIN_VALUE;\n  this.margin = LayoutConstants.DEFAULT_GRAPH_MARGIN;\n  this.edges = [];\n  this.nodes = [];\n  this.isConnected = false;\n  this.parent = parent;\n\n  if (obj2 != null && obj2 instanceof LGraphManager) {\n    this.graphManager = obj2;\n  } else if (obj2 != null && obj2 instanceof Layout) {\n    this.graphManager = obj2.graphManager;\n  }\n}\n\nLGraph.prototype = Object.create(LGraphObject.prototype);\nfor (var prop in LGraphObject) {\n  LGraph[prop] = LGraphObject[prop];\n}\n\nLGraph.prototype.getNodes = function () {\n  return this.nodes;\n};\n\nLGraph.prototype.getEdges = function () {\n  return this.edges;\n};\n\nLGraph.prototype.getGraphManager = function () {\n  return this.graphManager;\n};\n\nLGraph.prototype.getParent = function () {\n  return this.parent;\n};\n\nLGraph.prototype.getLeft = function () {\n  return this.left;\n};\n\nLGraph.prototype.getRight = function () {\n  return this.right;\n};\n\nLGraph.prototype.getTop = function () {\n  return this.top;\n};\n\nLGraph.prototype.getBottom = function () {\n  return this.bottom;\n};\n\nLGraph.prototype.isConnected = function () {\n  return this.isConnected;\n};\n\nLGraph.prototype.add = function (obj1, sourceNode, targetNode) {\n  if (sourceNode == null && targetNode == null) {\n    var newNode = obj1;\n    if (this.graphManager == null) {\n      throw \"Graph has no graph mgr!\";\n    }\n    if (this.getNodes().indexOf(newNode) > -1) {\n      throw \"Node already in graph!\";\n    }\n    newNode.owner = this;\n    this.getNodes().push(newNode);\n\n    return newNode;\n  } else {\n    var newEdge = obj1;\n    if (!(this.getNodes().indexOf(sourceNode) > -1 && this.getNodes().indexOf(targetNode) > -1)) {\n      throw \"Source or target not in graph!\";\n    }\n\n    if (!(sourceNode.owner == targetNode.owner && sourceNode.owner == this)) {\n      throw \"Both owners must be this graph!\";\n    }\n\n    if (sourceNode.owner != targetNode.owner) {\n      return null;\n    }\n\n    // set source and target\n    newEdge.source = sourceNode;\n    newEdge.target = targetNode;\n\n    // set as intra-graph edge\n    newEdge.isInterGraph = false;\n\n    // add to graph edge list\n    this.getEdges().push(newEdge);\n\n    // add to incidency lists\n    sourceNode.edges.push(newEdge);\n\n    if (targetNode != sourceNode) {\n      targetNode.edges.push(newEdge);\n    }\n\n    return newEdge;\n  }\n};\n\nLGraph.prototype.remove = function (obj) {\n  var node = obj;\n  if (obj instanceof LNode) {\n    if (node == null) {\n      throw \"Node is null!\";\n    }\n    if (!(node.owner != null && node.owner == this)) {\n      throw \"Owner graph is invalid!\";\n    }\n    if (this.graphManager == null) {\n      throw \"Owner graph manager is invalid!\";\n    }\n    // remove incident edges first (make a copy to do it safely)\n    var edgesToBeRemoved = node.edges.slice();\n    var edge;\n    var s = edgesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      edge = edgesToBeRemoved[i];\n\n      if (edge.isInterGraph) {\n        this.graphManager.remove(edge);\n      } else {\n        edge.source.owner.remove(edge);\n      }\n    }\n\n    // now the node itself\n    var index = this.nodes.indexOf(node);\n    if (index == -1) {\n      throw \"Node not in owner node list!\";\n    }\n\n    this.nodes.splice(index, 1);\n  } else if (obj instanceof LEdge) {\n    var edge = obj;\n    if (edge == null) {\n      throw \"Edge is null!\";\n    }\n    if (!(edge.source != null && edge.target != null)) {\n      throw \"Source and/or target is null!\";\n    }\n    if (!(edge.source.owner != null && edge.target.owner != null && edge.source.owner == this && edge.target.owner == this)) {\n      throw \"Source and/or target owner is invalid!\";\n    }\n\n    var sourceIndex = edge.source.edges.indexOf(edge);\n    var targetIndex = edge.target.edges.indexOf(edge);\n    if (!(sourceIndex > -1 && targetIndex > -1)) {\n      throw \"Source and/or target doesn't know this edge!\";\n    }\n\n    edge.source.edges.splice(sourceIndex, 1);\n\n    if (edge.target != edge.source) {\n      edge.target.edges.splice(targetIndex, 1);\n    }\n\n    var index = edge.source.owner.getEdges().indexOf(edge);\n    if (index == -1) {\n      throw \"Not in owner's edge list!\";\n    }\n\n    edge.source.owner.getEdges().splice(index, 1);\n  }\n};\n\nLGraph.prototype.updateLeftTop = function () {\n  var top = Integer.MAX_VALUE;\n  var left = Integer.MAX_VALUE;\n  var nodeTop;\n  var nodeLeft;\n  var margin;\n\n  var nodes = this.getNodes();\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    nodeTop = lNode.getTop();\n    nodeLeft = lNode.getLeft();\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n  }\n\n  // Do we have any nodes in this graph?\n  if (top == Integer.MAX_VALUE) {\n    return null;\n  }\n\n  if (nodes[0].getParent().paddingLeft != undefined) {\n    margin = nodes[0].getParent().paddingLeft;\n  } else {\n    margin = this.margin;\n  }\n\n  this.left = left - margin;\n  this.top = top - margin;\n\n  // Apply the margins and return the result\n  return new Point(this.left, this.top);\n};\n\nLGraph.prototype.updateBounds = function (recursive) {\n  // calculate bounds\n  var left = Integer.MAX_VALUE;\n  var right = -Integer.MAX_VALUE;\n  var top = Integer.MAX_VALUE;\n  var bottom = -Integer.MAX_VALUE;\n  var nodeLeft;\n  var nodeRight;\n  var nodeTop;\n  var nodeBottom;\n  var margin;\n\n  var nodes = this.nodes;\n  var s = nodes.length;\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n\n    if (recursive && lNode.child != null) {\n      lNode.updateBounds();\n    }\n    nodeLeft = lNode.getLeft();\n    nodeRight = lNode.getRight();\n    nodeTop = lNode.getTop();\n    nodeBottom = lNode.getBottom();\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n\n    if (right < nodeRight) {\n      right = nodeRight;\n    }\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (bottom < nodeBottom) {\n      bottom = nodeBottom;\n    }\n  }\n\n  var boundingRect = new RectangleD(left, top, right - left, bottom - top);\n  if (left == Integer.MAX_VALUE) {\n    this.left = this.parent.getLeft();\n    this.right = this.parent.getRight();\n    this.top = this.parent.getTop();\n    this.bottom = this.parent.getBottom();\n  }\n\n  if (nodes[0].getParent().paddingLeft != undefined) {\n    margin = nodes[0].getParent().paddingLeft;\n  } else {\n    margin = this.margin;\n  }\n\n  this.left = boundingRect.x - margin;\n  this.right = boundingRect.x + boundingRect.width + margin;\n  this.top = boundingRect.y - margin;\n  this.bottom = boundingRect.y + boundingRect.height + margin;\n};\n\nLGraph.calculateBounds = function (nodes) {\n  var left = Integer.MAX_VALUE;\n  var right = -Integer.MAX_VALUE;\n  var top = Integer.MAX_VALUE;\n  var bottom = -Integer.MAX_VALUE;\n  var nodeLeft;\n  var nodeRight;\n  var nodeTop;\n  var nodeBottom;\n\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    nodeLeft = lNode.getLeft();\n    nodeRight = lNode.getRight();\n    nodeTop = lNode.getTop();\n    nodeBottom = lNode.getBottom();\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n\n    if (right < nodeRight) {\n      right = nodeRight;\n    }\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (bottom < nodeBottom) {\n      bottom = nodeBottom;\n    }\n  }\n\n  var boundingRect = new RectangleD(left, top, right - left, bottom - top);\n\n  return boundingRect;\n};\n\nLGraph.prototype.getInclusionTreeDepth = function () {\n  if (this == this.graphManager.getRoot()) {\n    return 1;\n  } else {\n    return this.parent.getInclusionTreeDepth();\n  }\n};\n\nLGraph.prototype.getEstimatedSize = function () {\n  if (this.estimatedSize == Integer.MIN_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.estimatedSize;\n};\n\nLGraph.prototype.calcEstimatedSize = function () {\n  var size = 0;\n  var nodes = this.nodes;\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    size += lNode.calcEstimatedSize();\n  }\n\n  if (size == 0) {\n    this.estimatedSize = LayoutConstants.EMPTY_COMPOUND_NODE_SIZE;\n  } else {\n    this.estimatedSize = size / Math.sqrt(this.nodes.length);\n  }\n\n  return this.estimatedSize;\n};\n\nLGraph.prototype.updateConnected = function () {\n  var self = this;\n  if (this.nodes.length == 0) {\n    this.isConnected = true;\n    return;\n  }\n\n  var queue = new LinkedList();\n  var visited = new Set();\n  var currentNode = this.nodes[0];\n  var neighborEdges;\n  var currentNeighbor;\n  var childrenOfNode = currentNode.withChildren();\n  childrenOfNode.forEach(function (node) {\n    queue.push(node);\n    visited.add(node);\n  });\n\n  while (queue.length !== 0) {\n    currentNode = queue.shift();\n\n    // Traverse all neighbors of this node\n    neighborEdges = currentNode.getEdges();\n    var size = neighborEdges.length;\n    for (var i = 0; i < size; i++) {\n      var neighborEdge = neighborEdges[i];\n      currentNeighbor = neighborEdge.getOtherEndInGraph(currentNode, this);\n\n      // Add unvisited neighbors to the list to visit\n      if (currentNeighbor != null && !visited.has(currentNeighbor)) {\n        var childrenOfNeighbor = currentNeighbor.withChildren();\n\n        childrenOfNeighbor.forEach(function (node) {\n          queue.push(node);\n          visited.add(node);\n        });\n      }\n    }\n  }\n\n  this.isConnected = false;\n\n  if (visited.size >= this.nodes.length) {\n    var noOfVisitedInThisGraph = 0;\n\n    visited.forEach(function (visitedNode) {\n      if (visitedNode.owner == self) {\n        noOfVisitedInThisGraph++;\n      }\n    });\n\n    if (noOfVisitedInThisGraph == this.nodes.length) {\n      this.isConnected = true;\n    }\n  }\n};\n\nmodule.exports = LGraph;\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraph;\nvar LEdge = __webpack_require__(1);\n\nfunction LGraphManager(layout) {\n  LGraph = __webpack_require__(6); // It may be better to initilize this out of this function but it gives an error (Right-hand side of 'instanceof' is not callable) now.\n  this.layout = layout;\n\n  this.graphs = [];\n  this.edges = [];\n}\n\nLGraphManager.prototype.addRoot = function () {\n  var ngraph = this.layout.newGraph();\n  var nnode = this.layout.newNode(null);\n  var root = this.add(ngraph, nnode);\n  this.setRootGraph(root);\n  return this.rootGraph;\n};\n\nLGraphManager.prototype.add = function (newGraph, parentNode, newEdge, sourceNode, targetNode) {\n  //there are just 2 parameters are passed then it adds an LGraph else it adds an LEdge\n  if (newEdge == null && sourceNode == null && targetNode == null) {\n    if (newGraph == null) {\n      throw \"Graph is null!\";\n    }\n    if (parentNode == null) {\n      throw \"Parent node is null!\";\n    }\n    if (this.graphs.indexOf(newGraph) > -1) {\n      throw \"Graph already in this graph mgr!\";\n    }\n\n    this.graphs.push(newGraph);\n\n    if (newGraph.parent != null) {\n      throw \"Already has a parent!\";\n    }\n    if (parentNode.child != null) {\n      throw \"Already has a child!\";\n    }\n\n    newGraph.parent = parentNode;\n    parentNode.child = newGraph;\n\n    return newGraph;\n  } else {\n    //change the order of the parameters\n    targetNode = newEdge;\n    sourceNode = parentNode;\n    newEdge = newGraph;\n    var sourceGraph = sourceNode.getOwner();\n    var targetGraph = targetNode.getOwner();\n\n    if (!(sourceGraph != null && sourceGraph.getGraphManager() == this)) {\n      throw \"Source not in this graph mgr!\";\n    }\n    if (!(targetGraph != null && targetGraph.getGraphManager() == this)) {\n      throw \"Target not in this graph mgr!\";\n    }\n\n    if (sourceGraph == targetGraph) {\n      newEdge.isInterGraph = false;\n      return sourceGraph.add(newEdge, sourceNode, targetNode);\n    } else {\n      newEdge.isInterGraph = true;\n\n      // set source and target\n      newEdge.source = sourceNode;\n      newEdge.target = targetNode;\n\n      // add edge to inter-graph edge list\n      if (this.edges.indexOf(newEdge) > -1) {\n        throw \"Edge already in inter-graph edge list!\";\n      }\n\n      this.edges.push(newEdge);\n\n      // add edge to source and target incidency lists\n      if (!(newEdge.source != null && newEdge.target != null)) {\n        throw \"Edge source and/or target is null!\";\n      }\n\n      if (!(newEdge.source.edges.indexOf(newEdge) == -1 && newEdge.target.edges.indexOf(newEdge) == -1)) {\n        throw \"Edge already in source and/or target incidency list!\";\n      }\n\n      newEdge.source.edges.push(newEdge);\n      newEdge.target.edges.push(newEdge);\n\n      return newEdge;\n    }\n  }\n};\n\nLGraphManager.prototype.remove = function (lObj) {\n  if (lObj instanceof LGraph) {\n    var graph = lObj;\n    if (graph.getGraphManager() != this) {\n      throw \"Graph not in this graph mgr\";\n    }\n    if (!(graph == this.rootGraph || graph.parent != null && graph.parent.graphManager == this)) {\n      throw \"Invalid parent node!\";\n    }\n\n    // first the edges (make a copy to do it safely)\n    var edgesToBeRemoved = [];\n\n    edgesToBeRemoved = edgesToBeRemoved.concat(graph.getEdges());\n\n    var edge;\n    var s = edgesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      edge = edgesToBeRemoved[i];\n      graph.remove(edge);\n    }\n\n    // then the nodes (make a copy to do it safely)\n    var nodesToBeRemoved = [];\n\n    nodesToBeRemoved = nodesToBeRemoved.concat(graph.getNodes());\n\n    var node;\n    s = nodesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      node = nodesToBeRemoved[i];\n      graph.remove(node);\n    }\n\n    // check if graph is the root\n    if (graph == this.rootGraph) {\n      this.setRootGraph(null);\n    }\n\n    // now remove the graph itself\n    var index = this.graphs.indexOf(graph);\n    this.graphs.splice(index, 1);\n\n    // also reset the parent of the graph\n    graph.parent = null;\n  } else if (lObj instanceof LEdge) {\n    edge = lObj;\n    if (edge == null) {\n      throw \"Edge is null!\";\n    }\n    if (!edge.isInterGraph) {\n      throw \"Not an inter-graph edge!\";\n    }\n    if (!(edge.source != null && edge.target != null)) {\n      throw \"Source and/or target is null!\";\n    }\n\n    // remove edge from source and target nodes' incidency lists\n\n    if (!(edge.source.edges.indexOf(edge) != -1 && edge.target.edges.indexOf(edge) != -1)) {\n      throw \"Source and/or target doesn't know this edge!\";\n    }\n\n    var index = edge.source.edges.indexOf(edge);\n    edge.source.edges.splice(index, 1);\n    index = edge.target.edges.indexOf(edge);\n    edge.target.edges.splice(index, 1);\n\n    // remove edge from owner graph manager's inter-graph edge list\n\n    if (!(edge.source.owner != null && edge.source.owner.getGraphManager() != null)) {\n      throw \"Edge owner graph or owner graph manager is null!\";\n    }\n    if (edge.source.owner.getGraphManager().edges.indexOf(edge) == -1) {\n      throw \"Not in owner graph manager's edge list!\";\n    }\n\n    var index = edge.source.owner.getGraphManager().edges.indexOf(edge);\n    edge.source.owner.getGraphManager().edges.splice(index, 1);\n  }\n};\n\nLGraphManager.prototype.updateBounds = function () {\n  this.rootGraph.updateBounds(true);\n};\n\nLGraphManager.prototype.getGraphs = function () {\n  return this.graphs;\n};\n\nLGraphManager.prototype.getAllNodes = function () {\n  if (this.allNodes == null) {\n    var nodeList = [];\n    var graphs = this.getGraphs();\n    var s = graphs.length;\n    for (var i = 0; i < s; i++) {\n      nodeList = nodeList.concat(graphs[i].getNodes());\n    }\n    this.allNodes = nodeList;\n  }\n  return this.allNodes;\n};\n\nLGraphManager.prototype.resetAllNodes = function () {\n  this.allNodes = null;\n};\n\nLGraphManager.prototype.resetAllEdges = function () {\n  this.allEdges = null;\n};\n\nLGraphManager.prototype.resetAllNodesToApplyGravitation = function () {\n  this.allNodesToApplyGravitation = null;\n};\n\nLGraphManager.prototype.getAllEdges = function () {\n  if (this.allEdges == null) {\n    var edgeList = [];\n    var graphs = this.getGraphs();\n    var s = graphs.length;\n    for (var i = 0; i < graphs.length; i++) {\n      edgeList = edgeList.concat(graphs[i].getEdges());\n    }\n\n    edgeList = edgeList.concat(this.edges);\n\n    this.allEdges = edgeList;\n  }\n  return this.allEdges;\n};\n\nLGraphManager.prototype.getAllNodesToApplyGravitation = function () {\n  return this.allNodesToApplyGravitation;\n};\n\nLGraphManager.prototype.setAllNodesToApplyGravitation = function (nodeList) {\n  if (this.allNodesToApplyGravitation != null) {\n    throw \"assert failed\";\n  }\n\n  this.allNodesToApplyGravitation = nodeList;\n};\n\nLGraphManager.prototype.getRoot = function () {\n  return this.rootGraph;\n};\n\nLGraphManager.prototype.setRootGraph = function (graph) {\n  if (graph.getGraphManager() != this) {\n    throw \"Root not in this graph mgr!\";\n  }\n\n  this.rootGraph = graph;\n  // root graph must have a root node associated with it for convenience\n  if (graph.parent == null) {\n    graph.parent = this.layout.newNode(\"Root node\");\n  }\n};\n\nLGraphManager.prototype.getLayout = function () {\n  return this.layout;\n};\n\nLGraphManager.prototype.isOneAncestorOfOther = function (firstNode, secondNode) {\n  if (!(firstNode != null && secondNode != null)) {\n    throw \"assert failed\";\n  }\n\n  if (firstNode == secondNode) {\n    return true;\n  }\n  // Is second node an ancestor of the first one?\n  var ownerGraph = firstNode.getOwner();\n  var parentNode;\n\n  do {\n    parentNode = ownerGraph.getParent();\n\n    if (parentNode == null) {\n      break;\n    }\n\n    if (parentNode == secondNode) {\n      return true;\n    }\n\n    ownerGraph = parentNode.getOwner();\n    if (ownerGraph == null) {\n      break;\n    }\n  } while (true);\n  // Is first node an ancestor of the second one?\n  ownerGraph = secondNode.getOwner();\n\n  do {\n    parentNode = ownerGraph.getParent();\n\n    if (parentNode == null) {\n      break;\n    }\n\n    if (parentNode == firstNode) {\n      return true;\n    }\n\n    ownerGraph = parentNode.getOwner();\n    if (ownerGraph == null) {\n      break;\n    }\n  } while (true);\n\n  return false;\n};\n\nLGraphManager.prototype.calcLowestCommonAncestors = function () {\n  var edge;\n  var sourceNode;\n  var targetNode;\n  var sourceAncestorGraph;\n  var targetAncestorGraph;\n\n  var edges = this.getAllEdges();\n  var s = edges.length;\n  for (var i = 0; i < s; i++) {\n    edge = edges[i];\n\n    sourceNode = edge.source;\n    targetNode = edge.target;\n    edge.lca = null;\n    edge.sourceInLca = sourceNode;\n    edge.targetInLca = targetNode;\n\n    if (sourceNode == targetNode) {\n      edge.lca = sourceNode.getOwner();\n      continue;\n    }\n\n    sourceAncestorGraph = sourceNode.getOwner();\n\n    while (edge.lca == null) {\n      edge.targetInLca = targetNode;\n      targetAncestorGraph = targetNode.getOwner();\n\n      while (edge.lca == null) {\n        if (targetAncestorGraph == sourceAncestorGraph) {\n          edge.lca = targetAncestorGraph;\n          break;\n        }\n\n        if (targetAncestorGraph == this.rootGraph) {\n          break;\n        }\n\n        if (edge.lca != null) {\n          throw \"assert failed\";\n        }\n        edge.targetInLca = targetAncestorGraph.getParent();\n        targetAncestorGraph = edge.targetInLca.getOwner();\n      }\n\n      if (sourceAncestorGraph == this.rootGraph) {\n        break;\n      }\n\n      if (edge.lca == null) {\n        edge.sourceInLca = sourceAncestorGraph.getParent();\n        sourceAncestorGraph = edge.sourceInLca.getOwner();\n      }\n    }\n\n    if (edge.lca == null) {\n      throw \"assert failed\";\n    }\n  }\n};\n\nLGraphManager.prototype.calcLowestCommonAncestor = function (firstNode, secondNode) {\n  if (firstNode == secondNode) {\n    return firstNode.getOwner();\n  }\n  var firstOwnerGraph = firstNode.getOwner();\n\n  do {\n    if (firstOwnerGraph == null) {\n      break;\n    }\n    var secondOwnerGraph = secondNode.getOwner();\n\n    do {\n      if (secondOwnerGraph == null) {\n        break;\n      }\n\n      if (secondOwnerGraph == firstOwnerGraph) {\n        return secondOwnerGraph;\n      }\n      secondOwnerGraph = secondOwnerGraph.getParent().getOwner();\n    } while (true);\n\n    firstOwnerGraph = firstOwnerGraph.getParent().getOwner();\n  } while (true);\n\n  return firstOwnerGraph;\n};\n\nLGraphManager.prototype.calcInclusionTreeDepths = function (graph, depth) {\n  if (graph == null && depth == null) {\n    graph = this.rootGraph;\n    depth = 1;\n  }\n  var node;\n\n  var nodes = graph.getNodes();\n  var s = nodes.length;\n  for (var i = 0; i < s; i++) {\n    node = nodes[i];\n    node.inclusionTreeDepth = depth;\n\n    if (node.child != null) {\n      this.calcInclusionTreeDepths(node.child, depth + 1);\n    }\n  }\n};\n\nLGraphManager.prototype.includesInvalidEdge = function () {\n  var edge;\n  var edgesToRemove = [];\n\n  var s = this.edges.length;\n  for (var i = 0; i < s; i++) {\n    edge = this.edges[i];\n\n    if (this.isOneAncestorOfOther(edge.source, edge.target)) {\n      edgesToRemove.push(edge);\n    }\n  }\n\n  // Remove invalid edges from graph manager\n  for (var i = 0; i < edgesToRemove.length; i++) {\n    this.remove(edgesToRemove[i]);\n  }\n\n  // Invalid edges are cleared, so return false\n  return false;\n};\n\nmodule.exports = LGraphManager;\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n/**\n * This class maintains a list of static geometry related utility methods.\n *\n *\n * Copyright: i-Vis Research Group, Bilkent University, 2007 - present\n */\n\nvar Point = __webpack_require__(12);\n\nfunction IGeometry() {}\n\n/**\n * This method calculates *half* the amount in x and y directions of the two\n * input rectangles needed to separate them keeping their respective\n * positioning, and returns the result in the input array. An input\n * separation buffer added to the amount in both directions. We assume that\n * the two rectangles do intersect.\n */\nIGeometry.calcSeparationAmount = function (rectA, rectB, overlapAmount, separationBuffer) {\n  if (!rectA.intersects(rectB)) {\n    throw \"assert failed\";\n  }\n\n  var directions = new Array(2);\n\n  this.decideDirectionsForOverlappingNodes(rectA, rectB, directions);\n\n  overlapAmount[0] = Math.min(rectA.getRight(), rectB.getRight()) - Math.max(rectA.x, rectB.x);\n  overlapAmount[1] = Math.min(rectA.getBottom(), rectB.getBottom()) - Math.max(rectA.y, rectB.y);\n\n  // update the overlapping amounts for the following cases:\n  if (rectA.getX() <= rectB.getX() && rectA.getRight() >= rectB.getRight()) {\n    /* Case x.1:\n    *\n    * rectA\n    * \t|                       |\n    * \t|        _________      |\n    * \t|        |       |      |\n    * \t|________|_______|______|\n    * \t\t\t |       |\n    *           |       |\n    *        rectB\n    */\n    overlapAmount[0] += Math.min(rectB.getX() - rectA.getX(), rectA.getRight() - rectB.getRight());\n  } else if (rectB.getX() <= rectA.getX() && rectB.getRight() >= rectA.getRight()) {\n    /* Case x.2:\n    *\n    * rectB\n    * \t|                       |\n    * \t|        _________      |\n    * \t|        |       |      |\n    * \t|________|_______|______|\n    * \t\t\t |       |\n    *           |       |\n    *        rectA\n    */\n    overlapAmount[0] += Math.min(rectA.getX() - rectB.getX(), rectB.getRight() - rectA.getRight());\n  }\n  if (rectA.getY() <= rectB.getY() && rectA.getBottom() >= rectB.getBottom()) {\n    /* Case y.1:\n     *          ________ rectA\n     *         |\n     *         |\n     *   ______|____  rectB\n     *         |    |\n     *         |    |\n     *   ______|____|\n     *         |\n     *         |\n     *         |________\n     *\n     */\n    overlapAmount[1] += Math.min(rectB.getY() - rectA.getY(), rectA.getBottom() - rectB.getBottom());\n  } else if (rectB.getY() <= rectA.getY() && rectB.getBottom() >= rectA.getBottom()) {\n    /* Case y.2:\n    *          ________ rectB\n    *         |\n    *         |\n    *   ______|____  rectA\n    *         |    |\n    *         |    |\n    *   ______|____|\n    *         |\n    *         |\n    *         |________\n    *\n    */\n    overlapAmount[1] += Math.min(rectA.getY() - rectB.getY(), rectB.getBottom() - rectA.getBottom());\n  }\n\n  // find slope of the line passes two centers\n  var slope = Math.abs((rectB.getCenterY() - rectA.getCenterY()) / (rectB.getCenterX() - rectA.getCenterX()));\n  // if centers are overlapped\n  if (rectB.getCenterY() === rectA.getCenterY() && rectB.getCenterX() === rectA.getCenterX()) {\n    // assume the slope is 1 (45 degree)\n    slope = 1.0;\n  }\n\n  var moveByY = slope * overlapAmount[0];\n  var moveByX = overlapAmount[1] / slope;\n  if (overlapAmount[0] < moveByX) {\n    moveByX = overlapAmount[0];\n  } else {\n    moveByY = overlapAmount[1];\n  }\n  // return half the amount so that if each rectangle is moved by these\n  // amounts in opposite directions, overlap will be resolved\n  overlapAmount[0] = -1 * directions[0] * (moveByX / 2 + separationBuffer);\n  overlapAmount[1] = -1 * directions[1] * (moveByY / 2 + separationBuffer);\n};\n\n/**\n * This method decides the separation direction of overlapping nodes\n *\n * if directions[0] = -1, then rectA goes left\n * if directions[0] = 1,  then rectA goes right\n * if directions[1] = -1, then rectA goes up\n * if directions[1] = 1,  then rectA goes down\n */\nIGeometry.decideDirectionsForOverlappingNodes = function (rectA, rectB, directions) {\n  if (rectA.getCenterX() < rectB.getCenterX()) {\n    directions[0] = -1;\n  } else {\n    directions[0] = 1;\n  }\n\n  if (rectA.getCenterY() < rectB.getCenterY()) {\n    directions[1] = -1;\n  } else {\n    directions[1] = 1;\n  }\n};\n\n/**\n * This method calculates the intersection (clipping) points of the two\n * input rectangles with line segment defined by the centers of these two\n * rectangles. The clipping points are saved in the input double array and\n * whether or not the two rectangles overlap is returned.\n */\nIGeometry.getIntersection2 = function (rectA, rectB, result) {\n  //result[0-1] will contain clipPoint of rectA, result[2-3] will contain clipPoint of rectB\n  var p1x = rectA.getCenterX();\n  var p1y = rectA.getCenterY();\n  var p2x = rectB.getCenterX();\n  var p2y = rectB.getCenterY();\n\n  //if two rectangles intersect, then clipping points are centers\n  if (rectA.intersects(rectB)) {\n    result[0] = p1x;\n    result[1] = p1y;\n    result[2] = p2x;\n    result[3] = p2y;\n    return true;\n  }\n  //variables for rectA\n  var topLeftAx = rectA.getX();\n  var topLeftAy = rectA.getY();\n  var topRightAx = rectA.getRight();\n  var bottomLeftAx = rectA.getX();\n  var bottomLeftAy = rectA.getBottom();\n  var bottomRightAx = rectA.getRight();\n  var halfWidthA = rectA.getWidthHalf();\n  var halfHeightA = rectA.getHeightHalf();\n  //variables for rectB\n  var topLeftBx = rectB.getX();\n  var topLeftBy = rectB.getY();\n  var topRightBx = rectB.getRight();\n  var bottomLeftBx = rectB.getX();\n  var bottomLeftBy = rectB.getBottom();\n  var bottomRightBx = rectB.getRight();\n  var halfWidthB = rectB.getWidthHalf();\n  var halfHeightB = rectB.getHeightHalf();\n\n  //flag whether clipping points are found\n  var clipPointAFound = false;\n  var clipPointBFound = false;\n\n  // line is vertical\n  if (p1x === p2x) {\n    if (p1y > p2y) {\n      result[0] = p1x;\n      result[1] = topLeftAy;\n      result[2] = p2x;\n      result[3] = bottomLeftBy;\n      return false;\n    } else if (p1y < p2y) {\n      result[0] = p1x;\n      result[1] = bottomLeftAy;\n      result[2] = p2x;\n      result[3] = topLeftBy;\n      return false;\n    } else {\n      //not line, return null;\n    }\n  }\n  // line is horizontal\n  else if (p1y === p2y) {\n      if (p1x > p2x) {\n        result[0] = topLeftAx;\n        result[1] = p1y;\n        result[2] = topRightBx;\n        result[3] = p2y;\n        return false;\n      } else if (p1x < p2x) {\n        result[0] = topRightAx;\n        result[1] = p1y;\n        result[2] = topLeftBx;\n        result[3] = p2y;\n        return false;\n      } else {\n        //not valid line, return null;\n      }\n    } else {\n      //slopes of rectA's and rectB's diagonals\n      var slopeA = rectA.height / rectA.width;\n      var slopeB = rectB.height / rectB.width;\n\n      //slope of line between center of rectA and center of rectB\n      var slopePrime = (p2y - p1y) / (p2x - p1x);\n      var cardinalDirectionA = void 0;\n      var cardinalDirectionB = void 0;\n      var tempPointAx = void 0;\n      var tempPointAy = void 0;\n      var tempPointBx = void 0;\n      var tempPointBy = void 0;\n\n      //determine whether clipping point is the corner of nodeA\n      if (-slopeA === slopePrime) {\n        if (p1x > p2x) {\n          result[0] = bottomLeftAx;\n          result[1] = bottomLeftAy;\n          clipPointAFound = true;\n        } else {\n          result[0] = topRightAx;\n          result[1] = topLeftAy;\n          clipPointAFound = true;\n        }\n      } else if (slopeA === slopePrime) {\n        if (p1x > p2x) {\n          result[0] = topLeftAx;\n          result[1] = topLeftAy;\n          clipPointAFound = true;\n        } else {\n          result[0] = bottomRightAx;\n          result[1] = bottomLeftAy;\n          clipPointAFound = true;\n        }\n      }\n\n      //determine whether clipping point is the corner of nodeB\n      if (-slopeB === slopePrime) {\n        if (p2x > p1x) {\n          result[2] = bottomLeftBx;\n          result[3] = bottomLeftBy;\n          clipPointBFound = true;\n        } else {\n          result[2] = topRightBx;\n          result[3] = topLeftBy;\n          clipPointBFound = true;\n        }\n      } else if (slopeB === slopePrime) {\n        if (p2x > p1x) {\n          result[2] = topLeftBx;\n          result[3] = topLeftBy;\n          clipPointBFound = true;\n        } else {\n          result[2] = bottomRightBx;\n          result[3] = bottomLeftBy;\n          clipPointBFound = true;\n        }\n      }\n\n      //if both clipping points are corners\n      if (clipPointAFound && clipPointBFound) {\n        return false;\n      }\n\n      //determine Cardinal Direction of rectangles\n      if (p1x > p2x) {\n        if (p1y > p2y) {\n          cardinalDirectionA = this.getCardinalDirection(slopeA, slopePrime, 4);\n          cardinalDirectionB = this.getCardinalDirection(slopeB, slopePrime, 2);\n        } else {\n          cardinalDirectionA = this.getCardinalDirection(-slopeA, slopePrime, 3);\n          cardinalDirectionB = this.getCardinalDirection(-slopeB, slopePrime, 1);\n        }\n      } else {\n        if (p1y > p2y) {\n          cardinalDirectionA = this.getCardinalDirection(-slopeA, slopePrime, 1);\n          cardinalDirectionB = this.getCardinalDirection(-slopeB, slopePrime, 3);\n        } else {\n          cardinalDirectionA = this.getCardinalDirection(slopeA, slopePrime, 2);\n          cardinalDirectionB = this.getCardinalDirection(slopeB, slopePrime, 4);\n        }\n      }\n      //calculate clipping Point if it is not found before\n      if (!clipPointAFound) {\n        switch (cardinalDirectionA) {\n          case 1:\n            tempPointAy = topLeftAy;\n            tempPointAx = p1x + -halfHeightA / slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 2:\n            tempPointAx = bottomRightAx;\n            tempPointAy = p1y + halfWidthA * slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 3:\n            tempPointAy = bottomLeftAy;\n            tempPointAx = p1x + halfHeightA / slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 4:\n            tempPointAx = bottomLeftAx;\n            tempPointAy = p1y + -halfWidthA * slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n        }\n      }\n      if (!clipPointBFound) {\n        switch (cardinalDirectionB) {\n          case 1:\n            tempPointBy = topLeftBy;\n            tempPointBx = p2x + -halfHeightB / slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 2:\n            tempPointBx = bottomRightBx;\n            tempPointBy = p2y + halfWidthB * slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 3:\n            tempPointBy = bottomLeftBy;\n            tempPointBx = p2x + halfHeightB / slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 4:\n            tempPointBx = bottomLeftBx;\n            tempPointBy = p2y + -halfWidthB * slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n        }\n      }\n    }\n  return false;\n};\n\n/**\n * This method returns in which cardinal direction does input point stays\n * 1: North\n * 2: East\n * 3: South\n * 4: West\n */\nIGeometry.getCardinalDirection = function (slope, slopePrime, line) {\n  if (slope > slopePrime) {\n    return line;\n  } else {\n    return 1 + line % 4;\n  }\n};\n\n/**\n * This method calculates the intersection of the two lines defined by\n * point pairs (s1,s2) and (f1,f2).\n */\nIGeometry.getIntersection = function (s1, s2, f1, f2) {\n  if (f2 == null) {\n    return this.getIntersection2(s1, s2, f1);\n  }\n\n  var x1 = s1.x;\n  var y1 = s1.y;\n  var x2 = s2.x;\n  var y2 = s2.y;\n  var x3 = f1.x;\n  var y3 = f1.y;\n  var x4 = f2.x;\n  var y4 = f2.y;\n  var x = void 0,\n      y = void 0; // intersection point\n  var a1 = void 0,\n      a2 = void 0,\n      b1 = void 0,\n      b2 = void 0,\n      c1 = void 0,\n      c2 = void 0; // coefficients of line eqns.\n  var denom = void 0;\n\n  a1 = y2 - y1;\n  b1 = x1 - x2;\n  c1 = x2 * y1 - x1 * y2; // { a1*x + b1*y + c1 = 0 is line 1 }\n\n  a2 = y4 - y3;\n  b2 = x3 - x4;\n  c2 = x4 * y3 - x3 * y4; // { a2*x + b2*y + c2 = 0 is line 2 }\n\n  denom = a1 * b2 - a2 * b1;\n\n  if (denom === 0) {\n    return null;\n  }\n\n  x = (b1 * c2 - b2 * c1) / denom;\n  y = (a2 * c1 - a1 * c2) / denom;\n\n  return new Point(x, y);\n};\n\n/**\n * This method finds and returns the angle of the vector from the + x-axis\n * in clockwise direction (compatible w/ Java coordinate system!).\n */\nIGeometry.angleOfVector = function (Cx, Cy, Nx, Ny) {\n  var C_angle = void 0;\n\n  if (Cx !== Nx) {\n    C_angle = Math.atan((Ny - Cy) / (Nx - Cx));\n\n    if (Nx < Cx) {\n      C_angle += Math.PI;\n    } else if (Ny < Cy) {\n      C_angle += this.TWO_PI;\n    }\n  } else if (Ny < Cy) {\n    C_angle = this.ONE_AND_HALF_PI; // 270 degrees\n  } else {\n    C_angle = this.HALF_PI; // 90 degrees\n  }\n\n  return C_angle;\n};\n\n/**\n * This method checks whether the given two line segments (one with point\n * p1 and p2, the other with point p3 and p4) intersect at a point other\n * than these points.\n */\nIGeometry.doIntersect = function (p1, p2, p3, p4) {\n  var a = p1.x;\n  var b = p1.y;\n  var c = p2.x;\n  var d = p2.y;\n  var p = p3.x;\n  var q = p3.y;\n  var r = p4.x;\n  var s = p4.y;\n  var det = (c - a) * (s - q) - (r - p) * (d - b);\n\n  if (det === 0) {\n    return false;\n  } else {\n    var lambda = ((s - q) * (r - a) + (p - r) * (s - b)) / det;\n    var gamma = ((b - d) * (r - a) + (c - a) * (s - b)) / det;\n    return 0 < lambda && lambda < 1 && 0 < gamma && gamma < 1;\n  }\n};\n\n/**\n * This method checks and calculates the intersection of \n * a line segment and a circle.\n */\nIGeometry.findCircleLineIntersections = function (Ex, Ey, Lx, Ly, Cx, Cy, r) {\n\n  // E is the starting point of the ray,\n  // L is the end point of the ray,\n  // C is the center of sphere you're testing against\n  // r is the radius of that sphere\n\n  // Compute:\n  // d = L - E ( Direction vector of ray, from start to end )\n  // f = E - C ( Vector from center sphere to ray start )\n\n  // Then the intersection is found by..\n  // P = E + t * d\n  // This is a parametric equation:\n  // Px = Ex + tdx\n  // Py = Ey + tdy\n\n  // get a, b, c values\n  var a = (Lx - Ex) * (Lx - Ex) + (Ly - Ey) * (Ly - Ey);\n  var b = 2 * ((Ex - Cx) * (Lx - Ex) + (Ey - Cy) * (Ly - Ey));\n  var c = (Ex - Cx) * (Ex - Cx) + (Ey - Cy) * (Ey - Cy) - r * r;\n\n  // get discriminant\n  var disc = b * b - 4 * a * c;\n  if (disc >= 0) {\n    // insert into quadratic formula\n    var t1 = (-b + Math.sqrt(b * b - 4 * a * c)) / (2 * a);\n    var t2 = (-b - Math.sqrt(b * b - 4 * a * c)) / (2 * a);\n    var intersections = null;\n    if (t1 >= 0 && t1 <= 1) {\n      // t1 is the intersection, and it's closer than t2\n      // (since t1 uses -b - discriminant)\n      // Impale, Poke\n      return [t1];\n    }\n\n    // here t1 didn't intersect so we are either started\n    // inside the sphere or completely past it\n    if (t2 >= 0 && t2 <= 1) {\n      // ExitWound\n      return [t2];\n    }\n\n    return intersections;\n  } else return null;\n};\n\n// -----------------------------------------------------------------------------\n// Section: Class Constants\n// -----------------------------------------------------------------------------\n/**\n * Some useful pre-calculated constants\n */\nIGeometry.HALF_PI = 0.5 * Math.PI;\nIGeometry.ONE_AND_HALF_PI = 1.5 * Math.PI;\nIGeometry.TWO_PI = 2.0 * Math.PI;\nIGeometry.THREE_PI = 3.0 * Math.PI;\n\nmodule.exports = IGeometry;\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction IMath() {}\n\n/**\n * This method returns the sign of the input value.\n */\nIMath.sign = function (value) {\n  if (value > 0) {\n    return 1;\n  } else if (value < 0) {\n    return -1;\n  } else {\n    return 0;\n  }\n};\n\nIMath.floor = function (value) {\n  return value < 0 ? Math.ceil(value) : Math.floor(value);\n};\n\nIMath.ceil = function (value) {\n  return value < 0 ? Math.floor(value) : Math.ceil(value);\n};\n\nmodule.exports = IMath;\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction Integer() {}\n\nInteger.MAX_VALUE = 2147483647;\nInteger.MIN_VALUE = -2147483648;\n\nmodule.exports = Integer;\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar nodeFrom = function nodeFrom(value) {\n  return { value: value, next: null, prev: null };\n};\n\nvar add = function add(prev, node, next, list) {\n  if (prev !== null) {\n    prev.next = node;\n  } else {\n    list.head = node;\n  }\n\n  if (next !== null) {\n    next.prev = node;\n  } else {\n    list.tail = node;\n  }\n\n  node.prev = prev;\n  node.next = next;\n\n  list.length++;\n\n  return node;\n};\n\nvar _remove = function _remove(node, list) {\n  var prev = node.prev,\n      next = node.next;\n\n\n  if (prev !== null) {\n    prev.next = next;\n  } else {\n    list.head = next;\n  }\n\n  if (next !== null) {\n    next.prev = prev;\n  } else {\n    list.tail = prev;\n  }\n\n  node.prev = node.next = null;\n\n  list.length--;\n\n  return node;\n};\n\nvar LinkedList = function () {\n  function LinkedList(vals) {\n    var _this = this;\n\n    _classCallCheck(this, LinkedList);\n\n    this.length = 0;\n    this.head = null;\n    this.tail = null;\n\n    if (vals != null) {\n      vals.forEach(function (v) {\n        return _this.push(v);\n      });\n    }\n  }\n\n  _createClass(LinkedList, [{\n    key: \"size\",\n    value: function size() {\n      return this.length;\n    }\n  }, {\n    key: \"insertBefore\",\n    value: function insertBefore(val, otherNode) {\n      return add(otherNode.prev, nodeFrom(val), otherNode, this);\n    }\n  }, {\n    key: \"insertAfter\",\n    value: function insertAfter(val, otherNode) {\n      return add(otherNode, nodeFrom(val), otherNode.next, this);\n    }\n  }, {\n    key: \"insertNodeBefore\",\n    value: function insertNodeBefore(newNode, otherNode) {\n      return add(otherNode.prev, newNode, otherNode, this);\n    }\n  }, {\n    key: \"insertNodeAfter\",\n    value: function insertNodeAfter(newNode, otherNode) {\n      return add(otherNode, newNode, otherNode.next, this);\n    }\n  }, {\n    key: \"push\",\n    value: function push(val) {\n      return add(this.tail, nodeFrom(val), null, this);\n    }\n  }, {\n    key: \"unshift\",\n    value: function unshift(val) {\n      return add(null, nodeFrom(val), this.head, this);\n    }\n  }, {\n    key: \"remove\",\n    value: function remove(node) {\n      return _remove(node, this);\n    }\n  }, {\n    key: \"pop\",\n    value: function pop() {\n      return _remove(this.tail, this).value;\n    }\n  }, {\n    key: \"popNode\",\n    value: function popNode() {\n      return _remove(this.tail, this);\n    }\n  }, {\n    key: \"shift\",\n    value: function shift() {\n      return _remove(this.head, this).value;\n    }\n  }, {\n    key: \"shiftNode\",\n    value: function shiftNode() {\n      return _remove(this.head, this);\n    }\n  }, {\n    key: \"get_object_at\",\n    value: function get_object_at(index) {\n      if (index <= this.length()) {\n        var i = 1;\n        var current = this.head;\n        while (i < index) {\n          current = current.next;\n          i++;\n        }\n        return current.value;\n      }\n    }\n  }, {\n    key: \"set_object_at\",\n    value: function set_object_at(index, value) {\n      if (index <= this.length()) {\n        var i = 1;\n        var current = this.head;\n        while (i < index) {\n          current = current.next;\n          i++;\n        }\n        current.value = value;\n      }\n    }\n  }]);\n\n  return LinkedList;\n}();\n\nmodule.exports = LinkedList;\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n/*\r\n *This class is the javascript implementation of the Point.java class in jdk\r\n */\nfunction Point(x, y, p) {\n  this.x = null;\n  this.y = null;\n  if (x == null && y == null && p == null) {\n    this.x = 0;\n    this.y = 0;\n  } else if (typeof x == 'number' && typeof y == 'number' && p == null) {\n    this.x = x;\n    this.y = y;\n  } else if (x.constructor.name == 'Point' && y == null && p == null) {\n    p = x;\n    this.x = p.x;\n    this.y = p.y;\n  }\n}\n\nPoint.prototype.getX = function () {\n  return this.x;\n};\n\nPoint.prototype.getY = function () {\n  return this.y;\n};\n\nPoint.prototype.getLocation = function () {\n  return new Point(this.x, this.y);\n};\n\nPoint.prototype.setLocation = function (x, y, p) {\n  if (x.constructor.name == 'Point' && y == null && p == null) {\n    p = x;\n    this.setLocation(p.x, p.y);\n  } else if (typeof x == 'number' && typeof y == 'number' && p == null) {\n    //if both parameters are integer just move (x,y) location\n    if (parseInt(x) == x && parseInt(y) == y) {\n      this.move(x, y);\n    } else {\n      this.x = Math.floor(x + 0.5);\n      this.y = Math.floor(y + 0.5);\n    }\n  }\n};\n\nPoint.prototype.move = function (x, y) {\n  this.x = x;\n  this.y = y;\n};\n\nPoint.prototype.translate = function (dx, dy) {\n  this.x += dx;\n  this.y += dy;\n};\n\nPoint.prototype.equals = function (obj) {\n  if (obj.constructor.name == \"Point\") {\n    var pt = obj;\n    return this.x == pt.x && this.y == pt.y;\n  }\n  return this == obj;\n};\n\nPoint.prototype.toString = function () {\n  return new Point().constructor.name + \"[x=\" + this.x + \",y=\" + this.y + \"]\";\n};\n\nmodule.exports = Point;\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction RectangleD(x, y, width, height) {\n  this.x = 0;\n  this.y = 0;\n  this.width = 0;\n  this.height = 0;\n\n  if (x != null && y != null && width != null && height != null) {\n    this.x = x;\n    this.y = y;\n    this.width = width;\n    this.height = height;\n  }\n}\n\nRectangleD.prototype.getX = function () {\n  return this.x;\n};\n\nRectangleD.prototype.setX = function (x) {\n  this.x = x;\n};\n\nRectangleD.prototype.getY = function () {\n  return this.y;\n};\n\nRectangleD.prototype.setY = function (y) {\n  this.y = y;\n};\n\nRectangleD.prototype.getWidth = function () {\n  return this.width;\n};\n\nRectangleD.prototype.setWidth = function (width) {\n  this.width = width;\n};\n\nRectangleD.prototype.getHeight = function () {\n  return this.height;\n};\n\nRectangleD.prototype.setHeight = function (height) {\n  this.height = height;\n};\n\nRectangleD.prototype.getRight = function () {\n  return this.x + this.width;\n};\n\nRectangleD.prototype.getBottom = function () {\n  return this.y + this.height;\n};\n\nRectangleD.prototype.intersects = function (a) {\n  if (this.getRight() < a.x) {\n    return false;\n  }\n\n  if (this.getBottom() < a.y) {\n    return false;\n  }\n\n  if (a.getRight() < this.x) {\n    return false;\n  }\n\n  if (a.getBottom() < this.y) {\n    return false;\n  }\n\n  return true;\n};\n\nRectangleD.prototype.getCenterX = function () {\n  return this.x + this.width / 2;\n};\n\nRectangleD.prototype.getMinX = function () {\n  return this.getX();\n};\n\nRectangleD.prototype.getMaxX = function () {\n  return this.getX() + this.width;\n};\n\nRectangleD.prototype.getCenterY = function () {\n  return this.y + this.height / 2;\n};\n\nRectangleD.prototype.getMinY = function () {\n  return this.getY();\n};\n\nRectangleD.prototype.getMaxY = function () {\n  return this.getY() + this.height;\n};\n\nRectangleD.prototype.getWidthHalf = function () {\n  return this.width / 2;\n};\n\nRectangleD.prototype.getHeightHalf = function () {\n  return this.height / 2;\n};\n\nmodule.exports = RectangleD;\n\n/***/ }),\n/* 14 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nfunction UniqueIDGeneretor() {}\n\nUniqueIDGeneretor.lastID = 0;\n\nUniqueIDGeneretor.createID = function (obj) {\n  if (UniqueIDGeneretor.isPrimitive(obj)) {\n    return obj;\n  }\n  if (obj.uniqueID != null) {\n    return obj.uniqueID;\n  }\n  obj.uniqueID = UniqueIDGeneretor.getString();\n  UniqueIDGeneretor.lastID++;\n  return obj.uniqueID;\n};\n\nUniqueIDGeneretor.getString = function (id) {\n  if (id == null) id = UniqueIDGeneretor.lastID;\n  return \"Object#\" + id + \"\";\n};\n\nUniqueIDGeneretor.isPrimitive = function (arg) {\n  var type = typeof arg === \"undefined\" ? \"undefined\" : _typeof(arg);\n  return arg == null || type != \"object\" && type != \"function\";\n};\n\nmodule.exports = UniqueIDGeneretor;\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar LayoutConstants = __webpack_require__(0);\nvar LGraphManager = __webpack_require__(7);\nvar LNode = __webpack_require__(3);\nvar LEdge = __webpack_require__(1);\nvar LGraph = __webpack_require__(6);\nvar PointD = __webpack_require__(5);\nvar Transform = __webpack_require__(17);\nvar Emitter = __webpack_require__(29);\n\nfunction Layout(isRemoteUse) {\n  Emitter.call(this);\n\n  //Layout Quality: 0:draft, 1:default, 2:proof\n  this.layoutQuality = LayoutConstants.QUALITY;\n  //Whether layout should create bendpoints as needed or not\n  this.createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n  //Whether layout should be incremental or not\n  this.incremental = LayoutConstants.DEFAULT_INCREMENTAL;\n  //Whether we animate from before to after layout node positions\n  this.animationOnLayout = LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT;\n  //Whether we animate the layout process or not\n  this.animationDuringLayout = LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT;\n  //Number iterations that should be done between two successive animations\n  this.animationPeriod = LayoutConstants.DEFAULT_ANIMATION_PERIOD;\n  /**\r\n   * Whether or not leaf nodes (non-compound nodes) are of uniform sizes. When\r\n   * they are, both spring and repulsion forces between two leaf nodes can be\r\n   * calculated without the expensive clipping point calculations, resulting\r\n   * in major speed-up.\r\n   */\n  this.uniformLeafNodeSizes = LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES;\n  /**\r\n   * This is used for creation of bendpoints by using dummy nodes and edges.\r\n   * Maps an LEdge to its dummy bendpoint path.\r\n   */\n  this.edgeToDummyNodes = new Map();\n  this.graphManager = new LGraphManager(this);\n  this.isLayoutFinished = false;\n  this.isSubLayout = false;\n  this.isRemoteUse = false;\n\n  if (isRemoteUse != null) {\n    this.isRemoteUse = isRemoteUse;\n  }\n}\n\nLayout.RANDOM_SEED = 1;\n\nLayout.prototype = Object.create(Emitter.prototype);\n\nLayout.prototype.getGraphManager = function () {\n  return this.graphManager;\n};\n\nLayout.prototype.getAllNodes = function () {\n  return this.graphManager.getAllNodes();\n};\n\nLayout.prototype.getAllEdges = function () {\n  return this.graphManager.getAllEdges();\n};\n\nLayout.prototype.getAllNodesToApplyGravitation = function () {\n  return this.graphManager.getAllNodesToApplyGravitation();\n};\n\nLayout.prototype.newGraphManager = function () {\n  var gm = new LGraphManager(this);\n  this.graphManager = gm;\n  return gm;\n};\n\nLayout.prototype.newGraph = function (vGraph) {\n  return new LGraph(null, this.graphManager, vGraph);\n};\n\nLayout.prototype.newNode = function (vNode) {\n  return new LNode(this.graphManager, vNode);\n};\n\nLayout.prototype.newEdge = function (vEdge) {\n  return new LEdge(null, null, vEdge);\n};\n\nLayout.prototype.checkLayoutSuccess = function () {\n  return this.graphManager.getRoot() == null || this.graphManager.getRoot().getNodes().length == 0 || this.graphManager.includesInvalidEdge();\n};\n\nLayout.prototype.runLayout = function () {\n  this.isLayoutFinished = false;\n\n  if (this.tilingPreLayout) {\n    this.tilingPreLayout();\n  }\n\n  this.initParameters();\n  var isLayoutSuccessfull;\n\n  if (this.checkLayoutSuccess()) {\n    isLayoutSuccessfull = false;\n  } else {\n    isLayoutSuccessfull = this.layout();\n  }\n\n  if (LayoutConstants.ANIMATE === 'during') {\n    // If this is a 'during' layout animation. Layout is not finished yet. \n    // We need to perform these in index.js when layout is really finished.\n    return false;\n  }\n\n  if (isLayoutSuccessfull) {\n    if (!this.isSubLayout) {\n      this.doPostLayout();\n    }\n  }\n\n  if (this.tilingPostLayout) {\n    this.tilingPostLayout();\n  }\n\n  this.isLayoutFinished = true;\n\n  return isLayoutSuccessfull;\n};\n\n/**\r\n * This method performs the operations required after layout.\r\n */\nLayout.prototype.doPostLayout = function () {\n  //assert !isSubLayout : \"Should not be called on sub-layout!\";\n  // Propagate geometric changes to v-level objects\n  if (!this.incremental) {\n    this.transform();\n  }\n  this.update();\n};\n\n/**\r\n * This method updates the geometry of the target graph according to\r\n * calculated layout.\r\n */\nLayout.prototype.update2 = function () {\n  // update bend points\n  if (this.createBendsAsNeeded) {\n    this.createBendpointsFromDummyNodes();\n\n    // reset all edges, since the topology has changed\n    this.graphManager.resetAllEdges();\n  }\n\n  // perform edge, node and root updates if layout is not called\n  // remotely\n  if (!this.isRemoteUse) {\n    // update all edges\n    var edge;\n    var allEdges = this.graphManager.getAllEdges();\n    for (var i = 0; i < allEdges.length; i++) {\n      edge = allEdges[i];\n      //      this.update(edge);\n    }\n\n    // recursively update nodes\n    var node;\n    var nodes = this.graphManager.getRoot().getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      node = nodes[i];\n      //      this.update(node);\n    }\n\n    // update root graph\n    this.update(this.graphManager.getRoot());\n  }\n};\n\nLayout.prototype.update = function (obj) {\n  if (obj == null) {\n    this.update2();\n  } else if (obj instanceof LNode) {\n    var node = obj;\n    if (node.getChild() != null) {\n      // since node is compound, recursively update child nodes\n      var nodes = node.getChild().getNodes();\n      for (var i = 0; i < nodes.length; i++) {\n        update(nodes[i]);\n      }\n    }\n\n    // if the l-level node is associated with a v-level graph object,\n    // then it is assumed that the v-level node implements the\n    // interface Updatable.\n    if (node.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vNode = node.vGraphObject;\n\n      // call the update method of the interface\n      vNode.update(node);\n    }\n  } else if (obj instanceof LEdge) {\n    var edge = obj;\n    // if the l-level edge is associated with a v-level graph object,\n    // then it is assumed that the v-level edge implements the\n    // interface Updatable.\n\n    if (edge.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vEdge = edge.vGraphObject;\n\n      // call the update method of the interface\n      vEdge.update(edge);\n    }\n  } else if (obj instanceof LGraph) {\n    var graph = obj;\n    // if the l-level graph is associated with a v-level graph object,\n    // then it is assumed that the v-level object implements the\n    // interface Updatable.\n\n    if (graph.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vGraph = graph.vGraphObject;\n\n      // call the update method of the interface\n      vGraph.update(graph);\n    }\n  }\n};\n\n/**\r\n * This method is used to set all layout parameters to default values\r\n * determined at compile time.\r\n */\nLayout.prototype.initParameters = function () {\n  if (!this.isSubLayout) {\n    this.layoutQuality = LayoutConstants.QUALITY;\n    this.animationDuringLayout = LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT;\n    this.animationPeriod = LayoutConstants.DEFAULT_ANIMATION_PERIOD;\n    this.animationOnLayout = LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT;\n    this.incremental = LayoutConstants.DEFAULT_INCREMENTAL;\n    this.createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n    this.uniformLeafNodeSizes = LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES;\n  }\n\n  if (this.animationDuringLayout) {\n    this.animationOnLayout = false;\n  }\n};\n\nLayout.prototype.transform = function (newLeftTop) {\n  if (newLeftTop == undefined) {\n    this.transform(new PointD(0, 0));\n  } else {\n    // create a transformation object (from Eclipse to layout). When an\n    // inverse transform is applied, we get upper-left coordinate of the\n    // drawing or the root graph at given input coordinate (some margins\n    // already included in calculation of left-top).\n\n    var trans = new Transform();\n    var leftTop = this.graphManager.getRoot().updateLeftTop();\n\n    if (leftTop != null) {\n      trans.setWorldOrgX(newLeftTop.x);\n      trans.setWorldOrgY(newLeftTop.y);\n\n      trans.setDeviceOrgX(leftTop.x);\n      trans.setDeviceOrgY(leftTop.y);\n\n      var nodes = this.getAllNodes();\n      var node;\n\n      for (var i = 0; i < nodes.length; i++) {\n        node = nodes[i];\n        node.transform(trans);\n      }\n    }\n  }\n};\n\nLayout.prototype.positionNodesRandomly = function (graph) {\n\n  if (graph == undefined) {\n    //assert !this.incremental;\n    this.positionNodesRandomly(this.getGraphManager().getRoot());\n    this.getGraphManager().getRoot().updateBounds(true);\n  } else {\n    var lNode;\n    var childGraph;\n\n    var nodes = graph.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      lNode = nodes[i];\n      childGraph = lNode.getChild();\n\n      if (childGraph == null) {\n        lNode.scatter();\n      } else if (childGraph.getNodes().length == 0) {\n        lNode.scatter();\n      } else {\n        this.positionNodesRandomly(childGraph);\n        lNode.updateBounds();\n      }\n    }\n  }\n};\n\n/**\r\n * This method returns a list of trees where each tree is represented as a\r\n * list of l-nodes. The method returns a list of size 0 when:\r\n * - The graph is not flat or\r\n * - One of the component(s) of the graph is not a tree.\r\n */\nLayout.prototype.getFlatForest = function () {\n  var flatForest = [];\n  var isForest = true;\n\n  // Quick reference for all nodes in the graph manager associated with\n  // this layout. The list should not be changed.\n  var allNodes = this.graphManager.getRoot().getNodes();\n\n  // First be sure that the graph is flat\n  var isFlat = true;\n\n  for (var i = 0; i < allNodes.length; i++) {\n    if (allNodes[i].getChild() != null) {\n      isFlat = false;\n    }\n  }\n\n  // Return empty forest if the graph is not flat.\n  if (!isFlat) {\n    return flatForest;\n  }\n\n  // Run BFS for each component of the graph.\n\n  var visited = new Set();\n  var toBeVisited = [];\n  var parents = new Map();\n  var unProcessedNodes = [];\n\n  unProcessedNodes = unProcessedNodes.concat(allNodes);\n\n  // Each iteration of this loop finds a component of the graph and\n  // decides whether it is a tree or not. If it is a tree, adds it to the\n  // forest and continued with the next component.\n\n  while (unProcessedNodes.length > 0 && isForest) {\n    toBeVisited.push(unProcessedNodes[0]);\n\n    // Start the BFS. Each iteration of this loop visits a node in a\n    // BFS manner.\n    while (toBeVisited.length > 0 && isForest) {\n      //pool operation\n      var currentNode = toBeVisited[0];\n      toBeVisited.splice(0, 1);\n      visited.add(currentNode);\n\n      // Traverse all neighbors of this node\n      var neighborEdges = currentNode.getEdges();\n\n      for (var i = 0; i < neighborEdges.length; i++) {\n        var currentNeighbor = neighborEdges[i].getOtherEnd(currentNode);\n\n        // If BFS is not growing from this neighbor.\n        if (parents.get(currentNode) != currentNeighbor) {\n          // We haven't previously visited this neighbor.\n          if (!visited.has(currentNeighbor)) {\n            toBeVisited.push(currentNeighbor);\n            parents.set(currentNeighbor, currentNode);\n          }\n          // Since we have previously visited this neighbor and\n          // this neighbor is not parent of currentNode, given\n          // graph contains a component that is not tree, hence\n          // it is not a forest.\n          else {\n              isForest = false;\n              break;\n            }\n        }\n      }\n    }\n\n    // The graph contains a component that is not a tree. Empty\n    // previously found trees. The method will end.\n    if (!isForest) {\n      flatForest = [];\n    }\n    // Save currently visited nodes as a tree in our forest. Reset\n    // visited and parents lists. Continue with the next component of\n    // the graph, if any.\n    else {\n        var temp = [].concat(_toConsumableArray(visited));\n        flatForest.push(temp);\n        //flatForest = flatForest.concat(temp);\n        //unProcessedNodes.removeAll(visited);\n        for (var i = 0; i < temp.length; i++) {\n          var value = temp[i];\n          var index = unProcessedNodes.indexOf(value);\n          if (index > -1) {\n            unProcessedNodes.splice(index, 1);\n          }\n        }\n        visited = new Set();\n        parents = new Map();\n      }\n  }\n\n  return flatForest;\n};\n\n/**\r\n * This method creates dummy nodes (an l-level node with minimal dimensions)\r\n * for the given edge (one per bendpoint). The existing l-level structure\r\n * is updated accordingly.\r\n */\nLayout.prototype.createDummyNodesForBendpoints = function (edge) {\n  var dummyNodes = [];\n  var prev = edge.source;\n\n  var graph = this.graphManager.calcLowestCommonAncestor(edge.source, edge.target);\n\n  for (var i = 0; i < edge.bendpoints.length; i++) {\n    // create new dummy node\n    var dummyNode = this.newNode(null);\n    dummyNode.setRect(new Point(0, 0), new Dimension(1, 1));\n\n    graph.add(dummyNode);\n\n    // create new dummy edge between prev and dummy node\n    var dummyEdge = this.newEdge(null);\n    this.graphManager.add(dummyEdge, prev, dummyNode);\n\n    dummyNodes.add(dummyNode);\n    prev = dummyNode;\n  }\n\n  var dummyEdge = this.newEdge(null);\n  this.graphManager.add(dummyEdge, prev, edge.target);\n\n  this.edgeToDummyNodes.set(edge, dummyNodes);\n\n  // remove real edge from graph manager if it is inter-graph\n  if (edge.isInterGraph()) {\n    this.graphManager.remove(edge);\n  }\n  // else, remove the edge from the current graph\n  else {\n      graph.remove(edge);\n    }\n\n  return dummyNodes;\n};\n\n/**\r\n * This method creates bendpoints for edges from the dummy nodes\r\n * at l-level.\r\n */\nLayout.prototype.createBendpointsFromDummyNodes = function () {\n  var edges = [];\n  edges = edges.concat(this.graphManager.getAllEdges());\n  edges = [].concat(_toConsumableArray(this.edgeToDummyNodes.keys())).concat(edges);\n\n  for (var k = 0; k < edges.length; k++) {\n    var lEdge = edges[k];\n\n    if (lEdge.bendpoints.length > 0) {\n      var path = this.edgeToDummyNodes.get(lEdge);\n\n      for (var i = 0; i < path.length; i++) {\n        var dummyNode = path[i];\n        var p = new PointD(dummyNode.getCenterX(), dummyNode.getCenterY());\n\n        // update bendpoint's location according to dummy node\n        var ebp = lEdge.bendpoints.get(i);\n        ebp.x = p.x;\n        ebp.y = p.y;\n\n        // remove the dummy node, dummy edges incident with this\n        // dummy node is also removed (within the remove method)\n        dummyNode.getOwner().remove(dummyNode);\n      }\n\n      // add the real edge to graph\n      this.graphManager.add(lEdge, lEdge.source, lEdge.target);\n    }\n  }\n};\n\nLayout.transform = function (sliderValue, defaultValue, minDiv, maxMul) {\n  if (minDiv != undefined && maxMul != undefined) {\n    var value = defaultValue;\n\n    if (sliderValue <= 50) {\n      var minValue = defaultValue / minDiv;\n      value -= (defaultValue - minValue) / 50 * (50 - sliderValue);\n    } else {\n      var maxValue = defaultValue * maxMul;\n      value += (maxValue - defaultValue) / 50 * (sliderValue - 50);\n    }\n\n    return value;\n  } else {\n    var a, b;\n\n    if (sliderValue <= 50) {\n      a = 9.0 * defaultValue / 500.0;\n      b = defaultValue / 10.0;\n    } else {\n      a = 9.0 * defaultValue / 50.0;\n      b = -8 * defaultValue;\n    }\n\n    return a * sliderValue + b;\n  }\n};\n\n/**\r\n * This method finds and returns the center of the given nodes, assuming\r\n * that the given nodes form a tree in themselves.\r\n */\nLayout.findCenterOfTree = function (nodes) {\n  var list = [];\n  list = list.concat(nodes);\n\n  var removedNodes = [];\n  var remainingDegrees = new Map();\n  var foundCenter = false;\n  var centerNode = null;\n\n  if (list.length == 1 || list.length == 2) {\n    foundCenter = true;\n    centerNode = list[0];\n  }\n\n  for (var i = 0; i < list.length; i++) {\n    var node = list[i];\n    var degree = node.getNeighborsList().size;\n    remainingDegrees.set(node, node.getNeighborsList().size);\n\n    if (degree == 1) {\n      removedNodes.push(node);\n    }\n  }\n\n  var tempList = [];\n  tempList = tempList.concat(removedNodes);\n\n  while (!foundCenter) {\n    var tempList2 = [];\n    tempList2 = tempList2.concat(tempList);\n    tempList = [];\n\n    for (var i = 0; i < list.length; i++) {\n      var node = list[i];\n\n      var index = list.indexOf(node);\n      if (index >= 0) {\n        list.splice(index, 1);\n      }\n\n      var neighbours = node.getNeighborsList();\n\n      neighbours.forEach(function (neighbour) {\n        if (removedNodes.indexOf(neighbour) < 0) {\n          var otherDegree = remainingDegrees.get(neighbour);\n          var newDegree = otherDegree - 1;\n\n          if (newDegree == 1) {\n            tempList.push(neighbour);\n          }\n\n          remainingDegrees.set(neighbour, newDegree);\n        }\n      });\n    }\n\n    removedNodes = removedNodes.concat(tempList);\n\n    if (list.length == 1 || list.length == 2) {\n      foundCenter = true;\n      centerNode = list[0];\n    }\n  }\n\n  return centerNode;\n};\n\n/**\r\n * During the coarsening process, this layout may be referenced by two graph managers\r\n * this setter function grants access to change the currently being used graph manager\r\n */\nLayout.prototype.setGraphManager = function (gm) {\n  this.graphManager = gm;\n};\n\nmodule.exports = Layout;\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction RandomSeed() {}\n// adapted from: https://stackoverflow.com/a/19303725\nRandomSeed.seed = 1;\nRandomSeed.x = 0;\n\nRandomSeed.nextDouble = function () {\n  RandomSeed.x = Math.sin(RandomSeed.seed++) * 10000;\n  return RandomSeed.x - Math.floor(RandomSeed.x);\n};\n\nmodule.exports = RandomSeed;\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar PointD = __webpack_require__(5);\n\nfunction Transform(x, y) {\n  this.lworldOrgX = 0.0;\n  this.lworldOrgY = 0.0;\n  this.ldeviceOrgX = 0.0;\n  this.ldeviceOrgY = 0.0;\n  this.lworldExtX = 1.0;\n  this.lworldExtY = 1.0;\n  this.ldeviceExtX = 1.0;\n  this.ldeviceExtY = 1.0;\n}\n\nTransform.prototype.getWorldOrgX = function () {\n  return this.lworldOrgX;\n};\n\nTransform.prototype.setWorldOrgX = function (wox) {\n  this.lworldOrgX = wox;\n};\n\nTransform.prototype.getWorldOrgY = function () {\n  return this.lworldOrgY;\n};\n\nTransform.prototype.setWorldOrgY = function (woy) {\n  this.lworldOrgY = woy;\n};\n\nTransform.prototype.getWorldExtX = function () {\n  return this.lworldExtX;\n};\n\nTransform.prototype.setWorldExtX = function (wex) {\n  this.lworldExtX = wex;\n};\n\nTransform.prototype.getWorldExtY = function () {\n  return this.lworldExtY;\n};\n\nTransform.prototype.setWorldExtY = function (wey) {\n  this.lworldExtY = wey;\n};\n\n/* Device related */\n\nTransform.prototype.getDeviceOrgX = function () {\n  return this.ldeviceOrgX;\n};\n\nTransform.prototype.setDeviceOrgX = function (dox) {\n  this.ldeviceOrgX = dox;\n};\n\nTransform.prototype.getDeviceOrgY = function () {\n  return this.ldeviceOrgY;\n};\n\nTransform.prototype.setDeviceOrgY = function (doy) {\n  this.ldeviceOrgY = doy;\n};\n\nTransform.prototype.getDeviceExtX = function () {\n  return this.ldeviceExtX;\n};\n\nTransform.prototype.setDeviceExtX = function (dex) {\n  this.ldeviceExtX = dex;\n};\n\nTransform.prototype.getDeviceExtY = function () {\n  return this.ldeviceExtY;\n};\n\nTransform.prototype.setDeviceExtY = function (dey) {\n  this.ldeviceExtY = dey;\n};\n\nTransform.prototype.transformX = function (x) {\n  var xDevice = 0.0;\n  var worldExtX = this.lworldExtX;\n  if (worldExtX != 0.0) {\n    xDevice = this.ldeviceOrgX + (x - this.lworldOrgX) * this.ldeviceExtX / worldExtX;\n  }\n\n  return xDevice;\n};\n\nTransform.prototype.transformY = function (y) {\n  var yDevice = 0.0;\n  var worldExtY = this.lworldExtY;\n  if (worldExtY != 0.0) {\n    yDevice = this.ldeviceOrgY + (y - this.lworldOrgY) * this.ldeviceExtY / worldExtY;\n  }\n\n  return yDevice;\n};\n\nTransform.prototype.inverseTransformX = function (x) {\n  var xWorld = 0.0;\n  var deviceExtX = this.ldeviceExtX;\n  if (deviceExtX != 0.0) {\n    xWorld = this.lworldOrgX + (x - this.ldeviceOrgX) * this.lworldExtX / deviceExtX;\n  }\n\n  return xWorld;\n};\n\nTransform.prototype.inverseTransformY = function (y) {\n  var yWorld = 0.0;\n  var deviceExtY = this.ldeviceExtY;\n  if (deviceExtY != 0.0) {\n    yWorld = this.lworldOrgY + (y - this.ldeviceOrgY) * this.lworldExtY / deviceExtY;\n  }\n  return yWorld;\n};\n\nTransform.prototype.inverseTransformPoint = function (inPoint) {\n  var outPoint = new PointD(this.inverseTransformX(inPoint.x), this.inverseTransformY(inPoint.y));\n  return outPoint;\n};\n\nmodule.exports = Transform;\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar Layout = __webpack_require__(15);\nvar FDLayoutConstants = __webpack_require__(4);\nvar LayoutConstants = __webpack_require__(0);\nvar IGeometry = __webpack_require__(8);\nvar IMath = __webpack_require__(9);\n\nfunction FDLayout() {\n  Layout.call(this);\n\n  this.useSmartIdealEdgeLengthCalculation = FDLayoutConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION;\n  this.gravityConstant = FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH;\n  this.compoundGravityConstant = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH;\n  this.gravityRangeFactor = FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR;\n  this.compoundGravityRangeFactor = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR;\n  this.displacementThresholdPerNode = 3.0 * FDLayoutConstants.DEFAULT_EDGE_LENGTH / 100;\n  this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n  this.initialCoolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n  this.totalDisplacement = 0.0;\n  this.oldTotalDisplacement = 0.0;\n  this.maxIterations = FDLayoutConstants.MAX_ITERATIONS;\n}\n\nFDLayout.prototype = Object.create(Layout.prototype);\n\nfor (var prop in Layout) {\n  FDLayout[prop] = Layout[prop];\n}\n\nFDLayout.prototype.initParameters = function () {\n  Layout.prototype.initParameters.call(this, arguments);\n\n  this.totalIterations = 0;\n  this.notAnimatedIterations = 0;\n\n  this.useFRGridVariant = FDLayoutConstants.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION;\n\n  this.grid = [];\n};\n\nFDLayout.prototype.calcIdealEdgeLengths = function () {\n  var edge;\n  var originalIdealLength;\n  var lcaDepth;\n  var source;\n  var target;\n  var sizeOfSourceInLca;\n  var sizeOfTargetInLca;\n\n  var allEdges = this.getGraphManager().getAllEdges();\n  for (var i = 0; i < allEdges.length; i++) {\n    edge = allEdges[i];\n\n    originalIdealLength = edge.idealLength;\n\n    if (edge.isInterGraph) {\n      source = edge.getSource();\n      target = edge.getTarget();\n\n      sizeOfSourceInLca = edge.getSourceInLca().getEstimatedSize();\n      sizeOfTargetInLca = edge.getTargetInLca().getEstimatedSize();\n\n      if (this.useSmartIdealEdgeLengthCalculation) {\n        edge.idealLength += sizeOfSourceInLca + sizeOfTargetInLca - 2 * LayoutConstants.SIMPLE_NODE_SIZE;\n      }\n\n      lcaDepth = edge.getLca().getInclusionTreeDepth();\n\n      edge.idealLength += originalIdealLength * FDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR * (source.getInclusionTreeDepth() + target.getInclusionTreeDepth() - 2 * lcaDepth);\n    }\n  }\n};\n\nFDLayout.prototype.initSpringEmbedder = function () {\n\n  var s = this.getAllNodes().length;\n  if (this.incremental) {\n    if (s > FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) {\n      this.coolingFactor = Math.max(this.coolingFactor * FDLayoutConstants.COOLING_ADAPTATION_FACTOR, this.coolingFactor - (s - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) / (FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) * this.coolingFactor * (1 - FDLayoutConstants.COOLING_ADAPTATION_FACTOR));\n    }\n    this.maxNodeDisplacement = FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL;\n  } else {\n    if (s > FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) {\n      this.coolingFactor = Math.max(FDLayoutConstants.COOLING_ADAPTATION_FACTOR, 1.0 - (s - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) / (FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) * (1 - FDLayoutConstants.COOLING_ADAPTATION_FACTOR));\n    } else {\n      this.coolingFactor = 1.0;\n    }\n    this.initialCoolingFactor = this.coolingFactor;\n    this.maxNodeDisplacement = FDLayoutConstants.MAX_NODE_DISPLACEMENT;\n  }\n\n  this.maxIterations = Math.max(this.getAllNodes().length * 5, this.maxIterations);\n\n  // Reassign this attribute by using new constant value\n  this.displacementThresholdPerNode = 3.0 * FDLayoutConstants.DEFAULT_EDGE_LENGTH / 100;\n  this.totalDisplacementThreshold = this.displacementThresholdPerNode * this.getAllNodes().length;\n\n  this.repulsionRange = this.calcRepulsionRange();\n};\n\nFDLayout.prototype.calcSpringForces = function () {\n  var lEdges = this.getAllEdges();\n  var edge;\n\n  for (var i = 0; i < lEdges.length; i++) {\n    edge = lEdges[i];\n\n    this.calcSpringForce(edge, edge.idealLength);\n  }\n};\n\nFDLayout.prototype.calcRepulsionForces = function () {\n  var gridUpdateAllowed = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  var forceToNodeSurroundingUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var i, j;\n  var nodeA, nodeB;\n  var lNodes = this.getAllNodes();\n  var processedNodeSet;\n\n  if (this.useFRGridVariant) {\n    if (this.totalIterations % FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD == 1 && gridUpdateAllowed) {\n      this.updateGrid();\n    }\n\n    processedNodeSet = new Set();\n\n    // calculate repulsion forces between each nodes and its surrounding\n    for (i = 0; i < lNodes.length; i++) {\n      nodeA = lNodes[i];\n      this.calculateRepulsionForceOfANode(nodeA, processedNodeSet, gridUpdateAllowed, forceToNodeSurroundingUpdate);\n      processedNodeSet.add(nodeA);\n    }\n  } else {\n    for (i = 0; i < lNodes.length; i++) {\n      nodeA = lNodes[i];\n\n      for (j = i + 1; j < lNodes.length; j++) {\n        nodeB = lNodes[j];\n\n        // If both nodes are not members of the same graph, skip.\n        if (nodeA.getOwner() != nodeB.getOwner()) {\n          continue;\n        }\n\n        this.calcRepulsionForce(nodeA, nodeB);\n      }\n    }\n  }\n};\n\nFDLayout.prototype.calcGravitationalForces = function () {\n  var node;\n  var lNodes = this.getAllNodesToApplyGravitation();\n\n  for (var i = 0; i < lNodes.length; i++) {\n    node = lNodes[i];\n    this.calcGravitationalForce(node);\n  }\n};\n\nFDLayout.prototype.moveNodes = function () {\n  var lNodes = this.getAllNodes();\n  var node;\n\n  for (var i = 0; i < lNodes.length; i++) {\n    node = lNodes[i];\n    node.move();\n  }\n};\n\nFDLayout.prototype.calcSpringForce = function (edge, idealLength) {\n  var sourceNode = edge.getSource();\n  var targetNode = edge.getTarget();\n\n  var length;\n  var springForce;\n  var springForceX;\n  var springForceY;\n\n  // Update edge length\n  if (this.uniformLeafNodeSizes && sourceNode.getChild() == null && targetNode.getChild() == null) {\n    edge.updateLengthSimple();\n  } else {\n    edge.updateLength();\n\n    if (edge.isOverlapingSourceAndTarget) {\n      return;\n    }\n  }\n\n  length = edge.getLength();\n\n  if (length == 0) return;\n\n  // Calculate spring forces\n  springForce = edge.edgeElasticity * (length - idealLength);\n\n  // Project force onto x and y axes\n  springForceX = springForce * (edge.lengthX / length);\n  springForceY = springForce * (edge.lengthY / length);\n\n  // Apply forces on the end nodes\n  sourceNode.springForceX += springForceX;\n  sourceNode.springForceY += springForceY;\n  targetNode.springForceX -= springForceX;\n  targetNode.springForceY -= springForceY;\n};\n\nFDLayout.prototype.calcRepulsionForce = function (nodeA, nodeB) {\n  var rectA = nodeA.getRect();\n  var rectB = nodeB.getRect();\n  var overlapAmount = new Array(2);\n  var clipPoints = new Array(4);\n  var distanceX;\n  var distanceY;\n  var distanceSquared;\n  var distance;\n  var repulsionForce;\n  var repulsionForceX;\n  var repulsionForceY;\n\n  if (rectA.intersects(rectB)) // two nodes overlap\n    {\n      // calculate separation amount in x and y directions\n      IGeometry.calcSeparationAmount(rectA, rectB, overlapAmount, FDLayoutConstants.DEFAULT_EDGE_LENGTH / 2.0);\n\n      repulsionForceX = 2 * overlapAmount[0];\n      repulsionForceY = 2 * overlapAmount[1];\n\n      var childrenConstant = nodeA.noOfChildren * nodeB.noOfChildren / (nodeA.noOfChildren + nodeB.noOfChildren);\n\n      // Apply forces on the two nodes\n      nodeA.repulsionForceX -= childrenConstant * repulsionForceX;\n      nodeA.repulsionForceY -= childrenConstant * repulsionForceY;\n      nodeB.repulsionForceX += childrenConstant * repulsionForceX;\n      nodeB.repulsionForceY += childrenConstant * repulsionForceY;\n    } else // no overlap\n    {\n      // calculate distance\n\n      if (this.uniformLeafNodeSizes && nodeA.getChild() == null && nodeB.getChild() == null) // simply base repulsion on distance of node centers\n        {\n          distanceX = rectB.getCenterX() - rectA.getCenterX();\n          distanceY = rectB.getCenterY() - rectA.getCenterY();\n        } else // use clipping points\n        {\n          IGeometry.getIntersection(rectA, rectB, clipPoints);\n\n          distanceX = clipPoints[2] - clipPoints[0];\n          distanceY = clipPoints[3] - clipPoints[1];\n        }\n\n      // No repulsion range. FR grid variant should take care of this.\n      if (Math.abs(distanceX) < FDLayoutConstants.MIN_REPULSION_DIST) {\n        distanceX = IMath.sign(distanceX) * FDLayoutConstants.MIN_REPULSION_DIST;\n      }\n\n      if (Math.abs(distanceY) < FDLayoutConstants.MIN_REPULSION_DIST) {\n        distanceY = IMath.sign(distanceY) * FDLayoutConstants.MIN_REPULSION_DIST;\n      }\n\n      distanceSquared = distanceX * distanceX + distanceY * distanceY;\n      distance = Math.sqrt(distanceSquared);\n\n      // Here we use half of the nodes' repulsion values for backward compatibility\n      repulsionForce = (nodeA.nodeRepulsion / 2 + nodeB.nodeRepulsion / 2) * nodeA.noOfChildren * nodeB.noOfChildren / distanceSquared;\n\n      // Project force onto x and y axes\n      repulsionForceX = repulsionForce * distanceX / distance;\n      repulsionForceY = repulsionForce * distanceY / distance;\n\n      // Apply forces on the two nodes    \n      nodeA.repulsionForceX -= repulsionForceX;\n      nodeA.repulsionForceY -= repulsionForceY;\n      nodeB.repulsionForceX += repulsionForceX;\n      nodeB.repulsionForceY += repulsionForceY;\n    }\n};\n\nFDLayout.prototype.calcGravitationalForce = function (node) {\n  var ownerGraph;\n  var ownerCenterX;\n  var ownerCenterY;\n  var distanceX;\n  var distanceY;\n  var absDistanceX;\n  var absDistanceY;\n  var estimatedSize;\n  ownerGraph = node.getOwner();\n\n  ownerCenterX = (ownerGraph.getRight() + ownerGraph.getLeft()) / 2;\n  ownerCenterY = (ownerGraph.getTop() + ownerGraph.getBottom()) / 2;\n  distanceX = node.getCenterX() - ownerCenterX;\n  distanceY = node.getCenterY() - ownerCenterY;\n  absDistanceX = Math.abs(distanceX) + node.getWidth() / 2;\n  absDistanceY = Math.abs(distanceY) + node.getHeight() / 2;\n\n  if (node.getOwner() == this.graphManager.getRoot()) // in the root graph\n    {\n      estimatedSize = ownerGraph.getEstimatedSize() * this.gravityRangeFactor;\n\n      if (absDistanceX > estimatedSize || absDistanceY > estimatedSize) {\n        node.gravitationForceX = -this.gravityConstant * distanceX;\n        node.gravitationForceY = -this.gravityConstant * distanceY;\n      }\n    } else // inside a compound\n    {\n      estimatedSize = ownerGraph.getEstimatedSize() * this.compoundGravityRangeFactor;\n\n      if (absDistanceX > estimatedSize || absDistanceY > estimatedSize) {\n        node.gravitationForceX = -this.gravityConstant * distanceX * this.compoundGravityConstant;\n        node.gravitationForceY = -this.gravityConstant * distanceY * this.compoundGravityConstant;\n      }\n    }\n};\n\nFDLayout.prototype.isConverged = function () {\n  var converged;\n  var oscilating = false;\n\n  if (this.totalIterations > this.maxIterations / 3) {\n    oscilating = Math.abs(this.totalDisplacement - this.oldTotalDisplacement) < 2;\n  }\n\n  converged = this.totalDisplacement < this.totalDisplacementThreshold;\n\n  this.oldTotalDisplacement = this.totalDisplacement;\n\n  return converged || oscilating;\n};\n\nFDLayout.prototype.animate = function () {\n  if (this.animationDuringLayout && !this.isSubLayout) {\n    if (this.notAnimatedIterations == this.animationPeriod) {\n      this.update();\n      this.notAnimatedIterations = 0;\n    } else {\n      this.notAnimatedIterations++;\n    }\n  }\n};\n\n//This method calculates the number of children (weight) for all nodes\nFDLayout.prototype.calcNoOfChildrenForAllNodes = function () {\n  var node;\n  var allNodes = this.graphManager.getAllNodes();\n\n  for (var i = 0; i < allNodes.length; i++) {\n    node = allNodes[i];\n    node.noOfChildren = node.getNoOfChildren();\n  }\n};\n\n// -----------------------------------------------------------------------------\n// Section: FR-Grid Variant Repulsion Force Calculation\n// -----------------------------------------------------------------------------\n\nFDLayout.prototype.calcGrid = function (graph) {\n\n  var sizeX = 0;\n  var sizeY = 0;\n\n  sizeX = parseInt(Math.ceil((graph.getRight() - graph.getLeft()) / this.repulsionRange));\n  sizeY = parseInt(Math.ceil((graph.getBottom() - graph.getTop()) / this.repulsionRange));\n\n  var grid = new Array(sizeX);\n\n  for (var i = 0; i < sizeX; i++) {\n    grid[i] = new Array(sizeY);\n  }\n\n  for (var i = 0; i < sizeX; i++) {\n    for (var j = 0; j < sizeY; j++) {\n      grid[i][j] = new Array();\n    }\n  }\n\n  return grid;\n};\n\nFDLayout.prototype.addNodeToGrid = function (v, left, top) {\n\n  var startX = 0;\n  var finishX = 0;\n  var startY = 0;\n  var finishY = 0;\n\n  startX = parseInt(Math.floor((v.getRect().x - left) / this.repulsionRange));\n  finishX = parseInt(Math.floor((v.getRect().width + v.getRect().x - left) / this.repulsionRange));\n  startY = parseInt(Math.floor((v.getRect().y - top) / this.repulsionRange));\n  finishY = parseInt(Math.floor((v.getRect().height + v.getRect().y - top) / this.repulsionRange));\n\n  for (var i = startX; i <= finishX; i++) {\n    for (var j = startY; j <= finishY; j++) {\n      this.grid[i][j].push(v);\n      v.setGridCoordinates(startX, finishX, startY, finishY);\n    }\n  }\n};\n\nFDLayout.prototype.updateGrid = function () {\n  var i;\n  var nodeA;\n  var lNodes = this.getAllNodes();\n\n  this.grid = this.calcGrid(this.graphManager.getRoot());\n\n  // put all nodes to proper grid cells\n  for (i = 0; i < lNodes.length; i++) {\n    nodeA = lNodes[i];\n    this.addNodeToGrid(nodeA, this.graphManager.getRoot().getLeft(), this.graphManager.getRoot().getTop());\n  }\n};\n\nFDLayout.prototype.calculateRepulsionForceOfANode = function (nodeA, processedNodeSet, gridUpdateAllowed, forceToNodeSurroundingUpdate) {\n\n  if (this.totalIterations % FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD == 1 && gridUpdateAllowed || forceToNodeSurroundingUpdate) {\n    var surrounding = new Set();\n    nodeA.surrounding = new Array();\n    var nodeB;\n    var grid = this.grid;\n\n    for (var i = nodeA.startX - 1; i < nodeA.finishX + 2; i++) {\n      for (var j = nodeA.startY - 1; j < nodeA.finishY + 2; j++) {\n        if (!(i < 0 || j < 0 || i >= grid.length || j >= grid[0].length)) {\n          for (var k = 0; k < grid[i][j].length; k++) {\n            nodeB = grid[i][j][k];\n\n            // If both nodes are not members of the same graph, \n            // or both nodes are the same, skip.\n            if (nodeA.getOwner() != nodeB.getOwner() || nodeA == nodeB) {\n              continue;\n            }\n\n            // check if the repulsion force between\n            // nodeA and nodeB has already been calculated\n            if (!processedNodeSet.has(nodeB) && !surrounding.has(nodeB)) {\n              var distanceX = Math.abs(nodeA.getCenterX() - nodeB.getCenterX()) - (nodeA.getWidth() / 2 + nodeB.getWidth() / 2);\n              var distanceY = Math.abs(nodeA.getCenterY() - nodeB.getCenterY()) - (nodeA.getHeight() / 2 + nodeB.getHeight() / 2);\n\n              // if the distance between nodeA and nodeB \n              // is less then calculation range\n              if (distanceX <= this.repulsionRange && distanceY <= this.repulsionRange) {\n                //then add nodeB to surrounding of nodeA\n                surrounding.add(nodeB);\n              }\n            }\n          }\n        }\n      }\n    }\n\n    nodeA.surrounding = [].concat(_toConsumableArray(surrounding));\n  }\n  for (i = 0; i < nodeA.surrounding.length; i++) {\n    this.calcRepulsionForce(nodeA, nodeA.surrounding[i]);\n  }\n};\n\nFDLayout.prototype.calcRepulsionRange = function () {\n  return 0.0;\n};\n\nmodule.exports = FDLayout;\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LEdge = __webpack_require__(1);\nvar FDLayoutConstants = __webpack_require__(4);\n\nfunction FDLayoutEdge(source, target, vEdge) {\n  LEdge.call(this, source, target, vEdge);\n\n  // Ideal length and elasticity value for this edge\n  this.idealLength = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\n  this.edgeElasticity = FDLayoutConstants.DEFAULT_SPRING_STRENGTH;\n}\n\nFDLayoutEdge.prototype = Object.create(LEdge.prototype);\n\nfor (var prop in LEdge) {\n  FDLayoutEdge[prop] = LEdge[prop];\n}\n\nmodule.exports = FDLayoutEdge;\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LNode = __webpack_require__(3);\nvar FDLayoutConstants = __webpack_require__(4);\n\nfunction FDLayoutNode(gm, loc, size, vNode) {\n  // alternative constructor is handled inside LNode\n  LNode.call(this, gm, loc, size, vNode);\n\n  // Repulsion value of this node\n  this.nodeRepulsion = FDLayoutConstants.DEFAULT_REPULSION_STRENGTH;\n\n  //Spring, repulsion and gravitational forces acting on this node\n  this.springForceX = 0;\n  this.springForceY = 0;\n  this.repulsionForceX = 0;\n  this.repulsionForceY = 0;\n  this.gravitationForceX = 0;\n  this.gravitationForceY = 0;\n  //Amount by which this node is to be moved in this iteration\n  this.displacementX = 0;\n  this.displacementY = 0;\n\n  //Start and finish grid coordinates that this node is fallen into\n  this.startX = 0;\n  this.finishX = 0;\n  this.startY = 0;\n  this.finishY = 0;\n\n  //Geometric neighbors of this node\n  this.surrounding = [];\n}\n\nFDLayoutNode.prototype = Object.create(LNode.prototype);\n\nfor (var prop in LNode) {\n  FDLayoutNode[prop] = LNode[prop];\n}\n\nFDLayoutNode.prototype.setGridCoordinates = function (_startX, _finishX, _startY, _finishY) {\n  this.startX = _startX;\n  this.finishX = _finishX;\n  this.startY = _startY;\n  this.finishY = _finishY;\n};\n\nmodule.exports = FDLayoutNode;\n\n/***/ }),\n/* 21 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction DimensionD(width, height) {\n  this.width = 0;\n  this.height = 0;\n  if (width !== null && height !== null) {\n    this.height = height;\n    this.width = width;\n  }\n}\n\nDimensionD.prototype.getWidth = function () {\n  return this.width;\n};\n\nDimensionD.prototype.setWidth = function (width) {\n  this.width = width;\n};\n\nDimensionD.prototype.getHeight = function () {\n  return this.height;\n};\n\nDimensionD.prototype.setHeight = function (height) {\n  this.height = height;\n};\n\nmodule.exports = DimensionD;\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar UniqueIDGeneretor = __webpack_require__(14);\n\nfunction HashMap() {\n  this.map = {};\n  this.keys = [];\n}\n\nHashMap.prototype.put = function (key, value) {\n  var theId = UniqueIDGeneretor.createID(key);\n  if (!this.contains(theId)) {\n    this.map[theId] = value;\n    this.keys.push(key);\n  }\n};\n\nHashMap.prototype.contains = function (key) {\n  var theId = UniqueIDGeneretor.createID(key);\n  return this.map[key] != null;\n};\n\nHashMap.prototype.get = function (key) {\n  var theId = UniqueIDGeneretor.createID(key);\n  return this.map[theId];\n};\n\nHashMap.prototype.keySet = function () {\n  return this.keys;\n};\n\nmodule.exports = HashMap;\n\n/***/ }),\n/* 23 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar UniqueIDGeneretor = __webpack_require__(14);\n\nfunction HashSet() {\n  this.set = {};\n}\n;\n\nHashSet.prototype.add = function (obj) {\n  var theId = UniqueIDGeneretor.createID(obj);\n  if (!this.contains(theId)) this.set[theId] = obj;\n};\n\nHashSet.prototype.remove = function (obj) {\n  delete this.set[UniqueIDGeneretor.createID(obj)];\n};\n\nHashSet.prototype.clear = function () {\n  this.set = {};\n};\n\nHashSet.prototype.contains = function (obj) {\n  return this.set[UniqueIDGeneretor.createID(obj)] == obj;\n};\n\nHashSet.prototype.isEmpty = function () {\n  return this.size() === 0;\n};\n\nHashSet.prototype.size = function () {\n  return Object.keys(this.set).length;\n};\n\n//concats this.set to the given list\nHashSet.prototype.addAllTo = function (list) {\n  var keys = Object.keys(this.set);\n  var length = keys.length;\n  for (var i = 0; i < length; i++) {\n    list.push(this.set[keys[i]]);\n  }\n};\n\nHashSet.prototype.size = function () {\n  return Object.keys(this.set).length;\n};\n\nHashSet.prototype.addAll = function (list) {\n  var s = list.length;\n  for (var i = 0; i < s; i++) {\n    var v = list[i];\n    this.add(v);\n  }\n};\n\nmodule.exports = HashSet;\n\n/***/ }),\n/* 24 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n// Some matrix (1d and 2d array) operations\nfunction Matrix() {}\n\n/**\n * matrix multiplication\n * array1, array2 and result are 2d arrays\n */\nMatrix.multMat = function (array1, array2) {\n  var result = [];\n\n  for (var i = 0; i < array1.length; i++) {\n    result[i] = [];\n    for (var j = 0; j < array2[0].length; j++) {\n      result[i][j] = 0;\n      for (var k = 0; k < array1[0].length; k++) {\n        result[i][j] += array1[i][k] * array2[k][j];\n      }\n    }\n  }\n  return result;\n};\n\n/**\n * matrix transpose\n * array and result are 2d arrays\n */\nMatrix.transpose = function (array) {\n  var result = [];\n\n  for (var i = 0; i < array[0].length; i++) {\n    result[i] = [];\n    for (var j = 0; j < array.length; j++) {\n      result[i][j] = array[j][i];\n    }\n  }\n\n  return result;\n};\n\n/**\n * multiply array with constant\n * array and result are 1d arrays\n */\nMatrix.multCons = function (array, constant) {\n  var result = [];\n\n  for (var i = 0; i < array.length; i++) {\n    result[i] = array[i] * constant;\n  }\n\n  return result;\n};\n\n/**\n * substract two arrays\n * array1, array2 and result are 1d arrays\n */\nMatrix.minusOp = function (array1, array2) {\n  var result = [];\n\n  for (var i = 0; i < array1.length; i++) {\n    result[i] = array1[i] - array2[i];\n  }\n\n  return result;\n};\n\n/**\n * dot product of two arrays with same size\n * array1 and array2 are 1d arrays\n */\nMatrix.dotProduct = function (array1, array2) {\n  var product = 0;\n\n  for (var i = 0; i < array1.length; i++) {\n    product += array1[i] * array2[i];\n  }\n\n  return product;\n};\n\n/**\n * magnitude of an array\n * array is 1d array\n */\nMatrix.mag = function (array) {\n  return Math.sqrt(this.dotProduct(array, array));\n};\n\n/**\n * normalization of an array\n * array and result are 1d array\n */\nMatrix.normalize = function (array) {\n  var result = [];\n  var magnitude = this.mag(array);\n\n  for (var i = 0; i < array.length; i++) {\n    result[i] = array[i] / magnitude;\n  }\n\n  return result;\n};\n\n/**\n * multiply an array with centering matrix\n * array and result are 1d array\n */\nMatrix.multGamma = function (array) {\n  var result = [];\n  var sum = 0;\n\n  for (var i = 0; i < array.length; i++) {\n    sum += array[i];\n  }\n\n  sum *= -1 / array.length;\n\n  for (var _i = 0; _i < array.length; _i++) {\n    result[_i] = sum + array[_i];\n  }\n  return result;\n};\n\n/**\n * a special matrix multiplication\n * result = 0.5 * C * INV * C^T * array\n * array and result are 1d, C and INV are 2d arrays\n */\nMatrix.multL = function (array, C, INV) {\n  var result = [];\n  var temp1 = [];\n  var temp2 = [];\n\n  // multiply by C^T\n  for (var i = 0; i < C[0].length; i++) {\n    var sum = 0;\n    for (var j = 0; j < C.length; j++) {\n      sum += -0.5 * C[j][i] * array[j];\n    }\n    temp1[i] = sum;\n  }\n  // multiply the result by INV\n  for (var _i2 = 0; _i2 < INV.length; _i2++) {\n    var _sum = 0;\n    for (var _j = 0; _j < INV.length; _j++) {\n      _sum += INV[_i2][_j] * temp1[_j];\n    }\n    temp2[_i2] = _sum;\n  }\n  // multiply the result by C\n  for (var _i3 = 0; _i3 < C.length; _i3++) {\n    var _sum2 = 0;\n    for (var _j2 = 0; _j2 < C[0].length; _j2++) {\n      _sum2 += C[_i3][_j2] * temp2[_j2];\n    }\n    result[_i3] = _sum2;\n  }\n\n  return result;\n};\n\nmodule.exports = Matrix;\n\n/***/ }),\n/* 25 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n * A classic Quicksort algorithm with Hoare's partition\n * - Works also on LinkedList objects\n *\n * Copyright: i-Vis Research Group, Bilkent University, 2007 - present\n */\n\nvar LinkedList = __webpack_require__(11);\n\nvar Quicksort = function () {\n    function Quicksort(A, compareFunction) {\n        _classCallCheck(this, Quicksort);\n\n        if (compareFunction !== null || compareFunction !== undefined) this.compareFunction = this._defaultCompareFunction;\n\n        var length = void 0;\n        if (A instanceof LinkedList) length = A.size();else length = A.length;\n\n        this._quicksort(A, 0, length - 1);\n    }\n\n    _createClass(Quicksort, [{\n        key: '_quicksort',\n        value: function _quicksort(A, p, r) {\n            if (p < r) {\n                var q = this._partition(A, p, r);\n                this._quicksort(A, p, q);\n                this._quicksort(A, q + 1, r);\n            }\n        }\n    }, {\n        key: '_partition',\n        value: function _partition(A, p, r) {\n            var x = this._get(A, p);\n            var i = p;\n            var j = r;\n            while (true) {\n                while (this.compareFunction(x, this._get(A, j))) {\n                    j--;\n                }while (this.compareFunction(this._get(A, i), x)) {\n                    i++;\n                }if (i < j) {\n                    this._swap(A, i, j);\n                    i++;\n                    j--;\n                } else return j;\n            }\n        }\n    }, {\n        key: '_get',\n        value: function _get(object, index) {\n            if (object instanceof LinkedList) return object.get_object_at(index);else return object[index];\n        }\n    }, {\n        key: '_set',\n        value: function _set(object, index, value) {\n            if (object instanceof LinkedList) object.set_object_at(index, value);else object[index] = value;\n        }\n    }, {\n        key: '_swap',\n        value: function _swap(A, i, j) {\n            var temp = this._get(A, i);\n            this._set(A, i, this._get(A, j));\n            this._set(A, j, temp);\n        }\n    }, {\n        key: '_defaultCompareFunction',\n        value: function _defaultCompareFunction(a, b) {\n            return b > a;\n        }\n    }]);\n\n    return Quicksort;\n}();\n\nmodule.exports = Quicksort;\n\n/***/ }),\n/* 26 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n// Singular Value Decomposition implementation\nfunction SVD() {};\n\n/* Below singular value decomposition (svd) code including hypot function is adopted from https://github.com/dragonfly-ai/JamaJS\n   Some changes are applied to make the code compatible with the fcose code and to make it independent from Jama.\n   Input matrix is changed to a 2D array instead of Jama matrix. Matrix dimensions are taken according to 2D array instead of using Jama functions.\n   An object that includes singular value components is created for return. \n   The types of input parameters of the hypot function are removed. \n   let is used instead of var for the variable initialization.\n*/\n/*\n                               Apache License\n                           Version 2.0, January 2004\n                        http://www.apache.org/licenses/\n\n   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n   1. Definitions.\n\n      \"License\" shall mean the terms and conditions for use, reproduction,\n      and distribution as defined by Sections 1 through 9 of this document.\n\n      \"Licensor\" shall mean the copyright owner or entity authorized by\n      the copyright owner that is granting the License.\n\n      \"Legal Entity\" shall mean the union of the acting entity and all\n      other entities that control, are controlled by, or are under common\n      control with that entity. For the purposes of this definition,\n      \"control\" means (i) the power, direct or indirect, to cause the\n      direction or management of such entity, whether by contract or\n      otherwise, or (ii) ownership of fifty percent (50%) or more of the\n      outstanding shares, or (iii) beneficial ownership of such entity.\n\n      \"You\" (or \"Your\") shall mean an individual or Legal Entity\n      exercising permissions granted by this License.\n\n      \"Source\" form shall mean the preferred form for making modifications,\n      including but not limited to software source code, documentation\n      source, and configuration files.\n\n      \"Object\" form shall mean any form resulting from mechanical\n      transformation or translation of a Source form, including but\n      not limited to compiled object code, generated documentation,\n      and conversions to other media types.\n\n      \"Work\" shall mean the work of authorship, whether in Source or\n      Object form, made available under the License, as indicated by a\n      copyright notice that is included in or attached to the work\n      (an example is provided in the Appendix below).\n\n      \"Derivative Works\" shall mean any work, whether in Source or Object\n      form, that is based on (or derived from) the Work and for which the\n      editorial revisions, annotations, elaborations, or other modifications\n      represent, as a whole, an original work of authorship. For the purposes\n      of this License, Derivative Works shall not include works that remain\n      separable from, or merely link (or bind by name) to the interfaces of,\n      the Work and Derivative Works thereof.\n\n      \"Contribution\" shall mean any work of authorship, including\n      the original version of the Work and any modifications or additions\n      to that Work or Derivative Works thereof, that is intentionally\n      submitted to Licensor for inclusion in the Work by the copyright owner\n      or by an individual or Legal Entity authorized to submit on behalf of\n      the copyright owner. For the purposes of this definition, \"submitted\"\n      means any form of electronic, verbal, or written communication sent\n      to the Licensor or its representatives, including but not limited to\n      communication on electronic mailing lists, source code control systems,\n      and issue tracking systems that are managed by, or on behalf of, the\n      Licensor for the purpose of discussing and improving the Work, but\n      excluding communication that is conspicuously marked or otherwise\n      designated in writing by the copyright owner as \"Not a Contribution.\"\n\n      \"Contributor\" shall mean Licensor and any individual or Legal Entity\n      on behalf of whom a Contribution has been received by Licensor and\n      subsequently incorporated within the Work.\n\n   2. Grant of Copyright License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      copyright license to reproduce, prepare Derivative Works of,\n      publicly display, publicly perform, sublicense, and distribute the\n      Work and such Derivative Works in Source or Object form.\n\n   3. Grant of Patent License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      (except as stated in this section) patent license to make, have made,\n      use, offer to sell, sell, import, and otherwise transfer the Work,\n      where such license applies only to those patent claims licensable\n      by such Contributor that are necessarily infringed by their\n      Contribution(s) alone or by combination of their Contribution(s)\n      with the Work to which such Contribution(s) was submitted. If You\n      institute patent litigation against any entity (including a\n      cross-claim or counterclaim in a lawsuit) alleging that the Work\n      or a Contribution incorporated within the Work constitutes direct\n      or contributory patent infringement, then any patent licenses\n      granted to You under this License for that Work shall terminate\n      as of the date such litigation is filed.\n\n   4. Redistribution. You may reproduce and distribute copies of the\n      Work or Derivative Works thereof in any medium, with or without\n      modifications, and in Source or Object form, provided that You\n      meet the following conditions:\n\n      (a) You must give any other recipients of the Work or\n          Derivative Works a copy of this License; and\n\n      (b) You must cause any modified files to carry prominent notices\n          stating that You changed the files; and\n\n      (c) You must retain, in the Source form of any Derivative Works\n          that You distribute, all copyright, patent, trademark, and\n          attribution notices from the Source form of the Work,\n          excluding those notices that do not pertain to any part of\n          the Derivative Works; and\n\n      (d) If the Work includes a \"NOTICE\" text file as part of its\n          distribution, then any Derivative Works that You distribute must\n          include a readable copy of the attribution notices contained\n          within such NOTICE file, excluding those notices that do not\n          pertain to any part of the Derivative Works, in at least one\n          of the following places: within a NOTICE text file distributed\n          as part of the Derivative Works; within the Source form or\n          documentation, if provided along with the Derivative Works; or,\n          within a display generated by the Derivative Works, if and\n          wherever such third-party notices normally appear. The contents\n          of the NOTICE file are for informational purposes only and\n          do not modify the License. You may add Your own attribution\n          notices within Derivative Works that You distribute, alongside\n          or as an addendum to the NOTICE text from the Work, provided\n          that such additional attribution notices cannot be construed\n          as modifying the License.\n\n      You may add Your own copyright statement to Your modifications and\n      may provide additional or different license terms and conditions\n      for use, reproduction, or distribution of Your modifications, or\n      for any such Derivative Works as a whole, provided Your use,\n      reproduction, and distribution of the Work otherwise complies with\n      the conditions stated in this License.\n\n   5. Submission of Contributions. Unless You explicitly state otherwise,\n      any Contribution intentionally submitted for inclusion in the Work\n      by You to the Licensor shall be under the terms and conditions of\n      this License, without any additional terms or conditions.\n      Notwithstanding the above, nothing herein shall supersede or modify\n      the terms of any separate license agreement you may have executed\n      with Licensor regarding such Contributions.\n\n   6. Trademarks. This License does not grant permission to use the trade\n      names, trademarks, service marks, or product names of the Licensor,\n      except as required for reasonable and customary use in describing the\n      origin of the Work and reproducing the content of the NOTICE file.\n\n   7. Disclaimer of Warranty. Unless required by applicable law or\n      agreed to in writing, Licensor provides the Work (and each\n      Contributor provides its Contributions) on an \"AS IS\" BASIS,\n      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or\n      implied, including, without limitation, any warranties or conditions\n      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A\n      PARTICULAR PURPOSE. You are solely responsible for determining the\n      appropriateness of using or redistributing the Work and assume any\n      risks associated with Your exercise of permissions under this License.\n\n   8. Limitation of Liability. In no event and under no legal theory,\n      whether in tort (including negligence), contract, or otherwise,\n      unless required by applicable law (such as deliberate and grossly\n      negligent acts) or agreed to in writing, shall any Contributor be\n      liable to You for damages, including any direct, indirect, special,\n      incidental, or consequential damages of any character arising as a\n      result of this License or out of the use or inability to use the\n      Work (including but not limited to damages for loss of goodwill,\n      work stoppage, computer failure or malfunction, or any and all\n      other commercial damages or losses), even if such Contributor\n      has been advised of the possibility of such damages.\n\n   9. Accepting Warranty or Additional Liability. While redistributing\n      the Work or Derivative Works thereof, You may choose to offer,\n      and charge a fee for, acceptance of support, warranty, indemnity,\n      or other liability obligations and/or rights consistent with this\n      License. However, in accepting such obligations, You may act only\n      on Your own behalf and on Your sole responsibility, not on behalf\n      of any other Contributor, and only if You agree to indemnify,\n      defend, and hold each Contributor harmless for any liability\n      incurred by, or claims asserted against, such Contributor by reason\n      of your accepting any such warranty or additional liability.\n\n   END OF TERMS AND CONDITIONS\n\n   APPENDIX: How to apply the Apache License to your work.\n\n      To apply the Apache License to your work, attach the following\n      boilerplate notice, with the fields enclosed by brackets \"{}\"\n      replaced with your own identifying information. (Don't include\n      the brackets!)  The text should be enclosed in the appropriate\n      comment syntax for the file format. We also recommend that a\n      file or class name and description of purpose be included on the\n      same \"printed page\" as the copyright notice for easier\n      identification within third-party archives.\n\n   Copyright {yyyy} {name of copyright owner}\n\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n\n       http://www.apache.org/licenses/LICENSE-2.0\n\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.\n*/\n\nSVD.svd = function (A) {\n  this.U = null;\n  this.V = null;\n  this.s = null;\n  this.m = 0;\n  this.n = 0;\n  this.m = A.length;\n  this.n = A[0].length;\n  var nu = Math.min(this.m, this.n);\n  this.s = function (s) {\n    var a = [];\n    while (s-- > 0) {\n      a.push(0);\n    }return a;\n  }(Math.min(this.m + 1, this.n));\n  this.U = function (dims) {\n    var allocate = function allocate(dims) {\n      if (dims.length == 0) {\n        return 0;\n      } else {\n        var array = [];\n        for (var i = 0; i < dims[0]; i++) {\n          array.push(allocate(dims.slice(1)));\n        }\n        return array;\n      }\n    };\n    return allocate(dims);\n  }([this.m, nu]);\n  this.V = function (dims) {\n    var allocate = function allocate(dims) {\n      if (dims.length == 0) {\n        return 0;\n      } else {\n        var array = [];\n        for (var i = 0; i < dims[0]; i++) {\n          array.push(allocate(dims.slice(1)));\n        }\n        return array;\n      }\n    };\n    return allocate(dims);\n  }([this.n, this.n]);\n  var e = function (s) {\n    var a = [];\n    while (s-- > 0) {\n      a.push(0);\n    }return a;\n  }(this.n);\n  var work = function (s) {\n    var a = [];\n    while (s-- > 0) {\n      a.push(0);\n    }return a;\n  }(this.m);\n  var wantu = true;\n  var wantv = true;\n  var nct = Math.min(this.m - 1, this.n);\n  var nrt = Math.max(0, Math.min(this.n - 2, this.m));\n  for (var k = 0; k < Math.max(nct, nrt); k++) {\n    if (k < nct) {\n      this.s[k] = 0;\n      for (var i = k; i < this.m; i++) {\n        this.s[k] = SVD.hypot(this.s[k], A[i][k]);\n      }\n      ;\n      if (this.s[k] !== 0.0) {\n        if (A[k][k] < 0.0) {\n          this.s[k] = -this.s[k];\n        }\n        for (var _i = k; _i < this.m; _i++) {\n          A[_i][k] /= this.s[k];\n        }\n        ;\n        A[k][k] += 1.0;\n      }\n      this.s[k] = -this.s[k];\n    }\n    for (var j = k + 1; j < this.n; j++) {\n      if (function (lhs, rhs) {\n        return lhs && rhs;\n      }(k < nct, this.s[k] !== 0.0)) {\n        var t = 0;\n        for (var _i2 = k; _i2 < this.m; _i2++) {\n          t += A[_i2][k] * A[_i2][j];\n        }\n        ;\n        t = -t / A[k][k];\n        for (var _i3 = k; _i3 < this.m; _i3++) {\n          A[_i3][j] += t * A[_i3][k];\n        }\n        ;\n      }\n      e[j] = A[k][j];\n    }\n    ;\n    if (function (lhs, rhs) {\n      return lhs && rhs;\n    }(wantu, k < nct)) {\n      for (var _i4 = k; _i4 < this.m; _i4++) {\n        this.U[_i4][k] = A[_i4][k];\n      }\n      ;\n    }\n    if (k < nrt) {\n      e[k] = 0;\n      for (var _i5 = k + 1; _i5 < this.n; _i5++) {\n        e[k] = SVD.hypot(e[k], e[_i5]);\n      }\n      ;\n      if (e[k] !== 0.0) {\n        if (e[k + 1] < 0.0) {\n          e[k] = -e[k];\n        }\n        for (var _i6 = k + 1; _i6 < this.n; _i6++) {\n          e[_i6] /= e[k];\n        }\n        ;\n        e[k + 1] += 1.0;\n      }\n      e[k] = -e[k];\n      if (function (lhs, rhs) {\n        return lhs && rhs;\n      }(k + 1 < this.m, e[k] !== 0.0)) {\n        for (var _i7 = k + 1; _i7 < this.m; _i7++) {\n          work[_i7] = 0.0;\n        }\n        ;\n        for (var _j = k + 1; _j < this.n; _j++) {\n          for (var _i8 = k + 1; _i8 < this.m; _i8++) {\n            work[_i8] += e[_j] * A[_i8][_j];\n          }\n          ;\n        }\n        ;\n        for (var _j2 = k + 1; _j2 < this.n; _j2++) {\n          var _t = -e[_j2] / e[k + 1];\n          for (var _i9 = k + 1; _i9 < this.m; _i9++) {\n            A[_i9][_j2] += _t * work[_i9];\n          }\n          ;\n        }\n        ;\n      }\n      if (wantv) {\n        for (var _i10 = k + 1; _i10 < this.n; _i10++) {\n          this.V[_i10][k] = e[_i10];\n        };\n      }\n    }\n  };\n  var p = Math.min(this.n, this.m + 1);\n  if (nct < this.n) {\n    this.s[nct] = A[nct][nct];\n  }\n  if (this.m < p) {\n    this.s[p - 1] = 0.0;\n  }\n  if (nrt + 1 < p) {\n    e[nrt] = A[nrt][p - 1];\n  }\n  e[p - 1] = 0.0;\n  if (wantu) {\n    for (var _j3 = nct; _j3 < nu; _j3++) {\n      for (var _i11 = 0; _i11 < this.m; _i11++) {\n        this.U[_i11][_j3] = 0.0;\n      }\n      ;\n      this.U[_j3][_j3] = 1.0;\n    };\n    for (var _k = nct - 1; _k >= 0; _k--) {\n      if (this.s[_k] !== 0.0) {\n        for (var _j4 = _k + 1; _j4 < nu; _j4++) {\n          var _t2 = 0;\n          for (var _i12 = _k; _i12 < this.m; _i12++) {\n            _t2 += this.U[_i12][_k] * this.U[_i12][_j4];\n          };\n          _t2 = -_t2 / this.U[_k][_k];\n          for (var _i13 = _k; _i13 < this.m; _i13++) {\n            this.U[_i13][_j4] += _t2 * this.U[_i13][_k];\n          };\n        };\n        for (var _i14 = _k; _i14 < this.m; _i14++) {\n          this.U[_i14][_k] = -this.U[_i14][_k];\n        };\n        this.U[_k][_k] = 1.0 + this.U[_k][_k];\n        for (var _i15 = 0; _i15 < _k - 1; _i15++) {\n          this.U[_i15][_k] = 0.0;\n        };\n      } else {\n        for (var _i16 = 0; _i16 < this.m; _i16++) {\n          this.U[_i16][_k] = 0.0;\n        };\n        this.U[_k][_k] = 1.0;\n      }\n    };\n  }\n  if (wantv) {\n    for (var _k2 = this.n - 1; _k2 >= 0; _k2--) {\n      if (function (lhs, rhs) {\n        return lhs && rhs;\n      }(_k2 < nrt, e[_k2] !== 0.0)) {\n        for (var _j5 = _k2 + 1; _j5 < nu; _j5++) {\n          var _t3 = 0;\n          for (var _i17 = _k2 + 1; _i17 < this.n; _i17++) {\n            _t3 += this.V[_i17][_k2] * this.V[_i17][_j5];\n          };\n          _t3 = -_t3 / this.V[_k2 + 1][_k2];\n          for (var _i18 = _k2 + 1; _i18 < this.n; _i18++) {\n            this.V[_i18][_j5] += _t3 * this.V[_i18][_k2];\n          };\n        };\n      }\n      for (var _i19 = 0; _i19 < this.n; _i19++) {\n        this.V[_i19][_k2] = 0.0;\n      };\n      this.V[_k2][_k2] = 1.0;\n    };\n  }\n  var pp = p - 1;\n  var iter = 0;\n  var eps = Math.pow(2.0, -52.0);\n  var tiny = Math.pow(2.0, -966.0);\n  while (p > 0) {\n    var _k3 = void 0;\n    var kase = void 0;\n    for (_k3 = p - 2; _k3 >= -1; _k3--) {\n      if (_k3 === -1) {\n        break;\n      }\n      if (Math.abs(e[_k3]) <= tiny + eps * (Math.abs(this.s[_k3]) + Math.abs(this.s[_k3 + 1]))) {\n        e[_k3] = 0.0;\n        break;\n      }\n    };\n    if (_k3 === p - 2) {\n      kase = 4;\n    } else {\n      var ks = void 0;\n      for (ks = p - 1; ks >= _k3; ks--) {\n        if (ks === _k3) {\n          break;\n        }\n        var _t4 = (ks !== p ? Math.abs(e[ks]) : 0.0) + (ks !== _k3 + 1 ? Math.abs(e[ks - 1]) : 0.0);\n        if (Math.abs(this.s[ks]) <= tiny + eps * _t4) {\n          this.s[ks] = 0.0;\n          break;\n        }\n      };\n      if (ks === _k3) {\n        kase = 3;\n      } else if (ks === p - 1) {\n        kase = 1;\n      } else {\n        kase = 2;\n        _k3 = ks;\n      }\n    }\n    _k3++;\n    switch (kase) {\n      case 1:\n        {\n          var f = e[p - 2];\n          e[p - 2] = 0.0;\n          for (var _j6 = p - 2; _j6 >= _k3; _j6--) {\n            var _t5 = SVD.hypot(this.s[_j6], f);\n            var cs = this.s[_j6] / _t5;\n            var sn = f / _t5;\n            this.s[_j6] = _t5;\n            if (_j6 !== _k3) {\n              f = -sn * e[_j6 - 1];\n              e[_j6 - 1] = cs * e[_j6 - 1];\n            }\n            if (wantv) {\n              for (var _i20 = 0; _i20 < this.n; _i20++) {\n                _t5 = cs * this.V[_i20][_j6] + sn * this.V[_i20][p - 1];\n                this.V[_i20][p - 1] = -sn * this.V[_i20][_j6] + cs * this.V[_i20][p - 1];\n                this.V[_i20][_j6] = _t5;\n              };\n            }\n          };\n        };\n        break;\n      case 2:\n        {\n          var _f = e[_k3 - 1];\n          e[_k3 - 1] = 0.0;\n          for (var _j7 = _k3; _j7 < p; _j7++) {\n            var _t6 = SVD.hypot(this.s[_j7], _f);\n            var _cs = this.s[_j7] / _t6;\n            var _sn = _f / _t6;\n            this.s[_j7] = _t6;\n            _f = -_sn * e[_j7];\n            e[_j7] = _cs * e[_j7];\n            if (wantu) {\n              for (var _i21 = 0; _i21 < this.m; _i21++) {\n                _t6 = _cs * this.U[_i21][_j7] + _sn * this.U[_i21][_k3 - 1];\n                this.U[_i21][_k3 - 1] = -_sn * this.U[_i21][_j7] + _cs * this.U[_i21][_k3 - 1];\n                this.U[_i21][_j7] = _t6;\n              };\n            }\n          };\n        };\n        break;\n      case 3:\n        {\n          var scale = Math.max(Math.max(Math.max(Math.max(Math.abs(this.s[p - 1]), Math.abs(this.s[p - 2])), Math.abs(e[p - 2])), Math.abs(this.s[_k3])), Math.abs(e[_k3]));\n          var sp = this.s[p - 1] / scale;\n          var spm1 = this.s[p - 2] / scale;\n          var epm1 = e[p - 2] / scale;\n          var sk = this.s[_k3] / scale;\n          var ek = e[_k3] / scale;\n          var b = ((spm1 + sp) * (spm1 - sp) + epm1 * epm1) / 2.0;\n          var c = sp * epm1 * (sp * epm1);\n          var shift = 0.0;\n          if (function (lhs, rhs) {\n            return lhs || rhs;\n          }(b !== 0.0, c !== 0.0)) {\n            shift = Math.sqrt(b * b + c);\n            if (b < 0.0) {\n              shift = -shift;\n            }\n            shift = c / (b + shift);\n          }\n          var _f2 = (sk + sp) * (sk - sp) + shift;\n          var g = sk * ek;\n          for (var _j8 = _k3; _j8 < p - 1; _j8++) {\n            var _t7 = SVD.hypot(_f2, g);\n            var _cs2 = _f2 / _t7;\n            var _sn2 = g / _t7;\n            if (_j8 !== _k3) {\n              e[_j8 - 1] = _t7;\n            }\n            _f2 = _cs2 * this.s[_j8] + _sn2 * e[_j8];\n            e[_j8] = _cs2 * e[_j8] - _sn2 * this.s[_j8];\n            g = _sn2 * this.s[_j8 + 1];\n            this.s[_j8 + 1] = _cs2 * this.s[_j8 + 1];\n            if (wantv) {\n              for (var _i22 = 0; _i22 < this.n; _i22++) {\n                _t7 = _cs2 * this.V[_i22][_j8] + _sn2 * this.V[_i22][_j8 + 1];\n                this.V[_i22][_j8 + 1] = -_sn2 * this.V[_i22][_j8] + _cs2 * this.V[_i22][_j8 + 1];\n                this.V[_i22][_j8] = _t7;\n              };\n            }\n            _t7 = SVD.hypot(_f2, g);\n            _cs2 = _f2 / _t7;\n            _sn2 = g / _t7;\n            this.s[_j8] = _t7;\n            _f2 = _cs2 * e[_j8] + _sn2 * this.s[_j8 + 1];\n            this.s[_j8 + 1] = -_sn2 * e[_j8] + _cs2 * this.s[_j8 + 1];\n            g = _sn2 * e[_j8 + 1];\n            e[_j8 + 1] = _cs2 * e[_j8 + 1];\n            if (wantu && _j8 < this.m - 1) {\n              for (var _i23 = 0; _i23 < this.m; _i23++) {\n                _t7 = _cs2 * this.U[_i23][_j8] + _sn2 * this.U[_i23][_j8 + 1];\n                this.U[_i23][_j8 + 1] = -_sn2 * this.U[_i23][_j8] + _cs2 * this.U[_i23][_j8 + 1];\n                this.U[_i23][_j8] = _t7;\n              };\n            }\n          };\n          e[p - 2] = _f2;\n          iter = iter + 1;\n        };\n        break;\n      case 4:\n        {\n          if (this.s[_k3] <= 0.0) {\n            this.s[_k3] = this.s[_k3] < 0.0 ? -this.s[_k3] : 0.0;\n            if (wantv) {\n              for (var _i24 = 0; _i24 <= pp; _i24++) {\n                this.V[_i24][_k3] = -this.V[_i24][_k3];\n              };\n            }\n          }\n          while (_k3 < pp) {\n            if (this.s[_k3] >= this.s[_k3 + 1]) {\n              break;\n            }\n            var _t8 = this.s[_k3];\n            this.s[_k3] = this.s[_k3 + 1];\n            this.s[_k3 + 1] = _t8;\n            if (wantv && _k3 < this.n - 1) {\n              for (var _i25 = 0; _i25 < this.n; _i25++) {\n                _t8 = this.V[_i25][_k3 + 1];\n                this.V[_i25][_k3 + 1] = this.V[_i25][_k3];\n                this.V[_i25][_k3] = _t8;\n              };\n            }\n            if (wantu && _k3 < this.m - 1) {\n              for (var _i26 = 0; _i26 < this.m; _i26++) {\n                _t8 = this.U[_i26][_k3 + 1];\n                this.U[_i26][_k3 + 1] = this.U[_i26][_k3];\n                this.U[_i26][_k3] = _t8;\n              };\n            }\n            _k3++;\n          };\n          iter = 0;\n          p--;\n        };\n        break;\n    }\n  };\n  var result = { U: this.U, V: this.V, S: this.s };\n  return result;\n};\n\n// sqrt(a^2 + b^2) without under/overflow.\nSVD.hypot = function (a, b) {\n  var r = void 0;\n  if (Math.abs(a) > Math.abs(b)) {\n    r = b / a;\n    r = Math.abs(a) * Math.sqrt(1 + r * r);\n  } else if (b != 0) {\n    r = a / b;\n    r = Math.abs(b) * Math.sqrt(1 + r * r);\n  } else {\n    r = 0.0;\n  }\n  return r;\n};\n\nmodule.exports = SVD;\n\n/***/ }),\n/* 27 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n *   Needleman-Wunsch algorithm is an procedure to compute the optimal global alignment of two string\n *   sequences by S.B.Needleman and C.D.Wunsch (1970).\n *\n *   Aside from the inputs, you can assign the scores for,\n *   - Match: The two characters at the current index are same.\n *   - Mismatch: The two characters at the current index are different.\n *   - Insertion/Deletion(gaps): The best alignment involves one letter aligning to a gap in the other string.\n */\n\nvar NeedlemanWunsch = function () {\n    function NeedlemanWunsch(sequence1, sequence2) {\n        var match_score = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n        var mismatch_penalty = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : -1;\n        var gap_penalty = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : -1;\n\n        _classCallCheck(this, NeedlemanWunsch);\n\n        this.sequence1 = sequence1;\n        this.sequence2 = sequence2;\n        this.match_score = match_score;\n        this.mismatch_penalty = mismatch_penalty;\n        this.gap_penalty = gap_penalty;\n\n        // Just the remove redundancy\n        this.iMax = sequence1.length + 1;\n        this.jMax = sequence2.length + 1;\n\n        // Grid matrix of scores\n        this.grid = new Array(this.iMax);\n        for (var i = 0; i < this.iMax; i++) {\n            this.grid[i] = new Array(this.jMax);\n\n            for (var j = 0; j < this.jMax; j++) {\n                this.grid[i][j] = 0;\n            }\n        }\n\n        // Traceback matrix (2D array, each cell is an array of boolean values for [`Diag`, `Up`, `Left`] positions)\n        this.tracebackGrid = new Array(this.iMax);\n        for (var _i = 0; _i < this.iMax; _i++) {\n            this.tracebackGrid[_i] = new Array(this.jMax);\n\n            for (var _j = 0; _j < this.jMax; _j++) {\n                this.tracebackGrid[_i][_j] = [null, null, null];\n            }\n        }\n\n        // The aligned sequences (return multiple possibilities)\n        this.alignments = [];\n\n        // Final alignment score\n        this.score = -1;\n\n        // Calculate scores and tracebacks\n        this.computeGrids();\n    }\n\n    _createClass(NeedlemanWunsch, [{\n        key: \"getScore\",\n        value: function getScore() {\n            return this.score;\n        }\n    }, {\n        key: \"getAlignments\",\n        value: function getAlignments() {\n            return this.alignments;\n        }\n\n        // Main dynamic programming procedure\n\n    }, {\n        key: \"computeGrids\",\n        value: function computeGrids() {\n            // Fill in the first row\n            for (var j = 1; j < this.jMax; j++) {\n                this.grid[0][j] = this.grid[0][j - 1] + this.gap_penalty;\n                this.tracebackGrid[0][j] = [false, false, true];\n            }\n\n            // Fill in the first column\n            for (var i = 1; i < this.iMax; i++) {\n                this.grid[i][0] = this.grid[i - 1][0] + this.gap_penalty;\n                this.tracebackGrid[i][0] = [false, true, false];\n            }\n\n            // Fill the rest of the grid\n            for (var _i2 = 1; _i2 < this.iMax; _i2++) {\n                for (var _j2 = 1; _j2 < this.jMax; _j2++) {\n                    // Find the max score(s) among [`Diag`, `Up`, `Left`]\n                    var diag = void 0;\n                    if (this.sequence1[_i2 - 1] === this.sequence2[_j2 - 1]) diag = this.grid[_i2 - 1][_j2 - 1] + this.match_score;else diag = this.grid[_i2 - 1][_j2 - 1] + this.mismatch_penalty;\n\n                    var up = this.grid[_i2 - 1][_j2] + this.gap_penalty;\n                    var left = this.grid[_i2][_j2 - 1] + this.gap_penalty;\n\n                    // If there exists multiple max values, capture them for multiple paths\n                    var maxOf = [diag, up, left];\n                    var indices = this.arrayAllMaxIndexes(maxOf);\n\n                    // Update Grids\n                    this.grid[_i2][_j2] = maxOf[indices[0]];\n                    this.tracebackGrid[_i2][_j2] = [indices.includes(0), indices.includes(1), indices.includes(2)];\n                }\n            }\n\n            // Update alignment score\n            this.score = this.grid[this.iMax - 1][this.jMax - 1];\n        }\n\n        // Gets all possible valid sequence combinations\n\n    }, {\n        key: \"alignmentTraceback\",\n        value: function alignmentTraceback() {\n            var inProcessAlignments = [];\n\n            inProcessAlignments.push({ pos: [this.sequence1.length, this.sequence2.length],\n                seq1: \"\",\n                seq2: \"\"\n            });\n\n            while (inProcessAlignments[0]) {\n                var current = inProcessAlignments[0];\n                var directions = this.tracebackGrid[current.pos[0]][current.pos[1]];\n\n                if (directions[0]) {\n                    inProcessAlignments.push({ pos: [current.pos[0] - 1, current.pos[1] - 1],\n                        seq1: this.sequence1[current.pos[0] - 1] + current.seq1,\n                        seq2: this.sequence2[current.pos[1] - 1] + current.seq2\n                    });\n                }\n                if (directions[1]) {\n                    inProcessAlignments.push({ pos: [current.pos[0] - 1, current.pos[1]],\n                        seq1: this.sequence1[current.pos[0] - 1] + current.seq1,\n                        seq2: '-' + current.seq2\n                    });\n                }\n                if (directions[2]) {\n                    inProcessAlignments.push({ pos: [current.pos[0], current.pos[1] - 1],\n                        seq1: '-' + current.seq1,\n                        seq2: this.sequence2[current.pos[1] - 1] + current.seq2\n                    });\n                }\n\n                if (current.pos[0] === 0 && current.pos[1] === 0) this.alignments.push({ sequence1: current.seq1,\n                    sequence2: current.seq2\n                });\n\n                inProcessAlignments.shift();\n            }\n\n            return this.alignments;\n        }\n\n        // Helper Functions\n\n    }, {\n        key: \"getAllIndexes\",\n        value: function getAllIndexes(arr, val) {\n            var indexes = [],\n                i = -1;\n            while ((i = arr.indexOf(val, i + 1)) !== -1) {\n                indexes.push(i);\n            }\n            return indexes;\n        }\n    }, {\n        key: \"arrayAllMaxIndexes\",\n        value: function arrayAllMaxIndexes(array) {\n            return this.getAllIndexes(array, Math.max.apply(null, array));\n        }\n    }]);\n\n    return NeedlemanWunsch;\n}();\n\nmodule.exports = NeedlemanWunsch;\n\n/***/ }),\n/* 28 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar layoutBase = function layoutBase() {\n  return;\n};\n\nlayoutBase.FDLayout = __webpack_require__(18);\nlayoutBase.FDLayoutConstants = __webpack_require__(4);\nlayoutBase.FDLayoutEdge = __webpack_require__(19);\nlayoutBase.FDLayoutNode = __webpack_require__(20);\nlayoutBase.DimensionD = __webpack_require__(21);\nlayoutBase.HashMap = __webpack_require__(22);\nlayoutBase.HashSet = __webpack_require__(23);\nlayoutBase.IGeometry = __webpack_require__(8);\nlayoutBase.IMath = __webpack_require__(9);\nlayoutBase.Integer = __webpack_require__(10);\nlayoutBase.Point = __webpack_require__(12);\nlayoutBase.PointD = __webpack_require__(5);\nlayoutBase.RandomSeed = __webpack_require__(16);\nlayoutBase.RectangleD = __webpack_require__(13);\nlayoutBase.Transform = __webpack_require__(17);\nlayoutBase.UniqueIDGeneretor = __webpack_require__(14);\nlayoutBase.Quicksort = __webpack_require__(25);\nlayoutBase.LinkedList = __webpack_require__(11);\nlayoutBase.LGraphObject = __webpack_require__(2);\nlayoutBase.LGraph = __webpack_require__(6);\nlayoutBase.LEdge = __webpack_require__(1);\nlayoutBase.LGraphManager = __webpack_require__(7);\nlayoutBase.LNode = __webpack_require__(3);\nlayoutBase.Layout = __webpack_require__(15);\nlayoutBase.LayoutConstants = __webpack_require__(0);\nlayoutBase.NeedlemanWunsch = __webpack_require__(27);\nlayoutBase.Matrix = __webpack_require__(24);\nlayoutBase.SVD = __webpack_require__(26);\n\nmodule.exports = layoutBase;\n\n/***/ }),\n/* 29 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction Emitter() {\n  this.listeners = [];\n}\n\nvar p = Emitter.prototype;\n\np.addListener = function (event, callback) {\n  this.listeners.push({\n    event: event,\n    callback: callback\n  });\n};\n\np.removeListener = function (event, callback) {\n  for (var i = this.listeners.length; i >= 0; i--) {\n    var l = this.listeners[i];\n\n    if (l.event === event && l.callback === callback) {\n      this.listeners.splice(i, 1);\n    }\n  }\n};\n\np.emit = function (event, data) {\n  for (var i = 0; i < this.listeners.length; i++) {\n    var l = this.listeners[i];\n\n    if (event === l.event) {\n      l.callback(data);\n    }\n  }\n};\n\nmodule.exports = Emitter;\n\n/***/ })\n/******/ ]);\n});", "import {\n  getIconSVG,\n  registerIconPacks,\n  unknownIcon\n} from \"./chunk-H2D2JQ3I.mjs\";\nimport {\n  createText\n} from \"./chunk-C3MQ5ANM.mjs\";\nimport {\n  populateCommonDb\n} from \"./chunk-4BMEZGHF.mjs\";\nimport {\n  ImperativeState\n} from \"./chunk-XZIHB7SX.mjs\";\nimport \"./chunk-O4NI6UNU.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-7B677QYD.mjs\";\nimport {\n  __name,\n  clear,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n  setupGraphViewbox\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/architecture/architectureParser.ts\nimport { parse } from \"@mermaid-js/parser\";\n\n// src/diagrams/architecture/architectureTypes.ts\nvar ArchitectureDirectionName = {\n  L: \"left\",\n  R: \"right\",\n  T: \"top\",\n  B: \"bottom\"\n};\nvar ArchitectureDirectionArrow = {\n  L: /* @__PURE__ */ __name((scale) => `${scale},${scale / 2} 0,${scale} 0,0`, \"L\"),\n  R: /* @__PURE__ */ __name((scale) => `0,${scale / 2} ${scale},0 ${scale},${scale}`, \"R\"),\n  T: /* @__PURE__ */ __name((scale) => `0,0 ${scale},0 ${scale / 2},${scale}`, \"T\"),\n  B: /* @__PURE__ */ __name((scale) => `${scale / 2},0 ${scale},${scale} 0,${scale}`, \"B\")\n};\nvar ArchitectureDirectionArrowShift = {\n  L: /* @__PURE__ */ __name((orig, arrowSize) => orig - arrowSize + 2, \"L\"),\n  R: /* @__PURE__ */ __name((orig, _arrowSize) => orig - 2, \"R\"),\n  T: /* @__PURE__ */ __name((orig, arrowSize) => orig - arrowSize + 2, \"T\"),\n  B: /* @__PURE__ */ __name((orig, _arrowSize) => orig - 2, \"B\")\n};\nvar getOppositeArchitectureDirection = /* @__PURE__ */ __name(function(x) {\n  if (isArchitectureDirectionX(x)) {\n    return x === \"L\" ? \"R\" : \"L\";\n  } else {\n    return x === \"T\" ? \"B\" : \"T\";\n  }\n}, \"getOppositeArchitectureDirection\");\nvar isArchitectureDirection = /* @__PURE__ */ __name(function(x) {\n  const temp = x;\n  return temp === \"L\" || temp === \"R\" || temp === \"T\" || temp === \"B\";\n}, \"isArchitectureDirection\");\nvar isArchitectureDirectionX = /* @__PURE__ */ __name(function(x) {\n  const temp = x;\n  return temp === \"L\" || temp === \"R\";\n}, \"isArchitectureDirectionX\");\nvar isArchitectureDirectionY = /* @__PURE__ */ __name(function(x) {\n  const temp = x;\n  return temp === \"T\" || temp === \"B\";\n}, \"isArchitectureDirectionY\");\nvar isArchitectureDirectionXY = /* @__PURE__ */ __name(function(a, b) {\n  const aX_bY = isArchitectureDirectionX(a) && isArchitectureDirectionY(b);\n  const aY_bX = isArchitectureDirectionY(a) && isArchitectureDirectionX(b);\n  return aX_bY || aY_bX;\n}, \"isArchitectureDirectionXY\");\nvar isArchitecturePairXY = /* @__PURE__ */ __name(function(pair) {\n  const lhs = pair[0];\n  const rhs = pair[1];\n  const aX_bY = isArchitectureDirectionX(lhs) && isArchitectureDirectionY(rhs);\n  const aY_bX = isArchitectureDirectionY(lhs) && isArchitectureDirectionX(rhs);\n  return aX_bY || aY_bX;\n}, \"isArchitecturePairXY\");\nvar isValidArchitectureDirectionPair = /* @__PURE__ */ __name(function(x) {\n  return x !== \"LL\" && x !== \"RR\" && x !== \"TT\" && x !== \"BB\";\n}, \"isValidArchitectureDirectionPair\");\nvar getArchitectureDirectionPair = /* @__PURE__ */ __name(function(sourceDir, targetDir) {\n  const pair = `${sourceDir}${targetDir}`;\n  return isValidArchitectureDirectionPair(pair) ? pair : void 0;\n}, \"getArchitectureDirectionPair\");\nvar shiftPositionByArchitectureDirectionPair = /* @__PURE__ */ __name(function([x, y], pair) {\n  const lhs = pair[0];\n  const rhs = pair[1];\n  if (isArchitectureDirectionX(lhs)) {\n    if (isArchitectureDirectionY(rhs)) {\n      return [x + (lhs === \"L\" ? -1 : 1), y + (rhs === \"T\" ? 1 : -1)];\n    } else {\n      return [x + (lhs === \"L\" ? -1 : 1), y];\n    }\n  } else {\n    if (isArchitectureDirectionX(rhs)) {\n      return [x + (rhs === \"L\" ? 1 : -1), y + (lhs === \"T\" ? 1 : -1)];\n    } else {\n      return [x, y + (lhs === \"T\" ? 1 : -1)];\n    }\n  }\n}, \"shiftPositionByArchitectureDirectionPair\");\nvar getArchitectureDirectionXYFactors = /* @__PURE__ */ __name(function(pair) {\n  if (pair === \"LT\" || pair === \"TL\") {\n    return [1, 1];\n  } else if (pair === \"BL\" || pair === \"LB\") {\n    return [1, -1];\n  } else if (pair === \"BR\" || pair === \"RB\") {\n    return [-1, -1];\n  } else {\n    return [-1, 1];\n  }\n}, \"getArchitectureDirectionXYFactors\");\nvar getArchitectureDirectionAlignment = /* @__PURE__ */ __name(function(a, b) {\n  if (isArchitectureDirectionXY(a, b)) {\n    return \"bend\";\n  } else if (isArchitectureDirectionX(a)) {\n    return \"horizontal\";\n  }\n  return \"vertical\";\n}, \"getArchitectureDirectionAlignment\");\nvar isArchitectureService = /* @__PURE__ */ __name(function(x) {\n  const temp = x;\n  return temp.type === \"service\";\n}, \"isArchitectureService\");\nvar isArchitectureJunction = /* @__PURE__ */ __name(function(x) {\n  const temp = x;\n  return temp.type === \"junction\";\n}, \"isArchitectureJunction\");\nvar edgeData = /* @__PURE__ */ __name((edge) => {\n  return edge.data();\n}, \"edgeData\");\nvar nodeData = /* @__PURE__ */ __name((node) => {\n  return node.data();\n}, \"nodeData\");\n\n// src/diagrams/architecture/architectureDb.ts\nvar DEFAULT_ARCHITECTURE_CONFIG = defaultConfig_default.architecture;\nvar state = new ImperativeState(() => ({\n  nodes: {},\n  groups: {},\n  edges: [],\n  registeredIds: {},\n  config: DEFAULT_ARCHITECTURE_CONFIG,\n  dataStructures: void 0,\n  elements: {}\n}));\nvar clear2 = /* @__PURE__ */ __name(() => {\n  state.reset();\n  clear();\n}, \"clear\");\nvar addService = /* @__PURE__ */ __name(function({\n  id,\n  icon,\n  in: parent,\n  title,\n  iconText\n}) {\n  if (state.records.registeredIds[id] !== void 0) {\n    throw new Error(\n      `The service id [${id}] is already in use by another ${state.records.registeredIds[id]}`\n    );\n  }\n  if (parent !== void 0) {\n    if (id === parent) {\n      throw new Error(`The service [${id}] cannot be placed within itself`);\n    }\n    if (state.records.registeredIds[parent] === void 0) {\n      throw new Error(\n        `The service [${id}]'s parent does not exist. Please make sure the parent is created before this service`\n      );\n    }\n    if (state.records.registeredIds[parent] === \"node\") {\n      throw new Error(`The service [${id}]'s parent is not a group`);\n    }\n  }\n  state.records.registeredIds[id] = \"node\";\n  state.records.nodes[id] = {\n    id,\n    type: \"service\",\n    icon,\n    iconText,\n    title,\n    edges: [],\n    in: parent\n  };\n}, \"addService\");\nvar getServices = /* @__PURE__ */ __name(() => Object.values(state.records.nodes).filter(isArchitectureService), \"getServices\");\nvar addJunction = /* @__PURE__ */ __name(function({ id, in: parent }) {\n  state.records.registeredIds[id] = \"node\";\n  state.records.nodes[id] = {\n    id,\n    type: \"junction\",\n    edges: [],\n    in: parent\n  };\n}, \"addJunction\");\nvar getJunctions = /* @__PURE__ */ __name(() => Object.values(state.records.nodes).filter(isArchitectureJunction), \"getJunctions\");\nvar getNodes = /* @__PURE__ */ __name(() => Object.values(state.records.nodes), \"getNodes\");\nvar getNode = /* @__PURE__ */ __name((id) => state.records.nodes[id], \"getNode\");\nvar addGroup = /* @__PURE__ */ __name(function({ id, icon, in: parent, title }) {\n  if (state.records.registeredIds[id] !== void 0) {\n    throw new Error(\n      `The group id [${id}] is already in use by another ${state.records.registeredIds[id]}`\n    );\n  }\n  if (parent !== void 0) {\n    if (id === parent) {\n      throw new Error(`The group [${id}] cannot be placed within itself`);\n    }\n    if (state.records.registeredIds[parent] === void 0) {\n      throw new Error(\n        `The group [${id}]'s parent does not exist. Please make sure the parent is created before this group`\n      );\n    }\n    if (state.records.registeredIds[parent] === \"node\") {\n      throw new Error(`The group [${id}]'s parent is not a group`);\n    }\n  }\n  state.records.registeredIds[id] = \"group\";\n  state.records.groups[id] = {\n    id,\n    icon,\n    title,\n    in: parent\n  };\n}, \"addGroup\");\nvar getGroups = /* @__PURE__ */ __name(() => {\n  return Object.values(state.records.groups);\n}, \"getGroups\");\nvar addEdge = /* @__PURE__ */ __name(function({\n  lhsId,\n  rhsId,\n  lhsDir,\n  rhsDir,\n  lhsInto,\n  rhsInto,\n  lhsGroup,\n  rhsGroup,\n  title\n}) {\n  if (!isArchitectureDirection(lhsDir)) {\n    throw new Error(\n      `Invalid direction given for left hand side of edge ${lhsId}--${rhsId}. Expected (L,R,T,B) got ${lhsDir}`\n    );\n  }\n  if (!isArchitectureDirection(rhsDir)) {\n    throw new Error(\n      `Invalid direction given for right hand side of edge ${lhsId}--${rhsId}. Expected (L,R,T,B) got ${rhsDir}`\n    );\n  }\n  if (state.records.nodes[lhsId] === void 0 && state.records.groups[lhsId] === void 0) {\n    throw new Error(\n      `The left-hand id [${lhsId}] does not yet exist. Please create the service/group before declaring an edge to it.`\n    );\n  }\n  if (state.records.nodes[rhsId] === void 0 && state.records.groups[lhsId] === void 0) {\n    throw new Error(\n      `The right-hand id [${rhsId}] does not yet exist. Please create the service/group before declaring an edge to it.`\n    );\n  }\n  const lhsGroupId = state.records.nodes[lhsId].in;\n  const rhsGroupId = state.records.nodes[rhsId].in;\n  if (lhsGroup && lhsGroupId && rhsGroupId && lhsGroupId == rhsGroupId) {\n    throw new Error(\n      `The left-hand id [${lhsId}] is modified to traverse the group boundary, but the edge does not pass through two groups.`\n    );\n  }\n  if (rhsGroup && lhsGroupId && rhsGroupId && lhsGroupId == rhsGroupId) {\n    throw new Error(\n      `The right-hand id [${rhsId}] is modified to traverse the group boundary, but the edge does not pass through two groups.`\n    );\n  }\n  const edge = {\n    lhsId,\n    lhsDir,\n    lhsInto,\n    lhsGroup,\n    rhsId,\n    rhsDir,\n    rhsInto,\n    rhsGroup,\n    title\n  };\n  state.records.edges.push(edge);\n  if (state.records.nodes[lhsId] && state.records.nodes[rhsId]) {\n    state.records.nodes[lhsId].edges.push(state.records.edges[state.records.edges.length - 1]);\n    state.records.nodes[rhsId].edges.push(state.records.edges[state.records.edges.length - 1]);\n  }\n}, \"addEdge\");\nvar getEdges = /* @__PURE__ */ __name(() => state.records.edges, \"getEdges\");\nvar getDataStructures = /* @__PURE__ */ __name(() => {\n  if (state.records.dataStructures === void 0) {\n    const groupAlignments = {};\n    const adjList = Object.entries(state.records.nodes).reduce((prevOuter, [id, service]) => {\n      prevOuter[id] = service.edges.reduce((prevInner, edge) => {\n        const lhsGroupId = getNode(edge.lhsId)?.in;\n        const rhsGroupId = getNode(edge.rhsId)?.in;\n        if (lhsGroupId && rhsGroupId && lhsGroupId !== rhsGroupId) {\n          const alignment = getArchitectureDirectionAlignment(edge.lhsDir, edge.rhsDir);\n          if (alignment !== \"bend\") {\n            groupAlignments[lhsGroupId] ??= {};\n            groupAlignments[lhsGroupId][rhsGroupId] = alignment;\n            groupAlignments[rhsGroupId] ??= {};\n            groupAlignments[rhsGroupId][lhsGroupId] = alignment;\n          }\n        }\n        if (edge.lhsId === id) {\n          const pair = getArchitectureDirectionPair(edge.lhsDir, edge.rhsDir);\n          if (pair) {\n            prevInner[pair] = edge.rhsId;\n          }\n        } else {\n          const pair = getArchitectureDirectionPair(edge.rhsDir, edge.lhsDir);\n          if (pair) {\n            prevInner[pair] = edge.lhsId;\n          }\n        }\n        return prevInner;\n      }, {});\n      return prevOuter;\n    }, {});\n    const firstId = Object.keys(adjList)[0];\n    const visited = { [firstId]: 1 };\n    const notVisited = Object.keys(adjList).reduce(\n      (prev, id) => id === firstId ? prev : { ...prev, [id]: 1 },\n      {}\n    );\n    const BFS = /* @__PURE__ */ __name((startingId) => {\n      const spatialMap = { [startingId]: [0, 0] };\n      const queue = [startingId];\n      while (queue.length > 0) {\n        const id = queue.shift();\n        if (id) {\n          visited[id] = 1;\n          delete notVisited[id];\n          const adj = adjList[id];\n          const [posX, posY] = spatialMap[id];\n          Object.entries(adj).forEach(([dir, rhsId]) => {\n            if (!visited[rhsId]) {\n              spatialMap[rhsId] = shiftPositionByArchitectureDirectionPair(\n                [posX, posY],\n                dir\n              );\n              queue.push(rhsId);\n            }\n          });\n        }\n      }\n      return spatialMap;\n    }, \"BFS\");\n    const spatialMaps = [BFS(firstId)];\n    while (Object.keys(notVisited).length > 0) {\n      spatialMaps.push(BFS(Object.keys(notVisited)[0]));\n    }\n    state.records.dataStructures = {\n      adjList,\n      spatialMaps,\n      groupAlignments\n    };\n  }\n  return state.records.dataStructures;\n}, \"getDataStructures\");\nvar setElementForId = /* @__PURE__ */ __name((id, element) => {\n  state.records.elements[id] = element;\n}, \"setElementForId\");\nvar getElementById = /* @__PURE__ */ __name((id) => state.records.elements[id], \"getElementById\");\nvar db = {\n  clear: clear2,\n  setDiagramTitle,\n  getDiagramTitle,\n  setAccTitle,\n  getAccTitle,\n  setAccDescription,\n  getAccDescription,\n  addService,\n  getServices,\n  addJunction,\n  getJunctions,\n  getNodes,\n  getNode,\n  addGroup,\n  getGroups,\n  addEdge,\n  getEdges,\n  setElementForId,\n  getElementById,\n  getDataStructures\n};\nfunction getConfigField(field) {\n  const arch = getConfig().architecture;\n  if (arch?.[field]) {\n    return arch[field];\n  }\n  return DEFAULT_ARCHITECTURE_CONFIG[field];\n}\n__name(getConfigField, \"getConfigField\");\n\n// src/diagrams/architecture/architectureParser.ts\nvar populateDb = /* @__PURE__ */ __name((ast, db2) => {\n  populateCommonDb(ast, db2);\n  ast.groups.map(db2.addGroup);\n  ast.services.map((service) => db2.addService({ ...service, type: \"service\" }));\n  ast.junctions.map((service) => db2.addJunction({ ...service, type: \"junction\" }));\n  ast.edges.map(db2.addEdge);\n}, \"populateDb\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"architecture\", input);\n    log.debug(ast);\n    populateDb(ast, db);\n  }, \"parse\")\n};\n\n// src/diagrams/architecture/architectureStyles.ts\nvar getStyles = /* @__PURE__ */ __name((options) => `\n  .edge {\n    stroke-width: ${options.archEdgeWidth};\n    stroke: ${options.archEdgeColor};\n    fill: none;\n  }\n\n  .arrow {\n    fill: ${options.archEdgeArrowColor};\n  }\n\n  .node-bkg {\n    fill: none;\n    stroke: ${options.archGroupBorderColor};\n    stroke-width: ${options.archGroupBorderWidth};\n    stroke-dasharray: 8;\n  }\n  .node-icon-text {\n    display: flex; \n    align-items: center;\n  }\n  \n  .node-icon-text > div {\n    color: #fff;\n    margin: 1px;\n    height: fit-content;\n    text-align: center;\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n  }\n`, \"getStyles\");\nvar architectureStyles_default = getStyles;\n\n// src/diagrams/architecture/architectureRenderer.ts\nimport cytoscape from \"cytoscape\";\nimport fcose from \"cytoscape-fcose\";\nimport { select } from \"d3\";\n\n// src/diagrams/architecture/architectureIcons.ts\nvar wrapIcon = /* @__PURE__ */ __name((icon) => {\n  return `<g><rect width=\"80\" height=\"80\" style=\"fill: #087ebf; stroke-width: 0px;\"/>${icon}</g>`;\n}, \"wrapIcon\");\nvar architectureIcons = {\n  prefix: \"mermaid-architecture\",\n  height: 80,\n  width: 80,\n  icons: {\n    database: {\n      body: wrapIcon(\n        '<path id=\"b\" data-name=\"4\" d=\"m20,57.86c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><path id=\"c\" data-name=\"3\" d=\"m20,45.95c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><path id=\"d\" data-name=\"2\" d=\"m20,34.05c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><ellipse id=\"e\" data-name=\"1\" cx=\"40\" cy=\"22.14\" rx=\"20\" ry=\"7.14\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><line x1=\"20\" y1=\"57.86\" x2=\"20\" y2=\"22.14\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><line x1=\"60\" y1=\"57.86\" x2=\"60\" y2=\"22.14\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/>'\n      )\n    },\n    server: {\n      body: wrapIcon(\n        '<rect x=\"17.5\" y=\"17.5\" width=\"45\" height=\"45\" rx=\"2\" ry=\"2\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><line x1=\"17.5\" y1=\"32.5\" x2=\"62.5\" y2=\"32.5\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><line x1=\"17.5\" y1=\"47.5\" x2=\"62.5\" y2=\"47.5\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><g><path d=\"m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z\" style=\"fill: #fff; stroke-width: 0px;\"/><path d=\"m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10;\"/></g><g><path d=\"m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z\" style=\"fill: #fff; stroke-width: 0px;\"/><path d=\"m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10;\"/></g><g><path d=\"m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z\" style=\"fill: #fff; stroke-width: 0px;\"/><path d=\"m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10;\"/></g><g><circle cx=\"32.5\" cy=\"25\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/><circle cx=\"27.5\" cy=\"25\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/><circle cx=\"22.5\" cy=\"25\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/></g><g><circle cx=\"32.5\" cy=\"40\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/><circle cx=\"27.5\" cy=\"40\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/><circle cx=\"22.5\" cy=\"40\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/></g><g><circle cx=\"32.5\" cy=\"55\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/><circle cx=\"27.5\" cy=\"55\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/><circle cx=\"22.5\" cy=\"55\" r=\".75\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10;\"/></g>'\n      )\n    },\n    disk: {\n      body: wrapIcon(\n        '<rect x=\"20\" y=\"15\" width=\"40\" height=\"50\" rx=\"1\" ry=\"1\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><ellipse cx=\"24\" cy=\"19.17\" rx=\".8\" ry=\".83\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><ellipse cx=\"56\" cy=\"19.17\" rx=\".8\" ry=\".83\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><ellipse cx=\"24\" cy=\"60.83\" rx=\".8\" ry=\".83\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><ellipse cx=\"56\" cy=\"60.83\" rx=\".8\" ry=\".83\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><ellipse cx=\"40\" cy=\"33.75\" rx=\"14\" ry=\"14.58\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><ellipse cx=\"40\" cy=\"33.75\" rx=\"4\" ry=\"4.17\" style=\"fill: #fff; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><path d=\"m37.51,42.52l-4.83,13.22c-.26.71-1.1,1.02-1.76.64l-4.18-2.42c-.66-.38-.81-1.26-.33-1.84l9.01-10.8c.88-1.05,2.56-.08,2.09,1.2Z\" style=\"fill: #fff; stroke-width: 0px;\"/>'\n      )\n    },\n    internet: {\n      body: wrapIcon(\n        '<circle cx=\"40\" cy=\"40\" r=\"22.5\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><line x1=\"40\" y1=\"17.5\" x2=\"40\" y2=\"62.5\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><line x1=\"17.5\" y1=\"40\" x2=\"62.5\" y2=\"40\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><path d=\"m39.99,17.51c-15.28,11.1-15.28,33.88,0,44.98\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><path d=\"m40.01,17.51c15.28,11.1,15.28,33.88,0,44.98\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><line x1=\"19.75\" y1=\"30.1\" x2=\"60.25\" y2=\"30.1\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/><line x1=\"19.75\" y1=\"49.9\" x2=\"60.25\" y2=\"49.9\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/>'\n      )\n    },\n    cloud: {\n      body: wrapIcon(\n        '<path d=\"m65,47.5c0,2.76-2.24,5-5,5H20c-2.76,0-5-2.24-5-5,0-1.87,1.03-3.51,2.56-4.36-.04-.21-.06-.42-.06-.64,0-2.6,2.48-4.74,5.65-4.97,1.65-4.51,6.34-7.76,11.85-7.76.86,0,1.69.08,2.5.23,2.09-1.57,4.69-2.5,7.5-2.5,6.1,0,11.19,4.38,12.28,10.17,2.14.56,3.72,2.51,3.72,4.83,0,.03,0,.07-.01.1,2.29.46,4.01,2.48,4.01,4.9Z\" style=\"fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;\"/>'\n      )\n    },\n    unknown: unknownIcon,\n    blank: {\n      body: wrapIcon(\"\")\n    }\n  }\n};\n\n// src/diagrams/architecture/svgDraw.ts\nvar drawEdges = /* @__PURE__ */ __name(async function(edgesEl, cy) {\n  const padding = getConfigField(\"padding\");\n  const iconSize = getConfigField(\"iconSize\");\n  const halfIconSize = iconSize / 2;\n  const arrowSize = iconSize / 6;\n  const halfArrowSize = arrowSize / 2;\n  await Promise.all(\n    cy.edges().map(async (edge) => {\n      const {\n        source,\n        sourceDir,\n        sourceArrow,\n        sourceGroup,\n        target,\n        targetDir,\n        targetArrow,\n        targetGroup,\n        label\n      } = edgeData(edge);\n      let { x: startX, y: startY } = edge[0].sourceEndpoint();\n      const { x: midX, y: midY } = edge[0].midpoint();\n      let { x: endX, y: endY } = edge[0].targetEndpoint();\n      const groupEdgeShift = padding + 4;\n      if (sourceGroup) {\n        if (isArchitectureDirectionX(sourceDir)) {\n          startX += sourceDir === \"L\" ? -groupEdgeShift : groupEdgeShift;\n        } else {\n          startY += sourceDir === \"T\" ? -groupEdgeShift : groupEdgeShift + 18;\n        }\n      }\n      if (targetGroup) {\n        if (isArchitectureDirectionX(targetDir)) {\n          endX += targetDir === \"L\" ? -groupEdgeShift : groupEdgeShift;\n        } else {\n          endY += targetDir === \"T\" ? -groupEdgeShift : groupEdgeShift + 18;\n        }\n      }\n      if (!sourceGroup && db.getNode(source)?.type === \"junction\") {\n        if (isArchitectureDirectionX(sourceDir)) {\n          startX += sourceDir === \"L\" ? halfIconSize : -halfIconSize;\n        } else {\n          startY += sourceDir === \"T\" ? halfIconSize : -halfIconSize;\n        }\n      }\n      if (!targetGroup && db.getNode(target)?.type === \"junction\") {\n        if (isArchitectureDirectionX(targetDir)) {\n          endX += targetDir === \"L\" ? halfIconSize : -halfIconSize;\n        } else {\n          endY += targetDir === \"T\" ? halfIconSize : -halfIconSize;\n        }\n      }\n      if (edge[0]._private.rscratch) {\n        const g = edgesEl.insert(\"g\");\n        g.insert(\"path\").attr(\"d\", `M ${startX},${startY} L ${midX},${midY} L${endX},${endY} `).attr(\"class\", \"edge\");\n        if (sourceArrow) {\n          const xShift = isArchitectureDirectionX(sourceDir) ? ArchitectureDirectionArrowShift[sourceDir](startX, arrowSize) : startX - halfArrowSize;\n          const yShift = isArchitectureDirectionY(sourceDir) ? ArchitectureDirectionArrowShift[sourceDir](startY, arrowSize) : startY - halfArrowSize;\n          g.insert(\"polygon\").attr(\"points\", ArchitectureDirectionArrow[sourceDir](arrowSize)).attr(\"transform\", `translate(${xShift},${yShift})`).attr(\"class\", \"arrow\");\n        }\n        if (targetArrow) {\n          const xShift = isArchitectureDirectionX(targetDir) ? ArchitectureDirectionArrowShift[targetDir](endX, arrowSize) : endX - halfArrowSize;\n          const yShift = isArchitectureDirectionY(targetDir) ? ArchitectureDirectionArrowShift[targetDir](endY, arrowSize) : endY - halfArrowSize;\n          g.insert(\"polygon\").attr(\"points\", ArchitectureDirectionArrow[targetDir](arrowSize)).attr(\"transform\", `translate(${xShift},${yShift})`).attr(\"class\", \"arrow\");\n        }\n        if (label) {\n          const axis = !isArchitectureDirectionXY(sourceDir, targetDir) ? isArchitectureDirectionX(sourceDir) ? \"X\" : \"Y\" : \"XY\";\n          let width = 0;\n          if (axis === \"X\") {\n            width = Math.abs(startX - endX);\n          } else if (axis === \"Y\") {\n            width = Math.abs(startY - endY) / 1.5;\n          } else {\n            width = Math.abs(startX - endX) / 2;\n          }\n          const textElem = g.append(\"g\");\n          await createText(\n            textElem,\n            label,\n            {\n              useHtmlLabels: false,\n              width,\n              classes: \"architecture-service-label\"\n            },\n            getConfig()\n          );\n          textElem.attr(\"dy\", \"1em\").attr(\"alignment-baseline\", \"middle\").attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\");\n          if (axis === \"X\") {\n            textElem.attr(\"transform\", \"translate(\" + midX + \", \" + midY + \")\");\n          } else if (axis === \"Y\") {\n            textElem.attr(\"transform\", \"translate(\" + midX + \", \" + midY + \") rotate(-90)\");\n          } else if (axis === \"XY\") {\n            const pair = getArchitectureDirectionPair(sourceDir, targetDir);\n            if (pair && isArchitecturePairXY(pair)) {\n              const bboxOrig = textElem.node().getBoundingClientRect();\n              const [x, y] = getArchitectureDirectionXYFactors(pair);\n              textElem.attr(\"dominant-baseline\", \"auto\").attr(\"transform\", `rotate(${-1 * x * y * 45})`);\n              const bboxNew = textElem.node().getBoundingClientRect();\n              textElem.attr(\n                \"transform\",\n                `\n                translate(${midX}, ${midY - bboxOrig.height / 2})\n                translate(${x * bboxNew.width / 2}, ${y * bboxNew.height / 2})\n                rotate(${-1 * x * y * 45}, 0, ${bboxOrig.height / 2})\n              `\n              );\n            }\n          }\n        }\n      }\n    })\n  );\n}, \"drawEdges\");\nvar drawGroups = /* @__PURE__ */ __name(async function(groupsEl, cy) {\n  const padding = getConfigField(\"padding\");\n  const groupIconSize = padding * 0.75;\n  const fontSize = getConfigField(\"fontSize\");\n  const iconSize = getConfigField(\"iconSize\");\n  const halfIconSize = iconSize / 2;\n  await Promise.all(\n    cy.nodes().map(async (node) => {\n      const data = nodeData(node);\n      if (data.type === \"group\") {\n        const { h, w, x1, y1 } = node.boundingBox();\n        groupsEl.append(\"rect\").attr(\"x\", x1 + halfIconSize).attr(\"y\", y1 + halfIconSize).attr(\"width\", w).attr(\"height\", h).attr(\"class\", \"node-bkg\");\n        const groupLabelContainer = groupsEl.append(\"g\");\n        let shiftedX1 = x1;\n        let shiftedY1 = y1;\n        if (data.icon) {\n          const bkgElem = groupLabelContainer.append(\"g\");\n          bkgElem.html(\n            `<g>${await getIconSVG(data.icon, { height: groupIconSize, width: groupIconSize, fallbackPrefix: architectureIcons.prefix })}</g>`\n          );\n          bkgElem.attr(\n            \"transform\",\n            \"translate(\" + (shiftedX1 + halfIconSize + 1) + \", \" + (shiftedY1 + halfIconSize + 1) + \")\"\n          );\n          shiftedX1 += groupIconSize;\n          shiftedY1 += fontSize / 2 - 1 - 2;\n        }\n        if (data.label) {\n          const textElem = groupLabelContainer.append(\"g\");\n          await createText(\n            textElem,\n            data.label,\n            {\n              useHtmlLabels: false,\n              width: w,\n              classes: \"architecture-service-label\"\n            },\n            getConfig()\n          );\n          textElem.attr(\"dy\", \"1em\").attr(\"alignment-baseline\", \"middle\").attr(\"dominant-baseline\", \"start\").attr(\"text-anchor\", \"start\");\n          textElem.attr(\n            \"transform\",\n            \"translate(\" + (shiftedX1 + halfIconSize + 4) + \", \" + (shiftedY1 + halfIconSize + 2) + \")\"\n          );\n        }\n      }\n    })\n  );\n}, \"drawGroups\");\nvar drawServices = /* @__PURE__ */ __name(async function(db2, elem, services) {\n  for (const service of services) {\n    const serviceElem = elem.append(\"g\");\n    const iconSize = getConfigField(\"iconSize\");\n    if (service.title) {\n      const textElem = serviceElem.append(\"g\");\n      await createText(\n        textElem,\n        service.title,\n        {\n          useHtmlLabels: false,\n          width: iconSize * 1.5,\n          classes: \"architecture-service-label\"\n        },\n        getConfig()\n      );\n      textElem.attr(\"dy\", \"1em\").attr(\"alignment-baseline\", \"middle\").attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\");\n      textElem.attr(\"transform\", \"translate(\" + iconSize / 2 + \", \" + iconSize + \")\");\n    }\n    const bkgElem = serviceElem.append(\"g\");\n    if (service.icon) {\n      bkgElem.html(\n        `<g>${await getIconSVG(service.icon, { height: iconSize, width: iconSize, fallbackPrefix: architectureIcons.prefix })}</g>`\n      );\n    } else if (service.iconText) {\n      bkgElem.html(\n        `<g>${await getIconSVG(\"blank\", { height: iconSize, width: iconSize, fallbackPrefix: architectureIcons.prefix })}</g>`\n      );\n      const textElemContainer = bkgElem.append(\"g\");\n      const fo = textElemContainer.append(\"foreignObject\").attr(\"width\", iconSize).attr(\"height\", iconSize);\n      const divElem = fo.append(\"div\").attr(\"class\", \"node-icon-text\").attr(\"style\", `height: ${iconSize}px;`).append(\"div\").html(service.iconText);\n      const fontSize = parseInt(\n        window.getComputedStyle(divElem.node(), null).getPropertyValue(\"font-size\").replace(/\\D/g, \"\")\n      ) ?? 16;\n      divElem.attr(\"style\", `-webkit-line-clamp: ${Math.floor((iconSize - 2) / fontSize)};`);\n    } else {\n      bkgElem.append(\"path\").attr(\"class\", \"node-bkg\").attr(\"id\", \"node-\" + service.id).attr(\n        \"d\",\n        `M0 ${iconSize} v${-iconSize} q0,-5 5,-5 h${iconSize} q5,0 5,5 v${iconSize} H0 Z`\n      );\n    }\n    serviceElem.attr(\"class\", \"architecture-service\");\n    const { width, height } = serviceElem._groups[0][0].getBBox();\n    service.width = width;\n    service.height = height;\n    db2.setElementForId(service.id, serviceElem);\n  }\n  return 0;\n}, \"drawServices\");\nvar drawJunctions = /* @__PURE__ */ __name(function(db2, elem, junctions) {\n  junctions.forEach((junction) => {\n    const junctionElem = elem.append(\"g\");\n    const iconSize = getConfigField(\"iconSize\");\n    const bkgElem = junctionElem.append(\"g\");\n    bkgElem.append(\"rect\").attr(\"id\", \"node-\" + junction.id).attr(\"fill-opacity\", \"0\").attr(\"width\", iconSize).attr(\"height\", iconSize);\n    junctionElem.attr(\"class\", \"architecture-junction\");\n    const { width, height } = junctionElem._groups[0][0].getBBox();\n    junctionElem.width = width;\n    junctionElem.height = height;\n    db2.setElementForId(junction.id, junctionElem);\n  });\n}, \"drawJunctions\");\n\n// src/diagrams/architecture/architectureRenderer.ts\nregisterIconPacks([\n  {\n    name: architectureIcons.prefix,\n    icons: architectureIcons\n  }\n]);\ncytoscape.use(fcose);\nfunction addServices(services, cy) {\n  services.forEach((service) => {\n    cy.add({\n      group: \"nodes\",\n      data: {\n        type: \"service\",\n        id: service.id,\n        icon: service.icon,\n        label: service.title,\n        parent: service.in,\n        width: getConfigField(\"iconSize\"),\n        height: getConfigField(\"iconSize\")\n      },\n      classes: \"node-service\"\n    });\n  });\n}\n__name(addServices, \"addServices\");\nfunction addJunctions(junctions, cy) {\n  junctions.forEach((junction) => {\n    cy.add({\n      group: \"nodes\",\n      data: {\n        type: \"junction\",\n        id: junction.id,\n        parent: junction.in,\n        width: getConfigField(\"iconSize\"),\n        height: getConfigField(\"iconSize\")\n      },\n      classes: \"node-junction\"\n    });\n  });\n}\n__name(addJunctions, \"addJunctions\");\nfunction positionNodes(db2, cy) {\n  cy.nodes().map((node) => {\n    const data = nodeData(node);\n    if (data.type === \"group\") {\n      return;\n    }\n    data.x = node.position().x;\n    data.y = node.position().y;\n    const nodeElem = db2.getElementById(data.id);\n    nodeElem.attr(\"transform\", \"translate(\" + (data.x || 0) + \",\" + (data.y || 0) + \")\");\n  });\n}\n__name(positionNodes, \"positionNodes\");\nfunction addGroups(groups, cy) {\n  groups.forEach((group) => {\n    cy.add({\n      group: \"nodes\",\n      data: {\n        type: \"group\",\n        id: group.id,\n        icon: group.icon,\n        label: group.title,\n        parent: group.in\n      },\n      classes: \"node-group\"\n    });\n  });\n}\n__name(addGroups, \"addGroups\");\nfunction addEdges(edges, cy) {\n  edges.forEach((parsedEdge) => {\n    const { lhsId, rhsId, lhsInto, lhsGroup, rhsInto, lhsDir, rhsDir, rhsGroup, title } = parsedEdge;\n    const edgeType = isArchitectureDirectionXY(parsedEdge.lhsDir, parsedEdge.rhsDir) ? \"segments\" : \"straight\";\n    const edge = {\n      id: `${lhsId}-${rhsId}`,\n      label: title,\n      source: lhsId,\n      sourceDir: lhsDir,\n      sourceArrow: lhsInto,\n      sourceGroup: lhsGroup,\n      sourceEndpoint: lhsDir === \"L\" ? \"0 50%\" : lhsDir === \"R\" ? \"100% 50%\" : lhsDir === \"T\" ? \"50% 0\" : \"50% 100%\",\n      target: rhsId,\n      targetDir: rhsDir,\n      targetArrow: rhsInto,\n      targetGroup: rhsGroup,\n      targetEndpoint: rhsDir === \"L\" ? \"0 50%\" : rhsDir === \"R\" ? \"100% 50%\" : rhsDir === \"T\" ? \"50% 0\" : \"50% 100%\"\n    };\n    cy.add({\n      group: \"edges\",\n      data: edge,\n      classes: edgeType\n    });\n  });\n}\n__name(addEdges, \"addEdges\");\nfunction getAlignments(db2, spatialMaps, groupAlignments) {\n  const flattenAlignments = /* @__PURE__ */ __name((alignmentObj, alignmentDir) => {\n    return Object.entries(alignmentObj).reduce(\n      (prev, [dir, alignments2]) => {\n        let cnt = 0;\n        const arr = Object.entries(alignments2);\n        if (arr.length === 1) {\n          prev[dir] = arr[0][1];\n          return prev;\n        }\n        for (let i = 0; i < arr.length - 1; i++) {\n          for (let j = i + 1; j < arr.length; j++) {\n            const [aGroupId, aNodeIds] = arr[i];\n            const [bGroupId, bNodeIds] = arr[j];\n            const alignment = groupAlignments[aGroupId]?.[bGroupId];\n            if (alignment === alignmentDir) {\n              prev[dir] ??= [];\n              prev[dir] = [...prev[dir], ...aNodeIds, ...bNodeIds];\n            } else if (aGroupId === \"default\" || bGroupId === \"default\") {\n              prev[dir] ??= [];\n              prev[dir] = [...prev[dir], ...aNodeIds, ...bNodeIds];\n            } else {\n              const keyA = `${dir}-${cnt++}`;\n              prev[keyA] = aNodeIds;\n              const keyB = `${dir}-${cnt++}`;\n              prev[keyB] = bNodeIds;\n            }\n          }\n        }\n        return prev;\n      },\n      {}\n    );\n  }, \"flattenAlignments\");\n  const alignments = spatialMaps.map((spatialMap) => {\n    const horizontalAlignments = {};\n    const verticalAlignments = {};\n    Object.entries(spatialMap).forEach(([id, [x, y]]) => {\n      const nodeGroup = db2.getNode(id)?.in ?? \"default\";\n      horizontalAlignments[y] ??= {};\n      horizontalAlignments[y][nodeGroup] ??= [];\n      horizontalAlignments[y][nodeGroup].push(id);\n      verticalAlignments[x] ??= {};\n      verticalAlignments[x][nodeGroup] ??= [];\n      verticalAlignments[x][nodeGroup].push(id);\n    });\n    return {\n      horiz: Object.values(flattenAlignments(horizontalAlignments, \"horizontal\")).filter(\n        (arr) => arr.length > 1\n      ),\n      vert: Object.values(flattenAlignments(verticalAlignments, \"vertical\")).filter(\n        (arr) => arr.length > 1\n      )\n    };\n  });\n  const [horizontal, vertical] = alignments.reduce(\n    ([prevHoriz, prevVert], { horiz, vert }) => {\n      return [\n        [...prevHoriz, ...horiz],\n        [...prevVert, ...vert]\n      ];\n    },\n    [[], []]\n  );\n  return {\n    horizontal,\n    vertical\n  };\n}\n__name(getAlignments, \"getAlignments\");\nfunction getRelativeConstraints(spatialMaps) {\n  const relativeConstraints = [];\n  const posToStr = /* @__PURE__ */ __name((pos) => `${pos[0]},${pos[1]}`, \"posToStr\");\n  const strToPos = /* @__PURE__ */ __name((pos) => pos.split(\",\").map((p) => parseInt(p)), \"strToPos\");\n  spatialMaps.forEach((spatialMap) => {\n    const invSpatialMap = Object.fromEntries(\n      Object.entries(spatialMap).map(([id, pos]) => [posToStr(pos), id])\n    );\n    const queue = [posToStr([0, 0])];\n    const visited = {};\n    const directions = {\n      L: [-1, 0],\n      R: [1, 0],\n      T: [0, 1],\n      B: [0, -1]\n    };\n    while (queue.length > 0) {\n      const curr = queue.shift();\n      if (curr) {\n        visited[curr] = 1;\n        const currId = invSpatialMap[curr];\n        if (currId) {\n          const currPos = strToPos(curr);\n          Object.entries(directions).forEach(([dir, shift]) => {\n            const newPos = posToStr([currPos[0] + shift[0], currPos[1] + shift[1]]);\n            const newId = invSpatialMap[newPos];\n            if (newId && !visited[newPos]) {\n              queue.push(newPos);\n              relativeConstraints.push({\n                [ArchitectureDirectionName[dir]]: newId,\n                [ArchitectureDirectionName[getOppositeArchitectureDirection(dir)]]: currId,\n                gap: 1.5 * getConfigField(\"iconSize\")\n              });\n            }\n          });\n        }\n      }\n    }\n  });\n  return relativeConstraints;\n}\n__name(getRelativeConstraints, \"getRelativeConstraints\");\nfunction layoutArchitecture(services, junctions, groups, edges, db2, { spatialMaps, groupAlignments }) {\n  return new Promise((resolve) => {\n    const renderEl = select(\"body\").append(\"div\").attr(\"id\", \"cy\").attr(\"style\", \"display:none\");\n    const cy = cytoscape({\n      container: document.getElementById(\"cy\"),\n      style: [\n        {\n          selector: \"edge\",\n          style: {\n            \"curve-style\": \"straight\",\n            label: \"data(label)\",\n            \"source-endpoint\": \"data(sourceEndpoint)\",\n            \"target-endpoint\": \"data(targetEndpoint)\"\n          }\n        },\n        {\n          selector: \"edge.segments\",\n          style: {\n            \"curve-style\": \"segments\",\n            \"segment-weights\": \"0\",\n            \"segment-distances\": [0.5],\n            // @ts-ignore Incorrect library types\n            \"edge-distances\": \"endpoints\",\n            \"source-endpoint\": \"data(sourceEndpoint)\",\n            \"target-endpoint\": \"data(targetEndpoint)\"\n          }\n        },\n        {\n          selector: \"node\",\n          style: {\n            // @ts-ignore Incorrect library types\n            \"compound-sizing-wrt-labels\": \"include\"\n          }\n        },\n        {\n          selector: \"node[label]\",\n          style: {\n            \"text-valign\": \"bottom\",\n            \"text-halign\": \"center\",\n            \"font-size\": `${getConfigField(\"fontSize\")}px`\n          }\n        },\n        {\n          selector: \".node-service\",\n          style: {\n            label: \"data(label)\",\n            width: \"data(width)\",\n            height: \"data(height)\"\n          }\n        },\n        {\n          selector: \".node-junction\",\n          style: {\n            width: \"data(width)\",\n            height: \"data(height)\"\n          }\n        },\n        {\n          selector: \".node-group\",\n          style: {\n            // @ts-ignore Incorrect library types\n            padding: `${getConfigField(\"padding\")}px`\n          }\n        }\n      ]\n    });\n    renderEl.remove();\n    addGroups(groups, cy);\n    addServices(services, cy);\n    addJunctions(junctions, cy);\n    addEdges(edges, cy);\n    const alignmentConstraint = getAlignments(db2, spatialMaps, groupAlignments);\n    const relativePlacementConstraint = getRelativeConstraints(spatialMaps);\n    const layout = cy.layout({\n      name: \"fcose\",\n      quality: \"proof\",\n      styleEnabled: false,\n      animate: false,\n      nodeDimensionsIncludeLabels: false,\n      // Adjust the edge parameters if it passes through the border of a group\n      // Hacky fix for: https://github.com/iVis-at-Bilkent/cytoscape.js-fcose/issues/67\n      idealEdgeLength(edge) {\n        const [nodeA, nodeB] = edge.connectedNodes();\n        const { parent: parentA } = nodeData(nodeA);\n        const { parent: parentB } = nodeData(nodeB);\n        const elasticity = parentA === parentB ? 1.5 * getConfigField(\"iconSize\") : 0.5 * getConfigField(\"iconSize\");\n        return elasticity;\n      },\n      edgeElasticity(edge) {\n        const [nodeA, nodeB] = edge.connectedNodes();\n        const { parent: parentA } = nodeData(nodeA);\n        const { parent: parentB } = nodeData(nodeB);\n        const elasticity = parentA === parentB ? 0.45 : 1e-3;\n        return elasticity;\n      },\n      alignmentConstraint,\n      relativePlacementConstraint\n    });\n    layout.one(\"layoutstop\", () => {\n      function getSegmentWeights(source, target, pointX, pointY) {\n        let W, D;\n        const { x: sX, y: sY } = source;\n        const { x: tX, y: tY } = target;\n        D = (pointY - sY + (sX - pointX) * (sY - tY) / (sX - tX)) / Math.sqrt(1 + Math.pow((sY - tY) / (sX - tX), 2));\n        W = Math.sqrt(Math.pow(pointY - sY, 2) + Math.pow(pointX - sX, 2) - Math.pow(D, 2));\n        const distAB = Math.sqrt(Math.pow(tX - sX, 2) + Math.pow(tY - sY, 2));\n        W = W / distAB;\n        let delta1 = (tX - sX) * (pointY - sY) - (tY - sY) * (pointX - sX);\n        switch (true) {\n          case delta1 >= 0:\n            delta1 = 1;\n            break;\n          case delta1 < 0:\n            delta1 = -1;\n            break;\n        }\n        let delta2 = (tX - sX) * (pointX - sX) + (tY - sY) * (pointY - sY);\n        switch (true) {\n          case delta2 >= 0:\n            delta2 = 1;\n            break;\n          case delta2 < 0:\n            delta2 = -1;\n            break;\n        }\n        D = Math.abs(D) * delta1;\n        W = W * delta2;\n        return {\n          distances: D,\n          weights: W\n        };\n      }\n      __name(getSegmentWeights, \"getSegmentWeights\");\n      cy.startBatch();\n      for (const edge of Object.values(cy.edges())) {\n        if (edge.data?.()) {\n          const { x: sX, y: sY } = edge.source().position();\n          const { x: tX, y: tY } = edge.target().position();\n          if (sX !== tX && sY !== tY) {\n            const sEP = edge.sourceEndpoint();\n            const tEP = edge.targetEndpoint();\n            const { sourceDir } = edgeData(edge);\n            const [pointX, pointY] = isArchitectureDirectionY(sourceDir) ? [sEP.x, tEP.y] : [tEP.x, sEP.y];\n            const { weights, distances } = getSegmentWeights(sEP, tEP, pointX, pointY);\n            edge.style(\"segment-distances\", distances);\n            edge.style(\"segment-weights\", weights);\n          }\n        }\n      }\n      cy.endBatch();\n      layout.run();\n    });\n    layout.run();\n    cy.ready((e) => {\n      log.info(\"Ready\", e);\n      resolve(cy);\n    });\n  });\n}\n__name(layoutArchitecture, \"layoutArchitecture\");\nvar draw = /* @__PURE__ */ __name(async (text, id, _version, diagObj) => {\n  const db2 = diagObj.db;\n  const services = db2.getServices();\n  const junctions = db2.getJunctions();\n  const groups = db2.getGroups();\n  const edges = db2.getEdges();\n  const ds = db2.getDataStructures();\n  const svg = selectSvgElement(id);\n  const edgesElem = svg.append(\"g\");\n  edgesElem.attr(\"class\", \"architecture-edges\");\n  const servicesElem = svg.append(\"g\");\n  servicesElem.attr(\"class\", \"architecture-services\");\n  const groupElem = svg.append(\"g\");\n  groupElem.attr(\"class\", \"architecture-groups\");\n  await drawServices(db2, servicesElem, services);\n  drawJunctions(db2, servicesElem, junctions);\n  const cy = await layoutArchitecture(services, junctions, groups, edges, db2, ds);\n  await drawEdges(edgesElem, cy);\n  await drawGroups(groupElem, cy);\n  positionNodes(db2, cy);\n  setupGraphViewbox(void 0, svg, getConfigField(\"padding\"), getConfigField(\"useMaxWidth\"));\n}, \"draw\");\nvar renderer = { draw };\n\n// src/diagrams/architecture/architectureDiagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer,\n  styles: architectureStyles_default\n};\nexport {\n  diagram\n};\n", "import {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/common/populateCommonDb.ts\nfunction populateCommonDb(ast, db) {\n  if (ast.accDescr) {\n    db.setAccDescription?.(ast.accDescr);\n  }\n  if (ast.accTitle) {\n    db.setAccTitle?.(ast.accTitle);\n  }\n  if (ast.title) {\n    db.setDiagramTitle?.(ast.title);\n  }\n}\n__name(populateCommonDb, \"populateCommonDb\");\n\nexport {\n  populateCommonDb\n};\n", "import {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/utils/imperativeState.ts\nvar ImperativeState = class {\n  /**\n   * @param init - Function that creates the default state.\n   */\n  constructor(init) {\n    this.init = init;\n    this.records = this.init();\n  }\n  static {\n    __name(this, \"ImperativeState\");\n  }\n  reset() {\n    this.records = this.init();\n  }\n};\n\nexport {\n  ImperativeState\n};\n"], "names": [], "sourceRoot": ""}