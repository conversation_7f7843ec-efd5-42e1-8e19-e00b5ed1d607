[{"title": "Install in Google Colab", "url": "https://github.com/OpenBB-finance/OpenBB/blob/develop/examples/googleColab.ipynb", "img": "https://raw.githubusercontent.com/OpenBB-finance/OpenBBTerminal/develop/examples/googleColab.webp", "description": "Install the OpenBB Platform in Google Colab and get started pulling data and creating visualizations."}, {"title": "Find Symbols", "url": "https://github.com/OpenBB-finance/OpenBB/blob/develop/examples/findSymbols.ipynb", "img": "https://raw.githubusercontent.com/OpenBB-finance/OpenBBTerminal/develop/examples/findSymbols.webp", "description": "An introduction to discovering, finding, screening, and searching symbols using different sources."}, {"title": "Load Historical Price Data", "url": "https://github.com/OpenBB-finance/OpenBB/blob/develop/examples/loadHistoricalPriceData.ipynb", "img": "https://my.openbb.co/assets/images/sdk/examples/loadHistoricalPriceData.webp", "description": "Loading data with different intervals and sources, ticker symbology, load data from other asset classes, load multiple tickers in one go, draw lines on plotly."}, {"title": "Copper To Gold Ratio", "url": "https://github.com/OpenBB-finance/OpenBB/blob/develop/examples/copperToGoldRatio.ipynb", "img": "https://my.openbb.co/assets/images/sdk/examples/copperToGoldRatio.webp", "description": "Calculate copper to gold ratio, load front-month future prices, 10-year constant maturity vs treasury bill, basic dataframe operations, plotting on 2 y-axis."}, {"title": "USD Liquidity Index", "url": "https://github.com/OpenBB-finance/OpenBB/blob/develop/examples/usdLiquidityIndex.ipynb", "img": "https://my.openbb.co/assets/images/sdk/examples/usdLiquidityIndex.webp", "description": "Query the Federal Reserve Economic Database and recreate the USD Liquidity Index, load multiple data series, basic operations on a dataframe, normalization methods, and creating custom chart."}, {"title": "Financial Statements", "url": "https://github.com/OpenBB-finance/OpenBB/blob/develop/examples/financialStatements.ipynb", "img": "https://raw.githubusercontent.com/OpenBB-finance/OpenBBTerminal/develop/examples/financialStatements.webp", "description": "Get started with financial statements in the OpenBB Platform. This notebook compares the data from different providers and demonstrates how to access items within the three main financial statements - balance, cash, and income."}, {"title": "Implied Earnings Move", "url": "https://github.com/OpenBB-finance/OpenBB/blob/develop/examples/impliedEarningsMove.ipynb", "img": "https://raw.githubusercontent.com/OpenBB-finance/OpenBBTerminal/develop/examples/impliedEarningsMove.webp", "description": "Calculate the implied earnings move using options prices. This notebook demonstrates how to get the data from free sources and apply filters to arrive at the expected move, as a percent, in either direction."}, {"title": "Streamlit News Headlines", "url": "https://github.com/OpenBB-finance/OpenBB/blob/develop/examples/streamlit/news.py", "img": "https://raw.githubusercontent.com/OpenBB-finance/OpenBBTerminal/develop/examples/streamlit_news.webp", "description": "An example Streamlit dashboard for news headlines with data from Biztoc, Benzinga, FMP, Intrinio, and Tiingo."}]