#!/usr/bin/env python3
"""
OpenBB Platform 使用示例
展示如何使用OpenBB进行金融数据分析
"""

import time
from datetime import datetime, timedelta
import pandas as pd


def setup_openbb():
    """设置OpenBB环境"""
    try:
        from openbb_core.app.static.app_factory import create_app
        app = create_app()
        print("✅ OpenBB Platform 初始化成功")
        return app
    except Exception as e:
        print(f"❌ OpenBB 初始化失败: {e}")
        return None


def get_stock_data_yfinance(symbol="AAPL", period="1mo"):
    """使用yfinance获取股票数据"""
    try:
        import yfinance as yf
        
        print(f"📈 正在获取 {symbol} 的股票数据...")
        
        # 添加延迟避免API限制
        time.sleep(1)
        
        ticker = yf.Ticker(symbol)
        
        # 获取基本信息
        try:
            info = ticker.info
            if info:
                print(f"   公司名称: {info.get('longName', 'N/A')}")
                print(f"   行业: {info.get('industry', 'N/A')}")
                print(f"   市值: {info.get('marketCap', 'N/A')}")
        except:
            print("   基本信息获取失败（可能是API限制）")
        
        # 获取历史数据
        try:
            data = ticker.history(period=period)
            if not data.empty:
                print(f"   ✅ 成功获取 {len(data)} 条历史数据")
                print(f"   最新收盘价: ${data['Close'].iloc[-1]:.2f}")
                print(f"   期间最高价: ${data['High'].max():.2f}")
                print(f"   期间最低价: ${data['Low'].min():.2f}")
                return data
            else:
                print("   ⚠️ 历史数据为空")
                return None
        except Exception as e:
            print(f"   ❌ 历史数据获取失败: {e}")
            return None
            
    except Exception as e:
        print(f"❌ yfinance 数据获取失败: {e}")
        return None


def analyze_stock_data(data, symbol):
    """分析股票数据"""
    if data is None or data.empty:
        print("❌ 无数据可分析")
        return
    
    print(f"\n📊 {symbol} 数据分析:")
    print("=" * 40)
    
    # 基本统计
    print(f"数据期间: {data.index[0].strftime('%Y-%m-%d')} 到 {data.index[-1].strftime('%Y-%m-%d')}")
    print(f"交易日数: {len(data)}")
    
    # 价格统计
    close_prices = data['Close']
    print(f"\n价格统计:")
    print(f"  平均收盘价: ${close_prices.mean():.2f}")
    print(f"  价格标准差: ${close_prices.std():.2f}")
    print(f"  最高收盘价: ${close_prices.max():.2f}")
    print(f"  最低收盘价: ${close_prices.min():.2f}")
    
    # 计算收益率
    returns = close_prices.pct_change().dropna()
    if not returns.empty:
        print(f"\n收益率统计:")
        print(f"  平均日收益率: {returns.mean():.4f} ({returns.mean()*100:.2f}%)")
        print(f"  收益率标准差: {returns.std():.4f} ({returns.std()*100:.2f}%)")
        print(f"  最大单日涨幅: {returns.max():.4f} ({returns.max()*100:.2f}%)")
        print(f"  最大单日跌幅: {returns.min():.4f} ({returns.min()*100:.2f}%)")
    
    # 成交量统计
    if 'Volume' in data.columns:
        volume = data['Volume']
        print(f"\n成交量统计:")
        print(f"  平均成交量: {volume.mean():,.0f}")
        print(f"  最大成交量: {volume.max():,.0f}")
        print(f"  最小成交量: {volume.min():,.0f}")


def create_simple_chart(data, symbol):
    """创建简单的文本图表"""
    if data is None or data.empty:
        return
    
    print(f"\n📈 {symbol} 价格趋势 (最近10个交易日):")
    print("=" * 50)
    
    # 取最近10天的数据
    recent_data = data.tail(10)
    
    # 计算相对位置用于简单可视化
    prices = recent_data['Close']
    min_price = prices.min()
    max_price = prices.max()
    price_range = max_price - min_price
    
    if price_range == 0:
        print("价格无变化")
        return
    
    for date, price in prices.items():
        # 计算相对位置 (0-20的范围)
        relative_pos = int(((price - min_price) / price_range) * 20)
        
        # 创建简单的条形图
        bar = "█" * relative_pos + "░" * (20 - relative_pos)
        
        print(f"{date.strftime('%m-%d')}: {bar} ${price:.2f}")


def main():
    """主函数"""
    print("🚀 OpenBB Platform 股票分析示例")
    print("=" * 50)
    
    # 初始化OpenBB
    app = setup_openbb()
    if app is None:
        print("❌ OpenBB 初始化失败，退出程序")
        return
    
    # 要分析的股票列表
    symbols = ["AAPL", "MSFT", "GOOGL"]
    
    for symbol in symbols:
        print(f"\n{'='*60}")
        print(f"分析股票: {symbol}")
        print(f"{'='*60}")
        
        # 获取数据
        data = get_stock_data_yfinance(symbol, period="1mo")
        
        if data is not None:
            # 分析数据
            analyze_stock_data(data, symbol)
            
            # 创建简单图表
            create_simple_chart(data, symbol)
        
        # 添加延迟避免API限制
        time.sleep(2)
    
    print(f"\n{'='*60}")
    print("📋 分析完成！")
    print("\n💡 提示:")
    print("- 如果遇到API限制，请稍后再试")
    print("- 可以修改股票代码和时间周期")
    print("- 更多功能请参考OpenBB文档")


if __name__ == "__main__":
    main()
