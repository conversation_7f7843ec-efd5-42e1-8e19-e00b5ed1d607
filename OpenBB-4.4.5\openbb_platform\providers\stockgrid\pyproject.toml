[tool.poetry]
name = "openbb-stockgrid"
version = "1.4.1"
description = "stockgrid extension for OpenBB"
authors = ["OpenBB Team <<EMAIL>>"]
license = "AGPL-3.0-only"
readme = "README.md"
packages = [{ include = "openbb_stockgrid" }]

[tool.poetry.dependencies]
python = ">=3.9.21,<3.13"
openbb-core = "^1.4.6"
pytest-freezegun = "^0.4.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.plugins."openbb_provider_extension"]
stockgrid = "openbb_stockgrid:stockgrid_provider"
