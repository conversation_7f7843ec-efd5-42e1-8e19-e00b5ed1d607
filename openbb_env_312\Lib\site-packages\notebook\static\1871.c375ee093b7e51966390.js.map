{"version": 3, "file": "1871.c375ee093b7e51966390.js?v=c375ee093b7e51966390", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;AAEqD;;AAErD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,oDAAoD,UAAU;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA,GAAG;;AAEiE;;;ACvEpB;AACd;AACkI;AAC3H;AACZ;AACU;AACV;AACG;AAChC;AACA;AACA,WAAW,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,mBAAS;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,mBAAmB;AACvC,oBAAoB,cAAc;AAClC;AACA,gBAAgB,8BAAY,YAAY,sCAAoB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,wBAAwB;AAChD,wBAAwB,gBAAgB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,aAAG;AAC/B;AACA;AACA,4BAA4B,aAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,0BAA0B,mBAAS;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,wBAAwB;AAChD,wBAAwB,gBAAgB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,aAAG;AAC/B;AACA;AACA,4BAA4B,aAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,wBAAwB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,aAAG;AAC/B;AACA;AACA,4BAA4B,aAAG;AAC/B;AACA;AACA,4BAA4B,aAAG;AAC/B;AACA;AACA;AACA,wBAAwB,gBAAgB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kCAAkC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,oBAAoB,qBAAqB;AACzC;AACA;AACA,gBAAgB,gBAAgB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,mBAAmB;AACnC,gBAAgB,kBAAkB;AAClC,eAAe,aAAG,UAAU,uBAAS,YAAY,aAAG,UAAU,uBAAS,kCAAkC,gCAAkB;AAC3H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,6BAA6B;AAC7C,cAAc,UAAU,EAAE,8BAAY;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,wBAAwB;AACxC,gBAAgB,gBAAgB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,aAAG;AACvB;AACA;AACA,oBAAoB,aAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,gBAAgB,uCAAuC;AACvD,gBAAgB,+BAA+B;AAC/C,cAAc,uBAAS;AACvB,8BAA8B,8BAAY;AAC1C,6CAA6C,6BAAW;AACxD,oBAAoB,mBAAI,6BAA6B,4DAA4D,gCAAkB,oCAAoC;AACvK;AACA;AACA;AACA;AACA;AACA,YAAY,gCAAc;AAC1B;AACA;AACA,YAAY,8BAAY;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,qBAAqB,6KAA6K;AAClN,gBAAgB,gBAAgB;AAChC;AACA,gBAAgB,2BAA2B;AAC3C,0BAA0B,8BAAY;AACtC,6BAA6B,kBAAQ;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,YAAY;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,KAAK,GAAG,MAAM;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,2DAA2D,iBAAiB;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,6BAAW;AACpC,eAAe,mBAAI,aAAa,eAAe;AAC/C;AACA;AACA;AACA;AACA;AACA,gBAAgB,iMAAiM;AACjN,gBAAgB,qDAAqD;AACrE,gBAAgB,qCAAqC,EAAE,8BAAY;AACnE,uBAAuB,2BAAS;AAChC;AACA;AACA,gBAAgB,mBAAI,WAAW,iaAAia;AAChc;AACA;AACA;AACA;AACA;AACA,gBAAgB,sLAAsL;AACtM,gBAAgB,qDAAqD;AACrE;AACA,4BAA4B,6BAAW;AACvC,gBAAgB,gDAAgD,EAAE,8BAAY;AAC9E,uBAAuB,2BAAS;AAChC;AACA;AACA,gBAAgB,mBAAI,WAAW,0HAA0H,yBAAyB,4QAA4Q;AAC9b;AACA;AACA;AACA;AACA;AACA,gBAAgB,yKAAyK;AACzL,gBAAgB,qDAAqD;AACrE,gBAAgB,+CAA+C,EAAE,8BAAY;AAC7E,uBAAuB,2BAAS;AAChC;AACA;AACA,gBAAgB,mBAAI,WAAW,iXAAiX;AAChZ;AACA;AACA;AACA;AACA,gBAAgB,qBAAqB,4LAA4L;AACjO,gBAAgB,gBAAgB;AAChC,cAAc,uBAAuB;AACrC;AACA,0BAA0B,8BAAY;AACtC,gBAAgB,2BAA2B;AAC3C,6BAA6B,kBAAQ;AACrC;AACA,iCAAiC,kBAAQ;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,YAAY;AACpC;AACA;AACA;AACA,kDAAkD,kBAAQ;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,KAAK,GAAG,MAAM;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,6BAAW;AACpC,eAAe,mBAAI,aAAa,eAAe;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,4LAA4L;AAC5M,gBAAgB,wFAAwF;AACxG,gBAAgB,UAAU,+BAA+B,qBAAqB;AAC9E;AACA,gBAAgB,uDAAuD,EAAE,8BAAY;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,mBAAI,oBAAoB,2cAA2c;AAC1f;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAe,UAAU,EAAC;AAC1B;;ACrlBgD;AACwC;AACjD;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,kJAAkJ;AAC9J,YAAY,QAAQ;AACpB,YAAY,yDAAyD;AACrE,YAAY;AACZ;AACA,6CAA6C,EAAE,8BAAY;AAC3D,mBAAmB,2BAAS;AAC5B,gCAAgC,gCAAkB;AAClD,+BAA+B,gCAAkB;AACjD;AACA;AACA;AACA,sBAAsB,6BAAW;AACjC;AACA;AACA,oBAAoB,kBAAQ;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,0BAA0B,6BAAW;AACrC;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,YAAY,mBAAI,WAAW,WAAW,yBAAyB,+VAA+V;AAC9Z;AACA,0DAAe,YAAY,EAAC;AAC5B;;;;;AC/D+D;AAC7B;AACL;AACQ;AACN;AACmH;AAClJ;AACA;AACA;AACA;AACA;AACA,yBAAyB,mBAAS;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,mCAAmC;AACvD,oBAAoB,+BAA+B;AACnD,oBAAoB,cAAc;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,2BAA2B;AACvD;AACA,gBAAgB,+BAA+B,aAAa,IAAI;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,8BAA8B;AAC9C,gBAAgB,iBAAiB;AACjC;AACA,aAAa,4BAAU;AACvB,oBAAoB,YAAY,aAAa,IAAI;AACjD;AACA;AACA,yBAAyB;AACzB;AACA,aAAa,4BAAU;AACvB,oBAAoB,mBAAmB;AACvC;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,oBAAoB,aAAa,IAAI;AACrD,8BAA8B,iDAA+B;AAC7D;AACA;AACA;AACA;AACA,gBAAgB,mBAAmB;AACnC,kBAAkB,aAAa,EAAE,mDAAmD;AACpF;AACA;AACA;AACA;AACA,gBAAgB,wCAAwC,8DAA8D;AACtH,gBAAgB,iEAAiE;AACjF,gBAAgB,4BAA4B;AAC5C,gBAAgB,mCAAmC;AACnD,gBAAgB,8FAA8F,EAAE,8BAAY;AAC5H,uBAAuB,2BAAS,GAAG,gBAAgB;AACnD,0BAA0B,aAAG,cAAc,wBAAU;AACrD,iCAAiC,cAAI,eAAe,wBAAU;AAC9D;AACA;AACA;AACA;AACA;AACA,oBAAoB,WAAW;AAC/B;AACA,sCAAsC,8BAAY,GAAG,UAAU;AAC/D;AACA;AACA,cAAc,gCAAkB;AAChC,cAAc,gCAAkB;AAChC;AACA;AACA;AACA;AACA,SAAS;AACT,gBAAgB,oBAAK,UAAU,wDAAwD,mBAAI,UAAU,mCAAmC,mBAAI,WAAW,gCAAgC,KAAK,EAAE,mDAAmD,aAAa,4BAA4B,yFAAyF,iBAAO,yJAAyJ,2BAA2B,gNAAgN,GAAG,sBAAsB,mBAAI,iBAAiB,qCAAqC,IAAI;AACr1B;AACA;AACA,uDAAe,UAAU,EAAC;AAC1B;;AC9HgD;AACF;AACP;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,oDAAoD;AAChE,sCAAsC,sBAAQ;AAC9C,YAAY,cAAc;AAC1B;AACA;AACA;AACA;AACA;AACA,yBAAyB,yBAAW;AACpC;AACA;AACA;AACA;AACA,eAAe,MAAM;AACrB,wBAAwB,MAAM;AAC9B;AACA;AACA;AACA;AACA;AACA,cAAc,0BAAQ;AACtB,cAAc,0BAAQ;AACtB;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,iCAAiC,MAAM;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,mBAAI,gBAAgB,mDAAmD;AAClF;AACA,yDAAe,WAAW,EAAC;AAC3B;;ACrEwB,aAAa,wDAAwD,YAAY,mBAAmB,KAAK,mBAAmB,sEAAsE,SAAS,yBAAyB,kCAAkC,0jBAA0jB,MAAM,aAAa,uFAAuF,SAAS,GAAG,+hBAA+hB,gCAAgC,KAAK,iDAAiD,qGAAqG,SAAS,GAAG,IAAI,IAAI,MAAM,2BAA2B,GAAG,uFAAuF,GAAG,sBAAsB,IAAI,uBAAuB,IAAI,KAAK,GAAG,GAAG,GAAG,uDAAuD,EAAE,8GAA8G,GAAG,oHAAoH,IAAI,iDAAiD,IAAI,8DAA8D,IAAI,8KAA8K,IAAI,eAAe,IAAI,EAAE,mJAAmJ,IAAI,iCAAiC,mRAAmR,GAAG,IAAI,GAAG,qLAAqL,EAAE,uCAAuC,EAAE,kCAAkC,EAAE,2BAA2B,EAAE,uMAAuM,eAAe,kCAAkC,wBAAwB,eAAe,qCAAqC,wBAAwB,eAAe,mGAAmG,wBAAwB,eAAe,oBAAoB,iDAAiD,GAAG,8CAA8C,wBAAwB,iBAAiB,4CAA4C,OAAO,uBAAuB,+BAA+B,+DAA+D,uBAAuB,uDAAuD,SAAS,OAAO,0BAA0B,6CAA6C,QAAQ,wFAAwF,IAAI,0BAA0B,MAAM,+DAA+D,eAAe,6BAA6B,qBAAqB,wCAAwC,sDAAsD,2BAA2B,eAAe,MAAM,SAAS,IAAI,uRAAuR,GAAG,UAAU,eAAe,eAAe,KAAK,iBAAiB,KAAK,oBAAoB,eAAe,6SAA6S,eAAe,kEAAkE,qBAAqB,kBAAkB,aAAa,gBAAgB,aAAa,aAAa,sBAAsB,4BAA4B,6EAA6E,+DAA+D,oBAAoB,mBAAmB,YAAY,6EAA6E,4CAA4C,oBAAoB,EAAE,wCAAwC,6BAA6B,sCAAsC,EAAE,6BAA6B,iBAAiB,0BAA0B,EAAE,sBAAsB,eAAe,oBAAoB,eAAe,wBAAwB,+BAA+B,EAAE,eAAe,wBAAwB,yCAAyC,EAAE,eAAe,qBAAqB,0CAA0C,eAAe,sBAAsB,iBAAiB,EAAE,iBAAiB,kCAAkC,SAAS,4EAA4E,cAAc,wBAAwB,eAAe,IAAI,uHAAuH,SAAS,YAAY,SAAS,eAAe,0BAA0B,mBAAmB,oCAAoC,wBAAwB,eAAe,+BAA+B,mBAAmB,oCAAoC,wBAAwB,eAAe,+BAA+B,mBAAmB,qBAAqB,YAAY,eAAe,oBAAoB,oBAAoB,sBAAsB,EAAE,cAAc,SAAS,cAAc,YAAY,kBAAkB,mCAAmC,mBAAmB,QAAQ,qBAAqB,KAAK,iCAAiC,WAAW,YAAY,qBAAqB,EAAE,qBAAqB,0BAA0B,EAAE,UAAU,EAAE,qCAAqC,gBAAgB,0EAA0E,EAAE,iBAAiB,oBAAoB,MAAM,6DAA6D,QAAQ,cAAc,kBAAkB,SAAS,oDAAoD,qBAAqB,qBAAqB,OAAO,SAAS,GAAG,KAAK,sDAAsD,SAAS,6BAA6B,oCAAoC,MAAM,kCAAkC,KAAK,2DAA2D,YAAY,MAAM,OAAO,0BAA0B,YAAY,IAAI,gBAAgB,mBAAmB,gCAAgC,uBAAuB,WAAW,oBAAoB,2EAA2E,uBAAuB,KAAK,oCAAoC,aAAa,yEAAyE,gCAAgC,sBAAsB,+BAA+B,4BAA4B,2BAA2B,kCAAkC,2FAA2F,GAAG,oGAAoG,oBAAoB,+DAA+D,iCAAiC,SAAS,GAAG,OAAO,2BAA2B,qGAAqG,6DAA6D,uBAAe,CAAC,eAAe,IAAI,gBAAgB,iCAAiC,wCAAwC,OAAO,yBAAyB,eAAe,SAAS,WAAW,uGAAuG,QAAQ,YAAY,yBAAyB,gDAAgD,sCAAsC,gBAAgB,qDAAqD,UAAU,EAAE,oBAAoB,qDAAqD,UAAU,EAAE,gBAAgB,+BAA+B,qCAAqC,EAAE,0BAA0B,2BAA2B,UAAU,cAAc,UAAU,yBAAyB,OAAO,KAAK,WAAW,iBAAiB,+BAA+B,uFAAuF,EAAE,iBAAiB,+BAA+B,2BAA2B,4BAA4B,UAAU,SAAS,eAAe,sCAAsC,8BAA8B,IAAI,YAAY,wBAAwB,+BAA+B,WAAW,mBAAmB,YAAY,yBAAyB,gDAAgD,UAAU,UAAU,UAAU,cAAc,+BAA+B,mCAAmC,6BAA6B,0DAA0D,EAAE,cAAc,4DAA4D,8DAA8D,wBAAwB,QAAQ,GAAG,kBAAkB,kBAAkB,oBAAoB,qCAAqC,0DAA0D,EAAE,gBAAgB,iCAAiC,0CAA0C,EAAE,iEAAiE,MAAM,sEAAsE,sCAAsC,wFAAwF,4BAA4B,UAAU,mDAAmD,sBAAsB,6BAA6B,oBAAoB,OAAO,2BAA2B,6BAA6B,UAAU,UAAU,GAAG,kBAAkB,iCAAiC,YAAY,YAAY,gCAAgC,oCAAoC,2BAA2B,wFAAwF,EAAE,WAAW,sCAAsC,iDAAiD,yBAAyB,8DAA8D,kBAAkB,kCAAkC,+BAA+B,WAAW,sBAAsB,0BAA0B,EAAE,0BAA0B,kFAAkF,WAAW,sBAAsB,uCAAuC,EAAE,yBAAyB,6BAA6B,kBAAkB,mCAAmC,WAAW,yCAAyC,yBAAyB,yEAAyE,6CAA6C,gBAAgB,qDAAqD,UAAU,kBAAkB,UAAU,uCAAuC,uBAAuB,GAAG,YAAY,eAAe,+BAA+B,0BAA0B,oCAAoC,sFAAsF,OAAO,cAAc,qCAAqC,kDAAkD,kCAAkC,4EAA4E,4BAA4B,UAAU,qBAAqB,YAAY,2CAA2C,UAAU,kBAAkB,UAAU,uDAAuD,eAAe,oBAAoB,SAAS,6CAA6C,eAAe,MAAM,qBAAqB,eAAe,oBAAoB,SAAS,GAAG,KAAK,WAAW,gCAAgC,gFAAgF,mBAAmB,iBAAiB,qCAAqC,mBAAmB,8BAA8B,UAAU,kBAAkB,qBAAqB,qCAAqC,mBAAmB,0BAA0B,UAAU,kBAAkB,kBAAkB,gCAAgC,sBAAsB,EAAE,iBAAiB,uDAAuD,UAAU,kBAAkB,0BAA0B,sDAAsD,UAAU,oBAAoB,kFAAkF,qBAAqB,qBAAqB,gBAAgB,uBAAuB,oCAAoC,EAAE,EAAE,QAAQ,KAAK,WAAW,EAAE,4CAA4C,IAAI,SAAS,qBAAqB,MAAM,oGAAoG,MAAM,KAAK,0BAA0B,4BAA4B,8BAA8B,0BAA0B,gBAAgB,qBAAqB,yDAAyD,QAAQ,yBAAyB,uBAAuB,yBAAyB,yCAAyC,kCAAkC,EAAE,qBAAqB,mBAAmB,SAAS,YAAY,WAAW,KAAK,QAAQ,uCAAuC,8CAA8C,iBAAiB,iBAAiB,EAAE,OAAO,cAAc,4CAA4C,aAAa,mBAAmB,gBAAgB,+CAA+C,gCAAgC,UAAU,IAAI,OAAO,mDAAe,IAAI,IAAI,wBAAwB,mBAAmB,oBAAoB,YAAY,kBAAkB,QAAQ,WAAW,sCAAsC,SAAS,MAAM,OAAO,sBAAc,YAAY,EAAoE;AACjmf;;;;;;;;;ACD+D;AAC7B;AACuI;AAClI;AACV;AACA;AACU;AACV;AACI;AACjC;AACA;AACA;AACA;AACA;AACA,0BAA0B,mBAAS;AACnC;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kCAAkC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,qBAAqB;AAC7C,yCAAyC;AACzC,gBAAgB,eAAK;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,qBAAqB;AACzC,oBAAoB,oCAAoC,EAAE,8BAAY;AACtE;AACA;AACA,mBAAmB,aAAG;AACtB,4BAA4B,aAAa,EAAE,4BAA4B,EAAE,QAAQ;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kCAAkC;AAC1D;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA,6BAA6B;AAC7B,iBAAiB;AACjB,mDAAmD;AACnD,gCAAgC,8BAA8B;AAC9D;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,+BAA+B;AACnD,kCAAkC;AAClC;AACA,gBAAgB,kBAAQ;AACxB;AACA;AACA,oBAAoB,qBAAO;AAC3B,4BAA4B,cAAc;AAC1C,4DAA4D,eAAe,qBAAO,GAAG;AACrF;AACA;AACA,8BAA8B,wBAAU,gBAAgB,wBAAU;AAClE;AACA;AACA;AACA;AACA;AACA,YAAY,aAAG;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,SAAS;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,YAAY,iBAAiB,IAAI;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,gCAAkB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,gCAAgC,8JAA8J;AAC9M,gBAAgB,qEAAqE;AACrF,gBAAgB,cAAc;AAC9B;AACA,0BAA0B,8BAAY;AACtC,gBAAgB,oCAAoC;AACpD;AACA;AACA;AACA;AACA;AACA,gCAAgC,iCAAe;AAC/C;AACA;AACA,oBAAoB,oBAAK,UAAU,WAAW,mBAAI,QAAQ,oCAAoC,cAAc,YAAY,mBAAI,CAAC,YAAQ,IAAI,0BAA0B,gCAAkB,qDAAqD,GAAG,GAAG,mBAAI,UAAU,kCAAkC,IAAI;AACpS;AACA,yBAAyB,6BAAW;AACpC;AACA;AACA;AACA;AACA;AACA,oDAAoD,aAAG,UAAU,4BAAc,QAAQ,sCAAwB;AAC/G;AACA,+BAA+B,8BAAY;AAC3C,sCAAsC,aAAG,qBAAqB;AAC9D;AACA,8BAA8B,mBAAI,gBAAgB,qDAAqD,aAAG,UAAU,4BAAc,WAAW,yCAAyC,aAAG,sGAAsG,aAAG,mXAAmX;AACrpB;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,mBAAI,aAAa,mDAAmD;AACnF;AACA;AACA,yDAAe,WAAW,EAAC;AAC3B;;ACrOsF;AACvC;AACwI;AAChJ;AACR;AACQ;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,0BAA0B;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,+BAAa;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,6BAAW;AACxD,oBAAoB,mBAAI,6BAA6B,4DAA4D,gCAAkB,+DAA+D;AAClM;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,sMAAsM;AAClN,YAAY,4CAA4C;AACxD,sBAAsB,8BAAY;AAClC,0BAA0B,6BAAW;AACrC,qCAAqC,6BAAW;AAChD,8BAA8B,6BAAW;AACzC,+BAA+B,6BAAW;AAC1C;AACA,8BAA8B,oBAAM;AACpC,qBAAqB,8BAAY;AACjC;AACA;AACA;AACA,uCAAuC,yBAAW;AAClD;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,gCAAgC;AAC5C;AACA,0BAA0B,cAAI;AAC9B,QAAQ,4BAAc;AACtB,sBAAsB,4BAAc,IAAI,cAAI,eAAe,4BAAc;AACzE;AACA,mBAAmB,mBAAI,mBAAmB,+QAA+Q;AACzT,wBAAwB,oBAAM;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,sCAAwB;AACxC;AACA;AACA,oEAAoE,mBAAI,CAAC,YAAQ,IAAI,uBAAuB;AAC5G;AACA;AACA,wDAAwD,+BAAa,SAAS;AAC9E;AACA;AACA;AACA;AACA,YAAY,KAAqC,EAAE,EAE1C;AACT;AACA;AACA;AACA;AACA;AACA,2BAA2B,mBAAI,sBAAsB,kJAAkJ;AACvM;AACA;AACA;AACA;AACA,0HAA0H,mBAAI,uBAAuB,wHAAwH;AAC7Q;AACA,sBAAsB,mBAAI,6BAA6B,IAAI,+BAAa,4FAA4F;AACpK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,mBAAI,kBAAkB,yBAAyB,oBAAK,CAAC,oBAAS,IAAI,8FAA8F,mBAAI,gBAAgB,8VAA8V,kBAAQ,wBAAwB,sEAAsE,gFAAgF,mBAAI,gBAAgB,8VAA8V,kBAAQ,wBAAwB,sEAAsE,KAAK,GAAG;AACprC;AACA;AACA;AACA;AACA,0BAA0B,mBAAS;AACnC;AACA,gBAAgB,4BAAU;AAC1B;AACA;AACA,eAAe,mBAAI,sBAAsB,eAAe;AACxD;AACA;AACA,yDAAe,WAAW,EAAC;AAC3B;;AC3KgD;AAC+B;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,0KAA0K;AACtL,YAAY,gBAAgB;AAC5B,YAAY,qDAAqD;AACjE,uDAAuD,6BAAW;AAClE;AACA,kBAAkB,2BAAS;AAC3B;AACA;AACA,YAAY,uEAAuE,EAAE,8BAAY;AACjG;AACA;AACA,mBAAmB,2BAAS;AAC5B,YAAY,mBAAI,WAAW,WAAW,yBAAyB,yXAAyX;AACxb;AACA,yDAAe,WAAW,EAAC;AAC3B;;ACvBkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,qBAAqB;AACjC,IAAI,uBAAS;AACb;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,uDAAe,SAAS,EAAC;AACzB;;AChBsC;AACI;AACQ;AACV;AACA;AACA;AACA;AACJ;AACpC;AACA;AACA,oBAAoB,gBAAgB;AACpC,oBAAoB,iBAAU;AAC9B;AACA,oBAAoB;AACpB,mBAAmB;AACnB,mBAAmB;AACnB,oBAAoB,gBAAgB;AACpC,mBAAmB;AACnB,mBAAmB;AACnB,iBAAiB;AACjB;AACA;AACA,wDAAe,MAAM,EAAC;AACtB;;ACvBgD;AACwB;AACxE;AACA;AACA;AACA;AACA;AACe;AACf,YAAY,oDAAoD;AAChE,oBAAoB,8BAAY;AAChC,YAAY,6BAA6B;AACzC;AACA;AACA;AACA,qCAAqC,6BAAW;AAChD,YAAY,mBAAI,6BAA6B,IAAI,+BAAa,8FAA8F;AAC5J;AACA;;ACjB+D;AAC/D;AACA;AACA;AACA;AACe;AACf,YAAY,kLAAkL;AAC9L,YAAY,yDAAyD;AACrE;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,oBAAK,UAAU,iCAAiC,mBAAI,UAAU,sEAAsE,kBAAkB,mBAAI,UAAU,oDAAoD,oBAAK,UAAU;AACnP;AACA;AACA,qBAAqB,4CAA4C,mBAAI,iBAAiB,kJAAkJ,mCAAmC,mBAAI,mBAAmB,oJAAoJ,gBAAgB,mBAAI,eAAe,2HAA2H,kBAAkB,mBAAI,iBAAiB,2HAA2H,KAAK,GAAG,KAAK;AACnwB;AACA;;ACnB+D;AACN;AACzD;AACA;AACA;AACA;AACe;AACf,YAAY,mHAAmH;AAC/H,sBAAsB,8BAAY;AAClC,0CAA0C,6BAAW;AACrD,mCAAmC,6BAAW;AAC9C,oCAAoC,6BAAW;AAC/C;AACA,YAAY,mBAAmB,WAAW,IAAI;AAC9C,YAAY,oBAAK,eAAe,mDAAmD,mBAAI,4BAA4B,iIAAiI,GAAG,mBAAI,kCAAkC,sIAAsI,GAAG,mBAAI,UAAU;AACpb,iCAAiC,mBAAmB,MAAM,mBAAI,2BAA2B,cAAc,UAAU,cAAc,mBAAI,cAAc,0HAA0H,KAAK;AAChR;AACA;;ACjBgD;AACkB;AAClE;AACA;AACA;AACA;AACA;AACe;AACf,YAAY,wDAAwD;AACpE,oBAAoB,8BAAY;AAChC,YAAY,6BAA6B;AACzC;AACA;AACA;AACA,+BAA+B,6BAAW;AAC1C,YAAY,mBAAI,uBAAuB,IAAI,yBAAO,sGAAsG;AACxJ;AACA;;ACjBsF;AAClD;AACyC;AAC7E;AACA;AACA;AACA;AACA;AACA;AACe;AACf,YAAY;AACZ;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA,2CAA2C,sBAAsB;AACjE;AACA;AACA;AACA,WAAW,+BAAa;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,yBAAW,IAAI,UAAU,SAAS;AACxD,oBAAoB,yBAAW,IAAI,UAAU,SAAS;AACtD,qBAAqB,yBAAW,IAAI,UAAU,SAAS;AACvD,YAAY,oBAAK,CAAC,oBAAS,IAAI,WAAW,mBAAI,YAAY,qKAAqK,4BAAU,mHAAmH,oCAAkB,yBAAyB,sCAAsC,mBAAI,eAAe,IAAI,4BAAU;AAC9c;AACA;AACA,2BAA2B,mBAAI,aAAa,gBAAgB;AAC5D,iBAAiB,GAAG,cAAc,GAAG,MAAM;AAC3C;AACA;;ACxCgD;AACK;AACrD;AACA;AACe,wBAAwB,UAAU;AACjD,YAAY,sDAAsD,EAAE,wCAAsB;AAC1F;AACA;AACA;AACA,YAAY,mBAAI,UAAU,UAAU,mBAAI,aAAa,iEAAiE,kCAAkC,yBAAyB,GAAG;AACpL;AACA;;ACXgD;AACC;AAClC;AACf,YAAY,2EAA2E;AACvF,YAAY,mBAAI,aAAa,sCAAsC,UAAU,EAAE,UAAU,4BAA4B,mBAAI,QAAQ,kCAAkC,KAAK,GAAG,GAAG;AAC9K;AACO;AACP,YAAY,YAAY,iBAAiB,IAAI;AAC7C,YAAY,mBAAI,eAAe,uBAAuB,gCAAkB,oEAAoE;AAC5I;AACO;AACP,YAAY,YAAY,iBAAiB,IAAI;AAC7C,YAAY,mBAAI,eAAe,uBAAuB,gCAAkB,mFAAmF;AAC3J;AACO;AACP,YAAY,YAAY,iBAAiB,IAAI;AAC7C,YAAY,mBAAI,eAAe,uBAAuB,gCAAkB,6EAA6E;AACrJ;AACO;AACP,YAAY,YAAY,iBAAiB,IAAI;AAC7C,YAAY,mBAAI,eAAe,uBAAuB,gCAAkB,8FAA8F;AACtK;AACA;;ACtBgD;AACC;AACX;AACtC;AACA;AACe,qBAAqB,yCAAyC;AAC7E,YAAY,kBAAkB;AAC9B,YAAY,mBAAI,UAAU,4BAA4B,mBAAI,QAAQ,kDAAkD,UAAU,aAAa,mBAAI,CAAC,UAAU,IAAI,uFAAuF,gCAAkB,uEAAuE,GAAG,GAAG;AACpV;AACA;;ACT0C;AACN;AACkD;AACtF;AACA;AACA,oBAAoB;AACpB,iBAAiB;AACjB,kBAAkB;AAClB,sBAAsB;AACtB,oBAAoB;AACpB,oBAAoB;AACpB;AACA;AACA,sDAAe,eAAe,EAAC;AAC/B;;ACdgD;AAChD;AACA;AACA;AACA;AACe;AACf,YAAY,kBAAkB;AAC9B;AACA;AACA;AACA;AACA,gBAAgB,mBAAI,QAAQ,+DAA+D;AAC3F;AACA;AACA,gBAAgB,mBAAI,UAAU,+DAA+D;AAC7F;AACA;AACA;;ACjB+D;AACb;AAClD;AACA;AACA;AACA;AACe,qBAAqB,mBAAmB;AACvD,YAAY,kBAAkB;AAC9B,YAAY,oBAAK,UAAU,mDAAmD,mBAAI,UAAU,sCAAsC,mBAAI,SAAS,oDAAoD,gCAAkB,eAAe,GAAG,GAAG,mBAAI,SAAS;AACvP,4BAA4B,mBAAI,SAAS,iEAAiE;AAC1G,iBAAiB,GAAG,IAAI;AACxB;AACA;;ACZ+D;AAC/D;AACA;AACA;AACA;AACA;AACe;AACf,YAAY,sBAAsB;AAClC;AACA;AACA;AACA,YAAY,oBAAK,YAAY,uEAAuE,mBAAI,WAAW,wDAAwD,IAAI;AAC/K;AACA;;ACb+D;AACN;AAC7B;AAC5B;AACA;AACA;AACA;AACA;AACe;AACf,YAAY,qGAAqG;AACjH,sBAAsB,8BAAY;AAClC,qCAAqC,6BAAW;AAChD;AACA,eAAe,mBAAI,UAAU,yCAAyC;AACtE;AACA,YAAY,oBAAK,6BAA6B,qCAAqC,mBAAI,CAAC,KAAK,IAAI,0CAA0C,8EAA8E;AACzN;AACA;;ACjB4C;AAC5C,8DAAe,aAAa,EAAC;AAC7B;;ACFgD;AACV;AACtC;AACA;AACA;AACA;AACe;AACf,YAAY,wBAAwB;AACpC;AACA;AACA;AACA,eAAe,yBAAO;AACtB,YAAY,mBAAI,UAAU,UAAU,mBAAI,SAAS;AACjD;AACA;AACA,wBAAwB,mBAAI,SAAS,2CAA2C;AAChF,aAAa,GAAG,GAAG;AACnB;AACA;;AClBgD;AACX;AACrC;AACA;AACA;AACA;AACe;AACf,YAAY,iBAAiB;AAC7B;AACA;AACA;AACA,eAAe,wBAAM;AACrB;AACA,gBAAgB,mBAAI,QAAQ,iDAAiD;AAC7E;AACA,YAAY,mBAAI,UAAU,iDAAiD;AAC3E;AACA;;ACjB+D;AAC6B;AAC5F;AACA;AACA;AACA;AACA;AACA;AACe;AACf,YAAY,4HAA4H;AACxI,oBAAoB,8BAAY;AAChC,+BAA+B,6BAAW;AAC1C,qCAAqC,6BAAW;AAChD;AACA,YAAY,mBAAmB,WAAW,IAAI;AAC9C,YAAY,oBAAK,eAAe,uCAAuC,mBAAI,uBAAuB,IAAI,yBAAO,sGAAsG,oBAAoB,mBAAI,6BAA6B,IAAI,+BAAa,8FAA8F,4CAA4C,2BAAS,iCAAiC,mBAAI,cAAc,0IAA0I,KAAK;AAC9mB;AACA;;ACjB+D;AAC/D,MAAM,gCAAqB;AAC3B;AACA;AACA;AACA;AACe;AACf,YAAY,sBAAsB;AAClC,YAAY,oBAAK,aAAa,sCAAsC,mBAAI,WAAW,iCAAiC,gCAAqB,EAAE,IAAI;AAC/I;AACA;;ACV+D;AACd;AACV;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,qCAAqC;AACjD,YAAY,kBAAkB;AAC9B,wBAAwB,gCAAkB;AAC1C;AACA;AACA,wBAAwB,gCAAkB;AAC1C;AACA;AACA;AACA;AACA,8BAA8B,gCAAkB;AAChD,kBAAkB,gCAAkB;AACpC,kBAAkB,gCAAkB;AACpC;AACA;AACA,YAAY,oBAAK,UAAU,2CAA2C,mBAAI,QAAQ,UAAU,mBAAI,CAAC,YAAQ,IAAI,2DAA2D,GAAG,aAAa,mBAAI,UAAU,2CAA2C,IAAI;AACrP;AACA,iEAAe,gBAAgB,EAAC;AAChC;;AC3B+D;AACa;AAClC;AAC1C;AACA;AACA;AACA;AACA;AACe;AACf,YAAY,sIAAsI;AAClJ,YAAY,6BAA6B;AACzC;AACA,YAAY,eAAe;AAC3B,qCAAqC,gCAAkB;AACvD,uBAAuB,sCAAwB;AAC/C;AACA,gBAAgB,mBAAI,UAAU,yDAAyD;AACvF;AACA,YAAY,mBAAI,UAAU,+CAA+C,oBAAK,UAAU,6BAA6B,mBAAI,UAAU,iDAAiD,oBAAK,UAAU,oCAAoC,mBAAI,CAAC,KAAK,IAAI,4CAA4C,GAAG,OAAO,GAAG,mBAAI,YAAY,gDAAgD,GAAG,gFAAgF,IAAI,GAAG,GAAG,mBAAI,UAAU,sEAAsE,GAAG,mBAAI,UAAU,iCAAiC,mBAAI,iBAAiB,mDAAmD,aAAa,+GAA+G,GAAG,IAAI,GAAG;AAC/xB;AACA;;ACpB4E;AACd;AACR;AACU;AACZ;AACJ;AACE;AACd;AACQ;AACU;AACF;AACI;AAClB;AACY;AACgB;AAClE;AACA;AACA,qCAAqC;AACrC,8BAA8B;AAC9B,0BAA0B;AAC1B,+BAA+B;AAC/B,yBAAyB,eAAe;AACxC,yBAAyB;AACzB,kCAAkC,gBAAgB;AAClD,2BAA2B,SAAS;AACpC,qBAAqB;AACrB,0BAA0B;AAC1B,yBAAyB;AACzB,2BAA2B;AAC3B,4BAA4B,UAAU;AACtC,kCAAkC,0BAAgB;AAClD,gCAAgC;AAChC;AACA;AACA,2DAAe,SAAS,EAAC;AACzB;;ACnC+D;AACM;AACqC;AAC1G;AACA;AACA,wBAAwB,WAAW;AACnC,uBAAuB,iBAAiB,qBAAG,QAAQ;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,yCAAyC;AACrD;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,UAAU,6CAA6C;AACvD,UAAU,yCAAyC;AACnD;AACA;AACA,oBAAoB,2CAA2C,IAAI,+CAA+C,IAAI,+CAA+C;AACrK;AACA;AACA;AACA,uBAAuB,qGAAqG;AAC5H;AACA,YAAY,eAAe;AAC3B,YAAY,mBAAI,iBAAiB,UAAU,iBAAiB,4DAA4D,+CAA+C,gOAAgO,oCAAkB,UAAU;AACna;AACA;AACA;AACA;AACA,yBAAyB,qIAAqI;AAC9J,YAAY,kBAAkB;AAC9B,sCAAsC,sBAAQ;AAC9C,8BAA8B,wBAAU;AACxC,iBAAiB;AACjB,KAAK,EAAE,iCAAe;AACtB,IAAI,uBAAS;AACb,2BAA2B,8BAAY;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,iCAAe;AACpC;AACA,KAAK;AACL,yBAAyB,yBAAW;AACpC,mBAAmB,mBAAmB;AACtC,KAAK;AACL,yBAAyB,yBAAW;AACpC;AACA;AACA;AACA;AACA,0BAA0B,iCAAe;AACzC,iBAAiB,8BAAY;AAC7B,KAAK;AACL,wBAAwB,yBAAW;AACnC;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,YAAY,oBAAK,SAAS,8GAA8G,mBAAI,SAAS,yCAAyC,mBAAI,gBAAgB,2LAA2L,GAAG,oFAAoF,mBAAI,SAAS,yCAAyC,mBAAI,QAAQ,+FAA+F,gCAAkB,YAAY,GAAG,oFAAoF,mBAAI,SAAS,yCAAyC,mBAAI,QAAQ,mGAAmG,gCAAkB,cAAc,GAAG,KAAK;AACv8B;AACA,4DAAe,aAAa,EAAC;AAC7B;;AC5EgD;AAChD;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD,YAAY,gBAAgB;AAC5B,WAAW,mBAAI,kBAAkB,sBAAsB;AACvD;AACA,gEAAe,iBAAiB,EAAC;AACjC;;ACX+D;AAC3B;AAC+E;AACnH;AACA;AACA;AACA;AACA;AACA,0BAA0B,qIAAqI;AAC/J;AACA,qCAAqC,6BAAW;AAChD;AACA;AACA;AACA,qBAAqB,yCAAuB;AAC5C,yBAAyB,yBAAW;AACpC,uBAAuB,yBAAW;AAClC,wBAAwB,yBAAW;AACnC;AACA,YAAY,oBAAK,UAAU,uBAAuB,uCAAuC,8CAA8C,mBAAI,6BAA6B,IAAI,+BAAa,wFAAwF,IAAI,oBAAK,YAAY,WAAW,mBAAI,YAAY,2PAA2P,oCAAkB,MAAM,GAAG,4BAAU,CAAC,mBAAI,WAAW,iBAAiB,gBAAgB,IAAI;AACtpB;AACA,6DAAe,cAAc,EAAC;AAC9B;;ACtB+D;AAC3B;AAC2H;AAC/J;AACA;AACA;AACA;AACA;AACA,4BAA4B,yBAAyB,uDAAuD,kEAAkE;AAC9K;AACA,uBAAuB,yBAAW,IAAI,UAAU,SAAS,gBAAgB,0CAAwB;AACjG,wBAAwB,yBAAW,IAAI,UAAU,SAAS,iBAAiB,0CAAwB;AACnG,YAAY,mBAAI,UAAU;AAC1B;AACA,gCAAgC,uCAAqB;AACrD;AACA;AACA;AACA;AACA,iCAAiC,wCAAsB;AACvD;AACA;AACA,iCAAiC,0CAAwB;AACzD;AACA;AACA,kCAAkC,oBAAK,WAAW,WAAW,mBAAI,YAAY,sBAAsB,0BAAQ,0OAA0O,oCAAkB,MAAM,GAAG,mBAAI,WAAW,wBAAwB,IAAI;AAC3Z,iCAAiC,mBAAI,YAAY,8BAA8B,YAAY,uBAAuB,aAAa,mBAAI,UAAU,uBAAuB,YAAY,aAAa,mBAAI,YAAY,oBAAoB,GAAG;AACpO,aAAa,GAAG;AAChB;AACA,+DAAe,gBAAgB,EAAC;AAChC;;AC9BgD;AACN;AAC1C;AACA;AACA;AACA;AACA;AACe;AACf,YAAY,wCAAwC;AACpD,8BAA8B,6BAAW;AACzC,WAAW,mBAAI,sBAAsB,yDAAyD;AAC9F;AACA;;ACZgD;AACZ;AACM;AAC1C;AACA;AACA;AACA;AACA;AACe;AACf,YAAY,8BAA8B;AAC1C,8BAA8B,6BAAW;AACzC,yBAAyB,yBAAW;AACpC,WAAW,mBAAI,sBAAsB,gDAAgD;AACrF;AACA;;ACdgD;AACmB;AACnE;AACA;AACA;AACA;AACA;AACe;AACf,YAAY,qCAAqC;AACjD,8BAA8B,6BAAW;AACzC,YAAY,mBAAI,sBAAsB,yCAAyC,4BAAU,uCAAuC,4BAAU,UAAU;AACpJ;AACA;;ACZgD;AACN;AAC1C;AACA;AACA;AACA;AACe;AACf,YAAY,oBAAoB;AAChC,8BAA8B,6BAAW;AACzC,WAAW,mBAAI,sBAAsB,yBAAyB;AAC9D;AACA;;ACXsF;AACxC;AACgC;AACvC;AACvC;AACA;AACA;AACA;AACA,6BAA6B,WAAW,OAAO,0BAA0B;AACzE;AACA;AACA,YAAY,mBAAmB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,2BAA2B,qBAAqB;AAChD,YAAY,kBAAkB;AAC9B,YAAY,sBAAsB;AAClC;AACA;AACA;AACA;AACA,eAAe,mBAAI,UAAU,uBAAuB,kBAAkB,6BAA6B;AACnG;AACA,YAAY,oBAAK,CAAC,oBAAS,IAAI,gBAAgB,mBAAI,QAAQ,qBAAqB,KAAK,wEAAwE,gCAAkB,gBAAgB,IAAI;AACnM;AACA,qBAAqB,+BAA+B;AACpD;AACA;AACA;AACA,YAAY,kBAAkB;AAC9B,YAAY,mBAAI,SAAS;AACzB,oBAAoB,mBAAmB;AACvC,oBAAoB,oBAAK,SAAS,WAAW,mBAAI,CAAC,YAAQ,IAAI,0BAA0B,gCAAkB,yCAAyC,cAAc,mBAAI,oBAAoB,wCAAwC,IAAI;AACrO,SAAS,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA,gBAAgB,aAAa,EAAE,+BAAa;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,6EAA6E;AACzF,8BAA8B,6BAAW;AACzC,sCAAsC,sBAAQ;AAC9C,yBAAyB,yBAAW;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,KAAK;AACL,YAAY,oBAAK,UAAU,WAAW,mBAAI,sBAAsB,oMAAoM,GAAG,mBAAI,cAAc,wEAAwE,IAAI;AACrW;AACA,yDAAe,UAAU,EAAC;AAC1B;;ACxGgD;AAChD;AACA;AACA;AACA;AACA;AACA,wBAAwB,YAAY;AACpC,WAAW,mBAAI,YAAY,oFAAoF;AAC/G;AACA,2DAAe,YAAY,EAAC;AAC5B;;ACVgD;AACN;AAC1C;AACA;AACA;AACA;AACe;AACf,YAAY,oBAAoB;AAChC,8BAA8B,6BAAW;AACzC,WAAW,mBAAI,sBAAsB,4BAA4B;AACjE;AACA;;ACX+D;AAC3B;AACyE;AAC7G;AACA;AACA;AACA;AACA;AACA,uBAAuB,iGAAiG;AACxH,YAAY,gDAAgD;AAC5D,uBAAuB,yBAAW,IAAI,UAAU,SAAS,gBAAgB,0CAAwB;AACjG,wBAAwB,yBAAW,IAAI,UAAU,SAAS,iBAAiB,0CAAwB;AACnG,YAAY,mBAAI,UAAU;AAC1B;AACA,gCAAgC,uCAAqB;AACrD;AACA;AACA;AACA,+BAA+B,oBAAK,WAAW,WAAW,mBAAI,YAAY,mBAAmB,0BAAQ,kPAAkP,oCAAkB,MAAM,GAAG,mBAAI,WAAW,wBAAwB,IAAI;AAC7Z,iCAAiC,mBAAI,YAAY,2BAA2B,YAAY,oBAAoB,SAAS,mBAAI,UAAU,oBAAoB,YAAY,aAAa,mBAAI,YAAY,iBAAiB,GAAG;AACpN,aAAa,GAAG;AAChB;AACA,0DAAe,WAAW,EAAC;AAC3B;;ACvB+D;AAC/D;AACA;AACA;AACA;AACA;AACe;AACf,YAAY,mBAAmB,aAAa,mBAAmB,GAAG,IAAI;AACtE,YAAY,oBAAK,UAAU,6CAA6C,mBAAI,sBAAsB,yBAAyB,GAAG,mBAAI,WAAW,0CAA0C,IAAI;AAC3L;AACA;;ACV+D;AAC3B;AACkE;AACtG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,wIAAwI;AAChK,YAAY,qDAAqD;AACjE;AACA,wBAAwB,yBAAW;AACnC;AACA,2BAA2B,0CAAwB;AACnD,KAAK;AACL,uBAAuB,yBAAW;AAClC;AACA,0BAA0B,0CAAwB;AAClD,KAAK;AACL,yBAAyB,yBAAW;AACpC;AACA,wBAAwB,0CAAwB;AAChD,KAAK;AACL,4BAA4B,0CAAwB;AACpD,YAAY,oBAAK,aAAa,+SAA+S,oCAAkB,8DAA8D,mBAAI,aAAa,kCAAkC;AAChd,mCAAmC,cAAc;AACjD;AACA,4BAA4B,mBAAI,aAAa,uDAAuD;AACpG,iBAAiB,IAAI;AACrB;AACA,2DAAe,YAAY,EAAC;AAC5B;;ACxCgD;AACZ;AACa;AACjD;AACA;AACA;AACA;AACA,0BAA0B,gBAAgB,mGAAmG;AAC7I,yBAAyB,yBAAW,IAAI,UAAU,SAAS;AAC3D,uBAAuB,yBAAW,IAAI,UAAU,SAAS;AACzD,wBAAwB,yBAAW,IAAI,UAAU,SAAS;AAC1D,YAAY,mBAAI,eAAe,8RAA8R,oCAAkB,MAAM;AACrV;AACA;AACA;AACA,eAAe;AACf;AACA,6DAAe,cAAc,EAAC;AAC9B;;AClBgD;AACN;AAC1C;AACA;AACA;AACA;AACe;AACf,YAAY,oBAAoB;AAChC,8BAA8B,6BAAW;AACzC,WAAW,mBAAI,sBAAsB,UAAU;AAC/C;AACA;;ACXgD;AACZ;AACM;AAC1C;AACA;AACA;AACA;AACA;AACe;AACf,YAAY,8BAA8B;AAC1C,8BAA8B,6BAAW;AACzC,yBAAyB,yBAAW,gCAAgC,MAAM;AAC1E,WAAW,mBAAI,sBAAsB,gDAAgD;AACrF;AACA;;ACdgD;AACN;AAC1C;AACA;AACA;AACA;AACe;AACf,YAAY,oBAAoB;AAChC,8BAA8B,6BAAW;AACzC,WAAW,mBAAI,sBAAsB,uBAAuB;AAC5D;AACA;;ACXgD;AACN;AAC1C;AACA;AACA;AACA;AACe;AACf,YAAY,oBAAoB;AAChC,8BAA8B,6BAAW;AACzC,WAAW,mBAAI,sBAAsB,0BAA0B;AAC/D;AACA;;ACX4C;AACQ;AACN;AACI;AACV;AACF;AACQ;AACN;AACF;AACI;AACI;AACN;AACA;AACE;AACI;AACR;AACA;AACF;AACM;AAC1C;AACA;AACA,qBAAqB;AACrB,yBAAyB;AACzB,sBAAsB;AACtB,wBAAwB;AACxB,mBAAmB;AACnB,kBAAkB;AAClB,sBAAsB;AACtB,mBAAmB;AACnB,kBAAkB;AAClB,oBAAoB;AACpB,sBAAsB;AACtB,mBAAmB;AACnB,mBAAmB;AACnB,oBAAoB;AACpB,kBAAkB;AAClB,sBAAsB;AACtB,kBAAkB;AAClB,oBAAoB;AACpB,iBAAiB;AACjB;AACA;AACA,yDAAe,OAAO,EAAC;AACvB;;AC3CsD;AACb;AACM;AACJ;AAC3C;AACA;AACA;AACA;AACe;AACf;AACA,gBAAgB,iBAAM;AACtB,mBAAmB,oBAAS;AAC5B,iBAAiB,kBAAO;AACxB,sBAAsB;AACtB,uBAAuB;AACvB,yBAAyB,qCAAuB;AAChD;AACA;AACA;;AClB+D;AAClB;AAC4N;AAC3O;AACQ;AACN;AACI;AACmB;AACvD;AACe,MAAM,SAAI,SAAS,mBAAS;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,cAAK;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,4CAA8B,eAAe,sBAAQ;AAC3F,+CAA+C,sBAAQ;AACvD;AACA;AACA;AACA;AACA;AACA,qCAAqC,sBAAQ;AAC7C;AACA,8CAA8C,aAAI;AAClD;AACA;AACA;AACA,gCAAgC,iBAAQ;AACxC;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,2EAA2E;AAC/F,oBAAoB,uCAAuC;AAC3D,gBAAgB,0BAAQ;AACxB;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,qCAAmB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,8BAAY;AAClC;AACA;AACA;AACA;AACA,4BAA4B,6BAAW;AACvC;AACA;AACA;AACA;AACA;AACA,8DAA8D,yBAAyB;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,WAAW;AAC/B;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA,+CAA+C;AAC/C;AACA,8DAA8D,yBAAyB;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,SAAS;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,UAAU;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,mDAAmD;AACvE,kBAAkB,wBAAwB;AAC1C,oBAAoB,sBAAsB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,6BAAW;AACxD;AACA;AACA;AACA;AACA;AACA,mDAAmD;AACnD,iBAAiB;AACjB;AACA,mCAAmC,2DAA2D;AAC9F;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,4BAAU;AAC9C;AACA;AACA,2BAA2B,uBAAS;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,4BAAU;AACvB;AACA,kCAAkC,4BAAU;AAC5C,qBAAqB;AACrB;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,YAAY;AAChC,iBAAiB,4BAAU;AAC3B,iBAAiB,4BAAU;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,mCAAiB;AAC3C;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,wEAAwE;AACxE;AACA;AACA;AACA;AACA,oDAAoD;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,qCAAmB,GAAG,qBAAqB;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,8BAAY;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,4CAA4C;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,wCAAwC;AACxD,gBAAgB,cAAc;AAC9B,wBAAwB,8BAAY;AACpC,kCAAkC,6BAAW;AAC7C;AACA,oBAAoB,mBAAI,sBAAsB,8CAA8C,oFAAoF;AAChL;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,wDAAwD;AACxE,gBAAgB,cAAc;AAC9B,gBAAgB,2DAA2D,EAAE,kBAAkB;AAC/F;AACA,sBAAsB,iCAAiC;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,uBAAuB,mCAAmC;AAC1D;AACA;AACA;AACA;AACA,sCAAsC,mCAAqB;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,uCAAuC;AACvD,gBAAgB,WAAW;AAC3B,qBAAqB,gBAAO;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wEAAwE,UAAU;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,kEAAkE;AAClF,gBAAgB,+BAA+B;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,qCAAmB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA,+CAA+C;AAC/C,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,2PAA2P;AAC3Q,gBAAgB,oDAAoD;AACpE;AACA,gBAAgB,4BAA4B;AAC5C,gBAAgB,eAAe;AAC/B;AACA;AACA;AACA;AACA;AACA,cAAc,CAAC,oCAAsB,wBAAwB,EAAE,8BAAY;AAC3E;AACA,8BAA8B,2BAA2B;AACzD;AACA,iCAAiC,CAAC,4BAAc,KAAK,CAAC,oCAAsB;AAC5E,gBAAgB,oBAAK,YAAY,mVAAmV,mBAAI,iBAAiB,yTAAyT,yBAAyB,mBAAI,iBAAiB,8CAA8C,+DAA+D;AAC71B;AACA;AACA;;ACphBgD;AACb;AACE;AACrC;AACe;AACf,yBAAyB,4CAA4C;AACrE;AACA,mBAAmB;AACnB,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,6BAA6B,iGAAiG;AAC9H,KAAK;AACL;AACA;;ACpBqC;AACD;AACkB;AACb;AACzC,0CAAe,SAAI,EAAC;AACpB;;;;;;;;;;;;;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,WAAW,EAAE;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,gBAAgB;AACtD,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,IAAI,GAAG,MAAM;AAC/B;AACA,cAAc,IAAI,GAAG,MAAM;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6DAA6D;AAC7D,KAAK;AACL,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,QAAQ,EAAE,SAAS;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,0BAA0B;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,mCAAmC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,gCAAgC;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,4BAA4B;AAC7C;AACA;AACA,oBAAoB,0BAA0B;AAC9C,oBAAoB,IAAI,IAAI,MAAM;AAClC;AACA,yCAAyC,IAAI,IAAI,IAAI;AACrD;AACA;AACA,iBAAiB,iCAAiC;AAClD,qDAAqD,IAAI,IAAI,SAAS,IAAI,MAAM;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,gBAAgB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,sBAAsB,EAAE,YAAY;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,WAAW,EAAE,WAAW,EAAE,kBAAkB;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,gBAAgB;AACpC,QAAQ,IAA6D;AACrE;AACA,cAAc,oBAAoB,GAAG,IAAI;AACzC;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,KAAqC,QAAQ,CAAU,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ,EAAE,KAAqC,QAAQ,CAAU,CAAC;AAC9F,kDAAkD,IAAI,IAAI,OAAO;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP,6BAA6B,0BAA0B;AACvD;AACA;;;;;;;ACjZA,iBAAiB,mBAAO,CAAC,KAAe;AACxC,YAAY,mBAAO,CAAC,KAAS;;AAE7B;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;;;;;;;;AClBA,cAAc,mBAAO,CAAC,KAAY;AAClC,cAAc,mBAAO,CAAC,KAAY;AAClC,eAAe,mBAAO,CAAC,KAAa;;AAEpC;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,WAAW,UAAU;AACrB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;AC7BA,eAAe,mBAAO,CAAC,KAAa;AACpC,eAAe,mBAAO,CAAC,KAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,sBAAsB;AACjC,aAAa,QAAQ;AACrB;AACA;AACA,kBAAkB;AAClB;AACA;AACA,WAAW;AACX;AACA;AACA,6BAA6B;AAC7B,CAAC;;AAED;;;;;;;;ACxBA,gBAAgB,mBAAO,CAAC,KAAc;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,cAAc;AACzB,aAAa,SAAS;AACtB;AACA;AACA,kBAAkB,QAAQ,OAAO,UAAU;AAC3C;AACA;AACA;AACA;AACA,WAAW,QAAQ,SAAS;AAC5B;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ,SAAS;AAC5B;AACA;AACA;AACA;;AAEA;;;;;;;;;;ACjCa;;AAEb,QAAQ,mBAAO,CAAC,KAAW;AAC3B,IAAI,IAAqC;AACzC,EAAE,SAAkB;AACpB,EAAE,yBAAmB;AACrB,EAAE,KAAK,UAkBN;;;;;;;;;;ACxBY;AACb,6BAA6C,EAAE,aAAa,CAAC;AAC7D,kBAAkB,mBAAO,CAAC,KAAsB;AAChD,yBAAiB;AACjB;AACA;AACA;AACA,YAAY,mBAAO,CAAC,KAAS;AAC7B,yBAAa;AACb;AACA;AACA;AACA,kBAAkB,mBAAO,CAAC,KAAsB;AAChD,yBAAc;AACd,yBAAe;AACf,yBAAa;AACb;AACA,qCAAqC,uBAAuB;AAC5D;AACA,yBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAc;AACd;AACA;AACA;AACA,yBAAe;AACf;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,yBAAgB;AAChB;AACA;AACA;AACA,yBAAiB;AACjB;AACA;AACA;AACA,yBAAiB;AACjB;AACA;AACA;AACA,yBAAc;AACd;AACA;AACA;AACA,UAAa;AACb;AACA;AACA;AACA;AACA;AACA,yBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,wBAAwB;AACvE;AACA;AACA;AACA;AACA;AACA,yBAAuB;;;;;;;;;AC9EV;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;;;;;;;;;AC7Cb;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,gBAAgB,mBAAO,CAAC,IAAY;AACpC,mBAAmB,mBAAO,CAAC,KAAc;AACzC,kBAAkB,mBAAO,CAAC,KAAa;AACvC;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,uBAAuB;AACpD;AACA;AACA;AACA,4CAA4C,gBAAgB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwD,0BAA0B;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mFAAmF;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,mCAAmC;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,iBAAiB;;;;;;;;;AChMJ;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA;AACA,4BAA4B,wEAAwE,yBAAyB,UAAU;AACvI,0CAA0C,6BAA6B;AACvE,+BAA+B,aAAa;AAC5C;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA;AACA;AACA,0CAA0C,uBAAuB;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA,WAAW,aAAa,GAAG,YAAY;AACvC;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AC5Ga;AACb,8CAA6C,EAAE,aAAa,EAAC", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/nanoid/index.browser.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/fields/ArrayField.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/fields/BooleanField.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/fields/MultiSchemaField.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/fields/NumberField.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/markdown-to-jsx/dist/index.modern.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/fields/ObjectField.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/fields/SchemaField.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/fields/StringField.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/fields/NullField.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/fields/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/ArrayFieldDescriptionTemplate.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/ArrayFieldItemTemplate.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/ArrayFieldTemplate.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/ArrayFieldTitleTemplate.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/BaseInputTemplate.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/ButtonTemplates/SubmitButton.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/ButtonTemplates/IconButton.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/ButtonTemplates/AddButton.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/ButtonTemplates/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/DescriptionField.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/ErrorList.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/FieldTemplate/Label.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/FieldTemplate/FieldTemplate.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/FieldTemplate/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/FieldErrorTemplate.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/FieldHelpTemplate.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/ObjectFieldTemplate.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/TitleField.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/UnsupportedField.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/WrapIfAdditionalTemplate.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/templates/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/AltDateWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/AltDateTimeWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/CheckboxWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/CheckboxesWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/ColorWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/DateWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/DateTimeWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/EmailWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/FileWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/HiddenWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/PasswordWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/RadioWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/RangeWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/SelectWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/TextareaWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/TextWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/TimeWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/URLWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/UpDownWidget.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/widgets/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/getDefaultRegistry.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/components/Form.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/withTheme.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@rjsf/core/lib/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/free-style/dist.es2015/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/_basePick.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/_basePickBy.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/pick.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/lodash/unset.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/react-dom/client.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/typestyle/lib/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/typestyle/lib/internal/formatting.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/typestyle/lib/internal/typestyle.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/typestyle/lib/internal/utilities.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/typestyle/lib/types.js"], "sourcesContent": ["// This file replaces `index.js` in bundlers like webpack or Rollup,\n// according to `browser` config in `package.json`.\n\nimport { url<PERSON>lphabet } from './url-alphabet/index.js'\n\nlet random = bytes => crypto.getRandomValues(new Uint8Array(bytes))\n\nlet customRandom = (alphabet, defaultSize, getRandom) => {\n  // First, a bitmask is necessary to generate the ID. The bitmask makes bytes\n  // values closer to the alphabet size. The bitmask calculates the closest\n  // `2^31 - 1` number, which exceeds the alphabet size.\n  // For example, the bitmask for the alphabet size 30 is 31 (00011111).\n  // `Math.clz32` is not used, because it is not available in browsers.\n  let mask = (2 << (Math.log(alphabet.length - 1) / Math.LN2)) - 1\n  // Though, the bitmask solution is not perfect since the bytes exceeding\n  // the alphabet size are refused. Therefore, to reliably generate the ID,\n  // the random bytes redundancy has to be satisfied.\n\n  // Note: every hardware random generator call is performance expensive,\n  // because the system call for entropy collection takes a lot of time.\n  // So, to avoid additional system calls, extra bytes are requested in advance.\n\n  // Next, a step determines how many random bytes to generate.\n  // The number of random bytes gets decided upon the ID size, mask,\n  // alphabet size, and magic number 1.6 (using 1.6 peaks at performance\n  // according to benchmarks).\n\n  // `-~f => Math.ceil(f)` if f is a float\n  // `-~i => i + 1` if i is an integer\n  let step = -~((1.6 * mask * defaultSize) / alphabet.length)\n\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      // A compact alternative for `for (var i = 0; i < step; i++)`.\n      let j = step | 0\n      while (j--) {\n        // Adding `|| ''` refuses a random byte that exceeds the alphabet size.\n        id += alphabet[bytes[j] & mask] || ''\n        if (id.length === size) return id\n      }\n    }\n  }\n}\n\nlet customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size, random)\n\nlet nanoid = (size = 21) =>\n  crypto.getRandomValues(new Uint8Array(size)).reduce((id, byte) => {\n    // It is incorrect to use bytes exceeding the alphabet size.\n    // The following mask reduces the random byte in the 0-255 value\n    // range to the 0-63 value range. Therefore, adding hacks, such\n    // as empty string fallback or magic numbers, is unneccessary because\n    // the bitmask trims bytes down to the alphabet size.\n    byte &= 63\n    if (byte < 36) {\n      // `0-9a-z`\n      id += byte.toString(36)\n    } else if (byte < 62) {\n      // `A-Z`\n      id += (byte - 26).toString(36).toUpperCase()\n    } else if (byte > 62) {\n      id += '-'\n    } else {\n      id += '_'\n    }\n    return id\n  }, '')\n\nexport { nanoid, customAlphabet, customRandom, urlAlphabet, random }\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Component } from 'react';\nimport { getTemplate, getWidget, getUiOptions, isFixedItems, allowAdditionalItems, isCustomWidget, optionsList, TranslatableString, ITEMS_KEY, } from '@rjsf/utils';\nimport cloneDeep from 'lodash/cloneDeep';\nimport get from 'lodash/get';\nimport isObject from 'lodash/isObject';\nimport set from 'lodash/set';\nimport { nanoid } from 'nanoid';\n/** Used to generate a unique ID for an element in a row */\nfunction generateRowId() {\n    return nanoid();\n}\n/** Converts the `formData` into `KeyedFormDataType` data, using the `generateRowId()` function to create the key\n *\n * @param formData - The data for the form\n * @returns - The `formData` converted into a `KeyedFormDataType` element\n */\nfunction generateKeyedFormData(formData) {\n    return !Array.isArray(formData)\n        ? []\n        : formData.map((item) => {\n            return {\n                key: generateRowId(),\n                item,\n            };\n        });\n}\n/** Converts `KeyedFormDataType` data into the inner `formData`\n *\n * @param keyedFormData - The `KeyedFormDataType` to be converted\n * @returns - The inner `formData` item(s) in the `keyedFormData`\n */\nfunction keyedToPlainFormData(keyedFormData) {\n    if (Array.isArray(keyedFormData)) {\n        return keyedFormData.map((keyedItem) => keyedItem.item);\n    }\n    return [];\n}\n/** The `ArrayField` component is used to render a field in the schema that is of type `array`. It supports both normal\n * and fixed array, allowing user to add and remove elements from the array data.\n */\nclass ArrayField extends Component {\n    /** Constructs an `ArrayField` from the `props`, generating the initial keyed data from the `formData`\n     *\n     * @param props - The `FieldProps` for this template\n     */\n    constructor(props) {\n        super(props);\n        /** Returns the default form information for an item based on the schema for that item. Deals with the possibility\n         * that the schema is fixed and allows additional items.\n         */\n        this._getNewFormDataRow = () => {\n            const { schema, registry } = this.props;\n            const { schemaUtils } = registry;\n            let itemSchema = schema.items;\n            if (isFixedItems(schema) && allowAdditionalItems(schema)) {\n                itemSchema = schema.additionalItems;\n            }\n            // Cast this as a T to work around schema utils being for T[] caused by the FieldProps<T[], S, F> call on the class\n            return schemaUtils.getDefaultFormState(itemSchema);\n        };\n        /** Callback handler for when the user clicks on the add button. Creates a new row of keyed form data at the end of\n         * the list, adding it into the state, and then returning `onChange()` with the plain form data converted from the\n         * keyed data\n         *\n         * @param event - The event for the click\n         */\n        this.onAddClick = (event) => {\n            this._handleAddClick(event);\n        };\n        /** Callback handler for when the user clicks on the add button on an existing array element. Creates a new row of\n         * keyed form data inserted at the `index`, adding it into the state, and then returning `onChange()` with the plain\n         * form data converted from the keyed data\n         *\n         * @param index - The index at which the add button is clicked\n         */\n        this.onAddIndexClick = (index) => {\n            return (event) => {\n                this._handleAddClick(event, index);\n            };\n        };\n        /** Callback handler for when the user clicks on the copy button on an existing array element. Clones the row of\n         * keyed form data at the `index` into the next position in the state, and then returning `onChange()` with the plain\n         * form data converted from the keyed data\n         *\n         * @param index - The index at which the copy button is clicked\n         */\n        this.onCopyIndexClick = (index) => {\n            return (event) => {\n                if (event) {\n                    event.preventDefault();\n                }\n                const { onChange, errorSchema } = this.props;\n                const { keyedFormData } = this.state;\n                // refs #195: revalidate to ensure properly reindexing errors\n                let newErrorSchema;\n                if (errorSchema) {\n                    newErrorSchema = {};\n                    for (const idx in errorSchema) {\n                        const i = parseInt(idx);\n                        if (i <= index) {\n                            set(newErrorSchema, [i], errorSchema[idx]);\n                        }\n                        else if (i > index) {\n                            set(newErrorSchema, [i + 1], errorSchema[idx]);\n                        }\n                    }\n                }\n                const newKeyedFormDataRow = {\n                    key: generateRowId(),\n                    item: cloneDeep(keyedFormData[index].item),\n                };\n                const newKeyedFormData = [...keyedFormData];\n                if (index !== undefined) {\n                    newKeyedFormData.splice(index + 1, 0, newKeyedFormDataRow);\n                }\n                else {\n                    newKeyedFormData.push(newKeyedFormDataRow);\n                }\n                this.setState({\n                    keyedFormData: newKeyedFormData,\n                    updatedKeyedFormData: true,\n                }, () => onChange(keyedToPlainFormData(newKeyedFormData), newErrorSchema));\n            };\n        };\n        /** Callback handler for when the user clicks on the remove button on an existing array element. Removes the row of\n         * keyed form data at the `index` in the state, and then returning `onChange()` with the plain form data converted\n         * from the keyed data\n         *\n         * @param index - The index at which the remove button is clicked\n         */\n        this.onDropIndexClick = (index) => {\n            return (event) => {\n                if (event) {\n                    event.preventDefault();\n                }\n                const { onChange, errorSchema } = this.props;\n                const { keyedFormData } = this.state;\n                // refs #195: revalidate to ensure properly reindexing errors\n                let newErrorSchema;\n                if (errorSchema) {\n                    newErrorSchema = {};\n                    for (const idx in errorSchema) {\n                        const i = parseInt(idx);\n                        if (i < index) {\n                            set(newErrorSchema, [i], errorSchema[idx]);\n                        }\n                        else if (i > index) {\n                            set(newErrorSchema, [i - 1], errorSchema[idx]);\n                        }\n                    }\n                }\n                const newKeyedFormData = keyedFormData.filter((_, i) => i !== index);\n                this.setState({\n                    keyedFormData: newKeyedFormData,\n                    updatedKeyedFormData: true,\n                }, () => onChange(keyedToPlainFormData(newKeyedFormData), newErrorSchema));\n            };\n        };\n        /** Callback handler for when the user clicks on one of the move item buttons on an existing array element. Moves the\n         * row of keyed form data at the `index` to the `newIndex` in the state, and then returning `onChange()` with the\n         * plain form data converted from the keyed data\n         *\n         * @param index - The index of the item to move\n         * @param newIndex - The index to where the item is to be moved\n         */\n        this.onReorderClick = (index, newIndex) => {\n            return (event) => {\n                if (event) {\n                    event.preventDefault();\n                    event.currentTarget.blur();\n                }\n                const { onChange, errorSchema } = this.props;\n                let newErrorSchema;\n                if (errorSchema) {\n                    newErrorSchema = {};\n                    for (const idx in errorSchema) {\n                        const i = parseInt(idx);\n                        if (i == index) {\n                            set(newErrorSchema, [newIndex], errorSchema[index]);\n                        }\n                        else if (i == newIndex) {\n                            set(newErrorSchema, [index], errorSchema[newIndex]);\n                        }\n                        else {\n                            set(newErrorSchema, [idx], errorSchema[i]);\n                        }\n                    }\n                }\n                const { keyedFormData } = this.state;\n                function reOrderArray() {\n                    // Copy item\n                    const _newKeyedFormData = keyedFormData.slice();\n                    // Moves item from index to newIndex\n                    _newKeyedFormData.splice(index, 1);\n                    _newKeyedFormData.splice(newIndex, 0, keyedFormData[index]);\n                    return _newKeyedFormData;\n                }\n                const newKeyedFormData = reOrderArray();\n                this.setState({\n                    keyedFormData: newKeyedFormData,\n                }, () => onChange(keyedToPlainFormData(newKeyedFormData), newErrorSchema));\n            };\n        };\n        /** Callback handler used to deal with changing the value of the data in the array at the `index`. Calls the\n         * `onChange` callback with the updated form data\n         *\n         * @param index - The index of the item being changed\n         */\n        this.onChangeForIndex = (index) => {\n            return (value, newErrorSchema, id) => {\n                const { formData, onChange, errorSchema } = this.props;\n                const arrayData = Array.isArray(formData) ? formData : [];\n                const newFormData = arrayData.map((item, i) => {\n                    // We need to treat undefined items as nulls to have validation.\n                    // See https://github.com/tdegrunt/jsonschema/issues/206\n                    const jsonValue = typeof value === 'undefined' ? null : value;\n                    return index === i ? jsonValue : item;\n                });\n                onChange(newFormData, errorSchema &&\n                    errorSchema && {\n                    ...errorSchema,\n                    [index]: newErrorSchema,\n                }, id);\n            };\n        };\n        /** Callback handler used to change the value for a checkbox */\n        this.onSelectChange = (value) => {\n            const { onChange, idSchema } = this.props;\n            onChange(value, undefined, idSchema && idSchema.$id);\n        };\n        const { formData = [] } = props;\n        const keyedFormData = generateKeyedFormData(formData);\n        this.state = {\n            keyedFormData,\n            updatedKeyedFormData: false,\n        };\n    }\n    /** React lifecycle method that is called when the props are about to change allowing the state to be updated. It\n     * regenerates the keyed form data and returns it\n     *\n     * @param nextProps - The next set of props data\n     * @param prevState - The previous set of state data\n     */\n    static getDerivedStateFromProps(nextProps, prevState) {\n        // Don't call getDerivedStateFromProps if keyed formdata was just updated.\n        if (prevState.updatedKeyedFormData) {\n            return {\n                updatedKeyedFormData: false,\n            };\n        }\n        const nextFormData = Array.isArray(nextProps.formData) ? nextProps.formData : [];\n        const previousKeyedFormData = prevState.keyedFormData || [];\n        const newKeyedFormData = nextFormData.length === previousKeyedFormData.length\n            ? previousKeyedFormData.map((previousKeyedFormDatum, index) => {\n                return {\n                    key: previousKeyedFormDatum.key,\n                    item: nextFormData[index],\n                };\n            })\n            : generateKeyedFormData(nextFormData);\n        return {\n            keyedFormData: newKeyedFormData,\n        };\n    }\n    /** Returns the appropriate title for an item by getting first the title from the schema.items, then falling back to\n     * the description from the schema.items, and finally the string \"Item\"\n     */\n    get itemTitle() {\n        const { schema, registry } = this.props;\n        const { translateString } = registry;\n        return get(schema, [ITEMS_KEY, 'title'], get(schema, [ITEMS_KEY, 'description'], translateString(TranslatableString.ArrayItemTitle)));\n    }\n    /** Determines whether the item described in the schema is always required, which is determined by whether any item\n     * may be null.\n     *\n     * @param itemSchema - The schema for the item\n     * @return - True if the item schema type does not contain the \"null\" type\n     */\n    isItemRequired(itemSchema) {\n        if (Array.isArray(itemSchema.type)) {\n            // While we don't yet support composite/nullable jsonschema types, it's\n            // future-proof to check for requirement against these.\n            return !itemSchema.type.includes('null');\n        }\n        // All non-null array item types are inherently required by design\n        return itemSchema.type !== 'null';\n    }\n    /** Determines whether more items can be added to the array. If the uiSchema indicates the array doesn't allow adding\n     * then false is returned. Otherwise, if the schema indicates that there are a maximum number of items and the\n     * `formData` matches that value, then false is returned, otherwise true is returned.\n     *\n     * @param formItems - The list of items in the form\n     * @returns - True if the item is addable otherwise false\n     */\n    canAddItem(formItems) {\n        const { schema, uiSchema, registry } = this.props;\n        let { addable } = getUiOptions(uiSchema, registry.globalUiOptions);\n        if (addable !== false) {\n            // if ui:options.addable was not explicitly set to false, we can add\n            // another item if we have not exceeded maxItems yet\n            if (schema.maxItems !== undefined) {\n                addable = formItems.length < schema.maxItems;\n            }\n            else {\n                addable = true;\n            }\n        }\n        return addable;\n    }\n    /** Callback handler for when the user clicks on the add or add at index buttons. Creates a new row of keyed form data\n     * either at the end of the list (when index is not specified) or inserted at the `index` when it is, adding it into\n     * the state, and then returning `onChange()` with the plain form data converted from the keyed data\n     *\n     * @param event - The event for the click\n     * @param [index] - The optional index at which to add the new data\n     */\n    _handleAddClick(event, index) {\n        if (event) {\n            event.preventDefault();\n        }\n        const { onChange, errorSchema } = this.props;\n        const { keyedFormData } = this.state;\n        // refs #195: revalidate to ensure properly reindexing errors\n        let newErrorSchema;\n        if (errorSchema) {\n            newErrorSchema = {};\n            for (const idx in errorSchema) {\n                const i = parseInt(idx);\n                if (index === undefined || i < index) {\n                    set(newErrorSchema, [i], errorSchema[idx]);\n                }\n                else if (i >= index) {\n                    set(newErrorSchema, [i + 1], errorSchema[idx]);\n                }\n            }\n        }\n        const newKeyedFormDataRow = {\n            key: generateRowId(),\n            item: this._getNewFormDataRow(),\n        };\n        const newKeyedFormData = [...keyedFormData];\n        if (index !== undefined) {\n            newKeyedFormData.splice(index, 0, newKeyedFormDataRow);\n        }\n        else {\n            newKeyedFormData.push(newKeyedFormDataRow);\n        }\n        this.setState({\n            keyedFormData: newKeyedFormData,\n            updatedKeyedFormData: true,\n        }, () => onChange(keyedToPlainFormData(newKeyedFormData), newErrorSchema));\n    }\n    /** Renders the `ArrayField` depending on the specific needs of the schema and uischema elements\n     */\n    render() {\n        const { schema, uiSchema, idSchema, registry } = this.props;\n        const { schemaUtils, translateString } = registry;\n        if (!(ITEMS_KEY in schema)) {\n            const uiOptions = getUiOptions(uiSchema);\n            const UnsupportedFieldTemplate = getTemplate('UnsupportedFieldTemplate', registry, uiOptions);\n            return (_jsx(UnsupportedFieldTemplate, { schema: schema, idSchema: idSchema, reason: translateString(TranslatableString.MissingItems), registry: registry }));\n        }\n        if (schemaUtils.isMultiSelect(schema)) {\n            // If array has enum or uniqueItems set to true, call renderMultiSelect() to render the default multiselect widget or a custom widget, if specified.\n            return this.renderMultiSelect();\n        }\n        if (isCustomWidget(uiSchema)) {\n            return this.renderCustomWidget();\n        }\n        if (isFixedItems(schema)) {\n            return this.renderFixedArray();\n        }\n        if (schemaUtils.isFilesArray(schema, uiSchema)) {\n            return this.renderFiles();\n        }\n        return this.renderNormalArray();\n    }\n    /** Renders a normal array without any limitations of length\n     */\n    renderNormalArray() {\n        const { schema, uiSchema = {}, errorSchema, idSchema, name, disabled = false, readonly = false, autofocus = false, required = false, registry, onBlur, onFocus, idPrefix, idSeparator = '_', rawErrors, } = this.props;\n        const { keyedFormData } = this.state;\n        const title = schema.title === undefined ? name : schema.title;\n        const { schemaUtils, formContext } = registry;\n        const uiOptions = getUiOptions(uiSchema);\n        const _schemaItems = isObject(schema.items) ? schema.items : {};\n        const itemsSchema = schemaUtils.retrieveSchema(_schemaItems);\n        const formData = keyedToPlainFormData(this.state.keyedFormData);\n        const canAdd = this.canAddItem(formData);\n        const arrayProps = {\n            canAdd,\n            items: keyedFormData.map((keyedItem, index) => {\n                const { key, item } = keyedItem;\n                // While we are actually dealing with a single item of type T, the types require a T[], so cast\n                const itemCast = item;\n                const itemSchema = schemaUtils.retrieveSchema(_schemaItems, itemCast);\n                const itemErrorSchema = errorSchema ? errorSchema[index] : undefined;\n                const itemIdPrefix = idSchema.$id + idSeparator + index;\n                const itemIdSchema = schemaUtils.toIdSchema(itemSchema, itemIdPrefix, itemCast, idPrefix, idSeparator);\n                return this.renderArrayFieldItem({\n                    key,\n                    index,\n                    name: name && `${name}-${index}`,\n                    canAdd,\n                    canMoveUp: index > 0,\n                    canMoveDown: index < formData.length - 1,\n                    itemSchema,\n                    itemIdSchema,\n                    itemErrorSchema,\n                    itemData: itemCast,\n                    itemUiSchema: uiSchema.items,\n                    autofocus: autofocus && index === 0,\n                    onBlur,\n                    onFocus,\n                    rawErrors,\n                    totalItems: keyedFormData.length,\n                });\n            }),\n            className: `field field-array field-array-of-${itemsSchema.type}`,\n            disabled,\n            idSchema,\n            uiSchema,\n            onAddClick: this.onAddClick,\n            readonly,\n            required,\n            schema,\n            title,\n            formContext,\n            formData,\n            rawErrors,\n            registry,\n        };\n        const Template = getTemplate('ArrayFieldTemplate', registry, uiOptions);\n        return _jsx(Template, { ...arrayProps });\n    }\n    /** Renders an array using the custom widget provided by the user in the `uiSchema`\n     */\n    renderCustomWidget() {\n        var _a;\n        const { schema, idSchema, uiSchema, disabled = false, readonly = false, autofocus = false, required = false, hideError, placeholder, onBlur, onFocus, formData: items = [], registry, rawErrors, name, } = this.props;\n        const { widgets, formContext, globalUiOptions, schemaUtils } = registry;\n        const { widget, title: uiTitle, ...options } = getUiOptions(uiSchema, globalUiOptions);\n        const Widget = getWidget(schema, widget, widgets);\n        const label = (_a = uiTitle !== null && uiTitle !== void 0 ? uiTitle : schema.title) !== null && _a !== void 0 ? _a : name;\n        const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);\n        return (_jsx(Widget, { id: idSchema.$id, name: name, multiple: true, onChange: this.onSelectChange, onBlur: onBlur, onFocus: onFocus, options: options, schema: schema, uiSchema: uiSchema, registry: registry, value: items, disabled: disabled, readonly: readonly, hideError: hideError, required: required, label: label, hideLabel: !displayLabel, placeholder: placeholder, formContext: formContext, autofocus: autofocus, rawErrors: rawErrors }));\n    }\n    /** Renders an array as a set of checkboxes\n     */\n    renderMultiSelect() {\n        var _a;\n        const { schema, idSchema, uiSchema, formData: items = [], disabled = false, readonly = false, autofocus = false, required = false, placeholder, onBlur, onFocus, registry, rawErrors, name, } = this.props;\n        const { widgets, schemaUtils, formContext, globalUiOptions } = registry;\n        const itemsSchema = schemaUtils.retrieveSchema(schema.items, items);\n        const enumOptions = optionsList(itemsSchema);\n        const { widget = 'select', title: uiTitle, ...options } = getUiOptions(uiSchema, globalUiOptions);\n        const Widget = getWidget(schema, widget, widgets);\n        const label = (_a = uiTitle !== null && uiTitle !== void 0 ? uiTitle : schema.title) !== null && _a !== void 0 ? _a : name;\n        const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);\n        return (_jsx(Widget, { id: idSchema.$id, name: name, multiple: true, onChange: this.onSelectChange, onBlur: onBlur, onFocus: onFocus, options: { ...options, enumOptions }, schema: schema, uiSchema: uiSchema, registry: registry, value: items, disabled: disabled, readonly: readonly, required: required, label: label, hideLabel: !displayLabel, placeholder: placeholder, formContext: formContext, autofocus: autofocus, rawErrors: rawErrors }));\n    }\n    /** Renders an array of files using the `FileWidget`\n     */\n    renderFiles() {\n        var _a;\n        const { schema, uiSchema, idSchema, name, disabled = false, readonly = false, autofocus = false, required = false, onBlur, onFocus, registry, formData: items = [], rawErrors, } = this.props;\n        const { widgets, formContext, globalUiOptions, schemaUtils } = registry;\n        const { widget = 'files', title: uiTitle, ...options } = getUiOptions(uiSchema, globalUiOptions);\n        const Widget = getWidget(schema, widget, widgets);\n        const label = (_a = uiTitle !== null && uiTitle !== void 0 ? uiTitle : schema.title) !== null && _a !== void 0 ? _a : name;\n        const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);\n        return (_jsx(Widget, { options: options, id: idSchema.$id, name: name, multiple: true, onChange: this.onSelectChange, onBlur: onBlur, onFocus: onFocus, schema: schema, uiSchema: uiSchema, value: items, disabled: disabled, readonly: readonly, required: required, registry: registry, formContext: formContext, autofocus: autofocus, rawErrors: rawErrors, label: label, hideLabel: !displayLabel }));\n    }\n    /** Renders an array that has a maximum limit of items\n     */\n    renderFixedArray() {\n        const { schema, uiSchema = {}, formData = [], errorSchema, idPrefix, idSeparator = '_', idSchema, name, disabled = false, readonly = false, autofocus = false, required = false, registry, onBlur, onFocus, rawErrors, } = this.props;\n        const { keyedFormData } = this.state;\n        let { formData: items = [] } = this.props;\n        const title = schema.title || name;\n        const uiOptions = getUiOptions(uiSchema);\n        const { schemaUtils, formContext } = registry;\n        const _schemaItems = isObject(schema.items) ? schema.items : [];\n        const itemSchemas = _schemaItems.map((item, index) => schemaUtils.retrieveSchema(item, formData[index]));\n        const additionalSchema = isObject(schema.additionalItems)\n            ? schemaUtils.retrieveSchema(schema.additionalItems, formData)\n            : null;\n        if (!items || items.length < itemSchemas.length) {\n            // to make sure at least all fixed items are generated\n            items = items || [];\n            items = items.concat(new Array(itemSchemas.length - items.length));\n        }\n        // These are the props passed into the render function\n        const canAdd = this.canAddItem(items) && !!additionalSchema;\n        const arrayProps = {\n            canAdd,\n            className: 'field field-array field-array-fixed-items',\n            disabled,\n            idSchema,\n            formData,\n            items: keyedFormData.map((keyedItem, index) => {\n                const { key, item } = keyedItem;\n                // While we are actually dealing with a single item of type T, the types require a T[], so cast\n                const itemCast = item;\n                const additional = index >= itemSchemas.length;\n                const itemSchema = (additional && isObject(schema.additionalItems)\n                    ? schemaUtils.retrieveSchema(schema.additionalItems, itemCast)\n                    : itemSchemas[index]) || {};\n                const itemIdPrefix = idSchema.$id + idSeparator + index;\n                const itemIdSchema = schemaUtils.toIdSchema(itemSchema, itemIdPrefix, itemCast, idPrefix, idSeparator);\n                const itemUiSchema = additional\n                    ? uiSchema.additionalItems || {}\n                    : Array.isArray(uiSchema.items)\n                        ? uiSchema.items[index]\n                        : uiSchema.items || {};\n                const itemErrorSchema = errorSchema ? errorSchema[index] : undefined;\n                return this.renderArrayFieldItem({\n                    key,\n                    index,\n                    name: name && `${name}-${index}`,\n                    canAdd,\n                    canRemove: additional,\n                    canMoveUp: index >= itemSchemas.length + 1,\n                    canMoveDown: additional && index < items.length - 1,\n                    itemSchema,\n                    itemData: itemCast,\n                    itemUiSchema,\n                    itemIdSchema,\n                    itemErrorSchema,\n                    autofocus: autofocus && index === 0,\n                    onBlur,\n                    onFocus,\n                    rawErrors,\n                    totalItems: keyedFormData.length,\n                });\n            }),\n            onAddClick: this.onAddClick,\n            readonly,\n            required,\n            registry,\n            schema,\n            uiSchema,\n            title,\n            formContext,\n            rawErrors,\n        };\n        const Template = getTemplate('ArrayFieldTemplate', registry, uiOptions);\n        return _jsx(Template, { ...arrayProps });\n    }\n    /** Renders the individual array item using a `SchemaField` along with the additional properties required to be send\n     * back to the `ArrayFieldItemTemplate`.\n     *\n     * @param props - The props for the individual array item to be rendered\n     */\n    renderArrayFieldItem(props) {\n        const { key, index, name, canAdd, canRemove = true, canMoveUp, canMoveDown, itemSchema, itemData, itemUiSchema, itemIdSchema, itemErrorSchema, autofocus, onBlur, onFocus, rawErrors, totalItems, } = props;\n        const { disabled, hideError, idPrefix, idSeparator, readonly, uiSchema, registry, formContext } = this.props;\n        const { fields: { ArraySchemaField, SchemaField }, globalUiOptions, } = registry;\n        const ItemSchemaField = ArraySchemaField || SchemaField;\n        const { orderable = true, removable = true, copyable = false } = getUiOptions(uiSchema, globalUiOptions);\n        const has = {\n            moveUp: orderable && canMoveUp,\n            moveDown: orderable && canMoveDown,\n            copy: copyable && canAdd,\n            remove: removable && canRemove,\n            toolbar: false,\n        };\n        has.toolbar = Object.keys(has).some((key) => has[key]);\n        return {\n            children: (_jsx(ItemSchemaField, { name: name, index: index, schema: itemSchema, uiSchema: itemUiSchema, formData: itemData, formContext: formContext, errorSchema: itemErrorSchema, idPrefix: idPrefix, idSeparator: idSeparator, idSchema: itemIdSchema, required: this.isItemRequired(itemSchema), onChange: this.onChangeForIndex(index), onBlur: onBlur, onFocus: onFocus, registry: registry, disabled: disabled, readonly: readonly, hideError: hideError, autofocus: autofocus, rawErrors: rawErrors })),\n            className: 'array-item',\n            disabled,\n            canAdd,\n            hasCopy: has.copy,\n            hasToolbar: has.toolbar,\n            hasMoveUp: has.moveUp,\n            hasMoveDown: has.moveDown,\n            hasRemove: has.remove,\n            index,\n            totalItems,\n            key,\n            onAddIndexClick: this.onAddIndexClick,\n            onCopyIndexClick: this.onCopyIndexClick,\n            onDropIndexClick: this.onDropIndexClick,\n            onReorderClick: this.onReorderClick,\n            readonly,\n            registry,\n            schema: itemSchema,\n            uiSchema: itemUiSchema,\n        };\n    }\n}\n/** `ArrayField` is `React.ComponentType<FieldProps<T[], S, F>>` (necessarily) but the `registry` requires things to be a\n * `Field` which is defined as `React.ComponentType<FieldProps<T, S, F>>`, so cast it to make `registry` happy.\n */\nexport default ArrayField;\n//# sourceMappingURL=ArrayField.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getWidget, getUiOptions, optionsList, TranslatableString, } from '@rjsf/utils';\nimport isObject from 'lodash/isObject';\n/** The `BooleanField` component is used to render a field in the schema is boolean. It constructs `enumOptions` for the\n * two boolean values based on the various alternatives in the schema.\n *\n * @param props - The `FieldProps` for this template\n */\nfunction BooleanField(props) {\n    var _a, _b;\n    const { schema, name, uiSchema, idSchema, formData, registry, required, disabled, readonly, hideError, autofocus, onChange, onFocus, onBlur, rawErrors, } = props;\n    const { title } = schema;\n    const { widgets, formContext, translateString, globalUiOptions } = registry;\n    const { widget = 'checkbox', title: uiTitle, \n    // Unlike the other fields, don't use `getDisplayLabel()` since it always returns false for the boolean type\n    label: displayLabel = true, ...options } = getUiOptions(uiSchema, globalUiOptions);\n    const Widget = getWidget(schema, widget, widgets);\n    const yes = translateString(TranslatableString.YesLabel);\n    const no = translateString(TranslatableString.NoLabel);\n    let enumOptions;\n    const label = (_a = uiTitle !== null && uiTitle !== void 0 ? uiTitle : title) !== null && _a !== void 0 ? _a : name;\n    if (Array.isArray(schema.oneOf)) {\n        enumOptions = optionsList({\n            oneOf: schema.oneOf\n                .map((option) => {\n                if (isObject(option)) {\n                    return {\n                        ...option,\n                        title: option.title || (option.const === true ? yes : no),\n                    };\n                }\n                return undefined;\n            })\n                .filter((o) => o), // cast away the error that typescript can't grok is fixed\n        });\n    }\n    else {\n        // We deprecated enumNames in v5. It's intentionally omitted from RSJFSchema type, so we need to cast here.\n        const schemaWithEnumNames = schema;\n        const enums = (_b = schema.enum) !== null && _b !== void 0 ? _b : [true, false];\n        if (!schemaWithEnumNames.enumNames && enums.length === 2 && enums.every((v) => typeof v === 'boolean')) {\n            enumOptions = [\n                {\n                    value: enums[0],\n                    label: enums[0] ? yes : no,\n                },\n                {\n                    value: enums[1],\n                    label: enums[1] ? yes : no,\n                },\n            ];\n        }\n        else {\n            enumOptions = optionsList({\n                enum: enums,\n                // NOTE: enumNames is deprecated, but still supported for now.\n                enumNames: schemaWithEnumNames.enumNames,\n            });\n        }\n    }\n    return (_jsx(Widget, { options: { ...options, enumOptions }, schema: schema, uiSchema: uiSchema, id: idSchema.$id, name: name, onChange: onChange, onFocus: onFocus, onBlur: onBlur, label: label, hideLabel: !displayLabel, value: formData, required: required, disabled: disabled, readonly: readonly, hideError: hideError, registry: registry, formContext: formContext, autofocus: autofocus, rawErrors: rawErrors }));\n}\nexport default BooleanField;\n//# sourceMappingURL=BooleanField.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Component } from 'react';\nimport get from 'lodash/get';\nimport isEmpty from 'lodash/isEmpty';\nimport omit from 'lodash/omit';\nimport { deepEquals, ERRORS_KEY, getDiscriminatorFieldFromSchema, getUiOptions, getWidget, mergeSchemas, TranslatableString, } from '@rjsf/utils';\n/** The `AnyOfField` component is used to render a field in the schema that is an `anyOf`, `allOf` or `oneOf`. It tracks\n * the currently selected option and cleans up any irrelevant data in `formData`.\n *\n * @param props - The `FieldProps` for this template\n */\nclass AnyOfField extends Component {\n    /** Constructs an `AnyOfField` with the given `props` to initialize the initially selected option in state\n     *\n     * @param props - The `FieldProps` for this template\n     */\n    constructor(props) {\n        super(props);\n        /** Callback handler to remember what the currently selected option is. In addition to that the `formData` is updated\n         * to remove properties that are not part of the newly selected option schema, and then the updated data is passed to\n         * the `onChange` handler.\n         *\n         * @param option - The new option value being selected\n         */\n        this.onOptionChange = (option) => {\n            const { selectedOption, retrievedOptions } = this.state;\n            const { formData, onChange, registry } = this.props;\n            const { schemaUtils } = registry;\n            const intOption = option !== undefined ? parseInt(option, 10) : -1;\n            if (intOption === selectedOption) {\n                return;\n            }\n            const newOption = intOption >= 0 ? retrievedOptions[intOption] : undefined;\n            const oldOption = selectedOption >= 0 ? retrievedOptions[selectedOption] : undefined;\n            let newFormData = schemaUtils.sanitizeDataForNewSchema(newOption, oldOption, formData);\n            if (newFormData && newOption) {\n                // Call getDefaultFormState to make sure defaults are populated on change. Pass \"excludeObjectChildren\"\n                // so that only the root objects themselves are created without adding undefined children properties\n                newFormData = schemaUtils.getDefaultFormState(newOption, newFormData, 'excludeObjectChildren');\n            }\n            onChange(newFormData, undefined, this.getFieldId());\n            this.setState({ selectedOption: intOption });\n        };\n        const { formData, options, registry: { schemaUtils }, } = this.props;\n        // cache the retrieved options in state in case they have $refs to save doing it later\n        const retrievedOptions = options.map((opt) => schemaUtils.retrieveSchema(opt, formData));\n        this.state = {\n            retrievedOptions,\n            selectedOption: this.getMatchingOption(0, formData, retrievedOptions),\n        };\n    }\n    /** React lifecycle method that is called when the props and/or state for this component is updated. It recomputes the\n     * currently selected option based on the overall `formData`\n     *\n     * @param prevProps - The previous `FieldProps` for this template\n     * @param prevState - The previous `AnyOfFieldState` for this template\n     */\n    componentDidUpdate(prevProps, prevState) {\n        const { formData, options, idSchema } = this.props;\n        const { selectedOption } = this.state;\n        let newState = this.state;\n        if (!deepEquals(prevProps.options, options)) {\n            const { registry: { schemaUtils }, } = this.props;\n            // re-cache the retrieved options in state in case they have $refs to save doing it later\n            const retrievedOptions = options.map((opt) => schemaUtils.retrieveSchema(opt, formData));\n            newState = { selectedOption, retrievedOptions };\n        }\n        if (!deepEquals(formData, prevProps.formData) && idSchema.$id === prevProps.idSchema.$id) {\n            const { retrievedOptions } = newState;\n            const matchingOption = this.getMatchingOption(selectedOption, formData, retrievedOptions);\n            if (prevState && matchingOption !== selectedOption) {\n                newState = { selectedOption: matchingOption, retrievedOptions };\n            }\n        }\n        if (newState !== this.state) {\n            this.setState(newState);\n        }\n    }\n    /** Determines the best matching option for the given `formData` and `options`.\n     *\n     * @param formData - The new formData\n     * @param options - The list of options to choose from\n     * @return - The index of the `option` that best matches the `formData`\n     */\n    getMatchingOption(selectedOption, formData, options) {\n        const { schema, registry: { schemaUtils }, } = this.props;\n        const discriminator = getDiscriminatorFieldFromSchema(schema);\n        const option = schemaUtils.getClosestMatchingOption(formData, options, selectedOption, discriminator);\n        return option;\n    }\n    getFieldId() {\n        const { idSchema, schema } = this.props;\n        return `${idSchema.$id}${schema.oneOf ? '__oneof_select' : '__anyof_select'}`;\n    }\n    /** Renders the `AnyOfField` selector along with a `SchemaField` for the value of the `formData`\n     */\n    render() {\n        const { name, disabled = false, errorSchema = {}, formContext, onBlur, onFocus, registry, schema, uiSchema, } = this.props;\n        const { widgets, fields, translateString, globalUiOptions, schemaUtils } = registry;\n        const { SchemaField: _SchemaField } = fields;\n        const { selectedOption, retrievedOptions } = this.state;\n        const { widget = 'select', placeholder, autofocus, autocomplete, title = schema.title, ...uiOptions } = getUiOptions(uiSchema, globalUiOptions);\n        const Widget = getWidget({ type: 'number' }, widget, widgets);\n        const rawErrors = get(errorSchema, ERRORS_KEY, []);\n        const fieldErrorSchema = omit(errorSchema, [ERRORS_KEY]);\n        const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);\n        const option = selectedOption >= 0 ? retrievedOptions[selectedOption] || null : null;\n        let optionSchema;\n        if (option) {\n            // merge top level required field\n            const { required } = schema;\n            // Merge in all the non-oneOf/anyOf properties and also skip the special ADDITIONAL_PROPERTY_FLAG property\n            optionSchema = required ? mergeSchemas({ required }, option) : option;\n        }\n        const translateEnum = title\n            ? TranslatableString.TitleOptionPrefix\n            : TranslatableString.OptionPrefix;\n        const translateParams = title ? [title] : [];\n        const enumOptions = retrievedOptions.map((opt, index) => ({\n            label: opt.title || translateString(translateEnum, translateParams.concat(String(index + 1))),\n            value: index,\n        }));\n        return (_jsxs(\"div\", { className: 'panel panel-default panel-body', children: [_jsx(\"div\", { className: 'form-group', children: _jsx(Widget, { id: this.getFieldId(), name: `${name}${schema.oneOf ? '__oneof_select' : '__anyof_select'}`, schema: { type: 'number', default: 0 }, onChange: this.onOptionChange, onBlur: onBlur, onFocus: onFocus, disabled: disabled || isEmpty(enumOptions), multiple: false, rawErrors: rawErrors, errorSchema: fieldErrorSchema, value: selectedOption >= 0 ? selectedOption : undefined, options: { enumOptions, ...uiOptions }, registry: registry, formContext: formContext, placeholder: placeholder, autocomplete: autocomplete, autofocus: autofocus, label: title !== null && title !== void 0 ? title : name, hideLabel: !displayLabel }) }), option !== null && _jsx(_SchemaField, { ...this.props, schema: optionSchema })] }));\n    }\n}\nexport default AnyOfField;\n//# sourceMappingURL=MultiSchemaField.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { useState, useCallback } from 'react';\nimport { asNumber } from '@rjsf/utils';\n// Matches a string that ends in a . character, optionally followed by a sequence of\n// digits followed by any number of 0 characters up until the end of the line.\n// Ensuring that there is at least one prefixed character is important so that\n// you don't incorrectly match against \"0\".\nconst trailingCharMatcherWithPrefix = /\\.([0-9]*0)*$/;\n// This is used for trimming the trailing 0 and . characters without affecting\n// the rest of the string. Its possible to use one RegEx with groups for this\n// functionality, but it is fairly complex compared to simply defining two\n// different matchers.\nconst trailingCharMatcher = /[0.]0*$/;\n/**\n * The NumberField class has some special handling for dealing with trailing\n * decimal points and/or zeroes. This logic is designed to allow trailing values\n * to be visible in the input element, but not be represented in the\n * corresponding form data.\n *\n * The algorithm is as follows:\n *\n * 1. When the input value changes the value is cached in the component state\n *\n * 2. The value is then normalized, removing trailing decimal points and zeros,\n *    then passed to the \"onChange\" callback\n *\n * 3. When the component is rendered, the formData value is checked against the\n *    value cached in the state. If it matches the cached value, the cached\n *    value is passed to the input instead of the formData value\n */\nfunction NumberField(props) {\n    const { registry, onChange, formData, value: initialValue } = props;\n    const [lastValue, setLastValue] = useState(initialValue);\n    const { StringField } = registry.fields;\n    let value = formData;\n    /** Handle the change from the `StringField` to properly convert to a number\n     *\n     * @param value - The current value for the change occurring\n     */\n    const handleChange = useCallback((value) => {\n        // Cache the original value in component state\n        setLastValue(value);\n        // Normalize decimals that don't start with a zero character in advance so\n        // that the rest of the normalization logic is simpler\n        if (`${value}`.charAt(0) === '.') {\n            value = `0${value}`;\n        }\n        // Check that the value is a string (this can happen if the widget used is a\n        // <select>, due to an enum declaration etc) then, if the value ends in a\n        // trailing decimal point or multiple zeroes, strip the trailing values\n        const processed = typeof value === 'string' && value.match(trailingCharMatcherWithPrefix)\n            ? asNumber(value.replace(trailingCharMatcher, ''))\n            : asNumber(value);\n        onChange(processed);\n    }, [onChange]);\n    if (typeof lastValue === 'string' && typeof value === 'number') {\n        // Construct a regular expression that checks for a string that consists\n        // of the formData value suffixed with zero or one '.' characters and zero\n        // or more '0' characters\n        const re = new RegExp(`${value}`.replace('.', '\\\\.') + '\\\\.?0*$');\n        // If the cached \"lastValue\" is a match, use that instead of the formData\n        // value to prevent the input value from changing in the UI\n        if (lastValue.match(re)) {\n            value = lastValue;\n        }\n    }\n    return _jsx(StringField, { ...props, formData: value, onChange: handleChange });\n}\nexport default NumberField;\n//# sourceMappingURL=NumberField.js.map", "import*as e from\"react\";function t(){return t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},t.apply(this,arguments)}const n=[\"children\",\"options\"],r={blockQuote:\"0\",breakLine:\"1\",breakThematic:\"2\",codeBlock:\"3\",codeFenced:\"4\",codeInline:\"5\",footnote:\"6\",footnoteReference:\"7\",gfmTask:\"8\",heading:\"9\",headingSetext:\"10\",htmlBlock:\"11\",htmlComment:\"12\",htmlSelfClosing:\"13\",image:\"14\",link:\"15\",linkAngleBraceStyleDetector:\"16\",linkBareUrlDetector:\"17\",linkMailtoDetector:\"18\",newlineCoalescer:\"19\",orderedList:\"20\",paragraph:\"21\",ref:\"22\",refImage:\"23\",refLink:\"24\",table:\"25\",tableSeparator:\"26\",text:\"27\",textBolded:\"28\",textEmphasized:\"29\",textEscaped:\"30\",textMarked:\"31\",textStrikethroughed:\"32\",unorderedList:\"33\"};var i;!function(e){e[e.MAX=0]=\"MAX\",e[e.HIGH=1]=\"HIGH\",e[e.MED=2]=\"MED\",e[e.LOW=3]=\"LOW\",e[e.MIN=4]=\"MIN\"}(i||(i={}));const l=[\"allowFullScreen\",\"allowTransparency\",\"autoComplete\",\"autoFocus\",\"autoPlay\",\"cellPadding\",\"cellSpacing\",\"charSet\",\"classId\",\"colSpan\",\"contentEditable\",\"contextMenu\",\"crossOrigin\",\"encType\",\"formAction\",\"formEncType\",\"formMethod\",\"formNoValidate\",\"formTarget\",\"frameBorder\",\"hrefLang\",\"inputMode\",\"keyParams\",\"keyType\",\"marginHeight\",\"marginWidth\",\"maxLength\",\"mediaGroup\",\"minLength\",\"noValidate\",\"radioGroup\",\"readOnly\",\"rowSpan\",\"spellCheck\",\"srcDoc\",\"srcLang\",\"srcSet\",\"tabIndex\",\"useMap\"].reduce((e,t)=>(e[t.toLowerCase()]=t,e),{class:\"className\",for:\"htmlFor\"}),a={amp:\"&\",apos:\"'\",gt:\">\",lt:\"<\",nbsp:\" \",quot:\"“\"},o=[\"style\",\"script\"],c=/([-A-Z0-9_:]+)(?:\\s*=\\s*(?:(?:\"((?:\\\\.|[^\"])*)\")|(?:'((?:\\\\.|[^'])*)')|(?:\\{((?:\\\\.|{[^}]*?}|[^}])*)\\})))?/gi,s=/mailto:/i,d=/\\n{2,}$/,u=/^(\\s*>[\\s\\S]*?)(?=\\n\\n|$)/,p=/^ *> ?/gm,f=/^(?:\\[!([^\\]]*)\\]\\n)?([\\s\\S]*)/,h=/^ {2,}\\n/,m=/^(?:( *[-*_])){3,} *(?:\\n *)+\\n/,g=/^(?: {1,3})?(`{3,}|~{3,}) *(\\S+)? *([^\\n]*?)?\\n([\\s\\S]*?)(?:\\1\\n?|$)/,y=/^(?: {4}[^\\n]+\\n*)+(?:\\n *)+\\n?/,k=/^(`+)((?:\\\\`|[^`])+)\\1/,x=/^(?:\\n *)*\\n/,b=/\\r\\n?/g,v=/^\\[\\^([^\\]]+)](:(.*)((\\n+ {4,}.*)|(\\n(?!\\[\\^).+))*)/,C=/^\\[\\^([^\\]]+)]/,$=/\\f/g,S=/^---[ \\t]*\\n(.|\\n)*\\n---[ \\t]*\\n/,w=/^\\s*?\\[(x|\\s)\\]/,E=/^ *(#{1,6}) *([^\\n]+?)(?: +#*)?(?:\\n *)*(?:\\n|$)/,z=/^ *(#{1,6}) +([^\\n]+?)(?: +#*)?(?:\\n *)*(?:\\n|$)/,L=/^([^\\n]+)\\n *(=|-){3,} *(?:\\n *)+\\n/,A=/^ *(?!<[a-z][^ >/]* ?\\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\\n?(\\s*(?:<\\1[^>]*?>[\\s\\S]*?<\\/\\1>|(?!<\\1\\b)[\\s\\S])*?)<\\/\\1>(?!<\\/\\1>)\\n*/i,T=/&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi,B=/^<!--[\\s\\S]*?(?:-->)/,O=/^(data|aria|x)-[a-z_][a-z\\d_.-]*$/,M=/^ *<([a-z][a-z0-9:]*)(?:\\s+((?:<.*?>|[^>])*))?\\/?>(?!<\\/\\1>)(\\s*\\n)?/i,R=/^\\{.*\\}$/,I=/^(https?:\\/\\/[^\\s<]+[^<.,:;\"')\\]\\s])/,U=/^<([^ >]+@[^ >]+)>/,D=/^<([^ >]+:\\/[^ >]+)>/,N=/-([a-z])?/gi,j=/^(\\|.*)\\n(?: *(\\|? *[-:]+ *\\|[-| :]*)\\n((?:.*\\|.*\\n)*))?\\n?/,H=/^\\[([^\\]]*)\\]:\\s+<?([^\\s>]+)>?\\s*(\"([^\"]*)\")?/,P=/^!\\[([^\\]]*)\\] ?\\[([^\\]]*)\\]/,_=/^\\[([^\\]]*)\\] ?\\[([^\\]]*)\\]/,F=/(\\n|^[-*]\\s|^#|^ {2,}|^-{2,}|^>\\s)/,G=/\\t/g,W=/(^ *\\||\\| *$)/g,Z=/^ *:-+: *$/,q=/^ *:-+ *$/,Q=/^ *-+: *$/,V=\"((?:\\\\[.*?\\\\][([].*?[)\\\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\\\\\\\1|[\\\\s\\\\S])+?)\",X=new RegExp(`^([*_])\\\\1${V}\\\\1\\\\1(?!\\\\1)`),J=new RegExp(`^([*_])${V}\\\\1(?!\\\\1)`),K=new RegExp(`^(==)${V}\\\\1`),Y=new RegExp(`^(~~)${V}\\\\1`),ee=/^\\\\([^0-9A-Za-z\\s])/,te=/\\\\([^0-9A-Za-z\\s])/g,ne=/^([\\s\\S](?:(?!  |[0-9]\\.)[^*_~\\-\\n<`\\\\\\[!])*)/,re=/^\\n+/,ie=/^([ \\t]*)/,le=/\\\\([^\\\\])/g,ae=/(?:^|\\n)( *)$/,oe=\"(?:\\\\d+\\\\.)\",ce=\"(?:[*+-])\";function se(e){return\"( *)(\"+(1===e?oe:ce)+\") +\"}const de=se(1),ue=se(2);function pe(e){return new RegExp(\"^\"+(1===e?de:ue))}const fe=pe(1),he=pe(2);function me(e){return new RegExp(\"^\"+(1===e?de:ue)+\"[^\\\\n]*(?:\\\\n(?!\\\\1\"+(1===e?oe:ce)+\" )[^\\\\n]*)*(\\\\n|$)\",\"gm\")}const ge=me(1),ye=me(2);function ke(e){const t=1===e?oe:ce;return new RegExp(\"^( *)(\"+t+\") [\\\\s\\\\S]+?(?:\\\\n{2,}(?! )(?!\\\\1\"+t+\" (?!\"+t+\" ))\\\\n*|\\\\s*\\\\n*$)\")}const xe=ke(1),be=ke(2);function ve(e,t){const n=1===t,i=n?xe:be,l=n?ge:ye,a=n?fe:he;return{match:Oe(function(e,t){const n=ae.exec(t.prevCapture);return n&&(t.list||!t.inline&&!t.simple)?i.exec(e=n[1]+e):null}),order:1,parse(e,t,r){const i=n?+e[2]:void 0,o=e[0].replace(d,\"\\n\").match(l);let c=!1;return{items:o.map(function(e,n){const i=a.exec(e)[0].length,l=new RegExp(\"^ {1,\"+i+\"}\",\"gm\"),s=e.replace(l,\"\").replace(a,\"\"),d=n===o.length-1,u=-1!==s.indexOf(\"\\n\\n\")||d&&c;c=u;const p=r.inline,f=r.list;let h;r.list=!0,u?(r.inline=!1,h=Ee(s)+\"\\n\\n\"):(r.inline=!0,h=Ee(s));const m=t(h,r);return r.inline=p,r.list=f,m}),ordered:n,start:i}},render:(t,n,i)=>e(t.ordered?\"ol\":\"ul\",{key:i.key,start:t.type===r.orderedList?t.start:void 0},t.items.map(function(t,r){return e(\"li\",{key:r},n(t,i))}))}}const Ce=new RegExp(\"^\\\\[((?:\\\\[[^\\\\]]*\\\\]|[^\\\\[\\\\]]|\\\\](?=[^\\\\[]*\\\\]))*)\\\\]\\\\(\\\\s*<?((?:\\\\([^)]*\\\\)|[^\\\\s\\\\\\\\]|\\\\\\\\.)*?)>?(?:\\\\s+['\\\"]([\\\\s\\\\S]*?)['\\\"])?\\\\s*\\\\)\"),$e=/^!\\[(.*?)\\]\\( *((?:\\([^)]*\\)|[^() ])*) *\"?([^)\"]*)?\"?\\)/,Se=[u,g,y,E,L,z,j,xe,be],we=[...Se,/^[^\\n]+(?:  \\n|\\n{2,})/,A,B,M];function Ee(e){let t=e.length;for(;t>0&&e[t-1]<=\" \";)t--;return e.slice(0,t)}function ze(e){return e.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g,\"a\").replace(/[çÇ]/g,\"c\").replace(/[ðÐ]/g,\"d\").replace(/[ÈÉÊËéèêë]/g,\"e\").replace(/[ÏïÎîÍíÌì]/g,\"i\").replace(/[Ññ]/g,\"n\").replace(/[øØœŒÕõÔôÓóÒò]/g,\"o\").replace(/[ÜüÛûÚúÙù]/g,\"u\").replace(/[ŸÿÝý]/g,\"y\").replace(/[^a-z0-9- ]/gi,\"\").replace(/ /gi,\"-\").toLowerCase()}function Le(e){return Q.test(e)?\"right\":Z.test(e)?\"center\":q.test(e)?\"left\":null}function Ae(e,t,n,r){const i=n.inTable;n.inTable=!0;let l=[[]],a=\"\";function o(){if(!a)return;const e=l[l.length-1];e.push.apply(e,t(a,n)),a=\"\"}return e.trim().split(/(`[^`]*`|\\\\\\||\\|)/).filter(Boolean).forEach((e,t,n)=>{\"|\"===e.trim()&&(o(),r)?0!==t&&t!==n.length-1&&l.push([]):a+=e}),o(),n.inTable=i,l}function Te(e,t,n){n.inline=!0;const i=e[2]?e[2].replace(W,\"\").split(\"|\").map(Le):[],l=e[3]?function(e,t,n){return e.trim().split(\"\\n\").map(function(e){return Ae(e,t,n,!0)})}(e[3],t,n):[],a=Ae(e[1],t,n,!!l.length);return n.inline=!1,l.length?{align:i,cells:l,header:a,type:r.table}:{children:a,type:r.paragraph}}function Be(e,t){return null==e.align[t]?{}:{textAlign:e.align[t]}}function Oe(e){return e.inline=1,e}function Me(e){return Oe(function(t,n){return n.inline?e.exec(t):null})}function Re(e){return Oe(function(t,n){return n.inline||n.simple?e.exec(t):null})}function Ie(e){return function(t,n){return n.inline||n.simple?null:e.exec(t)}}function Ue(e){return Oe(function(t){return e.exec(t)})}function De(e,t){if(t.inline||t.simple)return null;let n=\"\";e.split(\"\\n\").every(e=>(e+=\"\\n\",!Se.some(t=>t.test(e))&&(n+=e,!!e.trim())));const r=Ee(n);return\"\"==r?null:[n,,r]}function Ne(e){try{if(decodeURIComponent(e).replace(/[^A-Za-z0-9/:]/g,\"\").match(/^\\s*(javascript|vbscript|data(?!:image)):/i))return null}catch(e){return null}return e}function je(e){return e.replace(le,\"$1\")}function He(e,t,n){const r=n.inline||!1,i=n.simple||!1;n.inline=!0,n.simple=!0;const l=e(t,n);return n.inline=r,n.simple=i,l}function Pe(e,t,n){const r=n.inline||!1,i=n.simple||!1;n.inline=!1,n.simple=!0;const l=e(t,n);return n.inline=r,n.simple=i,l}function _e(e,t,n){const r=n.inline||!1;n.inline=!1;const i=e(t,n);return n.inline=r,i}const Fe=(e,t,n)=>({children:He(t,e[2],n)});function Ge(){return{}}function We(){return null}function Ze(...e){return e.filter(Boolean).join(\" \")}function qe(e,t,n){let r=e;const i=t.split(\".\");for(;i.length&&(r=r[i[0]],void 0!==r);)i.shift();return r||n}function Qe(n=\"\",i={}){function d(e,n,...r){const l=qe(i.overrides,`${e}.props`,{});return i.createElement(function(e,t){const n=qe(t,e);return n?\"function\"==typeof n||\"object\"==typeof n&&\"render\"in n?n:qe(t,`${e}.component`,e):e}(e,i.overrides),t({},n,l,{className:Ze(null==n?void 0:n.className,l.className)||void 0}),...r)}function W(e){e=e.replace(S,\"\");let t=!1;i.forceInline?t=!0:i.forceBlock||(t=!1===F.test(e));const n=ae(le(t?e:`${Ee(e).replace(re,\"\")}\\n\\n`,{inline:t}));for(;\"string\"==typeof n[n.length-1]&&!n[n.length-1].trim();)n.pop();if(null===i.wrapper)return n;const r=i.wrapper||(t?\"span\":\"div\");let l;if(n.length>1||i.forceWrapper)l=n;else{if(1===n.length)return l=n[0],\"string\"==typeof l?d(\"span\",{key:\"outer\"},l):l;l=null}return i.createElement(r,{key:\"outer\"},l)}function Z(e,t){const n=t.match(c);return n?n.reduce(function(t,n){const r=n.indexOf(\"=\");if(-1!==r){const a=function(e){return-1!==e.indexOf(\"-\")&&null===e.match(O)&&(e=e.replace(N,function(e,t){return t.toUpperCase()})),e}(n.slice(0,r)).trim(),o=function(e){const t=e[0];return('\"'===t||\"'\"===t)&&e.length>=2&&e[e.length-1]===t?e.slice(1,-1):e}(n.slice(r+1).trim()),c=l[a]||a;if(\"ref\"===c)return t;const s=t[c]=function(e,t,n,r){return\"style\"===t?n.split(/;\\s?/).reduce(function(e,t){const n=t.slice(0,t.indexOf(\":\"));return e[n.trim().replace(/(-[a-z])/g,e=>e[1].toUpperCase())]=t.slice(n.length+1).trim(),e},{}):\"href\"===t||\"src\"===t?r(n,e,t):(n.match(R)&&(n=n.slice(1,n.length-1)),\"true\"===n||\"false\"!==n&&n)}(e,a,o,i.sanitizer);\"string\"==typeof s&&(A.test(s)||M.test(s))&&(t[c]=W(s.trim()))}else\"style\"!==n&&(t[l[n]||n]=!0);return t},{}):null}i.overrides=i.overrides||{},i.sanitizer=i.sanitizer||Ne,i.slugify=i.slugify||ze,i.namedCodesToUnicode=i.namedCodesToUnicode?t({},a,i.namedCodesToUnicode):a,i.createElement=i.createElement||e.createElement;const q=[],Q={},V={[r.blockQuote]:{match:Ie(u),order:1,parse(e,t,n){const[,r,i]=e[0].replace(p,\"\").match(f);return{alert:r,children:t(i,n)}},render(e,t,n){const l={key:n.key};return e.alert&&(l.className=\"markdown-alert-\"+i.slugify(e.alert.toLowerCase(),ze),e.children.unshift({attrs:{},children:[{type:r.text,text:e.alert}],noInnerParse:!0,type:r.htmlBlock,tag:\"header\"})),d(\"blockquote\",l,t(e.children,n))}},[r.breakLine]:{match:Ue(h),order:1,parse:Ge,render:(e,t,n)=>d(\"br\",{key:n.key})},[r.breakThematic]:{match:Ie(m),order:1,parse:Ge,render:(e,t,n)=>d(\"hr\",{key:n.key})},[r.codeBlock]:{match:Ie(y),order:0,parse:e=>({lang:void 0,text:Ee(e[0].replace(/^ {4}/gm,\"\")).replace(te,\"$1\")}),render:(e,n,r)=>d(\"pre\",{key:r.key},d(\"code\",t({},e.attrs,{className:e.lang?`lang-${e.lang}`:\"\"}),e.text))},[r.codeFenced]:{match:Ie(g),order:0,parse:e=>({attrs:Z(\"code\",e[3]||\"\"),lang:e[2]||void 0,text:e[4].replace(te,\"$1\"),type:r.codeBlock})},[r.codeInline]:{match:Re(k),order:3,parse:e=>({text:e[2].replace(te,\"$1\")}),render:(e,t,n)=>d(\"code\",{key:n.key},e.text)},[r.footnote]:{match:Ie(v),order:0,parse:e=>(q.push({footnote:e[2],identifier:e[1]}),{}),render:We},[r.footnoteReference]:{match:Me(C),order:1,parse:e=>({target:`#${i.slugify(e[1],ze)}`,text:e[1]}),render:(e,t,n)=>d(\"a\",{key:n.key,href:i.sanitizer(e.target,\"a\",\"href\")},d(\"sup\",{key:n.key},e.text))},[r.gfmTask]:{match:Me(w),order:1,parse:e=>({completed:\"x\"===e[1].toLowerCase()}),render:(e,t,n)=>d(\"input\",{checked:e.completed,key:n.key,readOnly:!0,type:\"checkbox\"})},[r.heading]:{match:Ie(i.enforceAtxHeadings?z:E),order:1,parse:(e,t,n)=>({children:He(t,e[2],n),id:i.slugify(e[2],ze),level:e[1].length}),render:(e,t,n)=>d(`h${e.level}`,{id:e.id,key:n.key},t(e.children,n))},[r.headingSetext]:{match:Ie(L),order:0,parse:(e,t,n)=>({children:He(t,e[1],n),level:\"=\"===e[2]?1:2,type:r.heading})},[r.htmlBlock]:{match:Ue(A),order:1,parse(e,t,n){const[,r]=e[3].match(ie),i=new RegExp(`^${r}`,\"gm\"),l=e[3].replace(i,\"\"),a=(c=l,we.some(e=>e.test(c))?_e:He);var c;const s=e[1].toLowerCase(),d=-1!==o.indexOf(s),u=(d?s:e[1]).trim(),p={attrs:Z(u,e[2]),noInnerParse:d,tag:u};return n.inAnchor=n.inAnchor||\"a\"===s,d?p.text=e[3]:p.children=a(t,l,n),n.inAnchor=!1,p},render:(e,n,r)=>d(e.tag,t({key:r.key},e.attrs),e.text||(e.children?n(e.children,r):\"\"))},[r.htmlSelfClosing]:{match:Ue(M),order:1,parse(e){const t=e[1].trim();return{attrs:Z(t,e[2]||\"\"),tag:t}},render:(e,n,r)=>d(e.tag,t({},e.attrs,{key:r.key}))},[r.htmlComment]:{match:Ue(B),order:1,parse:()=>({}),render:We},[r.image]:{match:Re($e),order:1,parse:e=>({alt:e[1],target:je(e[2]),title:e[3]}),render:(e,t,n)=>d(\"img\",{key:n.key,alt:e.alt||void 0,title:e.title||void 0,src:i.sanitizer(e.target,\"img\",\"src\")})},[r.link]:{match:Me(Ce),order:3,parse:(e,t,n)=>({children:Pe(t,e[1],n),target:je(e[2]),title:e[3]}),render:(e,t,n)=>d(\"a\",{key:n.key,href:i.sanitizer(e.target,\"a\",\"href\"),title:e.title},t(e.children,n))},[r.linkAngleBraceStyleDetector]:{match:Me(D),order:0,parse:e=>({children:[{text:e[1],type:r.text}],target:e[1],type:r.link})},[r.linkBareUrlDetector]:{match:Oe((e,t)=>t.inAnchor||i.disableAutoLink?null:Me(I)(e,t)),order:0,parse:e=>({children:[{text:e[1],type:r.text}],target:e[1],title:void 0,type:r.link})},[r.linkMailtoDetector]:{match:Me(U),order:0,parse(e){let t=e[1],n=e[1];return s.test(n)||(n=\"mailto:\"+n),{children:[{text:t.replace(\"mailto:\",\"\"),type:r.text}],target:n,type:r.link}}},[r.orderedList]:ve(d,1),[r.unorderedList]:ve(d,2),[r.newlineCoalescer]:{match:Ie(x),order:3,parse:Ge,render:()=>\"\\n\"},[r.paragraph]:{match:Oe(De),order:3,parse:Fe,render:(e,t,n)=>d(\"p\",{key:n.key},t(e.children,n))},[r.ref]:{match:Me(H),order:0,parse:e=>(Q[e[1]]={target:e[2],title:e[4]},{}),render:We},[r.refImage]:{match:Re(P),order:0,parse:e=>({alt:e[1]||void 0,ref:e[2]}),render:(e,t,n)=>Q[e.ref]?d(\"img\",{key:n.key,alt:e.alt,src:i.sanitizer(Q[e.ref].target,\"img\",\"src\"),title:Q[e.ref].title}):null},[r.refLink]:{match:Me(_),order:0,parse:(e,t,n)=>({children:t(e[1],n),fallbackChildren:e[0],ref:e[2]}),render:(e,t,n)=>Q[e.ref]?d(\"a\",{key:n.key,href:i.sanitizer(Q[e.ref].target,\"a\",\"href\"),title:Q[e.ref].title},t(e.children,n)):d(\"span\",{key:n.key},e.fallbackChildren)},[r.table]:{match:Ie(j),order:1,parse:Te,render(e,t,n){const r=e;return d(\"table\",{key:n.key},d(\"thead\",null,d(\"tr\",null,r.header.map(function(e,i){return d(\"th\",{key:i,style:Be(r,i)},t(e,n))}))),d(\"tbody\",null,r.cells.map(function(e,i){return d(\"tr\",{key:i},e.map(function(e,i){return d(\"td\",{key:i,style:Be(r,i)},t(e,n))}))})))}},[r.text]:{match:Ue(ne),order:4,parse:e=>({text:e[0].replace(T,(e,t)=>i.namedCodesToUnicode[t]?i.namedCodesToUnicode[t]:e)}),render:e=>e.text},[r.textBolded]:{match:Re(X),order:2,parse:(e,t,n)=>({children:t(e[2],n)}),render:(e,t,n)=>d(\"strong\",{key:n.key},t(e.children,n))},[r.textEmphasized]:{match:Re(J),order:3,parse:(e,t,n)=>({children:t(e[2],n)}),render:(e,t,n)=>d(\"em\",{key:n.key},t(e.children,n))},[r.textEscaped]:{match:Re(ee),order:1,parse:e=>({text:e[1],type:r.text})},[r.textMarked]:{match:Re(K),order:3,parse:Fe,render:(e,t,n)=>d(\"mark\",{key:n.key},t(e.children,n))},[r.textStrikethroughed]:{match:Re(Y),order:3,parse:Fe,render:(e,t,n)=>d(\"del\",{key:n.key},t(e.children,n))}};!0===i.disableParsingRawHTML&&(delete V[r.htmlBlock],delete V[r.htmlSelfClosing]);const le=function(e){let t=Object.keys(e);function n(r,i){let l,a,o=[],c=\"\",s=\"\";for(i.prevCapture=i.prevCapture||\"\";r;){let d=0;for(;d<t.length;){if(c=t[d],l=e[c],i.inline&&!l.match.inline){d++;continue}const u=l.match(r,i);if(u){s=u[0],i.prevCapture+=s,r=r.substring(s.length),a=l.parse(u,n,i),null==a.type&&(a.type=c),o.push(a);break}d++}}return i.prevCapture=\"\",o}return t.sort(function(t,n){let r=e[t].order,i=e[n].order;return r!==i?r-i:t<n?-1:1}),function(e,t){return n(function(e){return e.replace(b,\"\\n\").replace($,\"\").replace(G,\"    \")}(e),t)}}(V),ae=(oe=function(e,t){return function(n,r,i){const l=e[n.type].render;return t?t(()=>l(n,r,i),n,r,i):l(n,r,i)}}(V,i.renderRule),function e(t,n={}){if(Array.isArray(t)){const r=n.key,i=[];let l=!1;for(let r=0;r<t.length;r++){n.key=r;const a=e(t[r],n),o=\"string\"==typeof a;o&&l?i[i.length-1]+=a:null!==a&&i.push(a),l=o}return n.key=r,i}return oe(t,e,n)});var oe;const ce=W(n);return q.length?d(\"div\",null,ce,d(\"footer\",{key:\"footer\"},q.map(function(e){return d(\"div\",{id:i.slugify(e.identifier,ze),key:e.identifier},e.identifier,ae(le(e.footnote,{inline:!0})))}))):ce}export default t=>{let{children:r=\"\",options:i}=t,l=function(e,t){if(null==e)return{};var n,r,i={},l=Object.keys(e);for(r=0;r<l.length;r++)t.indexOf(n=l[r])>=0||(i[n]=e[n]);return i}(t,n);return e.cloneElement(Qe(r,i),l)};export{r as RuleType,Qe as compiler,Ne as sanitizer,ze as slugify};\n//# sourceMappingURL=index.modern.js.map\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Component } from 'react';\nimport { getTemplate, getUiOptions, orderProperties, TranslatableString, ADDITIONAL_PROPERTY_FLAG, PROPERTIES_KEY, REF_KEY, ANY_OF_KEY, ONE_OF_KEY, } from '@rjsf/utils';\nimport Markdown from 'markdown-to-jsx';\nimport get from 'lodash/get';\nimport has from 'lodash/has';\nimport isObject from 'lodash/isObject';\nimport set from 'lodash/set';\nimport unset from 'lodash/unset';\n/** The `ObjectField` component is used to render a field in the schema that is of type `object`. It tracks whether an\n * additional property key was modified and what it was modified to\n *\n * @param props - The `FieldProps` for this template\n */\nclass ObjectField extends Component {\n    constructor() {\n        super(...arguments);\n        /** Set up the initial state */\n        this.state = {\n            wasPropertyKeyModified: false,\n            additionalProperties: {},\n        };\n        /** Returns the `onPropertyChange` handler for the `name` field. Handles the special case where a user is attempting\n         * to clear the data for a field added as an additional property. Calls the `onChange()` handler with the updated\n         * formData.\n         *\n         * @param name - The name of the property\n         * @param addedByAdditionalProperties - Flag indicating whether this property is an additional property\n         * @returns - The onPropertyChange callback for the `name` property\n         */\n        this.onPropertyChange = (name, addedByAdditionalProperties = false) => {\n            return (value, newErrorSchema, id) => {\n                const { formData, onChange, errorSchema } = this.props;\n                if (value === undefined && addedByAdditionalProperties) {\n                    // Don't set value = undefined for fields added by\n                    // additionalProperties. Doing so removes them from the\n                    // formData, which causes them to completely disappear\n                    // (including the input field for the property name). Unlike\n                    // fields which are \"mandated\" by the schema, these fields can\n                    // be set to undefined by clicking a \"delete field\" button, so\n                    // set empty values to the empty string.\n                    value = '';\n                }\n                const newFormData = { ...formData, [name]: value };\n                onChange(newFormData, errorSchema &&\n                    errorSchema && {\n                    ...errorSchema,\n                    [name]: newErrorSchema,\n                }, id);\n            };\n        };\n        /** Returns a callback to handle the onDropPropertyClick event for the given `key` which removes the old `key` data\n         * and calls the `onChange` callback with it\n         *\n         * @param key - The key for which the drop callback is desired\n         * @returns - The drop property click callback\n         */\n        this.onDropPropertyClick = (key) => {\n            return (event) => {\n                event.preventDefault();\n                const { onChange, formData } = this.props;\n                const copiedFormData = { ...formData };\n                unset(copiedFormData, key);\n                onChange(copiedFormData);\n            };\n        };\n        /** Computes the next available key name from the `preferredKey`, indexing through the already existing keys until one\n         * that is already not assigned is found.\n         *\n         * @param preferredKey - The preferred name of a new key\n         * @param [formData] - The form data in which to check if the desired key already exists\n         * @returns - The name of the next available key from `preferredKey`\n         */\n        this.getAvailableKey = (preferredKey, formData) => {\n            const { uiSchema, registry } = this.props;\n            const { duplicateKeySuffixSeparator = '-' } = getUiOptions(uiSchema, registry.globalUiOptions);\n            let index = 0;\n            let newKey = preferredKey;\n            while (has(formData, newKey)) {\n                newKey = `${preferredKey}${duplicateKeySuffixSeparator}${++index}`;\n            }\n            return newKey;\n        };\n        /** Returns a callback function that deals with the rename of a key for an additional property for a schema. That\n         * callback will attempt to rename the key and move the existing data to that key, calling `onChange` when it does.\n         *\n         * @param oldValue - The old value of a field\n         * @returns - The key change callback function\n         */\n        this.onKeyChange = (oldValue) => {\n            return (value, newErrorSchema) => {\n                if (oldValue === value) {\n                    return;\n                }\n                const { formData, onChange, errorSchema } = this.props;\n                value = this.getAvailableKey(value, formData);\n                const newFormData = {\n                    ...formData,\n                };\n                const newKeys = { [oldValue]: value };\n                const keyValues = Object.keys(newFormData).map((key) => {\n                    const newKey = newKeys[key] || key;\n                    return { [newKey]: newFormData[key] };\n                });\n                const renamedObj = Object.assign({}, ...keyValues);\n                this.setState({ wasPropertyKeyModified: true });\n                onChange(renamedObj, errorSchema &&\n                    errorSchema && {\n                    ...errorSchema,\n                    [value]: newErrorSchema,\n                });\n            };\n        };\n        /** Handles the adding of a new additional property on the given `schema`. Calls the `onChange` callback once the new\n         * default data for that field has been added to the formData.\n         *\n         * @param schema - The schema element to which the new property is being added\n         */\n        this.handleAddClick = (schema) => () => {\n            if (!schema.additionalProperties) {\n                return;\n            }\n            const { formData, onChange, registry } = this.props;\n            const newFormData = { ...formData };\n            let type = undefined;\n            if (isObject(schema.additionalProperties)) {\n                type = schema.additionalProperties.type;\n                let apSchema = schema.additionalProperties;\n                if (REF_KEY in apSchema) {\n                    const { schemaUtils } = registry;\n                    apSchema = schemaUtils.retrieveSchema({ $ref: apSchema[REF_KEY] }, formData);\n                    type = apSchema.type;\n                }\n                if (!type && (ANY_OF_KEY in apSchema || ONE_OF_KEY in apSchema)) {\n                    type = 'object';\n                }\n            }\n            const newKey = this.getAvailableKey('newKey', newFormData);\n            // Cast this to make the `set` work properly\n            set(newFormData, newKey, this.getDefaultValue(type));\n            onChange(newFormData);\n        };\n    }\n    /** Returns a flag indicating whether the `name` field is required in the object schema\n     *\n     * @param name - The name of the field to check for required-ness\n     * @returns - True if the field `name` is required, false otherwise\n     */\n    isRequired(name) {\n        const { schema } = this.props;\n        return Array.isArray(schema.required) && schema.required.indexOf(name) !== -1;\n    }\n    /** Returns a default value to be used for a new additional schema property of the given `type`\n     *\n     * @param type - The type of the new additional schema property\n     */\n    getDefaultValue(type) {\n        const { registry: { translateString }, } = this.props;\n        switch (type) {\n            case 'array':\n                return [];\n            case 'boolean':\n                return false;\n            case 'null':\n                return null;\n            case 'number':\n                return 0;\n            case 'object':\n                return {};\n            case 'string':\n            default:\n                // We don't have a datatype for some reason (perhaps additionalProperties was true)\n                return translateString(TranslatableString.NewStringDefault);\n        }\n    }\n    /** Renders the `ObjectField` from the given props\n     */\n    render() {\n        var _a, _b, _c;\n        const { schema: rawSchema, uiSchema = {}, formData, errorSchema, idSchema, name, required = false, disabled = false, readonly = false, hideError, idPrefix, idSeparator, onBlur, onFocus, registry, } = this.props;\n        const { fields, formContext, schemaUtils, translateString, globalUiOptions } = registry;\n        const { SchemaField } = fields;\n        const schema = schemaUtils.retrieveSchema(rawSchema, formData);\n        const uiOptions = getUiOptions(uiSchema, globalUiOptions);\n        const { properties: schemaProperties = {} } = schema;\n        const title = (_b = (_a = uiOptions.title) !== null && _a !== void 0 ? _a : schema.title) !== null && _b !== void 0 ? _b : name;\n        const description = (_c = uiOptions.description) !== null && _c !== void 0 ? _c : schema.description;\n        let orderedProperties;\n        try {\n            const properties = Object.keys(schemaProperties);\n            orderedProperties = orderProperties(properties, uiOptions.order);\n        }\n        catch (err) {\n            return (_jsxs(\"div\", { children: [_jsx(\"p\", { className: 'config-error', style: { color: 'red' }, children: _jsx(Markdown, { children: translateString(TranslatableString.InvalidObjectField, [name || 'root', err.message]) }) }), _jsx(\"pre\", { children: JSON.stringify(schema) })] }));\n        }\n        const Template = getTemplate('ObjectFieldTemplate', registry, uiOptions);\n        const templateProps = {\n            // getDisplayLabel() always returns false for object types, so just check the `uiOptions.label`\n            title: uiOptions.label === false ? '' : title,\n            description: uiOptions.label === false ? undefined : description,\n            properties: orderedProperties.map((name) => {\n                const addedByAdditionalProperties = has(schema, [PROPERTIES_KEY, name, ADDITIONAL_PROPERTY_FLAG]);\n                const fieldUiSchema = addedByAdditionalProperties ? uiSchema.additionalProperties : uiSchema[name];\n                const hidden = getUiOptions(fieldUiSchema).widget === 'hidden';\n                const fieldIdSchema = get(idSchema, [name], {});\n                return {\n                    content: (_jsx(SchemaField, { name: name, required: this.isRequired(name), schema: get(schema, [PROPERTIES_KEY, name], {}), uiSchema: fieldUiSchema, errorSchema: get(errorSchema, name), idSchema: fieldIdSchema, idPrefix: idPrefix, idSeparator: idSeparator, formData: get(formData, name), formContext: formContext, wasPropertyKeyModified: this.state.wasPropertyKeyModified, onKeyChange: this.onKeyChange(name), onChange: this.onPropertyChange(name, addedByAdditionalProperties), onBlur: onBlur, onFocus: onFocus, registry: registry, disabled: disabled, readonly: readonly, hideError: hideError, onDropPropertyClick: this.onDropPropertyClick }, name)),\n                    name,\n                    readonly,\n                    disabled,\n                    required,\n                    hidden,\n                };\n            }),\n            readonly,\n            disabled,\n            required,\n            idSchema,\n            uiSchema,\n            errorSchema,\n            schema,\n            formData,\n            formContext,\n            registry,\n        };\n        return _jsx(Template, { ...templateProps, onAddClick: this.handleAddClick });\n    }\n}\nexport default ObjectField;\n//# sourceMappingURL=ObjectField.js.map", "import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback, Component } from 'react';\nimport { ADDITIONAL_PROPERTY_FLAG, deepEquals, descriptionId, getSchemaType, getTemplate, getUiOptions, ID_KEY, mergeObjects, TranslatableString, UI_OPTIONS_KEY, } from '@rjsf/utils';\nimport isObject from 'lodash/isObject';\nimport omit from 'lodash/omit';\nimport Markdown from 'markdown-to-jsx';\n/** The map of component type to FieldName */\nconst COMPONENT_TYPES = {\n    array: 'ArrayField',\n    boolean: 'BooleanField',\n    integer: 'NumberField',\n    number: 'NumberField',\n    object: 'ObjectField',\n    string: 'StringField',\n    null: 'NullField',\n};\n/** Computes and returns which `Field` implementation to return in order to render the field represented by the\n * `schema`. The `uiOptions` are used to alter what potential `Field` implementation is actually returned. If no\n * appropriate `Field` implementation can be found then a wrapper around `UnsupportedFieldTemplate` is used.\n *\n * @param schema - The schema from which to obtain the type\n * @param uiOptions - The UI Options that may affect the component decision\n * @param idSchema - The id that is passed to the `UnsupportedFieldTemplate`\n * @param registry - The registry from which fields and templates are obtained\n * @returns - The `Field` component that is used to render the actual field data\n */\nfunction getFieldComponent(schema, uiOptions, idSchema, registry) {\n    const field = uiOptions.field;\n    const { fields, translateString } = registry;\n    if (typeof field === 'function') {\n        return field;\n    }\n    if (typeof field === 'string' && field in fields) {\n        return fields[field];\n    }\n    const schemaType = getSchemaType(schema);\n    const type = Array.isArray(schemaType) ? schemaType[0] : schemaType || '';\n    const schemaId = schema.$id;\n    let componentName = COMPONENT_TYPES[type];\n    if (schemaId && schemaId in fields) {\n        componentName = schemaId;\n    }\n    // If the type is not defined and the schema uses 'anyOf' or 'oneOf', don't\n    // render a field and let the MultiSchemaField component handle the form display\n    if (!componentName && (schema.anyOf || schema.oneOf)) {\n        return () => null;\n    }\n    return componentName in fields\n        ? fields[componentName]\n        : () => {\n            const UnsupportedFieldTemplate = getTemplate('UnsupportedFieldTemplate', registry, uiOptions);\n            return (_jsx(UnsupportedFieldTemplate, { schema: schema, idSchema: idSchema, reason: translateString(TranslatableString.UnknownFieldType, [String(schema.type)]), registry: registry }));\n        };\n}\n/** The `SchemaFieldRender` component is the work-horse of react-jsonschema-form, determining what kind of real field to\n * render based on the `schema`, `uiSchema` and all the other props. It also deals with rendering the `anyOf` and\n * `oneOf` fields.\n *\n * @param props - The `FieldProps` for this component\n */\nfunction SchemaFieldRender(props) {\n    const { schema: _schema, idSchema: _idSchema, uiSchema, formData, errorSchema, idPrefix, idSeparator, name, onChange, onKeyChange, onDropPropertyClick, required, registry, wasPropertyKeyModified = false, } = props;\n    const { formContext, schemaUtils, globalUiOptions } = registry;\n    const uiOptions = getUiOptions(uiSchema, globalUiOptions);\n    const FieldTemplate = getTemplate('FieldTemplate', registry, uiOptions);\n    const DescriptionFieldTemplate = getTemplate('DescriptionFieldTemplate', registry, uiOptions);\n    const FieldHelpTemplate = getTemplate('FieldHelpTemplate', registry, uiOptions);\n    const FieldErrorTemplate = getTemplate('FieldErrorTemplate', registry, uiOptions);\n    const schema = schemaUtils.retrieveSchema(_schema, formData);\n    const fieldId = _idSchema[ID_KEY];\n    const idSchema = mergeObjects(schemaUtils.toIdSchema(schema, fieldId, formData, idPrefix, idSeparator), _idSchema);\n    /** Intermediary `onChange` handler for field components that will inject the `id` of the current field into the\n     * `onChange` chain if it is not already being provided from a deeper level in the hierarchy\n     */\n    const handleFieldComponentChange = useCallback((formData, newErrorSchema, id) => {\n        const theId = id || fieldId;\n        return onChange(formData, newErrorSchema, theId);\n    }, [fieldId, onChange]);\n    const FieldComponent = getFieldComponent(schema, uiOptions, idSchema, registry);\n    const disabled = Boolean(props.disabled || uiOptions.disabled);\n    const readonly = Boolean(props.readonly || uiOptions.readonly || props.schema.readOnly || schema.readOnly);\n    const uiSchemaHideError = uiOptions.hideError;\n    // Set hideError to the value provided in the uiSchema, otherwise stick with the prop to propagate to children\n    const hideError = uiSchemaHideError === undefined ? props.hideError : Boolean(uiSchemaHideError);\n    const autofocus = Boolean(props.autofocus || uiOptions.autofocus);\n    if (Object.keys(schema).length === 0) {\n        return null;\n    }\n    const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);\n    const { __errors, ...fieldErrorSchema } = errorSchema || {};\n    // See #439: uiSchema: Don't pass consumed class names or style to child components\n    const fieldUiSchema = omit(uiSchema, ['ui:classNames', 'classNames', 'ui:style']);\n    if (UI_OPTIONS_KEY in fieldUiSchema) {\n        fieldUiSchema[UI_OPTIONS_KEY] = omit(fieldUiSchema[UI_OPTIONS_KEY], ['classNames', 'style']);\n    }\n    const field = (_jsx(FieldComponent, { ...props, onChange: handleFieldComponentChange, idSchema: idSchema, schema: schema, uiSchema: fieldUiSchema, disabled: disabled, readonly: readonly, hideError: hideError, autofocus: autofocus, errorSchema: fieldErrorSchema, formContext: formContext, rawErrors: __errors }));\n    const id = idSchema[ID_KEY];\n    // If this schema has a title defined, but the user has set a new key/label, retain their input.\n    let label;\n    if (wasPropertyKeyModified) {\n        label = name;\n    }\n    else {\n        label = ADDITIONAL_PROPERTY_FLAG in schema ? name : uiOptions.title || props.schema.title || schema.title || name;\n    }\n    const description = uiOptions.description || props.schema.description || schema.description || '';\n    const richDescription = uiOptions.enableMarkdownInDescription ? _jsx(Markdown, { children: description }) : description;\n    const help = uiOptions.help;\n    const hidden = uiOptions.widget === 'hidden';\n    const classNames = ['form-group', 'field', `field-${getSchemaType(schema)}`];\n    if (!hideError && __errors && __errors.length > 0) {\n        classNames.push('field-error has-error has-danger');\n    }\n    if (uiSchema === null || uiSchema === void 0 ? void 0 : uiSchema.classNames) {\n        if (process.env.NODE_ENV !== 'production') {\n            console.warn(\"'uiSchema.classNames' is deprecated and may be removed in a major release; Use 'ui:classNames' instead.\");\n        }\n        classNames.push(uiSchema.classNames);\n    }\n    if (uiOptions.classNames) {\n        classNames.push(uiOptions.classNames);\n    }\n    const helpComponent = (_jsx(FieldHelpTemplate, { help: help, idSchema: idSchema, schema: schema, uiSchema: uiSchema, hasErrors: !hideError && __errors && __errors.length > 0, registry: registry }));\n    /*\n     * AnyOf/OneOf errors handled by child schema\n     * unless it can be rendered as select control\n     */\n    const errorsComponent = hideError || ((schema.anyOf || schema.oneOf) && !schemaUtils.isSelect(schema)) ? undefined : (_jsx(FieldErrorTemplate, { errors: __errors, errorSchema: errorSchema, idSchema: idSchema, schema: schema, uiSchema: uiSchema, registry: registry }));\n    const fieldProps = {\n        description: (_jsx(DescriptionFieldTemplate, { id: descriptionId(id), description: richDescription, schema: schema, uiSchema: uiSchema, registry: registry })),\n        rawDescription: description,\n        help: helpComponent,\n        rawHelp: typeof help === 'string' ? help : undefined,\n        errors: errorsComponent,\n        rawErrors: hideError ? undefined : __errors,\n        id,\n        label,\n        hidden,\n        onChange,\n        onKeyChange,\n        onDropPropertyClick,\n        required,\n        disabled,\n        readonly,\n        hideError,\n        displayLabel,\n        classNames: classNames.join(' ').trim(),\n        style: uiOptions.style,\n        formContext,\n        formData,\n        schema,\n        uiSchema,\n        registry,\n    };\n    const _AnyOfField = registry.fields.AnyOfField;\n    const _OneOfField = registry.fields.OneOfField;\n    const isReplacingAnyOrOneOf = (uiSchema === null || uiSchema === void 0 ? void 0 : uiSchema['ui:field']) && (uiSchema === null || uiSchema === void 0 ? void 0 : uiSchema['ui:fieldReplacesAnyOrOneOf']) === true;\n    return (_jsx(FieldTemplate, { ...fieldProps, children: _jsxs(_Fragment, { children: [field, schema.anyOf && !isReplacingAnyOrOneOf && !schemaUtils.isSelect(schema) && (_jsx(_AnyOfField, { name: name, disabled: disabled, readonly: readonly, hideError: hideError, errorSchema: errorSchema, formData: formData, formContext: formContext, idPrefix: idPrefix, idSchema: idSchema, idSeparator: idSeparator, onBlur: props.onBlur, onChange: props.onChange, onFocus: props.onFocus, options: schema.anyOf.map((_schema) => schemaUtils.retrieveSchema(isObject(_schema) ? _schema : {}, formData)), registry: registry, schema: schema, uiSchema: uiSchema })), schema.oneOf && !isReplacingAnyOrOneOf && !schemaUtils.isSelect(schema) && (_jsx(_OneOfField, { name: name, disabled: disabled, readonly: readonly, hideError: hideError, errorSchema: errorSchema, formData: formData, formContext: formContext, idPrefix: idPrefix, idSchema: idSchema, idSeparator: idSeparator, onBlur: props.onBlur, onChange: props.onChange, onFocus: props.onFocus, options: schema.oneOf.map((_schema) => schemaUtils.retrieveSchema(isObject(_schema) ? _schema : {}, formData)), registry: registry, schema: schema, uiSchema: uiSchema }))] }) }));\n}\n/** The `SchemaField` component determines whether it is necessary to rerender the component based on any props changes\n * and if so, calls the `SchemaFieldRender` component with the props.\n */\nclass SchemaField extends Component {\n    shouldComponentUpdate(nextProps) {\n        return !deepEquals(this.props, nextProps);\n    }\n    render() {\n        return _jsx(SchemaFieldRender, { ...this.props });\n    }\n}\nexport default SchemaField;\n//# sourceMappingURL=SchemaField.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getWidget, getUiOptions, optionsList, hasWidget, } from '@rjsf/utils';\n/** The `StringField` component is used to render a schema field that represents a string type\n *\n * @param props - The `FieldProps` for this template\n */\nfunction StringField(props) {\n    var _a;\n    const { schema, name, uiSchema, idSchema, formData, required, disabled = false, readonly = false, autofocus = false, onChange, onBlur, onFocus, registry, rawErrors, hideError, } = props;\n    const { title, format } = schema;\n    const { widgets, formContext, schemaUtils, globalUiOptions } = registry;\n    const enumOptions = schemaUtils.isSelect(schema) ? optionsList(schema) : undefined;\n    let defaultWidget = enumOptions ? 'select' : 'text';\n    if (format && hasWidget(schema, format, widgets)) {\n        defaultWidget = format;\n    }\n    const { widget = defaultWidget, placeholder = '', title: uiTitle, ...options } = getUiOptions(uiSchema);\n    const displayLabel = schemaUtils.getDisplayLabel(schema, uiSchema, globalUiOptions);\n    const label = (_a = uiTitle !== null && uiTitle !== void 0 ? uiTitle : title) !== null && _a !== void 0 ? _a : name;\n    const Widget = getWidget(schema, widget, widgets);\n    return (_jsx(Widget, { options: { ...options, enumOptions }, schema: schema, uiSchema: uiSchema, id: idSchema.$id, name: name, label: label, hideLabel: !displayLabel, hideError: hideError, value: formData, onChange: onChange, onBlur: onBlur, onFocus: onFocus, required: required, disabled: disabled, readonly: readonly, formContext: formContext, autofocus: autofocus, registry: registry, placeholder: placeholder, rawErrors: rawErrors }));\n}\nexport default StringField;\n//# sourceMappingURL=StringField.js.map", "import { useEffect } from 'react';\n/** The `NullField` component is used to render a field in the schema is null. It also ensures that the `formData` is\n * also set to null if it has no value.\n *\n * @param props - The `FieldProps` for this template\n */\nfunction NullField(props) {\n    const { formData, onChange } = props;\n    useEffect(() => {\n        if (formData === undefined) {\n            onChange(null);\n        }\n    }, [formData, onChange]);\n    return null;\n}\nexport default NullField;\n//# sourceMappingURL=NullField.js.map", "import <PERSON><PERSON><PERSON><PERSON>ield from './ArrayField';\nimport Boolean<PERSON>ield from './BooleanField';\nimport MultiSchemaField from './MultiSchemaField';\nimport NumberField from './NumberField';\nimport ObjectField from './ObjectField';\nimport Schem<PERSON>Field from './SchemaField';\nimport StringField from './StringField';\nimport NullField from './NullField';\nfunction fields() {\n    return {\n        AnyOfField: MultiSchemaField,\n        ArrayField: ArrayField,\n        // ArrayField falls back to SchemaField if ArraySchemaField is not defined, which it isn't by default\n        BooleanField,\n        NumberField,\n        ObjectField,\n        OneOfField: MultiSchemaField,\n        SchemaField,\n        StringField,\n        NullField,\n    };\n}\nexport default fields;\n//# sourceMappingURL=index.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { descriptionId, getTemplate, getUiOptions, } from '@rjsf/utils';\n/** The `ArrayFieldDescriptionTemplate` component renders a `DescriptionFieldTemplate` with an `id` derived from\n * the `idSchema`.\n *\n * @param props - The `ArrayFieldDescriptionProps` for the component\n */\nexport default function ArrayFieldDescriptionTemplate(props) {\n    const { idSchema, description, registry, schema, uiSchema } = props;\n    const options = getUiOptions(uiSchema, registry.globalUiOptions);\n    const { label: displayLabel = true } = options;\n    if (!description || !displayLabel) {\n        return null;\n    }\n    const DescriptionFieldTemplate = getTemplate('DescriptionFieldTemplate', registry, options);\n    return (_jsx(DescriptionFieldTemplate, { id: descriptionId(idSchema), description: description, schema: schema, uiSchema: uiSchema, registry: registry }));\n}\n//# sourceMappingURL=ArrayFieldDescriptionTemplate.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n/** The `ArrayFieldItemTemplate` component is the template used to render an items of an array.\n *\n * @param props - The `ArrayFieldTemplateItemType` props for the component\n */\nexport default function ArrayFieldItemTemplate(props) {\n    const { children, className, disabled, hasToolbar, hasMoveDown, hasMoveUp, hasRemove, hasCopy, index, onCopyIndexClick, onDropIndexClick, onReorderClick, readonly, registry, uiSchema, } = props;\n    const { CopyButton, MoveDownButton, MoveUpButton, RemoveButton } = registry.templates.ButtonTemplates;\n    const btnStyle = {\n        flex: 1,\n        paddingLeft: 6,\n        paddingRight: 6,\n        fontWeight: 'bold',\n    };\n    return (_jsxs(\"div\", { className: className, children: [_jsx(\"div\", { className: hasToolbar ? 'col-xs-9' : 'col-xs-12', children: children }), hasToolbar && (_jsx(\"div\", { className: 'col-xs-3 array-item-toolbox', children: _jsxs(\"div\", { className: 'btn-group', style: {\n                        display: 'flex',\n                        justifyContent: 'space-around',\n                    }, children: [(hasMoveUp || hasMoveDown) && (_jsx(MoveUpButton, { style: btnStyle, disabled: disabled || readonly || !hasMoveUp, onClick: onReorderClick(index, index - 1), uiSchema: uiSchema, registry: registry })), (hasMoveUp || hasMoveDown) && (_jsx(MoveDownButton, { style: btnStyle, disabled: disabled || readonly || !hasMoveDown, onClick: onReorderClick(index, index + 1), uiSchema: uiSchema, registry: registry })), hasCopy && (_jsx(CopyButton, { style: btnStyle, disabled: disabled || readonly, onClick: onCopyIndexClick(index), uiSchema: uiSchema, registry: registry })), hasRemove && (_jsx(RemoveButton, { style: btnStyle, disabled: disabled || readonly, onClick: onDropIndexClick(index), uiSchema: uiSchema, registry: registry }))] }) }))] }));\n}\n//# sourceMappingURL=ArrayFieldItemTemplate.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { getTemplate, getUiOptions, } from '@rjsf/utils';\n/** The `ArrayFieldTemplate` component is the template used to render all items in an array.\n *\n * @param props - The `ArrayFieldTemplateItemType` props for the component\n */\nexport default function ArrayFieldTemplate(props) {\n    const { canAdd, className, disabled, idSchema, uiSchema, items, onAddClick, readonly, registry, required, schema, title, } = props;\n    const uiOptions = getUiOptions(uiSchema);\n    const ArrayFieldDescriptionTemplate = getTemplate('ArrayFieldDescriptionTemplate', registry, uiOptions);\n    const ArrayFieldItemTemplate = getTemplate('ArrayFieldItemTemplate', registry, uiOptions);\n    const ArrayFieldTitleTemplate = getTemplate('ArrayFieldTitleTemplate', registry, uiOptions);\n    // Button templates are not overridden in the uiSchema\n    const { ButtonTemplates: { AddButton }, } = registry.templates;\n    return (_jsxs(\"fieldset\", { className: className, id: idSchema.$id, children: [_jsx(ArrayFieldTitleTemplate, { idSchema: idSchema, title: uiOptions.title || title, required: required, schema: schema, uiSchema: uiSchema, registry: registry }), _jsx(ArrayFieldDescriptionTemplate, { idSchema: idSchema, description: uiOptions.description || schema.description, schema: schema, uiSchema: uiSchema, registry: registry }), _jsx(\"div\", { className: 'row array-item-list', children: items &&\n                    items.map(({ key, ...itemProps }) => (_jsx(ArrayFieldItemTemplate, { ...itemProps }, key))) }), canAdd && (_jsx(AddButton, { className: 'array-item-add', onClick: onAddClick, disabled: disabled || readonly, uiSchema: uiSchema, registry: registry }))] }));\n}\n//# sourceMappingURL=ArrayFieldTemplate.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getTemplate, getUiOptions, titleId, } from '@rjsf/utils';\n/** The `ArrayFieldTitleTemplate` component renders a `TitleFieldTemplate` with an `id` derived from\n * the `idSchema`.\n *\n * @param props - The `ArrayFieldTitleProps` for the component\n */\nexport default function ArrayFieldTitleTemplate(props) {\n    const { idSchema, title, schema, uiSchema, required, registry } = props;\n    const options = getUiOptions(uiSchema, registry.globalUiOptions);\n    const { label: displayLabel = true } = options;\n    if (!title || !displayLabel) {\n        return null;\n    }\n    const TitleFieldTemplate = getTemplate('TitleFieldTemplate', registry, options);\n    return (_jsx(TitleFieldTemplate, { id: titleId(idSchema), title: title, required: required, schema: schema, uiSchema: uiSchema, registry: registry }));\n}\n//# sourceMappingURL=ArrayFieldTitleTemplate.js.map", "import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback } from 'react';\nimport { ariaDescribedByIds, examplesId, getInputProps, } from '@rjsf/utils';\n/** The `BaseInputTemplate` is the template to use to render the basic `<input>` component for the `core` theme.\n * It is used as the template for rendering many of the <input> based widgets that differ by `type` and callbacks only.\n * It can be customized/overridden for other themes or individual implementations as needed.\n *\n * @param props - The `WidgetProps` for this template\n */\nexport default function BaseInputTemplate(props) {\n    const { id, name, // remove this from ...rest\n    value, readonly, disabled, autofocus, onBlur, onFocus, onChange, onChangeOverride, options, schema, uiSchema, formContext, registry, rawErrors, type, hideLabel, // remove this from ...rest\n    hideError, // remove this from ...rest\n    ...rest } = props;\n    // Note: since React 15.2.0 we can't forward unknown element attributes, so we\n    // exclude the \"options\" and \"schema\" ones here.\n    if (!id) {\n        console.log('No id for', props);\n        throw new Error(`no id for props ${JSON.stringify(props)}`);\n    }\n    const inputProps = {\n        ...rest,\n        ...getInputProps(schema, type, options),\n    };\n    let inputValue;\n    if (inputProps.type === 'number' || inputProps.type === 'integer') {\n        inputValue = value || value === 0 ? value : '';\n    }\n    else {\n        inputValue = value == null ? '' : value;\n    }\n    const _onChange = useCallback(({ target: { value } }) => onChange(value === '' ? options.emptyValue : value), [onChange, options]);\n    const _onBlur = useCallback(({ target: { value } }) => onBlur(id, value), [onBlur, id]);\n    const _onFocus = useCallback(({ target: { value } }) => onFocus(id, value), [onFocus, id]);\n    return (_jsxs(_Fragment, { children: [_jsx(\"input\", { id: id, name: id, className: 'form-control', readOnly: readonly, disabled: disabled, autoFocus: autofocus, value: inputValue, ...inputProps, list: schema.examples ? examplesId(id) : undefined, onChange: onChangeOverride || _onChange, onBlur: _onBlur, onFocus: _onFocus, \"aria-describedby\": ariaDescribedByIds(id, !!schema.examples) }), Array.isArray(schema.examples) && (_jsx(\"datalist\", { id: examplesId(id), children: schema.examples\n                    .concat(schema.default && !schema.examples.includes(schema.default) ? [schema.default] : [])\n                    .map((example) => {\n                    return _jsx(\"option\", { value: example }, example);\n                }) }, `datalist_${id}`))] }));\n}\n//# sourceMappingURL=BaseInputTemplate.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getSubmitButtonOptions } from '@rjsf/utils';\n/** The `SubmitButton` renders a button that represent the `Submit` action on a form\n */\nexport default function SubmitButton({ uiSchema }) {\n    const { submitText, norender, props: submitButtonProps = {} } = getSubmitButtonOptions(uiSchema);\n    if (norender) {\n        return null;\n    }\n    return (_jsx(\"div\", { children: _jsx(\"button\", { type: 'submit', ...submitButtonProps, className: `btn btn-info ${submitButtonProps.className || ''}`, children: submitText }) }));\n}\n//# sourceMappingURL=SubmitButton.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { TranslatableString } from '@rjsf/utils';\nexport default function IconButton(props) {\n    const { iconType = 'default', icon, className, uiSchema, registry, ...otherProps } = props;\n    return (_jsx(\"button\", { type: 'button', className: `btn btn-${iconType} ${className}`, ...otherProps, children: _jsx(\"i\", { className: `glyphicon glyphicon-${icon}` }) }));\n}\nexport function CopyButton(props) {\n    const { registry: { translateString }, } = props;\n    return (_jsx(IconButton, { title: translateString(TranslatableString.CopyButton), className: 'array-item-copy', ...props, icon: 'copy' }));\n}\nexport function MoveDownButton(props) {\n    const { registry: { translateString }, } = props;\n    return (_jsx(IconButton, { title: translateString(TranslatableString.MoveDownButton), className: 'array-item-move-down', ...props, icon: 'arrow-down' }));\n}\nexport function MoveUpButton(props) {\n    const { registry: { translateString }, } = props;\n    return (_jsx(IconButton, { title: translateString(TranslatableString.MoveUpButton), className: 'array-item-move-up', ...props, icon: 'arrow-up' }));\n}\nexport function RemoveButton(props) {\n    const { registry: { translateString }, } = props;\n    return (_jsx(IconButton, { title: translateString(TranslatableString.RemoveButton), className: 'array-item-remove', ...props, iconType: 'danger', icon: 'remove' }));\n}\n//# sourceMappingURL=IconButton.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { TranslatableString } from '@rjsf/utils';\nimport IconButton from './IconButton';\n/** The `AddButton` renders a button that represent the `Add` action on a form\n */\nexport default function AddButton({ className, onClick, disabled, registry, }) {\n    const { translateString } = registry;\n    return (_jsx(\"div\", { className: 'row', children: _jsx(\"p\", { className: `col-xs-3 col-xs-offset-9 text-right ${className}`, children: _jsx(IconButton, { iconType: 'info', icon: 'plus', className: 'btn-add col-xs-12', title: translateString(TranslatableString.AddButton), onClick: onClick, disabled: disabled, registry: registry }) }) }));\n}\n//# sourceMappingURL=AddButton.js.map", "import SubmitButton from './SubmitButton';\nimport AddButton from './AddButton';\nimport { CopyButton, MoveDownButton, MoveUpButton, RemoveButton } from './IconButton';\nfunction buttonTemplates() {\n    return {\n        SubmitButton,\n        AddButton,\n        CopyButton,\n        MoveDownButton,\n        MoveUpButton,\n        RemoveButton,\n    };\n}\nexport default buttonTemplates;\n//# sourceMappingURL=index.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\n/** The `DescriptionField` is the template to use to render the description of a field\n *\n * @param props - The `DescriptionFieldProps` for this component\n */\nexport default function DescriptionField(props) {\n    const { id, description } = props;\n    if (!description) {\n        return null;\n    }\n    if (typeof description === 'string') {\n        return (_jsx(\"p\", { id: id, className: 'field-description', children: description }));\n    }\n    else {\n        return (_jsx(\"div\", { id: id, className: 'field-description', children: description }));\n    }\n}\n//# sourceMappingURL=DescriptionField.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { TranslatableString, } from '@rjsf/utils';\n/** The `ErrorList` component is the template that renders the all the errors associated with the fields in the `Form`\n *\n * @param props - The `ErrorListProps` for this component\n */\nexport default function ErrorList({ errors, registry, }) {\n    const { translateString } = registry;\n    return (_jsxs(\"div\", { className: 'panel panel-danger errors', children: [_jsx(\"div\", { className: 'panel-heading', children: _jsx(\"h3\", { className: 'panel-title', children: translateString(TranslatableString.ErrorsLabel) }) }), _jsx(\"ul\", { className: 'list-group', children: errors.map((error, i) => {\n                    return (_jsx(\"li\", { className: 'list-group-item text-danger', children: error.stack }, i));\n                }) })] }));\n}\n//# sourceMappingURL=ErrorList.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst REQUIRED_FIELD_SYMBOL = '*';\n/** Renders a label for a field\n *\n * @param props - The `LabelProps` for this component\n */\nexport default function Label(props) {\n    const { label, required, id } = props;\n    if (!label) {\n        return null;\n    }\n    return (_jsxs(\"label\", { className: 'control-label', htmlFor: id, children: [label, required && _jsx(\"span\", { className: 'required', children: REQUIRED_FIELD_SYMBOL })] }));\n}\n//# sourceMappingURL=Label.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { getTemplate, getUiOptions, } from '@rjsf/utils';\nimport Label from './Label';\n/** The `FieldTemplate` component is the template used by `<PERSON><PERSON><PERSON><PERSON><PERSON>` to render any field. It renders the field\n * content, (label, description, children, errors and help) inside of a `WrapIfAdditional` component.\n *\n * @param props - The `FieldTemplateProps` for this component\n */\nexport default function FieldTemplate(props) {\n    const { id, label, children, errors, help, description, hidden, required, displayLabel, registry, uiSchema } = props;\n    const uiOptions = getUiOptions(uiSchema);\n    const WrapIfAdditionalTemplate = getTemplate('WrapIfAdditionalTemplate', registry, uiOptions);\n    if (hidden) {\n        return _jsx(\"div\", { className: 'hidden', children: children });\n    }\n    return (_jsxs(WrapIfAdditionalTemplate, { ...props, children: [displayLabel && _jsx(Label, { label: label, required: required, id: id }), displayLabel && description ? description : null, children, errors, help] }));\n}\n//# sourceMappingURL=FieldTemplate.js.map", "import FieldTemplate from './FieldTemplate';\nexport default FieldTemplate;\n//# sourceMappingURL=index.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { errorId } from '@rjsf/utils';\n/** The `FieldErrorTemplate` component renders the errors local to the particular field\n *\n * @param props - The `FieldErrorProps` for the errors being rendered\n */\nexport default function FieldErrorTemplate(props) {\n    const { errors = [], idSchema } = props;\n    if (errors.length === 0) {\n        return null;\n    }\n    const id = errorId(idSchema);\n    return (_jsx(\"div\", { children: _jsx(\"ul\", { id: id, className: 'error-detail bs-callout bs-callout-info', children: errors\n                .filter((elem) => !!elem)\n                .map((error, index) => {\n                return (_jsx(\"li\", { className: 'text-danger', children: error }, index));\n            }) }) }));\n}\n//# sourceMappingURL=FieldErrorTemplate.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { helpId } from '@rjsf/utils';\n/** The `FieldHelpTemplate` component renders any help desired for a field\n *\n * @param props - The `FieldHelpProps` to be rendered\n */\nexport default function FieldHelpTemplate(props) {\n    const { idSchema, help } = props;\n    if (!help) {\n        return null;\n    }\n    const id = helpId(idSchema);\n    if (typeof help === 'string') {\n        return (_jsx(\"p\", { id: id, className: 'help-block', children: help }));\n    }\n    return (_jsx(\"div\", { id: id, className: 'help-block', children: help }));\n}\n//# sourceMappingURL=FieldHelpTemplate.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { canExpand, descriptionId, getTemplate, getUiOptions, titleId, } from '@rjsf/utils';\n/** The `ObjectFieldTemplate` is the template to use to render all the inner properties of an object along with the\n * title and description if available. If the object is expandable, then an `AddButton` is also rendered after all\n * the properties.\n *\n * @param props - The `ObjectFieldTemplateProps` for this component\n */\nexport default function ObjectFieldTemplate(props) {\n    const { description, disabled, formData, idSchema, onAddClick, properties, readonly, registry, required, schema, title, uiSchema, } = props;\n    const options = getUiOptions(uiSchema);\n    const TitleFieldTemplate = getTemplate('TitleFieldTemplate', registry, options);\n    const DescriptionFieldTemplate = getTemplate('DescriptionFieldTemplate', registry, options);\n    // Button templates are not overridden in the uiSchema\n    const { ButtonTemplates: { AddButton }, } = registry.templates;\n    return (_jsxs(\"fieldset\", { id: idSchema.$id, children: [title && (_jsx(TitleFieldTemplate, { id: titleId(idSchema), title: title, required: required, schema: schema, uiSchema: uiSchema, registry: registry })), description && (_jsx(DescriptionFieldTemplate, { id: descriptionId(idSchema), description: description, schema: schema, uiSchema: uiSchema, registry: registry })), properties.map((prop) => prop.content), canExpand(schema, uiSchema, formData) && (_jsx(AddButton, { className: 'object-property-expand', onClick: onAddClick(schema), disabled: disabled || readonly, uiSchema: uiSchema, registry: registry }))] }));\n}\n//# sourceMappingURL=ObjectFieldTemplate.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst REQUIRED_FIELD_SYMBOL = '*';\n/** The `TitleField` is the template to use to render the title of a field\n *\n * @param props - The `TitleFieldProps` for this component\n */\nexport default function TitleField(props) {\n    const { id, title, required } = props;\n    return (_jsxs(\"legend\", { id: id, children: [title, required && _jsx(\"span\", { className: 'required', children: REQUIRED_FIELD_SYMBOL })] }));\n}\n//# sourceMappingURL=TitleField.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { TranslatableString } from '@rjsf/utils';\nimport Markdown from 'markdown-to-jsx';\n/** The `UnsupportedField` component is used to render a field in the schema is one that is not supported by\n * react-jsonschema-form.\n *\n * @param props - The `FieldProps` for this template\n */\nfunction UnsupportedField(props) {\n    const { schema, idSchema, reason, registry } = props;\n    const { translateString } = registry;\n    let translateEnum = TranslatableString.UnsupportedField;\n    const translateParams = [];\n    if (idSchema && idSchema.$id) {\n        translateEnum = TranslatableString.UnsupportedFieldWithId;\n        translateParams.push(idSchema.$id);\n    }\n    if (reason) {\n        translateEnum =\n            translateEnum === TranslatableString.UnsupportedField\n                ? TranslatableString.UnsupportedFieldWithReason\n                : TranslatableString.UnsupportedFieldWithIdAndReason;\n        translateParams.push(reason);\n    }\n    return (_jsxs(\"div\", { className: 'unsupported-field', children: [_jsx(\"p\", { children: _jsx(Markdown, { children: translateString(translateEnum, translateParams) }) }), schema && _jsx(\"pre\", { children: JSON.stringify(schema, null, 2) })] }));\n}\nexport default UnsupportedField;\n//# sourceMappingURL=UnsupportedField.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { ADDITIONAL_PROPERTY_FLAG, TranslatableString, } from '@rjsf/utils';\nimport Label from './FieldTemplate/Label';\n/** The `WrapIfAdditional` component is used by the `FieldTemplate` to rename, or remove properties that are\n * part of an `additionalProperties` part of a schema.\n *\n * @param props - The `WrapIfAdditionalProps` for this component\n */\nexport default function WrapIfAdditionalTemplate(props) {\n    const { id, classNames, style, disabled, label, onKeyChange, onDropPropertyClick, readonly, required, schema, children, uiSchema, registry, } = props;\n    const { templates, translateString } = registry;\n    // Button templates are not overridden in the uiSchema\n    const { RemoveButton } = templates.ButtonTemplates;\n    const keyLabel = translateString(TranslatableString.KeyLabel, [label]);\n    const additional = ADDITIONAL_PROPERTY_FLAG in schema;\n    if (!additional) {\n        return (_jsx(\"div\", { className: classNames, style: style, children: children }));\n    }\n    return (_jsx(\"div\", { className: classNames, style: style, children: _jsxs(\"div\", { className: 'row', children: [_jsx(\"div\", { className: 'col-xs-5 form-additional', children: _jsxs(\"div\", { className: 'form-group', children: [_jsx(Label, { label: keyLabel, required: required, id: `${id}-key` }), _jsx(\"input\", { className: 'form-control', type: 'text', id: `${id}-key`, onBlur: (event) => onKeyChange(event.target.value), defaultValue: label })] }) }), _jsx(\"div\", { className: 'form-additional form-group col-xs-5', children: children }), _jsx(\"div\", { className: 'col-xs-2', children: _jsx(RemoveButton, { className: 'array-item-remove btn-block', style: { border: '0' }, disabled: disabled || readonly, onClick: onDropPropertyClick(label), uiSchema: uiSchema, registry: registry }) })] }) }));\n}\n//# sourceMappingURL=WrapIfAdditionalTemplate.js.map", "import ArrayFieldDescriptionTemplate from './ArrayFieldDescriptionTemplate';\nimport ArrayFieldItemTemplate from './ArrayFieldItemTemplate';\nimport ArrayFieldTemplate from './ArrayFieldTemplate';\nimport ArrayFieldTitleTemplate from './ArrayFieldTitleTemplate';\nimport BaseInputTemplate from './BaseInputTemplate';\nimport ButtonTemplates from './ButtonTemplates';\nimport DescriptionField from './DescriptionField';\nimport ErrorList from './ErrorList';\nimport FieldTemplate from './FieldTemplate';\nimport FieldErrorTemplate from './FieldErrorTemplate';\nimport FieldHelpTemplate from './FieldHelpTemplate';\nimport ObjectFieldTemplate from './ObjectFieldTemplate';\nimport TitleField from './TitleField';\nimport UnsupportedField from './UnsupportedField';\nimport WrapIfAdditionalTemplate from './WrapIfAdditionalTemplate';\nfunction templates() {\n    return {\n        ArrayFieldDescriptionTemplate,\n        ArrayFieldItemTemplate,\n        ArrayFieldTemplate,\n        ArrayFieldTitleTemplate,\n        ButtonTemplates: ButtonTemplates(),\n        BaseInputTemplate,\n        DescriptionFieldTemplate: DescriptionField,\n        ErrorListTemplate: ErrorList,\n        FieldTemplate,\n        FieldErrorTemplate,\n        FieldHelpTemplate,\n        ObjectFieldTemplate,\n        TitleFieldTemplate: TitleField,\n        UnsupportedFieldTemplate: UnsupportedField,\n        WrapIfAdditionalTemplate,\n    };\n}\nexport default templates;\n//# sourceMappingURL=index.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback, useEffect, useReducer, useState } from 'react';\nimport { ariaDescribedByIds, parseDateString, toDateString, pad, TranslatableString, } from '@rjsf/utils';\nfunction rangeOptions(start, stop) {\n    const options = [];\n    for (let i = start; i <= stop; i++) {\n        options.push({ value: i, label: pad(i, 2) });\n    }\n    return options;\n}\nfunction readyForChange(state) {\n    return Object.values(state).every((value) => value !== -1);\n}\nfunction dateElementProps(state, time, yearsRange = [1900, new Date().getFullYear() + 2]) {\n    const { year, month, day, hour, minute, second } = state;\n    const data = [\n        {\n            type: 'year',\n            range: yearsRange,\n            value: year,\n        },\n        { type: 'month', range: [1, 12], value: month },\n        { type: 'day', range: [1, 31], value: day },\n    ];\n    if (time) {\n        data.push({ type: 'hour', range: [0, 23], value: hour }, { type: 'minute', range: [0, 59], value: minute }, { type: 'second', range: [0, 59], value: second });\n    }\n    return data;\n}\nfunction DateElement({ type, range, value, select, rootId, name, disabled, readonly, autofocus, registry, onBlur, onFocus, }) {\n    const id = rootId + '_' + type;\n    const { SelectWidget } = registry.widgets;\n    return (_jsx(SelectWidget, { schema: { type: 'integer' }, id: id, name: name, className: 'form-control', options: { enumOptions: rangeOptions(range[0], range[1]) }, placeholder: type, value: value, disabled: disabled, readonly: readonly, autofocus: autofocus, onChange: (value) => select(type, value), onBlur: onBlur, onFocus: onFocus, registry: registry, label: '', \"aria-describedby\": ariaDescribedByIds(rootId) }));\n}\n/** The `AltDateWidget` is an alternative widget for rendering date properties.\n * @param props - The `WidgetProps` for this component\n */\nfunction AltDateWidget({ time = false, disabled = false, readonly = false, autofocus = false, options, id, name, registry, onBlur, onFocus, onChange, value, }) {\n    const { translateString } = registry;\n    const [lastValue, setLastValue] = useState(value);\n    const [state, setState] = useReducer((state, action) => {\n        return { ...state, ...action };\n    }, parseDateString(value, time));\n    useEffect(() => {\n        const stateValue = toDateString(state, time);\n        if (readyForChange(state) && stateValue !== value) {\n            // The user changed the date to a new valid data via the comboboxes, so call onChange\n            onChange(stateValue);\n        }\n        else if (lastValue !== value) {\n            // We got a new value in the props\n            setLastValue(value);\n            setState(parseDateString(value, time));\n        }\n    }, [time, value, onChange, state, lastValue]);\n    const handleChange = useCallback((property, value) => {\n        setState({ [property]: value });\n    }, []);\n    const handleSetNow = useCallback((event) => {\n        event.preventDefault();\n        if (disabled || readonly) {\n            return;\n        }\n        const nextState = parseDateString(new Date().toJSON(), time);\n        onChange(toDateString(nextState, time));\n    }, [disabled, readonly, time]);\n    const handleClear = useCallback((event) => {\n        event.preventDefault();\n        if (disabled || readonly) {\n            return;\n        }\n        onChange(undefined);\n    }, [disabled, readonly, onChange]);\n    return (_jsxs(\"ul\", { className: 'list-inline', children: [dateElementProps(state, time, options.yearsRange).map((elemProps, i) => (_jsx(\"li\", { className: 'list-inline-item', children: _jsx(DateElement, { rootId: id, name: name, select: handleChange, ...elemProps, disabled: disabled, readonly: readonly, registry: registry, onBlur: onBlur, onFocus: onFocus, autofocus: autofocus && i === 0 }) }, i))), (options.hideNowButton !== 'undefined' ? !options.hideNowButton : true) && (_jsx(\"li\", { className: 'list-inline-item', children: _jsx(\"a\", { href: '#', className: 'btn btn-info btn-now', onClick: handleSetNow, children: translateString(TranslatableString.NowLabel) }) })), (options.hideClearButton !== 'undefined' ? !options.hideClearButton : true) && (_jsx(\"li\", { className: 'list-inline-item', children: _jsx(\"a\", { href: '#', className: 'btn btn-warning btn-clear', onClick: handleClear, children: translateString(TranslatableString.ClearLabel) }) }))] }));\n}\nexport default AltDateWidget;\n//# sourceMappingURL=AltDateWidget.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\n/** The `AltDateTimeWidget` is an alternative widget for rendering datetime properties.\n *  It uses the AltDateWidget for rendering, with the `time` prop set to true by default.\n *\n * @param props - The `WidgetProps` for this component\n */\nfunction AltDateTimeWidget({ time = true, ...props }) {\n    const { AltDateWidget } = props.registry.widgets;\n    return _jsx(AltDateWidget, { time: time, ...props });\n}\nexport default AltDateTimeWidget;\n//# sourceMappingURL=AltDateTimeWidget.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback } from 'react';\nimport { ariaDescribedByIds, descriptionId, getTemplate, labelValue, schemaRequiresTrueValue, } from '@rjsf/utils';\n/** The `CheckBoxWidget` is a widget for rendering boolean properties.\n *  It is typically used to represent a boolean.\n *\n * @param props - The `WidgetProps` for this component\n */\nfunction CheckboxWidget({ schema, uiSchema, options, id, value, disabled, readonly, label, hideLabel, autofocus = false, onBlur, onFocus, onChange, registry, }) {\n    var _a;\n    const DescriptionFieldTemplate = getTemplate('DescriptionFieldTemplate', registry, options);\n    // Because an unchecked checkbox will cause html5 validation to fail, only add\n    // the \"required\" attribute if the field value must be \"true\", due to the\n    // \"const\" or \"enum\" keywords\n    const required = schemaRequiresTrueValue(schema);\n    const handleChange = useCallback((event) => onChange(event.target.checked), [onChange]);\n    const handleBlur = useCallback((event) => onBlur(id, event.target.checked), [onBlur, id]);\n    const handleFocus = useCallback((event) => onFocus(id, event.target.checked), [onFocus, id]);\n    const description = (_a = options.description) !== null && _a !== void 0 ? _a : schema.description;\n    return (_jsxs(\"div\", { className: `checkbox ${disabled || readonly ? 'disabled' : ''}`, children: [!hideLabel && !!description && (_jsx(DescriptionFieldTemplate, { id: descriptionId(id), description: description, schema: schema, uiSchema: uiSchema, registry: registry })), _jsxs(\"label\", { children: [_jsx(\"input\", { type: 'checkbox', id: id, name: id, checked: typeof value === 'undefined' ? false : value, required: required, disabled: disabled || readonly, autoFocus: autofocus, onChange: handleChange, onBlur: handleBlur, onFocus: handleFocus, \"aria-describedby\": ariaDescribedByIds(id) }), labelValue(_jsx(\"span\", { children: label }), hideLabel)] })] }));\n}\nexport default CheckboxWidget;\n//# sourceMappingURL=CheckboxWidget.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback } from 'react';\nimport { ariaDescribedByIds, enumOptionsDeselectValue, enumOptionsIsSelected, enumOptionsSelectValue, enumOptionsValueForIndex, optionId, } from '@rjsf/utils';\n/** The `CheckboxesWidget` is a widget for rendering checkbox groups.\n *  It is typically used to represent an array of enums.\n *\n * @param props - The `WidgetProps` for this component\n */\nfunction CheckboxesWidget({ id, disabled, options: { inline = false, enumOptions, enumDisabled, emptyValue }, value, autofocus = false, readonly, onChange, onBlur, onFocus, }) {\n    const checkboxesValues = Array.isArray(value) ? value : [value];\n    const handleBlur = useCallback(({ target: { value } }) => onBlur(id, enumOptionsValueForIndex(value, enumOptions, emptyValue)), [onBlur, id]);\n    const handleFocus = useCallback(({ target: { value } }) => onFocus(id, enumOptionsValueForIndex(value, enumOptions, emptyValue)), [onFocus, id]);\n    return (_jsx(\"div\", { className: 'checkboxes', id: id, children: Array.isArray(enumOptions) &&\n            enumOptions.map((option, index) => {\n                const checked = enumOptionsIsSelected(option.value, checkboxesValues);\n                const itemDisabled = Array.isArray(enumDisabled) && enumDisabled.indexOf(option.value) !== -1;\n                const disabledCls = disabled || itemDisabled || readonly ? 'disabled' : '';\n                const handleChange = (event) => {\n                    if (event.target.checked) {\n                        onChange(enumOptionsSelectValue(index, checkboxesValues, enumOptions));\n                    }\n                    else {\n                        onChange(enumOptionsDeselectValue(index, checkboxesValues, enumOptions));\n                    }\n                };\n                const checkbox = (_jsxs(\"span\", { children: [_jsx(\"input\", { type: 'checkbox', id: optionId(id, index), name: id, checked: checked, value: String(index), disabled: disabled || itemDisabled || readonly, autoFocus: autofocus && index === 0, onChange: handleChange, onBlur: handleBlur, onFocus: handleFocus, \"aria-describedby\": ariaDescribedByIds(id) }), _jsx(\"span\", { children: option.label })] }));\n                return inline ? (_jsx(\"label\", { className: `checkbox-inline ${disabledCls}`, children: checkbox }, index)) : (_jsx(\"div\", { className: `checkbox ${disabledCls}`, children: _jsx(\"label\", { children: checkbox }) }, index));\n            }) }));\n}\nexport default CheckboxesWidget;\n//# sourceMappingURL=CheckboxesWidget.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getTemplate } from '@rjsf/utils';\n/** The `ColorWidget` component uses the `BaseInputTemplate` changing the type to `color` and disables it when it is\n * either disabled or readonly.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function ColorWidget(props) {\n    const { disabled, readonly, options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    return _jsx(BaseInputTemplate, { type: 'color', ...props, disabled: disabled || readonly });\n}\n//# sourceMappingURL=ColorWidget.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { useCallback } from 'react';\nimport { getTemplate } from '@rjsf/utils';\n/** The `DateWidget` component uses the `BaseInputTemplate` changing the type to `date` and transforms\n * the value to undefined when it is falsy during the `onChange` handling.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function DateWidget(props) {\n    const { onChange, options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    const handleChange = useCallback((value) => onChange(value || undefined), [onChange]);\n    return _jsx(BaseInputTemplate, { type: 'date', ...props, onChange: handleChange });\n}\n//# sourceMappingURL=DateWidget.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getTemplate, localToUTC, utcToLocal, } from '@rjsf/utils';\n/** The `DateTimeWidget` component uses the `BaseInputTemplate` changing the type to `datetime-local` and transforms\n * the value to/from utc using the appropriate utility functions.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function DateTimeWidget(props) {\n    const { onChange, value, options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    return (_jsx(BaseInputTemplate, { type: 'datetime-local', ...props, value: utcToLocal(value), onChange: (value) => onChange(localToUTC(value)) }));\n}\n//# sourceMappingURL=DateTimeWidget.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getTemplate } from '@rjsf/utils';\n/** The `EmailWidget` component uses the `BaseInputTemplate` changing the type to `email`.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function EmailWidget(props) {\n    const { options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    return _jsx(BaseInputTemplate, { type: 'email', ...props });\n}\n//# sourceMappingURL=EmailWidget.js.map", "import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback, useState } from 'react';\nimport { dataURItoBlob, getTemplate, TranslatableString, } from '@rjsf/utils';\nimport Markdown from 'markdown-to-jsx';\nfunction addNameToDataURL(dataURL, name) {\n    if (dataURL === null) {\n        return null;\n    }\n    return dataURL.replace(';base64', `;name=${encodeURIComponent(name)};base64`);\n}\nfunction processFile(file) {\n    const { name, size, type } = file;\n    return new Promise((resolve, reject) => {\n        const reader = new window.FileReader();\n        reader.onerror = reject;\n        reader.onload = (event) => {\n            var _a;\n            if (typeof ((_a = event.target) === null || _a === void 0 ? void 0 : _a.result) === 'string') {\n                resolve({\n                    dataURL: addNameToDataURL(event.target.result, name),\n                    name,\n                    size,\n                    type,\n                });\n            }\n            else {\n                resolve({\n                    dataURL: null,\n                    name,\n                    size,\n                    type,\n                });\n            }\n        };\n        reader.readAsDataURL(file);\n    });\n}\nfunction processFiles(files) {\n    return Promise.all(Array.from(files).map(processFile));\n}\nfunction FileInfoPreview({ fileInfo, registry, }) {\n    const { translateString } = registry;\n    const { dataURL, type, name } = fileInfo;\n    if (!dataURL) {\n        return null;\n    }\n    if (type.indexOf('image') !== -1) {\n        return _jsx(\"img\", { src: dataURL, style: { maxWidth: '100%' }, className: 'file-preview' });\n    }\n    return (_jsxs(_Fragment, { children: [' ', _jsx(\"a\", { download: `preview-${name}`, href: dataURL, className: 'file-download', children: translateString(TranslatableString.PreviewLabel) })] }));\n}\nfunction FilesInfo({ filesInfo, registry, preview, }) {\n    if (filesInfo.length === 0) {\n        return null;\n    }\n    const { translateString } = registry;\n    return (_jsx(\"ul\", { className: 'file-info', children: filesInfo.map((fileInfo, key) => {\n            const { name, size, type } = fileInfo;\n            return (_jsxs(\"li\", { children: [_jsx(Markdown, { children: translateString(TranslatableString.FilesInfo, [name, type, String(size)]) }), preview && _jsx(FileInfoPreview, { fileInfo: fileInfo, registry: registry })] }, key));\n        }) }));\n}\nfunction extractFileInfo(dataURLs) {\n    return dataURLs\n        .filter((dataURL) => dataURL)\n        .map((dataURL) => {\n        const { blob, name } = dataURItoBlob(dataURL);\n        return {\n            dataURL,\n            name: name,\n            size: blob.size,\n            type: blob.type,\n        };\n    });\n}\n/**\n *  The `FileWidget` is a widget for rendering file upload fields.\n *  It is typically used with a string property with data-url format.\n */\nfunction FileWidget(props) {\n    const { disabled, readonly, required, multiple, onChange, value, options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    const [filesInfo, setFilesInfo] = useState(Array.isArray(value) ? extractFileInfo(value) : extractFileInfo([value]));\n    const handleChange = useCallback((event) => {\n        if (!event.target.files) {\n            return;\n        }\n        // Due to variances in themes, dealing with multiple files for the array case now happens one file at a time.\n        // This is because we don't pass `multiple` into the `BaseInputTemplate` anymore. Instead, we deal with the single\n        // file in each event and concatenate them together ourselves\n        processFiles(event.target.files).then((filesInfoEvent) => {\n            const newValue = filesInfoEvent.map((fileInfo) => fileInfo.dataURL);\n            if (multiple) {\n                setFilesInfo(filesInfo.concat(filesInfoEvent[0]));\n                onChange(value.concat(newValue[0]));\n            }\n            else {\n                setFilesInfo(filesInfoEvent);\n                onChange(newValue[0]);\n            }\n        });\n    }, [multiple, value, filesInfo, onChange]);\n    return (_jsxs(\"div\", { children: [_jsx(BaseInputTemplate, { ...props, disabled: disabled || readonly, type: 'file', required: value ? false : required, onChangeOverride: handleChange, value: '', accept: options.accept ? String(options.accept) : undefined }), _jsx(FilesInfo, { filesInfo: filesInfo, registry: registry, preview: options.filePreview })] }));\n}\nexport default FileWidget;\n//# sourceMappingURL=FileWidget.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\n/** The `HiddenWidget` is a widget for rendering a hidden input field.\n *  It is typically used by setting type to \"hidden\".\n *\n * @param props - The `WidgetProps` for this component\n */\nfunction HiddenWidget({ id, value, }) {\n    return _jsx(\"input\", { type: 'hidden', id: id, name: id, value: typeof value === 'undefined' ? '' : value });\n}\nexport default HiddenWidget;\n//# sourceMappingURL=HiddenWidget.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getTemplate } from '@rjsf/utils';\n/** The `PasswordWidget` component uses the `BaseInputTemplate` changing the type to `password`.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function PasswordWidget(props) {\n    const { options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    return _jsx(BaseInputTemplate, { type: 'password', ...props });\n}\n//# sourceMappingURL=PasswordWidget.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback } from 'react';\nimport { ariaDescribedByIds, enumOptionsIsSelected, enumOptionsValueForIndex, optionId, } from '@rjsf/utils';\n/** The `RadioWidget` is a widget for rendering a radio group.\n *  It is typically used with a string property constrained with enum options.\n *\n * @param props - The `WidgetProps` for this component\n */\nfunction RadioWidget({ options, value, required, disabled, readonly, autofocus = false, onBlur, onFocus, onChange, id, }) {\n    const { enumOptions, enumDisabled, inline, emptyValue } = options;\n    const handleBlur = useCallback(({ target: { value } }) => onBlur(id, enumOptionsValueForIndex(value, enumOptions, emptyValue)), [onBlur, id]);\n    const handleFocus = useCallback(({ target: { value } }) => onFocus(id, enumOptionsValueForIndex(value, enumOptions, emptyValue)), [onFocus, id]);\n    return (_jsx(\"div\", { className: 'field-radio-group', id: id, children: Array.isArray(enumOptions) &&\n            enumOptions.map((option, i) => {\n                const checked = enumOptionsIsSelected(option.value, value);\n                const itemDisabled = Array.isArray(enumDisabled) && enumDisabled.indexOf(option.value) !== -1;\n                const disabledCls = disabled || itemDisabled || readonly ? 'disabled' : '';\n                const handleChange = () => onChange(option.value);\n                const radio = (_jsxs(\"span\", { children: [_jsx(\"input\", { type: 'radio', id: optionId(id, i), checked: checked, name: id, required: required, value: String(i), disabled: disabled || itemDisabled || readonly, autoFocus: autofocus && i === 0, onChange: handleChange, onBlur: handleBlur, onFocus: handleFocus, \"aria-describedby\": ariaDescribedByIds(id) }), _jsx(\"span\", { children: option.label })] }));\n                return inline ? (_jsx(\"label\", { className: `radio-inline ${disabledCls}`, children: radio }, i)) : (_jsx(\"div\", { className: `radio ${disabledCls}`, children: _jsx(\"label\", { children: radio }) }, i));\n            }) }));\n}\nexport default RadioWidget;\n//# sourceMappingURL=RadioWidget.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n/** The `RangeWidget` component uses the `BaseInputTemplate` changing the type to `range` and wrapping the result\n * in a div, with the value along side it.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function RangeWidget(props) {\n    const { value, registry: { templates: { BaseInputTemplate }, }, } = props;\n    return (_jsxs(\"div\", { className: 'field-range-wrapper', children: [_jsx(BaseInputTemplate, { type: 'range', ...props }), _jsx(\"span\", { className: 'range-view', children: value })] }));\n}\n//# sourceMappingURL=RangeWidget.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback } from 'react';\nimport { ariaDescribedByIds, enumOptionsIndexForValue, enumOptionsValueForIndex, } from '@rjsf/utils';\nfunction getValue(event, multiple) {\n    if (multiple) {\n        return Array.from(event.target.options)\n            .slice()\n            .filter((o) => o.selected)\n            .map((o) => o.value);\n    }\n    return event.target.value;\n}\n/** The `SelectWidget` is a widget for rendering dropdowns.\n *  It is typically used with string properties constrained with enum options.\n *\n * @param props - The `WidgetProps` for this component\n */\nfunction SelectWidget({ schema, id, options, value, required, disabled, readonly, multiple = false, autofocus = false, onChange, onBlur, onFocus, placeholder, }) {\n    const { enumOptions, enumDisabled, emptyValue: optEmptyVal } = options;\n    const emptyValue = multiple ? [] : '';\n    const handleFocus = useCallback((event) => {\n        const newValue = getValue(event, multiple);\n        return onFocus(id, enumOptionsValueForIndex(newValue, enumOptions, optEmptyVal));\n    }, [onFocus, id, schema, multiple, options]);\n    const handleBlur = useCallback((event) => {\n        const newValue = getValue(event, multiple);\n        return onBlur(id, enumOptionsValueForIndex(newValue, enumOptions, optEmptyVal));\n    }, [onBlur, id, schema, multiple, options]);\n    const handleChange = useCallback((event) => {\n        const newValue = getValue(event, multiple);\n        return onChange(enumOptionsValueForIndex(newValue, enumOptions, optEmptyVal));\n    }, [onChange, schema, multiple, options]);\n    const selectedIndexes = enumOptionsIndexForValue(value, enumOptions, multiple);\n    return (_jsxs(\"select\", { id: id, name: id, multiple: multiple, className: 'form-control', value: typeof selectedIndexes === 'undefined' ? emptyValue : selectedIndexes, required: required, disabled: disabled || readonly, autoFocus: autofocus, onBlur: handleBlur, onFocus: handleFocus, onChange: handleChange, \"aria-describedby\": ariaDescribedByIds(id), children: [!multiple && schema.default === undefined && _jsx(\"option\", { value: '', children: placeholder }), Array.isArray(enumOptions) &&\n                enumOptions.map(({ value, label }, i) => {\n                    const disabled = enumDisabled && enumDisabled.indexOf(value) !== -1;\n                    return (_jsx(\"option\", { value: String(i), disabled: disabled, children: label }, i));\n                })] }));\n}\nexport default SelectWidget;\n//# sourceMappingURL=SelectWidget.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { useCallback } from 'react';\nimport { ariaDescribedByIds } from '@rjsf/utils';\n/** The `TextareaWidget` is a widget for rendering input fields as textarea.\n *\n * @param props - The `WidgetProps` for this component\n */\nfunction TextareaWidget({ id, options = {}, placeholder, value, required, disabled, readonly, autofocus = false, onChange, onBlur, onFocus, }) {\n    const handleChange = useCallback(({ target: { value } }) => onChange(value === '' ? options.emptyValue : value), [onChange, options.emptyValue]);\n    const handleBlur = useCallback(({ target: { value } }) => onBlur(id, value), [onBlur, id]);\n    const handleFocus = useCallback(({ target: { value } }) => onFocus(id, value), [id, onFocus]);\n    return (_jsx(\"textarea\", { id: id, name: id, className: 'form-control', value: value ? value : '', placeholder: placeholder, required: required, disabled: disabled, readOnly: readonly, autoFocus: autofocus, rows: options.rows, onBlur: handleBlur, onFocus: handleFocus, onChange: handleChange, \"aria-describedby\": ariaDescribedByIds(id) }));\n}\nTextareaWidget.defaultProps = {\n    autofocus: false,\n    options: {},\n};\nexport default TextareaWidget;\n//# sourceMappingURL=TextareaWidget.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getTemplate } from '@rjsf/utils';\n/** The `TextWidget` component uses the `BaseInputTemplate`.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function TextWidget(props) {\n    const { options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    return _jsx(BaseInputTemplate, { ...props });\n}\n//# sourceMappingURL=TextWidget.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { useCallback } from 'react';\nimport { getTemplate } from '@rjsf/utils';\n/** The `TimeWidget` component uses the `BaseInputTemplate` changing the type to `time` and transforms\n * the value to undefined when it is falsy during the `onChange` handling.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function TimeWidget(props) {\n    const { onChange, options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    const handleChange = useCallback((value) => onChange(value ? `${value}:00` : undefined), [onChange]);\n    return _jsx(BaseInputTemplate, { type: 'time', ...props, onChange: handleChange });\n}\n//# sourceMappingURL=TimeWidget.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getTemplate } from '@rjsf/utils';\n/** The `URLWidget` component uses the `BaseInputTemplate` changing the type to `url`.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function URLWidget(props) {\n    const { options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    return _jsx(BaseInputTemplate, { type: 'url', ...props });\n}\n//# sourceMappingURL=URLWidget.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getTemplate } from '@rjsf/utils';\n/** The `UpDownWidget` component uses the `BaseInputTemplate` changing the type to `number`.\n *\n * @param props - The `WidgetProps` for this component\n */\nexport default function UpDownWidget(props) {\n    const { options, registry } = props;\n    const BaseInputTemplate = getTemplate('BaseInputTemplate', registry, options);\n    return _jsx(BaseInputTemplate, { type: 'number', ...props });\n}\n//# sourceMappingURL=UpDownWidget.js.map", "import AltDateWidget from './AltDateWidget';\nimport AltDateTimeWidget from './AltDateTimeWidget';\nimport CheckboxWidget from './CheckboxWidget';\nimport CheckboxesWidget from './CheckboxesWidget';\nimport ColorWidget from './ColorWidget';\nimport DateWidget from './DateWidget';\nimport DateTimeWidget from './DateTimeWidget';\nimport EmailWidget from './EmailWidget';\nimport FileWidget from './FileWidget';\nimport HiddenWidget from './HiddenWidget';\nimport PasswordWidget from './PasswordWidget';\nimport RadioWidget from './RadioWidget';\nimport RangeWidget from './RangeWidget';\nimport SelectWidget from './SelectWidget';\nimport TextareaWidget from './TextareaWidget';\nimport TextWidget from './TextWidget';\nimport TimeWidget from './TimeWidget';\nimport URLWidget from './URLWidget';\nimport UpDownWidget from './UpDownWidget';\nfunction widgets() {\n    return {\n        AltDateWidget,\n        AltDateTimeWidget,\n        CheckboxWidget,\n        CheckboxesWidget,\n        ColorWidget,\n        DateWidget,\n        DateTimeWidget,\n        EmailWidget,\n        FileWidget,\n        HiddenWidget,\n        PasswordWidget,\n        RadioWidget,\n        RangeWidget,\n        SelectWidget,\n        TextWidget,\n        TextareaWidget,\n        TimeWidget,\n        UpDownWidget,\n        URLWidget,\n    };\n}\nexport default widgets;\n//# sourceMappingURL=index.js.map", "import { englishStringTranslator } from '@rjsf/utils';\nimport fields from './components/fields';\nimport templates from './components/templates';\nimport widgets from './components/widgets';\n/** The default registry consists of all the fields, templates and widgets provided in the core implementation,\n * plus an empty `rootSchema` and `formContext. We omit schemaUtils here because it cannot be defaulted without a\n * rootSchema and validator. It will be added into the computed registry later in the Form.\n */\nexport default function getDefaultRegistry() {\n    return {\n        fields: fields(),\n        templates: templates(),\n        widgets: widgets(),\n        rootSchema: {},\n        formContext: {},\n        translateString: englishStringTranslator,\n    };\n}\n//# sourceMappingURL=getDefaultRegistry.js.map", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Component, createRef } from 'react';\nimport { createSchemaUtils, deepEquals, getTemplate, getUiOptions, isObject, mergeObjects, NAME_KEY, RJSF_ADDITONAL_PROPERTIES_FLAG, shouldRender, SUBMIT_BTN_OPTIONS_KEY, toErrorList, UI_GLOBAL_OPTIONS_KEY, UI_OPTIONS_KEY, validationDataMerge, } from '@rjsf/utils';\nimport _get from 'lodash/get';\nimport _isEmpty from 'lodash/isEmpty';\nimport _pick from 'lodash/pick';\nimport _toPath from 'lodash/toPath';\nimport getDefaultRegistry from '../getDefaultRegistry';\n/** The `Form` component renders the outer form and all the fields defined in the `schema` */\nexport default class Form extends Component {\n    /** Constructs the `Form` from the `props`. Will setup the initial state from the props. It will also call the\n     * `onChange` handler if the initially provided `formData` is modified to add missing default values as part of the\n     * state construction.\n     *\n     * @param props - The initial props for the `Form`\n     */\n    constructor(props) {\n        super(props);\n        /** Returns the `formData` with only the elements specified in the `fields` list\n         *\n         * @param formData - The data for the `Form`\n         * @param fields - The fields to keep while filtering\n         */\n        this.getUsedFormData = (formData, fields) => {\n            // For the case of a single input form\n            if (fields.length === 0 && typeof formData !== 'object') {\n                return formData;\n            }\n            // _pick has incorrect type definition, it works with string[][], because lodash/hasIn supports it\n            const data = _pick(formData, fields);\n            if (Array.isArray(formData)) {\n                return Object.keys(data).map((key) => data[key]);\n            }\n            return data;\n        };\n        /** Returns the list of field names from inspecting the `pathSchema` as well as using the `formData`\n         *\n         * @param pathSchema - The `PathSchema` object for the form\n         * @param [formData] - The form data to use while checking for empty objects/arrays\n         */\n        this.getFieldNames = (pathSchema, formData) => {\n            const getAllPaths = (_obj, acc = [], paths = [[]]) => {\n                Object.keys(_obj).forEach((key) => {\n                    if (typeof _obj[key] === 'object') {\n                        const newPaths = paths.map((path) => [...path, key]);\n                        // If an object is marked with additionalProperties, all its keys are valid\n                        if (_obj[key][RJSF_ADDITONAL_PROPERTIES_FLAG] && _obj[key][NAME_KEY] !== '') {\n                            acc.push(_obj[key][NAME_KEY]);\n                        }\n                        else {\n                            getAllPaths(_obj[key], acc, newPaths);\n                        }\n                    }\n                    else if (key === NAME_KEY && _obj[key] !== '') {\n                        paths.forEach((path) => {\n                            const formValue = _get(formData, path);\n                            // adds path to fieldNames if it points to a value\n                            // or an empty object/array\n                            if (typeof formValue !== 'object' ||\n                                _isEmpty(formValue) ||\n                                (Array.isArray(formValue) && formValue.every((val) => typeof val !== 'object'))) {\n                                acc.push(path);\n                            }\n                        });\n                    }\n                });\n                return acc;\n            };\n            return getAllPaths(pathSchema);\n        };\n        /** Function to handle changes made to a field in the `Form`. This handler receives an entirely new copy of the\n         * `formData` along with a new `ErrorSchema`. It will first update the `formData` with any missing default fields and\n         * then, if `omitExtraData` and `liveOmit` are turned on, the `formData` will be filterer to remove any extra data not\n         * in a form field. Then, the resulting formData will be validated if required. The state will be updated with the new\n         * updated (potentially filtered) `formData`, any errors that resulted from validation. Finally the `onChange`\n         * callback will be called if specified with the updated state.\n         *\n         * @param formData - The new form data from a change to a field\n         * @param newErrorSchema - The new `ErrorSchema` based on the field change\n         * @param id - The id of the field that caused the change\n         */\n        this.onChange = (formData, newErrorSchema, id) => {\n            const { extraErrors, omitExtraData, liveOmit, noValidate, liveValidate, onChange } = this.props;\n            const { schemaUtils, schema, retrievedSchema } = this.state;\n            if (isObject(formData) || Array.isArray(formData)) {\n                const newState = this.getStateFromProps(this.props, formData, retrievedSchema);\n                formData = newState.formData;\n            }\n            const mustValidate = !noValidate && liveValidate;\n            let state = { formData, schema };\n            let newFormData = formData;\n            let _retrievedSchema;\n            if (omitExtraData === true && liveOmit === true) {\n                _retrievedSchema = schemaUtils.retrieveSchema(schema, formData);\n                const pathSchema = schemaUtils.toPathSchema(_retrievedSchema, '', formData);\n                const fieldNames = this.getFieldNames(pathSchema, formData);\n                newFormData = this.getUsedFormData(formData, fieldNames);\n                state = {\n                    formData: newFormData,\n                };\n            }\n            if (mustValidate) {\n                const schemaValidation = this.validate(newFormData, schema, schemaUtils, retrievedSchema);\n                let errors = schemaValidation.errors;\n                let errorSchema = schemaValidation.errorSchema;\n                const schemaValidationErrors = errors;\n                const schemaValidationErrorSchema = errorSchema;\n                if (extraErrors) {\n                    const merged = validationDataMerge(schemaValidation, extraErrors);\n                    errorSchema = merged.errorSchema;\n                    errors = merged.errors;\n                }\n                state = {\n                    formData: newFormData,\n                    errors,\n                    errorSchema,\n                    schemaValidationErrors,\n                    schemaValidationErrorSchema,\n                };\n            }\n            else if (!noValidate && newErrorSchema) {\n                const errorSchema = extraErrors\n                    ? mergeObjects(newErrorSchema, extraErrors, 'preventDuplicates')\n                    : newErrorSchema;\n                state = {\n                    formData: newFormData,\n                    errorSchema: errorSchema,\n                    errors: toErrorList(errorSchema),\n                };\n            }\n            if (_retrievedSchema) {\n                state.retrievedSchema = _retrievedSchema;\n            }\n            this.setState(state, () => onChange && onChange({ ...this.state, ...state }, id));\n        };\n        /**\n         * Callback function to handle reset form data.\n         * - Reset all fields with default values.\n         * - Reset validations and errors\n         *\n         */\n        this.reset = () => {\n            const { onChange } = this.props;\n            const newState = this.getStateFromProps(this.props, undefined);\n            const newFormData = newState.formData;\n            const state = {\n                formData: newFormData,\n                errorSchema: {},\n                errors: [],\n                schemaValidationErrors: [],\n                schemaValidationErrorSchema: {},\n            };\n            this.setState(state, () => onChange && onChange({ ...this.state, ...state }));\n        };\n        /** Callback function to handle when a field on the form is blurred. Calls the `onBlur` callback for the `Form` if it\n         * was provided.\n         *\n         * @param id - The unique `id` of the field that was blurred\n         * @param data - The data associated with the field that was blurred\n         */\n        this.onBlur = (id, data) => {\n            const { onBlur } = this.props;\n            if (onBlur) {\n                onBlur(id, data);\n            }\n        };\n        /** Callback function to handle when a field on the form is focused. Calls the `onFocus` callback for the `Form` if it\n         * was provided.\n         *\n         * @param id - The unique `id` of the field that was focused\n         * @param data - The data associated with the field that was focused\n         */\n        this.onFocus = (id, data) => {\n            const { onFocus } = this.props;\n            if (onFocus) {\n                onFocus(id, data);\n            }\n        };\n        /** Callback function to handle when the form is submitted. First, it prevents the default event behavior. Nothing\n         * happens if the target and currentTarget of the event are not the same. It will omit any extra data in the\n         * `formData` in the state if `omitExtraData` is true. It will validate the resulting `formData`, reporting errors\n         * via the `onError()` callback unless validation is disabled. Finally, it will add in any `extraErrors` and then call\n         * back the `onSubmit` callback if it was provided.\n         *\n         * @param event - The submit HTML form event\n         */\n        this.onSubmit = (event) => {\n            event.preventDefault();\n            if (event.target !== event.currentTarget) {\n                return;\n            }\n            event.persist();\n            const { omitExtraData, extraErrors, noValidate, onSubmit } = this.props;\n            let { formData: newFormData } = this.state;\n            const { schema, schemaUtils } = this.state;\n            if (omitExtraData === true) {\n                const retrievedSchema = schemaUtils.retrieveSchema(schema, newFormData);\n                const pathSchema = schemaUtils.toPathSchema(retrievedSchema, '', newFormData);\n                const fieldNames = this.getFieldNames(pathSchema, newFormData);\n                newFormData = this.getUsedFormData(newFormData, fieldNames);\n            }\n            if (noValidate || this.validateForm()) {\n                // There are no errors generated through schema validation.\n                // Check for user provided errors and update state accordingly.\n                const errorSchema = extraErrors || {};\n                const errors = extraErrors ? toErrorList(extraErrors) : [];\n                this.setState({\n                    formData: newFormData,\n                    errors,\n                    errorSchema,\n                    schemaValidationErrors: [],\n                    schemaValidationErrorSchema: {},\n                }, () => {\n                    if (onSubmit) {\n                        onSubmit({ ...this.state, formData: newFormData, status: 'submitted' }, event);\n                    }\n                });\n            }\n        };\n        if (!props.validator) {\n            throw new Error('A validator is required for Form functionality to work');\n        }\n        this.state = this.getStateFromProps(props, props.formData);\n        if (this.props.onChange && !deepEquals(this.state.formData, this.props.formData)) {\n            this.props.onChange(this.state);\n        }\n        this.formElement = createRef();\n    }\n    /**\n     * `getSnapshotBeforeUpdate` is a React lifecycle method that is invoked right before the most recently rendered\n     * output is committed to the DOM. It enables your component to capture current values (e.g., scroll position) before\n     * they are potentially changed.\n     *\n     * In this case, it checks if the props have changed since the last render. If they have, it computes the next state\n     * of the component using `getStateFromProps` method and returns it along with a `shouldUpdate` flag set to `true` IF\n     * the `nextState` and `prevState` are different, otherwise `false`. This ensures that we have the most up-to-date\n     * state ready to be applied in `componentDidUpdate`.\n     *\n     * If `formData` hasn't changed, it simply returns an object with `shouldUpdate` set to `false`, indicating that a\n     * state update is not necessary.\n     *\n     * @param prevProps - The previous set of props before the update.\n     * @param prevState - The previous state before the update.\n     * @returns Either an object containing the next state and a flag indicating that an update should occur, or an object\n     *        with a flag indicating that an update is not necessary.\n     */\n    getSnapshotBeforeUpdate(prevProps, prevState) {\n        if (!deepEquals(this.props, prevProps)) {\n            const nextState = this.getStateFromProps(this.props, this.props.formData, prevProps.schema !== this.props.schema ? undefined : this.state.retrievedSchema);\n            const shouldUpdate = !deepEquals(nextState, prevState);\n            return { nextState, shouldUpdate };\n        }\n        return { shouldUpdate: false };\n    }\n    /**\n     * `componentDidUpdate` is a React lifecycle method that is invoked immediately after updating occurs. This method is\n     * not called for the initial render.\n     *\n     * Here, it checks if an update is necessary based on the `shouldUpdate` flag received from `getSnapshotBeforeUpdate`.\n     * If an update is required, it applies the next state and, if needed, triggers the `onChange` handler to inform about\n     * changes.\n     *\n     * This method effectively replaces the deprecated `UNSAFE_componentWillReceiveProps`, providing a safer alternative\n     * to handle prop changes and state updates.\n     *\n     * @param _ - The previous set of props.\n     * @param prevState - The previous state of the component before the update.\n     * @param snapshot - The value returned from `getSnapshotBeforeUpdate`.\n     */\n    componentDidUpdate(_, prevState, snapshot) {\n        if (snapshot.shouldUpdate) {\n            const { nextState } = snapshot;\n            if (!deepEquals(nextState.formData, this.props.formData) &&\n                !deepEquals(nextState.formData, prevState.formData) &&\n                this.props.onChange) {\n                this.props.onChange(nextState);\n            }\n            this.setState(nextState);\n        }\n    }\n    /** Extracts the updated state from the given `props` and `inputFormData`. As part of this process, the\n     * `inputFormData` is first processed to add any missing required defaults. After that, the data is run through the\n     * validation process IF required by the `props`.\n     *\n     * @param props - The props passed to the `Form`\n     * @param inputFormData - The new or current data for the `Form`\n     * @returns - The new state for the `Form`\n     */\n    getStateFromProps(props, inputFormData, retrievedSchema) {\n        const state = this.state || {};\n        const schema = 'schema' in props ? props.schema : this.props.schema;\n        const uiSchema = ('uiSchema' in props ? props.uiSchema : this.props.uiSchema) || {};\n        const edit = typeof inputFormData !== 'undefined';\n        const liveValidate = 'liveValidate' in props ? props.liveValidate : this.props.liveValidate;\n        const mustValidate = edit && !props.noValidate && liveValidate;\n        const rootSchema = schema;\n        const experimental_defaultFormStateBehavior = 'experimental_defaultFormStateBehavior' in props\n            ? props.experimental_defaultFormStateBehavior\n            : this.props.experimental_defaultFormStateBehavior;\n        let schemaUtils = state.schemaUtils;\n        if (!schemaUtils ||\n            schemaUtils.doesSchemaUtilsDiffer(props.validator, rootSchema, experimental_defaultFormStateBehavior)) {\n            schemaUtils = createSchemaUtils(props.validator, rootSchema, experimental_defaultFormStateBehavior);\n        }\n        const formData = schemaUtils.getDefaultFormState(schema, inputFormData);\n        const _retrievedSchema = retrievedSchema !== null && retrievedSchema !== void 0 ? retrievedSchema : schemaUtils.retrieveSchema(schema, formData);\n        const getCurrentErrors = () => {\n            if (props.noValidate) {\n                return { errors: [], errorSchema: {} };\n            }\n            else if (!props.liveValidate) {\n                return {\n                    errors: state.schemaValidationErrors || [],\n                    errorSchema: state.schemaValidationErrorSchema || {},\n                };\n            }\n            return {\n                errors: state.errors || [],\n                errorSchema: state.errorSchema || {},\n            };\n        };\n        let errors;\n        let errorSchema;\n        let schemaValidationErrors = state.schemaValidationErrors;\n        let schemaValidationErrorSchema = state.schemaValidationErrorSchema;\n        if (mustValidate) {\n            const schemaValidation = this.validate(formData, schema, schemaUtils, _retrievedSchema);\n            errors = schemaValidation.errors;\n            errorSchema = schemaValidation.errorSchema;\n            schemaValidationErrors = errors;\n            schemaValidationErrorSchema = errorSchema;\n        }\n        else {\n            const currentErrors = getCurrentErrors();\n            errors = currentErrors.errors;\n            errorSchema = currentErrors.errorSchema;\n        }\n        if (props.extraErrors) {\n            const merged = validationDataMerge({ errorSchema, errors }, props.extraErrors);\n            errorSchema = merged.errorSchema;\n            errors = merged.errors;\n        }\n        const idSchema = schemaUtils.toIdSchema(_retrievedSchema, uiSchema['ui:rootFieldId'], formData, props.idPrefix, props.idSeparator);\n        const nextState = {\n            schemaUtils,\n            schema,\n            uiSchema,\n            idSchema,\n            formData,\n            edit,\n            errors,\n            errorSchema,\n            schemaValidationErrors,\n            schemaValidationErrorSchema,\n            retrievedSchema: _retrievedSchema,\n        };\n        return nextState;\n    }\n    /** React lifecycle method that is used to determine whether component should be updated.\n     *\n     * @param nextProps - The next version of the props\n     * @param nextState - The next version of the state\n     * @returns - True if the component should be updated, false otherwise\n     */\n    shouldComponentUpdate(nextProps, nextState) {\n        return shouldRender(this, nextProps, nextState);\n    }\n    /** Validates the `formData` against the `schema` using the `altSchemaUtils` (if provided otherwise it uses the\n     * `schemaUtils` in the state), returning the results.\n     *\n     * @param formData - The new form data to validate\n     * @param schema - The schema used to validate against\n     * @param altSchemaUtils - The alternate schemaUtils to use for validation\n     */\n    validate(formData, schema = this.props.schema, altSchemaUtils, retrievedSchema) {\n        const schemaUtils = altSchemaUtils ? altSchemaUtils : this.state.schemaUtils;\n        const { customValidate, transformErrors, uiSchema } = this.props;\n        const resolvedSchema = retrievedSchema !== null && retrievedSchema !== void 0 ? retrievedSchema : schemaUtils.retrieveSchema(schema, formData);\n        return schemaUtils\n            .getValidator()\n            .validateFormData(formData, resolvedSchema, customValidate, transformErrors, uiSchema);\n    }\n    /** Renders any errors contained in the `state` in using the `ErrorList`, if not disabled by `showErrorList`. */\n    renderErrors(registry) {\n        const { errors, errorSchema, schema, uiSchema } = this.state;\n        const { formContext } = this.props;\n        const options = getUiOptions(uiSchema);\n        const ErrorListTemplate = getTemplate('ErrorListTemplate', registry, options);\n        if (errors && errors.length) {\n            return (_jsx(ErrorListTemplate, { errors: errors, errorSchema: errorSchema || {}, schema: schema, uiSchema: uiSchema, formContext: formContext, registry: registry }));\n        }\n        return null;\n    }\n    /** Returns the registry for the form */\n    getRegistry() {\n        var _a;\n        const { translateString: customTranslateString, uiSchema = {} } = this.props;\n        const { schemaUtils } = this.state;\n        const { fields, templates, widgets, formContext, translateString } = getDefaultRegistry();\n        return {\n            fields: { ...fields, ...this.props.fields },\n            templates: {\n                ...templates,\n                ...this.props.templates,\n                ButtonTemplates: {\n                    ...templates.ButtonTemplates,\n                    ...(_a = this.props.templates) === null || _a === void 0 ? void 0 : _a.ButtonTemplates,\n                },\n            },\n            widgets: { ...widgets, ...this.props.widgets },\n            rootSchema: this.props.schema,\n            formContext: this.props.formContext || formContext,\n            schemaUtils,\n            translateString: customTranslateString || translateString,\n            globalUiOptions: uiSchema[UI_GLOBAL_OPTIONS_KEY],\n        };\n    }\n    /** Provides a function that can be used to programmatically submit the `Form` */\n    submit() {\n        if (this.formElement.current) {\n            this.formElement.current.dispatchEvent(new CustomEvent('submit', {\n                cancelable: true,\n            }));\n            this.formElement.current.requestSubmit();\n        }\n    }\n    /** Attempts to focus on the field associated with the `error`. Uses the `property` field to compute path of the error\n     * field, then, using the `idPrefix` and `idSeparator` converts that path into an id. Then the input element with that\n     * id is attempted to be found using the `formElement` ref. If it is located, then it is focused.\n     *\n     * @param error - The error on which to focus\n     */\n    focusOnError(error) {\n        const { idPrefix = 'root', idSeparator = '_' } = this.props;\n        const { property } = error;\n        const path = _toPath(property);\n        if (path[0] === '') {\n            // Most of the time the `.foo` property results in the first element being empty, so replace it with the idPrefix\n            path[0] = idPrefix;\n        }\n        else {\n            // Otherwise insert the idPrefix into the first location using unshift\n            path.unshift(idPrefix);\n        }\n        const elementId = path.join(idSeparator);\n        let field = this.formElement.current.elements[elementId];\n        if (!field) {\n            // if not an exact match, try finding an input starting with the element id (like radio buttons or checkboxes)\n            field = this.formElement.current.querySelector(`input[id^=${elementId}`);\n        }\n        if (field && field.length) {\n            // If we got a list with length > 0\n            field = field[0];\n        }\n        if (field) {\n            field.focus();\n        }\n    }\n    /** Programmatically validate the form. If `onError` is provided, then it will be called with the list of errors the\n     * same way as would happen on form submission.\n     *\n     * @returns - True if the form is valid, false otherwise.\n     */\n    validateForm() {\n        const { extraErrors, extraErrorsBlockSubmit, focusOnFirstError, onError } = this.props;\n        const { formData, errors: prevErrors } = this.state;\n        const schemaValidation = this.validate(formData);\n        let errors = schemaValidation.errors;\n        let errorSchema = schemaValidation.errorSchema;\n        const schemaValidationErrors = errors;\n        const schemaValidationErrorSchema = errorSchema;\n        const hasError = errors.length > 0 || (extraErrors && extraErrorsBlockSubmit);\n        if (hasError) {\n            if (extraErrors) {\n                const merged = validationDataMerge(schemaValidation, extraErrors);\n                errorSchema = merged.errorSchema;\n                errors = merged.errors;\n            }\n            if (focusOnFirstError) {\n                if (typeof focusOnFirstError === 'function') {\n                    focusOnFirstError(errors[0]);\n                }\n                else {\n                    this.focusOnError(errors[0]);\n                }\n            }\n            this.setState({\n                errors,\n                errorSchema,\n                schemaValidationErrors,\n                schemaValidationErrorSchema,\n            }, () => {\n                if (onError) {\n                    onError(errors);\n                }\n                else {\n                    console.error('Form validation failed', errors);\n                }\n            });\n        }\n        else if (prevErrors.length > 0) {\n            this.setState({\n                errors: [],\n                errorSchema: {},\n                schemaValidationErrors: [],\n                schemaValidationErrorSchema: {},\n            });\n        }\n        return !hasError;\n    }\n    /** Renders the `Form` fields inside the <form> | `tagName` or `_internalFormWrapper`, rendering any errors if\n     * needed along with the submit button or any children of the form.\n     */\n    render() {\n        const { children, id, idPrefix, idSeparator, className = '', tagName, name, method, target, action, autoComplete, enctype, acceptcharset, noHtml5Validate = false, disabled = false, readonly = false, formContext, showErrorList = 'top', _internalFormWrapper, } = this.props;\n        const { schema, uiSchema, formData, errorSchema, idSchema } = this.state;\n        const registry = this.getRegistry();\n        const { SchemaField: _SchemaField } = registry.fields;\n        const { SubmitButton } = registry.templates.ButtonTemplates;\n        // The `semantic-ui` and `material-ui` themes have `_internalFormWrapper`s that take an `as` prop that is the\n        // PropTypes.elementType to use for the inner tag, so we'll need to pass `tagName` along if it is provided.\n        // NOTE, the `as` prop is native to `semantic-ui` and is emulated in the `material-ui` theme\n        const as = _internalFormWrapper ? tagName : undefined;\n        const FormTag = _internalFormWrapper || tagName || 'form';\n        let { [SUBMIT_BTN_OPTIONS_KEY]: submitOptions = {} } = getUiOptions(uiSchema);\n        if (disabled) {\n            submitOptions = { ...submitOptions, props: { ...submitOptions.props, disabled: true } };\n        }\n        const submitUiSchema = { [UI_OPTIONS_KEY]: { [SUBMIT_BTN_OPTIONS_KEY]: submitOptions } };\n        return (_jsxs(FormTag, { className: className ? className : 'rjsf', id: id, name: name, method: method, target: target, action: action, autoComplete: autoComplete, encType: enctype, acceptCharset: acceptcharset, noValidate: noHtml5Validate, onSubmit: this.onSubmit, as: as, ref: this.formElement, children: [showErrorList === 'top' && this.renderErrors(registry), _jsx(_SchemaField, { name: '', schema: schema, uiSchema: uiSchema, errorSchema: errorSchema, idSchema: idSchema, idPrefix: idPrefix, idSeparator: idSeparator, formContext: formContext, formData: formData, onChange: this.onChange, onBlur: this.onBlur, onFocus: this.onFocus, registry: registry, disabled: disabled, readonly: readonly }), children ? children : _jsx(SubmitButton, { uiSchema: submitUiSchema, registry: registry }), showErrorList === 'bottom' && this.renderErrors(registry)] }));\n    }\n}\n//# sourceMappingURL=Form.js.map", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { forwardRef } from 'react';\nimport Form from './components/Form';\n/** A Higher-Order component that creates a wrapper around a `Form` with the overrides from the `WithThemeProps` */\nexport default function withTheme(themeProps) {\n    return forwardRef(({ fields, widgets, templates, ...directProps }, ref) => {\n        var _a;\n        fields = { ...themeProps === null || themeProps === void 0 ? void 0 : themeProps.fields, ...fields };\n        widgets = { ...themeProps === null || themeProps === void 0 ? void 0 : themeProps.widgets, ...widgets };\n        templates = {\n            ...themeProps === null || themeProps === void 0 ? void 0 : themeProps.templates,\n            ...templates,\n            ButtonTemplates: {\n                ...(_a = themeProps === null || themeProps === void 0 ? void 0 : themeProps.templates) === null || _a === void 0 ? void 0 : _a.ButtonTemplates,\n                ...templates === null || templates === void 0 ? void 0 : templates.ButtonTemplates,\n            },\n        };\n        return (_jsx(Form, { ...themeProps, ...directProps, fields: fields, widgets: widgets, templates: templates, ref: ref }));\n    });\n}\n//# sourceMappingURL=withTheme.js.map", "import Form from './components/Form';\nimport withTheme from './withTheme';\nimport getDefaultRegistry from './getDefaultRegistry';\nexport { withTheme, getDefaultRegistry };\nexport default Form;\n//# sourceMappingURL=index.js.map", "/**\n * The unique id is used for unique hashes.\n */\nlet uniqueId = 0;\n/**\n * Quick dictionary lookup for unit-less numbers.\n */\nconst CSS_NUMBER = Object.create(null);\n/**\n * CSS properties that are valid unit-less numbers.\n *\n * Ref: https://github.com/facebook/react/blob/master/packages/react-dom/src/shared/CSSProperty.js\n */\nconst CSS_NUMBER_KEYS = [\n    \"animation-iteration-count\",\n    \"border-image-outset\",\n    \"border-image-slice\",\n    \"border-image-width\",\n    \"box-flex\",\n    \"box-flex-group\",\n    \"box-ordinal-group\",\n    \"column-count\",\n    \"columns\",\n    \"counter-increment\",\n    \"counter-reset\",\n    \"flex\",\n    \"flex-grow\",\n    \"flex-positive\",\n    \"flex-shrink\",\n    \"flex-negative\",\n    \"flex-order\",\n    \"font-weight\",\n    \"grid-area\",\n    \"grid-column\",\n    \"grid-column-end\",\n    \"grid-column-span\",\n    \"grid-column-start\",\n    \"grid-row\",\n    \"grid-row-end\",\n    \"grid-row-span\",\n    \"grid-row-start\",\n    \"line-clamp\",\n    \"line-height\",\n    \"opacity\",\n    \"order\",\n    \"orphans\",\n    \"tab-size\",\n    \"widows\",\n    \"z-index\",\n    \"zoom\",\n    // SVG properties.\n    \"fill-opacity\",\n    \"flood-opacity\",\n    \"stop-opacity\",\n    \"stroke-dasharray\",\n    \"stroke-dashoffset\",\n    \"stroke-miterlimit\",\n    \"stroke-opacity\",\n    \"stroke-width\"\n];\n// Add vendor prefixes to all unit-less properties.\nfor (const property of CSS_NUMBER_KEYS) {\n    for (const prefix of [\"-webkit-\", \"-ms-\", \"-moz-\", \"-o-\", \"\"]) {\n        CSS_NUMBER[prefix + property] = true;\n    }\n}\n/**\n * Escape a CSS class name.\n */\nfunction escape(str) {\n    return str.replace(/[ !#$%&()*+,./;<=>?@[\\]^`{|}~\"'\\\\]/g, \"\\\\$&\");\n}\n/**\n * Transform a JavaScript property into a CSS property.\n */\nfunction hyphenate(propertyName) {\n    return propertyName\n        .replace(/[A-Z]/g, (m) => `-${m.toLowerCase()}`)\n        .replace(/^ms-/, \"-ms-\"); // Internet Explorer vendor prefix.\n}\n/**\n * Generate a hash value from a string.\n */\nfunction stringHash(str) {\n    let value = 5381;\n    let len = str.length;\n    while (len--)\n        value = (value * 33) ^ str.charCodeAt(len);\n    return (value >>> 0).toString(36);\n}\n/**\n * Transform a style string to a CSS string.\n */\nfunction styleToString(key, value) {\n    if (value && typeof value === \"number\" && !CSS_NUMBER[key]) {\n        return `${key}:${value}px`;\n    }\n    return `${key}:${value}`;\n}\n/**\n * Sort an array of tuples by first value.\n */\nfunction sortTuples(value) {\n    return value.sort((a, b) => (a[0] > b[0] ? 1 : -1));\n}\n/**\n * Categorize user styles.\n */\nfunction parseStyles(styles, hasNestedStyles) {\n    const properties = [];\n    const nestedStyles = [];\n    // Sort keys before adding to styles.\n    for (const key of Object.keys(styles)) {\n        const name = key.trim();\n        const value = styles[key];\n        if (name.charCodeAt(0) !== 36 /* $ */ && value != null) {\n            if (typeof value === \"object\" && !Array.isArray(value)) {\n                nestedStyles.push([name, value]);\n            }\n            else {\n                properties.push([hyphenate(name), value]);\n            }\n        }\n    }\n    return {\n        style: stringifyProperties(sortTuples(properties)),\n        nested: hasNestedStyles ? nestedStyles : sortTuples(nestedStyles),\n        isUnique: !!styles.$unique\n    };\n}\n/**\n * Stringify an array of property tuples.\n */\nfunction stringifyProperties(properties) {\n    return properties\n        .map(([name, value]) => {\n        if (!Array.isArray(value))\n            return styleToString(name, value);\n        return value.map(x => styleToString(name, x)).join(\";\");\n    })\n        .join(\";\");\n}\n/**\n * Interpolate CSS selectors.\n */\nfunction interpolate(selector, parent) {\n    if (selector.indexOf(\"&\") === -1)\n        return `${parent} ${selector}`;\n    return selector.replace(/&/g, parent);\n}\n/**\n * Recursive loop building styles with deferred selectors.\n */\nfunction stylize(selector, styles, rulesList, stylesList, parent) {\n    const { style, nested, isUnique } = parseStyles(styles, selector !== \"\");\n    let pid = style;\n    if (selector.charCodeAt(0) === 64 /* @ */) {\n        const child = {\n            selector,\n            styles: [],\n            rules: [],\n            style: parent ? \"\" : style\n        };\n        rulesList.push(child);\n        // Nested styles support (e.g. `.foo > @media > .bar`).\n        if (style && parent) {\n            child.styles.push({ selector: parent, style, isUnique });\n        }\n        for (const [name, value] of nested) {\n            pid += name + stylize(name, value, child.rules, child.styles, parent);\n        }\n    }\n    else {\n        const key = parent ? interpolate(selector, parent) : selector;\n        if (style)\n            stylesList.push({ selector: key, style, isUnique });\n        for (const [name, value] of nested) {\n            pid += name + stylize(name, value, rulesList, stylesList, key);\n        }\n    }\n    return pid;\n}\n/**\n * Transform `stylize` tree into style objects.\n */\nfunction composeStylize(cache, pid, rulesList, stylesList, className, isStyle) {\n    for (const { selector, style, isUnique } of stylesList) {\n        const key = isStyle ? interpolate(selector, className) : selector;\n        const id = isUnique\n            ? `u\\0${(++uniqueId).toString(36)}`\n            : `s\\0${pid}\\0${style}`;\n        const item = new Style(style, id);\n        item.add(new Selector(key, `k\\0${pid}\\0${key}`));\n        cache.add(item);\n    }\n    for (const { selector, style, rules, styles } of rulesList) {\n        const item = new Rule(selector, style, `r\\0${pid}\\0${selector}\\0${style}`);\n        composeStylize(item, pid, rules, styles, className, isStyle);\n        cache.add(item);\n    }\n}\n/**\n * Cache to list to styles.\n */\nfunction join(arr) {\n    let res = \"\";\n    for (let i = 0; i < arr.length; i++)\n        res += arr[i];\n    return res;\n}\n/**\n * Noop changes.\n */\nconst noopChanges = {\n    add: () => undefined,\n    change: () => undefined,\n    remove: () => undefined\n};\n/**\n * Implement a cache/event emitter.\n */\nexport class Cache {\n    constructor(changes = noopChanges) {\n        this.changes = changes;\n        this.sheet = [];\n        this.changeId = 0;\n        this._keys = [];\n        this._children = Object.create(null);\n        this._counters = Object.create(null);\n    }\n    add(style) {\n        const count = this._counters[style.id] || 0;\n        const item = this._children[style.id] || style.clone();\n        this._counters[style.id] = count + 1;\n        if (count === 0) {\n            this._children[item.id] = item;\n            this._keys.push(item.id);\n            this.sheet.push(item.getStyles());\n            this.changeId++;\n            this.changes.add(item, this._keys.length - 1);\n        }\n        else if (item instanceof Cache && style instanceof Cache) {\n            const curIndex = this._keys.indexOf(style.id);\n            const prevItemChangeId = item.changeId;\n            item.merge(style);\n            if (item.changeId !== prevItemChangeId) {\n                this.sheet.splice(curIndex, 1, item.getStyles());\n                this.changeId++;\n                this.changes.change(item, curIndex, curIndex);\n            }\n        }\n    }\n    remove(style) {\n        const count = this._counters[style.id];\n        if (count) {\n            this._counters[style.id] = count - 1;\n            const item = this._children[style.id];\n            const index = this._keys.indexOf(item.id);\n            if (count === 1) {\n                delete this._counters[style.id];\n                delete this._children[style.id];\n                this._keys.splice(index, 1);\n                this.sheet.splice(index, 1);\n                this.changeId++;\n                this.changes.remove(item, index);\n            }\n            else if (item instanceof Cache && style instanceof Cache) {\n                const prevChangeId = item.changeId;\n                item.unmerge(style);\n                if (item.changeId !== prevChangeId) {\n                    this.sheet.splice(index, 1, item.getStyles());\n                    this.changeId++;\n                    this.changes.change(item, index, index);\n                }\n            }\n        }\n    }\n    values() {\n        return this._keys.map(key => this._children[key]);\n    }\n    merge(cache) {\n        for (const item of cache.values())\n            this.add(item);\n        return this;\n    }\n    unmerge(cache) {\n        for (const item of cache.values())\n            this.remove(item);\n        return this;\n    }\n    clone() {\n        return new Cache().merge(this);\n    }\n}\n/**\n * Selector is a dumb class made to represent nested CSS selectors.\n */\nexport class Selector {\n    constructor(selector, id) {\n        this.selector = selector;\n        this.id = id;\n    }\n    getStyles() {\n        return this.selector;\n    }\n    clone() {\n        return this;\n    }\n}\n/**\n * The style container registers a style string with selectors.\n */\nexport class Style extends Cache {\n    constructor(style, id) {\n        super();\n        this.style = style;\n        this.id = id;\n    }\n    getStyles() {\n        return `${this.sheet.join(\",\")}{${this.style}}`;\n    }\n    clone() {\n        return new Style(this.style, this.id).merge(this);\n    }\n}\n/**\n * Implement rule logic for style output.\n */\nexport class Rule extends Cache {\n    constructor(rule, style, id) {\n        super();\n        this.rule = rule;\n        this.style = style;\n        this.id = id;\n    }\n    getStyles() {\n        return `${this.rule}{${this.style}${join(this.sheet)}}`;\n    }\n    clone() {\n        return new Rule(this.rule, this.style, this.id).merge(this);\n    }\n}\nfunction key(pid, styles) {\n    const key = `f${stringHash(pid)}`;\n    if (process.env.NODE_ENV === \"production\" || !styles.$displayName)\n        return key;\n    return `${styles.$displayName}_${key}`;\n}\n/**\n * The FreeStyle class implements the API for everything else.\n */\nexport class FreeStyle extends Cache {\n    constructor(id, changes) {\n        super(changes);\n        this.id = id;\n    }\n    registerStyle(styles) {\n        const rulesList = [];\n        const stylesList = [];\n        const pid = stylize(\"&\", styles, rulesList, stylesList);\n        const id = key(pid, styles);\n        const selector = `.${process.env.NODE_ENV === \"production\" ? id : escape(id)}`;\n        composeStylize(this, pid, rulesList, stylesList, selector, true);\n        return id;\n    }\n    registerKeyframes(keyframes) {\n        return this.registerHashRule(\"@keyframes\", keyframes);\n    }\n    registerHashRule(prefix, styles) {\n        const rulesList = [];\n        const stylesList = [];\n        const pid = stylize(\"\", styles, rulesList, stylesList);\n        const id = key(pid, styles);\n        const selector = `${prefix} ${process.env.NODE_ENV === \"production\" ? id : escape(id)}`;\n        const rule = new Rule(selector, \"\", `h\\0${pid}\\0${prefix}`);\n        composeStylize(rule, pid, rulesList, stylesList, \"\", false);\n        this.add(rule);\n        return id;\n    }\n    registerRule(rule, styles) {\n        const rulesList = [];\n        const stylesList = [];\n        const pid = stylize(rule, styles, rulesList, stylesList);\n        composeStylize(this, pid, rulesList, stylesList, \"\", false);\n    }\n    registerCss(styles) {\n        return this.registerRule(\"\", styles);\n    }\n    getStyles() {\n        return join(this.sheet);\n    }\n    clone() {\n        return new FreeStyle(this.id, this.changes).merge(this);\n    }\n}\n/**\n * Exports a simple function to create a new instance.\n */\nexport function create(changes) {\n    return new FreeStyle(`f${(++uniqueId).toString(36)}`, changes);\n}\n//# sourceMappingURL=index.js.map", "var basePickBy = require('./_basePickBy'),\n    hasIn = require('./hasIn');\n\n/**\n * The base implementation of `_.pick` without support for individual\n * property identifiers.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @returns {Object} Returns the new object.\n */\nfunction basePick(object, paths) {\n  return basePickBy(object, paths, function(value, path) {\n    return hasIn(object, path);\n  });\n}\n\nmodule.exports = basePick;\n", "var baseGet = require('./_baseGet'),\n    baseSet = require('./_baseSet'),\n    castPath = require('./_castPath');\n\n/**\n * The base implementation of  `_.pickBy` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @param {Function} predicate The function invoked per property.\n * @returns {Object} Returns the new object.\n */\nfunction basePickBy(object, paths, predicate) {\n  var index = -1,\n      length = paths.length,\n      result = {};\n\n  while (++index < length) {\n    var path = paths[index],\n        value = baseGet(object, path);\n\n    if (predicate(value, path)) {\n      baseSet(result, castPath(path, object), value);\n    }\n  }\n  return result;\n}\n\nmodule.exports = basePickBy;\n", "var basePick = require('./_basePick'),\n    flatRest = require('./_flatRest');\n\n/**\n * Creates an object composed of the picked `object` properties.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to pick.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pick(object, ['a', 'c']);\n * // => { 'a': 1, 'c': 3 }\n */\nvar pick = flatRest(function(object, paths) {\n  return object == null ? {} : basePick(object, paths);\n});\n\nmodule.exports = pick;\n", "var baseUnset = require('./_baseUnset');\n\n/**\n * Removes the property at `path` of `object`.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to unset.\n * @returns {boolean} Returns `true` if the property is deleted, else `false`.\n * @example\n *\n * var object = { 'a': [{ 'b': { 'c': 7 } }] };\n * _.unset(object, 'a[0].b.c');\n * // => true\n *\n * console.log(object);\n * // => { 'a': [{ 'b': {} }] };\n *\n * _.unset(object, ['a', '0', 'b', 'c']);\n * // => true\n *\n * console.log(object);\n * // => { 'a': [{ 'b': {} }] };\n */\nfunction unset(object, path) {\n  return object == null ? true : baseUnset(object, path);\n}\n\nmodule.exports = unset;\n", "'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar typestyle_1 = require(\"./internal/typestyle\");\nexports.TypeStyle = typestyle_1.TypeStyle;\n/**\n * All the CSS types in the 'types' namespace\n */\nvar types = require(\"./types\");\nexports.types = types;\n/**\n * Export certain utilities\n */\nvar utilities_1 = require(\"./internal/utilities\");\nexports.extend = utilities_1.extend;\nexports.classes = utilities_1.classes;\nexports.media = utilities_1.media;\n/** Zero configuration, default instance of TypeStyle */\nvar ts = new typestyle_1.TypeStyle({ autoGenerateTag: true });\n/** Sets the target tag where we write the css on style updates */\nexports.setStylesTarget = ts.setStylesTarget;\n/**\n * Insert `raw` CSS as a string. This is useful for e.g.\n * - third party CSS that you are customizing with template strings\n * - generating raw CSS in JavaScript\n * - reset libraries like normalize.css that you can use without loaders\n */\nexports.cssRaw = ts.cssRaw;\n/**\n * Takes CSSProperties and registers it to a global selector (body, html, etc.)\n */\nexports.cssRule = ts.cssRule;\n/**\n * Renders styles to the singleton tag imediately\n * NOTE: You should only call it on initial render to prevent any non CSS flash.\n * After that it is kept sync using `requestAnimationFrame` and we haven't noticed any bad flashes.\n **/\nexports.forceRenderStyles = ts.forceRenderStyles;\n/**\n * Utility function to register an @font-face\n */\nexports.fontFace = ts.fontFace;\n/**\n * Allows use to use the stylesheet in a node.js environment\n */\nexports.getStyles = ts.getStyles;\n/**\n * Takes keyframes and returns a generated animationName\n */\nexports.keyframes = ts.keyframes;\n/**\n * Helps with testing. Reinitializes FreeStyle + raw\n */\nexports.reinit = ts.reinit;\n/**\n * Takes CSSProperties and return a generated className you can use on your component\n */\nexports.style = ts.style;\n/**\n * Takes an object where property names are ideal class names and property values are CSSProperties, and\n * returns an object where property names are the same ideal class names and the property values are\n * the actual generated class names using the ideal class name as the $debugName\n */\nexports.stylesheet = ts.stylesheet;\n/**\n * Creates a new instance of TypeStyle separate from the default instance.\n *\n * - Use this for creating a different typestyle instance for a shadow dom component.\n * - Use this if you don't want an auto tag generated and you just want to collect the CSS.\n *\n * NOTE: styles aren't shared between different instances.\n */\nfunction createTypeStyle(target) {\n    var instance = new typestyle_1.TypeStyle({ autoGenerateTag: false });\n    if (target) {\n        instance.setStylesTarget(target);\n    }\n    return instance;\n}\nexports.createTypeStyle = createTypeStyle;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n/**\n * We need to do the following to *our* objects before passing to freestyle:\n * - For any `$nest` directive move up to FreeStyle style nesting\n * - For any `$unique` directive map to FreeStyle Unique\n * - For any `$debugName` directive return the debug name\n */\nfunction convertToStyles(object) {\n    /** The final result we will return */\n    var styles = {};\n    for (var key in object) {\n        /** Grab the value upfront */\n        var val = object[key];\n        /** TypeStyle configuration options */\n        if (key === '$nest') {\n            var nested = val;\n            for (var selector in nested) {\n                var subproperties = nested[selector];\n                styles[selector] = convertToStyles(subproperties);\n            }\n        }\n        else if (key === '$debugName') {\n            styles.$displayName = val;\n        }\n        else {\n            styles[key] = val;\n        }\n    }\n    return styles;\n}\nexports.convertToStyles = convertToStyles;\n// todo: better name here\nfunction convertToKeyframes(frames) {\n    var result = {};\n    for (var offset in frames) {\n        if (offset !== '$debugName') {\n            result[offset] = frames[offset];\n        }\n    }\n    if (frames.$debugName) {\n        result.$displayName = frames.$debugName;\n    }\n    return result;\n}\nexports.convertToKeyframes = convertToKeyframes;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar FreeStyle = require(\"free-style\");\nvar formatting_1 = require(\"./formatting\");\nvar utilities_1 = require(\"./utilities\");\n/**\n * Creates an instance of free style with our options\n */\nvar createFreeStyle = function () { return FreeStyle.create(); };\n/**\n * Maintains a single stylesheet and keeps it in sync with requested styles\n */\nvar TypeStyle = /** @class */ (function () {\n    function TypeStyle(_a) {\n        var _this = this;\n        var autoGenerateTag = _a.autoGenerateTag;\n        /**\n         * Insert `raw` CSS as a string. This is useful for e.g.\n         * - third party CSS that you are customizing with template strings\n         * - generating raw CSS in JavaScript\n         * - reset libraries like normalize.css that you can use without loaders\n         */\n        this.cssRaw = function (mustBeValidCSS) {\n            if (!mustBeValidCSS) {\n                return;\n            }\n            _this._raw += mustBeValidCSS || '';\n            _this._pendingRawChange = true;\n            _this._styleUpdated();\n        };\n        /**\n         * Takes CSSProperties and registers it to a global selector (body, html, etc.)\n         */\n        this.cssRule = function (selector) {\n            var objects = [];\n            for (var _i = 1; _i < arguments.length; _i++) {\n                objects[_i - 1] = arguments[_i];\n            }\n            var styles = formatting_1.convertToStyles(utilities_1.extend.apply(void 0, objects));\n            _this._freeStyle.registerRule(selector, styles);\n            _this._styleUpdated();\n            return;\n        };\n        /**\n         * Renders styles to the singleton tag imediately\n         * NOTE: You should only call it on initial render to prevent any non CSS flash.\n         * After that it is kept sync using `requestAnimationFrame` and we haven't noticed any bad flashes.\n         **/\n        this.forceRenderStyles = function () {\n            var target = _this._getTag();\n            if (!target) {\n                return;\n            }\n            target.textContent = _this.getStyles();\n        };\n        /**\n         * Utility function to register an @font-face\n         */\n        this.fontFace = function () {\n            var fontFace = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                fontFace[_i] = arguments[_i];\n            }\n            var freeStyle = _this._freeStyle;\n            for (var _a = 0, _b = fontFace; _a < _b.length; _a++) {\n                var face = _b[_a];\n                freeStyle.registerRule('@font-face', face);\n            }\n            _this._styleUpdated();\n            return;\n        };\n        /**\n         * Allows use to use the stylesheet in a node.js environment\n         */\n        this.getStyles = function () {\n            return (_this._raw || '') + _this._freeStyle.getStyles();\n        };\n        /**\n         * Takes keyframes and returns a generated animationName\n         */\n        this.keyframes = function (frames) {\n            var keyframes = formatting_1.convertToKeyframes(frames);\n            // TODO: replace $debugName with display name\n            var animationName = _this._freeStyle.registerKeyframes(keyframes);\n            _this._styleUpdated();\n            return animationName;\n        };\n        /**\n         * Helps with testing. Reinitializes FreeStyle + raw\n         */\n        this.reinit = function () {\n            /** reinit freestyle */\n            var freeStyle = createFreeStyle();\n            _this._freeStyle = freeStyle;\n            _this._lastFreeStyleChangeId = freeStyle.changeId;\n            /** reinit raw */\n            _this._raw = '';\n            _this._pendingRawChange = false;\n            /** Clear any styles that were flushed */\n            var target = _this._getTag();\n            if (target) {\n                target.textContent = '';\n            }\n        };\n        /** Sets the target tag where we write the css on style updates */\n        this.setStylesTarget = function (tag) {\n            /** Clear any data in any previous tag */\n            if (_this._tag) {\n                _this._tag.textContent = '';\n            }\n            _this._tag = tag;\n            /** This special time buffer immediately */\n            _this.forceRenderStyles();\n        };\n        /**\n         * Takes an object where property names are ideal class names and property values are CSSProperties, and\n         * returns an object where property names are the same ideal class names and the property values are\n         * the actual generated class names using the ideal class name as the $debugName\n         */\n        this.stylesheet = function (classes) {\n            var classNames = Object.getOwnPropertyNames(classes);\n            var result = {};\n            for (var _i = 0, classNames_1 = classNames; _i < classNames_1.length; _i++) {\n                var className = classNames_1[_i];\n                var classDef = classes[className];\n                if (classDef) {\n                    classDef.$debugName = className;\n                    result[className] = _this.style(classDef);\n                }\n            }\n            return result;\n        };\n        var freeStyle = createFreeStyle();\n        this._autoGenerateTag = autoGenerateTag;\n        this._freeStyle = freeStyle;\n        this._lastFreeStyleChangeId = freeStyle.changeId;\n        this._pending = 0;\n        this._pendingRawChange = false;\n        this._raw = '';\n        this._tag = undefined;\n        // rebind prototype to TypeStyle.  It might be better to do a function() { return this.style.apply(this, arguments)}\n        this.style = this.style.bind(this);\n    }\n    /**\n     * Only calls cb all sync operations settle\n     */\n    TypeStyle.prototype._afterAllSync = function (cb) {\n        var _this = this;\n        this._pending++;\n        var pending = this._pending;\n        utilities_1.raf(function () {\n            if (pending !== _this._pending) {\n                return;\n            }\n            cb();\n        });\n    };\n    TypeStyle.prototype._getTag = function () {\n        if (this._tag) {\n            return this._tag;\n        }\n        if (this._autoGenerateTag) {\n            var tag = typeof window === 'undefined'\n                ? { textContent: '' }\n                : document.createElement('style');\n            if (typeof document !== 'undefined') {\n                document.head.appendChild(tag);\n            }\n            this._tag = tag;\n            return tag;\n        }\n        return undefined;\n    };\n    /** Checks if the style tag needs updating and if so queues up the change */\n    TypeStyle.prototype._styleUpdated = function () {\n        var _this = this;\n        var changeId = this._freeStyle.changeId;\n        var lastChangeId = this._lastFreeStyleChangeId;\n        if (!this._pendingRawChange && changeId === lastChangeId) {\n            return;\n        }\n        this._lastFreeStyleChangeId = changeId;\n        this._pendingRawChange = false;\n        this._afterAllSync(function () { return _this.forceRenderStyles(); });\n    };\n    TypeStyle.prototype.style = function () {\n        var className = this._freeStyle.registerStyle(formatting_1.convertToStyles(utilities_1.extend.apply(undefined, arguments)));\n        this._styleUpdated();\n        return className;\n    };\n    return TypeStyle;\n}());\nexports.TypeStyle = TypeStyle;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n/** Raf for node + browser */\nexports.raf = typeof requestAnimationFrame === 'undefined'\n    /**\n     * Make sure setTimeout is always invoked with\n     * `this` set to `window` or `global` automatically\n     **/\n    ? function (cb) { return setTimeout(cb); }\n    /**\n     * Make sure window.requestAnimationFrame is always invoked with `this` window\n     * We might have raf without window in case of `raf/polyfill` (recommended by React)\n     **/\n    : typeof window === 'undefined'\n        ? requestAnimationFrame\n        : requestAnimationFrame.bind(window);\n/**\n * Utility to join classes conditionally\n */\nfunction classes() {\n    var classes = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        classes[_i] = arguments[_i];\n    }\n    return classes\n        .map(function (c) { return c && typeof c === 'object' ? Object.keys(c).map(function (key) { return !!c[key] && key; }) : [c]; })\n        .reduce(function (flattened, c) { return flattened.concat(c); }, [])\n        .filter(function (c) { return !!c; })\n        .join(' ');\n}\nexports.classes = classes;\n/**\n * Merges various styles into a single style object.\n * Note: if two objects have the same property the last one wins\n */\nfunction extend() {\n    var objects = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        objects[_i] = arguments[_i];\n    }\n    /** The final result we will return */\n    var result = {};\n    for (var _a = 0, objects_1 = objects; _a < objects_1.length; _a++) {\n        var object = objects_1[_a];\n        if (object == null || object === false) {\n            continue;\n        }\n        for (var key in object) {\n            /** Falsy values except a explicit 0 is ignored */\n            var val = object[key];\n            if (!val && val !== 0) {\n                continue;\n            }\n            /** if nested media or pseudo selector */\n            if (key === '$nest' && val) {\n                result[key] = result['$nest'] ? extend(result['$nest'], val) : val;\n            }\n            /** if freestyle sub key that needs merging. We come here due to our recursive calls */\n            else if ((key.indexOf('&') !== -1 || key.indexOf('@media') === 0)) {\n                result[key] = result[key] ? extend(result[key], val) : val;\n            }\n            else {\n                result[key] = val;\n            }\n        }\n    }\n    return result;\n}\nexports.extend = extend;\n/**\n * Utility to help customize styles with media queries. e.g.\n * ```\n * style(\n *  media({maxWidth:500}, {color:'red'})\n * )\n * ```\n */\nexports.media = function (mediaQuery) {\n    var _a;\n    var objects = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        objects[_i - 1] = arguments[_i];\n    }\n    var mediaQuerySections = [];\n    if (mediaQuery.type)\n        mediaQuerySections.push(mediaQuery.type);\n    if (mediaQuery.orientation)\n        mediaQuerySections.push(\"(orientation: \" + mediaQuery.orientation + \")\");\n    if (mediaQuery.minWidth)\n        mediaQuerySections.push(\"(min-width: \" + mediaLength(mediaQuery.minWidth) + \")\");\n    if (mediaQuery.maxWidth)\n        mediaQuerySections.push(\"(max-width: \" + mediaLength(mediaQuery.maxWidth) + \")\");\n    if (mediaQuery.minHeight)\n        mediaQuerySections.push(\"(min-height: \" + mediaLength(mediaQuery.minHeight) + \")\");\n    if (mediaQuery.maxHeight)\n        mediaQuerySections.push(\"(max-height: \" + mediaLength(mediaQuery.maxHeight) + \")\");\n    if (mediaQuery.prefersColorScheme)\n        mediaQuerySections.push(\"(prefers-color-scheme: \" + mediaQuery.prefersColorScheme + \")\");\n    var stringMediaQuery = \"@media \" + mediaQuerySections.join(' and ');\n    var object = {\n        $nest: (_a = {},\n            _a[stringMediaQuery] = extend.apply(void 0, objects),\n            _a)\n    };\n    return object;\n};\nvar mediaLength = function (value) {\n    return typeof value === 'string' ? value : value + \"px\";\n};\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n"], "names": [], "sourceRoot": ""}