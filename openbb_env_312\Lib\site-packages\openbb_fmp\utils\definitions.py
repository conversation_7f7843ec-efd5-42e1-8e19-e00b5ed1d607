"""FMP Literal Definitions."""

from typing import Literal

Sectors = Literal[
    "consumer_cyclical",
    "energy",
    "technology",
    "industrials",
    "financial_services",
    "basic_materials",
    "communication_services",
    "consumer_defensive",
    "healthcare",
    "real_estate",
    "utilities",
    "industrial_goods",
    "financial",
    "services",
]

SECTORS = [
    "consumer_cyclical",
    "energy",
    "technology",
    "industrials",
    "financial_services",
    "basic_materials",
    "communication_services",
    "consumer_defensive",
    "healthcare",
    "real_estate",
    "utilities",
    "industrial_goods",
    "financial",
    "services",
]

EXCHANGES = [
    "amex",
    "ams",
    "ase",
    "asx",
    "ath",
    "bme",
    "bru",
    "bud",
    "bue",
    "cai",
    "cnq",
    "cph",
    "dfm",
    "doh",
    "etf",
    "euronext",
    "hel",
    "hkse",
    "ice",
    "iob",
    "ist",
    "jkt",
    "jnb",
    "jpx",
    "kls",
    "koe",
    "ksc",
    "kuw",
    "lse",
    "mex",
    "mutual_fund",
    "nasdaq",
    "neo",
    "nse",
    "nyse",
    "nze",
    "osl",
    "otc",
    "pnk",
    "pra",
    "ris",
    "sao",
    "sau",
    "set",
    "sgo",
    "shh",
    "shz",
    "six",
    "sto",
    "tai",
    "tlv",
    "tsx",
    "two",
    "vie",
    "wse",
    "xetra",
]

Exchanges = Literal[
    "amex",
    "ams",
    "ase",
    "asx",
    "ath",
    "bme",
    "bru",
    "bud",
    "bue",
    "cai",
    "cnq",
    "cph",
    "dfm",
    "doh",
    "etf",
    "euronext",
    "hel",
    "hkse",
    "ice",
    "iob",
    "ist",
    "jkt",
    "jnb",
    "jpx",
    "kls",
    "koe",
    "ksc",
    "kuw",
    "lse",
    "mex",
    "mutual_fund",
    "nasdaq",
    "neo",
    "nse",
    "nyse",
    "nze",
    "osl",
    "otc",
    "pnk",
    "pra",
    "ris",
    "sao",
    "sau",
    "set",
    "sgo",
    "shh",
    "shz",
    "six",
    "sto",
    "tai",
    "tlv",
    "tsx",
    "two",
    "vie",
    "wse",
    "xetra",
]

MARKETS = Literal[
    "AMEX",
    "AMS",
    "ASE",
    "ASX",
    "ATH",
    "BME",
    "BRU",
    "BUD",
    "BUE",
    "CAI",
    "CNQ",
    "CPH",
    "DFM",
    "DOH",
    "DUS",
    "ETF",
    "EURONEXT",
    "HEL",
    "HKSE",
    "ICE",
    "IOB",
    "IST",
    "JKT",
    "JNB",
    "JPX",
    "KLS",
    "KOE",
    "KSC",
    "KUW",
    "LSE",
    "MEX",
    "MIL",
    "MUTUAL_FUND",
    "NASDAQ",
    "NEO",
    "NSE",
    "NYSE",
    "NZE",
    "OSL",
    "OTC",
    "PNK",
    "PRA",
    "RIS",
    "SAO",
    "SAU",
    "SES",
    "SET",
    "SGO",
    "SHH",
    "SHZ",
    "SIX",
    "STO",
    "TAI",
    "TLV",
    "TSX",
    "TWO",
    "VIE",
    "WSE",
    "XETRA",
]

TRANSACTION_TYPES = Literal[
    "award",
    "conversion",
    "return",
    "expire_short",
    "in_kind",
    "gift",
    "expire_long",
    "discretionary",
    "other",
    "small",
    "exempt",
    "otm",
    "purchase",
    "sale",
    "tender",
    "will",
    "itm",
    "trust",
]

TRANSACTION_TYPES_DICT = {
    "award": "A-Award",
    "conversion": "C-Conversion",
    "return": "D-Return",
    "expire_short": "E-ExpireShort",
    "in_kind": "F-InKind",
    "gift": "G-Gift",
    "expire_long": "H-ExpireLong",
    "discretionary": "I-Discretionary",
    "other": "J-Other",
    "small": "L-Small",
    "exempt": "M-Exempt",
    "otm": "O-OutOfTheMoney",
    "purchase": "P-Purchase",
    "sale": "S-Sale",
    "tender": "U-Tender",
    "will": "W-Will",
    "itm": "X-InTheMoney",
    "trust": "Z-Trust",
}
