#!/usr/bin/env python3
"""
动态星空背景 - 可用于网站背景
作者: AI Assistant
功能: 创建一个美丽的动态星空效果，包含闪烁的星星、流星和渐变背景
"""

import pygame
import random
import math
import sys

# 初始化pygame
pygame.init()

# 屏幕设置
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
FPS = 60

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
BLUE = (0, 50, 100)
DARK_BLUE = (0, 20, 40)
PURPLE = (50, 0, 100)
YELLOW = (255, 255, 200)
LIGHT_BLUE = (100, 150, 255)

class Star:
    """星星类"""
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.size = random.uniform(1, 3)
        self.brightness = random.uniform(0.3, 1.0)
        self.twinkle_speed = random.uniform(0.02, 0.08)
        self.twinkle_phase = random.uniform(0, 2 * math.pi)
        self.color = random.choice([WHITE, YELLOW, LIGHT_BLUE])

    def update(self):
        """更新星星状态"""
        self.twinkle_phase += self.twinkle_speed
        current_brightness = self.brightness * (0.5 + 0.5 * math.sin(self.twinkle_phase))
        self.current_alpha = int(255 * current_brightness)

    def draw(self, screen):
        """绘制星星"""
        # 创建带透明度的颜色
        color_with_alpha = (*self.color, self.current_alpha)

        # 绘制星星主体
        pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), int(self.size))

        # 绘制星星光晕
        if self.size > 2:
            glow_size = int(self.size * 2)
            glow_alpha = max(0, self.current_alpha // 3)
            glow_color = (*self.color, glow_alpha)
            pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), glow_size, 1)

class ShootingStar:
    """流星类"""
    def __init__(self):
        self.reset()

    def reset(self):
        """重置流星位置"""
        self.x = random.randint(-100, SCREEN_WIDTH + 100)
        self.y = random.randint(-100, SCREEN_HEIGHT // 2)
        self.speed_x = random.uniform(3, 8)
        self.speed_y = random.uniform(1, 3)
        self.length = random.randint(20, 60)
        self.brightness = random.uniform(0.7, 1.0)
        self.life = random.randint(60, 120)  # 生命周期
        self.max_life = self.life

    def update(self):
        """更新流星位置"""
        self.x += self.speed_x
        self.y += self.speed_y
        self.life -= 1

        # 如果流星超出屏幕或生命结束，重置
        if (self.x > SCREEN_WIDTH + 100 or
            self.y > SCREEN_HEIGHT + 100 or
            self.life <= 0):
            # 随机决定是否立即生成新流星
            if random.random() < 0.1:  # 10%概率立即重生
                self.reset()
            else:
                self.life = -random.randint(60, 300)  # 等待一段时间

    def draw(self, screen):
        """绘制流星"""
        if self.life <= 0:
            return

        # 计算透明度
        alpha = int(255 * self.brightness * (self.life / self.max_life))

        # 绘制流星尾迹
        for i in range(self.length):
            trail_x = self.x - i * (self.speed_x / 3)
            trail_y = self.y - i * (self.speed_y / 3)

            if 0 <= trail_x <= SCREEN_WIDTH and 0 <= trail_y <= SCREEN_HEIGHT:
                trail_alpha = max(0, alpha - i * 5)
                if trail_alpha > 0:
                    size = max(1, 3 - i // 10)
                    color = (255, 255, 255)
                    pygame.draw.circle(screen, color, (int(trail_x), int(trail_y)), size)

class Nebula:
    """星云类"""
    def __init__(self):
        self.particles = []
        for _ in range(50):
            self.particles.append({
                'x': random.randint(0, SCREEN_WIDTH),
                'y': random.randint(0, SCREEN_HEIGHT),
                'size': random.randint(20, 80),
                'color': random.choice([PURPLE, DARK_BLUE, BLUE]),
                'alpha': random.randint(10, 30),
                'drift_x': random.uniform(-0.5, 0.5),
                'drift_y': random.uniform(-0.5, 0.5)
            })

    def update(self):
        """更新星云"""
        for particle in self.particles:
            particle['x'] += particle['drift_x']
            particle['y'] += particle['drift_y']

            # 边界检查
            if particle['x'] < -particle['size']:
                particle['x'] = SCREEN_WIDTH + particle['size']
            elif particle['x'] > SCREEN_WIDTH + particle['size']:
                particle['x'] = -particle['size']

            if particle['y'] < -particle['size']:
                particle['y'] = SCREEN_HEIGHT + particle['size']
            elif particle['y'] > SCREEN_HEIGHT + particle['size']:
                particle['y'] = -particle['size']

    def draw(self, screen):
        """绘制星云"""
        for particle in self.particles:
            # 创建临时surface用于透明度
            temp_surface = pygame.Surface((particle['size'] * 2, particle['size'] * 2))
            temp_surface.set_alpha(particle['alpha'])
            temp_surface.fill(particle['color'])

            # 绘制圆形星云
            pygame.draw.circle(temp_surface, particle['color'],
                             (particle['size'], particle['size']), particle['size'])

            screen.blit(temp_surface, (particle['x'] - particle['size'],
                                     particle['y'] - particle['size']))

class StarField:
    """星空场景类"""
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("动态星空背景")
        self.clock = pygame.time.Clock()

        # 创建星星
        self.stars = []
        for _ in range(200):
            x = random.randint(0, SCREEN_WIDTH)
            y = random.randint(0, SCREEN_HEIGHT)
            self.stars.append(Star(x, y))

        # 创建流星
        self.shooting_stars = []
        for _ in range(3):
            self.shooting_stars.append(ShootingStar())

        # 创建星云
        self.nebula = Nebula()

        # 背景渐变
        self.create_gradient_background()

    def create_gradient_background(self):
        """创建渐变背景"""
        self.background = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))

        for y in range(SCREEN_HEIGHT):
            # 从深蓝到黑色的渐变
            ratio = y / SCREEN_HEIGHT
            r = int(DARK_BLUE[0] * (1 - ratio))
            g = int(DARK_BLUE[1] * (1 - ratio))
            b = int(DARK_BLUE[2] * (1 - ratio))

            pygame.draw.line(self.background, (r, g, b), (0, y), (SCREEN_WIDTH, y))

    def update(self):
        """更新所有元素"""
        # 更新星星
        for star in self.stars:
            star.update()

        # 更新流星
        for shooting_star in self.shooting_stars:
            shooting_star.update()

        # 更新星云
        self.nebula.update()

    def draw(self):
        """绘制所有元素"""
        # 绘制背景
        self.screen.blit(self.background, (0, 0))

        # 绘制星云
        self.nebula.draw(self.screen)

        # 绘制星星
        for star in self.stars:
            star.draw(self.screen)

        # 绘制流星
        for shooting_star in self.shooting_stars:
            shooting_star.draw(self.screen)

        # 添加一些额外的光效
        self.draw_light_effects()

        pygame.display.flip()

    def draw_light_effects(self):
        """绘制额外的光效"""
        # 随机闪光效果
        if random.random() < 0.01:  # 1%概率
            flash_x = random.randint(0, SCREEN_WIDTH)
            flash_y = random.randint(0, SCREEN_HEIGHT)
            flash_size = random.randint(5, 15)

            # 绘制闪光
            for i in range(flash_size, 0, -2):
                alpha = int(255 * (i / flash_size) * 0.3)
                color = (*WHITE, alpha)
                pygame.draw.circle(self.screen, WHITE, (flash_x, flash_y), i, 1)

    def run(self):
        """运行星空动画"""
        running = True

        while running:
            # 处理事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif event.key == pygame.K_SPACE:
                        # 空格键添加新流星
                        self.shooting_stars.append(ShootingStar())
                    elif event.key == pygame.K_r:
                        # R键重置所有星星
                        self.stars.clear()
                        for _ in range(200):
                            x = random.randint(0, SCREEN_WIDTH)
                            y = random.randint(0, SCREEN_HEIGHT)
                            self.stars.append(Star(x, y))
                    elif event.key == pygame.K_n:
                        # N键重新生成星云
                        self.nebula = Nebula()

            # 更新和绘制
            self.update()
            self.draw()

            # 控制帧率
            self.clock.tick(FPS)

        pygame.quit()
        sys.exit()

def export_to_web():
    """导出为网页可用的代码"""
    web_code = '''
<!-- 动态星空背景 - HTML5 Canvas版本 -->
<canvas id="starfield" style="position: fixed; top: 0; left: 0; z-index: -1;"></canvas>

<script>
// 星空背景JavaScript代码
class StarField {
    constructor() {
        this.canvas = document.getElementById('starfield');
        this.ctx = this.canvas.getContext('2d');
        this.resize();

        this.stars = [];
        this.shootingStars = [];

        this.initStars();
        this.initShootingStars();

        window.addEventListener('resize', () => this.resize());
        this.animate();
    }

    resize() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
    }

    initStars() {
        for (let i = 0; i < 200; i++) {
            this.stars.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                size: Math.random() * 2 + 1,
                brightness: Math.random() * 0.7 + 0.3,
                twinkleSpeed: Math.random() * 0.06 + 0.02,
                twinklePhase: Math.random() * Math.PI * 2,
                color: ['#ffffff', '#ffffcc', '#ccddff'][Math.floor(Math.random() * 3)]
            });
        }
    }

    initShootingStars() {
        for (let i = 0; i < 3; i++) {
            this.shootingStars.push(this.createShootingStar());
        }
    }

    createShootingStar() {
        return {
            x: Math.random() * this.canvas.width,
            y: Math.random() * this.canvas.height * 0.5,
            speedX: Math.random() * 5 + 3,
            speedY: Math.random() * 2 + 1,
            length: Math.random() * 40 + 20,
            life: Math.random() * 60 + 60,
            maxLife: 120
        };
    }

    update() {
        // 更新星星闪烁
        this.stars.forEach(star => {
            star.twinklePhase += star.twinkleSpeed;
        });

        // 更新流星
        this.shootingStars.forEach(star => {
            star.x += star.speedX;
            star.y += star.speedY;
            star.life--;

            if (star.x > this.canvas.width + 100 ||
                star.y > this.canvas.height + 100 ||
                star.life <= 0) {
                Object.assign(star, this.createShootingStar());
            }
        });
    }

    draw() {
        // 清空画布并绘制渐变背景
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#001122');
        gradient.addColorStop(1, '#000000');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制星星
        this.stars.forEach(star => {
            const brightness = star.brightness * (0.5 + 0.5 * Math.sin(star.twinklePhase));
            this.ctx.globalAlpha = brightness;
            this.ctx.fillStyle = star.color;
            this.ctx.beginPath();
            this.ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
            this.ctx.fill();
        });

        // 绘制流星
        this.shootingStars.forEach(star => {
            if (star.life > 0) {
                const alpha = star.life / star.maxLife;
                this.ctx.globalAlpha = alpha;
                this.ctx.strokeStyle = '#ffffff';
                this.ctx.lineWidth = 2;
                this.ctx.beginPath();
                this.ctx.moveTo(star.x, star.y);
                this.ctx.lineTo(star.x - star.speedX * 10, star.y - star.speedY * 10);
                this.ctx.stroke();
            }
        });

        this.ctx.globalAlpha = 1;
    }

    animate() {
        this.update();
        this.draw();
        requestAnimationFrame(() => this.animate());
    }
}

// 启动星空背景
document.addEventListener('DOMContentLoaded', () => {
    new StarField();
});
</script>
'''

    with open('starfield_web.html', 'w', encoding='utf-8') as f:
        f.write(web_code)

    print("✅ 网页版星空背景已导出到 starfield_web.html")

def main():
    """主函数"""
    print("🌟 动态星空背景启动")
    print("=" * 50)
    print("功能特点:")
    print("✨ 200颗闪烁的星星")
    print("🌠 动态流星效果")
    print("🌌 渐变星云背景")
    print("💫 随机闪光效果")
    print("🎨 多种颜色的星星")
    print("=" * 50)
    print("控制说明:")
    print("- ESC键: 退出程序")
    print("- 空格键: 添加新流星")
    print("- R键: 重置所有星星")
    print("- N键: 重新生成星云")
    print("- 关闭窗口: 退出程序")
    print("=" * 50)
    print("🌐 网页版本:")
    print("运行后会自动生成 starfield_web.html 文件")
    print("可直接在浏览器中打开作为网站背景")
    print("=" * 50)

    # 导出网页版本
    export_to_web()

    print("✨ 享受美丽的星空吧！")
    print("按任意键开始...")
    input()

    # 创建并运行星空
    star_field = StarField()
    star_field.run()

if __name__ == "__main__":
    main()