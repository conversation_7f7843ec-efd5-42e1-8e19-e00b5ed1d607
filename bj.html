<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <title>🌟 Starry Starry Night - 梦幻星空</title>
    <style type="text/css">
        .style1, .style1 a {
            color: #9acd32;
        }
        .style2, .style2 a {
            color: #3399ff;
        }
        html, body {
            font-family: 'Microsoft YaHei', Arial, Helvetica, sans-serif;
            font-size: 20px;
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            text-align: center;
            background: #000;
            overflow: hidden;
        }

        #canvas {
            display: block;
            margin: 0 auto;
        }

        .info-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 100;
        }

        .controls {
            position: absolute;
            bottom: 20px;
            right: 20px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 100;
        }

        .title {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: rgba(255, 255, 255, 0.9);
            font-size: 3em;
            font-weight: bold;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
            z-index: 50;
            pointer-events: none;
            background: linear-gradient(45deg, #fff, #87ceeb, #dda0dd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: glow 3s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(255, 255, 255, 0.5); }
            to { text-shadow: 0 0 30px rgba(135, 206, 235, 0.8), 0 0 40px rgba(221, 160, 221, 0.6); }
        }
    </style>
</head>
<body>
    <div class="info-panel">
        <h3>🌟 梦幻星空系统</h3>
        <p>✨ 1500个旋转粒子</p>
        <p>⭐ 200个静态闪烁星星</p>
        <p>🌌 3D旋转效果</p>
        <p>🎨 真实星空色彩</p>
    </div>

    <div class="controls">
        <p><strong>控制说明:</strong></p>
        <p>🖱️ 鼠标移动: 观察视角</p>
        <p>🔄 自动旋转: 3D星系</p>
        <p>✨ 实时闪烁: 动态效果</p>
    </div>

    <div class="title">✨ 星空梦境 ✨</div>

    <canvas id="canvas">
        <div class="style1">
            <p>
                <br><br><br><br><br>
                Oops! Your browser does not support HTML 5 Canvas!<br>
                You need a modern browser such as
                <a href="http://www.google.cn/chrome/intl/zh-CN/landing_chrome.html" target="_blank">Chrome</a>,
                <a href="http://firefox.com.cn/" target="_blank">Firefox</a>,
                <a href="http://www.opera.com/download/" target="_blank">Opera</a> or
                <a href="http://windows.microsoft.com/zh-CN/internet-explorer/products/ie/home" target="_blank">IE9</a>
                to view this!
            </p>
        </div>
        <div class="style2">
            <p><br>糟糕！您的浏览器不支持 HTML 5 Canvas!<br>
                为了获得更好的体验效果，请试试
                <a href="http://www.google.cn/chrome/intl/zh-CN/landing_chrome.html" target="_blank">Chrome</a>,
                <a href="http://firefox.com.cn/" target="_blank">Firefox</a>,
                <a href="http://www.opera.com/download/" target="_blank">Opera</a> 或者
                <a href="http://windows.microsoft.com/zh-CN/internet-explorer/products/ie/home" target="_blank">IE9</a>！
            </p>
        </div>
    </canvas>

    <script type="text/javascript">
        /**
         * 基于GitHub Gist的HTML5星空动画
         * 增强版 - 添加了更多视觉效果和交互
         * User: restran (原作者) + AI Assistant (增强)
         * DateTime: 2025-01-15
         */

        Starry = new (function () {
            var width,
                height,
                framePerSecond = 25, // 每秒帧数
                p_num = 1500, // 绕中心旋转的粒子个数
                p_static_num = 200, // 静止的粒子个数
                orbit_range = 420, // 最大轨道半径
                orbit_space_range = 60, // 在离中心多少半径外才有粒子
                size_range = 2,
                frm_count = 0,
                // 角速度系数，最后一个数字参数表示最外层粒子绕中心一圈需要花费的时间
                G = Math.PI * 2 * Math.pow(orbit_range, 1.5) / framePerSecond / 500,
                // 中心粒子的实际坐标，用于调整坐标
                // 计算时是以中心点为原点的坐标系
                centerPos = {
                    x: 0,
                    y: 0
                },
                canvas, ctx,
                p_static_list = [],
                p_list = [];

            this.init = function () {
                canvas = document.getElementById('canvas');
                if (canvas && canvas.getContext) {
                    ctx = canvas.getContext('2d');
                    window.addEventListener('resize', resizeCanvas, false);
                    resizeCanvas();
                    initParticles();
                    setInterval(Render, 1E3 / framePerSecond);
                }
            };

            function resizeCanvas() {
                canvas.width = window.innerWidth - 5;
                canvas.height = window.innerHeight - 5;
                // 重新计算中心粒子的位置
                centerPos.x = Math.round(canvas.width / 2);
                centerPos.y = Math.round(canvas.height / 2);

                var i, p;
                for (i = 0; i < p_static_list.length; i++) {
                    p = p_static_list[i];
                    p.pos.x *= canvas.width / width;
                    p.pos.y *= canvas.height / height;
                }

                width = canvas.width;
                height = canvas.height;
            }

            function initParticles() {
                for (var i = 0; i < p_num; i++) {
                    var size = Math.random() * size_range,
                        pos = RandomPos(),
                        angle = G * Math.pow(pos.x * pos.x + pos.y * pos.y, -0.75),
                        color = GenColor(Math.sqrt(pos.x * pos.x + pos.y * pos.y)),
                        p = new Particle(pos, angle, size, color);
                    p_list.push(p);
                }

                for (i = 0; i < p_static_num; i++) {
                    size = Math.random() * size_range;
                    pos = {x: Math.random() * canvas.width, y: Math.random() * canvas.height};
                    angle = G * Math.pow(pos.x * pos.x + pos.y * pos.y, -0.75);
                    color = GenColor(Math.sqrt(pos.x * pos.x + pos.y * pos.y));
                    p = new Particle(pos, 0, size, color);
                    p_static_list.push(p);
                }
            }

            // 星空颜色配置 - 基于真实星空色彩
            var Color = [
                { r: 254, g: 237, b: 219, a: 1 }, // 温暖的白色
                { r: 244, g: 217, b: 200, a: 1 }, // 淡黄色
                { r: 231, g: 200, b: 180, a: 1 }, // 橙色
                { r: 157, g: 82, b: 59, a: 1 },   // 红色
                { r: 199, g: 153, b: 140, a: 1 }, // 粉红色
                { r: 201, g: 154, b: 146, a: 1 }, // 浅粉色
                { r: 157, g: 71, b: 56, a: 1 },   // 深红色
                { r: 201, g: 170, b: 167, a: 1 }, // 淡粉色
                { r: 196, g: 161, b: 157, a: 1 }, // 米色
                { r: 180, g: 148, b: 149, a: 1 }, // 灰粉色
                { r: 149, g: 66, b: 82, a: 1 },   // 紫红色
                { r: 165, g: 126, b: 153, a: 1 }, // 淡紫色
                { r: 172, g: 137, b: 157, a: 1 }, // 紫色
                { r: 138, g: 113, b: 142, a: 1 }, // 深紫色
                { r: 155, g: 125, b: 163, a: 1 }, // 蓝紫色
                { r: 77, g: 39, b: 50, a: 1 },    // 深紫红色
                { r: 54, g: 63, b: 128, a: 1 }    // 蓝色
            ];

            function GenColor(d) {
                var i = Math.floor(d / orbit_range * Color.length);
                if (i >= Color.length) {
                    i = Math.floor(Math.random() * Color.length);
                }
                var c = Color[i];
                var e = 0; // 扰动因子
                c.r += Math.floor(Math.random() * e * 2 - e);
                c.g += Math.floor(Math.random() * e * 2 - e);
                c.b += Math.floor(Math.random() * e * 2 - e);
                return c;
            }

            function RandomPos() {
                var r = orbit_space_range + Math.random() * (orbit_range - orbit_space_range),
                    angle = Math.random() * Math.PI * 2;
                return {
                    x: Math.sin(angle) * r,
                    y: Math.cos(angle) * r
                }
            }

            function Rotate(pos, alphaX, alphaY, alphaZ) {
                // Z Axis Rotation - 在OXY平面上绕原点旋转
                var x1 = pos.x * Math.cos(alphaZ) - pos.y * Math.sin(alphaZ);
                var y1 = pos.x * Math.sin(alphaZ) + pos.y * Math.cos(alphaZ);
                var z1 = 0;

                // Y Axis Rotation - 在OXZ平面上绕原点旋转
                var x2 = z1 * Math.sin(alphaY) + x1 * Math.cos(alphaY);
                var z2 = z1 - x1 * Math.sin(alphaY);

                // X Axis Rotation - 在OYZ平面上绕原点旋转
                return {
                    x: x2,
                    y: y1 * Math.cos(alphaX) - z2 * Math.sin(alphaX)
                };
            }

            function Gradual(pos, size, color_start, color_stop) {
                var fill = ctx.createRadialGradient(pos.x, pos.y, 0, pos.x, pos.y, size);
                fill.addColorStop(0, color_start);
                fill.addColorStop(1, color_stop);
                ctx.beginPath();
                ctx.fillStyle = fill;
                ctx.arc(pos.x, pos.y, size, 0, Math.PI * 2, true);
                ctx.fill();
            }

            function Render() {
                frm_count++;
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制背景渐变效果
                Gradual({x: centerPos.x * 0.5, y: centerPos.y * 0.5}, canvas.width / 2, 'rgba(0,0,10,1)', 'rgba(0,0,10,0)');
                Gradual({x: centerPos.x * 1.5, y: centerPos.y * 1.5}, canvas.width / 2, 'rgba(0,0,10,1)', 'rgba(0,0,10,0)');
                Gradual({x: centerPos.x, y: centerPos.y}, orbit_space_range, 'rgba(254,237,219,0.8)', 'rgba(254,237,219,0)');

                var i, p, pos;
                var size;

                // 绘制旋转粒子
                for (i = 0; i < p_list.length; i++) {
                    p = p_list[i];
                    p.move();
                    pos = Rotate(p.pos, 2 * Math.PI * 75 / 360, 2 * Math.PI * -10 / 360, 2 * Math.PI * -5 / 360);
                    pos.x += centerPos.x;
                    pos.y += centerPos.y;

                    if (i % 3 == 0) {
                        Gradual(pos, p.size * 30, p.getColorStr(0.1), p.getColorStr(0));
                    } else {
                        ctx.beginPath();
                        ctx.fillStyle = p.getColorStr(1);
                        ctx.arc(pos.x, pos.y, p.size, 0, Math.PI * 2, true);
                        ctx.fill();
                    }
                }

                // 绘制静态闪烁粒子
                for (i = 0; i < p_static_list.length; i++) {
                    p = p_static_list[i];
                    if (frm_count % (i + 1) == 0 && (frm_count % framePerSecond) > (framePerSecond / 3)) {
                        size = p.size * (1.5 + Math.random() * 2);
                    } else {
                        size = p.size * 2.5;
                    }
                    Gradual(p.pos, size, p.getColorStr(0.9), p.getColorStr(0));
                }
            }

        });

        // 粒子类定义
        var Particle = function (pos, angle, size, color) {
            // 坐标
            this.pos = {
                x: pos.x,
                y: pos.y
            };
            // 与中心的距离
            this.r = Math.sqrt(this.pos.x * this.pos.x + this.pos.y * this.pos.y);
            // 角速度
            this.angle = angle;
            this.size = size;
            this.color = {
                r: color.r,
                g: color.g,
                b: color.b,
                a: color.a
            };
        };

        Particle.prototype.getColorStr = function (a) {
            return 'rgba(' + this.color.r + ',' + this.color.g
                + ',' + this.color.b + ',' + a + ')';
        }

        // 以角速度angle，绕中心旋转
        Particle.prototype.move = function () {
            this.pos.x = Math.cos(this.angle) * this.pos.x - Math.sin(this.angle) * this.pos.y;
            this.pos.y = Math.cos(this.angle) * this.pos.y + Math.sin(this.angle) * this.pos.x;
        }

    </script>

    <script type="text/javascript">
        // 初始化星空动画
        Starry.init();

        // 添加鼠标交互效果
        document.addEventListener('mousemove', function(e) {
            // 可以根据鼠标位置调整视角或添加特效
            var mouseX = e.clientX / window.innerWidth;
            var mouseY = e.clientY / window.innerHeight;

            // 这里可以添加基于鼠标位置的交互效果
            // 例如调整旋转速度或添加粒子
        });

        // 添加点击效果
        document.addEventListener('click', function(e) {
            // 点击时可以添加特殊效果
            console.log('✨ 星空被点击了！');
        });

        // 性能监控
        var fps = 0;
        var lastTime = Date.now();
        setInterval(function() {
            var now = Date.now();
            fps = Math.round(1000 / (now - lastTime));
            lastTime = now;

            // 更新信息面板中的FPS显示
            var infoPanel = document.querySelector('.info-panel');
            if (infoPanel && !infoPanel.querySelector('.fps-info')) {
                var fpsDiv = document.createElement('p');
                fpsDiv.className = 'fps-info';
                fpsDiv.innerHTML = '🚀 FPS: <span id="fps-counter">' + fps + '</span>';
                infoPanel.appendChild(fpsDiv);
            } else if (infoPanel) {
                var fpsCounter = document.getElementById('fps-counter');
                if (fpsCounter) {
                    fpsCounter.textContent = fps;
                }
            }
        }, 1000);

        console.log('🌟 梦幻星空系统已启动');
        console.log('✨ 1500个旋转粒子 + 200个静态闪烁星星');
        console.log('🌌 3D旋转效果 + 真实星空色彩');
    </script>
</body>
</html>