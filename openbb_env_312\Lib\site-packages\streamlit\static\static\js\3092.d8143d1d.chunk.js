"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[3092],{49839:(e,t,n)=>{n.r(t),n.d(t,{default:()=>Nt});var i=n(66845),o=n(67930),a=n(6998),r=n(17330),l=n(57463),s=n(97943),d=n(41342),c=n(17875),u=n(87814),m=n(62622),h=n(16295),p=n(50641),g=n(25621),f=n(34367),b=n(31011),v=n(21e3),y=n(68411),w=n(9003),x=n(81354),C=n(46927),E=n(1515),M=n(92627);const T=(0,E.Z)("div",{target:"e2wxzia1"})((e=>{let{theme:t,locked:n,target:i}=e;return{padding:"0.5rem 0 0.5rem 0.5rem",position:"absolute",top:n?"-2.4rem":"-1rem",right:t.spacing.none,transition:"none",...!n&&{opacity:0,"&:active, &:focus-visible, &:hover":{transition:"opacity 150ms 100ms, top 100ms 100ms",opacity:1,top:"-2.4rem"},...i&&{["".concat(i,":hover &, ").concat(i,":active &, ").concat(i,":focus-visible &")]:{transition:"opacity 150ms 100ms, top 100ms 100ms",opacity:1,top:"-2.4rem"}}}}}),""),k=(0,E.Z)("div",{target:"e2wxzia0"})((e=>{let{theme:t}=e;return{color:(0,M.Iy)(t)?t.colors.fadedText60:t.colors.bodyText,display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"flex-end",boxShadow:"1px 2px 8px rgba(0, 0, 0, 0.08)",borderRadius:t.radii.lg,backgroundColor:t.colors.lightenedBg05,width:"fit-content",zIndex:t.zIndices.sidebar+1}}),"");var R=n(40864);function N(e){let{label:t,show_label:n,icon:i,onClick:o}=e;const a=(0,g.u)(),r=n?t:"";return(0,R.jsx)("div",{"data-testid":"stElementToolbarButton",children:(0,R.jsx)(y.Z,{content:(0,R.jsx)(v.ZP,{source:t,allowHTML:!1,style:{fontSize:a.fontSizes.sm}}),placement:y.u.TOP,onMouseEnterDelay:1e3,inline:!0,children:(0,R.jsxs)(w.ZP,{onClick:e=>{o&&o(),e.stopPropagation()},kind:x.nW.ELEMENT_TOOLBAR,children:[i&&(0,R.jsx)(C.Z,{content:i,size:"md",testid:"stElementToolbarButtonIcon"}),r&&(0,R.jsx)("span",{children:r})]})})})}const S=e=>{let{onExpand:t,onCollapse:n,isFullScreen:i,locked:o,children:a,target:r,disableFullscreenMode:l}=e;return(0,R.jsx)(T,{className:"stElementToolbar","data-testid":"stElementToolbar",locked:o||i,target:r,children:(0,R.jsxs)(k,{children:[a,t&&!l&&!i&&(0,R.jsx)(N,{label:"Fullscreen",icon:f.i,onClick:()=>t()}),n&&!l&&i&&(0,R.jsx)(N,{label:"Close fullscreen",icon:b.m,onClick:()=>n()})]})})};var _=n(38145),I=n.n(_),O=n(96825),D=n.n(O),F=n(29724),A=n.n(F),H=n(52347),z=n(53608),V=n.n(z),j=(n(87717),n(55842),n(91191));const L=["true","t","yes","y","on","1"],W=["false","f","no","n","off","0"];function B(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e="\u26a0\ufe0f ".concat(e),{kind:o.p6.Text,readonly:!0,allowOverlay:!0,data:e+(t?"\n\n".concat(t,"\n"):""),displayData:e,isError:!0}}function Y(e){return e.hasOwnProperty("isError")&&e.isError}function P(e){return e.hasOwnProperty("isMissingValue")&&e.isMissingValue}function Z(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]?{kind:o.p6.Loading,allowOverlay:!1,isMissingValue:!0}:{kind:o.p6.Loading,allowOverlay:!1}}function q(e,t){const n=t?"faded":"normal";return{kind:o.p6.Text,data:"",displayData:"",allowOverlay:!0,readonly:e,style:n}}function J(e){return{id:e.id,title:e.title,hasMenu:!1,themeOverride:e.themeOverride,icon:e.icon,...e.isStretched&&{grow:e.isIndex?1:3},...e.width&&{width:e.width}}}function U(e,t){return(0,p.le)(e)?t||{}:(0,p.le)(t)?e||{}:D()(e,t)}function K(e){if((0,p.le)(e))return[];if("number"===typeof e||"boolean"===typeof e)return[e];if("string"===typeof e){if(""===e)return[];if(!e.trim().startsWith("[")||!e.trim().endsWith("]"))return e.split(",");try{return JSON.parse(e)}catch(t){return[e]}}try{const t=JSON.parse(JSON.stringify(e,((e,t)=>"bigint"===typeof t?Number(t):t)));return Array.isArray(t)?t.map((e=>["string","number","boolean","null"].includes(typeof e)?e:G(e))):[G(t)]}catch(t){return[G(e)]}}function G(e){try{try{return I()(e)}catch(t){return JSON.stringify(e,((e,t)=>"bigint"===typeof t?Number(t):t))}}catch(t){return"[".concat(typeof e,"]")}}function X(e){if((0,p.le)(e))return null;if("boolean"===typeof e)return e;const t=G(e).toLowerCase().trim();return""===t?null:!!L.includes(t)||!W.includes(t)&&void 0}function Q(e){if((0,p.le)(e))return null;if(Array.isArray(e))return NaN;if("string"===typeof e){if(0===e.trim().length)return null;try{const t=A().unformat(e.trim());if((0,p.bb)(t))return t}catch(t){}}else if(e instanceof Int32Array)return Number(e[0]);return Number(e)}function $(e,t,n){return Number.isNaN(e)||!Number.isFinite(e)?"":(0,p.le)(t)||""===t?(0===n&&(e=Math.round(e)),A()(e).format((0,p.bb)(n)?"0,0.".concat("0".repeat(n)):"0,0.[0000]")):"percent"===t?new Intl.NumberFormat(void 0,{style:"percent",minimumFractionDigits:2,maximumFractionDigits:2}).format(e):["compact","scientific","engineering"].includes(t)?new Intl.NumberFormat(void 0,{notation:t}).format(e):"duration[ns]"===t?V().duration(e/1e6,"milliseconds").humanize():t.startsWith("period[")?j.fu.formatPeriodType(BigInt(e),t):(0,H.sprintf)(t,e)}function ee(e,t){return"locale"===t?new Intl.DateTimeFormat(void 0,{dateStyle:"medium",timeStyle:"medium"}).format(e.toDate()):"distance"===t?e.fromNow():"relative"===t?e.calendar():e.format(t)}function te(e){if((0,p.le)(e))return null;if(e instanceof Date)return isNaN(e.getTime())?void 0:e;if("string"===typeof e&&0===e.trim().length)return null;try{const t=Number(e);if(!isNaN(t)){let e=t;t>=10**18?e=t/1e3**3:t>=10**15?e=t/1e6:t>=10**12&&(e=t/1e3);const n=V().unix(e).utc();if(n.isValid())return n.toDate()}if("string"===typeof e){const t=V().utc(e);if(t.isValid())return t.toDate();const n=V().utc(e,[V().HTML5_FMT.TIME_MS,V().HTML5_FMT.TIME_SECONDS,V().HTML5_FMT.TIME]);if(n.isValid())return n.toDate()}}catch(t){return}}function ne(e){if(e%1===0)return 0;let t=e.toString();return-1!==t.indexOf("e")&&(t=e.toLocaleString("fullwide",{useGrouping:!1,maximumFractionDigits:20})),-1===t.indexOf(".")?0:t.split(".")[1].length}const ie=new RegExp(/(\r\n|\n|\r)/gm);function oe(e){return-1!==e.indexOf("\n")?e.replace(ie," "):e}var ae=n(23849);function re(e){const t={kind:o.p6.Text,data:"",displayData:"",allowOverlay:!0,contentAlignment:e.contentAlignment,readonly:!0,style:e.isIndex?"faded":"normal"};return{...e,kind:"object",sortMode:"default",isEditable:!1,getCell(e){try{const n=(0,p.bb)(e)?G(e):null,i=(0,p.bb)(n)?oe(n):"";return{...t,data:n,displayData:i,isMissingValue:(0,p.le)(e)}}catch(n){return B(G(e),"The value cannot be interpreted as a string. Error: ".concat(n))}},getCellValue:e=>void 0===e.data?null:e.data}}re.isEditableType=!1;const le=re;function se(e){const t=e.columnTypeOptions||{};let n;if(t.validate)try{n=new RegExp(t.validate,"us")}catch(r){n="Invalid validate regex: ".concat(t.validate,".\nError: ").concat(r)}const i={kind:o.p6.Text,data:"",displayData:"",allowOverlay:!0,contentAlignment:e.contentAlignment,readonly:!e.isEditable,style:e.isIndex?"faded":"normal"},a=i=>{if((0,p.le)(i))return!e.isRequired;let o=G(i),a=!1;return t.max_chars&&o.length>t.max_chars&&(o=o.slice(0,t.max_chars),a=!0),!(n instanceof RegExp&&!1===n.test(o))&&(!a||o)};return{...e,kind:"text",sortMode:"default",validateInput:a,getCell(e,t){if("string"===typeof n)return B(G(e),n);if(t){const t=a(e);if(!1===t)return B(G(e),"Invalid input.");"string"===typeof t&&(e=t)}try{const t=(0,p.bb)(e)?G(e):null,n=(0,p.bb)(t)?oe(t):"";return{...i,isMissingValue:(0,p.le)(t),data:t,displayData:n}}catch(r){return B("Incompatible value","The value cannot be interpreted as string. Error: ".concat(r))}},getCellValue:e=>void 0===e.data?null:e.data}}se.isEditableType=!0;const de=se;function ce(e,t){return e=t.startsWith("+")||t.startsWith("-")?e.utcOffset(t,!1):e.tz(t)}function ue(e,t,n,i,a,r,l){var s;const d=U({format:n,step:i,timezone:l},t.columnTypeOptions);let c,u,m;if((0,p.bb)(d.timezone))try{var h;c=(null===(h=ce(V()(),d.timezone))||void 0===h?void 0:h.utcOffset())||void 0}catch(b){}(0,p.bb)(d.min_value)&&(u=te(d.min_value)||void 0),(0,p.bb)(d.max_value)&&(m=te(d.max_value)||void 0);const g={kind:o.p6.Custom,allowOverlay:!0,copyData:"",readonly:!t.isEditable,contentAlign:t.contentAlignment,style:t.isIndex?"faded":"normal",data:{kind:"date-picker-cell",date:void 0,displayDate:"",step:(null===(s=d.step)||void 0===s?void 0:s.toString())||"1",format:a,min:u,max:m}},f=e=>{const n=te(e);return null===n?!t.isRequired:void 0!==n&&(!((0,p.bb)(u)&&r(n)<r(u))&&!((0,p.bb)(m)&&r(n)>r(m)))};return{...t,kind:e,sortMode:"default",validateInput:f,getCell(e,t){if(!0===t){const t=f(e);if(!1===t)return B(G(e),"Invalid input.");t instanceof Date&&(e=t)}const i=te(e);let o="",a="",r=c;if(void 0===i)return B(G(e),"The value cannot be interpreted as a datetime object.");if(null!==i){let e=V().utc(i);if(!e.isValid())return B(G(i),"This should never happen. Please report this bug. \nError: ".concat(e.toString()));if(d.timezone){try{e=ce(e,d.timezone)}catch(b){return B(e.toISOString(),"Failed to adjust to the provided timezone: ".concat(d.timezone,". \nError: ").concat(b))}r=e.utcOffset()}try{a=ee(e,d.format||n)}catch(b){return B(e.toISOString(),"Failed to format the date for rendering with: ".concat(d.format,". \nError: ").concat(b))}o=ee(e,n)}return{...g,copyData:o,isMissingValue:(0,p.le)(i),data:{...g.data,date:i,displayDate:a,timezoneOffset:r}}},getCellValue(e){var t;return(0,p.le)(null===e||void 0===e||null===(t=e.data)||void 0===t?void 0:t.date)?null:r(e.data.date)}}}function me(e){var t,n,i,o,a;let r="YYYY-MM-DD HH:mm:ss";(null===(t=e.columnTypeOptions)||void 0===t?void 0:t.step)>=60?r="YYYY-MM-DD HH:mm":(null===(n=e.columnTypeOptions)||void 0===n?void 0:n.step)<1&&(r="YYYY-MM-DD HH:mm:ss.SSS");const l=null===(i=e.arrowType)||void 0===i||null===(o=i.meta)||void 0===o?void 0:o.timezone,s=(0,p.bb)(l)||(0,p.bb)(null===e||void 0===e||null===(a=e.columnTypeOptions)||void 0===a?void 0:a.timezone);return ue("datetime",e,s?r+"Z":r,1,"datetime-local",(e=>s?e.toISOString():e.toISOString().replace("Z","")),l)}function he(e){var t,n;let i="HH:mm:ss";return(null===(t=e.columnTypeOptions)||void 0===t?void 0:t.step)>=60?i="HH:mm":(null===(n=e.columnTypeOptions)||void 0===n?void 0:n.step)<1&&(i="HH:mm:ss.SSS"),ue("time",e,i,1,"time",(e=>e.toISOString().split("T")[1].replace("Z","")))}function pe(e){return ue("date",e,"YYYY-MM-DD",1,"date",(e=>e.toISOString().split("T")[0]))}function ge(e){const t={kind:o.p6.Boolean,data:!1,allowOverlay:!1,contentAlign:e.contentAlignment,readonly:!e.isEditable,style:e.isIndex?"faded":"normal"};return{...e,kind:"checkbox",sortMode:"default",getCell(e){let n=null;return n=X(e),void 0===n?B(G(e),"The value cannot be interpreted as boolean."):{...t,data:n,isMissingValue:(0,p.le)(n)}},getCellValue:e=>void 0===e.data?null:e.data}}me.isEditableType=!0,he.isEditableType=!0,pe.isEditableType=!0,ge.isEditableType=!0;const fe=ge;function be(e){return e.startsWith("int")&&!e.startsWith("interval")||"range"===e||e.startsWith("uint")}function ve(e){const t=j.fu.getTypeName(e.arrowType);let n;"timedelta64[ns]"===t?n="duration[ns]":t.startsWith("period[")&&(n=t);const i=U({step:be(t)?1:void 0,min_value:t.startsWith("uint")?0:void 0,format:n},e.columnTypeOptions),a=(0,p.le)(i.min_value)||i.min_value<0,r=(0,p.bb)(i.step)&&!Number.isNaN(i.step)?ne(i.step):void 0,l={kind:o.p6.Number,data:void 0,displayData:"",readonly:!e.isEditable,allowOverlay:!0,contentAlign:e.contentAlignment||"right",style:e.isIndex?"faded":"normal",allowNegative:a,fixedDecimals:r},s=t=>{let n=Q(t);if((0,p.le)(n))return!e.isRequired;if(Number.isNaN(n))return!1;let o=!1;return(0,p.bb)(i.max_value)&&n>i.max_value&&(n=i.max_value,o=!0),!((0,p.bb)(i.min_value)&&n<i.min_value)&&(!o||n)};return{...e,kind:"number",sortMode:"smart",validateInput:s,getCell(e,t){if(!0===t){const t=s(e);if(!1===t)return B(G(e),"Invalid input.");"number"===typeof t&&(e=t)}let n=Q(e),o="";if((0,p.bb)(n)){if(Number.isNaN(n))return B(G(e),"The value cannot be interpreted as a number.");if((0,p.bb)(r)&&(a=n,n=0===(d=r)?Math.trunc(a):Math.trunc(a*10**d)/10**d),Number.isInteger(n)&&!Number.isSafeInteger(n))return B(G(e),"The value is larger than the maximum supported integer values in number columns (2^53).");try{o=$(n,i.format,r)}catch(c){return B(G(n),(0,p.bb)(i.format)?"Failed to format the number based on the provided format configuration: (".concat(i.format,"). Error: ").concat(c):"Failed to format the number. Error: ".concat(c))}}var a,d;return{...l,data:n,displayData:o,isMissingValue:(0,p.le)(n)}},getCellValue:e=>void 0===e.data?null:e.data}}ve.isEditableType=!0;const ye=ve;function we(e){let t="string";const n=U({options:"bool"===j.fu.getTypeName(e.arrowType)?[!0,!1]:[]},e.columnTypeOptions),i=new Set(n.options.map((e=>typeof e)));1===i.size&&(i.has("number")||i.has("bigint")?t="number":i.has("boolean")&&(t="boolean"));const a={kind:o.p6.Custom,allowOverlay:!0,copyData:"",contentAlign:e.contentAlignment,readonly:!e.isEditable,data:{kind:"dropdown-cell",allowedValues:[...!0!==e.isRequired?[null]:[],...n.options.filter((e=>null!==e&&""!==e)).map((e=>G(e)))],value:"",readonly:!e.isEditable}};return{...e,kind:"selectbox",sortMode:"default",getCell(e,t){let n=null;return(0,p.bb)(e)&&""!==e&&(n=G(e)),t&&!a.data.allowedValues.includes(n)?B(G(n),"The value is not part of the allowed options."):{...a,isMissingValue:null===n,copyData:n||"",data:{...a.data,value:n}}},getCellValue(e){var n,i,o,a,r,l,s;return(0,p.le)(null===(n=e.data)||void 0===n?void 0:n.value)||""===(null===(i=e.data)||void 0===i?void 0:i.value)?null:"number"===t?null!==(a=Q(null===(r=e.data)||void 0===r?void 0:r.value))&&void 0!==a?a:null:"boolean"===t?null!==(l=X(null===(s=e.data)||void 0===s?void 0:s.value))&&void 0!==l?l:null:null===(o=e.data)||void 0===o?void 0:o.value}}}we.isEditableType=!0;const xe=we;function Ce(e){const t={kind:o.p6.Bubble,data:[],allowOverlay:!0,contentAlign:e.contentAlignment,style:e.isIndex?"faded":"normal"};return{...e,kind:"list",sortMode:"default",isEditable:!1,getCell(e){const n=(0,p.le)(e)?[]:K(e);return{...t,data:n,isMissingValue:(0,p.le)(e),copyData:(0,p.le)(e)?"":G(n.map((e=>"string"===typeof e&&e.includes(",")?e.replace(/,/g," "):e)))}},getCellValue:e=>(0,p.le)(e.data)||P(e)?null:e.data}}Ce.isEditableType=!1;const Ee=Ce;function Me(e,t,n){const i=new RegExp("".concat(e,"[,\\s].*{(?:[^}]*[\\s;]{1})?").concat(t,":\\s*([^;}]+)[;]?.*}"),"gm");n=n.replace(/{/g," {");const o=i.exec(n);if(o)return o[1].trim()}function Te(e,t){const n=e.types.index[t],i=e.indexNames[t];let o=!0;return"range"===j.fu.getTypeName(n)&&(o=!1),{id:"index-".concat(t),name:i,title:i,isEditable:o,arrowType:n,isIndex:!0,isHidden:!1}}function ke(e,t){const n=e.columns[0][t];let i,o=e.types.data[t];if((0,p.le)(o)&&(o={meta:null,numpy_type:"object",pandas_type:"object"}),"categorical"===j.fu.getTypeName(o)){const n=e.getCategoricalOptions(t);(0,p.bb)(n)&&(i={options:n})}return{id:"column-".concat(n,"-").concat(t),name:n,title:n,isEditable:!0,arrowType:o,columnTypeOptions:i,isIndex:!1,isHidden:!1}}function Re(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;const i=e.arrowType?j.fu.getTypeName(e.arrowType):null;let a;if("object"===e.kind)a=e.getCell((0,p.bb)(t.content)?oe(j.fu.format(t.content,t.contentType,t.field)):null);else if(["time","date","datetime"].includes(e.kind)&&(0,p.bb)(t.content)&&("number"===typeof t.content||"bigint"===typeof t.content)){var r,l;let n;var s,d,c;if("time"===i&&(0,p.bb)(null===(r=t.field)||void 0===r||null===(l=r.type)||void 0===l?void 0:l.unit))n=V().unix(j.fu.convertToSeconds(t.content,null!==(s=null===(d=t.field)||void 0===d||null===(c=d.type)||void 0===c?void 0:c.unit)&&void 0!==s?s:0)).utc().toDate();else n=V().utc(Number(t.content)).toDate();a=e.getCell(n)}else if("decimal"===i){const n=(0,p.le)(t.content)?null:j.fu.format(t.content,t.contentType,t.field);a=e.getCell(n)}else a=e.getCell(t.content);if(Y(a))return a;if(!e.isEditable){if((0,p.bb)(t.displayContent)){var u;const e=oe(t.displayContent);a.kind===o.p6.Text||a.kind===o.p6.Number||a.kind===o.p6.Uri?a={...a,displayData:e}:a.kind===o.p6.Custom&&"date-picker-cell"===(null===(u=a.data)||void 0===u?void 0:u.kind)&&(a={...a,data:{...a.data,displayDate:e}})}n&&t.cssId&&(a=function(e,t,n){const i={},o=Me(t,"color",n);o&&(i.textDark=o);const a=Me(t,"background-color",n);return a&&(i.bgCell=a),"yellow"===a&&void 0===o&&(i.textDark="#31333F"),i?{...e,themeOverride:i}:e}(a,t.cssId,n))}return a}function Ne(e){const t=e.columnTypeOptions||{};let n,i;if(t.validate)try{n=new RegExp(t.validate,"us")}catch(l){n="Invalid validate regex: ".concat(t.validate,".\nError: ").concat(l)}if(!(0,p.le)(t.display_text)&&t.display_text.includes("(")&&t.display_text.includes(")"))try{i=new RegExp(t.display_text,"us")}catch(l){i=void 0}const a={kind:o.p6.Uri,readonly:!e.isEditable,allowOverlay:!0,contentAlign:e.contentAlignment,style:e.isIndex?"faded":"normal",hoverEffect:!0,data:"",displayData:"",copyData:""},r=i=>{if((0,p.le)(i))return!e.isRequired;const o=G(i);return!(t.max_chars&&o.length>t.max_chars)&&!(n instanceof RegExp&&!1===n.test(o))};return{...e,kind:"link",sortMode:"default",validateInput:r,getCell(e,o){if((0,p.le)(e))return{...a,data:null,isMissingValue:!0,onClickUri:()=>{}};const s=e;if("string"===typeof n)return B(G(s),n);if(o){if(!1===r(s))return B(G(s),"Invalid input.")}let d="";return s&&(d=void 0!==i?function(e,t){if((0,p.le)(t))return"";try{const n=t.match(e);return n&&void 0!==n[1]?decodeURI(n[1]):t}catch(l){return t}}(i,s):t.display_text||s),{...a,data:s,displayData:d,isMissingValue:(0,p.le)(s),onClickUri:e=>{window.open(s.startsWith("www.")?"https://".concat(s):s,"_blank","noopener,noreferrer"),e.preventDefault()},copyData:s}},getCellValue:e=>(0,p.le)(e.data)?null:e.data}}Ne.isEditableType=!0;const Se=Ne;function _e(e){const t={kind:o.p6.Image,data:[],displayData:[],readonly:!0,allowOverlay:!0,contentAlign:e.contentAlignment||"center",style:e.isIndex?"faded":"normal"};return{...e,kind:"image",sortMode:"default",isEditable:!1,getCell(e){const n=(0,p.bb)(e)?[G(e)]:[];return{...t,data:n,isMissingValue:!(0,p.bb)(e),displayData:n}},getCellValue:e=>void 0===e.data||0===e.data.length?null:e.data[0]}}_e.isEditableType=!1;const Ie=_e;function Oe(e){const t=be(j.fu.getTypeName(e.arrowType)),n=U({min_value:0,max_value:t?100:1,step:t?1:.01,format:t?"%3d%%":"percent"},e.columnTypeOptions);let i;try{i=$(n.max_value,n.format)}catch(l){i=G(n.max_value)}const a=(0,p.le)(n.step)||Number.isNaN(n.step)?void 0:ne(n.step),r={kind:o.p6.Custom,allowOverlay:!1,copyData:"",contentAlign:e.contentAlignment,data:{kind:"range-cell",min:n.min_value,max:n.max_value,step:n.step,value:n.min_value,label:String(n.min_value),measureLabel:i,readonly:!0}};return{...e,kind:"progress",sortMode:"smart",isEditable:!1,getCell(e){if((0,p.le)(e))return Z();if((0,p.le)(n.min_value)||(0,p.le)(n.max_value)||Number.isNaN(n.min_value)||Number.isNaN(n.max_value)||n.min_value>=n.max_value)return B("Invalid min/max parameters","The min_value (".concat(n.min_value,") and max_value (").concat(n.max_value,") parameters must be valid numbers."));if((0,p.le)(n.step)||Number.isNaN(n.step))return B("Invalid step parameter","The step parameter (".concat(n.step,") must be a valid number."));const t=Q(e);if(Number.isNaN(t)||(0,p.le)(t))return B(G(e),"The value cannot be interpreted as a number.");if(Number.isInteger(t)&&!Number.isSafeInteger(t))return B(G(e),"The value is larger than the maximum supported integer values in number columns (2^53).");let i="";try{i=$(t,n.format,a)}catch(l){return B(G(t),(0,p.bb)(n.format)?"Failed to format the number based on the provided format configuration: (".concat(n.format,"). Error: ").concat(l):"Failed to format the number. Error: ".concat(l))}const o=Math.min(n.max_value,Math.max(n.min_value,t));return{...r,isMissingValue:(0,p.le)(e),copyData:String(t),data:{...r.data,value:o,label:i}}},getCellValue(e){var t,n;return e.kind===o.p6.Loading||void 0===(null===(t=e.data)||void 0===t?void 0:t.value)?null:null===(n=e.data)||void 0===n?void 0:n.value}}}Oe.isEditableType=!1;const De=Oe;function Fe(e,t,n){const i=U({y_min:0,y_max:1},t.columnTypeOptions),a={kind:o.p6.Custom,allowOverlay:!1,copyData:"",contentAlign:t.contentAlignment,data:{kind:"sparkline-cell",values:[],displayValues:[],graphKind:n,yAxis:[i.y_min,i.y_max]}};return{...t,kind:e,sortMode:"default",isEditable:!1,getCell(e){if((0,p.le)(i.y_min)||(0,p.le)(i.y_max)||Number.isNaN(i.y_min)||Number.isNaN(i.y_max)||i.y_min>=i.y_max)return B("Invalid min/max y-axis configuration","The y_min (".concat(i.y_min,") and y_max (").concat(i.y_max,") configuration options must be valid numbers."));if((0,p.le)(e))return Z();const t=K(e),n=[];let o=[];if(0===t.length)return Z();let r=Number.MIN_SAFE_INTEGER,l=Number.MAX_SAFE_INTEGER;for(let i=0;i<t.length;i++){const e=Q(t[i]);if(Number.isNaN(e)||(0,p.le)(e))return B(G(t),"The value cannot be interpreted as a numeric array. ".concat(G(e)," is not a number."));e>r&&(r=e),e<l&&(l=e),n.push(e)}return o=n.length>0&&(r>i.y_max||l<i.y_min)?n.map((e=>r-l===0?r>(i.y_max||1)?i.y_max||1:i.y_min||0:((i.y_max||1)-(i.y_min||0))*((e-l)/(r-l))+(i.y_min||0))):n,{...a,copyData:n.join(","),data:{...a.data,values:o,displayValues:n.map((e=>$(e)))},isMissingValue:(0,p.le)(e)}},getCellValue(e){var t,n;return e.kind===o.p6.Loading||void 0===(null===(t=e.data)||void 0===t?void 0:t.values)?null:null===(n=e.data)||void 0===n?void 0:n.values}}}function Ae(e){return Fe("line_chart",e,"line")}function He(e){return Fe("bar_chart",e,"bar")}function ze(e){return Fe("area_chart",e,"area")}Ae.isEditableType=!1,He.isEditableType=!1,ze.isEditableType=!1;const Ve=new Map(Object.entries({object:le,text:de,checkbox:fe,selectbox:xe,list:Ee,number:ye,link:Se,datetime:me,date:pe,time:he,line_chart:Ae,bar_chart:He,area_chart:ze,image:Ie,progress:De})),je=[],Le="_index",We="_pos:",Be={small:75,medium:200,large:400};function Ye(e){if(!(0,p.le)(e))return"number"===typeof e?e:e in Be?Be[e]:void 0}function Pe(e,t){if(!t)return e;let n;return t.has(e.name)&&e.name!==Le?n=t.get(e.name):t.has("".concat(We).concat(e.indexNumber))?n=t.get("".concat(We).concat(e.indexNumber)):e.isIndex&&t.has(Le)&&(n=t.get(Le)),n?D()({...e},{title:n.label,width:Ye(n.width),isEditable:(0,p.bb)(n.disabled)?!n.disabled:void 0,isHidden:n.hidden,isRequired:n.required,columnTypeOptions:n.type_config,contentAlignment:n.alignment,defaultValue:n.default,help:n.help}):e}function Ze(e){var t;const n=null===(t=e.columnTypeOptions)||void 0===t?void 0:t.type;let i;return(0,p.bb)(n)&&(Ve.has(n)?i=Ve.get(n):(0,ae.KE)("Unknown column type configured in column configuration: ".concat(n))),(0,p.le)(i)&&(i=function(e){let t=e?j.fu.getTypeName(e):null;return t?(t=t.toLowerCase().trim(),["unicode","empty"].includes(t)?de:["datetime","datetimetz"].includes(t)?me:"time"===t?he:"date"===t?pe:["object","bytes"].includes(t)?le:["bool"].includes(t)?fe:["int8","int16","int32","int64","uint8","uint16","uint32","uint64","float16","float32","float64","float96","float128","range","decimal"].includes(t)?ye:"categorical"===t?xe:t.startsWith("list")?Ee:le):le}(e.arrowType)),i}const qe=function(e,t,n){const o=(0,g.u)(),a=i.useMemo((()=>function(e){if(!e)return new Map;try{return new Map(Object.entries(JSON.parse(e)))}catch(t){return(0,ae.H)(t),new Map}}(e.columns)),[e.columns]),r=e.useContainerWidth||(0,p.bb)(e.width)&&e.width>0;return{columns:i.useMemo((()=>{let i=function(e){const t=[],{dimensions:n}=e,i=n.headerColumns,o=n.dataColumns;if(0===i&&0===o)return t.push({id:"empty-index",title:"",indexNumber:0,isEditable:!1,isIndex:!0}),t;for(let a=0;a<i;a++){const n={...Te(e,a),indexNumber:a};t.push(n)}for(let a=0;a<o;a++){const n={...ke(e,a),indexNumber:a+i};t.push(n)}return t}(t).map((t=>{let i={...t,...Pe(t,a),isStretched:r};const l=Ze(i);return(e.editingMode===h.Eh.EditingMode.READ_ONLY||n||!1===l.isEditableType)&&(i={...i,isEditable:!1}),e.editingMode!==h.Eh.EditingMode.READ_ONLY&&1==i.isEditable&&(i={...i,icon:"editable"},i.isRequired&&e.editingMode===h.Eh.EditingMode.DYNAMIC&&(i={...i,isHidden:!1})),l(i,o)})).filter((e=>!e.isHidden));if(e.columnOrder&&e.columnOrder.length>0){const t=[];i.forEach((e=>{e.isIndex&&t.push(e)})),e.columnOrder.forEach((e=>{const n=i.find((t=>t.name===e));n&&!n.isIndex&&t.push(n)})),i=t}return i.length>0?i:[le({id:"empty-index",title:"",indexNumber:0,isEditable:!1,isIndex:!0})]}),[t,a,r,n,e.editingMode,e.columnOrder,o])}};function Je(e){return e.isIndex?Le:(0,p.le)(e.name)?"":e.name}const Ue=class{constructor(e){this.editedCells=new Map,this.addedRows=[],this.deletedRows=[],this.numRows=0,this.numRows=e}toJson(e){const t=new Map;e.forEach((e=>{t.set(e.indexNumber,e)}));const n={edited_rows:{},added_rows:[],deleted_rows:[]};this.editedCells.forEach(((e,i,o)=>{const a={};e.forEach(((e,n,i)=>{const o=t.get(n);o&&(a[Je(o)]=o.getCellValue(e))})),n.edited_rows[i]=a})),this.addedRows.forEach((e=>{const i={};let o=!1;e.forEach(((e,n,a)=>{const r=t.get(n);if(r){const t=r.getCellValue(e);r.isRequired&&r.isEditable&&P(e)&&(o=!0),(0,p.bb)(t)&&(i[Je(r)]=t)}})),o||n.added_rows.push(i)})),n.deleted_rows=this.deletedRows;return JSON.stringify(n,((e,t)=>void 0===t?null:t))}fromJson(e,t){this.editedCells=new Map,this.addedRows=[],this.deletedRows=[];const n=JSON.parse(e),i=new Map;t.forEach((e=>{i.set(e.indexNumber,e)}));const o=new Map;t.forEach((e=>{o.set(Je(e),e)})),Object.keys(n.edited_rows).forEach((e=>{const t=Number(e),i=n.edited_rows[e];Object.keys(i).forEach((e=>{const n=i[e],a=o.get(e);if(a){const e=a.getCell(n);var r;if(e)this.editedCells.has(t)||this.editedCells.set(t,new Map),null===(r=this.editedCells.get(t))||void 0===r||r.set(a.indexNumber,e)}}))})),n.added_rows.forEach((e=>{const t=new Map;Object.keys(e).forEach((n=>{const i=e[n],a=o.get(n);if(a){const e=a.getCell(i);e&&t.set(a.indexNumber,e)}})),this.addedRows.push(t)})),this.deletedRows=n.deleted_rows}isAddedRow(e){return e>=this.numRows}getCell(e,t){if(this.isAddedRow(t))return this.addedRows[t-this.numRows].get(e);const n=this.editedCells.get(t);return void 0!==n?n.get(e):void 0}setCell(e,t,n){if(this.isAddedRow(t)){if(t-this.numRows>=this.addedRows.length)return;this.addedRows[t-this.numRows].set(e,n)}else{void 0===this.editedCells.get(t)&&this.editedCells.set(t,new Map);this.editedCells.get(t).set(e,n)}}addRow(e){this.addedRows.push(e)}deleteRows(e){e.sort(((e,t)=>t-e)).forEach((e=>{this.deleteRow(e)}))}deleteRow(e){(0,p.le)(e)||e<0||(this.isAddedRow(e)?this.addedRows.splice(e-this.numRows,1):(this.deletedRows.includes(e)||(this.deletedRows.push(e),this.deletedRows=this.deletedRows.sort(((e,t)=>e-t))),this.editedCells.delete(e)))}getOriginalRowIndex(e){let t=e;for(let n=0;n<this.deletedRows.length&&!(this.deletedRows[n]>t);n++)t+=1;return t}getNumRows(){return this.numRows+this.addedRows.length-this.deletedRows.length}};var Ke=n(35704);const Ge=function(){const e=(0,g.u)(),t=i.useMemo((()=>({editable:e=>'<svg xmlns="http://www.w3.org/2000/svg" height="40" viewBox="0 96 960 960" width="40" fill="'.concat(e.bgColor,'"><path d="m800.641 679.743-64.384-64.384 29-29q7.156-6.948 17.642-6.948 10.485 0 17.742 6.948l29 29q6.948 7.464 6.948 17.95 0 10.486-6.948 17.434l-29 29Zm-310.64 246.256v-64.383l210.82-210.821 64.384 64.384-210.821 210.82h-64.383Zm-360-204.872v-50.254h289.743v50.254H130.001Zm0-162.564v-50.255h454.615v50.255H130.001Zm0-162.307v-50.255h454.615v50.255H130.001Z"/></svg>')})),[]);return{theme:i.useMemo((()=>({accentColor:e.colors.primary,accentFg:e.colors.white,accentLight:(0,Ke.DZ)(e.colors.primary,.9),borderColor:e.colors.fadedText05,horizontalBorderColor:e.colors.fadedText05,fontFamily:e.genericFonts.bodyFont,bgSearchResult:(0,Ke.DZ)(e.colors.primary,.9),resizeIndicatorColor:e.colors.primary,bgIconHeader:e.colors.fadedText60,fgIconHeader:e.colors.white,bgHeader:e.colors.bgMix,bgHeaderHasFocus:e.colors.secondaryBg,bgHeaderHovered:e.colors.secondaryBg,textHeader:e.colors.fadedText60,textHeaderSelected:e.colors.white,textGroupHeader:e.colors.fadedText60,headerFontStyle:"".concat(e.fontSizes.sm),baseFontStyle:e.fontSizes.sm,editorFontSize:e.fontSizes.sm,textDark:e.colors.bodyText,textMedium:(0,Ke.DZ)(e.colors.bodyText,.2),textLight:e.colors.fadedText40,textBubble:e.colors.fadedText60,bgCell:e.colors.bgColor,bgCellMedium:e.colors.bgColor,cellHorizontalPadding:8,cellVerticalPadding:3,bgBubble:e.colors.secondaryBg,bgBubbleSelected:e.colors.secondaryBg,linkColor:e.colors.linkText,drilldownBorder:e.colors.darkenedBgMix25})),[e]),tableBorderRadius:e.radii.lg,headerIcons:t}};const Xe=function(e,t,n,o){return{getCellContent:i.useCallback((i=>{let[a,r]=i;if(a>t.length-1)return B("Column index out of bounds.","This should never happen. Please report this bug.");if(r>n-1)return B("Row index out of bounds.","This should never happen. Please report this bug.");const l=t[a],s=l.indexNumber,d=o.current.getOriginalRowIndex(r);if(l.isEditable||o.current.isAddedRow(d)){const e=o.current.getCell(s,d);if(void 0!==e)return e}try{return Re(l,e.getCell(d+1,s),e.cssStyles)}catch(c){return(0,ae.H)(c),B("Error during cell creation.","This should never happen. Please report this bug. \nError: ".concat(c))}}),[t,n,e,o])}};var Qe=n(32700);const $e=function(e,t,n){const[o,a]=i.useState(),{getCellContent:r,getOriginalIndex:l}=(0,Qe.fF)({columns:t.map((e=>J(e))),getCellContent:n,rows:e,sort:o}),s=i.useMemo((()=>function(e,t){return void 0===t?e:e.map((e=>e.id===t.column.id?{...e,title:"asc"===t.direction?"\u2191 ".concat(e.title):"\u2193 ".concat(e.title)}:e))}(t,o)),[t,o]),d=i.useCallback((e=>{let t="asc";const n=s[e];if(o&&o.column.id===n.id){if("asc"!==o.direction)return void a(void 0);t="desc"}a({column:J(n),direction:t,mode:n.sortMode})}),[o,s]);return{columns:s,sortColumn:d,getOriginalIndex:l,getCellContent:r}},et=",",tt='"',nt='"',it="\n",ot=new RegExp("[".concat([et,tt,it].join(""),"]"));function at(e){return e.map((e=>function(e){if((0,p.le)(e))return"";const t=G(e);if(ot.test(t))return"".concat(tt).concat(t.replace(new RegExp(tt,"g"),nt+tt)).concat(tt);return t}(e))).join(et)+it}const rt=function(e,t,o){return{exportToCsv:i.useCallback((async()=>{try{const i=await n.e(5345).then(n.bind(n,95345)),a=(new Date).toISOString().slice(0,16).replace(":","-"),r="".concat(a,"_export.csv"),l=await i.showSaveFilePicker({suggestedName:r,types:[{accept:{"text/csv":[".csv"]}}],excludeAcceptAllOption:!1}),s=new TextEncoder,d=await l.createWritable();await d.write(s.encode("\ufeff"));const c=t.map((e=>e.name));await d.write(s.encode(at(c)));for(let n=0;n<o;n++){const i=[];t.forEach(((t,o,a)=>{i.push(t.getCellValue(e([o,n])))})),await d.write(s.encode(at(i)))}await d.close()}catch(i){(0,ae.KE)("Failed to export data as CSV",i)}}),[t,o,e])}};const lt=function(e,t,n,o,a,r,l){const s=i.useCallback(((t,i)=>{let[r,s]=t;const d=e[r];if(!d.isEditable)return;const c=d.indexNumber,u=n.current.getOriginalRowIndex(a(s)),m=o([r,s]),h=d.getCellValue(m),p=d.getCellValue(i);if(!Y(m)&&p===h)return;const g=d.getCell(p,!0);Y(g)?(0,ae.KE)("Not applying the cell edit since it causes this error:\n ".concat(g.data)):(n.current.setCell(c,u,{...g,lastUpdated:performance.now()}),l())}),[e,n,a,o,l]),d=i.useCallback((()=>{if(t)return;const i=new Map;e.forEach((e=>{i.set(e.indexNumber,e.getCell(e.defaultValue))})),n.current.addRow(i)}),[e,n,t]),c=i.useCallback((()=>{t||(d(),l())}),[d,l,t]),u=i.useCallback((i=>{var o;if(i.rows.length>0){if(t)return!0;const e=i.rows.toArray().map((e=>n.current.getOriginalRowIndex(a(e))));return n.current.deleteRows(e),l(!0),!1}if(null!==(o=i.current)&&void 0!==o&&o.range){const t=[],n=i.current.range;for(let i=n.y;i<n.y+n.height;i++)for(let o=n.x;o<n.x+n.width;o++){const n=e[o];n.isEditable&&!n.isRequired&&(t.push({cell:[o,i]}),s([o,i],n.getCell(null)))}return t.length>0&&(l(),r(t)),!1}return!0}),[e,n,t,r,a,l,s]),m=i.useCallback(((i,s)=>{const[c,u]=i,m=[];for(let h=0;h<s.length;h++){const i=s[h];if(h+u>=n.current.getNumRows()){if(t)break;d()}for(let t=0;t<i.length;t++){const r=i[t],l=h+u,s=t+c;if(s>=e.length)break;const d=e[s];if(d.isEditable){const e=d.getCell(r,!0);if((0,p.bb)(e)&&!Y(e)){const t=d.indexNumber,i=n.current.getOriginalRowIndex(a(l)),r=d.getCellValue(o([s,l]));d.getCellValue(e)!==r&&(n.current.setCell(t,i,{...e,lastUpdated:performance.now()}),m.push({cell:[s,l]}))}}}m.length>0&&(l(),r(m))}return!1}),[e,n,t,a,o,d,l,r]),h=i.useCallback(((t,n)=>{const i=t[0];if(i>=e.length)return!0;const o=e[i];if(o.validateInput){const e=o.validateInput(o.getCellValue(n));return!0===e||!1===e?e:o.getCell(e)}return!0}),[e]);return{onCellEdited:s,onPaste:m,onRowAppended:c,onDelete:u,validateCell:h}};const st=function(e,t){const[n,o]=i.useState(),a=i.useRef(null),r=i.useCallback((n=>{if(clearTimeout(a.current),a.current=0,o(void 0),("header"===n.kind||"cell"===n.kind)&&n.location){const i=n.location[0],r=n.location[1];let l;if(i<0||i>=e.length)return;const s=e[i];if("header"===n.kind&&(0,p.bb)(s))l=s.help;else if("cell"===n.kind){const e=t([i,r]);s.isRequired&&s.isEditable&&P(e)?l="\u26a0\ufe0f Please fill out this cell.":function(e){return e.hasOwnProperty("tooltip")&&""!==e.tooltip}(e)&&(l=e.tooltip)}l&&(a.current=setTimeout((()=>{l&&o({content:l,left:n.bounds.x+n.bounds.width/2,top:n.bounds.y})}),600))}}),[e,t,o,a]);return{tooltip:n,clearTooltip:i.useCallback((()=>{o(void 0)}),[o]),onItemHovered:r}};var dt=n(39806),ct=n(97613),ut=n(5527),mt=n(85e3),ht=n(37538);const pt=function(e){return{drawCell:i.useCallback(((t,n)=>{const{cell:i,theme:o,ctx:a,rect:r}=t,l=t.col;if(P(i)&&l<e.length){const i=e[l];return["checkbox","line_chart","bar_chart","progress"].includes(i.kind)?n():(e=>{const{cell:t,theme:n,ctx:i}=e;(0,dt.L6)({...e,theme:{...n,textDark:n.textLight,headerFontFull:"".concat(n.headerFontStyle," ").concat(n.fontFamily),baseFontFull:"".concat(n.baseFontStyle," ").concat(n.fontFamily),markerFontFull:"".concat(n.markerFontStyle," ").concat(n.fontFamily)},spriteManager:{},hyperWrapping:!1},"None",t.contentAlign),i.fillStyle=n.textDark})(t),void(i.isRequired&&i.isEditable&&function(e,t,n){e.save(),e.beginPath(),e.moveTo(t.x+t.width-8,t.y+1),e.lineTo(t.x+t.width,t.y+1),e.lineTo(t.x+t.width,t.y+1+8),e.fillStyle=n.accentColor,e.fill(),e.restore()}(a,r,o))}n()}),[e]),customRenderers:i.useMemo((()=>[ct.Z,ut.Z,mt.Z,ht.ZP,...je]),[])}};const gt=function(e){const[t,n]=(0,i.useState)((()=>new Map)),o=i.useCallback(((e,i,o,a)=>{e.id&&n(new Map(t).set(e.id,a))}),[t]);return{columns:i.useMemo((()=>e.map((e=>e.id&&t.has(e.id)&&void 0!==t.get(e.id)?{...e,width:t.get(e.id),grow:0}:e))),[e,t]),onColumnResize:o}},ft=2,bt=35,vt=50+ft,yt=2*bt+ft;const wt=function(e,t,n,o,a){let r,l=function(e){return Math.max(e*bt+ft,yt)}(t+1+(e.editingMode===h.Eh.EditingMode.DYNAMIC?1:0)),s=Math.min(l,400);e.height&&(s=Math.max(e.height,yt),l=Math.max(e.height,l)),o&&(s=Math.min(s,o),l=Math.min(l,o),e.height||(s=l));let d=n;e.useContainerWidth?r=n:e.width&&(r=Math.min(Math.max(e.width,vt),n),d=Math.min(Math.max(e.width,d),n));const[c,u]=i.useState({width:r||"100%",height:s});return i.useLayoutEffect((()=>{e.useContainerWidth&&"100%"===c.width&&u({width:n,height:c.height})}),[n]),i.useLayoutEffect((()=>{u({width:c.width,height:s})}),[t]),i.useLayoutEffect((()=>{u({width:r||"100%",height:c.height})}),[r]),i.useLayoutEffect((()=>{u({width:c.width,height:s})}),[s]),i.useLayoutEffect((()=>{if(a){const t=e.useContainerWidth||(0,p.bb)(e.width)&&e.width>0;u({width:t?d:"100%",height:l})}else u({width:r||"100%",height:s})}),[a]),{minHeight:yt,maxHeight:l,minWidth:vt,maxWidth:d,resizableSize:c,setResizableSize:u}},xt=(0,E.Z)("img",{target:"e24uaba0"})((()=>({maxWidth:"100%",maxHeight:"600px",objectFit:"scale-down"})),""),Ct=e=>{let{urls:t}=e;const n=t&&t.length>0?t[0]:"";return n.startsWith("http")?(0,R.jsx)("a",{href:n,target:"_blank",rel:"noreferrer noopener",children:(0,R.jsx)(xt,{src:n})}):(0,R.jsx)(xt,{src:n})};var Et=n(31572),Mt=n(13553),Tt=n(80152);const kt=function(e){let{top:t,left:n,content:o,clearTooltip:a}=e;const[r,l]=i.useState(!0),s=(0,g.u)(),{colors:d,fontSizes:c,radii:u}=s,m=i.useCallback((()=>{l(!1),a()}),[a,l]);return(0,R.jsx)(Et.Z,{content:(0,R.jsx)(Tt.Uo,{className:"stTooltipContent",children:(0,R.jsx)(v.ZP,{style:{fontSize:c.sm},source:o,allowHTML:!1})}),placement:Mt.r4.top,accessibilityType:Mt.SI.tooltip,showArrow:!1,popoverMargin:5,onClickOutside:m,onEsc:m,overrides:{Body:{style:{borderTopLeftRadius:u.md,borderTopRightRadius:u.md,borderBottomLeftRadius:u.md,borderBottomRightRadius:u.md,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent"}},Inner:{style:{backgroundColor:(0,M.Iy)(s)?d.bgColor:d.secondaryBg,color:d.bodyText,fontSize:c.sm,fontWeight:"normal",paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},isOpen:r,children:(0,R.jsx)("div",{className:"stTooltipTarget","data-testid":"stTooltipTarget",style:{position:"fixed",top:t,left:n}})})},Rt=(0,E.Z)("div",{target:"e1w7nams0"})((e=>{let{hasCustomizedScrollbars:t,theme:n}=e;return{position:"relative",display:"inline-block","& .glideDataEditor":{height:"100%",minWidth:"100%",borderRadius:n.radii.lg},"& .dvn-scroller":{...!t&&{scrollbarWidth:"thin"},overflowX:"auto !important",overflowY:"auto !important"}}}),"");n(2739),n(24665);const Nt=(0,m.Z)((function(e){let{element:t,data:n,width:m,height:g,disabled:f,widgetMgr:b,isFullScreen:v,disableFullscreenMode:y,expand:w,collapse:x,fragmentId:C}=e;const E=i.useRef(null),M=i.useRef(null),T=i.useRef(null),{theme:k,headerIcons:_,tableBorderRadius:I}=Ge(),[O,D]=i.useState(!0),[F,A]=i.useState(!1),[H,z]=i.useState(!1),[V,j]=i.useState(!1),L=i.useMemo((()=>window.matchMedia&&window.matchMedia("(pointer: coarse)").matches),[]),W=i.useMemo((()=>window.navigator.userAgent.includes("Mac OS")&&window.navigator.userAgent.includes("Safari")||window.navigator.userAgent.includes("Chrome")),[]),[B,Y]=i.useState({columns:o.EV.empty(),rows:o.EV.empty(),current:void 0}),P=i.useCallback((()=>{Y({columns:o.EV.empty(),rows:o.EV.empty(),current:void 0})}),[]),Z=i.useCallback((()=>{Y({columns:B.columns,rows:B.rows,current:void 0})}),[B]),U=i.useCallback((e=>{var t;null===(t=M.current)||void 0===t||t.updateCells(e)}),[]);(0,p.le)(t.editingMode)&&(t.editingMode=h.Eh.EditingMode.READ_ONLY);const{READ_ONLY:K,DYNAMIC:G}=h.Eh.EditingMode,X=n.dimensions,Q=Math.max(0,X.rows-1),$=0===Q&&!(t.editingMode===G&&X.dataColumns>0),ee=Q>15e4,te=i.useRef(new Ue(Q)),[ne,ie]=i.useState(te.current.getNumRows());i.useEffect((()=>{te.current=new Ue(Q),ie(te.current.getNumRows())}),[Q]);const oe=i.useCallback((()=>{te.current=new Ue(Q),ie(te.current.getNumRows())}),[Q]),{columns:ae}=qe(t,n,f);i.useEffect((()=>{if(t.editingMode!==K){const e=b.getStringValue(t);e&&(te.current.fromJson(e,ae),ie(te.current.getNumRows()))}}),[]);const{getCellContent:re}=Xe(n,ae,ne,te),{columns:le,sortColumn:se,getOriginalIndex:de,getCellContent:ce}=$e(Q,ae,re),ue=i.useCallback((function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];ne!==te.current.getNumRows()&&ie(te.current.getNumRows()),e&&P(),(0,p.Ds)(100,(()=>{const e=te.current.toJson(le);let i=b.getStringValue(t);void 0===i&&(i=new Ue(0).toJson([])),e!==i&&b.setStringValue(t,e,{fromUi:n},C)}))()}),[b,t,ne,P,le]),{exportToCsv:me}=rt(ce,le,ne),{onCellEdited:he,onPaste:pe,onRowAppended:ge,onDelete:fe,validateCell:be}=lt(le,t.editingMode!==G,te,ce,de,U,ue),{tooltip:ve,clearTooltip:ye,onItemHovered:we}=st(le,ce),{drawCell:xe,customRenderers:Ce}=pt(le),Ee=i.useMemo((()=>le.map((e=>J(e)))),[le]),{columns:Me,onColumnResize:Te}=gt(Ee),{minHeight:ke,maxHeight:Re,minWidth:Ne,maxWidth:Se,resizableSize:_e,setResizableSize:Ie}=wt(t,ne,m,g,v),Oe=i.useCallback((e=>{let[t,n]=e;return{...q(!0,!1),displayData:"empty",contentAlign:"center",allowOverlay:!1,themeOverride:{textDark:k.textLight},span:[0,Math.max(le.length-1,0)]}}),[le,k.textLight]);i.useEffect((()=>{const e=new u.K;return e.manageFormClearListener(b,t.formId,oe),()=>{e.disconnect()}}),[t.formId,oe,b]);const De=!$&&t.editingMode===G&&!f,Fe=B.rows.length>0,Ae=void 0!==B.current,He=$?0:le.filter((e=>e.isIndex)).length;return i.useEffect((()=>{setTimeout((()=>{if(T.current&&M.current){var e,t;const n=null===(e=T.current)||void 0===e||null===(t=e.querySelector(".dvn-stack"))||void 0===t?void 0:t.getBoundingClientRect();n&&(z(n.height>T.current.clientHeight),j(n.width>T.current.clientWidth))}}),1)}),[_e,ne,Me]),i.useEffect((()=>{Z()}),[v]),(0,R.jsxs)(Rt,{"data-testid":"stDataFrame",className:"stDataFrame",hasCustomizedScrollbars:W,ref:T,onMouseDown:e=>{if(T.current&&W){const t=T.current.getBoundingClientRect();V&&t.height-7<e.clientY-t.top&&e.stopPropagation(),H&&t.width-7<e.clientX-t.left&&e.stopPropagation()}},onBlur:e=>{O||L||e.currentTarget.contains(e.relatedTarget)||Z()},children:[(0,R.jsxs)(S,{isFullScreen:v,disableFullscreenMode:y,locked:Fe||Ae||L&&O,onExpand:w,onCollapse:x,target:Rt,children:[De&&Fe&&(0,R.jsx)(N,{label:"Delete row(s)",icon:l.H,onClick:()=>{fe&&(fe(B),ye())}}),De&&!Fe&&(0,R.jsx)(N,{label:"Add row",icon:s.m,onClick:()=>{ge&&(D(!0),ge(),ye())}}),!ee&&!$&&(0,R.jsx)(N,{label:"Download as CSV",icon:d.k,onClick:()=>me()}),!$&&(0,R.jsx)(N,{label:"Search",icon:c.o,onClick:()=>{F?A(!1):(D(!0),A(!0)),ye()}})]}),(0,R.jsx)(r.e,{"data-testid":"stDataFrameResizable",ref:E,defaultSize:_e,style:{border:"1px solid ".concat(k.borderColor),borderRadius:"".concat(I)},minHeight:ke,maxHeight:Re,minWidth:Ne,maxWidth:Se,size:_e,enable:{top:!1,right:!1,bottom:!1,left:!1,topRight:!1,bottomRight:!0,bottomLeft:!1,topLeft:!1},grid:[1,bt],snapGap:bt/3,onResizeStop:(e,t,n,i)=>{E.current&&Ie({width:E.current.size.width,height:Re-E.current.size.height===ft?E.current.size.height+ft:E.current.size.height})},children:(0,R.jsx)(a.F,{className:"glideDataEditor",ref:M,columns:Me,rows:$?1:ne,minColumnWidth:50,maxColumnWidth:1e3,maxColumnAutoWidth:500,rowHeight:bt,headerHeight:bt,getCellContent:$?Oe:ce,onColumnResize:L?void 0:Te,resizeIndicator:"header",freezeColumns:He,smoothScrollX:!0,smoothScrollY:!0,verticalBorder:!0,getCellsForSelection:!0,rowMarkers:"none",rangeSelect:L?"cell":"rect",columnSelect:"none",rowSelect:"none",onItemHovered:we,keybindings:{downFill:!0},onKeyDown:e=>{(e.ctrlKey||e.metaKey)&&"f"===e.key&&(A((e=>!e)),e.stopPropagation(),e.preventDefault())},showSearch:F,onSearchClose:()=>{A(!1),ye()},onHeaderClicked:$||ee?void 0:se,gridSelection:B,onGridSelectionChange:e=>{(O||L)&&(Y(e),void 0!==ve&&ye())},theme:k,onMouseMove:e=>{"out-of-bounds"===e.kind&&O?D(!1):"out-of-bounds"===e.kind||O||D(!0)},fixedShadowX:!0,fixedShadowY:!0,experimental:{scrollbarWidthOverride:0,...W&&{paddingBottom:V?-6:void 0,paddingRight:H?-6:void 0}},drawCell:xe,customRenderers:Ce,imageEditorOverride:Ct,headerIcons:_,validateCell:be,onPaste:!1,...!$&&t.editingMode!==K&&!f&&{fillHandle:!L,onCellEdited:he,onPaste:pe,onDelete:fe},...!$&&t.editingMode===G&&{trailingRowOptions:{sticky:!1,tint:!0},rowMarkerTheme:{bgCell:k.bgHeader,bgCellMedium:k.bgHeader},rowMarkers:"checkbox",rowSelectionMode:"multi",rowSelect:f?"none":"multi",onRowAppended:f?void 0:ge,onHeaderClicked:void 0}})}),ve&&ve.content&&(0,R.jsx)(kt,{top:ve.top,left:ve.left,content:ve.content,clearTooltip:ye})]})}),!0)},87814:(e,t,n)=>{n.d(t,{K:()=>o});var i=n(50641);class o{constructor(){this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}manageFormClearListener(e,t,n){null!=this.formClearListener&&this.lastWidgetMgr===e&&this.lastFormId===t||(this.disconnect(),(0,i.bM)(t)&&(this.formClearListener=e.addFormClearedListener(t,n),this.lastWidgetMgr=e,this.lastFormId=t))}disconnect(){var e;null===(e=this.formClearListener)||void 0===e||e.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}}}]);