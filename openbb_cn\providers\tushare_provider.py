from __future__ import annotations

import os
import pandas as pd
import tushare as ts
from typing import Optional

# 导入股票代码转换器
try:
    from openbb_cn.utils.stock_code_converter import convert_stock_code
except ImportError:
    # 如果导入失败，提供一个简单的转换函数
    def convert_stock_code(code: str) -> str:
        """简单的股票代码转换"""
        if '.' in code:
            return code
        if code.startswith('6'):
            return f"{code}.SH"
        elif code.startswith(('0', '3')):
            return f"{code}.SZ"
        return code


class TushareProvider:
    def __init__(self, token: Optional[str] = None):
        self._token = token or os.getenv("TUSHARE_TOKEN")
        if not self._token:
            raise RuntimeError("Tushare token not set. Set TUSHARE_TOKEN in environment or .env")
        ts.set_token(self._token)
        self.pro = ts.pro_api()

    def daily(self, symbol: str, start: Optional[str] = None, end: Optional[str] = None) -> pd.DataFrame:
        df = self.pro.daily(ts_code=symbol, start_date=start, end_date=end)
        if df is None or df.empty:
            return pd.DataFrame()
        df = df.rename(
            columns={
                "trade_date": "trade_date",
                "open": "open",
                "high": "high",
                "low": "low",
                "close": "close",
                "vol": "volume",
            }
        )
        df["provider"] = "tushare"
        df["trade_date"] = pd.to_datetime(df["trade_date"]).dt.date
        df = df[["trade_date", "open", "high", "low", "close", "volume", "provider"]]
        return df.sort_values("trade_date")

    def search(self, query: str, limit: int = 20) -> pd.DataFrame:
        """智能搜索股票，支持股票代码和中文名称。
        自动处理各种输入格式，优先匹配精确结果。
        """
        try:
            fields = "ts_code,name,area,industry,list_date"
            basics = self.pro.stock_basic(exchange="", list_status="L", fields=fields)
        except Exception:
            return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])
        if basics is None or basics.empty:
            return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])

        q = str(query).strip()
        if not q:
            return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])

        # 智能搜索逻辑
        results = []

        # 1. 如果输入是纯数字（6位），使用智能转换器进行精确匹配
        if q.isdigit() and len(q) == 6:
            # 使用智能转换器自动识别交易所
            converted_code = convert_stock_code(q)
            if converted_code:
                exact_match = basics[basics["ts_code"] == converted_code]
                if not exact_match.empty:
                    results.append(exact_match)

            # 如果智能转换失败，回退到原来的方法
            if not results:
                for suffix in ['.SH', '.SZ']:
                    code_with_suffix = q + suffix
                    exact_match = basics[basics["ts_code"] == code_with_suffix]
                    if not exact_match.empty:
                        results.append(exact_match)

        # 2. 如果输入包含.，进行精确代码匹配
        elif '.' in q:
            exact_match = basics[basics["ts_code"].str.upper() == q.upper()]
            if not exact_match.empty:
                results.append(exact_match)

        # 3. 股票代码模糊匹配（不区分大小写）
        q_lower = q.lower()
        code_fuzzy = basics[basics["ts_code"].str.lower().str.contains(q_lower, na=False)]
        if not code_fuzzy.empty:
            results.append(code_fuzzy)

        # 4. 股票名称模糊匹配（支持中文）
        name_fuzzy = basics[basics["name"].str.contains(q, na=False)]
        if not name_fuzzy.empty:
            results.append(name_fuzzy)

        # 5. 行业匹配（作为补充）
        if len(q) >= 2:  # 至少2个字符才进行行业匹配
            industry_match = basics[basics["industry"].str.contains(q, na=False)]
            if not industry_match.empty:
                results.append(industry_match)

        # 合并结果并去重
        if results:
            combined = pd.concat(results, ignore_index=True)
            # 去重，保持顺序
            combined = combined.drop_duplicates(subset=['ts_code'], keep='first')

            # 排序：精确匹配优先，然后按代码排序
            def sort_key(row):
                ts_code = row['ts_code']
                name = row['name']

                # 精确匹配得分最高
                if (q.isdigit() and len(q) == 6 and ts_code.startswith(q)) or \
                   (ts_code.upper() == q.upper()) or (name == q):
                    return (0, ts_code)

                # 代码开头匹配
                if ts_code.lower().startswith(q_lower):
                    return (1, ts_code)

                # 名称开头匹配
                if name.startswith(q):
                    return (2, ts_code)

                # 其他匹配
                return (3, ts_code)

            combined['sort_key'] = combined.apply(sort_key, axis=1)
            combined = combined.sort_values('sort_key').drop('sort_key', axis=1)

            return combined.head(limit)

        return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])

    def daily_basic(self, symbol: str, start: Optional[str] = None, end: Optional[str] = None) -> pd.DataFrame:
        """估值与基础指标时间序列：返回 trade_date, pe, pb, ps。
        源字段：pe_ttm, pb, ps_ttm。
        """
        fields = "trade_date,pe_ttm,pb,ps_ttm"
        try:
            df = self.pro.daily_basic(ts_code=symbol, start_date=start, end_date=end, fields=fields)
        except Exception:
            return pd.DataFrame()
        if df is None or df.empty:
            return pd.DataFrame()
        df = df.rename(columns={"pe_ttm": "pe", "ps_ttm": "ps"})
        df["trade_date"] = pd.to_datetime(df["trade_date"]).dt.date
        df = df[["trade_date", "pe", "pb", "ps"]]
        return df.sort_values("trade_date")

