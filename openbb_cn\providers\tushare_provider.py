from __future__ import annotations

import os
import pandas as pd
import tushare as ts
from typing import Optional

# 导入股票代码转换器
try:
    from openbb_cn.utils.stock_code_converter import convert_stock_code
except ImportError:
    # 如果导入失败，提供一个简单的转换函数
    def convert_stock_code(code: str) -> str:
        """简单的股票代码转换"""
        if '.' in code:
            return code
        if code.startswith('6'):
            return f"{code}.SH"
        elif code.startswith(('0', '3')):
            return f"{code}.SZ"
        return code


class TushareProvider:
    def __init__(self, token: Optional[str] = None):
        # 确保加载.env文件
        try:
            from dotenv import load_dotenv
            load_dotenv()
        except ImportError:
            pass  # 如果没有python-dotenv，继续使用环境变量

        self._token = token or os.getenv("TUSHARE_TOKEN")
        if not self._token:
            raise RuntimeError("Tushare token not set. Set TUSHARE_TOKEN in environment or .env")
        ts.set_token(self._token)
        self.pro = ts.pro_api()

        # 添加缓存机制，避免重复调用API
        self._stock_cache = None
        self._cache_timestamp = None
        self._last_api_call = 0  # 上次API调用时间
        self._min_interval = 2   # 最小调用间隔（秒）

    def _get_backup_stock_data(self) -> pd.DataFrame:
        """获取备用股票数据，当Tushare API不可用时使用"""
        print("📋 使用备用股票数据库")

        # 扩展的备用股票数据
        backup_stocks = [
            {"ts_code": "000001.SZ", "name": "平安银行", "area": "深圳", "industry": "银行", "list_date": "19910403"},
            {"ts_code": "000002.SZ", "name": "万科A", "area": "深圳", "industry": "房地产开发", "list_date": "19910129"},
            {"ts_code": "000858.SZ", "name": "五粮液", "area": "宜宾", "industry": "白酒", "list_date": "19980427"},
            {"ts_code": "600000.SH", "name": "浦发银行", "area": "上海", "industry": "银行", "list_date": "19991110"},
            {"ts_code": "600036.SH", "name": "招商银行", "area": "深圳", "industry": "银行", "list_date": "20020409"},
            {"ts_code": "600276.SH", "name": "恒瑞医药", "area": "连云港", "industry": "化学制药", "list_date": "20001018"},
            {"ts_code": "600507.SH", "name": "方大特钢", "area": "南昌", "industry": "钢铁", "list_date": "20070112"},
            {"ts_code": "600519.SH", "name": "贵州茅台", "area": "遵义", "industry": "白酒", "list_date": "20010827"},
            {"ts_code": "600887.SH", "name": "伊利股份", "area": "呼和浩特", "industry": "乳品", "list_date": "19961212"},
            {"ts_code": "002415.SZ", "name": "海康威视", "area": "杭州", "industry": "安防设备", "list_date": "20100528"},
            {"ts_code": "000725.SZ", "name": "京东方A", "area": "北京", "industry": "显示器件", "list_date": "20010912"},
            {"ts_code": "002594.SZ", "name": "比亚迪", "area": "深圳", "industry": "汽车整车", "list_date": "20110630"},
            {"ts_code": "300059.SZ", "name": "东方财富", "area": "上海", "industry": "互联网传媒", "list_date": "20100315"},
            {"ts_code": "300750.SZ", "name": "宁德时代", "area": "宁德", "industry": "电池", "list_date": "20180611"},
            {"ts_code": "000166.SZ", "name": "申万宏源", "area": "上海", "industry": "证券", "list_date": "19940104"},
            {"ts_code": "600030.SH", "name": "中信证券", "area": "北京", "industry": "证券", "list_date": "20030106"},
            {"ts_code": "000776.SZ", "name": "广发证券", "area": "广州", "industry": "证券", "list_date": "20100809"},
            {"ts_code": "601318.SH", "name": "中国平安", "area": "深圳", "industry": "保险", "list_date": "20070301"},
            {"ts_code": "600104.SH", "name": "上汽集团", "area": "上海", "industry": "汽车整车", "list_date": "19970114"},
            {"ts_code": "000063.SZ", "name": "中兴通讯", "area": "深圳", "industry": "通信设备", "list_date": "19971201"},
        ]

        return pd.DataFrame(backup_stocks)

    def _check_rate_limit(self) -> bool:
        """检查API调用频率限制"""
        import time
        current_time = time.time()

        if current_time - self._last_api_call < self._min_interval:
            wait_time = self._min_interval - (current_time - self._last_api_call)
            print(f"⏳ API限流等待 {wait_time:.1f} 秒...")
            time.sleep(wait_time)

        self._last_api_call = time.time()
        return True

    def _generate_mock_daily_data(self, symbol: str, start: Optional[str] = None, end: Optional[str] = None) -> pd.DataFrame:
        """生成模拟的日线数据，当API不可用时使用"""
        import numpy as np
        from datetime import datetime, timedelta

        # 设置日期范围
        if end is None:
            end_date = datetime.now()
        else:
            end_date = datetime.strptime(end, "%Y%m%d")

        if start is None:
            start_date = end_date - timedelta(days=365)
        else:
            start_date = datetime.strptime(start, "%Y%m%d")

        # 生成交易日期（排除周末）
        dates = []
        current_date = start_date
        while current_date <= end_date:
            if current_date.weekday() < 5:  # 周一到周五
                dates.append(current_date)
            current_date += timedelta(days=1)

        if not dates:
            return pd.DataFrame()

        # 生成模拟价格数据
        np.random.seed(hash(symbol) % 2**32)  # 基于股票代码的固定种子
        n_days = len(dates)

        # 基础价格（根据股票代码设定）
        if symbol.startswith('6'):
            base_price = 15.0  # 上海股票
        elif symbol.startswith('3'):
            base_price = 25.0  # 创业板
        else:
            base_price = 12.0  # 深圳主板

        # 生成价格序列
        returns = np.random.normal(0.001, 0.02, n_days)  # 日收益率
        prices = [base_price]

        for i in range(1, n_days):
            new_price = prices[-1] * (1 + returns[i])
            prices.append(max(new_price, 0.1))  # 确保价格为正

        # 生成OHLC数据
        data = []
        for i, date in enumerate(dates):
            close = prices[i]
            volatility = 0.015

            high = close * (1 + np.random.uniform(0, volatility))
            low = close * (1 - np.random.uniform(0, volatility))
            open_price = low + (high - low) * np.random.random()

            # 确保OHLC逻辑正确
            high = max(high, open_price, close)
            low = min(low, open_price, close)

            volume = np.random.randint(1000000, 10000000)  # 成交量

            data.append({
                'trade_date': date.strftime('%Y%m%d'),
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close, 2),
                'volume': volume,
                'amount': round(volume * close, 2)
            })

        return pd.DataFrame(data)

    def daily(self, symbol: str, start: Optional[str] = None, end: Optional[str] = None) -> pd.DataFrame:
        try:
            # 检查API调用频率
            self._check_rate_limit()

            df = self.pro.daily(ts_code=symbol, start_date=start, end_date=end)
            if df is None or df.empty:
                print(f"⚠️ {symbol} 无历史数据，使用模拟数据")
                return self._generate_mock_daily_data(symbol, start, end)

            df = df.rename(
                columns={
                    "trade_date": "trade_date",
                    "open": "open",
                    "high": "high",
                    "low": "low",
                    "close": "close",
                    "vol": "volume",
                }
            )
            df["provider"] = "tushare"
            df["trade_date"] = pd.to_datetime(df["trade_date"]).dt.date
            df = df[["trade_date", "open", "high", "low", "close", "volume", "provider"]]
            return df.sort_values("trade_date")

        except Exception as e:
            print(f"❌ Tushare API调用失败: {e}")
            print(f"🔄 为 {symbol} 生成模拟数据")
            return self._generate_mock_daily_data(symbol, start, end)

    def search(self, query: str, limit: int = 20) -> pd.DataFrame:
        """智能搜索股票，支持股票代码和中文名称。
        自动处理各种输入格式，优先匹配精确结果。

        Args:
            query: 搜索关键词（股票代码或名称）
            limit: 返回结果数量限制

        Returns:
            包含搜索结果的DataFrame
        """
        print(f"🔍 搜索查询: '{query}'")  # 调试信息

        # 预处理查询参数
        q = str(query).strip()
        if not q:
            print("❌ 搜索查询为空")
            return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])

        try:
            # 检查缓存是否可用（避免频繁调用API）
            import time
            current_time = time.time()
            cache_valid = (
                self._stock_cache is not None and
                self._cache_timestamp is not None and
                (current_time - self._cache_timestamp) < 3600  # 缓存1小时
            )

            if cache_valid:
                print("📋 使用缓存的股票数据")
                basics = self._stock_cache
            else:
                print("📊 获取股票基础数据...")
                try:
                    # 根据Tushare文档，使用正确的参数
                    fields = "ts_code,name,area,industry,list_date"
                    basics = self.pro.stock_basic(
                        exchange="",
                        list_status="L",
                        fields=fields
                    )

                    if basics is not None and not basics.empty:
                        print(f"✅ 获取到 {len(basics)} 条基础数据")
                        # 更新缓存
                        self._stock_cache = basics.copy()
                        self._cache_timestamp = current_time
                    else:
                        print("❌ 获取基础数据为空")
                        raise Exception("API返回空数据")

                except Exception as api_error:
                    print(f"❌ Tushare API调用失败: {api_error}")
                    # 使用备用数据
                    basics = self._get_backup_stock_data()

        except Exception as e:
            print(f"❌ 获取基础数据失败: {e}")
            print("🔄 使用备用股票数据...")
            basics = self._get_backup_stock_data()

        if basics is None or basics.empty:
            print("❌ 基础数据为空，使用备用数据")  # 调试信息
            try:
                from openbb_cn.data.stock_list import search_stocks
                return search_stocks(q, limit)
            except ImportError:
                return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])

        # 智能搜索逻辑
        results = []

        # 1. 如果输入是纯数字（6位），使用智能转换器进行精确匹配
        if q.isdigit() and len(q) == 6:
            # 使用智能转换器自动识别交易所
            converted_code = convert_stock_code(q)
            if converted_code:
                exact_match = basics[basics["ts_code"] == converted_code]
                if not exact_match.empty:
                    results.append(exact_match)

            # 如果智能转换失败，回退到原来的方法
            if not results:
                for suffix in ['.SH', '.SZ']:
                    code_with_suffix = q + suffix
                    exact_match = basics[basics["ts_code"] == code_with_suffix]
                    if not exact_match.empty:
                        results.append(exact_match)

        # 2. 如果输入包含.，进行精确代码匹配
        elif '.' in q:
            exact_match = basics[basics["ts_code"].str.upper() == q.upper()]
            if not exact_match.empty:
                results.append(exact_match)

        # 3. 股票代码模糊匹配（不区分大小写）
        q_lower = q.lower()
        code_fuzzy = basics[basics["ts_code"].str.lower().str.contains(q_lower, na=False)]
        if not code_fuzzy.empty:
            results.append(code_fuzzy)

        # 4. 股票名称模糊匹配（支持中文）
        name_fuzzy = basics[basics["name"].str.contains(q, na=False)]
        if not name_fuzzy.empty:
            results.append(name_fuzzy)

        # 5. 行业匹配（作为补充）
        if len(q) >= 2:  # 至少2个字符才进行行业匹配
            industry_match = basics[basics["industry"].str.contains(q, na=False)]
            if not industry_match.empty:
                results.append(industry_match)

        # 合并结果并去重
        if results:
            combined = pd.concat(results, ignore_index=True)
            # 去重，保持顺序
            combined = combined.drop_duplicates(subset=['ts_code'], keep='first')

            # 排序：精确匹配优先，然后按代码排序
            def sort_key(row):
                ts_code = row['ts_code']
                name = row['name']

                # 精确匹配得分最高
                if (q.isdigit() and len(q) == 6 and ts_code.startswith(q)) or \
                   (ts_code.upper() == q.upper()) or (name == q):
                    return (0, ts_code)

                # 代码开头匹配
                if ts_code.lower().startswith(q_lower):
                    return (1, ts_code)

                # 名称开头匹配
                if name.startswith(q):
                    return (2, ts_code)

                # 其他匹配
                return (3, ts_code)

            combined['sort_key'] = combined.apply(sort_key, axis=1)
            combined = combined.sort_values('sort_key').drop('sort_key', axis=1)

            return combined.head(limit)

        return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])

    def daily_basic(self, symbol: str, start: Optional[str] = None, end: Optional[str] = None) -> pd.DataFrame:
        """估值与基础指标时间序列：返回 trade_date, pe, pb, ps。
        源字段：pe_ttm, pb, ps_ttm。
        """
        fields = "trade_date,pe_ttm,pb,ps_ttm"
        try:
            df = self.pro.daily_basic(ts_code=symbol, start_date=start, end_date=end, fields=fields)
        except Exception:
            return pd.DataFrame()
        if df is None or df.empty:
            return pd.DataFrame()
        df = df.rename(columns={"pe_ttm": "pe", "ps_ttm": "ps"})
        df["trade_date"] = pd.to_datetime(df["trade_date"]).dt.date
        df = df[["trade_date", "pe", "pb", "ps"]]
        return df.sort_values("trade_date")

