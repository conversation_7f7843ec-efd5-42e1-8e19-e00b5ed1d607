from __future__ import annotations

import os
import pandas as pd
import tushare as ts
from typing import Optional


class TushareProvider:
    def __init__(self, token: Optional[str] = None):
        self._token = token or os.getenv("TUSHARE_TOKEN")
        if not self._token:
            raise RuntimeError("Tushare token not set. Set TUSHARE_TOKEN in environment or .env")
        ts.set_token(self._token)
        self.pro = ts.pro_api()

    def daily(self, symbol: str, start: Optional[str] = None, end: Optional[str] = None) -> pd.DataFrame:
        df = self.pro.daily(ts_code=symbol, start_date=start, end_date=end)
        if df is None or df.empty:
            return pd.DataFrame()
        df = df.rename(
            columns={
                "trade_date": "trade_date",
                "open": "open",
                "high": "high",
                "low": "low",
                "close": "close",
                "vol": "volume",
            }
        )
        df["provider"] = "tushare"
        df["trade_date"] = pd.to_datetime(df["trade_date"]).dt.date
        df = df[["trade_date", "open", "high", "low", "close", "volume", "provider"]]
        return df.sort_values("trade_date")

    def search(self, query: str, limit: int = 20) -> pd.DataFrame:
        """模糊搜索股票，返回基础信息。
        仅筛选上市状态(L)的股票，并在 ts_code 或 name 中包含 query。
        """
        try:
            fields = "ts_code,name,area,industry,list_date"
            basics = self.pro.stock_basic(exchange="", list_status="L", fields=fields)
        except Exception:
            return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])  
        if basics is None or basics.empty:
            return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])  
        q = str(query).lower()
        mask = basics["ts_code"].str.lower().str.contains(q) | basics["name"].str.lower().str.contains(q)
        out = basics.loc[mask].head(limit).copy()
        return out

    def daily_basic(self, symbol: str, start: Optional[str] = None, end: Optional[str] = None) -> pd.DataFrame:
        """估值与基础指标时间序列：返回 trade_date, pe, pb, ps。
        源字段：pe_ttm, pb, ps_ttm。
        """
        fields = "trade_date,pe_ttm,pb,ps_ttm"
        try:
            df = self.pro.daily_basic(ts_code=symbol, start_date=start, end_date=end, fields=fields)
        except Exception:
            return pd.DataFrame()
        if df is None or df.empty:
            return pd.DataFrame()
        df = df.rename(columns={"pe_ttm": "pe", "ps_ttm": "ps"})
        df["trade_date"] = pd.to_datetime(df["trade_date"]).dt.date
        df = df[["trade_date", "pe", "pb", "ps"]]
        return df.sort_values("trade_date")

