[tool.poetry]
name = "openbb-yfinance"
version = "1.4.6"
description = "yfinance extension for OpenBB"
authors = ["OpenBB Team <<EMAIL>>"]
license = "AGPL-3.0-only"
readme = "README.md"
packages = [{ include = "openbb_yfinance" }]

[tool.poetry.dependencies]
python = ">=3.9.21,<3.13"
yfinance = "0.2.58"
openbb-core = "^1.4.7"
curl-adapter = ">=1.1.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.plugins."openbb_provider_extension"]
yfinance = "openbb_yfinance:yfinance_provider"
