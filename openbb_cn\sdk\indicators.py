from __future__ import annotations

import math
from dataclasses import dataclass
from typing import Iterable, List

import numpy as np
import pandas as pd


def _safe_zscore(s: pd.Series, window: int) -> pd.Series:
    rolling = s.rolling(window)
    mu = rolling.mean()
    sd = rolling.std(ddof=0)
    return (s - mu) / sd.replace(0, np.nan)


def _atr(df: pd.DataFrame, window: int = 14) -> pd.Series:
    high = df["high"].astype(float)
    low = df["low"].astype(float)
    close = df["close"].astype(float)
    prev_close = close.shift(1)
    tr = pd.concat([
        (high - low),
        (high - prev_close).abs(),
        (low - prev_close).abs(),
    ], axis=1).max(axis=1)
    return tr.rolling(window).mean()


def vcm(df: pd.DataFrame, window: int = 20, decay: float = 0.85) -> pd.Series:
    """Volume-Convected Momentum.
    Σ w_i * r_{t-i} * ΔlogV_{t-i} 的指数加权均值，并按收益波动做标准化。
    """
    close = df["close"].astype(float)
    volume = df["volume"].astype(float).replace(0, np.nan)
    r = np.log(close).diff()
    dlogv = np.log(volume).diff()
    prod = r * dlogv
    alpha = 1 - decay
    v = prod.ewm(alpha=alpha, adjust=False).mean()
    denom = r.rolling(window).std(ddof=0)
    return v / denom.replace(0, np.nan)


def tei(df: pd.DataFrame, window: int = 20) -> pd.Series:
    """Trend Entropy Index."""
    close = df["close"].astype(float)
    r = np.log(close).diff()
    # p = P(r>0) in rolling window
    pos = (r > 0).astype(float)
    p = pos.rolling(window).mean()
    # 熵，最大值 ln2（自然对数）
    eps = 1e-12
    H = -(p.clip(eps, 1 - eps) * np.log(p.clip(eps, 1 - eps)) + (1 - p.clip(eps, 1 - eps)) * np.log(1 - p.clip(eps, 1 - eps)))
    H_max = math.log(2.0)
    mu = r.rolling(window).mean()
    sd = r.rolling(window).std(ddof=0)
    return (mu / sd.replace(0, np.nan)) * (1 - H / H_max)


def lvr(df: pd.DataFrame, window: int = 20, atr_window: int = 14) -> pd.Series:
    """Liquidity Vacuum Risk.
    LVR = z(价格真实波幅相对ATR) - z(量能相对均量)
    """
    atr = _atr(df, atr_window)
    high = df["high"].astype(float)
    low = df["low"].astype(float)
    rng = (high - low) / atr.replace(0, np.nan)
    rng_z = _safe_zscore(rng, window)
    vol_rel = df["volume"].astype(float) / df["volume"].astype(float).rolling(window).mean()
    vol_z = _safe_zscore(vol_rel, window)
    return rng_z - vol_z


def api_index(df: pd.DataFrame, window: int = 20, gamma: float = 0.2) -> pd.Series:
    """Asymmetric Pressure Index."""
    close = df["close"].astype(float)
    r = np.log(close).diff()
    r_pos = r.clip(lower=0)
    r_neg = r.clip(upper=0)
    var_pos = (r_pos ** 2).rolling(window).mean()
    var_neg = (r_neg ** 2).rolling(window).mean()
    var_all = (r ** 2).rolling(window).mean()
    skew_v = df["volume"].astype(float).rolling(window).skew()
    return ((var_pos - var_neg) / var_all.replace(0, np.nan)) * (1 + gamma * skew_v.fillna(0))


def blce(df: pd.DataFrame, window: int = 20, alpha: float = 0.6, beta: float = 0.8, atr_window: int = 14) -> pd.DataFrame:
    """Breakout Likelihood from Constrained Energy.
    返回 DataFrame: energy, dir_bias, prob_up。
    """
    close = df["close"].astype(float)
    r = np.log(close).diff()
    rv = r.rolling(window).std(ddof=0)
    atr = _atr(df, atr_window)
    atr_rel = atr / close
    squeeze = 1 - (rv / atr_rel.replace(0, np.nan))
    # Bollinger Band width inverse
    mid = close.rolling(window).mean()
    sd = close.rolling(window).std(ddof=0)
    upper = mid + 2 * sd
    lower = mid - 2 * sd
    bb_width = (upper - lower) / mid.replace(0, np.nan)
    width_inv = 1 / bb_width.replace(0, np.nan)
    energy = _safe_zscore(squeeze, window) + alpha * _safe_zscore(width_inv, window)
    dir_bias = _safe_zscore(r.rolling(window).sum(), window)
    x = energy + beta * dir_bias
    prob_up = 1 / (1 + np.exp(-x.clip(-10, 10)))
    out = pd.DataFrame({"energy": energy, "dir_bias": dir_bias, "prob_up": prob_up})
    return out


def mtr(df: pd.DataFrame, windows: List[int] | None = None) -> pd.Series:
    """Multi-timescale Resonance.
    采用多尺度收益的“方向一致度”近似：同向占比映射到 [-1,1]。
    """
    if windows is None:
        windows = [5, 10, 20, 60]
    close = df["close"].astype(float)
    signals = []
    for w in windows:
        ema = close.ewm(span=w, adjust=False).mean()
        r = ema.pct_change()
        signals.append(np.sign(r))
    M = pd.concat(signals, axis=1)
    # 每时刻，计算所有成对符号乘积的平均
    n = M.shape[1]
    if n < 2:
        return pd.Series(index=close.index, dtype=float)
    prod_sum = pd.Series(0.0, index=close.index)
    pairs = 0
    for i in range(n):
        for j in range(i + 1, n):
            prod_sum = prod_sum + (M.iloc[:, i] * M.iloc[:, j])
            pairs += 1
    return prod_sum / pairs

