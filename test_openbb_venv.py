#!/usr/bin/env python3
"""
OpenBB 虚拟环境测试脚本
验证虚拟环境中的OpenBB安装是否正常
"""

import sys
import os
from datetime import datetime


def print_header():
    """打印测试头部信息"""
    print("🚀 OpenBB 虚拟环境测试")
    print("=" * 50)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    print("=" * 50)


def test_python_environment():
    """测试Python环境"""
    print("\n🐍 Python环境检查")
    print("-" * 30)
    
    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 运行在虚拟环境中")
        print(f"   虚拟环境路径: {sys.prefix}")
    else:
        print("⚠️ 可能不在虚拟环境中")
        print(f"   Python路径: {sys.prefix}")
    
    # 检查Python版本
    version_info = sys.version_info
    print(f"✅ Python版本: {version_info.major}.{version_info.minor}.{version_info.micro}")
    
    return True


def test_core_imports():
    """测试核心库导入"""
    print("\n📦 核心库导入测试")
    print("-" * 30)
    
    imports = [
        ("pandas", "数据处理库"),
        ("numpy", "数值计算库"),
        ("requests", "HTTP请求库"),
        ("json", "JSON处理"),
        ("datetime", "日期时间处理"),
    ]
    
    success_count = 0
    for module_name, description in imports:
        try:
            __import__(module_name)
            print(f"✅ {module_name}: {description}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {module_name}: 导入失败 - {e}")
    
    print(f"\n📊 核心库导入结果: {success_count}/{len(imports)}")
    return success_count == len(imports)


def test_openbb_imports():
    """测试OpenBB相关导入"""
    print("\n🔧 OpenBB组件导入测试")
    print("-" * 30)
    
    openbb_imports = [
        ("openbb_core", "OpenBB核心"),
        ("openbb_yfinance", "Yahoo Finance扩展"),
        ("openbb_equity", "股票数据扩展"),
        ("openbb_economy", "经济数据扩展"),
        ("openbb_news", "新闻数据扩展"),
        ("yfinance", "Yahoo Finance库"),
    ]
    
    success_count = 0
    for module_name, description in openbb_imports:
        try:
            __import__(module_name)
            print(f"✅ {module_name}: {description}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {module_name}: 导入失败 - {e}")
    
    print(f"\n📊 OpenBB组件导入结果: {success_count}/{len(openbb_imports)}")
    return success_count >= 4  # 至少4个组件成功


def test_openbb_core_functionality():
    """测试OpenBB核心功能"""
    print("\n⚙️ OpenBB核心功能测试")
    print("-" * 30)
    
    try:
        from openbb_core.app.static.app_factory import create_app
        
        # 创建应用
        app = create_app()
        print("✅ OpenBB应用创建成功")
        
        # 检查应用属性
        app_type = type(app).__name__
        print(f"✅ 应用类型: {app_type}")
        
        # 检查可用方法
        methods = [method for method in dir(app) if not method.startswith('_')]
        print(f"✅ 可用方法数量: {len(methods)}")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenBB核心功能测试失败: {e}")
        return False


def test_data_processing():
    """测试数据处理功能"""
    print("\n📊 数据处理功能测试")
    print("-" * 30)
    
    try:
        import pandas as pd
        import numpy as np
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=10, freq='D')
        data = pd.DataFrame({
            'Date': dates,
            'Price': np.random.uniform(100, 200, 10),
            'Volume': np.random.randint(1000, 10000, 10)
        })
        
        print(f"✅ 测试数据创建成功 - {len(data)}行数据")
        
        # 基本统计
        mean_price = data['Price'].mean()
        print(f"✅ 数据统计计算成功 - 平均价格: {mean_price:.2f}")
        
        # 数据导出测试
        csv_file = "venv_test_data.csv"
        data.to_csv(csv_file, index=False)
        print(f"✅ 数据导出成功: {csv_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据处理测试失败: {e}")
        return False


def test_yfinance_basic():
    """测试yfinance基本功能（不进行网络请求）"""
    print("\n📈 YFinance基本功能测试")
    print("-" * 30)
    
    try:
        import yfinance as yf
        
        # 创建ticker对象（不进行网络请求）
        ticker = yf.Ticker("AAPL")
        print("✅ YFinance Ticker对象创建成功")
        
        # 检查ticker属性
        if hasattr(ticker, 'ticker'):
            print(f"✅ Ticker符号: {ticker.ticker}")
        
        print("✅ YFinance基本功能正常（未进行网络请求）")
        return True
        
    except Exception as e:
        print(f"❌ YFinance测试失败: {e}")
        return False


def generate_summary_report(results):
    """生成测试总结报告"""
    print("\n" + "=" * 50)
    print("📋 测试总结报告")
    print("=" * 50)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\n📊 详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！虚拟环境配置完美！")
        print("\n💡 下一步建议:")
        print("1. 运行 python openbb_demo.py 查看完整功能演示")
        print("2. 开始您的金融数据分析项目")
        print("3. 探索更多OpenBB扩展功能")
    else:
        print("\n⚠️ 部分测试失败，请检查安装")
        print("\n🔧 故障排除建议:")
        print("1. 确保在正确的虚拟环境中运行")
        print("2. 重新安装失败的组件")
        print("3. 检查网络连接（如果涉及网络请求）")
    
    return passed_tests == total_tests


def main():
    """主测试函数"""
    print_header()
    
    # 执行所有测试
    test_results = {}
    
    test_results["Python环境"] = test_python_environment()
    test_results["核心库导入"] = test_core_imports()
    test_results["OpenBB导入"] = test_openbb_imports()
    test_results["OpenBB核心功能"] = test_openbb_core_functionality()
    test_results["数据处理"] = test_data_processing()
    test_results["YFinance基本功能"] = test_yfinance_basic()
    
    # 生成总结报告
    success = generate_summary_report(test_results)
    
    return success


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)
