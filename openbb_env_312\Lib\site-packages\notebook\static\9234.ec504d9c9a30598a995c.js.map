{"version": 3, "file": "9234.ec504d9c9a30598a995c.js?v=ec504d9c9a30598a995c", "mappings": ";;;;;;;;;;AAAA,2CAA2C;AAC3C;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,IAAI;AACJ;;AAEA,IAAI;AACJ;AACA;AACA;AACA,MAAM;AACN,kCAAkC;AAClC;AACA;AACA;AACA;;AAEA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA,IAAI;AACJ;;AAEA,IAAI,iBAAiB;AACrB;AACA,qBAAqB;;AAErB;AACA;AACA,MAAM;AACN;AACA;;AAEA,IAAI;AACJ;AACA;AACA,4BAA4B,GAAG,KAAK;;AAEpC,IAAI;AACJ;AACA;;AAEA,IAAI;AACJ;AACA;;AAEA,IAAI;AACJ;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;;AAEO;AACP;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/smalltalk.js"], "sourcesContent": ["var specialChars = /[+\\-\\/\\\\*~<>=@%|&?!.,:;^]/;\nvar keywords = /true|false|nil|self|super|thisContext/;\n\nvar Context = function(tokenizer, parent) {\n  this.next = tokenizer;\n  this.parent = parent;\n};\n\nvar Token = function(name, context, eos) {\n  this.name = name;\n  this.context = context;\n  this.eos = eos;\n};\n\nvar State = function() {\n  this.context = new Context(next, null);\n  this.expectVariable = true;\n  this.indentation = 0;\n  this.userIndentationDelta = 0;\n};\n\nState.prototype.userIndent = function(indentation, indentUnit) {\n  this.userIndentationDelta = indentation > 0 ? (indentation / indentUnit - this.indentation) : 0;\n};\n\nvar next = function(stream, context, state) {\n  var token = new Token(null, context, false);\n  var aChar = stream.next();\n\n  if (aChar === '\"') {\n    token = nextComment(stream, new Context(nextComment, context));\n\n  } else if (aChar === '\\'') {\n    token = nextString(stream, new Context(nextString, context));\n\n  } else if (aChar === '#') {\n    if (stream.peek() === '\\'') {\n      stream.next();\n      token = nextSymbol(stream, new Context(nextSymbol, context));\n    } else {\n      if (stream.eatWhile(/[^\\s.{}\\[\\]()]/))\n        token.name = 'string.special';\n      else\n        token.name = 'meta';\n    }\n\n  } else if (aChar === '$') {\n    if (stream.next() === '<') {\n      stream.eatWhile(/[^\\s>]/);\n      stream.next();\n    }\n    token.name = 'string.special';\n\n  } else if (aChar === '|' && state.expectVariable) {\n    token.context = new Context(nextTemporaries, context);\n\n  } else if (/[\\[\\]{}()]/.test(aChar)) {\n    token.name = 'bracket';\n    token.eos = /[\\[{(]/.test(aChar);\n\n    if (aChar === '[') {\n      state.indentation++;\n    } else if (aChar === ']') {\n      state.indentation = Math.max(0, state.indentation - 1);\n    }\n\n  } else if (specialChars.test(aChar)) {\n    stream.eatWhile(specialChars);\n    token.name = 'operator';\n    token.eos = aChar !== ';'; // ; cascaded message expression\n\n  } else if (/\\d/.test(aChar)) {\n    stream.eatWhile(/[\\w\\d]/);\n    token.name = 'number';\n\n  } else if (/[\\w_]/.test(aChar)) {\n    stream.eatWhile(/[\\w\\d_]/);\n    token.name = state.expectVariable ? (keywords.test(stream.current()) ? 'keyword' : 'variable') : null;\n\n  } else {\n    token.eos = state.expectVariable;\n  }\n\n  return token;\n};\n\nvar nextComment = function(stream, context) {\n  stream.eatWhile(/[^\"]/);\n  return new Token('comment', stream.eat('\"') ? context.parent : context, true);\n};\n\nvar nextString = function(stream, context) {\n  stream.eatWhile(/[^']/);\n  return new Token('string', stream.eat('\\'') ? context.parent : context, false);\n};\n\nvar nextSymbol = function(stream, context) {\n  stream.eatWhile(/[^']/);\n  return new Token('string.special', stream.eat('\\'') ? context.parent : context, false);\n};\n\nvar nextTemporaries = function(stream, context) {\n  var token = new Token(null, context, false);\n  var aChar = stream.next();\n\n  if (aChar === '|') {\n    token.context = context.parent;\n    token.eos = true;\n\n  } else {\n    stream.eatWhile(/[^|]/);\n    token.name = 'variable';\n  }\n\n  return token;\n};\n\nexport const smalltalk = {\n  name: \"smalltalk\",\n\n  startState: function() {\n    return new State;\n  },\n\n  token: function(stream, state) {\n    state.userIndent(stream.indentation(), stream.indentUnit);\n\n    if (stream.eatSpace()) {\n      return null;\n    }\n\n    var token = state.context.next(stream, state.context, state);\n    state.context = token.context;\n    state.expectVariable = token.eos;\n\n    return token.name;\n  },\n\n  blankLine: function(state, indentUnit) {\n    state.userIndent(0, indentUnit);\n  },\n\n  indent: function(state, textAfter, cx) {\n    var i = state.context.next === next && textAfter && textAfter.charAt(0) === ']' ? -1 : state.userIndentationDelta;\n    return (state.indentation + i) * cx.unit;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*\\]$/\n  }\n}\n"], "names": [], "sourceRoot": ""}