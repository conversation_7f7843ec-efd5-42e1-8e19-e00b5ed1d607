{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# The Copper-to-Gold Ratio Using the OpenBB Platform\n", "\n", "The copper-to-gold ratio is known as a leading economic indicator.  It is most commonly paired as a time series with the ten-year US Treasury yield. The notable events are the divergences in directional movement, signaling a fundamental regime change that will unfold over months and years.  Not something to go YOLO into, but a metric to shape a long-term view of global economic conditions.\n", "\n", "The ratio is defined as dividing the spot price of one ounce of copper by an ounce gold.  How much copper is bought with one ouce of gold.  Sounds simple enough,  divide the price of copper by the price of gold, done.  The OpenBB Platform can make quick work out of this task, really quick.  Let's explore."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import the Platform and Pandas for some calculations.\n", "\n", "import pandas as pd\n", "import plotly.graph_objects as go\n", "from openbb import obb"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The most accessible data is going to be the continuous front-month futures contracts for physical delivery, listed on the CME.  We'll create a Pandas Series for each asset, requesting weekly historical data using the `openbb-yfinance` data extension."]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["data = pd.DataFrame()\n", "cols_dict = {\"GC=F\": \"Gold\", \"HG=F\": \"Copper\"}\n", "data = (\n", "    obb.derivatives.futures.historical(\n", "        [\"GC\", \"HG\"],\n", "        start_date=\"2000-01-01\",\n", "        end_date=\"2024-08-19\",\n", "        interval=\"1W\",\n", "    )\n", "    .to_df()\n", "    .pivot(columns=\"symbol\", values=\"close\")\n", ")\n", "data.columns = [cols_dict[symbol] for symbol in data.columns]\n", "data.index = pd.to_datetime(data.index)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Let's inspect the results."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Gold</th>\n", "      <th>Copper</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2000-08-28</th>\n", "      <td>277.000000</td>\n", "      <td>0.889</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2000-09-04</th>\n", "      <td>273.299988</td>\n", "      <td>0.912</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  Gold  Copper\n", "date                          \n", "2000-08-28  277.000000   0.889\n", "2000-09-04  273.299988   0.912"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["data.head(2)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["To get the copper-to-gold ratio, divide the two columns along each row."]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Gold</th>\n", "      <th>Copper</th>\n", "      <th>Copper/Gold Ratio</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-08-12</th>\n", "      <td>2498.600098</td>\n", "      <td>4.1275</td>\n", "      <td>0.001652</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-19</th>\n", "      <td>2519.000000</td>\n", "      <td>4.1355</td>\n", "      <td>0.001642</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   Gold  Copper  Copper/Gold Ratio\n", "date                                              \n", "2024-08-12  2498.600098  4.1275           0.001652\n", "2024-08-19  2519.000000  4.1355           0.001642"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["data[\"Copper/Gold Ratio\"] = data[\"Copper\"] / data[\"Gold\"]\n", "\n", "data.tail(2)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Because the numbers are so small, the ratio is often be presented as a % value.  0.2% is a popular way to display the value.  However, to plot it on the same y-axis as a Treasury yield, it needs to be multiplied by 1000.  Let's alter the block above to include this."]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Gold</th>\n", "      <th>Copper</th>\n", "      <th>Copper/Gold Ratio</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-08-12</th>\n", "      <td>2498.600098</td>\n", "      <td>4.1275</td>\n", "      <td>1.651925</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-19</th>\n", "      <td>2519.000000</td>\n", "      <td>4.1355</td>\n", "      <td>1.641723</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   Gold  Copper  Copper/Gold Ratio\n", "date                                              \n", "2024-08-12  2498.600098  4.1275           1.651925\n", "2024-08-19  2519.000000  4.1355           1.641723"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["data[\"Copper/Gold Ratio\"] = (data[\"Copper\"] / data[\"Gold\"]) * 1000\n", "\n", "data.tail(2)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Now let's add a column for the daily 10 Year US Treasury Yield.  This can be requested using the `fred_series` function within the `economy` module.  The first line in the block below requests the data, the second assigns it to a column in the target DataFrame."]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Gold</th>\n", "      <th>Copper</th>\n", "      <th>Copper/Gold Ratio</th>\n", "      <th>US 10-Year Constant Maturity</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2000-08-28</th>\n", "      <td>277.000000</td>\n", "      <td>0.889</td>\n", "      <td>3.209386</td>\n", "      <td>5.78</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2000-09-04</th>\n", "      <td>273.299988</td>\n", "      <td>0.912</td>\n", "      <td>3.336992</td>\n", "      <td>5.68</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  Gold  Copper  Copper/Gold Ratio  \\\n", "date                                                \n", "2000-08-28  277.000000   0.889           3.209386   \n", "2000-09-04  273.299988   0.912           3.336992   \n", "\n", "            US 10-Year Constant Maturity  \n", "date                                      \n", "2000-08-28                          5.78  \n", "2000-09-04                          5.68  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["us10year = obb.economy.fred_series(\n", "    \"DGS10\", frequency=\"wem\", start_date=\"2000-08-28\", end_date=\"2024-08-19\"\n", ").to_df()[[\"DGS10\"]]\n", "\n", "data[\"US 10-Year Constant Maturity\"] = us10year[\"DGS10\"]\n", "\n", "data.head(2)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["With all the data collected, let's draw the chart to visualize the relationship."]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"name": "Copper/Gold Ratio (x1000) %", "type": "scatter", "x": ["2000-08-28T00:00:00", "2000-09-04T00:00:00", "2000-09-11T00:00:00", "2000-09-18T00:00:00", "2000-09-25T00:00:00", "2000-10-02T00:00:00", "2000-10-09T00:00:00", "2000-10-16T00:00:00", "2000-10-23T00:00:00", "2000-10-30T00:00:00", "2000-11-06T00:00:00", "2000-11-13T00:00:00", "2000-11-20T00:00:00", "2000-11-27T00:00:00", "2000-12-04T00:00:00", "2000-12-11T00:00:00", "2000-12-18T00:00:00", "2000-12-25T00:00:00", "2001-01-01T00:00:00", "2001-01-08T00:00:00", "2001-01-15T00:00:00", "2001-01-22T00:00:00", "2001-01-29T00:00:00", "2001-02-05T00:00:00", "2001-02-12T00:00:00", "2001-02-19T00:00:00", "2001-02-26T00:00:00", "2001-03-05T00:00:00", "2001-03-12T00:00:00", "2001-03-19T00:00:00", "2001-03-26T00:00:00", "2001-04-02T00:00:00", "2001-04-09T00:00:00", "2001-04-16T00:00:00", "2001-04-23T00:00:00", "2001-04-30T00:00:00", "2001-05-07T00:00:00", "2001-05-14T00:00:00", "2001-05-21T00:00:00", "2001-05-28T00:00:00", "2001-06-04T00:00:00", "2001-06-11T00:00:00", "2001-06-18T00:00:00", "2001-06-25T00:00:00", "2001-07-02T00:00:00", "2001-07-09T00:00:00", "2001-07-16T00:00:00", "2001-07-23T00:00:00", "2001-07-30T00:00:00", "2001-08-06T00:00:00", "2001-08-13T00:00:00", "2001-08-20T00:00:00", "2001-08-27T00:00:00", "2001-09-03T00:00:00", "2001-09-10T00:00:00", "2001-09-17T00:00:00", "2001-09-24T00:00:00", "2001-10-01T00:00:00", "2001-10-08T00:00:00", "2001-10-15T00:00:00", "2001-10-22T00:00:00", "2001-10-29T00:00:00", "2001-11-05T00:00:00", "2001-11-12T00:00:00", "2001-11-19T00:00:00", "2001-11-26T00:00:00", "2001-12-03T00:00:00", "2001-12-10T00:00:00", "2001-12-17T00:00:00", "2001-12-24T00:00:00", "2001-12-31T00:00:00", "2002-01-07T00:00:00", "2002-01-14T00:00:00", "2002-01-21T00:00:00", "2002-01-28T00:00:00", "2002-02-04T00:00:00", "2002-02-11T00:00:00", "2002-02-18T00:00:00", "2002-02-25T00:00:00", "2002-03-04T00:00:00", "2002-03-11T00:00:00", "2002-03-18T00:00:00", "2002-03-25T00:00:00", "2002-04-01T00:00:00", "2002-04-08T00:00:00", "2002-04-15T00:00:00", "2002-04-22T00:00:00", "2002-04-29T00:00:00", "2002-05-06T00:00:00", "2002-05-13T00:00:00", "2002-05-20T00:00:00", "2002-05-27T00:00:00", "2002-06-03T00:00:00", "2002-06-10T00:00:00", "2002-06-17T00:00:00", "2002-06-24T00:00:00", "2002-07-01T00:00:00", "2002-07-08T00:00:00", "2002-07-15T00:00:00", "2002-07-22T00:00:00", "2002-07-29T00:00:00", "2002-08-05T00:00:00", "2002-08-12T00:00:00", "2002-08-19T00:00:00", "2002-08-26T00:00:00", "2002-09-02T00:00:00", "2002-09-09T00:00:00", "2002-09-16T00:00:00", "2002-09-23T00:00:00", "2002-09-30T00:00:00", "2002-10-07T00:00:00", "2002-10-14T00:00:00", "2002-10-21T00:00:00", "2002-10-28T00:00:00", "2002-11-04T00:00:00", "2002-11-11T00:00:00", "2002-11-18T00:00:00", "2002-11-25T00:00:00", "2002-12-02T00:00:00", "2002-12-09T00:00:00", "2002-12-16T00:00:00", "2002-12-23T00:00:00", "2002-12-30T00:00:00", "2003-01-06T00:00:00", "2003-01-13T00:00:00", "2003-01-20T00:00:00", "2003-01-27T00:00:00", "2003-02-03T00:00:00", "2003-02-10T00:00:00", "2003-02-17T00:00:00", "2003-02-24T00:00:00", "2003-03-03T00:00:00", "2003-03-10T00:00:00", "2003-03-17T00:00:00", "2003-03-24T00:00:00", "2003-03-31T00:00:00", "2003-04-07T00:00:00", "2003-04-14T00:00:00", "2003-04-21T00:00:00", "2003-04-28T00:00:00", "2003-05-05T00:00:00", "2003-05-12T00:00:00", "2003-05-19T00:00:00", "2003-05-26T00:00:00", "2003-06-02T00:00:00", "2003-06-09T00:00:00", "2003-06-16T00:00:00", "2003-06-23T00:00:00", "2003-06-30T00:00:00", "2003-07-07T00:00:00", "2003-07-14T00:00:00", "2003-07-21T00:00:00", "2003-07-28T00:00:00", "2003-08-04T00:00:00", "2003-08-11T00:00:00", "2003-08-18T00:00:00", "2003-08-25T00:00:00", "2003-09-01T00:00:00", "2003-09-08T00:00:00", "2003-09-15T00:00:00", "2003-09-22T00:00:00", "2003-09-29T00:00:00", "2003-10-06T00:00:00", "2003-10-13T00:00:00", "2003-10-20T00:00:00", "2003-10-27T00:00:00", "2003-11-03T00:00:00", "2003-11-10T00:00:00", "2003-11-17T00:00:00", "2003-11-24T00:00:00", "2003-12-01T00:00:00", "2003-12-08T00:00:00", "2003-12-15T00:00:00", "2003-12-22T00:00:00", "2003-12-29T00:00:00", "2004-01-05T00:00:00", "2004-01-12T00:00:00", "2004-01-19T00:00:00", "2004-01-26T00:00:00", "2004-02-02T00:00:00", "2004-02-09T00:00:00", "2004-02-16T00:00:00", "2004-02-23T00:00:00", "2004-03-01T00:00:00", "2004-03-08T00:00:00", "2004-03-15T00:00:00", "2004-03-22T00:00:00", "2004-03-29T00:00:00", "2004-04-05T00:00:00", "2004-04-12T00:00:00", "2004-04-19T00:00:00", "2004-04-26T00:00:00", "2004-05-03T00:00:00", "2004-05-10T00:00:00", "2004-05-17T00:00:00", "2004-05-24T00:00:00", "2004-05-31T00:00:00", "2004-06-07T00:00:00", "2004-06-14T00:00:00", "2004-06-21T00:00:00", "2004-06-28T00:00:00", "2004-07-05T00:00:00", "2004-07-12T00:00:00", "2004-07-19T00:00:00", "2004-07-26T00:00:00", "2004-08-02T00:00:00", "2004-08-09T00:00:00", "2004-08-16T00:00:00", "2004-08-23T00:00:00", "2004-08-30T00:00:00", "2004-09-06T00:00:00", "2004-09-13T00:00:00", "2004-09-20T00:00:00", "2004-09-27T00:00:00", "2004-10-04T00:00:00", "2004-10-11T00:00:00", "2004-10-18T00:00:00", "2004-10-25T00:00:00", "2004-11-01T00:00:00", "2004-11-08T00:00:00", "2004-11-15T00:00:00", "2004-11-22T00:00:00", "2004-11-29T00:00:00", "2004-12-06T00:00:00", "2004-12-13T00:00:00", "2004-12-20T00:00:00", "2004-12-27T00:00:00", "2005-01-03T00:00:00", "2005-01-10T00:00:00", "2005-01-17T00:00:00", "2005-01-24T00:00:00", "2005-01-31T00:00:00", "2005-02-07T00:00:00", "2005-02-14T00:00:00", "2005-02-21T00:00:00", "2005-02-28T00:00:00", "2005-03-07T00:00:00", "2005-03-14T00:00:00", "2005-03-21T00:00:00", "2005-03-28T00:00:00", "2005-04-04T00:00:00", "2005-04-11T00:00:00", "2005-04-18T00:00:00", "2005-04-25T00:00:00", "2005-05-02T00:00:00", "2005-05-09T00:00:00", "2005-05-16T00:00:00", "2005-05-23T00:00:00", "2005-05-30T00:00:00", "2005-06-06T00:00:00", "2005-06-13T00:00:00", "2005-06-20T00:00:00", "2005-06-27T00:00:00", "2005-07-04T00:00:00", "2005-07-11T00:00:00", "2005-07-18T00:00:00", "2005-07-25T00:00:00", "2005-08-01T00:00:00", "2005-08-08T00:00:00", "2005-08-15T00:00:00", "2005-08-22T00:00:00", "2005-08-29T00:00:00", "2005-09-05T00:00:00", "2005-09-12T00:00:00", "2005-09-19T00:00:00", "2005-09-26T00:00:00", "2005-10-03T00:00:00", "2005-10-10T00:00:00", "2005-10-17T00:00:00", "2005-10-24T00:00:00", "2005-10-31T00:00:00", "2005-11-07T00:00:00", "2005-11-14T00:00:00", "2005-11-21T00:00:00", "2005-11-28T00:00:00", "2005-12-05T00:00:00", "2005-12-12T00:00:00", "2005-12-19T00:00:00", "2005-12-26T00:00:00", "2006-01-02T00:00:00", "2006-01-09T00:00:00", "2006-01-16T00:00:00", "2006-01-23T00:00:00", "2006-01-30T00:00:00", "2006-02-06T00:00:00", "2006-02-13T00:00:00", "2006-02-20T00:00:00", "2006-02-27T00:00:00", "2006-03-06T00:00:00", "2006-03-13T00:00:00", "2006-03-20T00:00:00", "2006-03-27T00:00:00", "2006-04-03T00:00:00", "2006-04-10T00:00:00", "2006-04-17T00:00:00", "2006-04-24T00:00:00", "2006-05-01T00:00:00", "2006-05-08T00:00:00", "2006-05-15T00:00:00", "2006-05-22T00:00:00", "2006-05-29T00:00:00", "2006-06-05T00:00:00", "2006-06-12T00:00:00", "2006-06-19T00:00:00", "2006-06-26T00:00:00", "2006-07-03T00:00:00", "2006-07-10T00:00:00", "2006-07-17T00:00:00", "2006-07-24T00:00:00", "2006-07-31T00:00:00", "2006-08-07T00:00:00", "2006-08-14T00:00:00", "2006-08-21T00:00:00", "2006-08-28T00:00:00", "2006-09-04T00:00:00", "2006-09-11T00:00:00", "2006-09-18T00:00:00", "2006-09-25T00:00:00", "2006-10-02T00:00:00", "2006-10-09T00:00:00", "2006-10-16T00:00:00", "2006-10-23T00:00:00", "2006-10-30T00:00:00", "2006-11-06T00:00:00", "2006-11-13T00:00:00", "2006-11-20T00:00:00", "2006-11-27T00:00:00", "2006-12-04T00:00:00", "2006-12-11T00:00:00", "2006-12-18T00:00:00", "2006-12-25T00:00:00", "2007-01-01T00:00:00", "2007-01-08T00:00:00", "2007-01-15T00:00:00", "2007-01-22T00:00:00", "2007-01-29T00:00:00", "2007-02-05T00:00:00", "2007-02-12T00:00:00", "2007-02-19T00:00:00", "2007-02-26T00:00:00", "2007-03-05T00:00:00", "2007-03-12T00:00:00", "2007-03-19T00:00:00", "2007-03-26T00:00:00", "2007-04-02T00:00:00", "2007-04-09T00:00:00", "2007-04-16T00:00:00", "2007-04-23T00:00:00", "2007-04-30T00:00:00", "2007-05-07T00:00:00", "2007-05-14T00:00:00", "2007-05-21T00:00:00", "2007-05-28T00:00:00", "2007-06-04T00:00:00", "2007-06-11T00:00:00", "2007-06-18T00:00:00", "2007-06-25T00:00:00", "2007-07-02T00:00:00", "2007-07-09T00:00:00", "2007-07-16T00:00:00", "2007-07-23T00:00:00", "2007-07-30T00:00:00", "2007-08-06T00:00:00", "2007-08-13T00:00:00", "2007-08-20T00:00:00", "2007-08-27T00:00:00", "2007-09-03T00:00:00", "2007-09-10T00:00:00", "2007-09-17T00:00:00", "2007-09-24T00:00:00", "2007-10-01T00:00:00", "2007-10-08T00:00:00", "2007-10-15T00:00:00", "2007-10-22T00:00:00", "2007-10-29T00:00:00", "2007-11-05T00:00:00", "2007-11-12T00:00:00", "2007-11-19T00:00:00", "2007-11-26T00:00:00", "2007-12-03T00:00:00", "2007-12-10T00:00:00", "2007-12-17T00:00:00", "2007-12-24T00:00:00", "2007-12-31T00:00:00", "2008-01-07T00:00:00", "2008-01-14T00:00:00", "2008-01-21T00:00:00", "2008-01-28T00:00:00", "2008-02-04T00:00:00", "2008-02-11T00:00:00", "2008-02-18T00:00:00", "2008-02-25T00:00:00", "2008-03-03T00:00:00", "2008-03-10T00:00:00", "2008-03-17T00:00:00", "2008-03-24T00:00:00", "2008-03-31T00:00:00", "2008-04-07T00:00:00", "2008-04-14T00:00:00", "2008-04-21T00:00:00", "2008-04-28T00:00:00", "2008-05-05T00:00:00", "2008-05-12T00:00:00", "2008-05-19T00:00:00", "2008-05-26T00:00:00", "2008-06-02T00:00:00", "2008-06-09T00:00:00", "2008-06-16T00:00:00", "2008-06-23T00:00:00", "2008-06-30T00:00:00", "2008-07-07T00:00:00", "2008-07-14T00:00:00", "2008-07-21T00:00:00", "2008-07-28T00:00:00", "2008-08-04T00:00:00", "2008-08-11T00:00:00", "2008-08-18T00:00:00", "2008-08-25T00:00:00", "2008-09-01T00:00:00", "2008-09-08T00:00:00", "2008-09-15T00:00:00", "2008-09-22T00:00:00", "2008-09-29T00:00:00", "2008-10-06T00:00:00", "2008-10-13T00:00:00", "2008-10-20T00:00:00", "2008-10-27T00:00:00", "2008-11-03T00:00:00", "2008-11-10T00:00:00", "2008-11-17T00:00:00", "2008-11-24T00:00:00", "2008-12-01T00:00:00", "2008-12-08T00:00:00", "2008-12-15T00:00:00", "2008-12-22T00:00:00", "2008-12-29T00:00:00", "2009-01-05T00:00:00", "2009-01-12T00:00:00", "2009-01-19T00:00:00", "2009-01-26T00:00:00", "2009-02-02T00:00:00", "2009-02-09T00:00:00", "2009-02-16T00:00:00", "2009-02-23T00:00:00", "2009-03-02T00:00:00", "2009-03-09T00:00:00", "2009-03-16T00:00:00", "2009-03-23T00:00:00", "2009-03-30T00:00:00", "2009-04-06T00:00:00", "2009-04-13T00:00:00", "2009-04-20T00:00:00", "2009-04-27T00:00:00", "2009-05-04T00:00:00", "2009-05-11T00:00:00", "2009-05-18T00:00:00", "2009-05-25T00:00:00", "2009-06-01T00:00:00", "2009-06-08T00:00:00", "2009-06-15T00:00:00", "2009-06-22T00:00:00", "2009-06-29T00:00:00", "2009-07-06T00:00:00", "2009-07-13T00:00:00", "2009-07-20T00:00:00", "2009-07-27T00:00:00", "2009-08-03T00:00:00", "2009-08-10T00:00:00", "2009-08-17T00:00:00", "2009-08-24T00:00:00", "2009-08-31T00:00:00", "2009-09-07T00:00:00", "2009-09-14T00:00:00", "2009-09-21T00:00:00", "2009-09-28T00:00:00", "2009-10-05T00:00:00", "2009-10-12T00:00:00", "2009-10-19T00:00:00", "2009-10-26T00:00:00", "2009-11-02T00:00:00", "2009-11-09T00:00:00", "2009-11-16T00:00:00", "2009-11-23T00:00:00", "2009-11-30T00:00:00", "2009-12-07T00:00:00", "2009-12-14T00:00:00", "2009-12-21T00:00:00", "2009-12-28T00:00:00", "2010-01-04T00:00:00", "2010-01-11T00:00:00", "2010-01-18T00:00:00", "2010-01-25T00:00:00", "2010-02-01T00:00:00", "2010-02-08T00:00:00", "2010-02-15T00:00:00", "2010-02-22T00:00:00", "2010-03-01T00:00:00", "2010-03-08T00:00:00", "2010-03-15T00:00:00", "2010-03-22T00:00:00", "2010-03-29T00:00:00", "2010-04-05T00:00:00", "2010-04-12T00:00:00", "2010-04-19T00:00:00", "2010-04-26T00:00:00", "2010-05-03T00:00:00", "2010-05-10T00:00:00", "2010-05-17T00:00:00", "2010-05-24T00:00:00", "2010-05-31T00:00:00", "2010-06-07T00:00:00", "2010-06-14T00:00:00", "2010-06-21T00:00:00", "2010-06-28T00:00:00", "2010-07-05T00:00:00", "2010-07-12T00:00:00", "2010-07-19T00:00:00", "2010-07-26T00:00:00", "2010-08-02T00:00:00", "2010-08-09T00:00:00", "2010-08-16T00:00:00", "2010-08-23T00:00:00", "2010-08-30T00:00:00", "2010-09-06T00:00:00", "2010-09-13T00:00:00", "2010-09-20T00:00:00", "2010-09-27T00:00:00", "2010-10-04T00:00:00", "2010-10-11T00:00:00", "2010-10-18T00:00:00", "2010-10-25T00:00:00", "2010-11-01T00:00:00", "2010-11-08T00:00:00", "2010-11-15T00:00:00", "2010-11-22T00:00:00", "2010-11-29T00:00:00", "2010-12-06T00:00:00", "2010-12-13T00:00:00", "2010-12-20T00:00:00", "2010-12-27T00:00:00", "2011-01-03T00:00:00", "2011-01-10T00:00:00", "2011-01-17T00:00:00", "2011-01-24T00:00:00", "2011-01-31T00:00:00", "2011-02-07T00:00:00", "2011-02-14T00:00:00", "2011-02-21T00:00:00", "2011-02-28T00:00:00", "2011-03-07T00:00:00", "2011-03-14T00:00:00", "2011-03-21T00:00:00", "2011-03-28T00:00:00", "2011-04-04T00:00:00", "2011-04-11T00:00:00", "2011-04-18T00:00:00", "2011-04-25T00:00:00", "2011-05-02T00:00:00", "2011-05-09T00:00:00", "2011-05-16T00:00:00", "2011-05-23T00:00:00", "2011-05-30T00:00:00", "2011-06-06T00:00:00", "2011-06-13T00:00:00", "2011-06-20T00:00:00", "2011-06-27T00:00:00", "2011-07-04T00:00:00", "2011-07-11T00:00:00", "2011-07-18T00:00:00", "2011-07-25T00:00:00", "2011-08-01T00:00:00", "2011-08-08T00:00:00", "2011-08-15T00:00:00", "2011-08-22T00:00:00", "2011-08-29T00:00:00", "2011-09-05T00:00:00", "2011-09-12T00:00:00", "2011-09-19T00:00:00", "2011-09-26T00:00:00", "2011-10-03T00:00:00", "2011-10-10T00:00:00", "2011-10-17T00:00:00", "2011-10-24T00:00:00", "2011-10-31T00:00:00", "2011-11-07T00:00:00", "2011-11-14T00:00:00", "2011-11-21T00:00:00", "2011-11-28T00:00:00", "2011-12-05T00:00:00", "2011-12-12T00:00:00", "2011-12-19T00:00:00", "2011-12-26T00:00:00", "2012-01-02T00:00:00", "2012-01-09T00:00:00", "2012-01-16T00:00:00", "2012-01-23T00:00:00", "2012-01-30T00:00:00", "2012-02-06T00:00:00", "2012-02-13T00:00:00", "2012-02-20T00:00:00", "2012-02-27T00:00:00", "2012-03-05T00:00:00", "2012-03-12T00:00:00", "2012-03-19T00:00:00", "2012-03-26T00:00:00", "2012-04-02T00:00:00", "2012-04-09T00:00:00", "2012-04-16T00:00:00", "2012-04-23T00:00:00", "2012-04-30T00:00:00", "2012-05-07T00:00:00", "2012-05-14T00:00:00", "2012-05-21T00:00:00", "2012-05-28T00:00:00", "2012-06-04T00:00:00", "2012-06-11T00:00:00", "2012-06-18T00:00:00", "2012-06-25T00:00:00", "2012-07-02T00:00:00", "2012-07-09T00:00:00", "2012-07-16T00:00:00", "2012-07-23T00:00:00", "2012-07-30T00:00:00", "2012-08-06T00:00:00", "2012-08-13T00:00:00", "2012-08-20T00:00:00", "2012-08-27T00:00:00", "2012-09-03T00:00:00", "2012-09-10T00:00:00", "2012-09-17T00:00:00", "2012-09-24T00:00:00", "2012-10-01T00:00:00", "2012-10-08T00:00:00", "2012-10-15T00:00:00", "2012-10-22T00:00:00", "2012-10-29T00:00:00", "2012-11-05T00:00:00", "2012-11-12T00:00:00", "2012-11-19T00:00:00", "2012-11-26T00:00:00", "2012-12-03T00:00:00", "2012-12-10T00:00:00", "2012-12-17T00:00:00", "2012-12-24T00:00:00", "2012-12-31T00:00:00", "2013-01-07T00:00:00", "2013-01-14T00:00:00", "2013-01-21T00:00:00", "2013-01-28T00:00:00", "2013-02-04T00:00:00", "2013-02-11T00:00:00", "2013-02-18T00:00:00", "2013-02-25T00:00:00", "2013-03-04T00:00:00", "2013-03-11T00:00:00", "2013-03-18T00:00:00", "2013-03-25T00:00:00", "2013-04-01T00:00:00", "2013-04-08T00:00:00", "2013-04-15T00:00:00", "2013-04-22T00:00:00", "2013-04-29T00:00:00", "2013-05-06T00:00:00", "2013-05-13T00:00:00", "2013-05-20T00:00:00", "2013-05-27T00:00:00", "2013-06-03T00:00:00", "2013-06-10T00:00:00", "2013-06-17T00:00:00", "2013-06-24T00:00:00", "2013-07-01T00:00:00", "2013-07-08T00:00:00", "2013-07-15T00:00:00", "2013-07-22T00:00:00", "2013-07-29T00:00:00", "2013-08-05T00:00:00", "2013-08-12T00:00:00", "2013-08-19T00:00:00", "2013-08-26T00:00:00", "2013-09-02T00:00:00", "2013-09-09T00:00:00", "2013-09-16T00:00:00", "2013-09-23T00:00:00", "2013-09-30T00:00:00", "2013-10-07T00:00:00", "2013-10-14T00:00:00", "2013-10-21T00:00:00", "2013-10-28T00:00:00", "2013-11-04T00:00:00", "2013-11-11T00:00:00", "2013-11-18T00:00:00", "2013-11-25T00:00:00", "2013-12-02T00:00:00", "2013-12-09T00:00:00", "2013-12-16T00:00:00", "2013-12-23T00:00:00", "2013-12-30T00:00:00", "2014-01-06T00:00:00", "2014-01-13T00:00:00", "2014-01-20T00:00:00", "2014-01-27T00:00:00", "2014-02-03T00:00:00", "2014-02-10T00:00:00", "2014-02-17T00:00:00", "2014-02-24T00:00:00", "2014-03-03T00:00:00", "2014-03-10T00:00:00", "2014-03-17T00:00:00", "2014-03-24T00:00:00", "2014-03-31T00:00:00", "2014-04-07T00:00:00", "2014-04-14T00:00:00", "2014-04-21T00:00:00", "2014-04-28T00:00:00", "2014-05-05T00:00:00", "2014-05-12T00:00:00", "2014-05-19T00:00:00", "2014-05-26T00:00:00", "2014-06-02T00:00:00", "2014-06-09T00:00:00", "2014-06-16T00:00:00", "2014-06-23T00:00:00", "2014-06-30T00:00:00", "2014-07-07T00:00:00", "2014-07-14T00:00:00", "2014-07-21T00:00:00", "2014-07-28T00:00:00", "2014-08-04T00:00:00", "2014-08-11T00:00:00", "2014-08-18T00:00:00", "2014-08-25T00:00:00", "2014-09-01T00:00:00", "2014-09-08T00:00:00", "2014-09-15T00:00:00", "2014-09-22T00:00:00", "2014-09-29T00:00:00", "2014-10-06T00:00:00", "2014-10-13T00:00:00", "2014-10-20T00:00:00", "2014-10-27T00:00:00", "2014-11-03T00:00:00", "2014-11-10T00:00:00", "2014-11-17T00:00:00", "2014-11-24T00:00:00", "2014-12-01T00:00:00", "2014-12-08T00:00:00", "2014-12-15T00:00:00", "2014-12-22T00:00:00", "2014-12-29T00:00:00", "2015-01-05T00:00:00", "2015-01-12T00:00:00", "2015-01-19T00:00:00", "2015-01-26T00:00:00", "2015-02-02T00:00:00", "2015-02-09T00:00:00", "2015-02-16T00:00:00", "2015-02-23T00:00:00", "2015-03-02T00:00:00", "2015-03-09T00:00:00", "2015-03-16T00:00:00", "2015-03-23T00:00:00", "2015-03-30T00:00:00", "2015-04-06T00:00:00", "2015-04-13T00:00:00", "2015-04-20T00:00:00", "2015-04-27T00:00:00", "2015-05-04T00:00:00", "2015-05-11T00:00:00", "2015-05-18T00:00:00", "2015-05-25T00:00:00", "2015-06-01T00:00:00", "2015-06-08T00:00:00", "2015-06-15T00:00:00", "2015-06-22T00:00:00", "2015-06-29T00:00:00", "2015-07-06T00:00:00", "2015-07-13T00:00:00", "2015-07-20T00:00:00", "2015-07-27T00:00:00", "2015-08-03T00:00:00", "2015-08-10T00:00:00", "2015-08-17T00:00:00", "2015-08-24T00:00:00", "2015-08-31T00:00:00", "2015-09-07T00:00:00", "2015-09-14T00:00:00", "2015-09-21T00:00:00", "2015-09-28T00:00:00", "2015-10-05T00:00:00", "2015-10-12T00:00:00", "2015-10-19T00:00:00", "2015-10-26T00:00:00", "2015-11-02T00:00:00", "2015-11-09T00:00:00", "2015-11-16T00:00:00", "2015-11-23T00:00:00", "2015-11-30T00:00:00", "2015-12-07T00:00:00", "2015-12-14T00:00:00", "2015-12-21T00:00:00", "2015-12-28T00:00:00", "2016-01-04T00:00:00", "2016-01-11T00:00:00", "2016-01-18T00:00:00", "2016-01-25T00:00:00", "2016-02-01T00:00:00", "2016-02-08T00:00:00", "2016-02-15T00:00:00", "2016-02-22T00:00:00", "2016-02-29T00:00:00", "2016-03-07T00:00:00", "2016-03-14T00:00:00", "2016-03-21T00:00:00", "2016-03-28T00:00:00", "2016-04-04T00:00:00", "2016-04-11T00:00:00", "2016-04-18T00:00:00", "2016-04-25T00:00:00", "2016-05-02T00:00:00", "2016-05-09T00:00:00", "2016-05-16T00:00:00", "2016-05-23T00:00:00", "2016-05-30T00:00:00", "2016-06-06T00:00:00", "2016-06-13T00:00:00", "2016-06-20T00:00:00", "2016-06-27T00:00:00", "2016-07-04T00:00:00", "2016-07-11T00:00:00", "2016-07-18T00:00:00", "2016-07-25T00:00:00", "2016-08-01T00:00:00", "2016-08-08T00:00:00", "2016-08-15T00:00:00", "2016-08-22T00:00:00", "2016-08-29T00:00:00", "2016-09-05T00:00:00", "2016-09-12T00:00:00", "2016-09-19T00:00:00", "2016-09-26T00:00:00", "2016-10-03T00:00:00", "2016-10-10T00:00:00", "2016-10-17T00:00:00", "2016-10-24T00:00:00", "2016-10-31T00:00:00", "2016-11-07T00:00:00", "2016-11-14T00:00:00", "2016-11-21T00:00:00", "2016-11-28T00:00:00", "2016-12-05T00:00:00", "2016-12-12T00:00:00", "2016-12-19T00:00:00", "2016-12-26T00:00:00", "2017-01-02T00:00:00", "2017-01-09T00:00:00", "2017-01-16T00:00:00", "2017-01-23T00:00:00", "2017-01-30T00:00:00", "2017-02-06T00:00:00", "2017-02-13T00:00:00", "2017-02-20T00:00:00", "2017-02-27T00:00:00", "2017-03-06T00:00:00", "2017-03-13T00:00:00", "2017-03-20T00:00:00", "2017-03-27T00:00:00", "2017-04-03T00:00:00", "2017-04-10T00:00:00", "2017-04-17T00:00:00", "2017-04-24T00:00:00", "2017-05-01T00:00:00", "2017-05-08T00:00:00", "2017-05-15T00:00:00", "2017-05-22T00:00:00", "2017-05-29T00:00:00", "2017-06-05T00:00:00", "2017-06-12T00:00:00", "2017-06-19T00:00:00", "2017-06-26T00:00:00", "2017-07-03T00:00:00", "2017-07-10T00:00:00", "2017-07-17T00:00:00", "2017-07-24T00:00:00", "2017-07-31T00:00:00", "2017-08-07T00:00:00", "2017-08-14T00:00:00", "2017-08-21T00:00:00", "2017-08-28T00:00:00", "2017-09-04T00:00:00", "2017-09-11T00:00:00", "2017-09-18T00:00:00", "2017-09-25T00:00:00", "2017-10-02T00:00:00", "2017-10-09T00:00:00", "2017-10-16T00:00:00", "2017-10-23T00:00:00", "2017-10-30T00:00:00", "2017-11-06T00:00:00", "2017-11-13T00:00:00", "2017-11-20T00:00:00", "2017-11-27T00:00:00", "2017-12-04T00:00:00", "2017-12-11T00:00:00", "2017-12-18T00:00:00", "2017-12-25T00:00:00", "2018-01-01T00:00:00", "2018-01-08T00:00:00", "2018-01-15T00:00:00", "2018-01-22T00:00:00", "2018-01-29T00:00:00", "2018-02-05T00:00:00", "2018-02-12T00:00:00", "2018-02-19T00:00:00", "2018-02-26T00:00:00", "2018-03-05T00:00:00", "2018-03-12T00:00:00", "2018-03-19T00:00:00", "2018-03-26T00:00:00", "2018-04-02T00:00:00", "2018-04-09T00:00:00", "2018-04-16T00:00:00", "2018-04-23T00:00:00", "2018-04-30T00:00:00", "2018-05-07T00:00:00", "2018-05-14T00:00:00", "2018-05-21T00:00:00", "2018-05-28T00:00:00", "2018-06-04T00:00:00", "2018-06-11T00:00:00", "2018-06-18T00:00:00", "2018-06-25T00:00:00", "2018-07-02T00:00:00", "2018-07-09T00:00:00", "2018-07-16T00:00:00", "2018-07-23T00:00:00", "2018-07-30T00:00:00", "2018-08-06T00:00:00", "2018-08-13T00:00:00", "2018-08-20T00:00:00", "2018-08-27T00:00:00", "2018-09-03T00:00:00", "2018-09-10T00:00:00", "2018-09-17T00:00:00", "2018-09-24T00:00:00", "2018-10-01T00:00:00", "2018-10-08T00:00:00", "2018-10-15T00:00:00", "2018-10-22T00:00:00", "2018-10-29T00:00:00", "2018-11-05T00:00:00", "2018-11-12T00:00:00", "2018-11-19T00:00:00", "2018-11-26T00:00:00", "2018-12-03T00:00:00", "2018-12-10T00:00:00", "2018-12-17T00:00:00", "2018-12-24T00:00:00", "2018-12-31T00:00:00", "2019-01-07T00:00:00", "2019-01-14T00:00:00", "2019-01-21T00:00:00", "2019-01-28T00:00:00", "2019-02-04T00:00:00", "2019-02-11T00:00:00", "2019-02-18T00:00:00", "2019-02-25T00:00:00", "2019-03-04T00:00:00", "2019-03-11T00:00:00", "2019-03-18T00:00:00", "2019-03-25T00:00:00", "2019-04-01T00:00:00", "2019-04-08T00:00:00", "2019-04-15T00:00:00", "2019-04-22T00:00:00", "2019-04-29T00:00:00", "2019-05-06T00:00:00", "2019-05-13T00:00:00", "2019-05-20T00:00:00", "2019-05-27T00:00:00", "2019-06-03T00:00:00", "2019-06-10T00:00:00", "2019-06-17T00:00:00", "2019-06-24T00:00:00", "2019-07-01T00:00:00", "2019-07-08T00:00:00", "2019-07-15T00:00:00", "2019-07-22T00:00:00", "2019-07-29T00:00:00", "2019-08-05T00:00:00", "2019-08-12T00:00:00", "2019-08-19T00:00:00", "2019-08-26T00:00:00", "2019-09-02T00:00:00", "2019-09-09T00:00:00", "2019-09-16T00:00:00", "2019-09-23T00:00:00", "2019-09-30T00:00:00", "2019-10-07T00:00:00", "2019-10-14T00:00:00", "2019-10-21T00:00:00", "2019-10-28T00:00:00", "2019-11-04T00:00:00", "2019-11-11T00:00:00", "2019-11-18T00:00:00", "2019-11-25T00:00:00", "2019-12-02T00:00:00", "2019-12-09T00:00:00", "2019-12-16T00:00:00", "2019-12-23T00:00:00", "2019-12-30T00:00:00", "2020-01-06T00:00:00", "2020-01-13T00:00:00", "2020-01-20T00:00:00", "2020-01-27T00:00:00", "2020-02-03T00:00:00", "2020-02-10T00:00:00", "2020-02-17T00:00:00", "2020-02-24T00:00:00", "2020-03-02T00:00:00", "2020-03-09T00:00:00", "2020-03-16T00:00:00", "2020-03-23T00:00:00", "2020-03-30T00:00:00", "2020-04-06T00:00:00", "2020-04-13T00:00:00", "2020-04-20T00:00:00", "2020-04-27T00:00:00", "2020-05-04T00:00:00", "2020-05-11T00:00:00", "2020-05-18T00:00:00", "2020-05-25T00:00:00", "2020-06-01T00:00:00", "2020-06-08T00:00:00", "2020-06-15T00:00:00", "2020-06-22T00:00:00", "2020-06-29T00:00:00", "2020-07-06T00:00:00", "2020-07-13T00:00:00", "2020-07-20T00:00:00", "2020-07-27T00:00:00", "2020-08-03T00:00:00", "2020-08-10T00:00:00", "2020-08-17T00:00:00", "2020-08-24T00:00:00", "2020-08-31T00:00:00", "2020-09-07T00:00:00", "2020-09-14T00:00:00", "2020-09-21T00:00:00", "2020-09-28T00:00:00", "2020-10-05T00:00:00", "2020-10-12T00:00:00", "2020-10-19T00:00:00", "2020-10-26T00:00:00", "2020-11-02T00:00:00", "2020-11-09T00:00:00", "2020-11-16T00:00:00", "2020-11-23T00:00:00", "2020-11-30T00:00:00", "2020-12-07T00:00:00", "2020-12-14T00:00:00", "2020-12-21T00:00:00", "2020-12-28T00:00:00", "2021-01-04T00:00:00", "2021-01-11T00:00:00", "2021-01-18T00:00:00", "2021-01-25T00:00:00", "2021-02-01T00:00:00", "2021-02-08T00:00:00", "2021-02-15T00:00:00", "2021-02-22T00:00:00", "2021-03-01T00:00:00", "2021-03-08T00:00:00", "2021-03-15T00:00:00", "2021-03-22T00:00:00", "2021-03-29T00:00:00", "2021-04-05T00:00:00", "2021-04-12T00:00:00", "2021-04-19T00:00:00", "2021-04-26T00:00:00", "2021-05-03T00:00:00", "2021-05-10T00:00:00", "2021-05-17T00:00:00", "2021-05-24T00:00:00", "2021-05-31T00:00:00", "2021-06-07T00:00:00", "2021-06-14T00:00:00", "2021-06-21T00:00:00", "2021-06-28T00:00:00", "2021-07-05T00:00:00", "2021-07-12T00:00:00", "2021-07-19T00:00:00", "2021-07-26T00:00:00", "2021-08-02T00:00:00", "2021-08-09T00:00:00", "2021-08-16T00:00:00", "2021-08-23T00:00:00", "2021-08-30T00:00:00", "2021-09-06T00:00:00", "2021-09-13T00:00:00", "2021-09-20T00:00:00", "2021-09-27T00:00:00", "2021-10-04T00:00:00", "2021-10-11T00:00:00", "2021-10-18T00:00:00", "2021-10-25T00:00:00", "2021-11-01T00:00:00", "2021-11-08T00:00:00", "2021-11-15T00:00:00", "2021-11-22T00:00:00", "2021-11-29T00:00:00", "2021-12-06T00:00:00", "2021-12-13T00:00:00", "2021-12-20T00:00:00", "2021-12-27T00:00:00", "2022-01-03T00:00:00", "2022-01-10T00:00:00", "2022-01-17T00:00:00", "2022-01-24T00:00:00", "2022-01-31T00:00:00", "2022-02-07T00:00:00", "2022-02-14T00:00:00", "2022-02-21T00:00:00", "2022-02-28T00:00:00", "2022-03-07T00:00:00", "2022-03-14T00:00:00", "2022-03-21T00:00:00", "2022-03-28T00:00:00", "2022-04-04T00:00:00", "2022-04-11T00:00:00", "2022-04-18T00:00:00", "2022-04-25T00:00:00", "2022-05-02T00:00:00", "2022-05-09T00:00:00", "2022-05-16T00:00:00", "2022-05-23T00:00:00", "2022-05-30T00:00:00", "2022-06-06T00:00:00", "2022-06-13T00:00:00", "2022-06-20T00:00:00", "2022-06-27T00:00:00", "2022-07-04T00:00:00", "2022-07-11T00:00:00", "2022-07-18T00:00:00", "2022-07-25T00:00:00", "2022-08-01T00:00:00", "2022-08-08T00:00:00", "2022-08-15T00:00:00", "2022-08-22T00:00:00", "2022-08-29T00:00:00", "2022-09-05T00:00:00", "2022-09-12T00:00:00", "2022-09-19T00:00:00", "2022-09-26T00:00:00", "2022-10-03T00:00:00", "2022-10-10T00:00:00", "2022-10-17T00:00:00", "2022-10-24T00:00:00", "2022-10-31T00:00:00", "2022-11-07T00:00:00", "2022-11-14T00:00:00", "2022-11-21T00:00:00", "2022-11-28T00:00:00", "2022-12-05T00:00:00", "2022-12-12T00:00:00", "2022-12-19T00:00:00", "2022-12-26T00:00:00", "2023-01-02T00:00:00", "2023-01-09T00:00:00", "2023-01-16T00:00:00", "2023-01-23T00:00:00", "2023-01-30T00:00:00", "2023-02-06T00:00:00", "2023-02-13T00:00:00", "2023-02-20T00:00:00", "2023-02-27T00:00:00", "2023-03-06T00:00:00", "2023-03-13T00:00:00", "2023-03-20T00:00:00", "2023-03-27T00:00:00", "2023-04-03T00:00:00", "2023-04-10T00:00:00", "2023-04-17T00:00:00", "2023-04-24T00:00:00", "2023-05-01T00:00:00", "2023-05-08T00:00:00", "2023-05-15T00:00:00", "2023-05-22T00:00:00", "2023-05-29T00:00:00", "2023-06-05T00:00:00", "2023-06-12T00:00:00", "2023-06-19T00:00:00", "2023-06-26T00:00:00", "2023-07-03T00:00:00", "2023-07-10T00:00:00", "2023-07-17T00:00:00", "2023-07-24T00:00:00", "2023-07-31T00:00:00", "2023-08-07T00:00:00", "2023-08-14T00:00:00", "2023-08-21T00:00:00", "2023-08-28T00:00:00", "2023-09-04T00:00:00", "2023-09-11T00:00:00", "2023-09-18T00:00:00", "2023-09-25T00:00:00", "2023-10-02T00:00:00", "2023-10-09T00:00:00", "2023-10-16T00:00:00", "2023-10-23T00:00:00", "2023-10-30T00:00:00", "2023-11-06T00:00:00", "2023-11-13T00:00:00", "2023-11-20T00:00:00", "2023-11-27T00:00:00", "2023-12-04T00:00:00", "2023-12-11T00:00:00", "2023-12-18T00:00:00", "2023-12-25T00:00:00", "2024-01-01T00:00:00", "2024-01-08T00:00:00", "2024-01-15T00:00:00", "2024-01-22T00:00:00", "2024-01-29T00:00:00", "2024-02-05T00:00:00", "2024-02-12T00:00:00", "2024-02-19T00:00:00", "2024-02-26T00:00:00", "2024-03-04T00:00:00", "2024-03-11T00:00:00", "2024-03-18T00:00:00", "2024-03-25T00:00:00", "2024-04-01T00:00:00", "2024-04-08T00:00:00", "2024-04-15T00:00:00", "2024-04-22T00:00:00", "2024-04-29T00:00:00", "2024-05-06T00:00:00", "2024-05-13T00:00:00", "2024-05-20T00:00:00", "2024-05-27T00:00:00", "2024-06-03T00:00:00", "2024-06-10T00:00:00", "2024-06-17T00:00:00", "2024-06-24T00:00:00", "2024-07-01T00:00:00", "2024-07-08T00:00:00", "2024-07-15T00:00:00", "2024-07-22T00:00:00", "2024-07-29T00:00:00", "2024-08-05T00:00:00", "2024-08-12T00:00:00", "2024-08-19T00:00:00"], "y": [3.2093862764241465, 3.336992466928681, 3.3786267276250554, 3.383002334881144, 3.3461256023206074, 3.3587079414379772, 3.2654186558922285, 3.171091355440647, 3.182677774923969, 3.1594860392607598, 3.1625708470047087, 3.093714674210798, 3.0773566152927336, 3.1987344692707196, 3.2604700524555885, 3.2267012650828675, 3.1421263279104146, 3.1121322775588314, 3.104477675993051, 3.2000758801702234, 3.2273932070183045, 3.192541954414075, 3.1598650789738008, 3.155059684895724, 3.195197324071253, 3.049060939285809, 3.145745918041151, 3.050221611664026, 3.035645087979539, 2.98470361282903, 2.9410624192464514, 2.869230738052955, 2.9373317210359033, 2.921072737651041, 2.8760895069803616, 2.9124387763323054, 2.820082253859893, 2.715727248887526, 2.828355633732829, 2.8059310041675283, 2.660936494874688, 2.641961474977388, 2.6184356646584237, 2.603473775535775, 2.6457314365120164, 2.6207411908773355, 2.571322791464078, 2.520568587060067, 2.5121313676682484, 2.404162213981263, 2.4489797535036213, 2.5027522253333974, 2.4726676813196273, 2.3874038646723674, 2.2639558153309074, 2.2507708769538333, 2.2093024662827196, 2.179135222249975, 2.23060732693164, 2.215280309219544, 2.2688018439881854, 2.1854898194205674, 2.301730314538986, 2.4754276553874575, 2.47526563132696, 2.6359986087984, 2.469879654933536, 2.413669078470134, 2.4334533180264257, 2.426763124103788, 2.4022948417057894, 2.4495477075022807, 2.4531968001198927, 2.5412484170647565, 2.564685328023417, 2.4102142654494356, 2.454758819276003, 2.3840381031775126, 2.4379193782806396, 2.574974020347366, 2.5733011645393313, 2.5058864290541725, 2.513218765660303, 2.402532492741525, 2.366765836902893, 2.4197818693425623, 2.3587933244806893, 2.3030107423679174, 2.356913120416966, 2.3309721921593405, 2.294007578984475, 2.3323124072088035, 2.395255573488837, 2.3926667521177962, 2.285890233627757, 2.448165815983092, 2.407526549385521, 2.343155804405052, 2.205927827963863, 2.264337476781142, 2.211726449599872, 2.149904358489355, 2.150143329005551, 2.231822470737654, 2.1991036813612133, 2.106908526586688, 2.1857231791724954, 2.0969245875102236, 2.065999264206137, 2.0366346031952216, 2.143534617335799, 2.200191866028813, 2.267070886229902, 2.2694480585152568, 2.2222222650958487, 2.235734269840089, 2.2934204866530563, 2.365845977004214, 2.2693840055150494, 2.172868999362355, 2.088105801093596, 2.003150056890549, 2.068621724020218, 2.0874471355056223, 2.1158159252739543, 2.0776750688013057, 2.1490632597804162, 2.05866454046706, 2.145495903173334, 2.2072200500061503, 2.2130209855617524, 2.18500576641316, 2.230312105096644, 2.37883435436553, 2.2071796795227336, 2.20104533468253, 2.2095151386479026, 2.249388673509027, 2.143071454990553, 2.096774116639168, 2.1055651587159154, 2.135607462018651, 2.0715835787889754, 2.139917616981538, 2.145999363647264, 2.1312394207941776, 2.169520147041114, 2.1914831372263697, 2.1267806702529604, 2.2505801924625475, 2.2551842305342773, 2.1905154854827376, 2.3577000010140194, 2.2438956383587216, 2.175110118133356, 2.2087578427169774, 2.1373434847125377, 2.190148327772523, 2.1554018858308783, 2.16081721963393, 2.1441702580887214, 2.2577152279288883, 2.3032654282855303, 2.3991394535682993, 2.289791779469493, 2.4369310185565127, 2.4602140585143726, 2.3919055499435755, 2.2563130566568086, 2.285786373860571, 2.384350384923986, 2.4096239362547864, 2.507331359435756, 2.4896920131950226, 2.5090208135823113, 2.5785646261972373, 2.6825668260466586, 2.749448472763155, 2.841869646904348, 2.923686644457744, 3.0331466683736577, 3.2893081880965322, 3.387992116645053, 3.3341642158685634, 3.311409197271097, 3.3430301781856655, 3.204216873480312, 3.2068310685951325, 3.1257442702501286, 3.3678303930230276, 3.1460390061140973, 3.1253228815951086, 3.1489304377938634, 3.119363392695824, 3.170478345406335, 3.243654815073546, 3.155538559924728, 3.047421657544212, 3.02455083267956, 3.0019855385951173, 3.0630179914651916, 3.1435583266743854, 3.2304476807991973, 3.1933418156700766, 3.345268461710352, 3.2066032635909707, 3.3116069194918123, 3.0772022116491473, 3.126704873198816, 3.0973782104796985, 3.1873601333016035, 3.2084772916272786, 3.337417221724896, 3.3563766206689047, 3.4826282015957566, 3.1430617300543546, 3.0840790395559123, 3.144690703900581, 3.179197337031666, 3.2610184818469885, 3.3169203979723156, 3.3129313142385524, 3.1469296990779405, 3.230006928700063, 3.3344654580376742, 3.355963024524766, 3.3988571166992188, 3.3778944437098564, 3.3924768721237704, 3.4602765223636553, 3.4347111744616456, 3.425120731482759, 3.4898929165036163, 3.4991805657941755, 3.423775543122026, 3.4362043801052105, 3.3191391888780872, 3.4315959352189536, 3.436543238114708, 3.504343669726999, 3.5687514346443074, 3.443163285815977, 3.458438905061168, 3.4436782201131186, 3.454588082083761, 3.3900522303032714, 3.533780655031968, 3.539780812377517, 3.7656359367556527, 3.8184370444274967, 3.8546658026575846, 3.803632316199659, 3.5916316525361762, 3.8436982436047487, 3.8721176639755774, 3.898045695257524, 3.9253315281654393, 3.863220410182903, 3.8966135368901678, 3.9306952656397347, 4.045496209094392, 3.9329129468932322, 3.7505568268569913, 3.640913989261133, 3.88025877416584, 3.841151306624097, 4.003581230789902, 3.952472308323012, 4.128479600207964, 4.0084567180899695, 4.237344078947633, 4.268631230786405, 4.324546701477821, 4.3487097711070035, 4.276773229437742, 4.136622517565848, 4.309694356054541, 4.434393604043463, 4.180042698053839, 3.9938852814464783, 3.898579532483414, 3.848238689143483, 4.110434790404273, 4.08530133673333, 4.0458015652211445, 4.003262340511362, 3.944166149484363, 4.009717344816497, 4.106315812886727, 4.275401664714408, 4.306249959128244, 4.27638358246581, 4.538579077581863, 4.808046991647239, 5.137614431864754, 5.117367389345217, 5.2726470526068105, 5.584260329489314, 5.547433865918581, 6.168381803487996, 5.766325724772072, 5.545872803093183, 5.858996335197897, 5.715629345349925, 5.643846936214232, 5.756163075176545, 5.744824632763556, 5.5140416820091875, 5.645872998654015, 5.687111653156162, 5.5453828120664435, 5.6363342350752035, 5.554662256762145, 5.565342358680256, 5.869422919883545, 5.753816460838918, 5.835595437715458, 5.778483547782291, 5.911076091664479, 5.771909028745921, 5.808600285805096, 5.668672623248517, 5.288722111758882, 4.897358347135204, 4.911504440123481, 4.940352928721482, 4.88134018116942, 4.962466432978015, 4.884552761791198, 4.57115172881027, 4.493073105644916, 4.170110656864987, 4.145483509432688, 3.9370575821559135, 4.067494205211465, 3.729495574597059, 3.7550561883476345, 3.945125633018451, 4.152393545315322, 4.197973621514098, 4.272307616013747, 4.610021777093915, 4.6733667560135705, 4.741327435362573, 4.9999999858547275, 5.149547537070352, 5.211705072766784, 5.178281773263256, 5.457654099445026, 5.369818374099285, 5.0355522427003425, 5.078613992848144, 5.069278683385679, 5.047250034087024, 5.22994667268874, 5.180566112048405, 5.328653236412591, 5.532403850928343, 5.430309461677796, 5.489106323278553, 5.380303209478205, 5.181412537301783, 5.064150498261049, 4.805145355117002, 5.037425163977161, 5.068350688486539, 4.651113132818442, 4.778748800864711, 4.8844679672047455, 4.887587692332027, 4.986510351831907, 4.8490717497108635, 4.643324470020713, 4.502487431694585, 4.1175376246469, 3.7747746831303957, 4.016800114391394, 3.622572688223089, 4.036052180493018, 3.8953927111625033, 3.70162626706809, 3.811606692911935, 3.6416150243755347, 3.6397869135063003, 3.664211634403831, 3.660876459537495, 3.488193430264267, 3.5880927813620254, 3.848540711773976, 3.9000885645278784, 4.008679889846631, 3.9625553902219175, 4.044435170158567, 3.8539369230681277, 3.9267074346477133, 4.143563343816304, 4.376787557066864, 4.303269863504213, 4.296755163432301, 4.462917064819239, 4.506483062814953, 4.243640457593229, 4.278642847487605, 4.056828234101746, 4.088808825530319, 4.047353108852933, 4.1296106532227554, 4.251636601648728, 4.179490061295244, 4.158617783606329, 3.9140177990633602, 3.8509350459497886, 3.9974099868101876, 3.9917491581311455, 3.964210897084465, 4.252544492862303, 4.240391582858284, 4.1354155956565455, 3.9161236262880625, 4.220702567602879, 3.7009063829115743, 3.4879373369475126, 3.2633608927906357, 2.5210428332249006, 2.777990144058055, 2.29735291236319, 2.572544661388562, 2.3083742049280307, 2.291891166783081, 1.9843374456864595, 1.9890957672489933, 1.8054630500646054, 1.7169373259279483, 1.560258141386505, 1.4642692746426982, 1.6431497789941132, 1.8090835328782169, 1.8032884513490601, 1.627945940800738, 1.576620329605974, 1.7753582442672435, 1.6303770224337748, 1.4114594245977288, 1.6208178681765093, 1.783780993596135, 1.7837169046083776, 1.8701611704027765, 1.9864572347308291, 2.2325816109267653, 2.346973326177281, 2.549573284739058, 2.266856561433129, 2.3676206536001736, 2.3479877701910286, 2.1785369676241584, 2.1935315199301275, 2.2450960542186498, 2.371841395401248, 2.518349248207997, 2.4000642863759687, 2.4380779385909905, 2.4148489452040445, 2.412299914489225, 2.575757578841146, 2.6416876492368204, 2.7435250784602863, 2.905567885258368, 2.992608353353982, 3.0203524041460876, 3.05433656977511, 2.8580760168190533, 2.8137128432779517, 2.7457390754359325, 2.7580285931396658, 2.6629784041746536, 2.6994654520090573, 2.700580781604385, 2.8647214848120406, 2.834952514079708, 2.6897086683132954, 2.659707879083234, 2.7084786749376475, 2.634559723685176, 2.7494009998631825, 2.7782739210888616, 2.8096866351338523, 2.9625939505264194, 3.0382580836893904, 2.976629904047926, 2.96876390722332, 3.065093805133095, 2.812557713586978, 2.712412301838758, 2.8260669997111543, 2.9938463982428103, 2.9227398255687027, 2.9983254915110145, 3.059010369313396, 3.038197554272731, 3.072812930181239, 3.179272936044963, 3.0871588577699476, 3.0902928195469337, 3.0452692360521882, 2.828150296088526, 2.5876033404641903, 2.544402675917151, 2.5954752137009267, 2.5548589410994103, 2.3125309265881837, 2.359426996163462, 2.2916005041808987, 2.462971663392147, 2.4051681175659216, 2.516947895157484, 2.46085863723498, 2.6816536783023173, 2.798510669750981, 2.7750539161198238, 2.6763518198539034, 2.6817144885241766, 2.72168291425242, 2.796589922214374, 2.7296103450273983, 2.7543901242163065, 2.788194535691061, 2.7987995743700944, 2.802038486091049, 2.796659720417491, 2.862805815206897, 2.749981710759557, 2.8225863221215546, 2.847883335049118, 2.8346399076298194, 2.753431544502571, 2.8426069561402434, 2.9653975830951977, 3.0124763901613774, 3.081159315247467, 3.123988411308671, 3.122031449139184, 3.236915613850619, 3.2065624091982574, 3.256134953597437, 3.3901950112328585, 3.3318626781269196, 3.226120189381342, 3.1490026436148133, 3.1312143081932753, 2.9511081052841206, 3.0577725289297324, 3.091298134787677, 2.9770325624124125, 3.050766805400754, 2.86339435989233, 2.9257583198164334, 2.6770566606889346, 2.6579266045263923, 2.6617114233623806, 2.7299839771595513, 2.7204972792814197, 2.6788611566742464, 2.650791646273034, 2.6667101450261343, 2.7314227090522234, 2.8955000172115595, 2.8565405476689656, 2.770788825333958, 2.7505775765464615, 2.747650827826063, 2.494541359942311, 2.3040455920038543, 2.153983462440618, 2.2847110154901586, 2.1919197209490955, 2.1479745205103966, 2.163236095749311, 1.9981679843582267, 1.9408787543457038, 1.9984704779353337, 2.0246164065474774, 1.9686870216825427, 2.1200320732117803, 2.029852369763853, 1.9367831570285183, 1.972516986561861, 1.9388904701669716, 2.0449341674088477, 2.0708779952232366, 2.0832288371800347, 2.158347434583562, 2.191531390362729, 2.1230122183709588, 2.2276741383070697, 2.248602540633853, 2.24246448186095, 2.2423614820423747, 2.239598341542425, 2.148448781505395, 2.175933753275684, 2.279669846310209, 2.251738796469072, 2.3400785699059186, 2.291704185237049, 2.29078042885633, 2.32821620605019, 2.183714037642847, 2.2507765386594776, 2.2956730368045664, 2.264242772341792, 2.303927773374565, 2.179881954052292, 2.1978581380076117, 2.0425794154764216, 2.064964471759734, 2.080516281784423, 2.1104725841361445, 2.1764889364120634, 2.1569310409084803, 2.199359146428542, 2.179146867604429, 2.117559840878532, 2.0990037680562974, 2.1047107628946335, 2.1171811099161455, 2.088872807570318, 2.050338386704495, 2.1041726036895096, 2.176234562137429, 2.142213425546322, 2.1303145242600534, 2.1286405404926367, 2.112912372246266, 2.1177733122015465, 2.0828218345143545, 2.080520931156018, 1.993584919713533, 2.0145248404943454, 2.014217904310414, 2.1213980130165524, 2.1405516655792094, 2.161221757474125, 2.1430294031930757, 2.1617620715008643, 2.2319640747216827, 2.192469820918807, 2.1721215010092547, 2.197234876287002, 2.261590993119195, 2.253001191321255, 2.319119737292373, 2.244975780069188, 2.2148354579053904, 2.214575640545896, 2.202825681006703, 2.1504171966044967, 2.1287935019949327, 2.120096396095687, 2.231179198608805, 2.2586539642820336, 2.1911117200560537, 2.2628558471827795, 2.335745985902103, 2.4331453145132738, 2.373449650751109, 2.361769418348888, 2.3615329229392095, 2.30988239541444, 2.397414043374089, 2.4926456891366606, 2.5393683417509747, 2.4812176544851425, 2.4356297660559787, 2.352273649415287, 2.4183581348813643, 2.519993898983262, 2.4527959215147574, 2.40166233098898, 2.3100064186155786, 2.349462761753108, 2.4510852624007864, 2.4971858198155155, 2.484683167052362, 2.514698075423555, 2.5749212183786865, 2.5053255642640653, 2.414596280475042, 2.508186739646079, 2.532502935757754, 2.464848890639968, 2.5936495452831796, 2.583160119861902, 2.6574818234628004, 2.7126326216277543, 2.7781927785395704, 2.853383848592156, 2.7503228615665116, 2.716772452537294, 2.699129277410428, 2.612890392105869, 2.596968031427421, 2.5892502034505127, 2.517437374528562, 2.519827709153632, 2.450809791449517, 2.358941901712004, 2.184916614535237, 2.2410179326634205, 2.3643529910433343, 2.335021559889953, 2.325017136776703, 2.3732023438556604, 2.4040902674308473, 2.365653394104845, 2.408529471923829, 2.444135076168644, 2.46206265629154, 2.5176621543435855, 2.438303676084991, 2.3792887268009375, 2.368561147553261, 2.3874146546081128, 2.4727355684146124, 2.4371728490517044, 2.4243814109177597, 2.4764024699191896, 2.4787415846644354, 2.41882483789475, 2.3740896003462884, 2.501955218854431, 2.4381706886078347, 2.494864805301813, 2.522156221250973, 2.54134773208174, 2.4948522401601987, 2.5117430334192323, 2.483210536322566, 2.4234836543540252, 2.4780703697653874, 2.613782061764077, 2.609866683698864, 2.5805907913401156, 2.5344468357667544, 2.4336283304334017, 2.458196770095983, 2.411211180804014, 2.4316413855710013, 2.372207718628707, 2.39333897376744, 2.2948190177741803, 2.0784712922899917, 1.9630975959769115, 1.977317259560631, 2.109571176035498, 2.13086026763605, 2.1674693161391967, 2.239815384126369, 2.2583970480527737, 2.323876447850737, 2.342167438389422, 2.3128854662723546, 2.2882836628142367, 2.2833305823557626, 2.3139911045282715, 2.3417292442157347, 2.495955690113521, 2.46488945795637, 2.406364652586197, 2.3528188936008725, 2.320077324752285, 2.3308783780093445, 2.290040695711695, 2.157303377136408, 2.255094147728284, 2.265262275754842, 2.1987562817789303, 2.212846619732118, 2.1937178143695744, 2.1622979395074666, 2.140115250998927, 2.1241800971840465, 1.989479105182799, 2.0708675054714023, 2.0698733000325165, 2.2310828211948133, 2.1061419258466545, 1.999563779714428, 2.048632494909085, 2.0902879471143034, 2.0336263218139012, 2.0201150227749842, 2.026719165589796, 2.0609598668023854, 2.007772000608996, 1.9082124249634465, 1.941393685733075, 1.912401919543001, 1.9574705100422638, 1.9724254566903394, 1.9601746384210772, 2.0046212070908997, 1.8368554440654907, 1.7769124821354039, 1.8214546832371485, 1.848799575550358, 1.8142166334658203, 1.6358647327471871, 1.6868497807714855, 1.7363501768608418, 1.788329683271811, 1.7784223445560052, 1.8180729931857547, 1.821270588183706, 1.768123138345261, 1.679275597125232, 1.74519503123529, 1.8466673260967403, 1.7677630547514538, 1.6637017420299058, 1.6314175704490803, 1.6436442122088635, 1.7416378291111234, 1.70308848490865, 1.5953353715571383, 1.5880077443224319, 1.5999999913302336, 1.6570659049006753, 1.561624673745803, 1.6814926368894552, 1.6884589288770426, 1.6460340936772464, 1.6110446550554638, 1.6024105333297267, 1.6163086034706617, 1.570185431467057, 1.5668255814197733, 1.570182739059261, 1.647648878999046, 1.6396202471586152, 1.6766921654271567, 1.7267195603597072, 1.6786369247116162, 1.6458644981949813, 1.715797669557626, 1.734826849747308, 2.0486309813829555, 2.0388911763744804, 2.263622615720216, 2.2253425783902614, 2.278764795852831, 2.2557914195127338, 2.184380274318484, 2.171739080677862, 2.165713844131454, 2.242533189173335, 2.1730465804957535, 2.2557024844946247, 2.141567444674148, 2.237524327038465, 2.1860859250465974, 2.1322301015998115, 2.1925744889756213, 2.154160102116848, 2.1788095597736548, 2.101826799669231, 2.1217830570219935, 2.1087458167586774, 1.9982113077984027, 1.9702500613717633, 2.050785854165841, 2.0570704741372006, 2.0534986036004867, 2.054761700386801, 2.0191701075381117, 2.014019395834676, 2.0859281895524364, 2.0434609250398723, 2.08804331196876, 2.1753848536828793, 2.1822770982640787, 2.1881624561232056, 2.164553781773267, 2.2630873797594453, 2.292775871025439, 2.2629495572166336, 2.2870810707433424, 2.3466150811377995, 2.338995884930966, 2.244799487835231, 2.217509784276505, 2.2612695261287845, 2.2922356613343737, 2.3706353889604124, 2.3960814400906294, 2.4671206563457972, 2.438707054718583, 2.4559811269441614, 2.4123702535261424, 2.363404757369484, 2.4605580240290776, 2.397169146020926, 2.3731127847580327, 2.4794704409056307, 2.5203856742010995, 2.5105258248833544, 2.4289933337597116, 2.3998799978197805, 2.377806139545914, 2.354616708378841, 2.381720175684878, 2.303327963919023, 2.396541640212986, 2.4156753699745255, 2.3472864490793257, 2.356321747981026, 2.3587278425820855, 2.2118875145135473, 2.2822799682237, 2.2929648208103344, 2.2810083306540436, 2.342709747970502, 2.307051345274336, 2.3352633008359587, 2.346474623662039, 2.3647498323685596, 2.353256961065632, 2.387627376649406, 2.5367845657245818, 2.4643025989240264, 2.3926936801284966, 2.3583472069454134, 2.2426850468422788, 2.2345918324314122, 2.2334282647404744, 2.2799770655128997, 2.2677483902743476, 2.2562134682332444, 2.2307692615721413, 2.237420021964155, 2.206948114360545, 2.1812164398677396, 2.1987448177577065, 2.3712590245880563, 2.3390683515503086, 2.290209822966536, 2.292915227660486, 2.259038420926973, 2.2263691343110183, 2.277195481112622, 2.2268733571885133, 2.296854348514728, 2.2690417237402865, 2.2762662066178683, 2.2116617141895274, 2.2316086937943678, 2.136305497484211, 2.0931321585405205, 2.067124103517856, 2.074042547719193, 2.139233460237834, 2.1103744909463895, 2.1072215384019994, 2.1389967627485285, 2.1261664983272173, 2.227655788775188, 2.262804687021103, 2.2313030481154676, 2.230373307116067, 2.1622446008363063, 2.2706884058150596, 2.2415529052856806, 2.282659348296232, 2.3016746232750336, 2.3114639055008213, 2.208411628765099, 2.166135328302882, 2.156139589282006, 2.1095090049927996, 2.0263438577919244, 1.9613034401789915, 1.9658981230493549, 1.9381178801604697, 1.91920265388203, 1.9034152280884786, 1.9090005687602865, 1.9258297444502377, 1.8868523665956936, 1.7743498205171184, 1.7265803506475086, 1.7123966374673134, 1.6546574500753926, 1.6674346870380226, 1.7364892166290211, 1.798242638926518, 1.7183041400303471, 1.7230338114556365, 1.6966539060961405, 1.7660349905645991, 1.7638758810151538, 1.7782594808621102, 1.7579575748595382, 1.8326147155368362, 1.795815377071464, 1.8095141017870024, 1.8026746751885172, 1.8579946617976304, 1.8843182306546553, 1.9061505403082857, 1.8737612881066523, 1.8064163111535487, 1.8096308455612649, 1.8280086294675124, 1.709311914118239, 1.5920146200385175, 1.6313910844623696, 1.6462374822875794, 1.591876486307765, 1.6277731881307165, 1.5399807750953192, 1.6322491632984646, 1.4760781812539319, 1.3529158160432966, 1.352757608006115, 1.3117728246865101, 1.393263057899717, 1.3623440960783664, 1.3682502460676718, 1.40856185843749, 1.3308428629839084, 1.3887927786218328, 1.397029217519047, 1.5254742848614455, 1.5043659057866818, 1.497794818127892, 1.5074752786095624, 1.5322309438423192, 1.6054944735383299, 1.5968036109687493, 1.5190006300070993, 1.456592570028729, 1.3882393709297876, 1.4744449904977999, 1.5059961270233553, 1.5244833160836369, 1.5835022240840746, 1.565692973186864, 1.5949491097765496, 1.6003661127921307, 1.5674665645249093, 1.6043240218523984, 1.6124789449106354, 1.6432702227972533, 1.6211249105230603, 1.6146233146639346, 1.6839900293274304, 1.7587845720032653, 1.907794964145828, 1.9140475473801226, 1.9154255005443335, 1.924219164605836, 1.8937176959126545, 1.856214666041194, 2.0028897606094715, 1.9696057669279214, 1.9590990191748476, 1.934444769950034, 2.009497978852965, 2.087176162018454, 2.30318731846355, 2.369365269773558, 2.406949289274721, 2.410875360922052, 2.3679222536405713, 2.3545202106992003, 2.3188531623175277, 2.3234668746779836, 2.349634529701306, 2.4440067164604087, 2.5343744513121607, 2.6028069780740757, 2.5376788575968363, 2.395694532987215, 2.4612351566670605, 2.401047617416613, 2.4190369833249252, 2.354488289580827, 2.419790524290144, 2.4021093478599242, 2.4046961115210093, 2.387710102467078, 2.4461528827303036, 2.4718637766747644, 2.4690340865742075, 2.4712709579714214, 2.3214486393615275, 2.3761423785816658, 2.362499292023533, 2.4854716220966093, 2.4268320729902864, 2.450420085890145, 2.3867387831312863, 2.438649361325474, 2.678814133541831, 2.5207463229675207, 2.454010208737496, 2.3942963656999092, 2.3852990350929764, 2.3814284306559235, 2.3981962390474836, 2.3942199754126277, 2.4022659339960373, 2.378866710953641, 2.42215117170519, 2.4377564562003036, 2.4501947011294867, 2.428571415971273, 2.4634237847913374, 2.410779267432463, 2.481733678957101, 2.447305516592557, 2.3804381317095356, 2.370527496459217, 2.5082692799628084, 2.3281385429740826, 2.45332452081925, 2.3984029855618654, 2.4399457793382684, 2.430984677349538, 2.3933228216544022, 2.373122724457024, 2.30189067644137, 2.265309372929616, 2.312991022489803, 2.324356620607626, 2.3248526912438146, 2.4263031877192534, 2.298156622097577, 2.188657647167182, 2.0585819821862894, 2.0117849290802643, 2.0291854041080737, 1.8999646880577963, 1.938799178209371, 2.033013818703327, 2.004061100101442, 2.043811947155214, 2.1077478272814716, 2.137837671575026, 2.001111164969334, 2.0865867565141034, 2.1301670232284105, 2.0491703125275906, 2.070500398046581, 2.0067626607659914, 2.101784804818361, 2.133858268871804, 2.113930307220218, 2.220926726701905, 2.237259329107853, 2.0731776464796696, 2.0683852109084335, 2.1426582756724044, 2.1556086870295963, 2.101396581980103, 2.1176011590833967, 2.091278855102931, 2.0966098371979984, 2.1932339071000646, 2.2054089578785825, 2.1901897908240415, 2.1810081855878924, 2.1620678800827626, 2.2353834976215063, 2.1865324065357017, 2.2070683424969206, 2.1726636958301513, 1.9867499104287878, 2.069270009006389, 2.0845605977117985, 2.0010934808058862, 2.0514934454422753, 2.010608749216209, 1.9446258645471581, 1.919054212554218, 1.844874680500878, 1.8812857824280314, 1.8895633491934951, 1.9066277603371027, 1.9309958865656833, 1.98350693335244, 1.9832213238999756, 1.9473218388420863, 1.9556640304418476, 2.0009183103851815, 1.9360585851260035, 1.9980616234096662, 1.9823675381562562, 1.9418160400446012, 1.9611897193282397, 1.967453319290872, 1.9654087555587048, 1.9185258463712305, 1.9563861676618683, 1.9037601948347787, 2.01612473339846, 1.980384730050321, 1.8574244465906289, 1.7866330669927415, 1.8274163268415775, 1.8450916337212695, 1.8542377903946718, 1.8861021388593557, 1.8961642494062623, 1.8874939255873286, 1.9116248010317485, 1.9222205914563706, 1.8936851683227347, 1.8815458989264986, 1.8578633309711994, 1.8273318506678708, 1.8687391763494658, 1.909212620779577, 1.8768725056552171, 1.8217763158033662, 1.9080288635982139, 1.9042480211776964, 1.847237624930656, 1.7832552172575231, 1.9063181280549408, 1.8516286875841246, 1.805492992269724, 1.8228920904608377, 1.8069350054709385, 1.8770847391388261, 1.9569126570803028, 1.989343260723179, 1.9826384256972722, 2.096426607487715, 2.0486602701430408, 1.9845883711403258, 1.9497224776238844, 1.952046113444762, 1.9400794972799251, 1.8861967438948324, 1.9503873537992718, 1.9049294842306799, 1.7612188948740966, 1.7245797549977022, 1.6836377923218442, 1.6364458119585235, 1.651925036380235, 1.641722887742609]}, {"name": "US 10-Year Constant Maturity %", "type": "scatter", "x": ["2000-08-28T00:00:00", "2000-09-04T00:00:00", "2000-09-11T00:00:00", "2000-09-18T00:00:00", "2000-09-25T00:00:00", "2000-10-02T00:00:00", "2000-10-09T00:00:00", "2000-10-16T00:00:00", "2000-10-23T00:00:00", "2000-10-30T00:00:00", "2000-11-06T00:00:00", "2000-11-13T00:00:00", "2000-11-20T00:00:00", "2000-11-27T00:00:00", "2000-12-04T00:00:00", "2000-12-11T00:00:00", "2000-12-18T00:00:00", "2000-12-25T00:00:00", "2001-01-01T00:00:00", "2001-01-08T00:00:00", "2001-01-15T00:00:00", "2001-01-22T00:00:00", "2001-01-29T00:00:00", "2001-02-05T00:00:00", "2001-02-12T00:00:00", "2001-02-19T00:00:00", "2001-02-26T00:00:00", "2001-03-05T00:00:00", "2001-03-12T00:00:00", "2001-03-19T00:00:00", "2001-03-26T00:00:00", "2001-04-02T00:00:00", "2001-04-09T00:00:00", "2001-04-16T00:00:00", "2001-04-23T00:00:00", "2001-04-30T00:00:00", "2001-05-07T00:00:00", "2001-05-14T00:00:00", "2001-05-21T00:00:00", "2001-05-28T00:00:00", "2001-06-04T00:00:00", "2001-06-11T00:00:00", "2001-06-18T00:00:00", "2001-06-25T00:00:00", "2001-07-02T00:00:00", "2001-07-09T00:00:00", "2001-07-16T00:00:00", "2001-07-23T00:00:00", "2001-07-30T00:00:00", "2001-08-06T00:00:00", "2001-08-13T00:00:00", "2001-08-20T00:00:00", "2001-08-27T00:00:00", "2001-09-03T00:00:00", "2001-09-10T00:00:00", "2001-09-17T00:00:00", "2001-09-24T00:00:00", "2001-10-01T00:00:00", "2001-10-08T00:00:00", "2001-10-15T00:00:00", "2001-10-22T00:00:00", "2001-10-29T00:00:00", "2001-11-05T00:00:00", "2001-11-12T00:00:00", "2001-11-19T00:00:00", "2001-11-26T00:00:00", "2001-12-03T00:00:00", "2001-12-10T00:00:00", "2001-12-17T00:00:00", "2001-12-24T00:00:00", "2001-12-31T00:00:00", "2002-01-07T00:00:00", "2002-01-14T00:00:00", "2002-01-21T00:00:00", "2002-01-28T00:00:00", "2002-02-04T00:00:00", "2002-02-11T00:00:00", "2002-02-18T00:00:00", "2002-02-25T00:00:00", "2002-03-04T00:00:00", "2002-03-11T00:00:00", "2002-03-18T00:00:00", "2002-03-25T00:00:00", "2002-04-01T00:00:00", "2002-04-08T00:00:00", "2002-04-15T00:00:00", "2002-04-22T00:00:00", "2002-04-29T00:00:00", "2002-05-06T00:00:00", "2002-05-13T00:00:00", "2002-05-20T00:00:00", "2002-05-27T00:00:00", "2002-06-03T00:00:00", "2002-06-10T00:00:00", "2002-06-17T00:00:00", "2002-06-24T00:00:00", "2002-07-01T00:00:00", "2002-07-08T00:00:00", "2002-07-15T00:00:00", "2002-07-22T00:00:00", "2002-07-29T00:00:00", "2002-08-05T00:00:00", "2002-08-12T00:00:00", "2002-08-19T00:00:00", "2002-08-26T00:00:00", "2002-09-02T00:00:00", "2002-09-09T00:00:00", "2002-09-16T00:00:00", "2002-09-23T00:00:00", "2002-09-30T00:00:00", "2002-10-07T00:00:00", "2002-10-14T00:00:00", "2002-10-21T00:00:00", "2002-10-28T00:00:00", "2002-11-04T00:00:00", "2002-11-11T00:00:00", "2002-11-18T00:00:00", "2002-11-25T00:00:00", "2002-12-02T00:00:00", "2002-12-09T00:00:00", "2002-12-16T00:00:00", "2002-12-23T00:00:00", "2002-12-30T00:00:00", "2003-01-06T00:00:00", "2003-01-13T00:00:00", "2003-01-20T00:00:00", "2003-01-27T00:00:00", "2003-02-03T00:00:00", "2003-02-10T00:00:00", "2003-02-17T00:00:00", "2003-02-24T00:00:00", "2003-03-03T00:00:00", "2003-03-10T00:00:00", "2003-03-17T00:00:00", "2003-03-24T00:00:00", "2003-03-31T00:00:00", "2003-04-07T00:00:00", "2003-04-14T00:00:00", "2003-04-21T00:00:00", "2003-04-28T00:00:00", "2003-05-05T00:00:00", "2003-05-12T00:00:00", "2003-05-19T00:00:00", "2003-05-26T00:00:00", "2003-06-02T00:00:00", "2003-06-09T00:00:00", "2003-06-16T00:00:00", "2003-06-23T00:00:00", "2003-06-30T00:00:00", "2003-07-07T00:00:00", "2003-07-14T00:00:00", "2003-07-21T00:00:00", "2003-07-28T00:00:00", "2003-08-04T00:00:00", "2003-08-11T00:00:00", "2003-08-18T00:00:00", "2003-08-25T00:00:00", "2003-09-01T00:00:00", "2003-09-08T00:00:00", "2003-09-15T00:00:00", "2003-09-22T00:00:00", "2003-09-29T00:00:00", "2003-10-06T00:00:00", "2003-10-13T00:00:00", "2003-10-20T00:00:00", "2003-10-27T00:00:00", "2003-11-03T00:00:00", "2003-11-10T00:00:00", "2003-11-17T00:00:00", "2003-11-24T00:00:00", "2003-12-01T00:00:00", "2003-12-08T00:00:00", "2003-12-15T00:00:00", "2003-12-22T00:00:00", "2003-12-29T00:00:00", "2004-01-05T00:00:00", "2004-01-12T00:00:00", "2004-01-19T00:00:00", "2004-01-26T00:00:00", "2004-02-02T00:00:00", "2004-02-09T00:00:00", "2004-02-16T00:00:00", "2004-02-23T00:00:00", "2004-03-01T00:00:00", "2004-03-08T00:00:00", "2004-03-15T00:00:00", "2004-03-22T00:00:00", "2004-03-29T00:00:00", "2004-04-05T00:00:00", "2004-04-12T00:00:00", "2004-04-19T00:00:00", "2004-04-26T00:00:00", "2004-05-03T00:00:00", "2004-05-10T00:00:00", "2004-05-17T00:00:00", "2004-05-24T00:00:00", "2004-05-31T00:00:00", "2004-06-07T00:00:00", "2004-06-14T00:00:00", "2004-06-21T00:00:00", "2004-06-28T00:00:00", "2004-07-05T00:00:00", "2004-07-12T00:00:00", "2004-07-19T00:00:00", "2004-07-26T00:00:00", "2004-08-02T00:00:00", "2004-08-09T00:00:00", "2004-08-16T00:00:00", "2004-08-23T00:00:00", "2004-08-30T00:00:00", "2004-09-06T00:00:00", "2004-09-13T00:00:00", "2004-09-20T00:00:00", "2004-09-27T00:00:00", "2004-10-04T00:00:00", "2004-10-11T00:00:00", "2004-10-18T00:00:00", "2004-10-25T00:00:00", "2004-11-01T00:00:00", "2004-11-08T00:00:00", "2004-11-15T00:00:00", "2004-11-22T00:00:00", "2004-11-29T00:00:00", "2004-12-06T00:00:00", "2004-12-13T00:00:00", "2004-12-20T00:00:00", "2004-12-27T00:00:00", "2005-01-03T00:00:00", "2005-01-10T00:00:00", "2005-01-17T00:00:00", "2005-01-24T00:00:00", "2005-01-31T00:00:00", "2005-02-07T00:00:00", "2005-02-14T00:00:00", "2005-02-21T00:00:00", "2005-02-28T00:00:00", "2005-03-07T00:00:00", "2005-03-14T00:00:00", "2005-03-21T00:00:00", "2005-03-28T00:00:00", "2005-04-04T00:00:00", "2005-04-11T00:00:00", "2005-04-18T00:00:00", "2005-04-25T00:00:00", "2005-05-02T00:00:00", "2005-05-09T00:00:00", "2005-05-16T00:00:00", "2005-05-23T00:00:00", "2005-05-30T00:00:00", "2005-06-06T00:00:00", "2005-06-13T00:00:00", "2005-06-20T00:00:00", "2005-06-27T00:00:00", "2005-07-04T00:00:00", "2005-07-11T00:00:00", "2005-07-18T00:00:00", "2005-07-25T00:00:00", "2005-08-01T00:00:00", "2005-08-08T00:00:00", "2005-08-15T00:00:00", "2005-08-22T00:00:00", "2005-08-29T00:00:00", "2005-09-05T00:00:00", "2005-09-12T00:00:00", "2005-09-19T00:00:00", "2005-09-26T00:00:00", "2005-10-03T00:00:00", "2005-10-10T00:00:00", "2005-10-17T00:00:00", "2005-10-24T00:00:00", "2005-10-31T00:00:00", "2005-11-07T00:00:00", "2005-11-14T00:00:00", "2005-11-21T00:00:00", "2005-11-28T00:00:00", "2005-12-05T00:00:00", "2005-12-12T00:00:00", "2005-12-19T00:00:00", "2005-12-26T00:00:00", "2006-01-02T00:00:00", "2006-01-09T00:00:00", "2006-01-16T00:00:00", "2006-01-23T00:00:00", "2006-01-30T00:00:00", "2006-02-06T00:00:00", "2006-02-13T00:00:00", "2006-02-20T00:00:00", "2006-02-27T00:00:00", "2006-03-06T00:00:00", "2006-03-13T00:00:00", "2006-03-20T00:00:00", "2006-03-27T00:00:00", "2006-04-03T00:00:00", "2006-04-10T00:00:00", "2006-04-17T00:00:00", "2006-04-24T00:00:00", "2006-05-01T00:00:00", "2006-05-08T00:00:00", "2006-05-15T00:00:00", "2006-05-22T00:00:00", "2006-05-29T00:00:00", "2006-06-05T00:00:00", "2006-06-12T00:00:00", "2006-06-19T00:00:00", "2006-06-26T00:00:00", "2006-07-03T00:00:00", "2006-07-10T00:00:00", "2006-07-17T00:00:00", "2006-07-24T00:00:00", "2006-07-31T00:00:00", "2006-08-07T00:00:00", "2006-08-14T00:00:00", "2006-08-21T00:00:00", "2006-08-28T00:00:00", "2006-09-04T00:00:00", "2006-09-11T00:00:00", "2006-09-18T00:00:00", "2006-09-25T00:00:00", "2006-10-02T00:00:00", "2006-10-09T00:00:00", "2006-10-16T00:00:00", "2006-10-23T00:00:00", "2006-10-30T00:00:00", "2006-11-06T00:00:00", "2006-11-13T00:00:00", "2006-11-20T00:00:00", "2006-11-27T00:00:00", "2006-12-04T00:00:00", "2006-12-11T00:00:00", "2006-12-18T00:00:00", "2006-12-25T00:00:00", "2007-01-01T00:00:00", "2007-01-08T00:00:00", "2007-01-15T00:00:00", "2007-01-22T00:00:00", "2007-01-29T00:00:00", "2007-02-05T00:00:00", "2007-02-12T00:00:00", "2007-02-19T00:00:00", "2007-02-26T00:00:00", "2007-03-05T00:00:00", "2007-03-12T00:00:00", "2007-03-19T00:00:00", "2007-03-26T00:00:00", "2007-04-02T00:00:00", "2007-04-09T00:00:00", "2007-04-16T00:00:00", "2007-04-23T00:00:00", "2007-04-30T00:00:00", "2007-05-07T00:00:00", "2007-05-14T00:00:00", "2007-05-21T00:00:00", "2007-05-28T00:00:00", "2007-06-04T00:00:00", "2007-06-11T00:00:00", "2007-06-18T00:00:00", "2007-06-25T00:00:00", "2007-07-02T00:00:00", "2007-07-09T00:00:00", "2007-07-16T00:00:00", "2007-07-23T00:00:00", "2007-07-30T00:00:00", "2007-08-06T00:00:00", "2007-08-13T00:00:00", "2007-08-20T00:00:00", "2007-08-27T00:00:00", "2007-09-03T00:00:00", "2007-09-10T00:00:00", "2007-09-17T00:00:00", "2007-09-24T00:00:00", "2007-10-01T00:00:00", "2007-10-08T00:00:00", "2007-10-15T00:00:00", "2007-10-22T00:00:00", "2007-10-29T00:00:00", "2007-11-05T00:00:00", "2007-11-12T00:00:00", "2007-11-19T00:00:00", "2007-11-26T00:00:00", "2007-12-03T00:00:00", "2007-12-10T00:00:00", "2007-12-17T00:00:00", "2007-12-24T00:00:00", "2007-12-31T00:00:00", "2008-01-07T00:00:00", "2008-01-14T00:00:00", "2008-01-21T00:00:00", "2008-01-28T00:00:00", "2008-02-04T00:00:00", "2008-02-11T00:00:00", "2008-02-18T00:00:00", "2008-02-25T00:00:00", "2008-03-03T00:00:00", "2008-03-10T00:00:00", "2008-03-17T00:00:00", "2008-03-24T00:00:00", "2008-03-31T00:00:00", "2008-04-07T00:00:00", "2008-04-14T00:00:00", "2008-04-21T00:00:00", "2008-04-28T00:00:00", "2008-05-05T00:00:00", "2008-05-12T00:00:00", "2008-05-19T00:00:00", "2008-05-26T00:00:00", "2008-06-02T00:00:00", "2008-06-09T00:00:00", "2008-06-16T00:00:00", "2008-06-23T00:00:00", "2008-06-30T00:00:00", "2008-07-07T00:00:00", "2008-07-14T00:00:00", "2008-07-21T00:00:00", "2008-07-28T00:00:00", "2008-08-04T00:00:00", "2008-08-11T00:00:00", "2008-08-18T00:00:00", "2008-08-25T00:00:00", "2008-09-01T00:00:00", "2008-09-08T00:00:00", "2008-09-15T00:00:00", "2008-09-22T00:00:00", "2008-09-29T00:00:00", "2008-10-06T00:00:00", "2008-10-13T00:00:00", "2008-10-20T00:00:00", "2008-10-27T00:00:00", "2008-11-03T00:00:00", "2008-11-10T00:00:00", "2008-11-17T00:00:00", "2008-11-24T00:00:00", "2008-12-01T00:00:00", "2008-12-08T00:00:00", "2008-12-15T00:00:00", "2008-12-22T00:00:00", "2008-12-29T00:00:00", "2009-01-05T00:00:00", "2009-01-12T00:00:00", "2009-01-19T00:00:00", "2009-01-26T00:00:00", "2009-02-02T00:00:00", "2009-02-09T00:00:00", "2009-02-16T00:00:00", "2009-02-23T00:00:00", "2009-03-02T00:00:00", "2009-03-09T00:00:00", "2009-03-16T00:00:00", "2009-03-23T00:00:00", "2009-03-30T00:00:00", "2009-04-06T00:00:00", "2009-04-13T00:00:00", "2009-04-20T00:00:00", "2009-04-27T00:00:00", "2009-05-04T00:00:00", "2009-05-11T00:00:00", "2009-05-18T00:00:00", "2009-05-25T00:00:00", "2009-06-01T00:00:00", "2009-06-08T00:00:00", "2009-06-15T00:00:00", "2009-06-22T00:00:00", "2009-06-29T00:00:00", "2009-07-06T00:00:00", "2009-07-13T00:00:00", "2009-07-20T00:00:00", "2009-07-27T00:00:00", "2009-08-03T00:00:00", "2009-08-10T00:00:00", "2009-08-17T00:00:00", "2009-08-24T00:00:00", "2009-08-31T00:00:00", "2009-09-07T00:00:00", "2009-09-14T00:00:00", "2009-09-21T00:00:00", "2009-09-28T00:00:00", "2009-10-05T00:00:00", "2009-10-12T00:00:00", "2009-10-19T00:00:00", "2009-10-26T00:00:00", "2009-11-02T00:00:00", "2009-11-09T00:00:00", "2009-11-16T00:00:00", "2009-11-23T00:00:00", "2009-11-30T00:00:00", "2009-12-07T00:00:00", "2009-12-14T00:00:00", "2009-12-21T00:00:00", "2009-12-28T00:00:00", "2010-01-04T00:00:00", "2010-01-11T00:00:00", "2010-01-18T00:00:00", "2010-01-25T00:00:00", "2010-02-01T00:00:00", "2010-02-08T00:00:00", "2010-02-15T00:00:00", "2010-02-22T00:00:00", "2010-03-01T00:00:00", "2010-03-08T00:00:00", "2010-03-15T00:00:00", "2010-03-22T00:00:00", "2010-03-29T00:00:00", "2010-04-05T00:00:00", "2010-04-12T00:00:00", "2010-04-19T00:00:00", "2010-04-26T00:00:00", "2010-05-03T00:00:00", "2010-05-10T00:00:00", "2010-05-17T00:00:00", "2010-05-24T00:00:00", "2010-05-31T00:00:00", "2010-06-07T00:00:00", "2010-06-14T00:00:00", "2010-06-21T00:00:00", "2010-06-28T00:00:00", "2010-07-05T00:00:00", "2010-07-12T00:00:00", "2010-07-19T00:00:00", "2010-07-26T00:00:00", "2010-08-02T00:00:00", "2010-08-09T00:00:00", "2010-08-16T00:00:00", "2010-08-23T00:00:00", "2010-08-30T00:00:00", "2010-09-06T00:00:00", "2010-09-13T00:00:00", "2010-09-20T00:00:00", "2010-09-27T00:00:00", "2010-10-04T00:00:00", "2010-10-11T00:00:00", "2010-10-18T00:00:00", "2010-10-25T00:00:00", "2010-11-01T00:00:00", "2010-11-08T00:00:00", "2010-11-15T00:00:00", "2010-11-22T00:00:00", "2010-11-29T00:00:00", "2010-12-06T00:00:00", "2010-12-13T00:00:00", "2010-12-20T00:00:00", "2010-12-27T00:00:00", "2011-01-03T00:00:00", "2011-01-10T00:00:00", "2011-01-17T00:00:00", "2011-01-24T00:00:00", "2011-01-31T00:00:00", "2011-02-07T00:00:00", "2011-02-14T00:00:00", "2011-02-21T00:00:00", "2011-02-28T00:00:00", "2011-03-07T00:00:00", "2011-03-14T00:00:00", "2011-03-21T00:00:00", "2011-03-28T00:00:00", "2011-04-04T00:00:00", "2011-04-11T00:00:00", "2011-04-18T00:00:00", "2011-04-25T00:00:00", "2011-05-02T00:00:00", "2011-05-09T00:00:00", "2011-05-16T00:00:00", "2011-05-23T00:00:00", "2011-05-30T00:00:00", "2011-06-06T00:00:00", "2011-06-13T00:00:00", "2011-06-20T00:00:00", "2011-06-27T00:00:00", "2011-07-04T00:00:00", "2011-07-11T00:00:00", "2011-07-18T00:00:00", "2011-07-25T00:00:00", "2011-08-01T00:00:00", "2011-08-08T00:00:00", "2011-08-15T00:00:00", "2011-08-22T00:00:00", "2011-08-29T00:00:00", "2011-09-05T00:00:00", "2011-09-12T00:00:00", "2011-09-19T00:00:00", "2011-09-26T00:00:00", "2011-10-03T00:00:00", "2011-10-10T00:00:00", "2011-10-17T00:00:00", "2011-10-24T00:00:00", "2011-10-31T00:00:00", "2011-11-07T00:00:00", "2011-11-14T00:00:00", "2011-11-21T00:00:00", "2011-11-28T00:00:00", "2011-12-05T00:00:00", "2011-12-12T00:00:00", "2011-12-19T00:00:00", "2011-12-26T00:00:00", "2012-01-02T00:00:00", "2012-01-09T00:00:00", "2012-01-16T00:00:00", "2012-01-23T00:00:00", "2012-01-30T00:00:00", "2012-02-06T00:00:00", "2012-02-13T00:00:00", "2012-02-20T00:00:00", "2012-02-27T00:00:00", "2012-03-05T00:00:00", "2012-03-12T00:00:00", "2012-03-19T00:00:00", "2012-03-26T00:00:00", "2012-04-02T00:00:00", "2012-04-09T00:00:00", "2012-04-16T00:00:00", "2012-04-23T00:00:00", "2012-04-30T00:00:00", "2012-05-07T00:00:00", "2012-05-14T00:00:00", "2012-05-21T00:00:00", "2012-05-28T00:00:00", "2012-06-04T00:00:00", "2012-06-11T00:00:00", "2012-06-18T00:00:00", "2012-06-25T00:00:00", "2012-07-02T00:00:00", "2012-07-09T00:00:00", "2012-07-16T00:00:00", "2012-07-23T00:00:00", "2012-07-30T00:00:00", "2012-08-06T00:00:00", "2012-08-13T00:00:00", "2012-08-20T00:00:00", "2012-08-27T00:00:00", "2012-09-03T00:00:00", "2012-09-10T00:00:00", "2012-09-17T00:00:00", "2012-09-24T00:00:00", "2012-10-01T00:00:00", "2012-10-08T00:00:00", "2012-10-15T00:00:00", "2012-10-22T00:00:00", "2012-10-29T00:00:00", "2012-11-05T00:00:00", "2012-11-12T00:00:00", "2012-11-19T00:00:00", "2012-11-26T00:00:00", "2012-12-03T00:00:00", "2012-12-10T00:00:00", "2012-12-17T00:00:00", "2012-12-24T00:00:00", "2012-12-31T00:00:00", "2013-01-07T00:00:00", "2013-01-14T00:00:00", "2013-01-21T00:00:00", "2013-01-28T00:00:00", "2013-02-04T00:00:00", "2013-02-11T00:00:00", "2013-02-18T00:00:00", "2013-02-25T00:00:00", "2013-03-04T00:00:00", "2013-03-11T00:00:00", "2013-03-18T00:00:00", "2013-03-25T00:00:00", "2013-04-01T00:00:00", "2013-04-08T00:00:00", "2013-04-15T00:00:00", "2013-04-22T00:00:00", "2013-04-29T00:00:00", "2013-05-06T00:00:00", "2013-05-13T00:00:00", "2013-05-20T00:00:00", "2013-05-27T00:00:00", "2013-06-03T00:00:00", "2013-06-10T00:00:00", "2013-06-17T00:00:00", "2013-06-24T00:00:00", "2013-07-01T00:00:00", "2013-07-08T00:00:00", "2013-07-15T00:00:00", "2013-07-22T00:00:00", "2013-07-29T00:00:00", "2013-08-05T00:00:00", "2013-08-12T00:00:00", "2013-08-19T00:00:00", "2013-08-26T00:00:00", "2013-09-02T00:00:00", "2013-09-09T00:00:00", "2013-09-16T00:00:00", "2013-09-23T00:00:00", "2013-09-30T00:00:00", "2013-10-07T00:00:00", "2013-10-14T00:00:00", "2013-10-21T00:00:00", "2013-10-28T00:00:00", "2013-11-04T00:00:00", "2013-11-11T00:00:00", "2013-11-18T00:00:00", "2013-11-25T00:00:00", "2013-12-02T00:00:00", "2013-12-09T00:00:00", "2013-12-16T00:00:00", "2013-12-23T00:00:00", "2013-12-30T00:00:00", "2014-01-06T00:00:00", "2014-01-13T00:00:00", "2014-01-20T00:00:00", "2014-01-27T00:00:00", "2014-02-03T00:00:00", "2014-02-10T00:00:00", "2014-02-17T00:00:00", "2014-02-24T00:00:00", "2014-03-03T00:00:00", "2014-03-10T00:00:00", "2014-03-17T00:00:00", "2014-03-24T00:00:00", "2014-03-31T00:00:00", "2014-04-07T00:00:00", "2014-04-14T00:00:00", "2014-04-21T00:00:00", "2014-04-28T00:00:00", "2014-05-05T00:00:00", "2014-05-12T00:00:00", "2014-05-19T00:00:00", "2014-05-26T00:00:00", "2014-06-02T00:00:00", "2014-06-09T00:00:00", "2014-06-16T00:00:00", "2014-06-23T00:00:00", "2014-06-30T00:00:00", "2014-07-07T00:00:00", "2014-07-14T00:00:00", "2014-07-21T00:00:00", "2014-07-28T00:00:00", "2014-08-04T00:00:00", "2014-08-11T00:00:00", "2014-08-18T00:00:00", "2014-08-25T00:00:00", "2014-09-01T00:00:00", "2014-09-08T00:00:00", "2014-09-15T00:00:00", "2014-09-22T00:00:00", "2014-09-29T00:00:00", "2014-10-06T00:00:00", "2014-10-13T00:00:00", "2014-10-20T00:00:00", "2014-10-27T00:00:00", "2014-11-03T00:00:00", "2014-11-10T00:00:00", "2014-11-17T00:00:00", "2014-11-24T00:00:00", "2014-12-01T00:00:00", "2014-12-08T00:00:00", "2014-12-15T00:00:00", "2014-12-22T00:00:00", "2014-12-29T00:00:00", "2015-01-05T00:00:00", "2015-01-12T00:00:00", "2015-01-19T00:00:00", "2015-01-26T00:00:00", "2015-02-02T00:00:00", "2015-02-09T00:00:00", "2015-02-16T00:00:00", "2015-02-23T00:00:00", "2015-03-02T00:00:00", "2015-03-09T00:00:00", "2015-03-16T00:00:00", "2015-03-23T00:00:00", "2015-03-30T00:00:00", "2015-04-06T00:00:00", "2015-04-13T00:00:00", "2015-04-20T00:00:00", "2015-04-27T00:00:00", "2015-05-04T00:00:00", "2015-05-11T00:00:00", "2015-05-18T00:00:00", "2015-05-25T00:00:00", "2015-06-01T00:00:00", "2015-06-08T00:00:00", "2015-06-15T00:00:00", "2015-06-22T00:00:00", "2015-06-29T00:00:00", "2015-07-06T00:00:00", "2015-07-13T00:00:00", "2015-07-20T00:00:00", "2015-07-27T00:00:00", "2015-08-03T00:00:00", "2015-08-10T00:00:00", "2015-08-17T00:00:00", "2015-08-24T00:00:00", "2015-08-31T00:00:00", "2015-09-07T00:00:00", "2015-09-14T00:00:00", "2015-09-21T00:00:00", "2015-09-28T00:00:00", "2015-10-05T00:00:00", "2015-10-12T00:00:00", "2015-10-19T00:00:00", "2015-10-26T00:00:00", "2015-11-02T00:00:00", "2015-11-09T00:00:00", "2015-11-16T00:00:00", "2015-11-23T00:00:00", "2015-11-30T00:00:00", "2015-12-07T00:00:00", "2015-12-14T00:00:00", "2015-12-21T00:00:00", "2015-12-28T00:00:00", "2016-01-04T00:00:00", "2016-01-11T00:00:00", "2016-01-18T00:00:00", "2016-01-25T00:00:00", "2016-02-01T00:00:00", "2016-02-08T00:00:00", "2016-02-15T00:00:00", "2016-02-22T00:00:00", "2016-02-29T00:00:00", "2016-03-07T00:00:00", "2016-03-14T00:00:00", "2016-03-21T00:00:00", "2016-03-28T00:00:00", "2016-04-04T00:00:00", "2016-04-11T00:00:00", "2016-04-18T00:00:00", "2016-04-25T00:00:00", "2016-05-02T00:00:00", "2016-05-09T00:00:00", "2016-05-16T00:00:00", "2016-05-23T00:00:00", "2016-05-30T00:00:00", "2016-06-06T00:00:00", "2016-06-13T00:00:00", "2016-06-20T00:00:00", "2016-06-27T00:00:00", "2016-07-04T00:00:00", "2016-07-11T00:00:00", "2016-07-18T00:00:00", "2016-07-25T00:00:00", "2016-08-01T00:00:00", "2016-08-08T00:00:00", "2016-08-15T00:00:00", "2016-08-22T00:00:00", "2016-08-29T00:00:00", "2016-09-05T00:00:00", "2016-09-12T00:00:00", "2016-09-19T00:00:00", "2016-09-26T00:00:00", "2016-10-03T00:00:00", "2016-10-10T00:00:00", "2016-10-17T00:00:00", "2016-10-24T00:00:00", "2016-10-31T00:00:00", "2016-11-07T00:00:00", "2016-11-14T00:00:00", "2016-11-21T00:00:00", "2016-11-28T00:00:00", "2016-12-05T00:00:00", "2016-12-12T00:00:00", "2016-12-19T00:00:00", "2016-12-26T00:00:00", "2017-01-02T00:00:00", "2017-01-09T00:00:00", "2017-01-16T00:00:00", "2017-01-23T00:00:00", "2017-01-30T00:00:00", "2017-02-06T00:00:00", "2017-02-13T00:00:00", "2017-02-20T00:00:00", "2017-02-27T00:00:00", "2017-03-06T00:00:00", "2017-03-13T00:00:00", "2017-03-20T00:00:00", "2017-03-27T00:00:00", "2017-04-03T00:00:00", "2017-04-10T00:00:00", "2017-04-17T00:00:00", "2017-04-24T00:00:00", "2017-05-01T00:00:00", "2017-05-08T00:00:00", "2017-05-15T00:00:00", "2017-05-22T00:00:00", "2017-05-29T00:00:00", "2017-06-05T00:00:00", "2017-06-12T00:00:00", "2017-06-19T00:00:00", "2017-06-26T00:00:00", "2017-07-03T00:00:00", "2017-07-10T00:00:00", "2017-07-17T00:00:00", "2017-07-24T00:00:00", "2017-07-31T00:00:00", "2017-08-07T00:00:00", "2017-08-14T00:00:00", "2017-08-21T00:00:00", "2017-08-28T00:00:00", "2017-09-04T00:00:00", "2017-09-11T00:00:00", "2017-09-18T00:00:00", "2017-09-25T00:00:00", "2017-10-02T00:00:00", "2017-10-09T00:00:00", "2017-10-16T00:00:00", "2017-10-23T00:00:00", "2017-10-30T00:00:00", "2017-11-06T00:00:00", "2017-11-13T00:00:00", "2017-11-20T00:00:00", "2017-11-27T00:00:00", "2017-12-04T00:00:00", "2017-12-11T00:00:00", "2017-12-18T00:00:00", "2017-12-25T00:00:00", "2018-01-01T00:00:00", "2018-01-08T00:00:00", "2018-01-15T00:00:00", "2018-01-22T00:00:00", "2018-01-29T00:00:00", "2018-02-05T00:00:00", "2018-02-12T00:00:00", "2018-02-19T00:00:00", "2018-02-26T00:00:00", "2018-03-05T00:00:00", "2018-03-12T00:00:00", "2018-03-19T00:00:00", "2018-03-26T00:00:00", "2018-04-02T00:00:00", "2018-04-09T00:00:00", "2018-04-16T00:00:00", "2018-04-23T00:00:00", "2018-04-30T00:00:00", "2018-05-07T00:00:00", "2018-05-14T00:00:00", "2018-05-21T00:00:00", "2018-05-28T00:00:00", "2018-06-04T00:00:00", "2018-06-11T00:00:00", "2018-06-18T00:00:00", "2018-06-25T00:00:00", "2018-07-02T00:00:00", "2018-07-09T00:00:00", "2018-07-16T00:00:00", "2018-07-23T00:00:00", "2018-07-30T00:00:00", "2018-08-06T00:00:00", "2018-08-13T00:00:00", "2018-08-20T00:00:00", "2018-08-27T00:00:00", "2018-09-03T00:00:00", "2018-09-10T00:00:00", "2018-09-17T00:00:00", "2018-09-24T00:00:00", "2018-10-01T00:00:00", "2018-10-08T00:00:00", "2018-10-15T00:00:00", "2018-10-22T00:00:00", "2018-10-29T00:00:00", "2018-11-05T00:00:00", "2018-11-12T00:00:00", "2018-11-19T00:00:00", "2018-11-26T00:00:00", "2018-12-03T00:00:00", "2018-12-10T00:00:00", "2018-12-17T00:00:00", "2018-12-24T00:00:00", "2018-12-31T00:00:00", "2019-01-07T00:00:00", "2019-01-14T00:00:00", "2019-01-21T00:00:00", "2019-01-28T00:00:00", "2019-02-04T00:00:00", "2019-02-11T00:00:00", "2019-02-18T00:00:00", "2019-02-25T00:00:00", "2019-03-04T00:00:00", "2019-03-11T00:00:00", "2019-03-18T00:00:00", "2019-03-25T00:00:00", "2019-04-01T00:00:00", "2019-04-08T00:00:00", "2019-04-15T00:00:00", "2019-04-22T00:00:00", "2019-04-29T00:00:00", "2019-05-06T00:00:00", "2019-05-13T00:00:00", "2019-05-20T00:00:00", "2019-05-27T00:00:00", "2019-06-03T00:00:00", "2019-06-10T00:00:00", "2019-06-17T00:00:00", "2019-06-24T00:00:00", "2019-07-01T00:00:00", "2019-07-08T00:00:00", "2019-07-15T00:00:00", "2019-07-22T00:00:00", "2019-07-29T00:00:00", "2019-08-05T00:00:00", "2019-08-12T00:00:00", "2019-08-19T00:00:00", "2019-08-26T00:00:00", "2019-09-02T00:00:00", "2019-09-09T00:00:00", "2019-09-16T00:00:00", "2019-09-23T00:00:00", "2019-09-30T00:00:00", "2019-10-07T00:00:00", "2019-10-14T00:00:00", "2019-10-21T00:00:00", "2019-10-28T00:00:00", "2019-11-04T00:00:00", "2019-11-11T00:00:00", "2019-11-18T00:00:00", "2019-11-25T00:00:00", "2019-12-02T00:00:00", "2019-12-09T00:00:00", "2019-12-16T00:00:00", "2019-12-23T00:00:00", "2019-12-30T00:00:00", "2020-01-06T00:00:00", "2020-01-13T00:00:00", "2020-01-20T00:00:00", "2020-01-27T00:00:00", "2020-02-03T00:00:00", "2020-02-10T00:00:00", "2020-02-17T00:00:00", "2020-02-24T00:00:00", "2020-03-02T00:00:00", "2020-03-09T00:00:00", "2020-03-16T00:00:00", "2020-03-23T00:00:00", "2020-03-30T00:00:00", "2020-04-06T00:00:00", "2020-04-13T00:00:00", "2020-04-20T00:00:00", "2020-04-27T00:00:00", "2020-05-04T00:00:00", "2020-05-11T00:00:00", "2020-05-18T00:00:00", "2020-05-25T00:00:00", "2020-06-01T00:00:00", "2020-06-08T00:00:00", "2020-06-15T00:00:00", "2020-06-22T00:00:00", "2020-06-29T00:00:00", "2020-07-06T00:00:00", "2020-07-13T00:00:00", "2020-07-20T00:00:00", "2020-07-27T00:00:00", "2020-08-03T00:00:00", "2020-08-10T00:00:00", "2020-08-17T00:00:00", "2020-08-24T00:00:00", "2020-08-31T00:00:00", "2020-09-07T00:00:00", "2020-09-14T00:00:00", "2020-09-21T00:00:00", "2020-09-28T00:00:00", "2020-10-05T00:00:00", "2020-10-12T00:00:00", "2020-10-19T00:00:00", "2020-10-26T00:00:00", "2020-11-02T00:00:00", "2020-11-09T00:00:00", "2020-11-16T00:00:00", "2020-11-23T00:00:00", "2020-11-30T00:00:00", "2020-12-07T00:00:00", "2020-12-14T00:00:00", "2020-12-21T00:00:00", "2020-12-28T00:00:00", "2021-01-04T00:00:00", "2021-01-11T00:00:00", "2021-01-18T00:00:00", "2021-01-25T00:00:00", "2021-02-01T00:00:00", "2021-02-08T00:00:00", "2021-02-15T00:00:00", "2021-02-22T00:00:00", "2021-03-01T00:00:00", "2021-03-08T00:00:00", "2021-03-15T00:00:00", "2021-03-22T00:00:00", "2021-03-29T00:00:00", "2021-04-05T00:00:00", "2021-04-12T00:00:00", "2021-04-19T00:00:00", "2021-04-26T00:00:00", "2021-05-03T00:00:00", "2021-05-10T00:00:00", "2021-05-17T00:00:00", "2021-05-24T00:00:00", "2021-05-31T00:00:00", "2021-06-07T00:00:00", "2021-06-14T00:00:00", "2021-06-21T00:00:00", "2021-06-28T00:00:00", "2021-07-05T00:00:00", "2021-07-12T00:00:00", "2021-07-19T00:00:00", "2021-07-26T00:00:00", "2021-08-02T00:00:00", "2021-08-09T00:00:00", "2021-08-16T00:00:00", "2021-08-23T00:00:00", "2021-08-30T00:00:00", "2021-09-06T00:00:00", "2021-09-13T00:00:00", "2021-09-20T00:00:00", "2021-09-27T00:00:00", "2021-10-04T00:00:00", "2021-10-11T00:00:00", "2021-10-18T00:00:00", "2021-10-25T00:00:00", "2021-11-01T00:00:00", "2021-11-08T00:00:00", "2021-11-15T00:00:00", "2021-11-22T00:00:00", "2021-11-29T00:00:00", "2021-12-06T00:00:00", "2021-12-13T00:00:00", "2021-12-20T00:00:00", "2021-12-27T00:00:00", "2022-01-03T00:00:00", "2022-01-10T00:00:00", "2022-01-17T00:00:00", "2022-01-24T00:00:00", "2022-01-31T00:00:00", "2022-02-07T00:00:00", "2022-02-14T00:00:00", "2022-02-21T00:00:00", "2022-02-28T00:00:00", "2022-03-07T00:00:00", "2022-03-14T00:00:00", "2022-03-21T00:00:00", "2022-03-28T00:00:00", "2022-04-04T00:00:00", "2022-04-11T00:00:00", "2022-04-18T00:00:00", "2022-04-25T00:00:00", "2022-05-02T00:00:00", "2022-05-09T00:00:00", "2022-05-16T00:00:00", "2022-05-23T00:00:00", "2022-05-30T00:00:00", "2022-06-06T00:00:00", "2022-06-13T00:00:00", "2022-06-20T00:00:00", "2022-06-27T00:00:00", "2022-07-04T00:00:00", "2022-07-11T00:00:00", "2022-07-18T00:00:00", "2022-07-25T00:00:00", "2022-08-01T00:00:00", "2022-08-08T00:00:00", "2022-08-15T00:00:00", "2022-08-22T00:00:00", "2022-08-29T00:00:00", "2022-09-05T00:00:00", "2022-09-12T00:00:00", "2022-09-19T00:00:00", "2022-09-26T00:00:00", "2022-10-03T00:00:00", "2022-10-10T00:00:00", "2022-10-17T00:00:00", "2022-10-24T00:00:00", "2022-10-31T00:00:00", "2022-11-07T00:00:00", "2022-11-14T00:00:00", "2022-11-21T00:00:00", "2022-11-28T00:00:00", "2022-12-05T00:00:00", "2022-12-12T00:00:00", "2022-12-19T00:00:00", "2022-12-26T00:00:00", "2023-01-02T00:00:00", "2023-01-09T00:00:00", "2023-01-16T00:00:00", "2023-01-23T00:00:00", "2023-01-30T00:00:00", "2023-02-06T00:00:00", "2023-02-13T00:00:00", "2023-02-20T00:00:00", "2023-02-27T00:00:00", "2023-03-06T00:00:00", "2023-03-13T00:00:00", "2023-03-20T00:00:00", "2023-03-27T00:00:00", "2023-04-03T00:00:00", "2023-04-10T00:00:00", "2023-04-17T00:00:00", "2023-04-24T00:00:00", "2023-05-01T00:00:00", "2023-05-08T00:00:00", "2023-05-15T00:00:00", "2023-05-22T00:00:00", "2023-05-29T00:00:00", "2023-06-05T00:00:00", "2023-06-12T00:00:00", "2023-06-19T00:00:00", "2023-06-26T00:00:00", "2023-07-03T00:00:00", "2023-07-10T00:00:00", "2023-07-17T00:00:00", "2023-07-24T00:00:00", "2023-07-31T00:00:00", "2023-08-07T00:00:00", "2023-08-14T00:00:00", "2023-08-21T00:00:00", "2023-08-28T00:00:00", "2023-09-04T00:00:00", "2023-09-11T00:00:00", "2023-09-18T00:00:00", "2023-09-25T00:00:00", "2023-10-02T00:00:00", "2023-10-09T00:00:00", "2023-10-16T00:00:00", "2023-10-23T00:00:00", "2023-10-30T00:00:00", "2023-11-06T00:00:00", "2023-11-13T00:00:00", "2023-11-20T00:00:00", "2023-11-27T00:00:00", "2023-12-04T00:00:00", "2023-12-11T00:00:00", "2023-12-18T00:00:00", "2023-12-25T00:00:00", "2024-01-01T00:00:00", "2024-01-08T00:00:00", "2024-01-15T00:00:00", "2024-01-22T00:00:00", "2024-01-29T00:00:00", "2024-02-05T00:00:00", "2024-02-12T00:00:00", "2024-02-19T00:00:00", "2024-02-26T00:00:00", "2024-03-04T00:00:00", "2024-03-11T00:00:00", "2024-03-18T00:00:00", "2024-03-25T00:00:00", "2024-04-01T00:00:00", "2024-04-08T00:00:00", "2024-04-15T00:00:00", "2024-04-22T00:00:00", "2024-04-29T00:00:00", "2024-05-06T00:00:00", "2024-05-13T00:00:00", "2024-05-20T00:00:00", "2024-05-27T00:00:00", "2024-06-03T00:00:00", "2024-06-10T00:00:00", "2024-06-17T00:00:00", "2024-06-24T00:00:00", "2024-07-01T00:00:00", "2024-07-08T00:00:00", "2024-07-15T00:00:00", "2024-07-22T00:00:00", "2024-07-29T00:00:00", "2024-08-05T00:00:00", "2024-08-12T00:00:00", "2024-08-19T00:00:00"], "y": [5.78, 5.68, 5.77, 5.88, 5.84, 5.83, 5.82, 5.74, 5.59, 5.74, 5.87, 5.77, 5.68, 5.64, 5.53, 5.37, 5.17, 5.02, 5.12, 4.94, 5.25, 5.25, 5.32, 5.18, 5.05, 5.11, 5.05, 4.98, 4.92, 4.82, 4.85, 4.98, 4.93, 5.28, 5.2, 5.35, 5.21, 5.46, 5.41, 5.52, 5.35, 5.32, 5.27, 5.16, 5.37, 5.37, 5.21, 5.13, 5.11, 5.19, 4.97, 4.91, 4.94, 4.85, 4.84, 4.63, 4.73, 4.55, 4.52, 4.62, 4.63, 4.5, 4.31, 4.34, 4.8, 5.05, 4.75, 5.17, 5.26, 5.18, 5.07, 5.09, 4.91, 4.94, 5.12, 4.94, 4.91, 4.86, 4.86, 5.02, 5.33, 5.32, 5.41, 5.44, 5.25, 5.15, 5.19, 5.13, 5.1, 5.23, 5.21, 5.16, 5.06, 5.07, 4.89, 4.87, 4.85, 4.84, 4.66, 4.51, 4.62, 4.29, 4.22, 4.29, 4.22, 4.14, 4.05, 3.9, 3.7, 3.63, 3.64, 3.83, 4.24, 4.1, 4.07, 3.85, 4.02, 4.19, 4.22, 4.06, 4.15, 3.98, 3.82, 4.09, 4.15, 4.05, 3.98, 4.01, 3.99, 3.95, 3.86, 3.68, 3.59, 3.82, 3.98, 3.83, 4.03, 4.04, 4, 3.92, 3.92, 3.64, 3.46, 3.34, 3.43, 3.29, 3.18, 3.32, 3.54, 3.74, 3.74, 4.19, 4.31, 4.35, 4.38, 4.49, 4.53, 4.45, 4.41, 4.28, 4.26, 4.09, 4.17, 4.29, 4.41, 4.3, 4.4, 4.49, 4.18, 4.23, 4.4, 4.29, 4.28, 4.18, 4.24, 4.41, 4.11, 4.04, 4.16, 4.18, 4.09, 4.05, 4.05, 4, 3.78, 3.78, 3.74, 3.91, 4.24, 4.25, 4.39, 4.46, 4.53, 4.81, 4.7, 4.75, 4.66, 4.78, 4.89, 4.7, 4.76, 4.48, 4.46, 4.38, 4.49, 4.48, 4.28, 4.26, 4.28, 4.19, 4.3, 4.16, 4.07, 4.01, 4.19, 4.15, 4.07, 3.99, 4.11, 4.22, 4.2, 4.18, 4.34, 4.24, 4.16, 4.21, 4.3, 4.23, 4.29, 4.23, 4.14, 4.14, 4.07, 4.08, 4.27, 4.36, 4.31, 4.52, 4.53, 4.64, 4.47, 4.45, 4.27, 4.26, 4.21, 4.29, 4.13, 4.07, 4.08, 3.96, 4.09, 4.11, 3.9, 4.06, 4.11, 4.22, 4.25, 4.32, 4.42, 4.27, 4.22, 4.2, 4.03, 4.18, 4.25, 4.3, 4.39, 4.35, 4.5, 4.45, 4.57, 4.65, 4.61, 4.46, 4.41, 4.57, 4.56, 4.45, 4.38, 4.39, 4.38, 4.36, 4.36, 4.54, 4.55, 4.58, 4.54, 4.59, 4.74, 4.77, 4.66, 4.7, 4.88, 4.97, 5.01, 4.99, 5.14, 5.12, 5.15, 5.04, 5.06, 5.02, 4.99, 5.14, 5.25, 5.15, 5.13, 5.07, 5.05, 4.99, 4.93, 5, 4.82, 4.8, 4.73, 4.8, 4.81, 4.56, 4.62, 4.7, 4.79, 4.83, 4.68, 4.71, 4.61, 4.6, 4.54, 4.43, 4.52, 4.6, 4.63, 4.71, 4.66, 4.77, 4.76, 4.9, 4.81, 4.8, 4.69, 4.63, 4.51, 4.56, 4.58, 4.6, 4.65, 4.75, 4.74, 4.66, 4.63, 4.64, 4.69, 4.79, 4.86, 4.93, 5.14, 5.15, 5.09, 5, 5.16, 5.05, 4.97, 4.82, 4.72, 4.78, 4.64, 4.6, 4.54, 4.34, 4.48, 4.63, 4.56, 4.65, 4.69, 4.42, 4.39, 4.35, 4.23, 4.07, 3.83, 3.89, 4.15, 4.2, 4.23, 4.04, 3.86, 3.81, 3.66, 3.61, 3.68, 3.62, 3.76, 3.91, 3.54, 3.46, 3.34, 3.56, 3.45, 3.57, 3.53, 3.75, 3.86, 3.88, 3.78, 3.83, 3.85, 3.98, 4.02, 4.25, 4.19, 3.99, 3.95, 3.9, 4.09, 4.06, 3.98, 3.99, 3.82, 3.79, 3.83, 3.66, 3.47, 3.83, 3.61, 3.48, 3.89, 3.91, 3.79, 3.96, 3.82, 3.68, 3.35, 2.72, 2.77, 2.53, 2.16, 2.13, 2.49, 2.34, 2.36, 2.7, 2.76, 3.07, 2.89, 2.78, 2.91, 2.89, 2.97, 2.68, 2.73, 2.95, 2.88, 2.88, 2.95, 3.19, 3.17, 3.22, 3.45, 3.71, 3.91, 3.76, 3.72, 3.51, 3.52, 3.38, 3.61, 3.75, 3.66, 3.8, 3.48, 3.48, 3.4, 3.45, 3.42, 3.49, 3.31, 3.24, 3.4, 3.41, 3.59, 3.45, 3.52, 3.33, 3.37, 3.21, 3.44, 3.56, 3.69, 3.85, 3.85, 3.85, 3.7, 3.66, 3.68, 3.62, 3.69, 3.8, 3.61, 3.72, 3.71, 3.67, 3.88, 4.01, 3.87, 3.83, 3.83, 3.72, 3.57, 3.47, 3.23, 3.31, 3.17, 3.28, 3.26, 3.05, 3, 3.08, 2.99, 3.03, 2.99, 2.86, 2.58, 2.6, 2.54, 2.72, 2.74, 2.72, 2.54, 2.5, 2.41, 2.52, 2.59, 2.66, 2.6, 2.92, 2.8, 2.84, 2.95, 3.29, 3.36, 3.36, 3.36, 3.32, 3.35, 3.43, 3.42, 3.68, 3.62, 3.59, 3.42, 3.51, 3.36, 3.34, 3.47, 3.45, 3.59, 3.4, 3.39, 3.31, 3.17, 3.15, 3.13, 3.07, 3.01, 3, 2.97, 2.95, 3.22, 2.94, 2.94, 3.03, 2.77, 2.4, 2.29, 2.1, 2.28, 2.02, 1.94, 1.97, 1.91, 1.8, 2.1, 2.18, 2.25, 2.17, 2.04, 2.04, 1.97, 1.97, 2.04, 2.03, 1.82, 2.03, 1.89, 1.98, 1.89, 2.09, 1.87, 1.93, 1.99, 2.01, 1.92, 2, 2.04, 2.39, 2.26, 2.22, 2.06, 2, 1.96, 1.95, 1.92, 1.78, 1.75, 1.75, 1.53, 1.6, 1.59, 1.63, 1.61, 1.53, 1.5, 1.47, 1.53, 1.59, 1.65, 1.82, 1.65, 1.57, 1.68, 1.85, 1.74, 1.64, 1.75, 1.7, 1.83, 1.74, 1.72, 1.61, 1.61, 1.66, 1.63, 1.63, 1.78, 1.79, 1.78, 1.92, 1.89, 1.87, 2, 2, 1.99, 2.01, 1.88, 1.88, 2.07, 1.96, 1.93, 1.86, 1.76, 1.72, 1.72, 1.7, 1.8, 1.92, 1.97, 2.01, 2.13, 2.22, 2.19, 2.57, 2.5, 2.65, 2.57, 2.5, 2.61, 2.67, 2.61, 2.88, 2.79, 2.78, 2.9, 2.88, 2.72, 2.64, 2.65, 2.7, 2.63, 2.54, 2.63, 2.77, 2.67, 2.74, 2.81, 2.86, 2.89, 2.94, 2.99, 2.98, 2.84, 2.84, 2.78, 2.61, 2.7, 2.75, 2.75, 2.6, 2.79, 2.7, 2.74, 2.73, 2.71, 2.65, 2.73, 2.7, 2.63, 2.66, 2.54, 2.54, 2.54, 2.62, 2.61, 2.63, 2.53, 2.63, 2.55, 2.49, 2.5, 2.51, 2.44, 2.39, 2.39, 2.35, 2.48, 2.6, 2.57, 2.5, 2.43, 2.31, 2.2, 2.27, 2.36, 2.38, 2.34, 2.3, 2.22, 2.26, 2.12, 2.17, 2.22, 2.04, 1.92, 1.83, 1.83, 1.68, 1.96, 2.02, 2.06, 2.08, 2.2, 2.1, 1.92, 1.96, 1.92, 1.94, 1.9, 1.94, 2.16, 2.28, 2.23, 2.21, 2.19, 2.39, 2.36, 2.37, 2.33, 2.3, 2.44, 2.38, 2.23, 2.16, 2.24, 2.16, 2.01, 2.21, 2.13, 2.18, 2.2, 2.1, 2.07, 2.12, 2.04, 2.07, 2.2, 2.36, 2.27, 2.25, 2.21, 2.23, 2.23, 2.2, 2.24, 2.24, 2.17, 2.03, 2.03, 1.97, 1.75, 1.74, 1.77, 1.74, 1.91, 1.97, 1.92, 1.89, 1.78, 1.73, 1.78, 1.91, 1.88, 1.77, 1.75, 1.84, 1.85, 1.73, 1.62, 1.67, 1.46, 1.46, 1.43, 1.59, 1.58, 1.51, 1.59, 1.55, 1.55, 1.57, 1.6, 1.68, 1.7, 1.59, 1.63, 1.73, 1.77, 1.77, 1.84, 1.83, 2.23, 2.33, 2.32, 2.39, 2.49, 2.54, 2.55, 2.45, 2.38, 2.4, 2.41, 2.49, 2.42, 2.43, 2.42, 2.36, 2.49, 2.62, 2.47, 2.38, 2.35, 2.37, 2.26, 2.28, 2.33, 2.39, 2.34, 2.25, 2.25, 2.18, 2.21, 2.19, 2.14, 2.35, 2.38, 2.31, 2.26, 2.3, 2.26, 2.22, 2.18, 2.16, 2.16, 2.14, 2.23, 2.22, 2.34, 2.37, 2.3, 2.38, 2.37, 2.32, 2.4, 2.37, 2.32, 2.37, 2.39, 2.39, 2.48, 2.4, 2.49, 2.55, 2.66, 2.7, 2.77, 2.86, 2.87, 2.86, 2.88, 2.87, 2.85, 2.85, 2.73, 2.78, 2.83, 2.98, 2.95, 2.95, 3, 3.06, 2.93, 2.94, 2.96, 2.92, 2.87, 2.87, 2.86, 2.85, 2.96, 2.98, 2.94, 2.88, 2.82, 2.85, 2.86, 2.94, 2.99, 3.08, 3.09, 3.23, 3.16, 3.2, 3.08, 3.2, 3.19, 3.06, 3.07, 2.98, 2.85, 2.86, 2.74, 2.69, 2.7, 2.71, 2.79, 2.75, 2.73, 2.65, 2.66, 2.67, 2.72, 2.64, 2.6, 2.43, 2.49, 2.52, 2.55, 2.59, 2.54, 2.51, 2.4, 2.41, 2.32, 2.07, 2.15, 2.09, 2.02, 2.03, 2.05, 2.09, 2.05, 2.06, 1.75, 1.65, 1.6, 1.54, 1.5, 1.63, 1.84, 1.72, 1.68, 1.56, 1.76, 1.8, 1.85, 1.79, 1.94, 1.81, 1.76, 1.83, 1.83, 1.89, 1.93, 1.9, 1.81, 1.85, 1.84, 1.61, 1.54, 1.56, 1.59, 1.38, 1.1, 0.54, 0.73, 0.76, 0.7, 0.67, 0.76, 0.63, 0.67, 0.64, 0.73, 0.73, 0.66, 0.66, 0.88, 0.71, 0.71, 0.64, 0.69, 0.64, 0.62, 0.62, 0.56, 0.59, 0.69, 0.65, 0.72, 0.72, 0.68, 0.68, 0.67, 0.78, 0.79, 0.78, 0.81, 0.87, 0.96, 0.91, 0.86, 0.84, 0.94, 0.9, 0.95, 0.94, 0.93, 1.15, 1.11, 1.05, 1.09, 1.19, 1.2, 1.37, 1.45, 1.59, 1.62, 1.69, 1.73, 1.73, 1.69, 1.61, 1.58, 1.63, 1.63, 1.64, 1.61, 1.58, 1.57, 1.51, 1.5, 1.49, 1.44, 1.38, 1.19, 1.29, 1.2, 1.33, 1.26, 1.25, 1.29, 1.33, 1.33, 1.31, 1.48, 1.49, 1.61, 1.59, 1.64, 1.58, 1.51, 1.63, 1.63, 1.52, 1.43, 1.42, 1.43, 1.48, 1.63, 1.78, 1.78, 1.75, 1.79, 1.92, 1.98, 1.92, 1.83, 1.78, 2.14, 2.32, 2.46, 2.42, 2.79, 2.85, 2.81, 2.99, 3.05, 2.88, 2.86, 2.74, 3.04, 3.43, 3.25, 3.2, 2.88, 2.99, 2.96, 2.81, 2.6, 2.77, 2.79, 3.03, 3.12, 3.2, 3.37, 3.49, 3.88, 3.67, 3.89, 4.02, 4.25, 4.1, 4.22, 3.88, 3.83, 3.69, 3.6, 3.61, 3.57, 3.75, 3.88, 3.53, 3.49, 3.52, 3.55, 3.63, 3.72, 3.82, 3.92, 3.98, 3.55, 3.47, 3.53, 3.43, 3.41, 3.6, 3.52, 3.59, 3.52, 3.5, 3.72, 3.8, 3.69, 3.73, 3.77, 3.72, 3.86, 4.01, 3.81, 3.86, 3.97, 4.09, 4.19, 4.34, 4.2, 4.18, 4.29, 4.32, 4.55, 4.69, 4.78, 4.71, 4.86, 4.88, 4.67, 4.63, 4.42, 4.39, 4.28, 4.23, 3.95, 3.9, 3.88, 4.01, 3.96, 4.11, 4.08, 4.17, 4.17, 4.3, 4.28, 4.22, 4.1, 4.34, 4.25, 4.33, 4.42, 4.63, 4.62, 4.63, 4.49, 4.48, 4.44, 4.46, 4.41, 4.47, 4.28, 4.25, 4.48, 4.28, 4.23, 4.26, 4.17, 3.78, 3.9, 3.86]}], "layout": {"legend": {"x": 1, "xanchor": "right", "y": 1, "yanchor": "top"}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Copper/Gold Ratio vs. US 10-Year Constant Maturity", "x": 0.5, "y": 0.9}, "xaxis": {"title": {"text": "Date"}}, "yaxis": {"title": {"text": "%"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = go.Figure()\n", "fig.add_scatter(\n", "    x=data.index, y=data[\"Copper/Gold Ratio\"], name=\"Copper/Gold Ratio (x1000) %\"\n", ")\n", "fig.add_scatter(\n", "    x=data.index,\n", "    y=data[\"US 10-Year Constant Maturity\"],\n", "    name=\"US 10-Year Constant Maturity %\",\n", ")\n", "fig.update(\n", "    {\n", "        \"layout\": {\n", "            \"xaxis\": {\"title\": \"Date\"},\n", "            \"yaxis\": {\"title\": \"%\"},\n", "            \"title\": \"Copper/Gold Ratio vs. US 10-Year Constant Maturity\",\n", "            \"title_y\": 0.90,\n", "            \"title_x\": 0.5,\n", "        }\n", "    }\n", ")\n", "fig.update_layout(legend=dict(yanchor=\"top\", y=1, xanchor=\"right\", x=1.0))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["What we have currently is the price relationship between one Troy ounce of gold and one pound of copper.  As we described the copper-to-gold ratio as the price-per-ounce of each, some adjustments are required to be true to the definition.\n", "\n", "- 1 ounce = 0.911458 Troy ounces\n", "- 1 pound = 16 ounces\n", "  \n", "To adjust the gold price as USD/ounce, multiply each row by 0.911458.  To adjust the copper price, divide each row by 16."]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Gold</th>\n", "      <th>Copper</th>\n", "      <th>Copper/Gold Ratio</th>\n", "      <th>US 10-Year Constant Maturity</th>\n", "      <th>Copper/Gold Ratio per Ounce (x1000) %</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-08-12</th>\n", "      <td>2498.600098</td>\n", "      <td>4.1275</td>\n", "      <td>1.651925</td>\n", "      <td>3.90</td>\n", "      <td>0.113275</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-19</th>\n", "      <td>2519.000000</td>\n", "      <td>4.1355</td>\n", "      <td>1.641723</td>\n", "      <td>3.86</td>\n", "      <td>0.112575</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   Gold  Copper  Copper/Gold Ratio  \\\n", "date                                                 \n", "2024-08-12  2498.600098  4.1275           1.651925   \n", "2024-08-19  2519.000000  4.1355           1.641723   \n", "\n", "            US 10-Year Constant Maturity  \\\n", "date                                       \n", "2024-08-12                          3.90   \n", "2024-08-19                          3.86   \n", "\n", "            Copper/Gold Ratio per Ounce (x1000) %  \n", "date                                               \n", "2024-08-12                               0.113275  \n", "2024-08-19                               0.112575  "]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["data[\"Copper/Gold Ratio per Ounce (x1000) %\"] = (\n", "    (data[\"Copper\"] / 16) / (data[\"Gold\"] * 0.911458)\n", ") * 1000\n", "\n", "data.tail(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's draw it!"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"name": "Copper/Gold Ratio (x1000) %", "type": "scatter", "x": ["2000-08-28T00:00:00", "2000-09-04T00:00:00", "2000-09-11T00:00:00", "2000-09-18T00:00:00", "2000-09-25T00:00:00", "2000-10-02T00:00:00", "2000-10-09T00:00:00", "2000-10-16T00:00:00", "2000-10-23T00:00:00", "2000-10-30T00:00:00", "2000-11-06T00:00:00", "2000-11-13T00:00:00", "2000-11-20T00:00:00", "2000-11-27T00:00:00", "2000-12-04T00:00:00", "2000-12-11T00:00:00", "2000-12-18T00:00:00", "2000-12-25T00:00:00", "2001-01-01T00:00:00", "2001-01-08T00:00:00", "2001-01-15T00:00:00", "2001-01-22T00:00:00", "2001-01-29T00:00:00", "2001-02-05T00:00:00", "2001-02-12T00:00:00", "2001-02-19T00:00:00", "2001-02-26T00:00:00", "2001-03-05T00:00:00", "2001-03-12T00:00:00", "2001-03-19T00:00:00", "2001-03-26T00:00:00", "2001-04-02T00:00:00", "2001-04-09T00:00:00", "2001-04-16T00:00:00", "2001-04-23T00:00:00", "2001-04-30T00:00:00", "2001-05-07T00:00:00", "2001-05-14T00:00:00", "2001-05-21T00:00:00", "2001-05-28T00:00:00", "2001-06-04T00:00:00", "2001-06-11T00:00:00", "2001-06-18T00:00:00", "2001-06-25T00:00:00", "2001-07-02T00:00:00", "2001-07-09T00:00:00", "2001-07-16T00:00:00", "2001-07-23T00:00:00", "2001-07-30T00:00:00", "2001-08-06T00:00:00", "2001-08-13T00:00:00", "2001-08-20T00:00:00", "2001-08-27T00:00:00", "2001-09-03T00:00:00", "2001-09-10T00:00:00", "2001-09-17T00:00:00", "2001-09-24T00:00:00", "2001-10-01T00:00:00", "2001-10-08T00:00:00", "2001-10-15T00:00:00", "2001-10-22T00:00:00", "2001-10-29T00:00:00", "2001-11-05T00:00:00", "2001-11-12T00:00:00", "2001-11-19T00:00:00", "2001-11-26T00:00:00", "2001-12-03T00:00:00", "2001-12-10T00:00:00", "2001-12-17T00:00:00", "2001-12-24T00:00:00", "2001-12-31T00:00:00", "2002-01-07T00:00:00", "2002-01-14T00:00:00", "2002-01-21T00:00:00", "2002-01-28T00:00:00", "2002-02-04T00:00:00", "2002-02-11T00:00:00", "2002-02-18T00:00:00", "2002-02-25T00:00:00", "2002-03-04T00:00:00", "2002-03-11T00:00:00", "2002-03-18T00:00:00", "2002-03-25T00:00:00", "2002-04-01T00:00:00", "2002-04-08T00:00:00", "2002-04-15T00:00:00", "2002-04-22T00:00:00", "2002-04-29T00:00:00", "2002-05-06T00:00:00", "2002-05-13T00:00:00", "2002-05-20T00:00:00", "2002-05-27T00:00:00", "2002-06-03T00:00:00", "2002-06-10T00:00:00", "2002-06-17T00:00:00", "2002-06-24T00:00:00", "2002-07-01T00:00:00", "2002-07-08T00:00:00", "2002-07-15T00:00:00", "2002-07-22T00:00:00", "2002-07-29T00:00:00", "2002-08-05T00:00:00", "2002-08-12T00:00:00", "2002-08-19T00:00:00", "2002-08-26T00:00:00", "2002-09-02T00:00:00", "2002-09-09T00:00:00", "2002-09-16T00:00:00", "2002-09-23T00:00:00", "2002-09-30T00:00:00", "2002-10-07T00:00:00", "2002-10-14T00:00:00", "2002-10-21T00:00:00", "2002-10-28T00:00:00", "2002-11-04T00:00:00", "2002-11-11T00:00:00", "2002-11-18T00:00:00", "2002-11-25T00:00:00", "2002-12-02T00:00:00", "2002-12-09T00:00:00", "2002-12-16T00:00:00", "2002-12-23T00:00:00", "2002-12-30T00:00:00", "2003-01-06T00:00:00", "2003-01-13T00:00:00", "2003-01-20T00:00:00", "2003-01-27T00:00:00", "2003-02-03T00:00:00", "2003-02-10T00:00:00", "2003-02-17T00:00:00", "2003-02-24T00:00:00", "2003-03-03T00:00:00", "2003-03-10T00:00:00", "2003-03-17T00:00:00", "2003-03-24T00:00:00", "2003-03-31T00:00:00", "2003-04-07T00:00:00", "2003-04-14T00:00:00", "2003-04-21T00:00:00", "2003-04-28T00:00:00", "2003-05-05T00:00:00", "2003-05-12T00:00:00", "2003-05-19T00:00:00", "2003-05-26T00:00:00", "2003-06-02T00:00:00", "2003-06-09T00:00:00", "2003-06-16T00:00:00", "2003-06-23T00:00:00", "2003-06-30T00:00:00", "2003-07-07T00:00:00", "2003-07-14T00:00:00", "2003-07-21T00:00:00", "2003-07-28T00:00:00", "2003-08-04T00:00:00", "2003-08-11T00:00:00", "2003-08-18T00:00:00", "2003-08-25T00:00:00", "2003-09-01T00:00:00", "2003-09-08T00:00:00", "2003-09-15T00:00:00", "2003-09-22T00:00:00", "2003-09-29T00:00:00", "2003-10-06T00:00:00", "2003-10-13T00:00:00", "2003-10-20T00:00:00", "2003-10-27T00:00:00", "2003-11-03T00:00:00", "2003-11-10T00:00:00", "2003-11-17T00:00:00", "2003-11-24T00:00:00", "2003-12-01T00:00:00", "2003-12-08T00:00:00", "2003-12-15T00:00:00", "2003-12-22T00:00:00", "2003-12-29T00:00:00", "2004-01-05T00:00:00", "2004-01-12T00:00:00", "2004-01-19T00:00:00", "2004-01-26T00:00:00", "2004-02-02T00:00:00", "2004-02-09T00:00:00", "2004-02-16T00:00:00", "2004-02-23T00:00:00", "2004-03-01T00:00:00", "2004-03-08T00:00:00", "2004-03-15T00:00:00", "2004-03-22T00:00:00", "2004-03-29T00:00:00", "2004-04-05T00:00:00", "2004-04-12T00:00:00", "2004-04-19T00:00:00", "2004-04-26T00:00:00", "2004-05-03T00:00:00", "2004-05-10T00:00:00", "2004-05-17T00:00:00", "2004-05-24T00:00:00", "2004-05-31T00:00:00", "2004-06-07T00:00:00", "2004-06-14T00:00:00", "2004-06-21T00:00:00", "2004-06-28T00:00:00", "2004-07-05T00:00:00", "2004-07-12T00:00:00", "2004-07-19T00:00:00", "2004-07-26T00:00:00", "2004-08-02T00:00:00", "2004-08-09T00:00:00", "2004-08-16T00:00:00", "2004-08-23T00:00:00", "2004-08-30T00:00:00", "2004-09-06T00:00:00", "2004-09-13T00:00:00", "2004-09-20T00:00:00", "2004-09-27T00:00:00", "2004-10-04T00:00:00", "2004-10-11T00:00:00", "2004-10-18T00:00:00", "2004-10-25T00:00:00", "2004-11-01T00:00:00", "2004-11-08T00:00:00", "2004-11-15T00:00:00", "2004-11-22T00:00:00", "2004-11-29T00:00:00", "2004-12-06T00:00:00", "2004-12-13T00:00:00", "2004-12-20T00:00:00", "2004-12-27T00:00:00", "2005-01-03T00:00:00", "2005-01-10T00:00:00", "2005-01-17T00:00:00", "2005-01-24T00:00:00", "2005-01-31T00:00:00", "2005-02-07T00:00:00", "2005-02-14T00:00:00", "2005-02-21T00:00:00", "2005-02-28T00:00:00", "2005-03-07T00:00:00", "2005-03-14T00:00:00", "2005-03-21T00:00:00", "2005-03-28T00:00:00", "2005-04-04T00:00:00", "2005-04-11T00:00:00", "2005-04-18T00:00:00", "2005-04-25T00:00:00", "2005-05-02T00:00:00", "2005-05-09T00:00:00", "2005-05-16T00:00:00", "2005-05-23T00:00:00", "2005-05-30T00:00:00", "2005-06-06T00:00:00", "2005-06-13T00:00:00", "2005-06-20T00:00:00", "2005-06-27T00:00:00", "2005-07-04T00:00:00", "2005-07-11T00:00:00", "2005-07-18T00:00:00", "2005-07-25T00:00:00", "2005-08-01T00:00:00", "2005-08-08T00:00:00", "2005-08-15T00:00:00", "2005-08-22T00:00:00", "2005-08-29T00:00:00", "2005-09-05T00:00:00", "2005-09-12T00:00:00", "2005-09-19T00:00:00", "2005-09-26T00:00:00", "2005-10-03T00:00:00", "2005-10-10T00:00:00", "2005-10-17T00:00:00", "2005-10-24T00:00:00", "2005-10-31T00:00:00", "2005-11-07T00:00:00", "2005-11-14T00:00:00", "2005-11-21T00:00:00", "2005-11-28T00:00:00", "2005-12-05T00:00:00", "2005-12-12T00:00:00", "2005-12-19T00:00:00", "2005-12-26T00:00:00", "2006-01-02T00:00:00", "2006-01-09T00:00:00", "2006-01-16T00:00:00", "2006-01-23T00:00:00", "2006-01-30T00:00:00", "2006-02-06T00:00:00", "2006-02-13T00:00:00", "2006-02-20T00:00:00", "2006-02-27T00:00:00", "2006-03-06T00:00:00", "2006-03-13T00:00:00", "2006-03-20T00:00:00", "2006-03-27T00:00:00", "2006-04-03T00:00:00", "2006-04-10T00:00:00", "2006-04-17T00:00:00", "2006-04-24T00:00:00", "2006-05-01T00:00:00", "2006-05-08T00:00:00", "2006-05-15T00:00:00", "2006-05-22T00:00:00", "2006-05-29T00:00:00", "2006-06-05T00:00:00", "2006-06-12T00:00:00", "2006-06-19T00:00:00", "2006-06-26T00:00:00", "2006-07-03T00:00:00", "2006-07-10T00:00:00", "2006-07-17T00:00:00", "2006-07-24T00:00:00", "2006-07-31T00:00:00", "2006-08-07T00:00:00", "2006-08-14T00:00:00", "2006-08-21T00:00:00", "2006-08-28T00:00:00", "2006-09-04T00:00:00", "2006-09-11T00:00:00", "2006-09-18T00:00:00", "2006-09-25T00:00:00", "2006-10-02T00:00:00", "2006-10-09T00:00:00", "2006-10-16T00:00:00", "2006-10-23T00:00:00", "2006-10-30T00:00:00", "2006-11-06T00:00:00", "2006-11-13T00:00:00", "2006-11-20T00:00:00", "2006-11-27T00:00:00", "2006-12-04T00:00:00", "2006-12-11T00:00:00", "2006-12-18T00:00:00", "2006-12-25T00:00:00", "2007-01-01T00:00:00", "2007-01-08T00:00:00", "2007-01-15T00:00:00", "2007-01-22T00:00:00", "2007-01-29T00:00:00", "2007-02-05T00:00:00", "2007-02-12T00:00:00", "2007-02-19T00:00:00", "2007-02-26T00:00:00", "2007-03-05T00:00:00", "2007-03-12T00:00:00", "2007-03-19T00:00:00", "2007-03-26T00:00:00", "2007-04-02T00:00:00", "2007-04-09T00:00:00", "2007-04-16T00:00:00", "2007-04-23T00:00:00", "2007-04-30T00:00:00", "2007-05-07T00:00:00", "2007-05-14T00:00:00", "2007-05-21T00:00:00", "2007-05-28T00:00:00", "2007-06-04T00:00:00", "2007-06-11T00:00:00", "2007-06-18T00:00:00", "2007-06-25T00:00:00", "2007-07-02T00:00:00", "2007-07-09T00:00:00", "2007-07-16T00:00:00", "2007-07-23T00:00:00", "2007-07-30T00:00:00", "2007-08-06T00:00:00", "2007-08-13T00:00:00", "2007-08-20T00:00:00", "2007-08-27T00:00:00", "2007-09-03T00:00:00", "2007-09-10T00:00:00", "2007-09-17T00:00:00", "2007-09-24T00:00:00", "2007-10-01T00:00:00", "2007-10-08T00:00:00", "2007-10-15T00:00:00", "2007-10-22T00:00:00", "2007-10-29T00:00:00", "2007-11-05T00:00:00", "2007-11-12T00:00:00", "2007-11-19T00:00:00", "2007-11-26T00:00:00", "2007-12-03T00:00:00", "2007-12-10T00:00:00", "2007-12-17T00:00:00", "2007-12-24T00:00:00", "2007-12-31T00:00:00", "2008-01-07T00:00:00", "2008-01-14T00:00:00", "2008-01-21T00:00:00", "2008-01-28T00:00:00", "2008-02-04T00:00:00", "2008-02-11T00:00:00", "2008-02-18T00:00:00", "2008-02-25T00:00:00", "2008-03-03T00:00:00", "2008-03-10T00:00:00", "2008-03-17T00:00:00", "2008-03-24T00:00:00", "2008-03-31T00:00:00", "2008-04-07T00:00:00", "2008-04-14T00:00:00", "2008-04-21T00:00:00", "2008-04-28T00:00:00", "2008-05-05T00:00:00", "2008-05-12T00:00:00", "2008-05-19T00:00:00", "2008-05-26T00:00:00", "2008-06-02T00:00:00", "2008-06-09T00:00:00", "2008-06-16T00:00:00", "2008-06-23T00:00:00", "2008-06-30T00:00:00", "2008-07-07T00:00:00", "2008-07-14T00:00:00", "2008-07-21T00:00:00", "2008-07-28T00:00:00", "2008-08-04T00:00:00", "2008-08-11T00:00:00", "2008-08-18T00:00:00", "2008-08-25T00:00:00", "2008-09-01T00:00:00", "2008-09-08T00:00:00", "2008-09-15T00:00:00", "2008-09-22T00:00:00", "2008-09-29T00:00:00", "2008-10-06T00:00:00", "2008-10-13T00:00:00", "2008-10-20T00:00:00", "2008-10-27T00:00:00", "2008-11-03T00:00:00", "2008-11-10T00:00:00", "2008-11-17T00:00:00", "2008-11-24T00:00:00", "2008-12-01T00:00:00", "2008-12-08T00:00:00", "2008-12-15T00:00:00", "2008-12-22T00:00:00", "2008-12-29T00:00:00", "2009-01-05T00:00:00", "2009-01-12T00:00:00", "2009-01-19T00:00:00", "2009-01-26T00:00:00", "2009-02-02T00:00:00", "2009-02-09T00:00:00", "2009-02-16T00:00:00", "2009-02-23T00:00:00", "2009-03-02T00:00:00", "2009-03-09T00:00:00", "2009-03-16T00:00:00", "2009-03-23T00:00:00", "2009-03-30T00:00:00", "2009-04-06T00:00:00", "2009-04-13T00:00:00", "2009-04-20T00:00:00", "2009-04-27T00:00:00", "2009-05-04T00:00:00", "2009-05-11T00:00:00", "2009-05-18T00:00:00", "2009-05-25T00:00:00", "2009-06-01T00:00:00", "2009-06-08T00:00:00", "2009-06-15T00:00:00", "2009-06-22T00:00:00", "2009-06-29T00:00:00", "2009-07-06T00:00:00", "2009-07-13T00:00:00", "2009-07-20T00:00:00", "2009-07-27T00:00:00", "2009-08-03T00:00:00", "2009-08-10T00:00:00", "2009-08-17T00:00:00", "2009-08-24T00:00:00", "2009-08-31T00:00:00", "2009-09-07T00:00:00", "2009-09-14T00:00:00", "2009-09-21T00:00:00", "2009-09-28T00:00:00", "2009-10-05T00:00:00", "2009-10-12T00:00:00", "2009-10-19T00:00:00", "2009-10-26T00:00:00", "2009-11-02T00:00:00", "2009-11-09T00:00:00", "2009-11-16T00:00:00", "2009-11-23T00:00:00", "2009-11-30T00:00:00", "2009-12-07T00:00:00", "2009-12-14T00:00:00", "2009-12-21T00:00:00", "2009-12-28T00:00:00", "2010-01-04T00:00:00", "2010-01-11T00:00:00", "2010-01-18T00:00:00", "2010-01-25T00:00:00", "2010-02-01T00:00:00", "2010-02-08T00:00:00", "2010-02-15T00:00:00", "2010-02-22T00:00:00", "2010-03-01T00:00:00", "2010-03-08T00:00:00", "2010-03-15T00:00:00", "2010-03-22T00:00:00", "2010-03-29T00:00:00", "2010-04-05T00:00:00", "2010-04-12T00:00:00", "2010-04-19T00:00:00", "2010-04-26T00:00:00", "2010-05-03T00:00:00", "2010-05-10T00:00:00", "2010-05-17T00:00:00", "2010-05-24T00:00:00", "2010-05-31T00:00:00", "2010-06-07T00:00:00", "2010-06-14T00:00:00", "2010-06-21T00:00:00", "2010-06-28T00:00:00", "2010-07-05T00:00:00", "2010-07-12T00:00:00", "2010-07-19T00:00:00", "2010-07-26T00:00:00", "2010-08-02T00:00:00", "2010-08-09T00:00:00", "2010-08-16T00:00:00", "2010-08-23T00:00:00", "2010-08-30T00:00:00", "2010-09-06T00:00:00", "2010-09-13T00:00:00", "2010-09-20T00:00:00", "2010-09-27T00:00:00", "2010-10-04T00:00:00", "2010-10-11T00:00:00", "2010-10-18T00:00:00", "2010-10-25T00:00:00", "2010-11-01T00:00:00", "2010-11-08T00:00:00", "2010-11-15T00:00:00", "2010-11-22T00:00:00", "2010-11-29T00:00:00", "2010-12-06T00:00:00", "2010-12-13T00:00:00", "2010-12-20T00:00:00", "2010-12-27T00:00:00", "2011-01-03T00:00:00", "2011-01-10T00:00:00", "2011-01-17T00:00:00", "2011-01-24T00:00:00", "2011-01-31T00:00:00", "2011-02-07T00:00:00", "2011-02-14T00:00:00", "2011-02-21T00:00:00", "2011-02-28T00:00:00", "2011-03-07T00:00:00", "2011-03-14T00:00:00", "2011-03-21T00:00:00", "2011-03-28T00:00:00", "2011-04-04T00:00:00", "2011-04-11T00:00:00", "2011-04-18T00:00:00", "2011-04-25T00:00:00", "2011-05-02T00:00:00", "2011-05-09T00:00:00", "2011-05-16T00:00:00", "2011-05-23T00:00:00", "2011-05-30T00:00:00", "2011-06-06T00:00:00", "2011-06-13T00:00:00", "2011-06-20T00:00:00", "2011-06-27T00:00:00", "2011-07-04T00:00:00", "2011-07-11T00:00:00", "2011-07-18T00:00:00", "2011-07-25T00:00:00", "2011-08-01T00:00:00", "2011-08-08T00:00:00", "2011-08-15T00:00:00", "2011-08-22T00:00:00", "2011-08-29T00:00:00", "2011-09-05T00:00:00", "2011-09-12T00:00:00", "2011-09-19T00:00:00", "2011-09-26T00:00:00", "2011-10-03T00:00:00", "2011-10-10T00:00:00", "2011-10-17T00:00:00", "2011-10-24T00:00:00", "2011-10-31T00:00:00", "2011-11-07T00:00:00", "2011-11-14T00:00:00", "2011-11-21T00:00:00", "2011-11-28T00:00:00", "2011-12-05T00:00:00", "2011-12-12T00:00:00", "2011-12-19T00:00:00", "2011-12-26T00:00:00", "2012-01-02T00:00:00", "2012-01-09T00:00:00", "2012-01-16T00:00:00", "2012-01-23T00:00:00", "2012-01-30T00:00:00", "2012-02-06T00:00:00", "2012-02-13T00:00:00", "2012-02-20T00:00:00", "2012-02-27T00:00:00", "2012-03-05T00:00:00", "2012-03-12T00:00:00", "2012-03-19T00:00:00", "2012-03-26T00:00:00", "2012-04-02T00:00:00", "2012-04-09T00:00:00", "2012-04-16T00:00:00", "2012-04-23T00:00:00", "2012-04-30T00:00:00", "2012-05-07T00:00:00", "2012-05-14T00:00:00", "2012-05-21T00:00:00", "2012-05-28T00:00:00", "2012-06-04T00:00:00", "2012-06-11T00:00:00", "2012-06-18T00:00:00", "2012-06-25T00:00:00", "2012-07-02T00:00:00", "2012-07-09T00:00:00", "2012-07-16T00:00:00", "2012-07-23T00:00:00", "2012-07-30T00:00:00", "2012-08-06T00:00:00", "2012-08-13T00:00:00", "2012-08-20T00:00:00", "2012-08-27T00:00:00", "2012-09-03T00:00:00", "2012-09-10T00:00:00", "2012-09-17T00:00:00", "2012-09-24T00:00:00", "2012-10-01T00:00:00", "2012-10-08T00:00:00", "2012-10-15T00:00:00", "2012-10-22T00:00:00", "2012-10-29T00:00:00", "2012-11-05T00:00:00", "2012-11-12T00:00:00", "2012-11-19T00:00:00", "2012-11-26T00:00:00", "2012-12-03T00:00:00", "2012-12-10T00:00:00", "2012-12-17T00:00:00", "2012-12-24T00:00:00", "2012-12-31T00:00:00", "2013-01-07T00:00:00", "2013-01-14T00:00:00", "2013-01-21T00:00:00", "2013-01-28T00:00:00", "2013-02-04T00:00:00", "2013-02-11T00:00:00", "2013-02-18T00:00:00", "2013-02-25T00:00:00", "2013-03-04T00:00:00", "2013-03-11T00:00:00", "2013-03-18T00:00:00", "2013-03-25T00:00:00", "2013-04-01T00:00:00", "2013-04-08T00:00:00", "2013-04-15T00:00:00", "2013-04-22T00:00:00", "2013-04-29T00:00:00", "2013-05-06T00:00:00", "2013-05-13T00:00:00", "2013-05-20T00:00:00", "2013-05-27T00:00:00", "2013-06-03T00:00:00", "2013-06-10T00:00:00", "2013-06-17T00:00:00", "2013-06-24T00:00:00", "2013-07-01T00:00:00", "2013-07-08T00:00:00", "2013-07-15T00:00:00", "2013-07-22T00:00:00", "2013-07-29T00:00:00", "2013-08-05T00:00:00", "2013-08-12T00:00:00", "2013-08-19T00:00:00", "2013-08-26T00:00:00", "2013-09-02T00:00:00", "2013-09-09T00:00:00", "2013-09-16T00:00:00", "2013-09-23T00:00:00", "2013-09-30T00:00:00", "2013-10-07T00:00:00", "2013-10-14T00:00:00", "2013-10-21T00:00:00", "2013-10-28T00:00:00", "2013-11-04T00:00:00", "2013-11-11T00:00:00", "2013-11-18T00:00:00", "2013-11-25T00:00:00", "2013-12-02T00:00:00", "2013-12-09T00:00:00", "2013-12-16T00:00:00", "2013-12-23T00:00:00", "2013-12-30T00:00:00", "2014-01-06T00:00:00", "2014-01-13T00:00:00", "2014-01-20T00:00:00", "2014-01-27T00:00:00", "2014-02-03T00:00:00", "2014-02-10T00:00:00", "2014-02-17T00:00:00", "2014-02-24T00:00:00", "2014-03-03T00:00:00", "2014-03-10T00:00:00", "2014-03-17T00:00:00", "2014-03-24T00:00:00", "2014-03-31T00:00:00", "2014-04-07T00:00:00", "2014-04-14T00:00:00", "2014-04-21T00:00:00", "2014-04-28T00:00:00", "2014-05-05T00:00:00", "2014-05-12T00:00:00", "2014-05-19T00:00:00", "2014-05-26T00:00:00", "2014-06-02T00:00:00", "2014-06-09T00:00:00", "2014-06-16T00:00:00", "2014-06-23T00:00:00", "2014-06-30T00:00:00", "2014-07-07T00:00:00", "2014-07-14T00:00:00", "2014-07-21T00:00:00", "2014-07-28T00:00:00", "2014-08-04T00:00:00", "2014-08-11T00:00:00", "2014-08-18T00:00:00", "2014-08-25T00:00:00", "2014-09-01T00:00:00", "2014-09-08T00:00:00", "2014-09-15T00:00:00", "2014-09-22T00:00:00", "2014-09-29T00:00:00", "2014-10-06T00:00:00", "2014-10-13T00:00:00", "2014-10-20T00:00:00", "2014-10-27T00:00:00", "2014-11-03T00:00:00", "2014-11-10T00:00:00", "2014-11-17T00:00:00", "2014-11-24T00:00:00", "2014-12-01T00:00:00", "2014-12-08T00:00:00", "2014-12-15T00:00:00", "2014-12-22T00:00:00", "2014-12-29T00:00:00", "2015-01-05T00:00:00", "2015-01-12T00:00:00", "2015-01-19T00:00:00", "2015-01-26T00:00:00", "2015-02-02T00:00:00", "2015-02-09T00:00:00", "2015-02-16T00:00:00", "2015-02-23T00:00:00", "2015-03-02T00:00:00", "2015-03-09T00:00:00", "2015-03-16T00:00:00", "2015-03-23T00:00:00", "2015-03-30T00:00:00", "2015-04-06T00:00:00", "2015-04-13T00:00:00", "2015-04-20T00:00:00", "2015-04-27T00:00:00", "2015-05-04T00:00:00", "2015-05-11T00:00:00", "2015-05-18T00:00:00", "2015-05-25T00:00:00", "2015-06-01T00:00:00", "2015-06-08T00:00:00", "2015-06-15T00:00:00", "2015-06-22T00:00:00", "2015-06-29T00:00:00", "2015-07-06T00:00:00", "2015-07-13T00:00:00", "2015-07-20T00:00:00", "2015-07-27T00:00:00", "2015-08-03T00:00:00", "2015-08-10T00:00:00", "2015-08-17T00:00:00", "2015-08-24T00:00:00", "2015-08-31T00:00:00", "2015-09-07T00:00:00", "2015-09-14T00:00:00", "2015-09-21T00:00:00", "2015-09-28T00:00:00", "2015-10-05T00:00:00", "2015-10-12T00:00:00", "2015-10-19T00:00:00", "2015-10-26T00:00:00", "2015-11-02T00:00:00", "2015-11-09T00:00:00", "2015-11-16T00:00:00", "2015-11-23T00:00:00", "2015-11-30T00:00:00", "2015-12-07T00:00:00", "2015-12-14T00:00:00", "2015-12-21T00:00:00", "2015-12-28T00:00:00", "2016-01-04T00:00:00", "2016-01-11T00:00:00", "2016-01-18T00:00:00", "2016-01-25T00:00:00", "2016-02-01T00:00:00", "2016-02-08T00:00:00", "2016-02-15T00:00:00", "2016-02-22T00:00:00", "2016-02-29T00:00:00", "2016-03-07T00:00:00", "2016-03-14T00:00:00", "2016-03-21T00:00:00", "2016-03-28T00:00:00", "2016-04-04T00:00:00", "2016-04-11T00:00:00", "2016-04-18T00:00:00", "2016-04-25T00:00:00", "2016-05-02T00:00:00", "2016-05-09T00:00:00", "2016-05-16T00:00:00", "2016-05-23T00:00:00", "2016-05-30T00:00:00", "2016-06-06T00:00:00", "2016-06-13T00:00:00", "2016-06-20T00:00:00", "2016-06-27T00:00:00", "2016-07-04T00:00:00", "2016-07-11T00:00:00", "2016-07-18T00:00:00", "2016-07-25T00:00:00", "2016-08-01T00:00:00", "2016-08-08T00:00:00", "2016-08-15T00:00:00", "2016-08-22T00:00:00", "2016-08-29T00:00:00", "2016-09-05T00:00:00", "2016-09-12T00:00:00", "2016-09-19T00:00:00", "2016-09-26T00:00:00", "2016-10-03T00:00:00", "2016-10-10T00:00:00", "2016-10-17T00:00:00", "2016-10-24T00:00:00", "2016-10-31T00:00:00", "2016-11-07T00:00:00", "2016-11-14T00:00:00", "2016-11-21T00:00:00", "2016-11-28T00:00:00", "2016-12-05T00:00:00", "2016-12-12T00:00:00", "2016-12-19T00:00:00", "2016-12-26T00:00:00", "2017-01-02T00:00:00", "2017-01-09T00:00:00", "2017-01-16T00:00:00", "2017-01-23T00:00:00", "2017-01-30T00:00:00", "2017-02-06T00:00:00", "2017-02-13T00:00:00", "2017-02-20T00:00:00", "2017-02-27T00:00:00", "2017-03-06T00:00:00", "2017-03-13T00:00:00", "2017-03-20T00:00:00", "2017-03-27T00:00:00", "2017-04-03T00:00:00", "2017-04-10T00:00:00", "2017-04-17T00:00:00", "2017-04-24T00:00:00", "2017-05-01T00:00:00", "2017-05-08T00:00:00", "2017-05-15T00:00:00", "2017-05-22T00:00:00", "2017-05-29T00:00:00", "2017-06-05T00:00:00", "2017-06-12T00:00:00", "2017-06-19T00:00:00", "2017-06-26T00:00:00", "2017-07-03T00:00:00", "2017-07-10T00:00:00", "2017-07-17T00:00:00", "2017-07-24T00:00:00", "2017-07-31T00:00:00", "2017-08-07T00:00:00", "2017-08-14T00:00:00", "2017-08-21T00:00:00", "2017-08-28T00:00:00", "2017-09-04T00:00:00", "2017-09-11T00:00:00", "2017-09-18T00:00:00", "2017-09-25T00:00:00", "2017-10-02T00:00:00", "2017-10-09T00:00:00", "2017-10-16T00:00:00", "2017-10-23T00:00:00", "2017-10-30T00:00:00", "2017-11-06T00:00:00", "2017-11-13T00:00:00", "2017-11-20T00:00:00", "2017-11-27T00:00:00", "2017-12-04T00:00:00", "2017-12-11T00:00:00", "2017-12-18T00:00:00", "2017-12-25T00:00:00", "2018-01-01T00:00:00", "2018-01-08T00:00:00", "2018-01-15T00:00:00", "2018-01-22T00:00:00", "2018-01-29T00:00:00", "2018-02-05T00:00:00", "2018-02-12T00:00:00", "2018-02-19T00:00:00", "2018-02-26T00:00:00", "2018-03-05T00:00:00", "2018-03-12T00:00:00", "2018-03-19T00:00:00", "2018-03-26T00:00:00", "2018-04-02T00:00:00", "2018-04-09T00:00:00", "2018-04-16T00:00:00", "2018-04-23T00:00:00", "2018-04-30T00:00:00", "2018-05-07T00:00:00", "2018-05-14T00:00:00", "2018-05-21T00:00:00", "2018-05-28T00:00:00", "2018-06-04T00:00:00", "2018-06-11T00:00:00", "2018-06-18T00:00:00", "2018-06-25T00:00:00", "2018-07-02T00:00:00", "2018-07-09T00:00:00", "2018-07-16T00:00:00", "2018-07-23T00:00:00", "2018-07-30T00:00:00", "2018-08-06T00:00:00", "2018-08-13T00:00:00", "2018-08-20T00:00:00", "2018-08-27T00:00:00", "2018-09-03T00:00:00", "2018-09-10T00:00:00", "2018-09-17T00:00:00", "2018-09-24T00:00:00", "2018-10-01T00:00:00", "2018-10-08T00:00:00", "2018-10-15T00:00:00", "2018-10-22T00:00:00", "2018-10-29T00:00:00", "2018-11-05T00:00:00", "2018-11-12T00:00:00", "2018-11-19T00:00:00", "2018-11-26T00:00:00", "2018-12-03T00:00:00", "2018-12-10T00:00:00", "2018-12-17T00:00:00", "2018-12-24T00:00:00", "2018-12-31T00:00:00", "2019-01-07T00:00:00", "2019-01-14T00:00:00", "2019-01-21T00:00:00", "2019-01-28T00:00:00", "2019-02-04T00:00:00", "2019-02-11T00:00:00", "2019-02-18T00:00:00", "2019-02-25T00:00:00", "2019-03-04T00:00:00", "2019-03-11T00:00:00", "2019-03-18T00:00:00", "2019-03-25T00:00:00", "2019-04-01T00:00:00", "2019-04-08T00:00:00", "2019-04-15T00:00:00", "2019-04-22T00:00:00", "2019-04-29T00:00:00", "2019-05-06T00:00:00", "2019-05-13T00:00:00", "2019-05-20T00:00:00", "2019-05-27T00:00:00", "2019-06-03T00:00:00", "2019-06-10T00:00:00", "2019-06-17T00:00:00", "2019-06-24T00:00:00", "2019-07-01T00:00:00", "2019-07-08T00:00:00", "2019-07-15T00:00:00", "2019-07-22T00:00:00", "2019-07-29T00:00:00", "2019-08-05T00:00:00", "2019-08-12T00:00:00", "2019-08-19T00:00:00", "2019-08-26T00:00:00", "2019-09-02T00:00:00", "2019-09-09T00:00:00", "2019-09-16T00:00:00", "2019-09-23T00:00:00", "2019-09-30T00:00:00", "2019-10-07T00:00:00", "2019-10-14T00:00:00", "2019-10-21T00:00:00", "2019-10-28T00:00:00", "2019-11-04T00:00:00", "2019-11-11T00:00:00", "2019-11-18T00:00:00", "2019-11-25T00:00:00", "2019-12-02T00:00:00", "2019-12-09T00:00:00", "2019-12-16T00:00:00", "2019-12-23T00:00:00", "2019-12-30T00:00:00", "2020-01-06T00:00:00", "2020-01-13T00:00:00", "2020-01-20T00:00:00", "2020-01-27T00:00:00", "2020-02-03T00:00:00", "2020-02-10T00:00:00", "2020-02-17T00:00:00", "2020-02-24T00:00:00", "2020-03-02T00:00:00", "2020-03-09T00:00:00", "2020-03-16T00:00:00", "2020-03-23T00:00:00", "2020-03-30T00:00:00", "2020-04-06T00:00:00", "2020-04-13T00:00:00", "2020-04-20T00:00:00", "2020-04-27T00:00:00", "2020-05-04T00:00:00", "2020-05-11T00:00:00", "2020-05-18T00:00:00", "2020-05-25T00:00:00", "2020-06-01T00:00:00", "2020-06-08T00:00:00", "2020-06-15T00:00:00", "2020-06-22T00:00:00", "2020-06-29T00:00:00", "2020-07-06T00:00:00", "2020-07-13T00:00:00", "2020-07-20T00:00:00", "2020-07-27T00:00:00", "2020-08-03T00:00:00", "2020-08-10T00:00:00", "2020-08-17T00:00:00", "2020-08-24T00:00:00", "2020-08-31T00:00:00", "2020-09-07T00:00:00", "2020-09-14T00:00:00", "2020-09-21T00:00:00", "2020-09-28T00:00:00", "2020-10-05T00:00:00", "2020-10-12T00:00:00", "2020-10-19T00:00:00", "2020-10-26T00:00:00", "2020-11-02T00:00:00", "2020-11-09T00:00:00", "2020-11-16T00:00:00", "2020-11-23T00:00:00", "2020-11-30T00:00:00", "2020-12-07T00:00:00", "2020-12-14T00:00:00", "2020-12-21T00:00:00", "2020-12-28T00:00:00", "2021-01-04T00:00:00", "2021-01-11T00:00:00", "2021-01-18T00:00:00", "2021-01-25T00:00:00", "2021-02-01T00:00:00", "2021-02-08T00:00:00", "2021-02-15T00:00:00", "2021-02-22T00:00:00", "2021-03-01T00:00:00", "2021-03-08T00:00:00", "2021-03-15T00:00:00", "2021-03-22T00:00:00", "2021-03-29T00:00:00", "2021-04-05T00:00:00", "2021-04-12T00:00:00", "2021-04-19T00:00:00", "2021-04-26T00:00:00", "2021-05-03T00:00:00", "2021-05-10T00:00:00", "2021-05-17T00:00:00", "2021-05-24T00:00:00", "2021-05-31T00:00:00", "2021-06-07T00:00:00", "2021-06-14T00:00:00", "2021-06-21T00:00:00", "2021-06-28T00:00:00", "2021-07-05T00:00:00", "2021-07-12T00:00:00", "2021-07-19T00:00:00", "2021-07-26T00:00:00", "2021-08-02T00:00:00", "2021-08-09T00:00:00", "2021-08-16T00:00:00", "2021-08-23T00:00:00", "2021-08-30T00:00:00", "2021-09-06T00:00:00", "2021-09-13T00:00:00", "2021-09-20T00:00:00", "2021-09-27T00:00:00", "2021-10-04T00:00:00", "2021-10-11T00:00:00", "2021-10-18T00:00:00", "2021-10-25T00:00:00", "2021-11-01T00:00:00", "2021-11-08T00:00:00", "2021-11-15T00:00:00", "2021-11-22T00:00:00", "2021-11-29T00:00:00", "2021-12-06T00:00:00", "2021-12-13T00:00:00", "2021-12-20T00:00:00", "2021-12-27T00:00:00", "2022-01-03T00:00:00", "2022-01-10T00:00:00", "2022-01-17T00:00:00", "2022-01-24T00:00:00", "2022-01-31T00:00:00", "2022-02-07T00:00:00", "2022-02-14T00:00:00", "2022-02-21T00:00:00", "2022-02-28T00:00:00", "2022-03-07T00:00:00", "2022-03-14T00:00:00", "2022-03-21T00:00:00", "2022-03-28T00:00:00", "2022-04-04T00:00:00", "2022-04-11T00:00:00", "2022-04-18T00:00:00", "2022-04-25T00:00:00", "2022-05-02T00:00:00", "2022-05-09T00:00:00", "2022-05-16T00:00:00", "2022-05-23T00:00:00", "2022-05-30T00:00:00", "2022-06-06T00:00:00", "2022-06-13T00:00:00", "2022-06-20T00:00:00", "2022-06-27T00:00:00", "2022-07-04T00:00:00", "2022-07-11T00:00:00", "2022-07-18T00:00:00", "2022-07-25T00:00:00", "2022-08-01T00:00:00", "2022-08-08T00:00:00", "2022-08-15T00:00:00", "2022-08-22T00:00:00", "2022-08-29T00:00:00", "2022-09-05T00:00:00", "2022-09-12T00:00:00", "2022-09-19T00:00:00", "2022-09-26T00:00:00", "2022-10-03T00:00:00", "2022-10-10T00:00:00", "2022-10-17T00:00:00", "2022-10-24T00:00:00", "2022-10-31T00:00:00", "2022-11-07T00:00:00", "2022-11-14T00:00:00", "2022-11-21T00:00:00", "2022-11-28T00:00:00", "2022-12-05T00:00:00", "2022-12-12T00:00:00", "2022-12-19T00:00:00", "2022-12-26T00:00:00", "2023-01-02T00:00:00", "2023-01-09T00:00:00", "2023-01-16T00:00:00", "2023-01-23T00:00:00", "2023-01-30T00:00:00", "2023-02-06T00:00:00", "2023-02-13T00:00:00", "2023-02-20T00:00:00", "2023-02-27T00:00:00", "2023-03-06T00:00:00", "2023-03-13T00:00:00", "2023-03-20T00:00:00", "2023-03-27T00:00:00", "2023-04-03T00:00:00", "2023-04-10T00:00:00", "2023-04-17T00:00:00", "2023-04-24T00:00:00", "2023-05-01T00:00:00", "2023-05-08T00:00:00", "2023-05-15T00:00:00", "2023-05-22T00:00:00", "2023-05-29T00:00:00", "2023-06-05T00:00:00", "2023-06-12T00:00:00", "2023-06-19T00:00:00", "2023-06-26T00:00:00", "2023-07-03T00:00:00", "2023-07-10T00:00:00", "2023-07-17T00:00:00", "2023-07-24T00:00:00", "2023-07-31T00:00:00", "2023-08-07T00:00:00", "2023-08-14T00:00:00", "2023-08-21T00:00:00", "2023-08-28T00:00:00", "2023-09-04T00:00:00", "2023-09-11T00:00:00", "2023-09-18T00:00:00", "2023-09-25T00:00:00", "2023-10-02T00:00:00", "2023-10-09T00:00:00", "2023-10-16T00:00:00", "2023-10-23T00:00:00", "2023-10-30T00:00:00", "2023-11-06T00:00:00", "2023-11-13T00:00:00", "2023-11-20T00:00:00", "2023-11-27T00:00:00", "2023-12-04T00:00:00", "2023-12-11T00:00:00", "2023-12-18T00:00:00", "2023-12-25T00:00:00", "2024-01-01T00:00:00", "2024-01-08T00:00:00", "2024-01-15T00:00:00", "2024-01-22T00:00:00", "2024-01-29T00:00:00", "2024-02-05T00:00:00", "2024-02-12T00:00:00", "2024-02-19T00:00:00", "2024-02-26T00:00:00", "2024-03-04T00:00:00", "2024-03-11T00:00:00", "2024-03-18T00:00:00", "2024-03-25T00:00:00", "2024-04-01T00:00:00", "2024-04-08T00:00:00", "2024-04-15T00:00:00", "2024-04-22T00:00:00", "2024-04-29T00:00:00", "2024-05-06T00:00:00", "2024-05-13T00:00:00", "2024-05-20T00:00:00", "2024-05-27T00:00:00", "2024-06-03T00:00:00", "2024-06-10T00:00:00", "2024-06-17T00:00:00", "2024-06-24T00:00:00", "2024-07-01T00:00:00", "2024-07-08T00:00:00", "2024-07-15T00:00:00", "2024-07-22T00:00:00", "2024-07-29T00:00:00", "2024-08-05T00:00:00", "2024-08-12T00:00:00", "2024-08-19T00:00:00"], "y": [3.2093862764241465, 3.336992466928681, 3.3786267276250554, 3.383002334881144, 3.3461256023206074, 3.3587079414379772, 3.2654186558922285, 3.171091355440647, 3.182677774923969, 3.1594860392607598, 3.1625708470047087, 3.093714674210798, 3.0773566152927336, 3.1987344692707196, 3.2604700524555885, 3.2267012650828675, 3.1421263279104146, 3.1121322775588314, 3.104477675993051, 3.2000758801702234, 3.2273932070183045, 3.192541954414075, 3.1598650789738008, 3.155059684895724, 3.195197324071253, 3.049060939285809, 3.145745918041151, 3.050221611664026, 3.035645087979539, 2.98470361282903, 2.9410624192464514, 2.869230738052955, 2.9373317210359033, 2.921072737651041, 2.8760895069803616, 2.9124387763323054, 2.820082253859893, 2.715727248887526, 2.828355633732829, 2.8059310041675283, 2.660936494874688, 2.641961474977388, 2.6184356646584237, 2.603473775535775, 2.6457314365120164, 2.6207411908773355, 2.571322791464078, 2.520568587060067, 2.5121313676682484, 2.404162213981263, 2.4489797535036213, 2.5027522253333974, 2.4726676813196273, 2.3874038646723674, 2.2639558153309074, 2.2507708769538333, 2.2093024662827196, 2.179135222249975, 2.23060732693164, 2.215280309219544, 2.2688018439881854, 2.1854898194205674, 2.301730314538986, 2.4754276553874575, 2.47526563132696, 2.6359986087984, 2.469879654933536, 2.413669078470134, 2.4334533180264257, 2.426763124103788, 2.4022948417057894, 2.4495477075022807, 2.4531968001198927, 2.5412484170647565, 2.564685328023417, 2.4102142654494356, 2.454758819276003, 2.3840381031775126, 2.4379193782806396, 2.574974020347366, 2.5733011645393313, 2.5058864290541725, 2.513218765660303, 2.402532492741525, 2.366765836902893, 2.4197818693425623, 2.3587933244806893, 2.3030107423679174, 2.356913120416966, 2.3309721921593405, 2.294007578984475, 2.3323124072088035, 2.395255573488837, 2.3926667521177962, 2.285890233627757, 2.448165815983092, 2.407526549385521, 2.343155804405052, 2.205927827963863, 2.264337476781142, 2.211726449599872, 2.149904358489355, 2.150143329005551, 2.231822470737654, 2.1991036813612133, 2.106908526586688, 2.1857231791724954, 2.0969245875102236, 2.065999264206137, 2.0366346031952216, 2.143534617335799, 2.200191866028813, 2.267070886229902, 2.2694480585152568, 2.2222222650958487, 2.235734269840089, 2.2934204866530563, 2.365845977004214, 2.2693840055150494, 2.172868999362355, 2.088105801093596, 2.003150056890549, 2.068621724020218, 2.0874471355056223, 2.1158159252739543, 2.0776750688013057, 2.1490632597804162, 2.05866454046706, 2.145495903173334, 2.2072200500061503, 2.2130209855617524, 2.18500576641316, 2.230312105096644, 2.37883435436553, 2.2071796795227336, 2.20104533468253, 2.2095151386479026, 2.249388673509027, 2.143071454990553, 2.096774116639168, 2.1055651587159154, 2.135607462018651, 2.0715835787889754, 2.139917616981538, 2.145999363647264, 2.1312394207941776, 2.169520147041114, 2.1914831372263697, 2.1267806702529604, 2.2505801924625475, 2.2551842305342773, 2.1905154854827376, 2.3577000010140194, 2.2438956383587216, 2.175110118133356, 2.2087578427169774, 2.1373434847125377, 2.190148327772523, 2.1554018858308783, 2.16081721963393, 2.1441702580887214, 2.2577152279288883, 2.3032654282855303, 2.3991394535682993, 2.289791779469493, 2.4369310185565127, 2.4602140585143726, 2.3919055499435755, 2.2563130566568086, 2.285786373860571, 2.384350384923986, 2.4096239362547864, 2.507331359435756, 2.4896920131950226, 2.5090208135823113, 2.5785646261972373, 2.6825668260466586, 2.749448472763155, 2.841869646904348, 2.923686644457744, 3.0331466683736577, 3.2893081880965322, 3.387992116645053, 3.3341642158685634, 3.311409197271097, 3.3430301781856655, 3.204216873480312, 3.2068310685951325, 3.1257442702501286, 3.3678303930230276, 3.1460390061140973, 3.1253228815951086, 3.1489304377938634, 3.119363392695824, 3.170478345406335, 3.243654815073546, 3.155538559924728, 3.047421657544212, 3.02455083267956, 3.0019855385951173, 3.0630179914651916, 3.1435583266743854, 3.2304476807991973, 3.1933418156700766, 3.345268461710352, 3.2066032635909707, 3.3116069194918123, 3.0772022116491473, 3.126704873198816, 3.0973782104796985, 3.1873601333016035, 3.2084772916272786, 3.337417221724896, 3.3563766206689047, 3.4826282015957566, 3.1430617300543546, 3.0840790395559123, 3.144690703900581, 3.179197337031666, 3.2610184818469885, 3.3169203979723156, 3.3129313142385524, 3.1469296990779405, 3.230006928700063, 3.3344654580376742, 3.355963024524766, 3.3988571166992188, 3.3778944437098564, 3.3924768721237704, 3.4602765223636553, 3.4347111744616456, 3.425120731482759, 3.4898929165036163, 3.4991805657941755, 3.423775543122026, 3.4362043801052105, 3.3191391888780872, 3.4315959352189536, 3.436543238114708, 3.504343669726999, 3.5687514346443074, 3.443163285815977, 3.458438905061168, 3.4436782201131186, 3.454588082083761, 3.3900522303032714, 3.533780655031968, 3.539780812377517, 3.7656359367556527, 3.8184370444274967, 3.8546658026575846, 3.803632316199659, 3.5916316525361762, 3.8436982436047487, 3.8721176639755774, 3.898045695257524, 3.9253315281654393, 3.863220410182903, 3.8966135368901678, 3.9306952656397347, 4.045496209094392, 3.9329129468932322, 3.7505568268569913, 3.640913989261133, 3.88025877416584, 3.841151306624097, 4.003581230789902, 3.952472308323012, 4.128479600207964, 4.0084567180899695, 4.237344078947633, 4.268631230786405, 4.324546701477821, 4.3487097711070035, 4.276773229437742, 4.136622517565848, 4.309694356054541, 4.434393604043463, 4.180042698053839, 3.9938852814464783, 3.898579532483414, 3.848238689143483, 4.110434790404273, 4.08530133673333, 4.0458015652211445, 4.003262340511362, 3.944166149484363, 4.009717344816497, 4.106315812886727, 4.275401664714408, 4.306249959128244, 4.27638358246581, 4.538579077581863, 4.808046991647239, 5.137614431864754, 5.117367389345217, 5.2726470526068105, 5.584260329489314, 5.547433865918581, 6.168381803487996, 5.766325724772072, 5.545872803093183, 5.858996335197897, 5.715629345349925, 5.643846936214232, 5.756163075176545, 5.744824632763556, 5.5140416820091875, 5.645872998654015, 5.687111653156162, 5.5453828120664435, 5.6363342350752035, 5.554662256762145, 5.565342358680256, 5.869422919883545, 5.753816460838918, 5.835595437715458, 5.778483547782291, 5.911076091664479, 5.771909028745921, 5.808600285805096, 5.668672623248517, 5.288722111758882, 4.897358347135204, 4.911504440123481, 4.940352928721482, 4.88134018116942, 4.962466432978015, 4.884552761791198, 4.57115172881027, 4.493073105644916, 4.170110656864987, 4.145483509432688, 3.9370575821559135, 4.067494205211465, 3.729495574597059, 3.7550561883476345, 3.945125633018451, 4.152393545315322, 4.197973621514098, 4.272307616013747, 4.610021777093915, 4.6733667560135705, 4.741327435362573, 4.9999999858547275, 5.149547537070352, 5.211705072766784, 5.178281773263256, 5.457654099445026, 5.369818374099285, 5.0355522427003425, 5.078613992848144, 5.069278683385679, 5.047250034087024, 5.22994667268874, 5.180566112048405, 5.328653236412591, 5.532403850928343, 5.430309461677796, 5.489106323278553, 5.380303209478205, 5.181412537301783, 5.064150498261049, 4.805145355117002, 5.037425163977161, 5.068350688486539, 4.651113132818442, 4.778748800864711, 4.8844679672047455, 4.887587692332027, 4.986510351831907, 4.8490717497108635, 4.643324470020713, 4.502487431694585, 4.1175376246469, 3.7747746831303957, 4.016800114391394, 3.622572688223089, 4.036052180493018, 3.8953927111625033, 3.70162626706809, 3.811606692911935, 3.6416150243755347, 3.6397869135063003, 3.664211634403831, 3.660876459537495, 3.488193430264267, 3.5880927813620254, 3.848540711773976, 3.9000885645278784, 4.008679889846631, 3.9625553902219175, 4.044435170158567, 3.8539369230681277, 3.9267074346477133, 4.143563343816304, 4.376787557066864, 4.303269863504213, 4.296755163432301, 4.462917064819239, 4.506483062814953, 4.243640457593229, 4.278642847487605, 4.056828234101746, 4.088808825530319, 4.047353108852933, 4.1296106532227554, 4.251636601648728, 4.179490061295244, 4.158617783606329, 3.9140177990633602, 3.8509350459497886, 3.9974099868101876, 3.9917491581311455, 3.964210897084465, 4.252544492862303, 4.240391582858284, 4.1354155956565455, 3.9161236262880625, 4.220702567602879, 3.7009063829115743, 3.4879373369475126, 3.2633608927906357, 2.5210428332249006, 2.777990144058055, 2.29735291236319, 2.572544661388562, 2.3083742049280307, 2.291891166783081, 1.9843374456864595, 1.9890957672489933, 1.8054630500646054, 1.7169373259279483, 1.560258141386505, 1.4642692746426982, 1.6431497789941132, 1.8090835328782169, 1.8032884513490601, 1.627945940800738, 1.576620329605974, 1.7753582442672435, 1.6303770224337748, 1.4114594245977288, 1.6208178681765093, 1.783780993596135, 1.7837169046083776, 1.8701611704027765, 1.9864572347308291, 2.2325816109267653, 2.346973326177281, 2.549573284739058, 2.266856561433129, 2.3676206536001736, 2.3479877701910286, 2.1785369676241584, 2.1935315199301275, 2.2450960542186498, 2.371841395401248, 2.518349248207997, 2.4000642863759687, 2.4380779385909905, 2.4148489452040445, 2.412299914489225, 2.575757578841146, 2.6416876492368204, 2.7435250784602863, 2.905567885258368, 2.992608353353982, 3.0203524041460876, 3.05433656977511, 2.8580760168190533, 2.8137128432779517, 2.7457390754359325, 2.7580285931396658, 2.6629784041746536, 2.6994654520090573, 2.700580781604385, 2.8647214848120406, 2.834952514079708, 2.6897086683132954, 2.659707879083234, 2.7084786749376475, 2.634559723685176, 2.7494009998631825, 2.7782739210888616, 2.8096866351338523, 2.9625939505264194, 3.0382580836893904, 2.976629904047926, 2.96876390722332, 3.065093805133095, 2.812557713586978, 2.712412301838758, 2.8260669997111543, 2.9938463982428103, 2.9227398255687027, 2.9983254915110145, 3.059010369313396, 3.038197554272731, 3.072812930181239, 3.179272936044963, 3.0871588577699476, 3.0902928195469337, 3.0452692360521882, 2.828150296088526, 2.5876033404641903, 2.544402675917151, 2.5954752137009267, 2.5548589410994103, 2.3125309265881837, 2.359426996163462, 2.2916005041808987, 2.462971663392147, 2.4051681175659216, 2.516947895157484, 2.46085863723498, 2.6816536783023173, 2.798510669750981, 2.7750539161198238, 2.6763518198539034, 2.6817144885241766, 2.72168291425242, 2.796589922214374, 2.7296103450273983, 2.7543901242163065, 2.788194535691061, 2.7987995743700944, 2.802038486091049, 2.796659720417491, 2.862805815206897, 2.749981710759557, 2.8225863221215546, 2.847883335049118, 2.8346399076298194, 2.753431544502571, 2.8426069561402434, 2.9653975830951977, 3.0124763901613774, 3.081159315247467, 3.123988411308671, 3.122031449139184, 3.236915613850619, 3.2065624091982574, 3.256134953597437, 3.3901950112328585, 3.3318626781269196, 3.226120189381342, 3.1490026436148133, 3.1312143081932753, 2.9511081052841206, 3.0577725289297324, 3.091298134787677, 2.9770325624124125, 3.050766805400754, 2.86339435989233, 2.9257583198164334, 2.6770566606889346, 2.6579266045263923, 2.6617114233623806, 2.7299839771595513, 2.7204972792814197, 2.6788611566742464, 2.650791646273034, 2.6667101450261343, 2.7314227090522234, 2.8955000172115595, 2.8565405476689656, 2.770788825333958, 2.7505775765464615, 2.747650827826063, 2.494541359942311, 2.3040455920038543, 2.153983462440618, 2.2847110154901586, 2.1919197209490955, 2.1479745205103966, 2.163236095749311, 1.9981679843582267, 1.9408787543457038, 1.9984704779353337, 2.0246164065474774, 1.9686870216825427, 2.1200320732117803, 2.029852369763853, 1.9367831570285183, 1.972516986561861, 1.9388904701669716, 2.0449341674088477, 2.0708779952232366, 2.0832288371800347, 2.158347434583562, 2.191531390362729, 2.1230122183709588, 2.2276741383070697, 2.248602540633853, 2.24246448186095, 2.2423614820423747, 2.239598341542425, 2.148448781505395, 2.175933753275684, 2.279669846310209, 2.251738796469072, 2.3400785699059186, 2.291704185237049, 2.29078042885633, 2.32821620605019, 2.183714037642847, 2.2507765386594776, 2.2956730368045664, 2.264242772341792, 2.303927773374565, 2.179881954052292, 2.1978581380076117, 2.0425794154764216, 2.064964471759734, 2.080516281784423, 2.1104725841361445, 2.1764889364120634, 2.1569310409084803, 2.199359146428542, 2.179146867604429, 2.117559840878532, 2.0990037680562974, 2.1047107628946335, 2.1171811099161455, 2.088872807570318, 2.050338386704495, 2.1041726036895096, 2.176234562137429, 2.142213425546322, 2.1303145242600534, 2.1286405404926367, 2.112912372246266, 2.1177733122015465, 2.0828218345143545, 2.080520931156018, 1.993584919713533, 2.0145248404943454, 2.014217904310414, 2.1213980130165524, 2.1405516655792094, 2.161221757474125, 2.1430294031930757, 2.1617620715008643, 2.2319640747216827, 2.192469820918807, 2.1721215010092547, 2.197234876287002, 2.261590993119195, 2.253001191321255, 2.319119737292373, 2.244975780069188, 2.2148354579053904, 2.214575640545896, 2.202825681006703, 2.1504171966044967, 2.1287935019949327, 2.120096396095687, 2.231179198608805, 2.2586539642820336, 2.1911117200560537, 2.2628558471827795, 2.335745985902103, 2.4331453145132738, 2.373449650751109, 2.361769418348888, 2.3615329229392095, 2.30988239541444, 2.397414043374089, 2.4926456891366606, 2.5393683417509747, 2.4812176544851425, 2.4356297660559787, 2.352273649415287, 2.4183581348813643, 2.519993898983262, 2.4527959215147574, 2.40166233098898, 2.3100064186155786, 2.349462761753108, 2.4510852624007864, 2.4971858198155155, 2.484683167052362, 2.514698075423555, 2.5749212183786865, 2.5053255642640653, 2.414596280475042, 2.508186739646079, 2.532502935757754, 2.464848890639968, 2.5936495452831796, 2.583160119861902, 2.6574818234628004, 2.7126326216277543, 2.7781927785395704, 2.853383848592156, 2.7503228615665116, 2.716772452537294, 2.699129277410428, 2.612890392105869, 2.596968031427421, 2.5892502034505127, 2.517437374528562, 2.519827709153632, 2.450809791449517, 2.358941901712004, 2.184916614535237, 2.2410179326634205, 2.3643529910433343, 2.335021559889953, 2.325017136776703, 2.3732023438556604, 2.4040902674308473, 2.365653394104845, 2.408529471923829, 2.444135076168644, 2.46206265629154, 2.5176621543435855, 2.438303676084991, 2.3792887268009375, 2.368561147553261, 2.3874146546081128, 2.4727355684146124, 2.4371728490517044, 2.4243814109177597, 2.4764024699191896, 2.4787415846644354, 2.41882483789475, 2.3740896003462884, 2.501955218854431, 2.4381706886078347, 2.494864805301813, 2.522156221250973, 2.54134773208174, 2.4948522401601987, 2.5117430334192323, 2.483210536322566, 2.4234836543540252, 2.4780703697653874, 2.613782061764077, 2.609866683698864, 2.5805907913401156, 2.5344468357667544, 2.4336283304334017, 2.458196770095983, 2.411211180804014, 2.4316413855710013, 2.372207718628707, 2.39333897376744, 2.2948190177741803, 2.0784712922899917, 1.9630975959769115, 1.977317259560631, 2.109571176035498, 2.13086026763605, 2.1674693161391967, 2.239815384126369, 2.2583970480527737, 2.323876447850737, 2.342167438389422, 2.3128854662723546, 2.2882836628142367, 2.2833305823557626, 2.3139911045282715, 2.3417292442157347, 2.495955690113521, 2.46488945795637, 2.406364652586197, 2.3528188936008725, 2.320077324752285, 2.3308783780093445, 2.290040695711695, 2.157303377136408, 2.255094147728284, 2.265262275754842, 2.1987562817789303, 2.212846619732118, 2.1937178143695744, 2.1622979395074666, 2.140115250998927, 2.1241800971840465, 1.989479105182799, 2.0708675054714023, 2.0698733000325165, 2.2310828211948133, 2.1061419258466545, 1.999563779714428, 2.048632494909085, 2.0902879471143034, 2.0336263218139012, 2.0201150227749842, 2.026719165589796, 2.0609598668023854, 2.007772000608996, 1.9082124249634465, 1.941393685733075, 1.912401919543001, 1.9574705100422638, 1.9724254566903394, 1.9601746384210772, 2.0046212070908997, 1.8368554440654907, 1.7769124821354039, 1.8214546832371485, 1.848799575550358, 1.8142166334658203, 1.6358647327471871, 1.6868497807714855, 1.7363501768608418, 1.788329683271811, 1.7784223445560052, 1.8180729931857547, 1.821270588183706, 1.768123138345261, 1.679275597125232, 1.74519503123529, 1.8466673260967403, 1.7677630547514538, 1.6637017420299058, 1.6314175704490803, 1.6436442122088635, 1.7416378291111234, 1.70308848490865, 1.5953353715571383, 1.5880077443224319, 1.5999999913302336, 1.6570659049006753, 1.561624673745803, 1.6814926368894552, 1.6884589288770426, 1.6460340936772464, 1.6110446550554638, 1.6024105333297267, 1.6163086034706617, 1.570185431467057, 1.5668255814197733, 1.570182739059261, 1.647648878999046, 1.6396202471586152, 1.6766921654271567, 1.7267195603597072, 1.6786369247116162, 1.6458644981949813, 1.715797669557626, 1.734826849747308, 2.0486309813829555, 2.0388911763744804, 2.263622615720216, 2.2253425783902614, 2.278764795852831, 2.2557914195127338, 2.184380274318484, 2.171739080677862, 2.165713844131454, 2.242533189173335, 2.1730465804957535, 2.2557024844946247, 2.141567444674148, 2.237524327038465, 2.1860859250465974, 2.1322301015998115, 2.1925744889756213, 2.154160102116848, 2.1788095597736548, 2.101826799669231, 2.1217830570219935, 2.1087458167586774, 1.9982113077984027, 1.9702500613717633, 2.050785854165841, 2.0570704741372006, 2.0534986036004867, 2.054761700386801, 2.0191701075381117, 2.014019395834676, 2.0859281895524364, 2.0434609250398723, 2.08804331196876, 2.1753848536828793, 2.1822770982640787, 2.1881624561232056, 2.164553781773267, 2.2630873797594453, 2.292775871025439, 2.2629495572166336, 2.2870810707433424, 2.3466150811377995, 2.338995884930966, 2.244799487835231, 2.217509784276505, 2.2612695261287845, 2.2922356613343737, 2.3706353889604124, 2.3960814400906294, 2.4671206563457972, 2.438707054718583, 2.4559811269441614, 2.4123702535261424, 2.363404757369484, 2.4605580240290776, 2.397169146020926, 2.3731127847580327, 2.4794704409056307, 2.5203856742010995, 2.5105258248833544, 2.4289933337597116, 2.3998799978197805, 2.377806139545914, 2.354616708378841, 2.381720175684878, 2.303327963919023, 2.396541640212986, 2.4156753699745255, 2.3472864490793257, 2.356321747981026, 2.3587278425820855, 2.2118875145135473, 2.2822799682237, 2.2929648208103344, 2.2810083306540436, 2.342709747970502, 2.307051345274336, 2.3352633008359587, 2.346474623662039, 2.3647498323685596, 2.353256961065632, 2.387627376649406, 2.5367845657245818, 2.4643025989240264, 2.3926936801284966, 2.3583472069454134, 2.2426850468422788, 2.2345918324314122, 2.2334282647404744, 2.2799770655128997, 2.2677483902743476, 2.2562134682332444, 2.2307692615721413, 2.237420021964155, 2.206948114360545, 2.1812164398677396, 2.1987448177577065, 2.3712590245880563, 2.3390683515503086, 2.290209822966536, 2.292915227660486, 2.259038420926973, 2.2263691343110183, 2.277195481112622, 2.2268733571885133, 2.296854348514728, 2.2690417237402865, 2.2762662066178683, 2.2116617141895274, 2.2316086937943678, 2.136305497484211, 2.0931321585405205, 2.067124103517856, 2.074042547719193, 2.139233460237834, 2.1103744909463895, 2.1072215384019994, 2.1389967627485285, 2.1261664983272173, 2.227655788775188, 2.262804687021103, 2.2313030481154676, 2.230373307116067, 2.1622446008363063, 2.2706884058150596, 2.2415529052856806, 2.282659348296232, 2.3016746232750336, 2.3114639055008213, 2.208411628765099, 2.166135328302882, 2.156139589282006, 2.1095090049927996, 2.0263438577919244, 1.9613034401789915, 1.9658981230493549, 1.9381178801604697, 1.91920265388203, 1.9034152280884786, 1.9090005687602865, 1.9258297444502377, 1.8868523665956936, 1.7743498205171184, 1.7265803506475086, 1.7123966374673134, 1.6546574500753926, 1.6674346870380226, 1.7364892166290211, 1.798242638926518, 1.7183041400303471, 1.7230338114556365, 1.6966539060961405, 1.7660349905645991, 1.7638758810151538, 1.7782594808621102, 1.7579575748595382, 1.8326147155368362, 1.795815377071464, 1.8095141017870024, 1.8026746751885172, 1.8579946617976304, 1.8843182306546553, 1.9061505403082857, 1.8737612881066523, 1.8064163111535487, 1.8096308455612649, 1.8280086294675124, 1.709311914118239, 1.5920146200385175, 1.6313910844623696, 1.6462374822875794, 1.591876486307765, 1.6277731881307165, 1.5399807750953192, 1.6322491632984646, 1.4760781812539319, 1.3529158160432966, 1.352757608006115, 1.3117728246865101, 1.393263057899717, 1.3623440960783664, 1.3682502460676718, 1.40856185843749, 1.3308428629839084, 1.3887927786218328, 1.397029217519047, 1.5254742848614455, 1.5043659057866818, 1.497794818127892, 1.5074752786095624, 1.5322309438423192, 1.6054944735383299, 1.5968036109687493, 1.5190006300070993, 1.456592570028729, 1.3882393709297876, 1.4744449904977999, 1.5059961270233553, 1.5244833160836369, 1.5835022240840746, 1.565692973186864, 1.5949491097765496, 1.6003661127921307, 1.5674665645249093, 1.6043240218523984, 1.6124789449106354, 1.6432702227972533, 1.6211249105230603, 1.6146233146639346, 1.6839900293274304, 1.7587845720032653, 1.907794964145828, 1.9140475473801226, 1.9154255005443335, 1.924219164605836, 1.8937176959126545, 1.856214666041194, 2.0028897606094715, 1.9696057669279214, 1.9590990191748476, 1.934444769950034, 2.009497978852965, 2.087176162018454, 2.30318731846355, 2.369365269773558, 2.406949289274721, 2.410875360922052, 2.3679222536405713, 2.3545202106992003, 2.3188531623175277, 2.3234668746779836, 2.349634529701306, 2.4440067164604087, 2.5343744513121607, 2.6028069780740757, 2.5376788575968363, 2.395694532987215, 2.4612351566670605, 2.401047617416613, 2.4190369833249252, 2.354488289580827, 2.419790524290144, 2.4021093478599242, 2.4046961115210093, 2.387710102467078, 2.4461528827303036, 2.4718637766747644, 2.4690340865742075, 2.4712709579714214, 2.3214486393615275, 2.3761423785816658, 2.362499292023533, 2.4854716220966093, 2.4268320729902864, 2.450420085890145, 2.3867387831312863, 2.438649361325474, 2.678814133541831, 2.5207463229675207, 2.454010208737496, 2.3942963656999092, 2.3852990350929764, 2.3814284306559235, 2.3981962390474836, 2.3942199754126277, 2.4022659339960373, 2.378866710953641, 2.42215117170519, 2.4377564562003036, 2.4501947011294867, 2.428571415971273, 2.4634237847913374, 2.410779267432463, 2.481733678957101, 2.447305516592557, 2.3804381317095356, 2.370527496459217, 2.5082692799628084, 2.3281385429740826, 2.45332452081925, 2.3984029855618654, 2.4399457793382684, 2.430984677349538, 2.3933228216544022, 2.373122724457024, 2.30189067644137, 2.265309372929616, 2.312991022489803, 2.324356620607626, 2.3248526912438146, 2.4263031877192534, 2.298156622097577, 2.188657647167182, 2.0585819821862894, 2.0117849290802643, 2.0291854041080737, 1.8999646880577963, 1.938799178209371, 2.033013818703327, 2.004061100101442, 2.043811947155214, 2.1077478272814716, 2.137837671575026, 2.001111164969334, 2.0865867565141034, 2.1301670232284105, 2.0491703125275906, 2.070500398046581, 2.0067626607659914, 2.101784804818361, 2.133858268871804, 2.113930307220218, 2.220926726701905, 2.237259329107853, 2.0731776464796696, 2.0683852109084335, 2.1426582756724044, 2.1556086870295963, 2.101396581980103, 2.1176011590833967, 2.091278855102931, 2.0966098371979984, 2.1932339071000646, 2.2054089578785825, 2.1901897908240415, 2.1810081855878924, 2.1620678800827626, 2.2353834976215063, 2.1865324065357017, 2.2070683424969206, 2.1726636958301513, 1.9867499104287878, 2.069270009006389, 2.0845605977117985, 2.0010934808058862, 2.0514934454422753, 2.010608749216209, 1.9446258645471581, 1.919054212554218, 1.844874680500878, 1.8812857824280314, 1.8895633491934951, 1.9066277603371027, 1.9309958865656833, 1.98350693335244, 1.9832213238999756, 1.9473218388420863, 1.9556640304418476, 2.0009183103851815, 1.9360585851260035, 1.9980616234096662, 1.9823675381562562, 1.9418160400446012, 1.9611897193282397, 1.967453319290872, 1.9654087555587048, 1.9185258463712305, 1.9563861676618683, 1.9037601948347787, 2.01612473339846, 1.980384730050321, 1.8574244465906289, 1.7866330669927415, 1.8274163268415775, 1.8450916337212695, 1.8542377903946718, 1.8861021388593557, 1.8961642494062623, 1.8874939255873286, 1.9116248010317485, 1.9222205914563706, 1.8936851683227347, 1.8815458989264986, 1.8578633309711994, 1.8273318506678708, 1.8687391763494658, 1.909212620779577, 1.8768725056552171, 1.8217763158033662, 1.9080288635982139, 1.9042480211776964, 1.847237624930656, 1.7832552172575231, 1.9063181280549408, 1.8516286875841246, 1.805492992269724, 1.8228920904608377, 1.8069350054709385, 1.8770847391388261, 1.9569126570803028, 1.989343260723179, 1.9826384256972722, 2.096426607487715, 2.0486602701430408, 1.9845883711403258, 1.9497224776238844, 1.952046113444762, 1.9400794972799251, 1.8861967438948324, 1.9503873537992718, 1.9049294842306799, 1.7612188948740966, 1.7245797549977022, 1.6836377923218442, 1.6364458119585235, 1.651925036380235, 1.641722887742609], "yaxis": "y"}, {"name": "US 10-Year Constant Maturity %", "type": "scatter", "x": ["2000-08-28T00:00:00", "2000-09-04T00:00:00", "2000-09-11T00:00:00", "2000-09-18T00:00:00", "2000-09-25T00:00:00", "2000-10-02T00:00:00", "2000-10-09T00:00:00", "2000-10-16T00:00:00", "2000-10-23T00:00:00", "2000-10-30T00:00:00", "2000-11-06T00:00:00", "2000-11-13T00:00:00", "2000-11-20T00:00:00", "2000-11-27T00:00:00", "2000-12-04T00:00:00", "2000-12-11T00:00:00", "2000-12-18T00:00:00", "2000-12-25T00:00:00", "2001-01-01T00:00:00", "2001-01-08T00:00:00", "2001-01-15T00:00:00", "2001-01-22T00:00:00", "2001-01-29T00:00:00", "2001-02-05T00:00:00", "2001-02-12T00:00:00", "2001-02-19T00:00:00", "2001-02-26T00:00:00", "2001-03-05T00:00:00", "2001-03-12T00:00:00", "2001-03-19T00:00:00", "2001-03-26T00:00:00", "2001-04-02T00:00:00", "2001-04-09T00:00:00", "2001-04-16T00:00:00", "2001-04-23T00:00:00", "2001-04-30T00:00:00", "2001-05-07T00:00:00", "2001-05-14T00:00:00", "2001-05-21T00:00:00", "2001-05-28T00:00:00", "2001-06-04T00:00:00", "2001-06-11T00:00:00", "2001-06-18T00:00:00", "2001-06-25T00:00:00", "2001-07-02T00:00:00", "2001-07-09T00:00:00", "2001-07-16T00:00:00", "2001-07-23T00:00:00", "2001-07-30T00:00:00", "2001-08-06T00:00:00", "2001-08-13T00:00:00", "2001-08-20T00:00:00", "2001-08-27T00:00:00", "2001-09-03T00:00:00", "2001-09-10T00:00:00", "2001-09-17T00:00:00", "2001-09-24T00:00:00", "2001-10-01T00:00:00", "2001-10-08T00:00:00", "2001-10-15T00:00:00", "2001-10-22T00:00:00", "2001-10-29T00:00:00", "2001-11-05T00:00:00", "2001-11-12T00:00:00", "2001-11-19T00:00:00", "2001-11-26T00:00:00", "2001-12-03T00:00:00", "2001-12-10T00:00:00", "2001-12-17T00:00:00", "2001-12-24T00:00:00", "2001-12-31T00:00:00", "2002-01-07T00:00:00", "2002-01-14T00:00:00", "2002-01-21T00:00:00", "2002-01-28T00:00:00", "2002-02-04T00:00:00", "2002-02-11T00:00:00", "2002-02-18T00:00:00", "2002-02-25T00:00:00", "2002-03-04T00:00:00", "2002-03-11T00:00:00", "2002-03-18T00:00:00", "2002-03-25T00:00:00", "2002-04-01T00:00:00", "2002-04-08T00:00:00", "2002-04-15T00:00:00", "2002-04-22T00:00:00", "2002-04-29T00:00:00", "2002-05-06T00:00:00", "2002-05-13T00:00:00", "2002-05-20T00:00:00", "2002-05-27T00:00:00", "2002-06-03T00:00:00", "2002-06-10T00:00:00", "2002-06-17T00:00:00", "2002-06-24T00:00:00", "2002-07-01T00:00:00", "2002-07-08T00:00:00", "2002-07-15T00:00:00", "2002-07-22T00:00:00", "2002-07-29T00:00:00", "2002-08-05T00:00:00", "2002-08-12T00:00:00", "2002-08-19T00:00:00", "2002-08-26T00:00:00", "2002-09-02T00:00:00", "2002-09-09T00:00:00", "2002-09-16T00:00:00", "2002-09-23T00:00:00", "2002-09-30T00:00:00", "2002-10-07T00:00:00", "2002-10-14T00:00:00", "2002-10-21T00:00:00", "2002-10-28T00:00:00", "2002-11-04T00:00:00", "2002-11-11T00:00:00", "2002-11-18T00:00:00", "2002-11-25T00:00:00", "2002-12-02T00:00:00", "2002-12-09T00:00:00", "2002-12-16T00:00:00", "2002-12-23T00:00:00", "2002-12-30T00:00:00", "2003-01-06T00:00:00", "2003-01-13T00:00:00", "2003-01-20T00:00:00", "2003-01-27T00:00:00", "2003-02-03T00:00:00", "2003-02-10T00:00:00", "2003-02-17T00:00:00", "2003-02-24T00:00:00", "2003-03-03T00:00:00", "2003-03-10T00:00:00", "2003-03-17T00:00:00", "2003-03-24T00:00:00", "2003-03-31T00:00:00", "2003-04-07T00:00:00", "2003-04-14T00:00:00", "2003-04-21T00:00:00", "2003-04-28T00:00:00", "2003-05-05T00:00:00", "2003-05-12T00:00:00", "2003-05-19T00:00:00", "2003-05-26T00:00:00", "2003-06-02T00:00:00", "2003-06-09T00:00:00", "2003-06-16T00:00:00", "2003-06-23T00:00:00", "2003-06-30T00:00:00", "2003-07-07T00:00:00", "2003-07-14T00:00:00", "2003-07-21T00:00:00", "2003-07-28T00:00:00", "2003-08-04T00:00:00", "2003-08-11T00:00:00", "2003-08-18T00:00:00", "2003-08-25T00:00:00", "2003-09-01T00:00:00", "2003-09-08T00:00:00", "2003-09-15T00:00:00", "2003-09-22T00:00:00", "2003-09-29T00:00:00", "2003-10-06T00:00:00", "2003-10-13T00:00:00", "2003-10-20T00:00:00", "2003-10-27T00:00:00", "2003-11-03T00:00:00", "2003-11-10T00:00:00", "2003-11-17T00:00:00", "2003-11-24T00:00:00", "2003-12-01T00:00:00", "2003-12-08T00:00:00", "2003-12-15T00:00:00", "2003-12-22T00:00:00", "2003-12-29T00:00:00", "2004-01-05T00:00:00", "2004-01-12T00:00:00", "2004-01-19T00:00:00", "2004-01-26T00:00:00", "2004-02-02T00:00:00", "2004-02-09T00:00:00", "2004-02-16T00:00:00", "2004-02-23T00:00:00", "2004-03-01T00:00:00", "2004-03-08T00:00:00", "2004-03-15T00:00:00", "2004-03-22T00:00:00", "2004-03-29T00:00:00", "2004-04-05T00:00:00", "2004-04-12T00:00:00", "2004-04-19T00:00:00", "2004-04-26T00:00:00", "2004-05-03T00:00:00", "2004-05-10T00:00:00", "2004-05-17T00:00:00", "2004-05-24T00:00:00", "2004-05-31T00:00:00", "2004-06-07T00:00:00", "2004-06-14T00:00:00", "2004-06-21T00:00:00", "2004-06-28T00:00:00", "2004-07-05T00:00:00", "2004-07-12T00:00:00", "2004-07-19T00:00:00", "2004-07-26T00:00:00", "2004-08-02T00:00:00", "2004-08-09T00:00:00", "2004-08-16T00:00:00", "2004-08-23T00:00:00", "2004-08-30T00:00:00", "2004-09-06T00:00:00", "2004-09-13T00:00:00", "2004-09-20T00:00:00", "2004-09-27T00:00:00", "2004-10-04T00:00:00", "2004-10-11T00:00:00", "2004-10-18T00:00:00", "2004-10-25T00:00:00", "2004-11-01T00:00:00", "2004-11-08T00:00:00", "2004-11-15T00:00:00", "2004-11-22T00:00:00", "2004-11-29T00:00:00", "2004-12-06T00:00:00", "2004-12-13T00:00:00", "2004-12-20T00:00:00", "2004-12-27T00:00:00", "2005-01-03T00:00:00", "2005-01-10T00:00:00", "2005-01-17T00:00:00", "2005-01-24T00:00:00", "2005-01-31T00:00:00", "2005-02-07T00:00:00", "2005-02-14T00:00:00", "2005-02-21T00:00:00", "2005-02-28T00:00:00", "2005-03-07T00:00:00", "2005-03-14T00:00:00", "2005-03-21T00:00:00", "2005-03-28T00:00:00", "2005-04-04T00:00:00", "2005-04-11T00:00:00", "2005-04-18T00:00:00", "2005-04-25T00:00:00", "2005-05-02T00:00:00", "2005-05-09T00:00:00", "2005-05-16T00:00:00", "2005-05-23T00:00:00", "2005-05-30T00:00:00", "2005-06-06T00:00:00", "2005-06-13T00:00:00", "2005-06-20T00:00:00", "2005-06-27T00:00:00", "2005-07-04T00:00:00", "2005-07-11T00:00:00", "2005-07-18T00:00:00", "2005-07-25T00:00:00", "2005-08-01T00:00:00", "2005-08-08T00:00:00", "2005-08-15T00:00:00", "2005-08-22T00:00:00", "2005-08-29T00:00:00", "2005-09-05T00:00:00", "2005-09-12T00:00:00", "2005-09-19T00:00:00", "2005-09-26T00:00:00", "2005-10-03T00:00:00", "2005-10-10T00:00:00", "2005-10-17T00:00:00", "2005-10-24T00:00:00", "2005-10-31T00:00:00", "2005-11-07T00:00:00", "2005-11-14T00:00:00", "2005-11-21T00:00:00", "2005-11-28T00:00:00", "2005-12-05T00:00:00", "2005-12-12T00:00:00", "2005-12-19T00:00:00", "2005-12-26T00:00:00", "2006-01-02T00:00:00", "2006-01-09T00:00:00", "2006-01-16T00:00:00", "2006-01-23T00:00:00", "2006-01-30T00:00:00", "2006-02-06T00:00:00", "2006-02-13T00:00:00", "2006-02-20T00:00:00", "2006-02-27T00:00:00", "2006-03-06T00:00:00", "2006-03-13T00:00:00", "2006-03-20T00:00:00", "2006-03-27T00:00:00", "2006-04-03T00:00:00", "2006-04-10T00:00:00", "2006-04-17T00:00:00", "2006-04-24T00:00:00", "2006-05-01T00:00:00", "2006-05-08T00:00:00", "2006-05-15T00:00:00", "2006-05-22T00:00:00", "2006-05-29T00:00:00", "2006-06-05T00:00:00", "2006-06-12T00:00:00", "2006-06-19T00:00:00", "2006-06-26T00:00:00", "2006-07-03T00:00:00", "2006-07-10T00:00:00", "2006-07-17T00:00:00", "2006-07-24T00:00:00", "2006-07-31T00:00:00", "2006-08-07T00:00:00", "2006-08-14T00:00:00", "2006-08-21T00:00:00", "2006-08-28T00:00:00", "2006-09-04T00:00:00", "2006-09-11T00:00:00", "2006-09-18T00:00:00", "2006-09-25T00:00:00", "2006-10-02T00:00:00", "2006-10-09T00:00:00", "2006-10-16T00:00:00", "2006-10-23T00:00:00", "2006-10-30T00:00:00", "2006-11-06T00:00:00", "2006-11-13T00:00:00", "2006-11-20T00:00:00", "2006-11-27T00:00:00", "2006-12-04T00:00:00", "2006-12-11T00:00:00", "2006-12-18T00:00:00", "2006-12-25T00:00:00", "2007-01-01T00:00:00", "2007-01-08T00:00:00", "2007-01-15T00:00:00", "2007-01-22T00:00:00", "2007-01-29T00:00:00", "2007-02-05T00:00:00", "2007-02-12T00:00:00", "2007-02-19T00:00:00", "2007-02-26T00:00:00", "2007-03-05T00:00:00", "2007-03-12T00:00:00", "2007-03-19T00:00:00", "2007-03-26T00:00:00", "2007-04-02T00:00:00", "2007-04-09T00:00:00", "2007-04-16T00:00:00", "2007-04-23T00:00:00", "2007-04-30T00:00:00", "2007-05-07T00:00:00", "2007-05-14T00:00:00", "2007-05-21T00:00:00", "2007-05-28T00:00:00", "2007-06-04T00:00:00", "2007-06-11T00:00:00", "2007-06-18T00:00:00", "2007-06-25T00:00:00", "2007-07-02T00:00:00", "2007-07-09T00:00:00", "2007-07-16T00:00:00", "2007-07-23T00:00:00", "2007-07-30T00:00:00", "2007-08-06T00:00:00", "2007-08-13T00:00:00", "2007-08-20T00:00:00", "2007-08-27T00:00:00", "2007-09-03T00:00:00", "2007-09-10T00:00:00", "2007-09-17T00:00:00", "2007-09-24T00:00:00", "2007-10-01T00:00:00", "2007-10-08T00:00:00", "2007-10-15T00:00:00", "2007-10-22T00:00:00", "2007-10-29T00:00:00", "2007-11-05T00:00:00", "2007-11-12T00:00:00", "2007-11-19T00:00:00", "2007-11-26T00:00:00", "2007-12-03T00:00:00", "2007-12-10T00:00:00", "2007-12-17T00:00:00", "2007-12-24T00:00:00", "2007-12-31T00:00:00", "2008-01-07T00:00:00", "2008-01-14T00:00:00", "2008-01-21T00:00:00", "2008-01-28T00:00:00", "2008-02-04T00:00:00", "2008-02-11T00:00:00", "2008-02-18T00:00:00", "2008-02-25T00:00:00", "2008-03-03T00:00:00", "2008-03-10T00:00:00", "2008-03-17T00:00:00", "2008-03-24T00:00:00", "2008-03-31T00:00:00", "2008-04-07T00:00:00", "2008-04-14T00:00:00", "2008-04-21T00:00:00", "2008-04-28T00:00:00", "2008-05-05T00:00:00", "2008-05-12T00:00:00", "2008-05-19T00:00:00", "2008-05-26T00:00:00", "2008-06-02T00:00:00", "2008-06-09T00:00:00", "2008-06-16T00:00:00", "2008-06-23T00:00:00", "2008-06-30T00:00:00", "2008-07-07T00:00:00", "2008-07-14T00:00:00", "2008-07-21T00:00:00", "2008-07-28T00:00:00", "2008-08-04T00:00:00", "2008-08-11T00:00:00", "2008-08-18T00:00:00", "2008-08-25T00:00:00", "2008-09-01T00:00:00", "2008-09-08T00:00:00", "2008-09-15T00:00:00", "2008-09-22T00:00:00", "2008-09-29T00:00:00", "2008-10-06T00:00:00", "2008-10-13T00:00:00", "2008-10-20T00:00:00", "2008-10-27T00:00:00", "2008-11-03T00:00:00", "2008-11-10T00:00:00", "2008-11-17T00:00:00", "2008-11-24T00:00:00", "2008-12-01T00:00:00", "2008-12-08T00:00:00", "2008-12-15T00:00:00", "2008-12-22T00:00:00", "2008-12-29T00:00:00", "2009-01-05T00:00:00", "2009-01-12T00:00:00", "2009-01-19T00:00:00", "2009-01-26T00:00:00", "2009-02-02T00:00:00", "2009-02-09T00:00:00", "2009-02-16T00:00:00", "2009-02-23T00:00:00", "2009-03-02T00:00:00", "2009-03-09T00:00:00", "2009-03-16T00:00:00", "2009-03-23T00:00:00", "2009-03-30T00:00:00", "2009-04-06T00:00:00", "2009-04-13T00:00:00", "2009-04-20T00:00:00", "2009-04-27T00:00:00", "2009-05-04T00:00:00", "2009-05-11T00:00:00", "2009-05-18T00:00:00", "2009-05-25T00:00:00", "2009-06-01T00:00:00", "2009-06-08T00:00:00", "2009-06-15T00:00:00", "2009-06-22T00:00:00", "2009-06-29T00:00:00", "2009-07-06T00:00:00", "2009-07-13T00:00:00", "2009-07-20T00:00:00", "2009-07-27T00:00:00", "2009-08-03T00:00:00", "2009-08-10T00:00:00", "2009-08-17T00:00:00", "2009-08-24T00:00:00", "2009-08-31T00:00:00", "2009-09-07T00:00:00", "2009-09-14T00:00:00", "2009-09-21T00:00:00", "2009-09-28T00:00:00", "2009-10-05T00:00:00", "2009-10-12T00:00:00", "2009-10-19T00:00:00", "2009-10-26T00:00:00", "2009-11-02T00:00:00", "2009-11-09T00:00:00", "2009-11-16T00:00:00", "2009-11-23T00:00:00", "2009-11-30T00:00:00", "2009-12-07T00:00:00", "2009-12-14T00:00:00", "2009-12-21T00:00:00", "2009-12-28T00:00:00", "2010-01-04T00:00:00", "2010-01-11T00:00:00", "2010-01-18T00:00:00", "2010-01-25T00:00:00", "2010-02-01T00:00:00", "2010-02-08T00:00:00", "2010-02-15T00:00:00", "2010-02-22T00:00:00", "2010-03-01T00:00:00", "2010-03-08T00:00:00", "2010-03-15T00:00:00", "2010-03-22T00:00:00", "2010-03-29T00:00:00", "2010-04-05T00:00:00", "2010-04-12T00:00:00", "2010-04-19T00:00:00", "2010-04-26T00:00:00", "2010-05-03T00:00:00", "2010-05-10T00:00:00", "2010-05-17T00:00:00", "2010-05-24T00:00:00", "2010-05-31T00:00:00", "2010-06-07T00:00:00", "2010-06-14T00:00:00", "2010-06-21T00:00:00", "2010-06-28T00:00:00", "2010-07-05T00:00:00", "2010-07-12T00:00:00", "2010-07-19T00:00:00", "2010-07-26T00:00:00", "2010-08-02T00:00:00", "2010-08-09T00:00:00", "2010-08-16T00:00:00", "2010-08-23T00:00:00", "2010-08-30T00:00:00", "2010-09-06T00:00:00", "2010-09-13T00:00:00", "2010-09-20T00:00:00", "2010-09-27T00:00:00", "2010-10-04T00:00:00", "2010-10-11T00:00:00", "2010-10-18T00:00:00", "2010-10-25T00:00:00", "2010-11-01T00:00:00", "2010-11-08T00:00:00", "2010-11-15T00:00:00", "2010-11-22T00:00:00", "2010-11-29T00:00:00", "2010-12-06T00:00:00", "2010-12-13T00:00:00", "2010-12-20T00:00:00", "2010-12-27T00:00:00", "2011-01-03T00:00:00", "2011-01-10T00:00:00", "2011-01-17T00:00:00", "2011-01-24T00:00:00", "2011-01-31T00:00:00", "2011-02-07T00:00:00", "2011-02-14T00:00:00", "2011-02-21T00:00:00", "2011-02-28T00:00:00", "2011-03-07T00:00:00", "2011-03-14T00:00:00", "2011-03-21T00:00:00", "2011-03-28T00:00:00", "2011-04-04T00:00:00", "2011-04-11T00:00:00", "2011-04-18T00:00:00", "2011-04-25T00:00:00", "2011-05-02T00:00:00", "2011-05-09T00:00:00", "2011-05-16T00:00:00", "2011-05-23T00:00:00", "2011-05-30T00:00:00", "2011-06-06T00:00:00", "2011-06-13T00:00:00", "2011-06-20T00:00:00", "2011-06-27T00:00:00", "2011-07-04T00:00:00", "2011-07-11T00:00:00", "2011-07-18T00:00:00", "2011-07-25T00:00:00", "2011-08-01T00:00:00", "2011-08-08T00:00:00", "2011-08-15T00:00:00", "2011-08-22T00:00:00", "2011-08-29T00:00:00", "2011-09-05T00:00:00", "2011-09-12T00:00:00", "2011-09-19T00:00:00", "2011-09-26T00:00:00", "2011-10-03T00:00:00", "2011-10-10T00:00:00", "2011-10-17T00:00:00", "2011-10-24T00:00:00", "2011-10-31T00:00:00", "2011-11-07T00:00:00", "2011-11-14T00:00:00", "2011-11-21T00:00:00", "2011-11-28T00:00:00", "2011-12-05T00:00:00", "2011-12-12T00:00:00", "2011-12-19T00:00:00", "2011-12-26T00:00:00", "2012-01-02T00:00:00", "2012-01-09T00:00:00", "2012-01-16T00:00:00", "2012-01-23T00:00:00", "2012-01-30T00:00:00", "2012-02-06T00:00:00", "2012-02-13T00:00:00", "2012-02-20T00:00:00", "2012-02-27T00:00:00", "2012-03-05T00:00:00", "2012-03-12T00:00:00", "2012-03-19T00:00:00", "2012-03-26T00:00:00", "2012-04-02T00:00:00", "2012-04-09T00:00:00", "2012-04-16T00:00:00", "2012-04-23T00:00:00", "2012-04-30T00:00:00", "2012-05-07T00:00:00", "2012-05-14T00:00:00", "2012-05-21T00:00:00", "2012-05-28T00:00:00", "2012-06-04T00:00:00", "2012-06-11T00:00:00", "2012-06-18T00:00:00", "2012-06-25T00:00:00", "2012-07-02T00:00:00", "2012-07-09T00:00:00", "2012-07-16T00:00:00", "2012-07-23T00:00:00", "2012-07-30T00:00:00", "2012-08-06T00:00:00", "2012-08-13T00:00:00", "2012-08-20T00:00:00", "2012-08-27T00:00:00", "2012-09-03T00:00:00", "2012-09-10T00:00:00", "2012-09-17T00:00:00", "2012-09-24T00:00:00", "2012-10-01T00:00:00", "2012-10-08T00:00:00", "2012-10-15T00:00:00", "2012-10-22T00:00:00", "2012-10-29T00:00:00", "2012-11-05T00:00:00", "2012-11-12T00:00:00", "2012-11-19T00:00:00", "2012-11-26T00:00:00", "2012-12-03T00:00:00", "2012-12-10T00:00:00", "2012-12-17T00:00:00", "2012-12-24T00:00:00", "2012-12-31T00:00:00", "2013-01-07T00:00:00", "2013-01-14T00:00:00", "2013-01-21T00:00:00", "2013-01-28T00:00:00", "2013-02-04T00:00:00", "2013-02-11T00:00:00", "2013-02-18T00:00:00", "2013-02-25T00:00:00", "2013-03-04T00:00:00", "2013-03-11T00:00:00", "2013-03-18T00:00:00", "2013-03-25T00:00:00", "2013-04-01T00:00:00", "2013-04-08T00:00:00", "2013-04-15T00:00:00", "2013-04-22T00:00:00", "2013-04-29T00:00:00", "2013-05-06T00:00:00", "2013-05-13T00:00:00", "2013-05-20T00:00:00", "2013-05-27T00:00:00", "2013-06-03T00:00:00", "2013-06-10T00:00:00", "2013-06-17T00:00:00", "2013-06-24T00:00:00", "2013-07-01T00:00:00", "2013-07-08T00:00:00", "2013-07-15T00:00:00", "2013-07-22T00:00:00", "2013-07-29T00:00:00", "2013-08-05T00:00:00", "2013-08-12T00:00:00", "2013-08-19T00:00:00", "2013-08-26T00:00:00", "2013-09-02T00:00:00", "2013-09-09T00:00:00", "2013-09-16T00:00:00", "2013-09-23T00:00:00", "2013-09-30T00:00:00", "2013-10-07T00:00:00", "2013-10-14T00:00:00", "2013-10-21T00:00:00", "2013-10-28T00:00:00", "2013-11-04T00:00:00", "2013-11-11T00:00:00", "2013-11-18T00:00:00", "2013-11-25T00:00:00", "2013-12-02T00:00:00", "2013-12-09T00:00:00", "2013-12-16T00:00:00", "2013-12-23T00:00:00", "2013-12-30T00:00:00", "2014-01-06T00:00:00", "2014-01-13T00:00:00", "2014-01-20T00:00:00", "2014-01-27T00:00:00", "2014-02-03T00:00:00", "2014-02-10T00:00:00", "2014-02-17T00:00:00", "2014-02-24T00:00:00", "2014-03-03T00:00:00", "2014-03-10T00:00:00", "2014-03-17T00:00:00", "2014-03-24T00:00:00", "2014-03-31T00:00:00", "2014-04-07T00:00:00", "2014-04-14T00:00:00", "2014-04-21T00:00:00", "2014-04-28T00:00:00", "2014-05-05T00:00:00", "2014-05-12T00:00:00", "2014-05-19T00:00:00", "2014-05-26T00:00:00", "2014-06-02T00:00:00", "2014-06-09T00:00:00", "2014-06-16T00:00:00", "2014-06-23T00:00:00", "2014-06-30T00:00:00", "2014-07-07T00:00:00", "2014-07-14T00:00:00", "2014-07-21T00:00:00", "2014-07-28T00:00:00", "2014-08-04T00:00:00", "2014-08-11T00:00:00", "2014-08-18T00:00:00", "2014-08-25T00:00:00", "2014-09-01T00:00:00", "2014-09-08T00:00:00", "2014-09-15T00:00:00", "2014-09-22T00:00:00", "2014-09-29T00:00:00", "2014-10-06T00:00:00", "2014-10-13T00:00:00", "2014-10-20T00:00:00", "2014-10-27T00:00:00", "2014-11-03T00:00:00", "2014-11-10T00:00:00", "2014-11-17T00:00:00", "2014-11-24T00:00:00", "2014-12-01T00:00:00", "2014-12-08T00:00:00", "2014-12-15T00:00:00", "2014-12-22T00:00:00", "2014-12-29T00:00:00", "2015-01-05T00:00:00", "2015-01-12T00:00:00", "2015-01-19T00:00:00", "2015-01-26T00:00:00", "2015-02-02T00:00:00", "2015-02-09T00:00:00", "2015-02-16T00:00:00", "2015-02-23T00:00:00", "2015-03-02T00:00:00", "2015-03-09T00:00:00", "2015-03-16T00:00:00", "2015-03-23T00:00:00", "2015-03-30T00:00:00", "2015-04-06T00:00:00", "2015-04-13T00:00:00", "2015-04-20T00:00:00", "2015-04-27T00:00:00", "2015-05-04T00:00:00", "2015-05-11T00:00:00", "2015-05-18T00:00:00", "2015-05-25T00:00:00", "2015-06-01T00:00:00", "2015-06-08T00:00:00", "2015-06-15T00:00:00", "2015-06-22T00:00:00", "2015-06-29T00:00:00", "2015-07-06T00:00:00", "2015-07-13T00:00:00", "2015-07-20T00:00:00", "2015-07-27T00:00:00", "2015-08-03T00:00:00", "2015-08-10T00:00:00", "2015-08-17T00:00:00", "2015-08-24T00:00:00", "2015-08-31T00:00:00", "2015-09-07T00:00:00", "2015-09-14T00:00:00", "2015-09-21T00:00:00", "2015-09-28T00:00:00", "2015-10-05T00:00:00", "2015-10-12T00:00:00", "2015-10-19T00:00:00", "2015-10-26T00:00:00", "2015-11-02T00:00:00", "2015-11-09T00:00:00", "2015-11-16T00:00:00", "2015-11-23T00:00:00", "2015-11-30T00:00:00", "2015-12-07T00:00:00", "2015-12-14T00:00:00", "2015-12-21T00:00:00", "2015-12-28T00:00:00", "2016-01-04T00:00:00", "2016-01-11T00:00:00", "2016-01-18T00:00:00", "2016-01-25T00:00:00", "2016-02-01T00:00:00", "2016-02-08T00:00:00", "2016-02-15T00:00:00", "2016-02-22T00:00:00", "2016-02-29T00:00:00", "2016-03-07T00:00:00", "2016-03-14T00:00:00", "2016-03-21T00:00:00", "2016-03-28T00:00:00", "2016-04-04T00:00:00", "2016-04-11T00:00:00", "2016-04-18T00:00:00", "2016-04-25T00:00:00", "2016-05-02T00:00:00", "2016-05-09T00:00:00", "2016-05-16T00:00:00", "2016-05-23T00:00:00", "2016-05-30T00:00:00", "2016-06-06T00:00:00", "2016-06-13T00:00:00", "2016-06-20T00:00:00", "2016-06-27T00:00:00", "2016-07-04T00:00:00", "2016-07-11T00:00:00", "2016-07-18T00:00:00", "2016-07-25T00:00:00", "2016-08-01T00:00:00", "2016-08-08T00:00:00", "2016-08-15T00:00:00", "2016-08-22T00:00:00", "2016-08-29T00:00:00", "2016-09-05T00:00:00", "2016-09-12T00:00:00", "2016-09-19T00:00:00", "2016-09-26T00:00:00", "2016-10-03T00:00:00", "2016-10-10T00:00:00", "2016-10-17T00:00:00", "2016-10-24T00:00:00", "2016-10-31T00:00:00", "2016-11-07T00:00:00", "2016-11-14T00:00:00", "2016-11-21T00:00:00", "2016-11-28T00:00:00", "2016-12-05T00:00:00", "2016-12-12T00:00:00", "2016-12-19T00:00:00", "2016-12-26T00:00:00", "2017-01-02T00:00:00", "2017-01-09T00:00:00", "2017-01-16T00:00:00", "2017-01-23T00:00:00", "2017-01-30T00:00:00", "2017-02-06T00:00:00", "2017-02-13T00:00:00", "2017-02-20T00:00:00", "2017-02-27T00:00:00", "2017-03-06T00:00:00", "2017-03-13T00:00:00", "2017-03-20T00:00:00", "2017-03-27T00:00:00", "2017-04-03T00:00:00", "2017-04-10T00:00:00", "2017-04-17T00:00:00", "2017-04-24T00:00:00", "2017-05-01T00:00:00", "2017-05-08T00:00:00", "2017-05-15T00:00:00", "2017-05-22T00:00:00", "2017-05-29T00:00:00", "2017-06-05T00:00:00", "2017-06-12T00:00:00", "2017-06-19T00:00:00", "2017-06-26T00:00:00", "2017-07-03T00:00:00", "2017-07-10T00:00:00", "2017-07-17T00:00:00", "2017-07-24T00:00:00", "2017-07-31T00:00:00", "2017-08-07T00:00:00", "2017-08-14T00:00:00", "2017-08-21T00:00:00", "2017-08-28T00:00:00", "2017-09-04T00:00:00", "2017-09-11T00:00:00", "2017-09-18T00:00:00", "2017-09-25T00:00:00", "2017-10-02T00:00:00", "2017-10-09T00:00:00", "2017-10-16T00:00:00", "2017-10-23T00:00:00", "2017-10-30T00:00:00", "2017-11-06T00:00:00", "2017-11-13T00:00:00", "2017-11-20T00:00:00", "2017-11-27T00:00:00", "2017-12-04T00:00:00", "2017-12-11T00:00:00", "2017-12-18T00:00:00", "2017-12-25T00:00:00", "2018-01-01T00:00:00", "2018-01-08T00:00:00", "2018-01-15T00:00:00", "2018-01-22T00:00:00", "2018-01-29T00:00:00", "2018-02-05T00:00:00", "2018-02-12T00:00:00", "2018-02-19T00:00:00", "2018-02-26T00:00:00", "2018-03-05T00:00:00", "2018-03-12T00:00:00", "2018-03-19T00:00:00", "2018-03-26T00:00:00", "2018-04-02T00:00:00", "2018-04-09T00:00:00", "2018-04-16T00:00:00", "2018-04-23T00:00:00", "2018-04-30T00:00:00", "2018-05-07T00:00:00", "2018-05-14T00:00:00", "2018-05-21T00:00:00", "2018-05-28T00:00:00", "2018-06-04T00:00:00", "2018-06-11T00:00:00", "2018-06-18T00:00:00", "2018-06-25T00:00:00", "2018-07-02T00:00:00", "2018-07-09T00:00:00", "2018-07-16T00:00:00", "2018-07-23T00:00:00", "2018-07-30T00:00:00", "2018-08-06T00:00:00", "2018-08-13T00:00:00", "2018-08-20T00:00:00", "2018-08-27T00:00:00", "2018-09-03T00:00:00", "2018-09-10T00:00:00", "2018-09-17T00:00:00", "2018-09-24T00:00:00", "2018-10-01T00:00:00", "2018-10-08T00:00:00", "2018-10-15T00:00:00", "2018-10-22T00:00:00", "2018-10-29T00:00:00", "2018-11-05T00:00:00", "2018-11-12T00:00:00", "2018-11-19T00:00:00", "2018-11-26T00:00:00", "2018-12-03T00:00:00", "2018-12-10T00:00:00", "2018-12-17T00:00:00", "2018-12-24T00:00:00", "2018-12-31T00:00:00", "2019-01-07T00:00:00", "2019-01-14T00:00:00", "2019-01-21T00:00:00", "2019-01-28T00:00:00", "2019-02-04T00:00:00", "2019-02-11T00:00:00", "2019-02-18T00:00:00", "2019-02-25T00:00:00", "2019-03-04T00:00:00", "2019-03-11T00:00:00", "2019-03-18T00:00:00", "2019-03-25T00:00:00", "2019-04-01T00:00:00", "2019-04-08T00:00:00", "2019-04-15T00:00:00", "2019-04-22T00:00:00", "2019-04-29T00:00:00", "2019-05-06T00:00:00", "2019-05-13T00:00:00", "2019-05-20T00:00:00", "2019-05-27T00:00:00", "2019-06-03T00:00:00", "2019-06-10T00:00:00", "2019-06-17T00:00:00", "2019-06-24T00:00:00", "2019-07-01T00:00:00", "2019-07-08T00:00:00", "2019-07-15T00:00:00", "2019-07-22T00:00:00", "2019-07-29T00:00:00", "2019-08-05T00:00:00", "2019-08-12T00:00:00", "2019-08-19T00:00:00", "2019-08-26T00:00:00", "2019-09-02T00:00:00", "2019-09-09T00:00:00", "2019-09-16T00:00:00", "2019-09-23T00:00:00", "2019-09-30T00:00:00", "2019-10-07T00:00:00", "2019-10-14T00:00:00", "2019-10-21T00:00:00", "2019-10-28T00:00:00", "2019-11-04T00:00:00", "2019-11-11T00:00:00", "2019-11-18T00:00:00", "2019-11-25T00:00:00", "2019-12-02T00:00:00", "2019-12-09T00:00:00", "2019-12-16T00:00:00", "2019-12-23T00:00:00", "2019-12-30T00:00:00", "2020-01-06T00:00:00", "2020-01-13T00:00:00", "2020-01-20T00:00:00", "2020-01-27T00:00:00", "2020-02-03T00:00:00", "2020-02-10T00:00:00", "2020-02-17T00:00:00", "2020-02-24T00:00:00", "2020-03-02T00:00:00", "2020-03-09T00:00:00", "2020-03-16T00:00:00", "2020-03-23T00:00:00", "2020-03-30T00:00:00", "2020-04-06T00:00:00", "2020-04-13T00:00:00", "2020-04-20T00:00:00", "2020-04-27T00:00:00", "2020-05-04T00:00:00", "2020-05-11T00:00:00", "2020-05-18T00:00:00", "2020-05-25T00:00:00", "2020-06-01T00:00:00", "2020-06-08T00:00:00", "2020-06-15T00:00:00", "2020-06-22T00:00:00", "2020-06-29T00:00:00", "2020-07-06T00:00:00", "2020-07-13T00:00:00", "2020-07-20T00:00:00", "2020-07-27T00:00:00", "2020-08-03T00:00:00", "2020-08-10T00:00:00", "2020-08-17T00:00:00", "2020-08-24T00:00:00", "2020-08-31T00:00:00", "2020-09-07T00:00:00", "2020-09-14T00:00:00", "2020-09-21T00:00:00", "2020-09-28T00:00:00", "2020-10-05T00:00:00", "2020-10-12T00:00:00", "2020-10-19T00:00:00", "2020-10-26T00:00:00", "2020-11-02T00:00:00", "2020-11-09T00:00:00", "2020-11-16T00:00:00", "2020-11-23T00:00:00", "2020-11-30T00:00:00", "2020-12-07T00:00:00", "2020-12-14T00:00:00", "2020-12-21T00:00:00", "2020-12-28T00:00:00", "2021-01-04T00:00:00", "2021-01-11T00:00:00", "2021-01-18T00:00:00", "2021-01-25T00:00:00", "2021-02-01T00:00:00", "2021-02-08T00:00:00", "2021-02-15T00:00:00", "2021-02-22T00:00:00", "2021-03-01T00:00:00", "2021-03-08T00:00:00", "2021-03-15T00:00:00", "2021-03-22T00:00:00", "2021-03-29T00:00:00", "2021-04-05T00:00:00", "2021-04-12T00:00:00", "2021-04-19T00:00:00", "2021-04-26T00:00:00", "2021-05-03T00:00:00", "2021-05-10T00:00:00", "2021-05-17T00:00:00", "2021-05-24T00:00:00", "2021-05-31T00:00:00", "2021-06-07T00:00:00", "2021-06-14T00:00:00", "2021-06-21T00:00:00", "2021-06-28T00:00:00", "2021-07-05T00:00:00", "2021-07-12T00:00:00", "2021-07-19T00:00:00", "2021-07-26T00:00:00", "2021-08-02T00:00:00", "2021-08-09T00:00:00", "2021-08-16T00:00:00", "2021-08-23T00:00:00", "2021-08-30T00:00:00", "2021-09-06T00:00:00", "2021-09-13T00:00:00", "2021-09-20T00:00:00", "2021-09-27T00:00:00", "2021-10-04T00:00:00", "2021-10-11T00:00:00", "2021-10-18T00:00:00", "2021-10-25T00:00:00", "2021-11-01T00:00:00", "2021-11-08T00:00:00", "2021-11-15T00:00:00", "2021-11-22T00:00:00", "2021-11-29T00:00:00", "2021-12-06T00:00:00", "2021-12-13T00:00:00", "2021-12-20T00:00:00", "2021-12-27T00:00:00", "2022-01-03T00:00:00", "2022-01-10T00:00:00", "2022-01-17T00:00:00", "2022-01-24T00:00:00", "2022-01-31T00:00:00", "2022-02-07T00:00:00", "2022-02-14T00:00:00", "2022-02-21T00:00:00", "2022-02-28T00:00:00", "2022-03-07T00:00:00", "2022-03-14T00:00:00", "2022-03-21T00:00:00", "2022-03-28T00:00:00", "2022-04-04T00:00:00", "2022-04-11T00:00:00", "2022-04-18T00:00:00", "2022-04-25T00:00:00", "2022-05-02T00:00:00", "2022-05-09T00:00:00", "2022-05-16T00:00:00", "2022-05-23T00:00:00", "2022-05-30T00:00:00", "2022-06-06T00:00:00", "2022-06-13T00:00:00", "2022-06-20T00:00:00", "2022-06-27T00:00:00", "2022-07-04T00:00:00", "2022-07-11T00:00:00", "2022-07-18T00:00:00", "2022-07-25T00:00:00", "2022-08-01T00:00:00", "2022-08-08T00:00:00", "2022-08-15T00:00:00", "2022-08-22T00:00:00", "2022-08-29T00:00:00", "2022-09-05T00:00:00", "2022-09-12T00:00:00", "2022-09-19T00:00:00", "2022-09-26T00:00:00", "2022-10-03T00:00:00", "2022-10-10T00:00:00", "2022-10-17T00:00:00", "2022-10-24T00:00:00", "2022-10-31T00:00:00", "2022-11-07T00:00:00", "2022-11-14T00:00:00", "2022-11-21T00:00:00", "2022-11-28T00:00:00", "2022-12-05T00:00:00", "2022-12-12T00:00:00", "2022-12-19T00:00:00", "2022-12-26T00:00:00", "2023-01-02T00:00:00", "2023-01-09T00:00:00", "2023-01-16T00:00:00", "2023-01-23T00:00:00", "2023-01-30T00:00:00", "2023-02-06T00:00:00", "2023-02-13T00:00:00", "2023-02-20T00:00:00", "2023-02-27T00:00:00", "2023-03-06T00:00:00", "2023-03-13T00:00:00", "2023-03-20T00:00:00", "2023-03-27T00:00:00", "2023-04-03T00:00:00", "2023-04-10T00:00:00", "2023-04-17T00:00:00", "2023-04-24T00:00:00", "2023-05-01T00:00:00", "2023-05-08T00:00:00", "2023-05-15T00:00:00", "2023-05-22T00:00:00", "2023-05-29T00:00:00", "2023-06-05T00:00:00", "2023-06-12T00:00:00", "2023-06-19T00:00:00", "2023-06-26T00:00:00", "2023-07-03T00:00:00", "2023-07-10T00:00:00", "2023-07-17T00:00:00", "2023-07-24T00:00:00", "2023-07-31T00:00:00", "2023-08-07T00:00:00", "2023-08-14T00:00:00", "2023-08-21T00:00:00", "2023-08-28T00:00:00", "2023-09-04T00:00:00", "2023-09-11T00:00:00", "2023-09-18T00:00:00", "2023-09-25T00:00:00", "2023-10-02T00:00:00", "2023-10-09T00:00:00", "2023-10-16T00:00:00", "2023-10-23T00:00:00", "2023-10-30T00:00:00", "2023-11-06T00:00:00", "2023-11-13T00:00:00", "2023-11-20T00:00:00", "2023-11-27T00:00:00", "2023-12-04T00:00:00", "2023-12-11T00:00:00", "2023-12-18T00:00:00", "2023-12-25T00:00:00", "2024-01-01T00:00:00", "2024-01-08T00:00:00", "2024-01-15T00:00:00", "2024-01-22T00:00:00", "2024-01-29T00:00:00", "2024-02-05T00:00:00", "2024-02-12T00:00:00", "2024-02-19T00:00:00", "2024-02-26T00:00:00", "2024-03-04T00:00:00", "2024-03-11T00:00:00", "2024-03-18T00:00:00", "2024-03-25T00:00:00", "2024-04-01T00:00:00", "2024-04-08T00:00:00", "2024-04-15T00:00:00", "2024-04-22T00:00:00", "2024-04-29T00:00:00", "2024-05-06T00:00:00", "2024-05-13T00:00:00", "2024-05-20T00:00:00", "2024-05-27T00:00:00", "2024-06-03T00:00:00", "2024-06-10T00:00:00", "2024-06-17T00:00:00", "2024-06-24T00:00:00", "2024-07-01T00:00:00", "2024-07-08T00:00:00", "2024-07-15T00:00:00", "2024-07-22T00:00:00", "2024-07-29T00:00:00", "2024-08-05T00:00:00", "2024-08-12T00:00:00", "2024-08-19T00:00:00"], "y": [5.78, 5.68, 5.77, 5.88, 5.84, 5.83, 5.82, 5.74, 5.59, 5.74, 5.87, 5.77, 5.68, 5.64, 5.53, 5.37, 5.17, 5.02, 5.12, 4.94, 5.25, 5.25, 5.32, 5.18, 5.05, 5.11, 5.05, 4.98, 4.92, 4.82, 4.85, 4.98, 4.93, 5.28, 5.2, 5.35, 5.21, 5.46, 5.41, 5.52, 5.35, 5.32, 5.27, 5.16, 5.37, 5.37, 5.21, 5.13, 5.11, 5.19, 4.97, 4.91, 4.94, 4.85, 4.84, 4.63, 4.73, 4.55, 4.52, 4.62, 4.63, 4.5, 4.31, 4.34, 4.8, 5.05, 4.75, 5.17, 5.26, 5.18, 5.07, 5.09, 4.91, 4.94, 5.12, 4.94, 4.91, 4.86, 4.86, 5.02, 5.33, 5.32, 5.41, 5.44, 5.25, 5.15, 5.19, 5.13, 5.1, 5.23, 5.21, 5.16, 5.06, 5.07, 4.89, 4.87, 4.85, 4.84, 4.66, 4.51, 4.62, 4.29, 4.22, 4.29, 4.22, 4.14, 4.05, 3.9, 3.7, 3.63, 3.64, 3.83, 4.24, 4.1, 4.07, 3.85, 4.02, 4.19, 4.22, 4.06, 4.15, 3.98, 3.82, 4.09, 4.15, 4.05, 3.98, 4.01, 3.99, 3.95, 3.86, 3.68, 3.59, 3.82, 3.98, 3.83, 4.03, 4.04, 4, 3.92, 3.92, 3.64, 3.46, 3.34, 3.43, 3.29, 3.18, 3.32, 3.54, 3.74, 3.74, 4.19, 4.31, 4.35, 4.38, 4.49, 4.53, 4.45, 4.41, 4.28, 4.26, 4.09, 4.17, 4.29, 4.41, 4.3, 4.4, 4.49, 4.18, 4.23, 4.4, 4.29, 4.28, 4.18, 4.24, 4.41, 4.11, 4.04, 4.16, 4.18, 4.09, 4.05, 4.05, 4, 3.78, 3.78, 3.74, 3.91, 4.24, 4.25, 4.39, 4.46, 4.53, 4.81, 4.7, 4.75, 4.66, 4.78, 4.89, 4.7, 4.76, 4.48, 4.46, 4.38, 4.49, 4.48, 4.28, 4.26, 4.28, 4.19, 4.3, 4.16, 4.07, 4.01, 4.19, 4.15, 4.07, 3.99, 4.11, 4.22, 4.2, 4.18, 4.34, 4.24, 4.16, 4.21, 4.3, 4.23, 4.29, 4.23, 4.14, 4.14, 4.07, 4.08, 4.27, 4.36, 4.31, 4.52, 4.53, 4.64, 4.47, 4.45, 4.27, 4.26, 4.21, 4.29, 4.13, 4.07, 4.08, 3.96, 4.09, 4.11, 3.9, 4.06, 4.11, 4.22, 4.25, 4.32, 4.42, 4.27, 4.22, 4.2, 4.03, 4.18, 4.25, 4.3, 4.39, 4.35, 4.5, 4.45, 4.57, 4.65, 4.61, 4.46, 4.41, 4.57, 4.56, 4.45, 4.38, 4.39, 4.38, 4.36, 4.36, 4.54, 4.55, 4.58, 4.54, 4.59, 4.74, 4.77, 4.66, 4.7, 4.88, 4.97, 5.01, 4.99, 5.14, 5.12, 5.15, 5.04, 5.06, 5.02, 4.99, 5.14, 5.25, 5.15, 5.13, 5.07, 5.05, 4.99, 4.93, 5, 4.82, 4.8, 4.73, 4.8, 4.81, 4.56, 4.62, 4.7, 4.79, 4.83, 4.68, 4.71, 4.61, 4.6, 4.54, 4.43, 4.52, 4.6, 4.63, 4.71, 4.66, 4.77, 4.76, 4.9, 4.81, 4.8, 4.69, 4.63, 4.51, 4.56, 4.58, 4.6, 4.65, 4.75, 4.74, 4.66, 4.63, 4.64, 4.69, 4.79, 4.86, 4.93, 5.14, 5.15, 5.09, 5, 5.16, 5.05, 4.97, 4.82, 4.72, 4.78, 4.64, 4.6, 4.54, 4.34, 4.48, 4.63, 4.56, 4.65, 4.69, 4.42, 4.39, 4.35, 4.23, 4.07, 3.83, 3.89, 4.15, 4.2, 4.23, 4.04, 3.86, 3.81, 3.66, 3.61, 3.68, 3.62, 3.76, 3.91, 3.54, 3.46, 3.34, 3.56, 3.45, 3.57, 3.53, 3.75, 3.86, 3.88, 3.78, 3.83, 3.85, 3.98, 4.02, 4.25, 4.19, 3.99, 3.95, 3.9, 4.09, 4.06, 3.98, 3.99, 3.82, 3.79, 3.83, 3.66, 3.47, 3.83, 3.61, 3.48, 3.89, 3.91, 3.79, 3.96, 3.82, 3.68, 3.35, 2.72, 2.77, 2.53, 2.16, 2.13, 2.49, 2.34, 2.36, 2.7, 2.76, 3.07, 2.89, 2.78, 2.91, 2.89, 2.97, 2.68, 2.73, 2.95, 2.88, 2.88, 2.95, 3.19, 3.17, 3.22, 3.45, 3.71, 3.91, 3.76, 3.72, 3.51, 3.52, 3.38, 3.61, 3.75, 3.66, 3.8, 3.48, 3.48, 3.4, 3.45, 3.42, 3.49, 3.31, 3.24, 3.4, 3.41, 3.59, 3.45, 3.52, 3.33, 3.37, 3.21, 3.44, 3.56, 3.69, 3.85, 3.85, 3.85, 3.7, 3.66, 3.68, 3.62, 3.69, 3.8, 3.61, 3.72, 3.71, 3.67, 3.88, 4.01, 3.87, 3.83, 3.83, 3.72, 3.57, 3.47, 3.23, 3.31, 3.17, 3.28, 3.26, 3.05, 3, 3.08, 2.99, 3.03, 2.99, 2.86, 2.58, 2.6, 2.54, 2.72, 2.74, 2.72, 2.54, 2.5, 2.41, 2.52, 2.59, 2.66, 2.6, 2.92, 2.8, 2.84, 2.95, 3.29, 3.36, 3.36, 3.36, 3.32, 3.35, 3.43, 3.42, 3.68, 3.62, 3.59, 3.42, 3.51, 3.36, 3.34, 3.47, 3.45, 3.59, 3.4, 3.39, 3.31, 3.17, 3.15, 3.13, 3.07, 3.01, 3, 2.97, 2.95, 3.22, 2.94, 2.94, 3.03, 2.77, 2.4, 2.29, 2.1, 2.28, 2.02, 1.94, 1.97, 1.91, 1.8, 2.1, 2.18, 2.25, 2.17, 2.04, 2.04, 1.97, 1.97, 2.04, 2.03, 1.82, 2.03, 1.89, 1.98, 1.89, 2.09, 1.87, 1.93, 1.99, 2.01, 1.92, 2, 2.04, 2.39, 2.26, 2.22, 2.06, 2, 1.96, 1.95, 1.92, 1.78, 1.75, 1.75, 1.53, 1.6, 1.59, 1.63, 1.61, 1.53, 1.5, 1.47, 1.53, 1.59, 1.65, 1.82, 1.65, 1.57, 1.68, 1.85, 1.74, 1.64, 1.75, 1.7, 1.83, 1.74, 1.72, 1.61, 1.61, 1.66, 1.63, 1.63, 1.78, 1.79, 1.78, 1.92, 1.89, 1.87, 2, 2, 1.99, 2.01, 1.88, 1.88, 2.07, 1.96, 1.93, 1.86, 1.76, 1.72, 1.72, 1.7, 1.8, 1.92, 1.97, 2.01, 2.13, 2.22, 2.19, 2.57, 2.5, 2.65, 2.57, 2.5, 2.61, 2.67, 2.61, 2.88, 2.79, 2.78, 2.9, 2.88, 2.72, 2.64, 2.65, 2.7, 2.63, 2.54, 2.63, 2.77, 2.67, 2.74, 2.81, 2.86, 2.89, 2.94, 2.99, 2.98, 2.84, 2.84, 2.78, 2.61, 2.7, 2.75, 2.75, 2.6, 2.79, 2.7, 2.74, 2.73, 2.71, 2.65, 2.73, 2.7, 2.63, 2.66, 2.54, 2.54, 2.54, 2.62, 2.61, 2.63, 2.53, 2.63, 2.55, 2.49, 2.5, 2.51, 2.44, 2.39, 2.39, 2.35, 2.48, 2.6, 2.57, 2.5, 2.43, 2.31, 2.2, 2.27, 2.36, 2.38, 2.34, 2.3, 2.22, 2.26, 2.12, 2.17, 2.22, 2.04, 1.92, 1.83, 1.83, 1.68, 1.96, 2.02, 2.06, 2.08, 2.2, 2.1, 1.92, 1.96, 1.92, 1.94, 1.9, 1.94, 2.16, 2.28, 2.23, 2.21, 2.19, 2.39, 2.36, 2.37, 2.33, 2.3, 2.44, 2.38, 2.23, 2.16, 2.24, 2.16, 2.01, 2.21, 2.13, 2.18, 2.2, 2.1, 2.07, 2.12, 2.04, 2.07, 2.2, 2.36, 2.27, 2.25, 2.21, 2.23, 2.23, 2.2, 2.24, 2.24, 2.17, 2.03, 2.03, 1.97, 1.75, 1.74, 1.77, 1.74, 1.91, 1.97, 1.92, 1.89, 1.78, 1.73, 1.78, 1.91, 1.88, 1.77, 1.75, 1.84, 1.85, 1.73, 1.62, 1.67, 1.46, 1.46, 1.43, 1.59, 1.58, 1.51, 1.59, 1.55, 1.55, 1.57, 1.6, 1.68, 1.7, 1.59, 1.63, 1.73, 1.77, 1.77, 1.84, 1.83, 2.23, 2.33, 2.32, 2.39, 2.49, 2.54, 2.55, 2.45, 2.38, 2.4, 2.41, 2.49, 2.42, 2.43, 2.42, 2.36, 2.49, 2.62, 2.47, 2.38, 2.35, 2.37, 2.26, 2.28, 2.33, 2.39, 2.34, 2.25, 2.25, 2.18, 2.21, 2.19, 2.14, 2.35, 2.38, 2.31, 2.26, 2.3, 2.26, 2.22, 2.18, 2.16, 2.16, 2.14, 2.23, 2.22, 2.34, 2.37, 2.3, 2.38, 2.37, 2.32, 2.4, 2.37, 2.32, 2.37, 2.39, 2.39, 2.48, 2.4, 2.49, 2.55, 2.66, 2.7, 2.77, 2.86, 2.87, 2.86, 2.88, 2.87, 2.85, 2.85, 2.73, 2.78, 2.83, 2.98, 2.95, 2.95, 3, 3.06, 2.93, 2.94, 2.96, 2.92, 2.87, 2.87, 2.86, 2.85, 2.96, 2.98, 2.94, 2.88, 2.82, 2.85, 2.86, 2.94, 2.99, 3.08, 3.09, 3.23, 3.16, 3.2, 3.08, 3.2, 3.19, 3.06, 3.07, 2.98, 2.85, 2.86, 2.74, 2.69, 2.7, 2.71, 2.79, 2.75, 2.73, 2.65, 2.66, 2.67, 2.72, 2.64, 2.6, 2.43, 2.49, 2.52, 2.55, 2.59, 2.54, 2.51, 2.4, 2.41, 2.32, 2.07, 2.15, 2.09, 2.02, 2.03, 2.05, 2.09, 2.05, 2.06, 1.75, 1.65, 1.6, 1.54, 1.5, 1.63, 1.84, 1.72, 1.68, 1.56, 1.76, 1.8, 1.85, 1.79, 1.94, 1.81, 1.76, 1.83, 1.83, 1.89, 1.93, 1.9, 1.81, 1.85, 1.84, 1.61, 1.54, 1.56, 1.59, 1.38, 1.1, 0.54, 0.73, 0.76, 0.7, 0.67, 0.76, 0.63, 0.67, 0.64, 0.73, 0.73, 0.66, 0.66, 0.88, 0.71, 0.71, 0.64, 0.69, 0.64, 0.62, 0.62, 0.56, 0.59, 0.69, 0.65, 0.72, 0.72, 0.68, 0.68, 0.67, 0.78, 0.79, 0.78, 0.81, 0.87, 0.96, 0.91, 0.86, 0.84, 0.94, 0.9, 0.95, 0.94, 0.93, 1.15, 1.11, 1.05, 1.09, 1.19, 1.2, 1.37, 1.45, 1.59, 1.62, 1.69, 1.73, 1.73, 1.69, 1.61, 1.58, 1.63, 1.63, 1.64, 1.61, 1.58, 1.57, 1.51, 1.5, 1.49, 1.44, 1.38, 1.19, 1.29, 1.2, 1.33, 1.26, 1.25, 1.29, 1.33, 1.33, 1.31, 1.48, 1.49, 1.61, 1.59, 1.64, 1.58, 1.51, 1.63, 1.63, 1.52, 1.43, 1.42, 1.43, 1.48, 1.63, 1.78, 1.78, 1.75, 1.79, 1.92, 1.98, 1.92, 1.83, 1.78, 2.14, 2.32, 2.46, 2.42, 2.79, 2.85, 2.81, 2.99, 3.05, 2.88, 2.86, 2.74, 3.04, 3.43, 3.25, 3.2, 2.88, 2.99, 2.96, 2.81, 2.6, 2.77, 2.79, 3.03, 3.12, 3.2, 3.37, 3.49, 3.88, 3.67, 3.89, 4.02, 4.25, 4.1, 4.22, 3.88, 3.83, 3.69, 3.6, 3.61, 3.57, 3.75, 3.88, 3.53, 3.49, 3.52, 3.55, 3.63, 3.72, 3.82, 3.92, 3.98, 3.55, 3.47, 3.53, 3.43, 3.41, 3.6, 3.52, 3.59, 3.52, 3.5, 3.72, 3.8, 3.69, 3.73, 3.77, 3.72, 3.86, 4.01, 3.81, 3.86, 3.97, 4.09, 4.19, 4.34, 4.2, 4.18, 4.29, 4.32, 4.55, 4.69, 4.78, 4.71, 4.86, 4.88, 4.67, 4.63, 4.42, 4.39, 4.28, 4.23, 3.95, 3.9, 3.88, 4.01, 3.96, 4.11, 4.08, 4.17, 4.17, 4.3, 4.28, 4.22, 4.1, 4.34, 4.25, 4.33, 4.42, 4.63, 4.62, 4.63, 4.49, 4.48, 4.44, 4.46, 4.41, 4.47, 4.28, 4.25, 4.48, 4.28, 4.23, 4.26, 4.17, 3.78, 3.9, 3.86], "yaxis": "y2"}], "layout": {"legend": {"font": {"size": 10}, "x": 1, "xanchor": "right", "y": 1, "yanchor": "top"}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Copper/Gold Ratio vs. US 10-Year Constant Maturity", "x": 0.5, "y": 0.9}, "xaxis": {"title": {"text": "Date"}}, "yaxis": {"position": 0, "showgrid": false, "side": "left", "title": {"font": {"size": 12}, "text": "Copper/Gold Ratio (x1000) %"}}, "yaxis2": {"overlaying": "y", "position": 1, "side": "right", "title": {"font": {"size": 12}, "text": "US 10-Year Constant Maturity %"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = go.Figure()\n", "\n", "# Add the first scatter trace with its own y-axis\n", "fig.add_scatter(\n", "    x=data.index,\n", "    y=data[\"Copper/Gold Ratio\"],\n", "    name=\"Copper/Gold Ratio (x1000) %\",\n", "    yaxis=\"y1\",\n", ")\n", "\n", "# Add the second scatter trace with its own y-axis\n", "fig.add_scatter(\n", "    x=data.index,\n", "    y=data[\"US 10-Year Constant Maturity\"],\n", "    name=\"US 10-Year Constant Maturity %\",\n", "    yaxis=\"y2\",\n", ")\n", "\n", "# Update the layout to include the y-axes and their titles\n", "fig.update_layout(\n", "    yaxis=dict(\n", "        title=\"Copper/Gold Ratio (x1000) %\",\n", "        side=\"left\",\n", "        position=0,\n", "        titlefont=dict(size=12),\n", "        showgrid=False,\n", "    ),\n", "    yaxis2=dict(\n", "        title=\"US 10-Year Constant Maturity %\",\n", "        side=\"right\",\n", "        overlaying=\"y\",\n", "        position=1,\n", "        titlefont=dict(size=12),\n", "    ),\n", "    xaxis=dict(title=\"Date\"),\n", "    title=\"Copper/Gold Ratio vs. US 10-Year Constant Maturity\",\n", "    title_y=0.90,\n", "    title_x=0.5,\n", ")\n", "\n", "# Set the legend position\n", "fig.update_layout(\n", "    legend=dict(yanchor=\"top\", y=1, xanchor=\"right\", x=1.0, font=dict(size=10))\n", ")\n", "\n", "# Show the plot\n", "fig.show()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["There you have it, folks!  The OpenBB Platform provides endless possibilities for creating unique indicators and analysis with the wide variety of data available at your fingertips.  We love seeing the creations of users, so be sure to tag us on social media and show off your work."]}], "metadata": {"kernelspec": {"display_name": "obb", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}