# 代码规范 - 避免常见错误

## 🚨 **严禁犯的常见错误**

### 1. **Python字符串格式化错误**

#### ❌ **禁止的错误写法**
```python
# 引号不匹配
print(f'✅ {item.get(" ts_code\)}')
print(f"结果: {data.get(' name\,")

# 转义字符错误
print(f'路径: {path\}')
```

#### ✅ **正确写法**
```python
# 使用一致的引号
print(f'✅ {item.get("ts_code")}')
print(f"结果: {data.get('name')}")

# 或者分离变量
ts_code = item.get('ts_code')
print(f'✅ {ts_code}')
```

### 2. **变量作用域错误**

#### ❌ **禁止的错误写法**
```python
try:
    # 一些代码
    result = some_function()
except Exception:
    return process_data(variable_not_defined_yet)  # 变量未定义

variable_not_defined_yet = "value"  # 定义太晚
```

#### ✅ **正确写法**
```python
# 先定义所有需要的变量
variable_name = "default_value"
result = None

try:
    result = some_function()
except Exception:
    return process_data(variable_name)  # 安全使用已定义变量
```

### 3. **PowerShell多行命令错误**

#### ❌ **禁止的错误写法**
```python
# 多行字符串在PowerShell中容易出错
launch-process(command="""python -c "
import requests
print('test')
"""")
```

#### ✅ **正确写法**
```python
# 使用分号分隔的单行命令
launch-process(command='python -c "import requests; print(\'test\')"')

# 或者创建临时脚本文件
```

## 📝 **Python编码规范**

### **核心规范（必须遵循）**

1. **缩进和格式**
   ```python
   # ✅ 使用4空格缩进
   def function_name():
       if condition:
           return value
   
   # ❌ 禁止使用Tab或不一致缩进
   ```

2. **行长度**
   ```python
   # ✅ 每行不超过88字符
   long_variable_name = some_function_with_long_name(
       parameter1, parameter2, parameter3
   )
   
   # ❌ 超长行
   long_variable_name = some_function_with_long_name(parameter1, parameter2, parameter3, parameter4, parameter5)
   ```

3. **命名规范**
   ```python
   # ✅ 变量和函数使用snake_case
   user_name = "john"
   def get_user_data():
       pass
   
   # ✅ 类使用PascalCase
   class UserManager:
       pass
   
   # ✅ 常量使用UPPER_CASE
   MAX_RETRY_COUNT = 3
   ```

4. **字符串格式化**
   ```python
   # ✅ 优先使用f-string
   message = f"用户 {user_name} 登录成功"
   
   # ✅ 复杂情况下分离变量
   user_info = user.get('name', 'Unknown')
   message = f"用户 {user_info} 登录成功"
   
   # ❌ 避免嵌套引号混乱
   # message = f"用户 {user.get(' name\,} 登录"  # 禁止
   ```

### **变量定义规范**

1. **变量必须先定义后使用**
   ```python
   # ✅ 正确顺序
   query = str(input_query).strip()
   results = []
   
   try:
       data = fetch_data(query)
       results = process_data(data)
   except Exception:
       results = get_default_data(query)  # query已定义，安全使用
   ```

2. **异常处理中的变量**
   ```python
   # ✅ 在try块之前定义所有可能用到的变量
   default_value = None
   processed_data = []
   
   try:
       raw_data = fetch_data()
       processed_data = process(raw_data)
   except Exception as e:
       print(f"错误: {e}")
       processed_data = default_value  # 安全使用
   ```

### **函数和类规范**

1. **函数定义**
   ```python
   def function_name(param1: str, param2: int = 0) -> str:
       """
       函数说明
       
       Args:
           param1: 参数1说明
           param2: 参数2说明，默认值0
           
       Returns:
           返回值说明
       """
       # 函数体
       return result
   ```

2. **错误处理**
   ```python
   def safe_function(data: dict) -> dict:
       """安全的函数，包含完整错误处理"""
       # 预定义所有可能用到的变量
       result = {}
       processed_data = None
       
       try:
           # 验证输入
           if not data:
               return {}
           
           # 处理数据
           processed_data = process_data(data)
           result = format_result(processed_data)
           
       except Exception as e:
           print(f"处理错误: {e}")
           # 使用预定义的变量，确保安全
           result = {"error": str(e), "data": processed_data}
       
       return result
   ```

## 🌐 **JavaScript错误处理规范**

### **API调用错误处理**
```javascript
// ❌ 禁止的错误写法
async function badApiCall() {
    const resp = await fetch('/api/data');
    const data = await resp.json();
    // 直接访问可能不存在的属性
    const value = data.final_value.toLocaleString();  // 可能报错
}

// ✅ 正确的错误处理
async function goodApiCall() {
    try {
        const resp = await fetch('/api/data');
        const data = await resp.json();

        // 验证数据结构
        if (data && typeof data === 'object') {
            // 安全访问属性
            const value = data.final && typeof data.final === 'number'
                ? data.final.toLocaleString()
                : '数据不可用';

            // 显示结果
            element.innerHTML = `结果: ${value}`;
        } else {
            element.innerHTML = '❌ 数据格式错误';
        }
    } catch (e) {
        console.error('API调用错误:', e);
        element.innerHTML = `❌ 请求失败: ${e.message}`;
    }
}
```

### **数值安全处理模板**
```javascript
// ❌ 禁止直接调用数值方法
const result = data.value.toFixed(2);  // 如果value是undefined会报错
const percentage = (data.rate * 100).toFixed(2);  // 如果rate是null会报错

// ✅ 安全的数值处理函数
function safeNumber(value, defaultValue = 0) {
    if (typeof value === 'number' && !isNaN(value) && isFinite(value)) {
        return value;
    }
    return defaultValue;
}

function safeFormat(value, decimals = 2, defaultValue = 0) {
    const num = safeNumber(value, defaultValue);
    return num.toFixed(decimals);
}

function safePercentage(value, decimals = 2) {
    return safeFormat(safeNumber(value) * 100, decimals);
}

// ✅ 使用示例
const result = safeFormat(data.value, 2);  // 安全，不会报错
const percentage = safePercentage(data.rate);  // 安全的百分比格式化
const currency = safeNumber(data.amount, 0).toLocaleString();  // 安全的货币格式化
```

### **数据验证模板**
```javascript
// ✅ 安全的数据访问模板
function safeDataAccess(data, path, defaultValue = 'N/A') {
    try {
        const keys = path.split('.');
        let value = data;

        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                return defaultValue;
            }
        }

        return value !== null && value !== undefined ? value : defaultValue;
    } catch (e) {
        console.error('数据访问错误:', e);
        return defaultValue;
    }
}

// 使用示例
const finalValue = safeDataAccess(data, 'final', 0);
const formattedValue = typeof finalValue === 'number'
    ? finalValue.toLocaleString()
    : finalValue;
```

## 🔧 **PowerShell命令规范**

### **单行命令**
```python
# ✅ 简单命令使用单行
launch_process(command='python -c "print(\'Hello World\')"')

# ✅ 使用分号分隔多个语句
launch_process(command='python -c "import os; print(os.getcwd())"')
```

### **复杂命令**
```python
# ✅ 创建临时脚本文件
script_content = '''
import requests
import json

data = {"test": "value"}
print(json.dumps(data))
'''

with open('temp_script.py', 'w') as f:
    f.write(script_content)

launch_process(command='python temp_script.py')
```

## 🚨 **数据准确性规范**

### **严禁的数据错误**
```python
# ❌ 禁止使用错误的股票名称
stock_name = "平安银行"  # 如果实际是其他股票
ai_analysis = f"分析{stock_name}..."  # 错误的名称会误导用户

# ❌ 禁止使用错误的数值
pe_ratio = 15.5  # 如果实际PE是25.3，这是严重错误
analysis = f"PE倍数{pe_ratio}表明估值合理"  # 基于错误数据的分析

# ❌ 禁止功能存在但页面不显示
# API存在：/quant/ga, /sim/backtest
# 但页面上没有对应按钮或界面 - 这是严重问题
```

### **必须验证的数据**
```python
# ✅ 验证股票代码和名称匹配
def verify_stock_info(ts_code: str, name: str) -> bool:
    """验证股票代码和名称是否匹配"""
    # 从可靠数据源验证
    verified_info = get_stock_info(ts_code)
    return verified_info.name == name

# ✅ 验证数值的合理性
def verify_financial_data(pe: float, pb: float, ps: float) -> bool:
    """验证财务数据的合理性"""
    if pe < 0 or pe > 1000:  # PE倍数异常
        return False
    if pb < 0 or pb > 100:   # PB倍数异常
        return False
    if ps < 0 or ps > 100:   # PS倍数异常
        return False
    return True

# ✅ 确保所有API功能在页面显示
def check_ui_completeness():
    """检查UI功能完整性"""
    required_features = [
        "搜索功能", "量化算法按钮", "模拟炒股界面",
        "自研指标显示", "估值倍数图表", "AI分析面板"
    ]
    # 验证每个功能都在页面上可见和可用
```

## ✅ **代码检查清单**

在提交代码前，必须检查：

### **基础代码质量**
1. **[ ]** 所有变量在使用前已定义
2. **[ ]** 字符串格式化语法正确，引号匹配
3. **[ ]** 异常处理块中不使用未定义变量
4. **[ ]** 函数参数类型注解完整
5. **[ ]** 行长度不超过88字符
6. **[ ]** 使用4空格缩进
7. **[ ]** 命名符合snake_case/PascalCase规范
8. **[ ]** PowerShell命令使用单行或临时文件

### **数据准确性检查**
9. **[ ]** 股票名称和代码匹配正确
10. **[ ]** 所有数值经过验证，在合理范围内
11. **[ ]** AI分析基于真实准确的数据
12. **[ ]** 估值倍数参数完整显示
13. **[ ]** 自研指标计算和显示正确

### **功能完整性检查**
14. **[ ]** 所有API功能在页面上有对应界面
15. **[ ]** 量化算法按钮可见且可用
16. **[ ]** 模拟炒股功能在页面显示
17. **[ ]** 搜索结果显示格式正确（去除.SH/.SZ后缀）
18. **[ ]** API调用使用完整股票代码（包含.SH/.SZ）

### **JavaScript错误处理检查**
19. **[ ]** 所有API调用都有try-catch错误处理
20. **[ ]** 访问对象属性前检查对象是否存在
21. **[ ]** 使用toLocaleString()前验证数值类型
22. **[ ]** API返回数据结构与前端期望一致
23. **[ ]** 添加console.error()用于调试
24. **[ ]** 错误信息对用户友好且具体

### **数值安全处理检查**
25. **[ ]** 调用toFixed()前验证是否为有效数字
26. **[ ]** 使用safeNumber()函数处理可能为undefined的数值
27. **[ ]** 使用safeFormat()函数进行安全的数值格式化
28. **[ ]** 检查isNaN()和isFinite()避免无效数值
29. **[ ]** 为所有数值操作提供默认值
30. **[ ]** 百分比计算使用safePercentage()函数

## 🚨 **紧急修复模板**

当发现变量未定义错误时，使用此模板：

```python
def fixed_function(input_param):
    """修复后的函数模板"""
    # 1. 预定义所有变量
    result = None
    processed_input = None
    error_message = ""
    
    # 2. 预处理输入
    try:
        processed_input = str(input_param).strip()
        if not processed_input:
            return default_return_value
    except Exception as e:
        error_message = str(e)
        return {"error": error_message}
    
    # 3. 主要逻辑
    try:
        result = main_processing(processed_input)
    except Exception as e:
        error_message = str(e)
        # 使用预定义变量进行错误处理
        result = fallback_processing(processed_input, error_message)
    
    return result
```

---

**📍 此文件位置**: `CODING_STANDARDS.md`
**🔄 更新频率**: 每次发现新的常见错误时立即更新
**👥 适用范围**: 所有Python和PowerShell代码
