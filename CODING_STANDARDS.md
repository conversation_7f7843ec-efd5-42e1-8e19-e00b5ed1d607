# 代码规范 - 避免常见错误

## 🚨 **严禁犯的常见错误**

### 1. **Python字符串格式化错误**

#### ❌ **禁止的错误写法**
```python
# 引号不匹配
print(f'✅ {item.get(" ts_code\)}')
print(f"结果: {data.get(' name\,")

# 转义字符错误
print(f'路径: {path\}')
```

#### ✅ **正确写法**
```python
# 使用一致的引号
print(f'✅ {item.get("ts_code")}')
print(f"结果: {data.get('name')}")

# 或者分离变量
ts_code = item.get('ts_code')
print(f'✅ {ts_code}')
```

### 2. **变量作用域错误**

#### ❌ **禁止的错误写法**
```python
try:
    # 一些代码
    result = some_function()
except Exception:
    return process_data(variable_not_defined_yet)  # 变量未定义

variable_not_defined_yet = "value"  # 定义太晚
```

#### ✅ **正确写法**
```python
# 先定义所有需要的变量
variable_name = "default_value"
result = None

try:
    result = some_function()
except Exception:
    return process_data(variable_name)  # 安全使用已定义变量
```

### 3. **PowerShell多行命令错误**

#### ❌ **禁止的错误写法**
```python
# 多行字符串在PowerShell中容易出错
launch-process(command="""python -c "
import requests
print('test')
"""")
```

#### ✅ **正确写法**
```python
# 使用分号分隔的单行命令
launch-process(command='python -c "import requests; print(\'test\')"')

# 或者创建临时脚本文件
```

## 📝 **Python编码规范**

### **核心规范（必须遵循）**

1. **缩进和格式**
   ```python
   # ✅ 使用4空格缩进
   def function_name():
       if condition:
           return value
   
   # ❌ 禁止使用Tab或不一致缩进
   ```

2. **行长度**
   ```python
   # ✅ 每行不超过88字符
   long_variable_name = some_function_with_long_name(
       parameter1, parameter2, parameter3
   )
   
   # ❌ 超长行
   long_variable_name = some_function_with_long_name(parameter1, parameter2, parameter3, parameter4, parameter5)
   ```

3. **命名规范**
   ```python
   # ✅ 变量和函数使用snake_case
   user_name = "john"
   def get_user_data():
       pass
   
   # ✅ 类使用PascalCase
   class UserManager:
       pass
   
   # ✅ 常量使用UPPER_CASE
   MAX_RETRY_COUNT = 3
   ```

4. **字符串格式化**
   ```python
   # ✅ 优先使用f-string
   message = f"用户 {user_name} 登录成功"
   
   # ✅ 复杂情况下分离变量
   user_info = user.get('name', 'Unknown')
   message = f"用户 {user_info} 登录成功"
   
   # ❌ 避免嵌套引号混乱
   # message = f"用户 {user.get(' name\,} 登录"  # 禁止
   ```

### **变量定义规范**

1. **变量必须先定义后使用**
   ```python
   # ✅ 正确顺序
   query = str(input_query).strip()
   results = []
   
   try:
       data = fetch_data(query)
       results = process_data(data)
   except Exception:
       results = get_default_data(query)  # query已定义，安全使用
   ```

2. **异常处理中的变量**
   ```python
   # ✅ 在try块之前定义所有可能用到的变量
   default_value = None
   processed_data = []
   
   try:
       raw_data = fetch_data()
       processed_data = process(raw_data)
   except Exception as e:
       print(f"错误: {e}")
       processed_data = default_value  # 安全使用
   ```

### **函数和类规范**

1. **函数定义**
   ```python
   def function_name(param1: str, param2: int = 0) -> str:
       """
       函数说明
       
       Args:
           param1: 参数1说明
           param2: 参数2说明，默认值0
           
       Returns:
           返回值说明
       """
       # 函数体
       return result
   ```

2. **错误处理**
   ```python
   def safe_function(data: dict) -> dict:
       """安全的函数，包含完整错误处理"""
       # 预定义所有可能用到的变量
       result = {}
       processed_data = None
       
       try:
           # 验证输入
           if not data:
               return {}
           
           # 处理数据
           processed_data = process_data(data)
           result = format_result(processed_data)
           
       except Exception as e:
           print(f"处理错误: {e}")
           # 使用预定义的变量，确保安全
           result = {"error": str(e), "data": processed_data}
       
       return result
   ```

## 🔧 **PowerShell命令规范**

### **单行命令**
```python
# ✅ 简单命令使用单行
launch_process(command='python -c "print(\'Hello World\')"')

# ✅ 使用分号分隔多个语句
launch_process(command='python -c "import os; print(os.getcwd())"')
```

### **复杂命令**
```python
# ✅ 创建临时脚本文件
script_content = '''
import requests
import json

data = {"test": "value"}
print(json.dumps(data))
'''

with open('temp_script.py', 'w') as f:
    f.write(script_content)

launch_process(command='python temp_script.py')
```

## ✅ **代码检查清单**

在提交代码前，必须检查：

1. **[ ]** 所有变量在使用前已定义
2. **[ ]** 字符串格式化语法正确，引号匹配
3. **[ ]** 异常处理块中不使用未定义变量
4. **[ ]** 函数参数类型注解完整
5. **[ ]** 行长度不超过88字符
6. **[ ]** 使用4空格缩进
7. **[ ]** 命名符合snake_case/PascalCase规范
8. **[ ]** PowerShell命令使用单行或临时文件

## 🚨 **紧急修复模板**

当发现变量未定义错误时，使用此模板：

```python
def fixed_function(input_param):
    """修复后的函数模板"""
    # 1. 预定义所有变量
    result = None
    processed_input = None
    error_message = ""
    
    # 2. 预处理输入
    try:
        processed_input = str(input_param).strip()
        if not processed_input:
            return default_return_value
    except Exception as e:
        error_message = str(e)
        return {"error": error_message}
    
    # 3. 主要逻辑
    try:
        result = main_processing(processed_input)
    except Exception as e:
        error_message = str(e)
        # 使用预定义变量进行错误处理
        result = fallback_processing(processed_input, error_message)
    
    return result
```

---

**📍 此文件位置**: `CODING_STANDARDS.md`
**🔄 更新频率**: 每次发现新的常见错误时立即更新
**👥 适用范围**: 所有Python和PowerShell代码
