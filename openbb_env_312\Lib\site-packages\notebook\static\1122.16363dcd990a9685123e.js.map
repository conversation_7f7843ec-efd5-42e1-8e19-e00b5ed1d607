{"version": 3, "file": "1122.16363dcd990a9685123e.js?v=16363dcd990a9685123e", "mappings": ";;;;;;;;;;AAAA;AACA,cAAc;AACd,kBAAkB,kBAAkB;AACpC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,0CAA0C;AAC1C,wCAAwC;AACxC,0CAA0C;AAC1C,yCAAyC;AACzC,4CAA4C;AAC5C,8CAA8C;AAC9C,sCAAsC;AACtC,gDAAgD;;AAEhD,oDAAoD;AACpD,oDAAoD;AACpD,kDAAkD;;AAElD,oCAAoC;AACpC,gEAAgE;AAChE,oDAAoD;AACpD;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,cAAc,MAAM;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;;AAEA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,sBAAsB;AACtB;AACA;AACA;AACA,0BAA0B,yCAAyC;AACnE;AACA;AACA,0BAA0B;AAC1B;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA,8BAA8B,wCAAwC;AACtE;AACA;;AAEA;;AAEA;AACA,GAAG;;AAEH;AACA,2BAA2B;AAC3B,oBAAoB,oBAAoB,yBAAyB;AACjE;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/ttcn.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nconst parserConfig = {\n  name: \"ttcn\",\n  keywords: words(\"activate address alive all alt altstep and and4b any\" +\n                  \" break case component const continue control deactivate\" +\n                  \" display do else encode enumerated except exception\" +\n                  \" execute extends extension external for from function\" +\n                  \" goto group if import in infinity inout interleave\" +\n                  \" label language length log match message mixed mod\" +\n                  \" modifies module modulepar mtc noblock not not4b nowait\" +\n                  \" of on optional or or4b out override param pattern port\" +\n                  \" procedure record recursive rem repeat return runs select\" +\n                  \" self sender set signature system template testcase to\" +\n                  \" type union value valueof var variant while with xor xor4b\"),\n  builtin: words(\"bit2hex bit2int bit2oct bit2str char2int char2oct encvalue\" +\n                 \" decomp decvalue float2int float2str hex2bit hex2int\" +\n                 \" hex2oct hex2str int2bit int2char int2float int2hex\" +\n                 \" int2oct int2str int2unichar isbound ischosen ispresent\" +\n                 \" isvalue lengthof log2str oct2bit oct2char oct2hex oct2int\" +\n                 \" oct2str regexp replace rnd sizeof str2bit str2float\" +\n                 \" str2hex str2int str2oct substr unichar2int unichar2char\" +\n                 \" enum2int\"),\n  types: words(\"anytype bitstring boolean char charstring default float\" +\n               \" hexstring integer objid octetstring universal verdicttype timer\"),\n  timerOps: words(\"read running start stop timeout\"),\n  portOps: words(\"call catch check clear getcall getreply halt raise receive\" +\n                 \" reply send trigger\"),\n  configOps: words(\"create connect disconnect done kill killed map unmap\"),\n  verdictOps: words(\"getverdict setverdict\"),\n  sutOps: words(\"action\"),\n  functionOps: words(\"apply derefers refers\"),\n\n  verdictConsts: words(\"error fail inconc none pass\"),\n  booleanConsts: words(\"true false\"),\n  otherConsts: words(\"null NULL omit\"),\n\n  visibilityModifiers: words(\"private public friend\"),\n  templateMatch: words(\"complement ifpresent subset superset permutation\"),\n  multiLineStrings: true\n}\n\nvar wordList = []\nfunction add(obj) {\n  if (obj) for (var prop in obj) if (obj.hasOwnProperty(prop))\n    wordList.push(prop);\n}\nadd(parserConfig.keywords);\nadd(parserConfig.builtin);\nadd(parserConfig.timerOps);\nadd(parserConfig.portOps);\n\nvar keywords = parserConfig.keywords || {},\n    builtin = parserConfig.builtin || {},\n    timerOps = parserConfig.timerOps || {},\n    portOps  = parserConfig.portOps || {},\n    configOps = parserConfig.configOps || {},\n    verdictOps = parserConfig.verdictOps || {},\n    sutOps = parserConfig.sutOps || {},\n    functionOps = parserConfig.functionOps || {},\n\n    verdictConsts = parserConfig.verdictConsts || {},\n    booleanConsts = parserConfig.booleanConsts || {},\n    otherConsts   = parserConfig.otherConsts || {},\n\n    types = parserConfig.types || {},\n    visibilityModifiers = parserConfig.visibilityModifiers || {},\n    templateMatch = parserConfig.templateMatch || {},\n    multiLineStrings = parserConfig.multiLineStrings,\n    indentStatements = parserConfig.indentStatements !== false;\nvar isOperatorChar = /[+\\-*&@=<>!\\/]/;\nvar curPunc;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n\n  if (ch == '\"' || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  if (/[\\[\\]{}\\(\\),;\\\\:\\?\\.]/.test(ch)) {\n    curPunc = ch;\n    return \"punctuation\";\n  }\n  if (ch == \"#\"){\n    stream.skipToEnd();\n    return \"atom\";\n  }\n  if (ch == \"%\"){\n    stream.eatWhile(/\\b/);\n    return \"atom\";\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  if (ch == \"/\") {\n    if (stream.eat(\"*\")) {\n      state.tokenize = tokenComment;\n      return tokenComment(stream, state);\n    }\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n  }\n  if (isOperatorChar.test(ch)) {\n    if(ch == \"@\"){\n      if(stream.match(\"try\") || stream.match(\"catch\")\n         || stream.match(\"lazy\")){\n        return \"keyword\";\n      }\n    }\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  stream.eatWhile(/[\\w\\$_\\xa1-\\uffff]/);\n  var cur = stream.current();\n\n  if (keywords.propertyIsEnumerable(cur)) return \"keyword\";\n  if (builtin.propertyIsEnumerable(cur)) return \"builtin\";\n\n  if (timerOps.propertyIsEnumerable(cur)) return \"def\";\n  if (configOps.propertyIsEnumerable(cur)) return \"def\";\n  if (verdictOps.propertyIsEnumerable(cur)) return \"def\";\n  if (portOps.propertyIsEnumerable(cur)) return \"def\";\n  if (sutOps.propertyIsEnumerable(cur)) return \"def\";\n  if (functionOps.propertyIsEnumerable(cur)) return \"def\";\n\n  if (verdictConsts.propertyIsEnumerable(cur)) return \"string\";\n  if (booleanConsts.propertyIsEnumerable(cur)) return \"string\";\n  if (otherConsts.propertyIsEnumerable(cur)) return \"string\";\n\n  if (types.propertyIsEnumerable(cur)) return \"typeName.standard\";\n  if (visibilityModifiers.propertyIsEnumerable(cur))\n    return \"modifier\";\n  if (templateMatch.propertyIsEnumerable(cur)) return \"atom\";\n\n  return \"variable\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped){\n        var afterQuote = stream.peek();\n        //look if the character after the quote is like the B in '10100010'B\n        if (afterQuote){\n          afterQuote = afterQuote.toLowerCase();\n          if(afterQuote == \"b\" || afterQuote == \"h\" || afterQuote == \"o\")\n            stream.next();\n        }\n        end = true; break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !(escaped || multiLineStrings))\n      state.tokenize = null;\n    return \"string\";\n  };\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction Context(indented, column, type, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.align = align;\n  this.prev = prev;\n}\n\nfunction pushContext(state, col, type) {\n  var indent = state.indented;\n  if (state.context && state.context.type == \"statement\")\n    indent = state.context.indented;\n  return state.context = new Context(indent, col, type, null, state.context);\n}\n\nfunction popContext(state) {\n  var t = state.context.type;\n  if (t == \")\" || t == \"]\" || t == \"}\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\n//Interface\nexport const ttcn = {\n  name: \"ttcn\",\n  startState: function() {\n    return {\n      tokenize: null,\n      context: new Context(0, 0, \"top\", false),\n      indented: 0,\n      startOfLine: true\n    };\n  },\n\n  token: function(stream, state) {\n    var ctx = state.context;\n    if (stream.sol()) {\n      if (ctx.align == null) ctx.align = false;\n      state.indented = stream.indentation();\n      state.startOfLine = true;\n    }\n    if (stream.eatSpace()) return null;\n    curPunc = null;\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\") return style;\n    if (ctx.align == null) ctx.align = true;\n\n    if ((curPunc == \";\" || curPunc == \":\" || curPunc == \",\")\n        && ctx.type == \"statement\"){\n      popContext(state);\n    }\n    else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n    else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n    else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n    else if (curPunc == \"}\") {\n      while (ctx.type == \"statement\") ctx = popContext(state);\n      if (ctx.type == \"}\") ctx = popContext(state);\n      while (ctx.type == \"statement\") ctx = popContext(state);\n    }\n    else if (curPunc == ctx.type) popContext(state);\n    else if (indentStatements &&\n             (((ctx.type == \"}\" || ctx.type == \"top\") && curPunc != ';') ||\n              (ctx.type == \"statement\" && curPunc == \"newstatement\")))\n      pushContext(state, stream.column(), \"statement\");\n\n    state.startOfLine = false;\n\n    return style;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[{}]$/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n    autocomplete: wordList\n  }\n};\n"], "names": [], "sourceRoot": ""}