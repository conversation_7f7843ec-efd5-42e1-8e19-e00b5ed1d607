[tool.poetry]
name = "empty-provider"
version = "0.0.1"
description = "Empty provider extension for OpenBB"
authors = ["OpenBB Team <<EMAIL>>"]
readme = "README.md"
packages = [{ include = "empty_provider" }]

[tool.poetry.dependencies]
python = "^3.9.21,<3.13"
openbb-core = "^1.4.6"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.plugins."openbb_provider_extension"]
empty = "empty_provider:empty_provider"
