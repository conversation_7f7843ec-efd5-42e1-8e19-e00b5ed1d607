name: 🏷️ Pull Request Labels

on:
  pull_request:
    types: [opened, reopened, labeled, unlabeled, synchronize]

jobs:
  label:
    runs-on: ubuntu-latest
    steps:
      # - uses: mheap/github-action-required-labels@v1
      #   with:
      #     mode: minimum
      #     count: 1
      #     labels: "guides, bug, build, docker, docs, feat XS, feat S, feat M, feat L, feat XL, help wanted, refactor, tests, dependencies, release"
      - uses: mheap/github-action-required-labels@v1
        with:
          mode: exactly
          count: 0
          labels: "do not merge"

      - name: 🏷️ Label OpenBB Platform PRs
        uses: srvaroa/labeler@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
