
<!--
DTD generated from ODD source 2024-07-08T10:01:21Z. .This template file is freely available and you are hereby authorised to copy, modify, and redistribute it in any way without further reference or permissions. When making such modifications, you are strongly recommended to change the present text to include an accurate statement of the licencing conditions applicable to your modified text.
TEI Edition: P5 Version 4.8.0. Last updated on 8th July 2024, revision 1f9891a87 
TEI Edition Location: https://www.tei-c.org/Vault/P5/4.8.0/

-->

<!-- start datatypes -->

<!ENTITY % teidata.certainty '(high|medium|low|unknown)' >

<!ENTITY % teidata.probability ' CDATA ' >

<!ENTITY % teidata.numeric ' CDATA ' >

<!ENTITY % teidata.interval ' CDATA ' >

<!ENTITY % teidata.count ' CDATA ' >

<!ENTITY % teidata.temporal.w3c ' CDATA ' >

<!ENTITY % teidata.duration.w3c ' CDATA ' >

<!ENTITY % teidata.truthValue ' CDATA ' >

<!ENTITY % teidata.xTruthValue ' CDATA ' >

<!ENTITY % teidata.language ' CDATA ' >

<!ENTITY % teidata.namespace ' CDATA ' >

<!ENTITY % teidata.outputMeasurement ' CDATA ' >

<!ENTITY % teidata.pattern ' CDATA ' >

<!ENTITY % teidata.pointer ' CDATA ' >

<!ENTITY % teidata.version ' CDATA ' >

<!ENTITY % teidata.versionNumber ' CDATA ' >

<!ENTITY % teidata.replacement ' CDATA' >

<!ENTITY % teidata.xpath ' CDATA' >

<!ENTITY % teidata.word ' CDATA ' >

<!ENTITY % teidata.text ' CDATA ' >

<!ENTITY % teidata.name ' CDATA ' >

<!ENTITY % teidata.xmlName ' CDATA ' >

<!ENTITY % teidata.prefix ' CDATA ' >

<!ENTITY % teidata.enumerated '%teidata.word;' >

<!ENTITY % teidata.gender '%teidata.enumerated;' >

<!ENTITY % teidata.sex '%teidata.enumerated;' >

<!ENTITY % teidata.probCert ' CDATA ' >

<!ENTITY % teidata.temporal.iso ' CDATA ' >

<!ENTITY % teidata.duration.iso ' CDATA ' >

<!-- end datatypes -->

<!-- predeclared classes -->

<!ENTITY % att.global.responsibility.attribute.cert '
cert %teidata.probCert;  #IMPLIED'>

<!ENTITY % att.global.responsibility.attribute.resp '
resp CDATA  #IMPLIED'>
<!ENTITY % att.global.responsibility.attributes '
 %att.global.responsibility.attribute.cert;
 %att.global.responsibility.attribute.resp;'> 
<!ENTITY % att.global.rendition.attribute.rend '
rend CDATA  #IMPLIED'>

<!ENTITY % att.global.rendition.attribute.style '
style %teidata.text;  #IMPLIED'>

<!ENTITY % att.global.rendition.attribute.rendition '
rendition CDATA  #IMPLIED'>
<!ENTITY % att.global.rendition.attributes '
 %att.global.rendition.attribute.rend;
 %att.global.rendition.attribute.style;
 %att.global.rendition.attribute.rendition;'> 
<!ENTITY % att.global.source.attribute.source '
source CDATA  #IMPLIED'>
<!ENTITY % att.global.source.attributes '
 %att.global.source.attribute.source;'> 
<!ENTITY % att.typed.attribute.type '
type %teidata.enumerated;  #IMPLIED'>

<!ENTITY % att.typed.attribute.subtype '
subtype %teidata.enumerated;  #IMPLIED'>
<!ENTITY % att.typed.attributes '
 %att.typed.attribute.type;
 %att.typed.attribute.subtype;'> 
<!ENTITY % model.entryPart ""> 
<!ENTITY % model.entryPart_sequence ""> 
<!ENTITY % model.entryPart_sequenceOptional ""> 
<!ENTITY % model.entryPart_sequenceOptionalRepeatable ""> 
<!ENTITY % model.entryPart_sequenceRepeatable ""> 
<!ENTITY % model.eventLike "event |listEvent"> 
<!ENTITY % model.eventLike_sequence "event, listEvent"> 
<!ENTITY % model.eventLike_sequenceOptional "event?, listEvent?"> 
<!ENTITY % model.eventLike_sequenceOptionalRepeatable "event*, listEvent*"> 
<!ENTITY % model.eventLike_sequenceRepeatable "event+, listEvent+"> 
<!ENTITY % model.placeNamePart "placeName |bloc |country |region |settlement |district |geogName"> 
<!ENTITY % model.placeNamePart_sequence "placeName, bloc, country, region, settlement, district, geogName"> 
<!ENTITY % model.placeNamePart_sequenceOptional "placeName?, bloc?, country?, region?, settlement?, district?, geogName?"> 
<!ENTITY % model.placeNamePart_sequenceOptionalRepeatable "placeName*, bloc*, country*, region*, settlement*, district*, geogName*"> 
<!ENTITY % model.placeNamePart_sequenceRepeatable "placeName+, bloc+, country+, region+, settlement+, district+, geogName+"> 
<!ENTITY % model.placeStateLike "%model.placeNamePart; |climate |location |population |state |terrain |trait"> 
<!ENTITY % model.placeStateLike_sequence "%model.placeNamePart;, climate, location, population, state, terrain, trait"> 
<!ENTITY % model.placeStateLike_sequenceOptional "%model.placeNamePart;?, climate?, location?, population?, state?, terrain?, trait?"> 
<!ENTITY % model.placeStateLike_sequenceOptionalRepeatable "%model.placeNamePart;*, climate*, location*, population*, state*, terrain*, trait*"> 
<!ENTITY % model.placeStateLike_sequenceRepeatable "%model.placeNamePart;+, climate+, location+, population+, state+, terrain+, trait+"> 
<!ENTITY % model.orgPart "%model.eventLike; |listOrg |listPerson |listPlace"> 
<!ENTITY % model.orgPart_sequence "%model.eventLike;, listOrg, listPerson, listPlace"> 
<!ENTITY % model.orgPart_sequenceOptional "%model.eventLike;?, listOrg?, listPerson?, listPlace?"> 
<!ENTITY % model.orgPart_sequenceOptionalRepeatable "%model.eventLike;*, listOrg*, listPerson*, listPlace*"> 
<!ENTITY % model.orgPart_sequenceRepeatable "%model.eventLike;+, listOrg+, listPerson+, listPlace+"> 
<!ENTITY % att.locatable.attribute.where '
where CDATA  #IMPLIED'>
<!ENTITY % att.locatable.attributes '
 %att.locatable.attribute.where;'> 
<!ENTITY % model.resource "text |standOff"> 
<!ENTITY % model.resource_sequence "text, standOff"> 
<!ENTITY % model.resource_sequenceOptional "text?, standOff?"> 
<!ENTITY % model.resource_sequenceOptionalRepeatable "text*, standOff*"> 
<!ENTITY % model.resource_sequenceRepeatable "text+, standOff+"> 
<!ENTITY % model.describedResource "teiCorpus |TEI"> 
<!ENTITY % model.describedResource_sequence "teiCorpus, TEI"> 
<!ENTITY % model.describedResource_sequenceOptional "teiCorpus?, TEI?"> 
<!ENTITY % model.describedResource_sequenceOptionalRepeatable "teiCorpus*, TEI*"> 
<!ENTITY % model.describedResource_sequenceRepeatable "teiCorpus+, TEI+"> 
<!ENTITY % model.objectLike "object |listObject"> 
<!ENTITY % model.objectLike_sequence "object, listObject"> 
<!ENTITY % model.objectLike_sequenceOptional "object?, listObject?"> 
<!ENTITY % model.objectLike_sequenceOptionalRepeatable "object*, listObject*"> 
<!ENTITY % model.objectLike_sequenceRepeatable "object+, listObject+"> 
<!ENTITY % att.datable.custom.attribute.when-custom '
when-custom CDATA  #IMPLIED'>

<!ENTITY % att.datable.custom.attribute.notBefore-custom '
notBefore-custom CDATA  #IMPLIED'>

<!ENTITY % att.datable.custom.attribute.notAfter-custom '
notAfter-custom CDATA  #IMPLIED'>

<!ENTITY % att.datable.custom.attribute.from-custom '
from-custom CDATA  #IMPLIED'>

<!ENTITY % att.datable.custom.attribute.to-custom '
to-custom CDATA  #IMPLIED'>

<!ENTITY % att.datable.custom.attribute.datingPoint '
datingPoint %teidata.pointer;  #IMPLIED'>

<!ENTITY % att.datable.custom.attribute.datingMethod '
datingMethod %teidata.pointer;  #IMPLIED'>
<!ENTITY % att.datable.custom.attributes '
 %att.datable.custom.attribute.when-custom;
 %att.datable.custom.attribute.notBefore-custom;
 %att.datable.custom.attribute.notAfter-custom;
 %att.datable.custom.attribute.from-custom;
 %att.datable.custom.attribute.to-custom;
 %att.datable.custom.attribute.datingPoint;
 %att.datable.custom.attribute.datingMethod;'> 
<!ENTITY % model.persNamePart "surname |forename |genName |nameLink |addName |roleName |persPronouns"> 
<!ENTITY % model.persNamePart_sequence "surname, forename, genName, nameLink, addName, roleName, persPronouns"> 
<!ENTITY % model.persNamePart_sequenceOptional "surname?, forename?, genName?, nameLink?, addName?, roleName?, persPronouns?"> 
<!ENTITY % model.persNamePart_sequenceOptionalRepeatable "surname*, forename*, genName*, nameLink*, addName*, roleName*, persPronouns*"> 
<!ENTITY % model.persNamePart_sequenceRepeatable "surname+, forename+, genName+, nameLink+, addName+, roleName+, persPronouns+"> 
<!ENTITY % att.datable.iso.attribute.when-iso '
when-iso %teidata.temporal.iso;  #IMPLIED'>

<!ENTITY % att.datable.iso.attribute.notBefore-iso '
notBefore-iso %teidata.temporal.iso;  #IMPLIED'>

<!ENTITY % att.datable.iso.attribute.notAfter-iso '
notAfter-iso %teidata.temporal.iso;  #IMPLIED'>

<!ENTITY % att.datable.iso.attribute.from-iso '
from-iso %teidata.temporal.iso;  #IMPLIED'>

<!ENTITY % att.datable.iso.attribute.to-iso '
to-iso %teidata.temporal.iso;  #IMPLIED'>
<!ENTITY % att.datable.iso.attributes '
 %att.datable.iso.attribute.when-iso;
 %att.datable.iso.attribute.notBefore-iso;
 %att.datable.iso.attribute.notAfter-iso;
 %att.datable.iso.attribute.from-iso;
 %att.datable.iso.attribute.to-iso;'> 
<!ENTITY % att.global.linking.attribute.corresp '
corresp CDATA  #IMPLIED'>

<!ENTITY % att.global.linking.attribute.synch '
synch CDATA  #IMPLIED'>

<!ENTITY % att.global.linking.attribute.sameAs '
sameAs %teidata.pointer;  #IMPLIED'>

<!ENTITY % att.global.linking.attribute.copyOf '
copyOf %teidata.pointer;  #IMPLIED'>

<!ENTITY % att.global.linking.attribute.next '
next %teidata.pointer;  #IMPLIED'>

<!ENTITY % att.global.linking.attribute.prev '
prev %teidata.pointer;  #IMPLIED'>

<!ENTITY % att.global.linking.attribute.exclude '
exclude CDATA  #IMPLIED'>

<!ENTITY % att.global.linking.attribute.select '
select CDATA  #IMPLIED'>
<!ENTITY % att.global.linking.attributes '
 %att.global.linking.attribute.corresp;
 %att.global.linking.attribute.synch;
 %att.global.linking.attribute.sameAs;
 %att.global.linking.attribute.copyOf;
 %att.global.linking.attribute.next;
 %att.global.linking.attribute.prev;
 %att.global.linking.attribute.exclude;
 %att.global.linking.attribute.select;'> 
<!-- end of predeclared classes -->

<!ENTITY % att.anchoring.attribute.anchored '
anchored %teidata.truthValue;  "true" '>

<!ENTITY % att.anchoring.attribute.targetEnd '
targetEnd CDATA  #IMPLIED'>
<!ENTITY % att.anchoring.attributes '
 %att.anchoring.attribute.anchored;
 %att.anchoring.attribute.targetEnd;'> 
<!ENTITY % att.ascribed.attribute.who '
who CDATA  #IMPLIED'>
<!ENTITY % att.ascribed.attributes '
 %att.ascribed.attribute.who;'> 
<!ENTITY % att.ascribed.directed.attribute.toWhom '
toWhom CDATA  #IMPLIED'>
<!ENTITY % att.ascribed.directed.attributes '%att.ascribed.attributes;
 %att.ascribed.directed.attribute.toWhom;'> 
<!ENTITY % att.canonical.attribute.key '
key %teidata.text;  #IMPLIED'>

<!ENTITY % att.canonical.attribute.ref '
ref CDATA  #IMPLIED'>
<!ENTITY % att.canonical.attributes '
 %att.canonical.attribute.key;
 %att.canonical.attribute.ref;'> 
<!ENTITY % att.ranging.attribute.atLeast '
atLeast %teidata.numeric;  #IMPLIED'>

<!ENTITY % att.ranging.attribute.atMost '
atMost %teidata.numeric;  #IMPLIED'>

<!ENTITY % att.ranging.attribute.min '
min %teidata.numeric;  #IMPLIED'>

<!ENTITY % att.ranging.attribute.max '
max %teidata.numeric;  #IMPLIED'>

<!ENTITY % att.ranging.attribute.confidence '
confidence %teidata.probability;  #IMPLIED'>
<!ENTITY % att.ranging.attributes '
 %att.ranging.attribute.atLeast;
 %att.ranging.attribute.atMost;
 %att.ranging.attribute.min;
 %att.ranging.attribute.max;
 %att.ranging.attribute.confidence;'> 
<!ENTITY % att.dimensions.attribute.unit '
unit %teidata.enumerated;  #IMPLIED'>

<!ENTITY % att.dimensions.attribute.quantity '
quantity %teidata.numeric;  #IMPLIED'>

<!ENTITY % att.dimensions.attribute.extent '
extent %teidata.text;  #IMPLIED'>

<!ENTITY % att.dimensions.attribute.precision '
precision %teidata.certainty;  #IMPLIED'>

<!ENTITY % att.dimensions.attribute.scope '
scope %teidata.enumerated;  #IMPLIED'>
<!ENTITY % att.dimensions.attributes '%att.ranging.attributes;
 %att.dimensions.attribute.unit;
 %att.dimensions.attribute.quantity;
 %att.dimensions.attribute.extent;
 %att.dimensions.attribute.precision;
 %att.dimensions.attribute.scope;'> 
<!ENTITY % att.written.attribute.hand '
hand %teidata.pointer;  #IMPLIED'>
<!ENTITY % att.written.attributes '
 %att.written.attribute.hand;'> 
<!ENTITY % att.breaking.attribute.break '
break %teidata.enumerated;  #IMPLIED'>
<!ENTITY % att.breaking.attributes '
 %att.breaking.attribute.break;'> 
<!ENTITY % att.cmc.attribute.generatedBy '
generatedBy %teidata.enumerated;  #IMPLIED'>
<!ENTITY % att.cmc.attributes '
 %att.cmc.attribute.generatedBy;'> 
<!ENTITY % att.cReferencing.attribute.cRef '
cRef %teidata.text;  #IMPLIED'>
<!ENTITY % att.cReferencing.attributes '
 %att.cReferencing.attribute.cRef;'> 
<!ENTITY % att.datable.w3c.attribute.when '
when %teidata.temporal.w3c;  #IMPLIED'>

<!ENTITY % att.datable.w3c.attribute.notBefore '
notBefore %teidata.temporal.w3c;  #IMPLIED'>

<!ENTITY % att.datable.w3c.attribute.notAfter '
notAfter %teidata.temporal.w3c;  #IMPLIED'>

<!ENTITY % att.datable.w3c.attribute.from '
from %teidata.temporal.w3c;  #IMPLIED'>

<!ENTITY % att.datable.w3c.attribute.to '
to %teidata.temporal.w3c;  #IMPLIED'>
<!ENTITY % att.datable.w3c.attributes '
 %att.datable.w3c.attribute.when;
 %att.datable.w3c.attribute.notBefore;
 %att.datable.w3c.attribute.notAfter;
 %att.datable.w3c.attribute.from;
 %att.datable.w3c.attribute.to;'> 
<!ENTITY % att.datable.attribute.period '
period CDATA  #IMPLIED'>
<!ENTITY % att.datable.attributes '%att.datable.w3c.attributes;%att.datable.iso.attributes;%att.datable.custom.attributes;
 %att.datable.attribute.period;'> 
<!ENTITY % att.datcat.attribute.datcat '
datcat CDATA  #IMPLIED'>

<!ENTITY % att.datcat.attribute.valueDatcat '
valueDatcat CDATA  #IMPLIED'>

<!ENTITY % att.datcat.attribute.targetDatcat '
targetDatcat CDATA  #IMPLIED'>
<!ENTITY % att.datcat.attributes '
 %att.datcat.attribute.datcat;
 %att.datcat.attribute.valueDatcat;
 %att.datcat.attribute.targetDatcat;'> 
<!ENTITY % att.declarable.attribute.default '
default (true|false) "false" '>
<!ENTITY % att.declarable.attributes '
 %att.declarable.attribute.default;'> 
<!ENTITY % att.declaring.attribute.decls '
decls CDATA  #IMPLIED'>
<!ENTITY % att.declaring.attributes '
 %att.declaring.attribute.decls;'> 
<!ENTITY % att.fragmentable.attribute.part '
part (Y|N|I|M|F) "N" '>
<!ENTITY % att.fragmentable.attributes '
 %att.fragmentable.attribute.part;'> 
<!ENTITY % att.divLike.attribute.org '
org (composite|uniform) "uniform" '>

<!ENTITY % att.divLike.attribute.sample '
sample (initial|medial|final|unknown|complete) "complete" '>
<!ENTITY % att.divLike.attributes '%att.fragmentable.attributes;
 %att.divLike.attribute.org;
 %att.divLike.attribute.sample;'> 
<!ENTITY % att.docStatus.attribute.status '
status %teidata.enumerated;  "draft" '>
<!ENTITY % att.docStatus.attributes '
 %att.docStatus.attribute.status;'> 
<!ENTITY % att.global.responsibility.attribute.cert '
cert %teidata.probCert;  #IMPLIED'>

<!ENTITY % att.global.responsibility.attribute.resp '
resp CDATA  #IMPLIED'>
<!ENTITY % att.global.responsibility.attributes '
 %att.global.responsibility.attribute.cert;
 %att.global.responsibility.attribute.resp;'> 
<!ENTITY % att.editLike.attribute.evidence '
evidence NMTOKENS  #IMPLIED'>

<!ENTITY % att.editLike.attribute.instant '
instant %teidata.xTruthValue;  "false" '>
<!ENTITY % att.editLike.attributes '
 %att.editLike.attribute.evidence;
 %att.editLike.attribute.instant;'> 
<!ENTITY % att.global.rendition.attribute.rend '
rend CDATA  #IMPLIED'>

<!ENTITY % att.global.rendition.attribute.style '
style %teidata.text;  #IMPLIED'>

<!ENTITY % att.global.rendition.attribute.rendition '
rendition CDATA  #IMPLIED'>
<!ENTITY % att.global.rendition.attributes '
 %att.global.rendition.attribute.rend;
 %att.global.rendition.attribute.style;
 %att.global.rendition.attribute.rendition;'> 
<!ENTITY % att.global.source.attribute.source '
source CDATA  #IMPLIED'>
<!ENTITY % att.global.source.attributes '
 %att.global.source.attribute.source;'> 
<!ENTITY % att.global.attribute.xmlid '
xml:id ID #IMPLIED'>

<!ENTITY % att.global.attribute.n '
n %teidata.text;  #IMPLIED'>

<!ENTITY % att.global.attribute.xmllang '
xml:lang %teidata.language;  #IMPLIED'>

<!ENTITY % att.global.attribute.xmlbase '
xml:base %teidata.pointer;  #IMPLIED'>

<!ENTITY % att.global.attribute.xmlspace '
xml:space (default|preserve) #IMPLIED'>
<!ENTITY % att.global.attributes '%att.global.rendition.attributes;%att.global.linking.attributes;%att.global.responsibility.attributes;%att.global.source.attributes;
 %att.global.attribute.xmlid;
 %att.global.attribute.n;
 %att.global.attribute.xmllang;
 %att.global.attribute.xmlbase;
 %att.global.attribute.xmlspace;'> 
<!ENTITY % att.handFeatures.attribute.scribe '
scribe %teidata.name;  #IMPLIED'>

<!ENTITY % att.handFeatures.attribute.scribeRef '
scribeRef CDATA  #IMPLIED'>

<!ENTITY % att.handFeatures.attribute.script '
script NMTOKENS  #IMPLIED'>

<!ENTITY % att.handFeatures.attribute.scriptRef '
scriptRef CDATA  #IMPLIED'>

<!ENTITY % att.handFeatures.attribute.medium '
medium NMTOKENS  #IMPLIED'>

<!ENTITY % att.handFeatures.attribute.scope '
scope (sole|major|minor) #IMPLIED'>
<!ENTITY % att.handFeatures.attributes '
 %att.handFeatures.attribute.scribe;
 %att.handFeatures.attribute.scribeRef;
 %att.handFeatures.attribute.script;
 %att.handFeatures.attribute.scriptRef;
 %att.handFeatures.attribute.medium;
 %att.handFeatures.attribute.scope;'> 
<!ENTITY % att.internetMedia.attribute.mimeType '
mimeType CDATA  #IMPLIED'>
<!ENTITY % att.internetMedia.attributes '
 %att.internetMedia.attribute.mimeType;'> 
<!ENTITY % att.media.attribute.width '
width %teidata.outputMeasurement;  #IMPLIED'>

<!ENTITY % att.media.attribute.height '
height %teidata.outputMeasurement;  #IMPLIED'>

<!ENTITY % att.media.attribute.scale '
scale %teidata.numeric;  #IMPLIED'>
<!ENTITY % att.media.attributes '%att.internetMedia.attributes;
 %att.media.attribute.width;
 %att.media.attribute.height;
 %att.media.attribute.scale;'> 
<!ENTITY % att.resourced.attribute.url '
url %teidata.pointer;  #REQUIRED'>
<!ENTITY % att.resourced.attributes '
 %att.resourced.attribute.url;'> 
<!ENTITY % att.measurement.attribute.unit '
unit %teidata.enumerated;  #IMPLIED'>

<!ENTITY % att.measurement.attribute.unitRef '
unitRef %teidata.pointer;  #IMPLIED'>

<!ENTITY % att.measurement.attribute.quantity '
quantity %teidata.numeric;  #IMPLIED'>

<!ENTITY % att.measurement.attribute.commodity '
commodity CDATA  #IMPLIED'>
<!ENTITY % att.measurement.attributes '
 %att.measurement.attribute.unit;
 %att.measurement.attribute.unitRef;
 %att.measurement.attribute.quantity;
 %att.measurement.attribute.commodity;'> 
<!ENTITY % att.naming.attribute.role '
role NMTOKENS  #IMPLIED'>

<!ENTITY % att.naming.attribute.nymRef '
nymRef CDATA  #IMPLIED'>
<!ENTITY % att.naming.attributes '%att.canonical.attributes;
 %att.naming.attribute.role;
 %att.naming.attribute.nymRef;'> 
<!ENTITY % att.notated.attribute.notation '
notation %teidata.enumerated;  #IMPLIED'>
<!ENTITY % att.notated.attributes '
 %att.notated.attribute.notation;'> 
<!ENTITY % att.placement.attribute.place '
place NMTOKENS  #IMPLIED'>
<!ENTITY % att.placement.attributes '
 %att.placement.attribute.place;'> 
<!ENTITY % att.typed.attribute.type '
type %teidata.enumerated;  #IMPLIED'>

<!ENTITY % att.typed.attribute.subtype '
subtype %teidata.enumerated;  #IMPLIED'>
<!ENTITY % att.typed.attributes '
 %att.typed.attribute.type;
 %att.typed.attribute.subtype;'> 
<!ENTITY % att.pointing.attribute.targetLang '
targetLang %teidata.language;  #IMPLIED'>

<!ENTITY % att.pointing.attribute.target '
target CDATA  #IMPLIED'>

<!ENTITY % att.pointing.attribute.evaluate '
evaluate (all|one|none) #IMPLIED'>
<!ENTITY % att.pointing.attributes '
 %att.pointing.attribute.targetLang;
 %att.pointing.attribute.target;
 %att.pointing.attribute.evaluate;'> 
<!ENTITY % att.pointing.group.attribute.domains '
domains CDATA  #IMPLIED'>

<!ENTITY % att.pointing.group.attribute.targFunc '
targFunc CDATA  #IMPLIED'>
<!ENTITY % att.pointing.group.attributes '%att.pointing.attributes;%att.typed.attributes;
 %att.pointing.group.attribute.domains;
 %att.pointing.group.attribute.targFunc;'> 
<!ENTITY % att.segLike.attribute.function '
function %teidata.enumerated;  #IMPLIED'>
<!ENTITY % att.segLike.attributes '%att.datcat.attributes;%att.fragmentable.attributes;
 %att.segLike.attribute.function;'> 
<!ENTITY % att.sortable.attribute.sortKey '
sortKey %teidata.word;  #IMPLIED'>
<!ENTITY % att.sortable.attributes '
 %att.sortable.attribute.sortKey;'> 
<!ENTITY % att.edition.attribute.ed '
ed CDATA  #IMPLIED'>

<!ENTITY % att.edition.attribute.edRef '
edRef CDATA  #IMPLIED'>
<!ENTITY % att.edition.attributes '
 %att.edition.attribute.ed;
 %att.edition.attribute.edRef;'> 
<!ENTITY % att.spanning.attribute.spanTo '
spanTo %teidata.pointer;  #IMPLIED'>
<!ENTITY % att.spanning.attributes '
 %att.spanning.attribute.spanTo;'> 
<!ENTITY % att.styleDef.attribute.scheme '
scheme (css|xslfo|free|other) #IMPLIED'>

<!ENTITY % att.styleDef.attribute.schemeVersion '
schemeVersion %teidata.versionNumber;  #IMPLIED'>
<!ENTITY % att.styleDef.attributes '
 %att.styleDef.attribute.scheme;
 %att.styleDef.attribute.schemeVersion;'> 
<!ENTITY % att.timed.attribute.start '
start %teidata.pointer;  #IMPLIED'>

<!ENTITY % att.timed.attribute.end '
end %teidata.pointer;  #IMPLIED'>
<!ENTITY % att.timed.attributes '
 %att.timed.attribute.start;
 %att.timed.attribute.end;'> 
<!ENTITY % att.transcriptional.attribute.status '
status %teidata.enumerated;  "unremarkable" '>

<!ENTITY % att.transcriptional.attribute.cause '
cause %teidata.enumerated;  #IMPLIED'>

<!ENTITY % att.transcriptional.attribute.seq '
seq %teidata.count;  #IMPLIED'>
<!ENTITY % att.transcriptional.attributes '%att.editLike.attributes;%att.written.attributes;
 %att.transcriptional.attribute.status;
 %att.transcriptional.attribute.cause;
 %att.transcriptional.attribute.seq;'> 
<!ENTITY % att.citing.attribute.unit '
unit %teidata.enumerated;  #IMPLIED'>

<!ENTITY % att.citing.attribute.from '
from %teidata.word;  #IMPLIED'>

<!ENTITY % att.citing.attribute.to '
to %teidata.word;  #IMPLIED'>
<!ENTITY % att.citing.attributes '
 %att.citing.attribute.unit;
 %att.citing.attribute.from;
 %att.citing.attribute.to;'> 
<!ENTITY % model.nameLike.agent "name |orgName |persName"> 
<!ENTITY % model.nameLike.agent_sequence "name, orgName, persName"> 
<!ENTITY % model.nameLike.agent_sequenceOptional "name?, orgName?, persName?"> 
<!ENTITY % model.nameLike.agent_sequenceOptionalRepeatable "name*, orgName*, persName*"> 
<!ENTITY % model.nameLike.agent_sequenceRepeatable "name+, orgName+, persName+"> 
<!ENTITY % model.segLike "seg"> 
<!ENTITY % model.segLike_sequence "seg"> 
<!ENTITY % model.segLike_sequenceOptional "seg?"> 
<!ENTITY % model.segLike_sequenceOptionalRepeatable "seg*"> 
<!ENTITY % model.segLike_sequenceRepeatable "seg+"> 
<!ENTITY % model.hiLike "hi |q"> 
<!ENTITY % model.hiLike_sequence "hi, q"> 
<!ENTITY % model.hiLike_sequenceOptional "hi?, q?"> 
<!ENTITY % model.hiLike_sequenceOptionalRepeatable "hi*, q*"> 
<!ENTITY % model.hiLike_sequenceRepeatable "hi+, q+"> 
<!ENTITY % model.emphLike "foreign |emph |distinct |mentioned |soCalled |gloss |term |title"> 
<!ENTITY % model.emphLike_sequence "foreign, emph, distinct, mentioned, soCalled, gloss, term, title"> 
<!ENTITY % model.emphLike_sequenceOptional "foreign?, emph?, distinct?, mentioned?, soCalled?, gloss?, term?, title?"> 
<!ENTITY % model.emphLike_sequenceOptionalRepeatable "foreign*, emph*, distinct*, mentioned*, soCalled*, gloss*, term*, title*"> 
<!ENTITY % model.emphLike_sequenceRepeatable "foreign+, emph+, distinct+, mentioned+, soCalled+, gloss+, term+, title+"> 
<!ENTITY % model.highlighted "%model.hiLike; |%model.emphLike;"> 
<!ENTITY % model.highlighted_sequence "%model.hiLike;, %model.emphLike;"> 
<!ENTITY % model.highlighted_sequenceOptional "%model.hiLike;?, %model.emphLike;?"> 
<!ENTITY % model.highlighted_sequenceOptionalRepeatable "%model.hiLike;*, %model.emphLike;*"> 
<!ENTITY % model.highlighted_sequenceRepeatable "%model.hiLike;+, %model.emphLike;+"> 
<!ENTITY % model.dateLike "date |time"> 
<!ENTITY % model.dateLike_sequence "date, time"> 
<!ENTITY % model.dateLike_sequenceOptional "date?, time?"> 
<!ENTITY % model.dateLike_sequenceOptionalRepeatable "date*, time*"> 
<!ENTITY % model.dateLike_sequenceRepeatable "date+, time+"> 
<!ENTITY % model.measureLike "num |measure |measureGrp |unit |geo"> 
<!ENTITY % model.measureLike_sequence "num, measure, measureGrp, unit, geo"> 
<!ENTITY % model.measureLike_sequenceOptional "num?, measure?, measureGrp?, unit?, geo?"> 
<!ENTITY % model.measureLike_sequenceOptionalRepeatable "num*, measure*, measureGrp*, unit*, geo*"> 
<!ENTITY % model.measureLike_sequenceRepeatable "num+, measure+, measureGrp+, unit+, geo+"> 
<!ENTITY % model.egLike ""> 
<!ENTITY % model.egLike_sequence ""> 
<!ENTITY % model.egLike_sequenceOptional ""> 
<!ENTITY % model.egLike_sequenceOptionalRepeatable ""> 
<!ENTITY % model.egLike_sequenceRepeatable ""> 
<!ENTITY % model.graphicLike "media |graphic |binaryObject"> 
<!ENTITY % model.graphicLike_sequence "media, graphic, binaryObject"> 
<!ENTITY % model.graphicLike_sequenceOptional "media?, graphic?, binaryObject?"> 
<!ENTITY % model.graphicLike_sequenceOptionalRepeatable "media*, graphic*, binaryObject*"> 
<!ENTITY % model.graphicLike_sequenceRepeatable "media+, graphic+, binaryObject+"> 
<!ENTITY % model.offsetLike "offset |geogFeat"> 
<!ENTITY % model.offsetLike_sequence "offset, geogFeat"> 
<!ENTITY % model.offsetLike_sequenceOptional "offset?, geogFeat?"> 
<!ENTITY % model.offsetLike_sequenceOptionalRepeatable "offset*, geogFeat*"> 
<!ENTITY % model.offsetLike_sequenceRepeatable "offset+, geogFeat+"> 
<!ENTITY % model.pPart.msdesc ""> 
<!ENTITY % model.pPart.msdesc_sequence ""> 
<!ENTITY % model.pPart.msdesc_sequenceOptional ""> 
<!ENTITY % model.pPart.msdesc_sequenceOptionalRepeatable ""> 
<!ENTITY % model.pPart.msdesc_sequenceRepeatable ""> 
<!ENTITY % model.pPart.editorial "choice |abbr |expan"> 
<!ENTITY % model.pPart.editorial_sequence "choice, abbr, expan"> 
<!ENTITY % model.pPart.editorial_sequenceOptional "choice?, abbr?, expan?"> 
<!ENTITY % model.pPart.editorial_sequenceOptionalRepeatable "choice*, abbr*, expan*"> 
<!ENTITY % model.pPart.editorial_sequenceRepeatable "choice+, abbr+, expan+"> 
<!ENTITY % model.pPart.transcriptional "sic |corr |reg |orig |add |del |unclear"> 
<!ENTITY % model.pPart.transcriptional_sequence "sic, corr, reg, orig, add, del, unclear"> 
<!ENTITY % model.pPart.transcriptional_sequenceOptional "sic?, corr?, reg?, orig?, add?, del?, unclear?"> 
<!ENTITY % model.pPart.transcriptional_sequenceOptionalRepeatable "sic*, corr*, reg*, orig*, add*, del*, unclear*"> 
<!ENTITY % model.pPart.transcriptional_sequenceRepeatable "sic+, corr+, reg+, orig+, add+, del+, unclear+"> 
<!ENTITY % model.pPart.edit "%model.pPart.editorial; |%model.pPart.transcriptional;"> 
<!ENTITY % model.pPart.edit_sequence "%model.pPart.editorial;, %model.pPart.transcriptional;"> 
<!ENTITY % model.pPart.edit_sequenceOptional "%model.pPart.editorial;?, %model.pPart.transcriptional;?"> 
<!ENTITY % model.pPart.edit_sequenceOptionalRepeatable "%model.pPart.editorial;*, %model.pPart.transcriptional;*"> 
<!ENTITY % model.pPart.edit_sequenceRepeatable "%model.pPart.editorial;+, %model.pPart.transcriptional;+"> 
<!ENTITY % model.ptrLike "ptr |ref"> 
<!ENTITY % model.ptrLike_sequence "ptr, ref"> 
<!ENTITY % model.ptrLike_sequenceOptional "ptr?, ref?"> 
<!ENTITY % model.ptrLike_sequenceOptionalRepeatable "ptr*, ref*"> 
<!ENTITY % model.ptrLike_sequenceRepeatable "ptr+, ref+"> 
<!ENTITY % model.lPart ""> 
<!ENTITY % model.lPart_sequence ""> 
<!ENTITY % model.lPart_sequenceOptional ""> 
<!ENTITY % model.lPart_sequenceOptionalRepeatable ""> 
<!ENTITY % model.lPart_sequenceRepeatable ""> 
<!ENTITY % model.global.meta "index |link |linkGrp |timeline |join |joinGrp |alt |altGrp"> 
<!ENTITY % model.global.meta_sequence "index, link, linkGrp, timeline, join, joinGrp, alt, altGrp"> 
<!ENTITY % model.global.meta_sequenceOptional "index?, link?, linkGrp?, timeline?, join?, joinGrp?, alt?, altGrp?"> 
<!ENTITY % model.global.meta_sequenceOptionalRepeatable "index*, link*, linkGrp*, timeline*, join*, joinGrp*, alt*, altGrp*"> 
<!ENTITY % model.global.meta_sequenceRepeatable "index+, link+, linkGrp+, timeline+, join+, joinGrp+, alt+, altGrp+"> 
<!ENTITY % model.milestoneLike "milestone |gb |pb |lb |cb |anchor"> 
<!ENTITY % model.milestoneLike_sequence "milestone, gb, pb, lb, cb, anchor"> 
<!ENTITY % model.milestoneLike_sequenceOptional "milestone?, gb?, pb?, lb?, cb?, anchor?"> 
<!ENTITY % model.milestoneLike_sequenceOptionalRepeatable "milestone*, gb*, pb*, lb*, cb*, anchor*"> 
<!ENTITY % model.milestoneLike_sequenceRepeatable "milestone+, gb+, pb+, lb+, cb+, anchor+"> 
<!ENTITY % model.gLike ""> 
<!ENTITY % model.gLike_sequence ""> 
<!ENTITY % model.gLike_sequenceOptional ""> 
<!ENTITY % model.gLike_sequenceOptionalRepeatable ""> 
<!ENTITY % model.gLike_sequenceRepeatable ""> 
<!ENTITY % model.oddDecl ""> 
<!ENTITY % model.oddDecl_sequence ""> 
<!ENTITY % model.oddDecl_sequenceOptional ""> 
<!ENTITY % model.oddDecl_sequenceOptionalRepeatable ""> 
<!ENTITY % model.oddDecl_sequenceRepeatable ""> 
<!ENTITY % model.phrase.xml ""> 
<!ENTITY % model.phrase.xml_sequence ""> 
<!ENTITY % model.phrase.xml_sequenceOptional ""> 
<!ENTITY % model.phrase.xml_sequenceOptionalRepeatable ""> 
<!ENTITY % model.phrase.xml_sequenceRepeatable ""> 
<!ENTITY % model.specDescLike ""> 
<!ENTITY % model.specDescLike_sequence ""> 
<!ENTITY % model.specDescLike_sequenceOptional ""> 
<!ENTITY % model.specDescLike_sequenceOptionalRepeatable ""> 
<!ENTITY % model.specDescLike_sequenceRepeatable ""> 
<!ENTITY % model.biblLike "bibl |biblStruct |listBibl |biblFull"> 
<!ENTITY % model.biblLike_sequence "bibl, biblStruct, listBibl, biblFull"> 
<!ENTITY % model.biblLike_sequenceOptional "bibl?, biblStruct?, listBibl?, biblFull?"> 
<!ENTITY % model.biblLike_sequenceOptionalRepeatable "bibl*, biblStruct*, listBibl*, biblFull*"> 
<!ENTITY % model.biblLike_sequenceRepeatable "bibl+, biblStruct+, listBibl+, biblFull+"> 
<!ENTITY % model.headLike "head"> 
<!ENTITY % model.headLike_sequence "head"> 
<!ENTITY % model.headLike_sequenceOptional "head?"> 
<!ENTITY % model.headLike_sequenceOptionalRepeatable "head*"> 
<!ENTITY % model.headLike_sequenceRepeatable "head+"> 
<!ENTITY % model.labelLike "desc |label"> 
<!ENTITY % model.labelLike_sequence "desc, label"> 
<!ENTITY % model.labelLike_sequenceOptional "desc?, label?"> 
<!ENTITY % model.labelLike_sequenceOptionalRepeatable "desc*, label*"> 
<!ENTITY % model.labelLike_sequenceRepeatable "desc+, label+"> 
<!ENTITY % model.listLike "list |listOrg |listEvent |listPerson |listPlace |listRelation |listObject |listNym"> 
<!ENTITY % model.listLike_sequence "list, listOrg, listEvent, listPerson, listPlace, listRelation, listObject, listNym"> 
<!ENTITY % model.listLike_sequenceOptional "list?, listOrg?, listEvent?, listPerson?, listPlace?, listRelation?, listObject?, listNym?"> 
<!ENTITY % model.listLike_sequenceOptionalRepeatable "list*, listOrg*, listEvent*, listPerson*, listPlace*, listRelation*, listObject*, listNym*"> 
<!ENTITY % model.listLike_sequenceRepeatable "list+, listOrg+, listEvent+, listPerson+, listPlace+, listRelation+, listObject+, listNym+"> 
<!ENTITY % model.noteLike "note |noteGrp"> 
<!ENTITY % model.noteLike_sequence "note, noteGrp"> 
<!ENTITY % model.noteLike_sequenceOptional "note?, noteGrp?"> 
<!ENTITY % model.noteLike_sequenceOptionalRepeatable "note*, noteGrp*"> 
<!ENTITY % model.noteLike_sequenceRepeatable "note+, noteGrp+"> 
<!ENTITY % model.lLike "l"> 
<!ENTITY % model.lLike_sequence "l"> 
<!ENTITY % model.lLike_sequenceOptional "l?"> 
<!ENTITY % model.lLike_sequenceOptionalRepeatable "l*"> 
<!ENTITY % model.lLike_sequenceRepeatable "l+"> 
<!ENTITY % model.pLike "p |ab"> 
<!ENTITY % model.pLike_sequence "p, ab"> 
<!ENTITY % model.pLike_sequenceOptional "p?, ab?"> 
<!ENTITY % model.pLike_sequenceOptionalRepeatable "p*, ab*"> 
<!ENTITY % model.pLike_sequenceRepeatable "p+, ab+"> 
<!ENTITY % model.stageLike "stage"> 
<!ENTITY % model.stageLike_sequence "stage"> 
<!ENTITY % model.stageLike_sequenceOptional "stage?"> 
<!ENTITY % model.stageLike_sequenceOptionalRepeatable "stage*"> 
<!ENTITY % model.stageLike_sequenceRepeatable "stage+"> 
<!ENTITY % model.entryPart ""> 
<!ENTITY % model.entryPart_sequence ""> 
<!ENTITY % model.entryPart_sequenceOptional ""> 
<!ENTITY % model.entryPart_sequenceOptionalRepeatable ""> 
<!ENTITY % model.entryPart_sequenceRepeatable ""> 
<!ENTITY % model.eventLike "event |listEvent"> 
<!ENTITY % model.eventLike_sequence "event, listEvent"> 
<!ENTITY % model.eventLike_sequenceOptional "event?, listEvent?"> 
<!ENTITY % model.eventLike_sequenceOptionalRepeatable "event*, listEvent*"> 
<!ENTITY % model.eventLike_sequenceRepeatable "event+, listEvent+"> 
<!ENTITY % model.global.edit "gap |ellipsis"> 
<!ENTITY % model.global.edit_sequence "gap, ellipsis"> 
<!ENTITY % model.global.edit_sequenceOptional "gap?, ellipsis?"> 
<!ENTITY % model.global.edit_sequenceOptionalRepeatable "gap*, ellipsis*"> 
<!ENTITY % model.global.edit_sequenceRepeatable "gap+, ellipsis+"> 
<!ENTITY % model.divPart "%model.lLike; |%model.pLike; |lg |sp"> 
<!ENTITY % model.divPart_sequence "%model.lLike;, %model.pLike;, lg, sp"> 
<!ENTITY % model.divPart_sequenceOptional "%model.lLike;?, %model.pLike;?, lg?, sp?"> 
<!ENTITY % model.divPart_sequenceOptionalRepeatable "%model.lLike;*, %model.pLike;*, lg*, sp*"> 
<!ENTITY % model.divPart_sequenceRepeatable "%model.lLike;+, %model.pLike;+, lg+, sp+"> 
<!ENTITY % model.persStateLike "persName |affiliation |age |education |faith |floruit |gender |langKnowledge |nationality |occupation |persona |persPronouns |residence |sex |socecStatus |state |trait"> 
<!ENTITY % model.persStateLike_sequence "persName, affiliation, age, education, faith, floruit, gender, langKnowledge, nationality, occupation, persona, persPronouns, residence, sex, socecStatus, state, trait"> 
<!ENTITY % model.persStateLike_sequenceOptional "persName?, affiliation?, age?, education?, faith?, floruit?, gender?, langKnowledge?, nationality?, occupation?, persona?, persPronouns?, residence?, sex?, socecStatus?, state?, trait?"> 
<!ENTITY % model.persStateLike_sequenceOptionalRepeatable "persName*, affiliation*, age*, education*, faith*, floruit*, gender*, langKnowledge*, nationality*, occupation*, persona*, persPronouns*, residence*, sex*, socecStatus*, state*, trait*"> 
<!ENTITY % model.persStateLike_sequenceRepeatable "persName+, affiliation+, age+, education+, faith+, floruit+, gender+, langKnowledge+, nationality+, occupation+, persona+, persPronouns+, residence+, sex+, socecStatus+, state+, trait+"> 
<!ENTITY % model.personLike "org |person |personGrp"> 
<!ENTITY % model.personLike_sequence "org, person, personGrp"> 
<!ENTITY % model.personLike_sequenceOptional "org?, person?, personGrp?"> 
<!ENTITY % model.personLike_sequenceOptionalRepeatable "org*, person*, personGrp*"> 
<!ENTITY % model.personLike_sequenceRepeatable "org+, person+, personGrp+"> 
<!ENTITY % model.personPart "%model.biblLike; |%model.eventLike; |%model.persStateLike; |name |idno |birth |death"> 
<!ENTITY % model.personPart_sequence "%model.biblLike;, %model.eventLike;, %model.persStateLike;, name, idno, birth, death"> 
<!ENTITY % model.personPart_sequenceOptional "%model.biblLike;?, %model.eventLike;?, %model.persStateLike;?, name?, idno?, birth?, death?"> 
<!ENTITY % model.personPart_sequenceOptionalRepeatable "%model.biblLike;*, %model.eventLike;*, %model.persStateLike;*, name*, idno*, birth*, death*"> 
<!ENTITY % model.personPart_sequenceRepeatable "%model.biblLike;+, %model.eventLike;+, %model.persStateLike;+, name+, idno+, birth+, death+"> 
<!ENTITY % model.placeNamePart "placeName |bloc |country |region |settlement |district |geogName"> 
<!ENTITY % model.placeNamePart_sequence "placeName, bloc, country, region, settlement, district, geogName"> 
<!ENTITY % model.placeNamePart_sequenceOptional "placeName?, bloc?, country?, region?, settlement?, district?, geogName?"> 
<!ENTITY % model.placeNamePart_sequenceOptionalRepeatable "placeName*, bloc*, country*, region*, settlement*, district*, geogName*"> 
<!ENTITY % model.placeNamePart_sequenceRepeatable "placeName+, bloc+, country+, region+, settlement+, district+, geogName+"> 
<!ENTITY % model.placeStateLike "%model.placeNamePart; |climate |location |population |state |terrain |trait"> 
<!ENTITY % model.placeStateLike_sequence "%model.placeNamePart;, climate, location, population, state, terrain, trait"> 
<!ENTITY % model.placeStateLike_sequenceOptional "%model.placeNamePart;?, climate?, location?, population?, state?, terrain?, trait?"> 
<!ENTITY % model.placeStateLike_sequenceOptionalRepeatable "%model.placeNamePart;*, climate*, location*, population*, state*, terrain*, trait*"> 
<!ENTITY % model.placeStateLike_sequenceRepeatable "%model.placeNamePart;+, climate+, location+, population+, state+, terrain+, trait+"> 
<!ENTITY % model.orgPart "%model.eventLike; |listOrg |listPerson |listPlace"> 
<!ENTITY % model.orgPart_sequence "%model.eventLike;, listOrg, listPerson, listPlace"> 
<!ENTITY % model.orgPart_sequenceOptional "%model.eventLike;?, listOrg?, listPerson?, listPlace?"> 
<!ENTITY % model.orgPart_sequenceOptionalRepeatable "%model.eventLike;*, listOrg*, listPerson*, listPlace*"> 
<!ENTITY % model.orgPart_sequenceRepeatable "%model.eventLike;+, listOrg+, listPerson+, listPlace+"> 
<!ENTITY % model.publicationStmtPart.agency "publisher |distributor |authority"> 
<!ENTITY % model.publicationStmtPart.agency_sequence "publisher, distributor, authority"> 
<!ENTITY % model.publicationStmtPart.agency_sequenceOptional "publisher?, distributor?, authority?"> 
<!ENTITY % model.publicationStmtPart.agency_sequenceOptionalRepeatable "publisher*, distributor*, authority*"> 
<!ENTITY % model.publicationStmtPart.agency_sequenceRepeatable "publisher+, distributor+, authority+"> 
<!ENTITY % model.publicationStmtPart.detail "%model.ptrLike; |address |date |pubPlace |idno |availability"> 
<!ENTITY % model.publicationStmtPart.detail_sequence "%model.ptrLike;, address, date, pubPlace, idno, availability"> 
<!ENTITY % model.publicationStmtPart.detail_sequenceOptional "%model.ptrLike;?, address?, date?, pubPlace?, idno?, availability?"> 
<!ENTITY % model.publicationStmtPart.detail_sequenceOptionalRepeatable "%model.ptrLike;*, address*, date*, pubPlace*, idno*, availability*"> 
<!ENTITY % model.publicationStmtPart.detail_sequenceRepeatable "%model.ptrLike;+, address+, date+, pubPlace+, idno+, availability+"> 
<!ENTITY % model.availabilityPart "licence"> 
<!ENTITY % model.availabilityPart_sequence "licence"> 
<!ENTITY % model.availabilityPart_sequenceOptional "licence?"> 
<!ENTITY % model.availabilityPart_sequenceOptionalRepeatable "licence*"> 
<!ENTITY % model.availabilityPart_sequenceRepeatable "licence+"> 
<!ENTITY % model.certLike ""> 
<!ENTITY % model.certLike_sequence ""> 
<!ENTITY % model.certLike_sequenceOptional ""> 
<!ENTITY % model.certLike_sequenceOptionalRepeatable ""> 
<!ENTITY % model.certLike_sequenceRepeatable ""> 
<!ENTITY % model.descLike "desc"> 
<!ENTITY % model.descLike_sequence "desc"> 
<!ENTITY % model.descLike_sequenceOptional "desc?"> 
<!ENTITY % model.descLike_sequenceOptionalRepeatable "desc*"> 
<!ENTITY % model.descLike_sequenceRepeatable "desc+"> 
<!ENTITY % model.quoteLike "quote |cit"> 
<!ENTITY % model.quoteLike_sequence "quote, cit"> 
<!ENTITY % model.quoteLike_sequenceOptional "quote?, cit?"> 
<!ENTITY % model.quoteLike_sequenceOptionalRepeatable "quote*, cit*"> 
<!ENTITY % model.quoteLike_sequenceRepeatable "quote+, cit+"> 
<!ENTITY % model.attributable "%model.quoteLike; |said |floatingText"> 
<!ENTITY % model.attributable_sequence "%model.quoteLike;, said, floatingText"> 
<!ENTITY % model.attributable_sequenceOptional "%model.quoteLike;?, said?, floatingText?"> 
<!ENTITY % model.attributable_sequenceOptionalRepeatable "%model.quoteLike;*, said*, floatingText*"> 
<!ENTITY % model.attributable_sequenceRepeatable "%model.quoteLike;+, said+, floatingText+"> 
<!ENTITY % model.respLike "author |editor |respStmt |meeting |sponsor |funder |principal"> 
<!ENTITY % model.respLike_sequence "author, editor, respStmt, meeting, sponsor, funder, principal"> 
<!ENTITY % model.respLike_sequenceOptional "author?, editor?, respStmt?, meeting?, sponsor?, funder?, principal?"> 
<!ENTITY % model.respLike_sequenceOptionalRepeatable "author*, editor*, respStmt*, meeting*, sponsor*, funder*, principal*"> 
<!ENTITY % model.respLike_sequenceRepeatable "author+, editor+, respStmt+, meeting+, sponsor+, funder+, principal+"> 
<!ENTITY % model.divWrapper "meeting |byline |dateline |argument |epigraph |salute |docAuthor |docDate"> 
<!ENTITY % model.divWrapper_sequence "meeting, byline, dateline, argument, epigraph, salute, docAuthor, docDate"> 
<!ENTITY % model.divWrapper_sequenceOptional "meeting?, byline?, dateline?, argument?, epigraph?, salute?, docAuthor?, docDate?"> 
<!ENTITY % model.divWrapper_sequenceOptionalRepeatable "meeting*, byline*, dateline*, argument*, epigraph*, salute*, docAuthor*, docDate*"> 
<!ENTITY % model.divWrapper_sequenceRepeatable "meeting+, byline+, dateline+, argument+, epigraph+, salute+, docAuthor+, docDate+"> 
<!ENTITY % model.divTopPart "%model.headLike; |opener |signed"> 
<!ENTITY % model.divTopPart_sequence "%model.headLike;, opener, signed"> 
<!ENTITY % model.divTopPart_sequenceOptional "%model.headLike;?, opener?, signed?"> 
<!ENTITY % model.divTopPart_sequenceOptionalRepeatable "%model.headLike;*, opener*, signed*"> 
<!ENTITY % model.divTopPart_sequenceRepeatable "%model.headLike;+, opener+, signed+"> 
<!ENTITY % model.divTop "%model.divWrapper; |%model.divTopPart;"> 
<!ENTITY % model.divTop_sequence "%model.divWrapper;, %model.divTopPart;"> 
<!ENTITY % model.divTop_sequenceOptional "%model.divWrapper;?, %model.divTopPart;?"> 
<!ENTITY % model.divTop_sequenceOptionalRepeatable "%model.divWrapper;*, %model.divTopPart;*"> 
<!ENTITY % model.divTop_sequenceRepeatable "%model.divWrapper;+, %model.divTopPart;+"> 
<!ENTITY % model.frontPart.drama ""> 
<!ENTITY % model.frontPart.drama_sequence ""> 
<!ENTITY % model.frontPart.drama_sequenceOptional ""> 
<!ENTITY % model.frontPart.drama_sequenceOptionalRepeatable ""> 
<!ENTITY % model.frontPart.drama_sequenceRepeatable ""> 
<!ENTITY % model.pLike.front "head |byline |dateline |argument |epigraph |docTitle |titlePart |docAuthor |docEdition |docImprint |docDate"> 
<!ENTITY % model.pLike.front_sequence "head, byline, dateline, argument, epigraph, docTitle, titlePart, docAuthor, docEdition, docImprint, docDate"> 
<!ENTITY % model.pLike.front_sequenceOptional "head?, byline?, dateline?, argument?, epigraph?, docTitle?, titlePart?, docAuthor?, docEdition?, docImprint?, docDate?"> 
<!ENTITY % model.pLike.front_sequenceOptionalRepeatable "head*, byline*, dateline*, argument*, epigraph*, docTitle*, titlePart*, docAuthor*, docEdition*, docImprint*, docDate*"> 
<!ENTITY % model.pLike.front_sequenceRepeatable "head+, byline+, dateline+, argument+, epigraph+, docTitle+, titlePart+, docAuthor+, docEdition+, docImprint+, docDate+"> 
<!ENTITY % model.divBottomPart "trailer |closer |signed |postscript"> 
<!ENTITY % model.divBottomPart_sequence "trailer, closer, signed, postscript"> 
<!ENTITY % model.divBottomPart_sequenceOptional "trailer?, closer?, signed?, postscript?"> 
<!ENTITY % model.divBottomPart_sequenceOptionalRepeatable "trailer*, closer*, signed*, postscript*"> 
<!ENTITY % model.divBottomPart_sequenceRepeatable "trailer+, closer+, signed+, postscript+"> 
<!ENTITY % model.divBottom "%model.divWrapper; |%model.divBottomPart;"> 
<!ENTITY % model.divBottom_sequence "%model.divWrapper;, %model.divBottomPart;"> 
<!ENTITY % model.divBottom_sequenceOptional "%model.divWrapper;?, %model.divBottomPart;?"> 
<!ENTITY % model.divBottom_sequenceOptionalRepeatable "%model.divWrapper;*, %model.divBottomPart;*"> 
<!ENTITY % model.divBottom_sequenceRepeatable "%model.divWrapper;+, %model.divBottomPart;+"> 
<!ENTITY % model.titlepagePart "graphic |binaryObject |byline |argument |epigraph |docTitle |titlePart |docAuthor |imprimatur |docEdition |docImprint |docDate"> 
<!ENTITY % model.titlepagePart_sequence "graphic, binaryObject, byline, argument, epigraph, docTitle, titlePart, docAuthor, imprimatur, docEdition, docImprint, docDate"> 
<!ENTITY % model.titlepagePart_sequenceOptional "graphic?, binaryObject?, byline?, argument?, epigraph?, docTitle?, titlePart?, docAuthor?, imprimatur?, docEdition?, docImprint?, docDate?"> 
<!ENTITY % model.titlepagePart_sequenceOptionalRepeatable "graphic*, binaryObject*, byline*, argument*, epigraph*, docTitle*, titlePart*, docAuthor*, imprimatur*, docEdition*, docImprint*, docDate*"> 
<!ENTITY % model.titlepagePart_sequenceRepeatable "graphic+, binaryObject+, byline+, argument+, epigraph+, docTitle+, titlePart+, docAuthor+, imprimatur+, docEdition+, docImprint+, docDate+"> 
<!ENTITY % model.choicePart "sic |corr |reg |orig |unclear |abbr |expan |seg"> 
<!ENTITY % model.choicePart_sequence "sic, corr, reg, orig, unclear, abbr, expan, seg"> 
<!ENTITY % model.choicePart_sequenceOptional "sic?, corr?, reg?, orig?, unclear?, abbr?, expan?, seg?"> 
<!ENTITY % model.choicePart_sequenceOptionalRepeatable "sic*, corr*, reg*, orig*, unclear*, abbr*, expan*, seg*"> 
<!ENTITY % model.choicePart_sequenceRepeatable "sic+, corr+, reg+, orig+, unclear+, abbr+, expan+, seg+"> 
<!ENTITY % model.imprintPart "publisher |biblScope |pubPlace |distributor"> 
<!ENTITY % model.imprintPart_sequence "publisher, biblScope, pubPlace, distributor"> 
<!ENTITY % model.imprintPart_sequenceOptional "publisher?, biblScope?, pubPlace?, distributor?"> 
<!ENTITY % model.imprintPart_sequenceOptionalRepeatable "publisher*, biblScope*, pubPlace*, distributor*"> 
<!ENTITY % model.imprintPart_sequenceRepeatable "publisher+, biblScope+, pubPlace+, distributor+"> 
<!ENTITY % model.catDescPart "textDesc"> 
<!ENTITY % model.catDescPart_sequence "textDesc"> 
<!ENTITY % model.catDescPart_sequenceOptional "textDesc?"> 
<!ENTITY % model.catDescPart_sequenceOptionalRepeatable "textDesc*"> 
<!ENTITY % model.catDescPart_sequenceRepeatable "textDesc+"> 
<!ENTITY % model.settingPart "locale |activity |placeName"> 
<!ENTITY % model.settingPart_sequence "locale, activity, placeName"> 
<!ENTITY % model.settingPart_sequenceOptional "locale?, activity?, placeName?"> 
<!ENTITY % model.settingPart_sequenceOptionalRepeatable "locale*, activity*, placeName*"> 
<!ENTITY % model.settingPart_sequenceRepeatable "locale+, activity+, placeName+"> 
<!ENTITY % model.textDescPart "channel |constitution |derivation |domain |factuality |interaction |preparedness"> 
<!ENTITY % model.textDescPart_sequence "channel, constitution, derivation, domain, factuality, interaction, preparedness"> 
<!ENTITY % model.textDescPart_sequenceOptional "channel?, constitution?, derivation?, domain?, factuality?, interaction?, preparedness?"> 
<!ENTITY % model.textDescPart_sequenceOptionalRepeatable "channel*, constitution*, derivation*, domain*, factuality*, interaction*, preparedness*"> 
<!ENTITY % model.textDescPart_sequenceRepeatable "channel+, constitution+, derivation+, domain+, factuality+, interaction+, preparedness+"> 
<!ENTITY % model.addressLike "email |address |affiliation"> 
<!ENTITY % model.addressLike_sequence "email, address, affiliation"> 
<!ENTITY % model.addressLike_sequenceOptional "email?, address?, affiliation?"> 
<!ENTITY % model.addressLike_sequenceOptionalRepeatable "email*, address*, affiliation*"> 
<!ENTITY % model.addressLike_sequenceRepeatable "email+, address+, affiliation+"> 
<!ENTITY % model.nameLike "%model.nameLike.agent; |%model.offsetLike; |%model.placeStateLike; |rs |idno |%model.persNamePart; |eventName |objectName"> 
<!ENTITY % model.nameLike_sequence "%model.nameLike.agent;, %model.offsetLike;, %model.placeStateLike;, rs, idno, %model.persNamePart;, eventName, objectName"> 
<!ENTITY % model.nameLike_sequenceOptional "%model.nameLike.agent;?, %model.offsetLike;?, %model.placeStateLike;?, rs?, idno?, %model.persNamePart;?, eventName?, objectName?"> 
<!ENTITY % model.nameLike_sequenceOptionalRepeatable "%model.nameLike.agent;*, %model.offsetLike;*, %model.placeStateLike;*, rs*, idno*, %model.persNamePart;*, eventName*, objectName*"> 
<!ENTITY % model.nameLike_sequenceRepeatable "%model.nameLike.agent;+, %model.offsetLike;+, %model.placeStateLike;+, rs+, idno+, %model.persNamePart;+, eventName+, objectName+"> 
<!ENTITY % model.global "%model.global.meta; |%model.milestoneLike; |%model.noteLike; |%model.global.edit;"> 
<!ENTITY % model.global_sequence "%model.global.meta;, %model.milestoneLike;, %model.noteLike;, %model.global.edit;"> 
<!ENTITY % model.global_sequenceOptional "%model.global.meta;?, %model.milestoneLike;?, %model.noteLike;?, %model.global.edit;?"> 
<!ENTITY % model.global_sequenceOptionalRepeatable "%model.global.meta;*, %model.milestoneLike;*, %model.noteLike;*, %model.global.edit;*"> 
<!ENTITY % model.global_sequenceRepeatable "%model.global.meta;+, %model.milestoneLike;+, %model.noteLike;+, %model.global.edit;+"> 
<!ENTITY % model.biblPart "%model.respLike; |%model.imprintPart; |quote |series |citedRange |bibl |relatedItem |textLang |edition |extent |availability |listRelation |objectIdentifier"> 
<!ENTITY % model.biblPart_sequence "%model.respLike;, %model.imprintPart;, quote, series, citedRange, bibl, relatedItem, textLang, edition, extent, availability, listRelation, objectIdentifier"> 
<!ENTITY % model.biblPart_sequenceOptional "%model.respLike;?, %model.imprintPart;?, quote?, series?, citedRange?, bibl?, relatedItem?, textLang?, edition?, extent?, availability?, listRelation?, objectIdentifier?"> 
<!ENTITY % model.biblPart_sequenceOptionalRepeatable "%model.respLike;*, %model.imprintPart;*, quote*, series*, citedRange*, bibl*, relatedItem*, textLang*, edition*, extent*, availability*, listRelation*, objectIdentifier*"> 
<!ENTITY % model.biblPart_sequenceRepeatable "%model.respLike;+, %model.imprintPart;+, quote+, series+, citedRange+, bibl+, relatedItem+, textLang+, edition+, extent+, availability+, listRelation+, objectIdentifier+"> 
<!ENTITY % model.frontPart "listBibl |divGen |titlePage"> 
<!ENTITY % model.frontPart_sequence "listBibl, divGen, titlePage"> 
<!ENTITY % model.frontPart_sequenceOptional "listBibl?, divGen?, titlePage?"> 
<!ENTITY % model.frontPart_sequenceOptionalRepeatable "listBibl*, divGen*, titlePage*"> 
<!ENTITY % model.frontPart_sequenceRepeatable "listBibl+, divGen+, titlePage+"> 
<!ENTITY % model.addrPart "%model.nameLike; |addrLine |street |postCode |postBox"> 
<!ENTITY % model.addrPart_sequence "%model.nameLike;, addrLine, street, postCode, postBox"> 
<!ENTITY % model.addrPart_sequenceOptional "%model.nameLike;?, addrLine?, street?, postCode?, postBox?"> 
<!ENTITY % model.addrPart_sequenceOptionalRepeatable "%model.nameLike;*, addrLine*, street*, postCode*, postBox*"> 
<!ENTITY % model.addrPart_sequenceRepeatable "%model.nameLike;+, addrLine+, street+, postCode+, postBox+"> 
<!ENTITY % model.pPart.data "%model.dateLike; |%model.measureLike; |%model.addressLike; |%model.nameLike;"> 
<!ENTITY % model.pPart.data_sequence "%model.dateLike;, %model.measureLike;, %model.addressLike;, %model.nameLike;"> 
<!ENTITY % model.pPart.data_sequenceOptional "%model.dateLike;?, %model.measureLike;?, %model.addressLike;?, %model.nameLike;?"> 
<!ENTITY % model.pPart.data_sequenceOptionalRepeatable "%model.dateLike;*, %model.measureLike;*, %model.addressLike;*, %model.nameLike;*"> 
<!ENTITY % model.pPart.data_sequenceRepeatable "%model.dateLike;+, %model.measureLike;+, %model.addressLike;+, %model.nameLike;+"> 
<!ENTITY % model.inter "%model.biblLike; |%model.labelLike; |%model.listLike; |%model.stageLike; |%model.attributable;"> 
<!ENTITY % model.inter_sequence "%model.biblLike;, %model.labelLike;, %model.listLike;, %model.stageLike;, %model.attributable;"> 
<!ENTITY % model.inter_sequenceOptional "%model.biblLike;?, %model.labelLike;?, %model.listLike;?, %model.stageLike;?, %model.attributable;?"> 
<!ENTITY % model.inter_sequenceOptionalRepeatable "%model.biblLike;*, %model.labelLike;*, %model.listLike;*, %model.stageLike;*, %model.attributable;*"> 
<!ENTITY % model.inter_sequenceRepeatable "%model.biblLike;+, %model.labelLike;+, %model.listLike;+, %model.stageLike;+, %model.attributable;+"> 
<!ENTITY % model.cmc ""> 
<!ENTITY % model.cmc_sequence ""> 
<!ENTITY % model.cmc_sequenceOptional ""> 
<!ENTITY % model.cmc_sequenceOptionalRepeatable ""> 
<!ENTITY % model.cmc_sequenceRepeatable ""> 
<!ENTITY % model.common "%model.divPart; |%model.inter; |q"> 
<!ENTITY % model.common_sequence "%model.divPart;, %model.inter;, q"> 
<!ENTITY % model.common_sequenceOptional "%model.divPart;?, %model.inter;?, q?"> 
<!ENTITY % model.common_sequenceOptionalRepeatable "%model.divPart;*, %model.inter;*, q*"> 
<!ENTITY % model.common_sequenceRepeatable "%model.divPart;+, %model.inter;+, q+"> 
<!ENTITY % model.phrase "%model.segLike; |%model.highlighted; |%model.graphicLike; |%model.pPart.edit; |%model.ptrLike; |%model.pPart.data; |ruby"> 
<!ENTITY % model.phrase_sequence "%model.segLike;, %model.highlighted;, %model.graphicLike;, %model.pPart.edit;, %model.ptrLike;, %model.pPart.data;, ruby"> 
<!ENTITY % model.phrase_sequenceOptional "%model.segLike;?, %model.highlighted;?, %model.graphicLike;?, %model.pPart.edit;?, %model.ptrLike;?, %model.pPart.data;?, ruby?"> 
<!ENTITY % model.phrase_sequenceOptionalRepeatable "%model.segLike;*, %model.highlighted;*, %model.graphicLike;*, %model.pPart.edit;*, %model.ptrLike;*, %model.pPart.data;*, ruby*"> 
<!ENTITY % model.phrase_sequenceRepeatable "%model.segLike;+, %model.highlighted;+, %model.graphicLike;+, %model.pPart.edit;+, %model.ptrLike;+, %model.pPart.data;+, ruby+"> 
<!ENTITY % model.paraPart "%model.lLike; |%model.global; |%model.inter; |%model.phrase; |lg"> 
<!ENTITY % model.paraPart_sequence "%model.lLike;, %model.global;, %model.inter;, %model.phrase;, lg"> 
<!ENTITY % model.paraPart_sequenceOptional "%model.lLike;?, %model.global;?, %model.inter;?, %model.phrase;?, lg?"> 
<!ENTITY % model.paraPart_sequenceOptionalRepeatable "%model.lLike;*, %model.global;*, %model.inter;*, %model.phrase;*, lg*"> 
<!ENTITY % model.paraPart_sequenceRepeatable "%model.lLike;+, %model.global;+, %model.inter;+, %model.phrase;+, lg+"> 
<!ENTITY % model.limitedPhrase "%model.hiLike; |%model.emphLike; |%model.pPart.editorial; |%model.ptrLike; |%model.pPart.data;"> 
<!ENTITY % model.limitedPhrase_sequence "%model.hiLike;, %model.emphLike;, %model.pPart.editorial;, %model.ptrLike;, %model.pPart.data;"> 
<!ENTITY % model.limitedPhrase_sequenceOptional "%model.hiLike;?, %model.emphLike;?, %model.pPart.editorial;?, %model.ptrLike;?, %model.pPart.data;?"> 
<!ENTITY % model.limitedPhrase_sequenceOptionalRepeatable "%model.hiLike;*, %model.emphLike;*, %model.pPart.editorial;*, %model.ptrLike;*, %model.pPart.data;*"> 
<!ENTITY % model.limitedPhrase_sequenceRepeatable "%model.hiLike;+, %model.emphLike;+, %model.pPart.editorial;+, %model.ptrLike;+, %model.pPart.data;+"> 
<!ENTITY % model.divLike "div"> 
<!ENTITY % model.divLike_sequence "div"> 
<!ENTITY % model.divLike_sequenceOptional "div?"> 
<!ENTITY % model.divLike_sequenceOptionalRepeatable "div*"> 
<!ENTITY % model.divLike_sequenceRepeatable "div+"> 
<!ENTITY % model.divGenLike "divGen"> 
<!ENTITY % model.divGenLike_sequence "divGen"> 
<!ENTITY % model.divGenLike_sequenceOptional "divGen?"> 
<!ENTITY % model.divGenLike_sequenceOptionalRepeatable "divGen*"> 
<!ENTITY % model.divGenLike_sequenceRepeatable "divGen+"> 
<!ENTITY % model.div1Like "div1"> 
<!ENTITY % model.div1Like_sequence "div1"> 
<!ENTITY % model.div1Like_sequenceOptional "div1?"> 
<!ENTITY % model.div1Like_sequenceOptionalRepeatable "div1*"> 
<!ENTITY % model.div1Like_sequenceRepeatable "div1+"> 
<!ENTITY % model.div2Like "div2"> 
<!ENTITY % model.div2Like_sequence "div2"> 
<!ENTITY % model.div2Like_sequenceOptional "div2?"> 
<!ENTITY % model.div2Like_sequenceOptionalRepeatable "div2*"> 
<!ENTITY % model.div2Like_sequenceRepeatable "div2+"> 
<!ENTITY % model.div3Like "div3"> 
<!ENTITY % model.div3Like_sequence "div3"> 
<!ENTITY % model.div3Like_sequenceOptional "div3?"> 
<!ENTITY % model.div3Like_sequenceOptionalRepeatable "div3*"> 
<!ENTITY % model.div3Like_sequenceRepeatable "div3+"> 
<!ENTITY % model.div4Like "div4"> 
<!ENTITY % model.div4Like_sequence "div4"> 
<!ENTITY % model.div4Like_sequenceOptional "div4?"> 
<!ENTITY % model.div4Like_sequenceOptionalRepeatable "div4*"> 
<!ENTITY % model.div4Like_sequenceRepeatable "div4+"> 
<!ENTITY % model.div5Like "div5"> 
<!ENTITY % model.div5Like_sequence "div5"> 
<!ENTITY % model.div5Like_sequenceOptional "div5?"> 
<!ENTITY % model.div5Like_sequenceOptionalRepeatable "div5*"> 
<!ENTITY % model.div5Like_sequenceRepeatable "div5+"> 
<!ENTITY % model.div6Like "div6"> 
<!ENTITY % model.div6Like_sequence "div6"> 
<!ENTITY % model.div6Like_sequenceOptional "div6?"> 
<!ENTITY % model.div6Like_sequenceOptionalRepeatable "div6*"> 
<!ENTITY % model.div6Like_sequenceRepeatable "div6+"> 
<!ENTITY % model.div7Like "div7"> 
<!ENTITY % model.div7Like_sequence "div7"> 
<!ENTITY % model.div7Like_sequenceOptional "div7?"> 
<!ENTITY % model.div7Like_sequenceOptionalRepeatable "div7*"> 
<!ENTITY % model.div7Like_sequenceRepeatable "div7+"> 
<!ENTITY % model.annotationLike "note |annotation"> 
<!ENTITY % model.annotationLike_sequence "note, annotation"> 
<!ENTITY % model.annotationLike_sequenceOptional "note?, annotation?"> 
<!ENTITY % model.annotationLike_sequenceOptionalRepeatable "note*, annotation*"> 
<!ENTITY % model.annotationLike_sequenceRepeatable "note+, annotation+"> 
<!ENTITY % model.annotationPart.body "ptr |ref |note"> 
<!ENTITY % model.annotationPart.body_sequence "ptr, ref, note"> 
<!ENTITY % model.annotationPart.body_sequenceOptional "ptr?, ref?, note?"> 
<!ENTITY % model.annotationPart.body_sequenceOptionalRepeatable "ptr*, ref*, note*"> 
<!ENTITY % model.annotationPart.body_sequenceRepeatable "ptr+, ref+, note+"> 
<!ENTITY % model.applicationLike "application"> 
<!ENTITY % model.applicationLike_sequence "application"> 
<!ENTITY % model.applicationLike_sequenceOptional "application?"> 
<!ENTITY % model.applicationLike_sequenceOptionalRepeatable "application*"> 
<!ENTITY % model.applicationLike_sequenceRepeatable "application+"> 
<!ENTITY % model.teiHeaderPart "encodingDesc |profileDesc |xenoData"> 
<!ENTITY % model.teiHeaderPart_sequence "encodingDesc, profileDesc, xenoData"> 
<!ENTITY % model.teiHeaderPart_sequenceOptional "encodingDesc?, profileDesc?, xenoData?"> 
<!ENTITY % model.teiHeaderPart_sequenceOptionalRepeatable "encodingDesc*, profileDesc*, xenoData*"> 
<!ENTITY % model.teiHeaderPart_sequenceRepeatable "encodingDesc+, profileDesc+, xenoData+"> 
<!ENTITY % model.sourceDescPart ""> 
<!ENTITY % model.sourceDescPart_sequence ""> 
<!ENTITY % model.sourceDescPart_sequenceOptional ""> 
<!ENTITY % model.sourceDescPart_sequenceOptionalRepeatable ""> 
<!ENTITY % model.sourceDescPart_sequenceRepeatable ""> 
<!ENTITY % model.encodingDescPart "schemaRef |projectDesc |samplingDecl |editorialDecl |tagsDecl |styleDefDecl |refsDecl |listPrefixDef |classDecl |geoDecl |unitDecl |appInfo"> 
<!ENTITY % model.encodingDescPart_sequence "schemaRef, projectDesc, samplingDecl, editorialDecl, tagsDecl, styleDefDecl, refsDecl, listPrefixDef, classDecl, geoDecl, unitDecl, appInfo"> 
<!ENTITY % model.encodingDescPart_sequenceOptional "schemaRef?, projectDesc?, samplingDecl?, editorialDecl?, tagsDecl?, styleDefDecl?, refsDecl?, listPrefixDef?, classDecl?, geoDecl?, unitDecl?, appInfo?"> 
<!ENTITY % model.encodingDescPart_sequenceOptionalRepeatable "schemaRef*, projectDesc*, samplingDecl*, editorialDecl*, tagsDecl*, styleDefDecl*, refsDecl*, listPrefixDef*, classDecl*, geoDecl*, unitDecl*, appInfo*"> 
<!ENTITY % model.encodingDescPart_sequenceRepeatable "schemaRef+, projectDesc+, samplingDecl+, editorialDecl+, tagsDecl+, styleDefDecl+, refsDecl+, listPrefixDef+, classDecl+, geoDecl+, unitDecl+, appInfo+"> 
<!ENTITY % model.editorialDeclPart "correction |normalization |quotation |hyphenation |segmentation |stdVals |interpretation |punctuation"> 
<!ENTITY % model.editorialDeclPart_sequence "correction, normalization, quotation, hyphenation, segmentation, stdVals, interpretation, punctuation"> 
<!ENTITY % model.editorialDeclPart_sequenceOptional "correction?, normalization?, quotation?, hyphenation?, segmentation?, stdVals?, interpretation?, punctuation?"> 
<!ENTITY % model.editorialDeclPart_sequenceOptionalRepeatable "correction*, normalization*, quotation*, hyphenation*, segmentation*, stdVals*, interpretation*, punctuation*"> 
<!ENTITY % model.editorialDeclPart_sequenceRepeatable "correction+, normalization+, quotation+, hyphenation+, segmentation+, stdVals+, interpretation+, punctuation+"> 
<!ENTITY % model.profileDescPart "abstract |creation |langUsage |textClass |calendarDesc |correspDesc |textDesc |particDesc |settingDesc"> 
<!ENTITY % model.profileDescPart_sequence "abstract, creation, langUsage, textClass, calendarDesc, correspDesc, textDesc, particDesc, settingDesc"> 
<!ENTITY % model.profileDescPart_sequenceOptional "abstract?, creation?, langUsage?, textClass?, calendarDesc?, correspDesc?, textDesc?, particDesc?, settingDesc?"> 
<!ENTITY % model.profileDescPart_sequenceOptionalRepeatable "abstract*, creation*, langUsage*, textClass*, calendarDesc*, correspDesc*, textDesc*, particDesc*, settingDesc*"> 
<!ENTITY % model.profileDescPart_sequenceRepeatable "abstract+, creation+, langUsage+, textClass+, calendarDesc+, correspDesc+, textDesc+, particDesc+, settingDesc+"> 
<!ENTITY % model.standOffPart "%model.global.meta; |%model.biblLike; |%model.listLike; |%model.annotationLike; |xenoData |listChange |seg |listAnnotation"> 
<!ENTITY % model.standOffPart_sequence "%model.global.meta;, %model.biblLike;, %model.listLike;, %model.annotationLike;, xenoData, listChange, seg, listAnnotation"> 
<!ENTITY % model.standOffPart_sequenceOptional "%model.global.meta;?, %model.biblLike;?, %model.listLike;?, %model.annotationLike;?, xenoData?, listChange?, seg?, listAnnotation?"> 
<!ENTITY % model.standOffPart_sequenceOptionalRepeatable "%model.global.meta;*, %model.biblLike;*, %model.listLike;*, %model.annotationLike;*, xenoData*, listChange*, seg*, listAnnotation*"> 
<!ENTITY % model.standOffPart_sequenceRepeatable "%model.global.meta;+, %model.biblLike;+, %model.listLike;+, %model.annotationLike;+, xenoData+, listChange+, seg+, listAnnotation+"> 
<!ENTITY % att.formula.attribute.formula '
formula %teidata.xpath;  #IMPLIED'>
<!ENTITY % att.formula.attributes '
 %att.formula.attribute.formula;'> 
<!ENTITY % att.locatable.attribute.where '
where CDATA  #IMPLIED'>
<!ENTITY % att.locatable.attributes '
 %att.locatable.attribute.where;'> 
<!ENTITY % model.correspActionPart "%model.dateLike; |%model.addressLike; |%model.nameLike; |note |noteGrp"> 
<!ENTITY % model.correspActionPart_sequence "%model.dateLike;, %model.addressLike;, %model.nameLike;, note, noteGrp"> 
<!ENTITY % model.correspActionPart_sequenceOptional "%model.dateLike;?, %model.addressLike;?, %model.nameLike;?, note?, noteGrp?"> 
<!ENTITY % model.correspActionPart_sequenceOptionalRepeatable "%model.dateLike;*, %model.addressLike;*, %model.nameLike;*, note*, noteGrp*"> 
<!ENTITY % model.correspActionPart_sequenceRepeatable "%model.dateLike;+, %model.addressLike;+, %model.nameLike;+, note+, noteGrp+"> 
<!ENTITY % model.correspContextPart "%model.ptrLike; |%model.pLike; |note |noteGrp"> 
<!ENTITY % model.correspContextPart_sequence "%model.ptrLike;, %model.pLike;, note, noteGrp"> 
<!ENTITY % model.correspContextPart_sequenceOptional "%model.ptrLike;?, %model.pLike;?, note?, noteGrp?"> 
<!ENTITY % model.correspContextPart_sequenceOptionalRepeatable "%model.ptrLike;*, %model.pLike;*, note*, noteGrp*"> 
<!ENTITY % model.correspContextPart_sequenceRepeatable "%model.ptrLike;+, %model.pLike;+, note+, noteGrp+"> 
<!ENTITY % model.correspDescPart "note |noteGrp |correspAction |correspContext"> 
<!ENTITY % model.correspDescPart_sequence "note, noteGrp, correspAction, correspContext"> 
<!ENTITY % model.correspDescPart_sequenceOptional "note?, noteGrp?, correspAction?, correspContext?"> 
<!ENTITY % model.correspDescPart_sequenceOptionalRepeatable "note*, noteGrp*, correspAction*, correspContext*"> 
<!ENTITY % model.correspDescPart_sequenceRepeatable "note+, noteGrp+, correspAction+, correspContext+"> 
<!ENTITY % model.resource "text |standOff"> 
<!ENTITY % model.resource_sequence "text, standOff"> 
<!ENTITY % model.resource_sequenceOptional "text?, standOff?"> 
<!ENTITY % model.resource_sequenceOptionalRepeatable "text*, standOff*"> 
<!ENTITY % model.resource_sequenceRepeatable "text+, standOff+"> 
<!ENTITY % model.describedResource "teiCorpus |TEI"> 
<!ENTITY % model.describedResource_sequence "teiCorpus, TEI"> 
<!ENTITY % model.describedResource_sequenceOptional "teiCorpus?, TEI?"> 
<!ENTITY % model.describedResource_sequenceOptionalRepeatable "teiCorpus*, TEI*"> 
<!ENTITY % model.describedResource_sequenceRepeatable "teiCorpus+, TEI+"> 
<!ENTITY % model.objectLike "object |listObject"> 
<!ENTITY % model.objectLike_sequence "object, listObject"> 
<!ENTITY % model.objectLike_sequenceOptional "object?, listObject?"> 
<!ENTITY % model.objectLike_sequenceOptionalRepeatable "object*, listObject*"> 
<!ENTITY % model.objectLike_sequenceRepeatable "object+, listObject+"> 
<!ENTITY % att.personal.attribute.full '
full (yes|abb|init) "yes" '>

<!ENTITY % att.personal.attribute.sort '
sort %teidata.count;  #IMPLIED'>
<!ENTITY % att.personal.attributes '%att.naming.attributes;
 %att.personal.attribute.full;
 %att.personal.attribute.sort;'> 
<!ENTITY % model.placeLike "place"> 
<!ENTITY % model.placeLike_sequence "place"> 
<!ENTITY % model.placeLike_sequenceOptional "place?"> 
<!ENTITY % model.placeLike_sequenceOptionalRepeatable "place*"> 
<!ENTITY % model.placeLike_sequenceRepeatable "place+"> 
<!ENTITY % att.calendarSystem.attribute.calendar '
calendar CDATA  #IMPLIED'>
<!ENTITY % att.calendarSystem.attributes '
 %att.calendarSystem.attribute.calendar;'> 
<!ENTITY % att.milestoneUnit.attribute.unit '
unit %teidata.enumerated;  #REQUIRED'>
<!ENTITY % att.milestoneUnit.attributes '
 %att.milestoneUnit.attribute.unit;'> 
<!ENTITY % att.citeStructurePart.attribute.use '
use %teidata.xpath;  #REQUIRED'>
<!ENTITY % att.citeStructurePart.attributes '
 %att.citeStructurePart.attribute.use;'> 
<!ENTITY % att.patternReplacement.attribute.matchPattern '
matchPattern %teidata.pattern;  #REQUIRED'>

<!ENTITY % att.patternReplacement.attribute.replacementPattern '
replacementPattern %teidata.replacement;  #REQUIRED'>
<!ENTITY % att.patternReplacement.attributes '
 %att.patternReplacement.attribute.matchPattern;
 %att.patternReplacement.attribute.replacementPattern;'> 
<!ENTITY % att.datable.custom.attribute.when-custom '
when-custom CDATA  #IMPLIED'>

<!ENTITY % att.datable.custom.attribute.notBefore-custom '
notBefore-custom CDATA  #IMPLIED'>

<!ENTITY % att.datable.custom.attribute.notAfter-custom '
notAfter-custom CDATA  #IMPLIED'>

<!ENTITY % att.datable.custom.attribute.from-custom '
from-custom CDATA  #IMPLIED'>

<!ENTITY % att.datable.custom.attribute.to-custom '
to-custom CDATA  #IMPLIED'>

<!ENTITY % att.datable.custom.attribute.datingPoint '
datingPoint %teidata.pointer;  #IMPLIED'>

<!ENTITY % att.datable.custom.attribute.datingMethod '
datingMethod %teidata.pointer;  #IMPLIED'>
<!ENTITY % att.datable.custom.attributes '
 %att.datable.custom.attribute.when-custom;
 %att.datable.custom.attribute.notBefore-custom;
 %att.datable.custom.attribute.notAfter-custom;
 %att.datable.custom.attribute.from-custom;
 %att.datable.custom.attribute.to-custom;
 %att.datable.custom.attribute.datingPoint;
 %att.datable.custom.attribute.datingMethod;'> 
<!ENTITY % model.persNamePart "surname |forename |genName |nameLink |addName |roleName |persPronouns"> 
<!ENTITY % model.persNamePart_sequence "surname, forename, genName, nameLink, addName, roleName, persPronouns"> 
<!ENTITY % model.persNamePart_sequenceOptional "surname?, forename?, genName?, nameLink?, addName?, roleName?, persPronouns?"> 
<!ENTITY % model.persNamePart_sequenceOptionalRepeatable "surname*, forename*, genName*, nameLink*, addName*, roleName*, persPronouns*"> 
<!ENTITY % model.persNamePart_sequenceRepeatable "surname+, forename+, genName+, nameLink+, addName+, roleName+, persPronouns+"> 
<!ENTITY % att.datable.iso.attribute.when-iso '
when-iso %teidata.temporal.iso;  #IMPLIED'>

<!ENTITY % att.datable.iso.attribute.notBefore-iso '
notBefore-iso %teidata.temporal.iso;  #IMPLIED'>

<!ENTITY % att.datable.iso.attribute.notAfter-iso '
notAfter-iso %teidata.temporal.iso;  #IMPLIED'>

<!ENTITY % att.datable.iso.attribute.from-iso '
from-iso %teidata.temporal.iso;  #IMPLIED'>

<!ENTITY % att.datable.iso.attribute.to-iso '
to-iso %teidata.temporal.iso;  #IMPLIED'>
<!ENTITY % att.datable.iso.attributes '
 %att.datable.iso.attribute.when-iso;
 %att.datable.iso.attribute.notBefore-iso;
 %att.datable.iso.attribute.notAfter-iso;
 %att.datable.iso.attribute.from-iso;
 %att.datable.iso.attribute.to-iso;'> 
<!ENTITY % att.global.linking.attribute.corresp '
corresp CDATA  #IMPLIED'>

<!ENTITY % att.global.linking.attribute.synch '
synch CDATA  #IMPLIED'>

<!ENTITY % att.global.linking.attribute.sameAs '
sameAs %teidata.pointer;  #IMPLIED'>

<!ENTITY % att.global.linking.attribute.copyOf '
copyOf %teidata.pointer;  #IMPLIED'>

<!ENTITY % att.global.linking.attribute.next '
next %teidata.pointer;  #IMPLIED'>

<!ENTITY % att.global.linking.attribute.prev '
prev %teidata.pointer;  #IMPLIED'>

<!ENTITY % att.global.linking.attribute.exclude '
exclude CDATA  #IMPLIED'>

<!ENTITY % att.global.linking.attribute.select '
select CDATA  #IMPLIED'>
<!ENTITY % att.global.linking.attributes '
 %att.global.linking.attribute.corresp;
 %att.global.linking.attribute.synch;
 %att.global.linking.attribute.sameAs;
 %att.global.linking.attribute.copyOf;
 %att.global.linking.attribute.next;
 %att.global.linking.attribute.prev;
 %att.global.linking.attribute.exclude;
 %att.global.linking.attribute.select;'> 
<!-- start rest of patterns -->

<!ENTITY % macro.abContent '(#PCDATA|%model.paraPart;|ab)*' >

<!ENTITY % macro.paraContent '(#PCDATA|%model.paraPart;)*' >

<!ENTITY % macro.limitedContent '(#PCDATA|%model.limitedPhrase;|%model.inter;)*' >

<!ENTITY % macro.phraseSeq '(#PCDATA|_DUMMY_model.gLike|%model.attributable;|%model.phrase;|%model.global;)*' >

<!ENTITY % macro.phraseSeq.limited '(#PCDATA|%model.limitedPhrase;|%model.global;)*' >

<!ENTITY % macro.specialPara '(#PCDATA|_DUMMY_model.gLike|%model.phrase;|%model.inter;|%model.divPart;|%model.global;)*' >

<!-- end patterns -->

<!-- start elements -->

<!--doc:(TEI document) contains a single TEI-conformant document, combining a single TEI header with one or more members of the model.resource class. Multiple TEI elements may be combined within a TEI (or teiCorpus) element. [4. Default Text Structure 16.1. Varieties of Composite Text] -->
<!ELEMENT TEI ((teiHeader,(((%model.resource;)+,(TEI)*)|(TEI)+)))>
<!ATTLIST TEI xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST TEI
 %att.global.attributes;
 %att.typed.attributes;
version %teidata.version;  #IMPLIED >
<!ATTLIST TEI xsi:schemaLocation CDATA #IMPLIED
 xmlns:xsi CDATA #FIXED 'http://www.w3.org/2001/XMLSchema-instance'
 >
<!--doc:(anonymous block) contains any component-level unit of text, acting as a container for phrase or inter level elements analogous to, but without the same constraints as, a paragraph. [17.3. Blocks, Segments, and Anchors] -->
<!ELEMENT ab %macro.abContent;>
<!ATTLIST ab xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST ab
 %att.global.attributes;
 %att.typed.attributes;
 %att.declaring.attributes;
 %att.fragmentable.attributes;
 %att.written.attributes;
 %att.cmc.attributes; >
<!--doc:(abbreviation) contains an abbreviation of any sort. [3.6.5. Abbreviations and Their Expansions] -->
<!ELEMENT abbr %macro.phraseSeq;>
<!ATTLIST abbr xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST abbr
 %att.global.attributes;
 %att.typed.attribute.subtype;
 %att.cmc.attributes;
type %teidata.enumerated;  #IMPLIED >
<!--doc:contains a summary or formal abstract prefixed to an existing source document by the encoder. [2.4.4. Abstracts] -->
<!ELEMENT abstract (%model.pLike;|%model.listLike;|listBibl)+>
<!ATTLIST abstract xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST abstract
 %att.global.attributes; >
<!--doc:(activity) contains a brief informal description of what a participant in a language interaction is doing other than speaking, if anything. [16.2.3. The Setting Description] -->
<!ELEMENT activity %macro.phraseSeq.limited;>
<!ATTLIST activity xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST activity
 %att.global.attributes; >
<!--doc:(addition) contains letters, words, or phrases inserted in the source text by an author, scribe, or a previous annotator or corrector. [3.5.3. Additions, Deletions, and Omissions] -->
<!ELEMENT add %macro.paraContent;>
<!ATTLIST add xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST add
 %att.global.attributes;
 %att.transcriptional.attributes;
 %att.placement.attributes;
 %att.typed.attributes;
 %att.dimensions.attributes;
 %att.cmc.attributes; >
<!--doc:(additional name) contains an additional name component, such as a nickname, epithet, or alias, or any other descriptive phrase used within a personal name. [14.2.1. Personal Names] -->
<!ELEMENT addName %macro.phraseSeq;>
<!ATTLIST addName xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST addName
 %att.global.attributes;
 %att.personal.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(address line) contains one line of a postal address. [3.6.2. Addresses 2.2.4. Publication, Distribution, Licensing, etc. ********. Imprint, Size of a Document, and Reprint Information] -->
<!ELEMENT addrLine %macro.phraseSeq;>
<!ATTLIST addrLine xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST addrLine
 %att.global.attributes; >
<!--doc:(address) contains a postal address, for example of a publisher, an organization, or an individual. [3.6.2. Addresses 2.2.4. Publication, Distribution, Licensing, etc. ********. Imprint, Size of a Document, and Reprint Information] -->
<!ELEMENT address (((%model.global;)*,((%model.addrPart;),(%model.global;)*)+))>
<!ATTLIST address xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST address
 %att.global.attributes;
 %att.cmc.attributes; >
<!--doc:(affiliation) contains an informal description of a person's present or past affiliation with some organization, for example an employer or sponsor. [16.2.2. The Participant Description] -->
<!ELEMENT affiliation %macro.phraseSeq;>
<!ATTLIST affiliation xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST affiliation
 %att.global.attributes;
 %att.editLike.attributes;
 %att.datable.attributes;
 %att.naming.attributes;
 %att.typed.attribute.subtype;
 %att.cmc.attributes;
type %teidata.enumerated;  #IMPLIED
calendar CDATA  #IMPLIED >
<!--doc:(age) specifies the age of a person. [********. Personal Characteristics] -->
<!ELEMENT age %macro.phraseSeq.limited;>
<!ATTLIST age xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST age
 %att.global.attributes;
 %att.editLike.attributes;
 %att.datable.attributes;
 %att.typed.attribute.subtype;
 %att.dimensions.attributes;
type %teidata.enumerated;  #IMPLIED
calendar CDATA  #IMPLIED
value %teidata.count;  #IMPLIED >
<!--doc:(alternation) identifies an alternation or a set of choices among elements or passages. [17.8. Alternation] -->
<!ELEMENT alt  EMPTY>
<!ATTLIST alt xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST alt
 %att.global.attributes;
 %att.pointing.attribute.targetLang;
 %att.pointing.attribute.evaluate;
 %att.typed.attributes;
 %att.cmc.attributes;
target CDATA  #IMPLIED
mode (excl|incl) #IMPLIED
weights CDATA  #IMPLIED >
<!--doc:(alternation group) groups a collection of alt elements and possibly pointers. [17.8. Alternation] -->
<!ELEMENT altGrp (((%model.descLike;)*,(alt|ptr)*))>
<!ATTLIST altGrp xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST altGrp
 %att.global.attributes;
 %att.pointing.group.attributes;
 %att.cmc.attributes;
mode (excl|incl) "excl"  >
<!--doc:(analytic level) contains bibliographic elements describing an item (e.g. an article or poem) published within a monograph or journal and not as an independent publication. [********. Analytic, Monographic, and Series Levels] -->
<!ELEMENT analytic (author|editor|respStmt|title|%model.ptrLike;|date|textLang|idno|availability)*>
<!ATTLIST analytic xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST analytic
 %att.global.attributes; >
<!--doc:(anchor point) attaches an identifier to a point within a text, whether or not it corresponds with a textual element. [8.4.2. Synchronization and Overlap 17.5. Correspondence and Alignment] -->
<!ELEMENT anchor  EMPTY>
<!ATTLIST anchor xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST anchor
 %att.global.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:represents an annotation following the Web Annotation Data Model. [17.10. The standOff Container] -->
<!ELEMENT annotation (((respStmt)*,(revisionDesc)*,(licence)*,(%model.annotationPart.body;)*))>
<!ATTLIST annotation xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST annotation
 %att.global.attribute.n;
 %att.global.attribute.xmllang;
 %att.global.attribute.xmlbase;
 %att.global.attribute.xmlspace;
 %att.global.rendition.attribute.rend;
 %att.global.rendition.attribute.style;
 %att.global.rendition.attribute.rendition;
 %att.global.linking.attribute.corresp;
 %att.global.linking.attribute.synch;
 %att.global.linking.attribute.sameAs;
 %att.global.linking.attribute.copyOf;
 %att.global.linking.attribute.next;
 %att.global.linking.attribute.prev;
 %att.global.linking.attribute.exclude;
 %att.global.linking.attribute.select;
 %att.global.responsibility.attribute.cert;
 %att.global.responsibility.attribute.resp;
 %att.global.source.attribute.source;
 %att.pointing.attribute.targetLang;
 %att.pointing.attribute.evaluate;
xml:id ID #REQUIRED
target CDATA  #REQUIRED
motivation NMTOKENS  #IMPLIED >
<!--doc:(application information) records information about an application which has edited the TEI file. [2.3.11. The Application Information Element] -->
<!ELEMENT appInfo (%model.applicationLike;)+>
<!ATTLIST appInfo xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST appInfo
 %att.global.attributes; >
<!--doc:provides information about an application which has acted upon the document. [2.3.11. The Application Information Element] -->
<!ELEMENT application (((%model.labelLike;)+,((%model.ptrLike;)*|(%model.pLike;)*)))>
<!ATTLIST application xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST application
 %att.global.attributes;
 %att.typed.attributes;
 %att.datable.attributes;
calendar CDATA  #IMPLIED
ident %teidata.name;  #REQUIRED
version %teidata.versionNumber;  #REQUIRED >
<!--doc:(argument) contains a formal list or prose description of the topics addressed by a subdivision of a text. [4.2. Elements Common to All Divisions 4.6. Title Pages] -->
<!ELEMENT argument (((%model.global;|%model.headLike;)*,((%model.common;),(%model.global;)*)+))>
<!ATTLIST argument xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST argument
 %att.global.attributes;
 %att.cmc.attributes; >
<!--doc:(author) in a bibliographic reference, contains the name(s) of an author, personal or corporate, of a work; for example in the same form as that provided by a recognized bibliographic name authority. [********. Titles, Authors, and Editors 2.2.1. The Title Statement] -->
<!ELEMENT author %macro.phraseSeq;>
<!ATTLIST author xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST author
 %att.global.attributes;
 %att.naming.attributes;
 %att.datable.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(release authority) supplies the name of a person or other agency responsible for making a work available, other than a publisher or distributor. [2.2.4. Publication, Distribution, Licensing, etc.] -->
<!ELEMENT authority %macro.phraseSeq.limited;>
<!ATTLIST authority xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST authority
 %att.global.attributes;
 %att.canonical.attributes; >
<!--doc:(availability) supplies information about the availability of a text, for example any restrictions on its use or distribution, its copyright status, any licence applying to it, etc. [2.2.4. Publication, Distribution, Licensing, etc.] -->
<!ELEMENT availability (%model.availabilityPart;|%model.pLike;)+>
<!ATTLIST availability xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST availability
 %att.global.attributes;
 %att.declarable.attributes;
status (free|unknown|restricted) #IMPLIED >
<!--doc:(back matter) contains any appendixes, etc. following the main part of a text. [4.7. Back Matter 4. Default Text Structure] -->
<!ELEMENT back (((%model.frontPart;|%model.pLike.front;|%model.pLike;|%model.listLike;|%model.global;)*,(((%model.div1Like;),(%model.frontPart;|%model.div1Like;|%model.global;)*)|((%model.divLike;),(%model.frontPart;|%model.divLike;|%model.global;)*))?,((%model.divBottomPart;),(%model.divBottomPart;|%model.global;)*)?))>
<!ATTLIST back xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST back
 %att.global.attributes;
 %att.declaring.attributes; >
<!--doc:(bibliographic citation) contains a loosely-structured bibliographic citation of which the sub-components may or may not be explicitly tagged. [3.12.1. Methods of Encoding Bibliographic References and Lists of References 2.2.7. The Source Description 16.3.2. Declarable Elements] -->
<!ELEMENT bibl (#PCDATA|_DUMMY_model.gLike|%model.highlighted;|%model.pPart.data;|%model.pPart.edit;|%model.segLike;|%model.ptrLike;|%model.biblPart;|%model.global;)*>
<!ATTLIST bibl xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST bibl
 %att.global.attributes;
 %att.declarable.attributes;
 %att.typed.attributes;
 %att.sortable.attributes;
 %att.docStatus.attributes;
 %att.cmc.attributes; >
<!--doc:(fully-structured bibliographic citation) contains a fully-structured bibliographic citation, in which all components of the TEI file description are present. [3.12.1. Methods of Encoding Bibliographic References and Lists of References 2.2. The File Description 2.2.7. The Source Description 16.3.2. Declarable Elements] -->
<!ELEMENT biblFull (((titleStmt,(editionStmt)?,(extent)?,publicationStmt,(seriesStmt)*,(notesStmt)?),(sourceDesc)*)|(fileDesc,profileDesc))>
<!ATTLIST biblFull xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST biblFull
 %att.global.attributes;
 %att.declarable.attributes;
 %att.sortable.attributes;
 %att.docStatus.attributes;
 %att.cmc.attributes; >
<!--doc:(scope of bibliographic reference) defines the scope of a bibliographic reference, for example as a list of page numbers, or a named subdivision of a larger work. [********. Scopes and Ranges in Bibliographic Citations] -->
<!ELEMENT biblScope %macro.phraseSeq;>
<!ATTLIST biblScope xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST biblScope
 %att.global.attributes;
 %att.citing.attributes; >
<!--doc:(structured bibliographic citation) contains a structured bibliographic citation, in which only bibliographic sub-elements appear and in a specified order. [3.12.1. Methods of Encoding Bibliographic References and Lists of References 2.2.7. The Source Description 16.3.2. Declarable Elements] -->
<!ELEMENT biblStruct (((analytic)*,(monogr,(series)*)+,(%model.noteLike;|%model.ptrLike;|relatedItem|citedRange)*))>
<!ATTLIST biblStruct xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST biblStruct
 %att.global.attributes;
 %att.declarable.attributes;
 %att.typed.attributes;
 %att.sortable.attributes;
 %att.docStatus.attributes;
 %att.cmc.attributes; >
<!--doc:provides encoded binary data representing an inline graphic, audio, video or other object. [3.10. Graphics and Other Non-textual Components] -->
<!ELEMENT binaryObject (#PCDATA)>
<!ATTLIST binaryObject xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST binaryObject
 %att.global.attributes;
 %att.media.attributes;
 %att.timed.attributes;
 %att.typed.attributes;
 %att.cmc.attributes;
encoding CDATA  #IMPLIED >
<!--doc:(birth) contains information about a person's birth, such as its date and place. [16.2.2. The Participant Description] -->
<!ELEMENT birth %macro.phraseSeq;>
<!ATTLIST birth xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST birth
 %att.global.attributes;
 %att.editLike.attributes;
 %att.datable.attributes;
 %att.dimensions.attributes;
 %att.naming.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED
calendar CDATA  #IMPLIED >
<!--doc:(bloc) contains the name of a geo-political unit consisting of two or more nation states or countries. [14.2.3. Place Names] -->
<!ELEMENT bloc %macro.phraseSeq;>
<!ATTLIST bloc xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST bloc
 %att.global.attributes;
 %att.naming.attributes;
 %att.typed.attributes;
 %att.datable.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(text body) contains the whole body of a single unitary text, excluding any front or back matter. [4. Default Text Structure] -->
<!ELEMENT body (((%model.global;)*,((%model.divTop;),(%model.global;|%model.divTop;)*)?,((%model.divGenLike;),(%model.global;|%model.divGenLike;)*)?,(((%model.divLike;),(%model.global;|%model.divGenLike;)*)+|((%model.div1Like;),(%model.global;|%model.divGenLike;)*)+|(((schemaSpec|%model.common;),(%model.global;)*)+,(((%model.divLike;),(%model.global;|%model.divGenLike;)*)+|((%model.div1Like;),(%model.global;|%model.divGenLike;)*)+)?)),((%model.divBottom;),(%model.global;)*)*))>
<!ATTLIST body xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST body
 %att.global.attributes;
 %att.declaring.attributes; >
<!--doc:(byline) contains the primary statement of responsibility given for a work on its title page or at the head or end of the work. [4.2.2. Openers and Closers 4.5. Front Matter] -->
<!ELEMENT byline (#PCDATA|_DUMMY_model.gLike|%model.phrase;|docAuthor|%model.global;)*>
<!ATTLIST byline xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST byline
 %att.global.attributes;
 %att.cmc.attributes; >
<!--doc:(canonical reference pattern) specifies an expression and replacement pattern for transforming a canonical reference into a URI. [*******. Milestone Method 2.3.6. The Reference System Declaration *******. Search-and-Replace Method] -->
<!ELEMENT cRefPattern (%model.pLike;)*>
<!ATTLIST cRefPattern xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST cRefPattern
 %att.global.attributes;
 %att.patternReplacement.attributes; >
<!--doc:(calendar) describes a calendar or dating system used in a dating formula in the text. [2.4.5. Calendar Description] -->
<!ELEMENT calendar (%model.pLike;)+>
<!ATTLIST calendar xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST calendar
 %att.global.attributes;
 %att.pointing.attributes;
 %att.typed.attributes; >
<!--doc:(calendar description) contains a description of the calendar system used in any dating expression found in the text. [2.4. The Profile Description 2.4.5. Calendar Description] -->
<!ELEMENT calendarDesc (calendar)+>
<!ATTLIST calendarDesc xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST calendarDesc
 %att.global.attributes; >
<!--doc:(category description) describes some category within a taxonomy or text typology, either in the form of a brief prose description or in terms of the situational parameters used by the TEI formal textDesc. [2.3.7. The Classification Declaration] -->
<!ELEMENT catDesc (#PCDATA|%model.limitedPhrase;|%model.catDescPart;)*>
<!ATTLIST catDesc xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST catDesc
 %att.global.attributes;
 %att.canonical.attributes; >
<!--doc:(category reference) specifies one or more defined categories within some taxonomy or text typology. [2.4.3. The Text Classification] -->
<!ELEMENT catRef  EMPTY>
<!ATTLIST catRef xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST catRef
 %att.global.attributes;
 %att.pointing.attributes;
scheme %teidata.pointer;  #IMPLIED >
<!--doc:(category) contains an individual descriptive category, possibly nested within a superordinate category, within a user-defined taxonomy. [2.3.7. The Classification Declaration] -->
<!ELEMENT category ((((catDesc)+|(%model.descLike;|equiv|gloss)*),(category)*))>
<!ATTLIST category xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST category
 %att.global.attributes;
 %att.datcat.attributes; >
<!--doc:(column beginning) marks the beginning of a new column of a text on a multi-column page. [3.11.3. Milestone
Elements] -->
<!ELEMENT cb  EMPTY>
<!ATTLIST cb xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST cb
 %att.global.attributes;
 %att.typed.attributes;
 %att.edition.attributes;
 %att.spanning.attributes;
 %att.breaking.attributes;
 %att.cmc.attributes; >
<!--doc:(change) documents a change or set of changes made during the production of a source document, or during the revision of an electronic file. [2.6. The Revision Description 2.4.1. Creation 12.7. Identifying Changes and Revisions] -->
<!ELEMENT change %macro.specialPara;>
<!ATTLIST change xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST change
 %att.ascribed.attributes;
 %att.datable.attributes;
 %att.docStatus.attributes;
 %att.global.attributes;
 %att.typed.attributes;
calendar CDATA  #IMPLIED
target CDATA  #IMPLIED >
<!--doc:(primary channel) describes the medium or channel by which a text is delivered or experienced. For a written text, this might be print, manuscript, email, etc.; for a spoken one, radio, telephone, face-to-face, etc. [16.2.1. The Text Description] -->
<!ELEMENT channel %macro.phraseSeq.limited;>
<!ATTLIST channel xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST channel
 %att.global.attributes;
mode (s|w|sw|ws|m|x) "x"  >
<!--doc:(choice) groups a number of alternative encodings for the same point in a text. [3.5. Simple Editorial Changes] -->
<!ELEMENT choice (%model.choicePart;|choice)>
<!ATTLIST choice xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST choice
 %att.global.attributes;
 %att.cmc.attributes; >
<!--doc:(cited quotation) contains a quotation from some other document, together with a bibliographic reference to its source. In a dictionary it may contain an example text with at least one occurrence of the word form, used in the sense being described, or a translation of the headword, or an example. [3.3.3. Quotation 4.3.1. Grouped Texts ********. Examples] -->
<!ELEMENT cit (%model.biblLike;|_DUMMY_model.egLike|_DUMMY_model.entryPart|%model.global;|%model.graphicLike;|%model.ptrLike;|%model.attributable;|pc|q)+>
<!ATTLIST cit xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST cit
 %att.global.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(citation data) specifies how information may be extracted from citation structures. [3.11.4. Declaring Reference Systems 17.2.5.4. Citation Structures] -->
<!ELEMENT citeData  EMPTY>
<!ATTLIST citeData xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST citeData
 %att.global.attributes;
 %att.citeStructurePart.attributes;
property %teidata.pointer;  #REQUIRED >
<!--doc:(citation structure) declares a structure and method for citing the current document. [3.11.4. Declaring Reference Systems 17.2.5.4. Citation Structures] -->
<!ELEMENT citeStructure (((citeData)*,(citeStructure)*,(%model.descLike;)*))>
<!ATTLIST citeStructure xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST citeStructure
 %att.global.attributes;
 %att.citeStructurePart.attributes;
delim CDATA #IMPLIED
match %teidata.xpath;  #REQUIRED
unit %teidata.enumerated;  #IMPLIED >
<!--doc:(cited range) defines the range of cited content, often represented by pages or other units [********. Scopes and Ranges in Bibliographic Citations] -->
<!ELEMENT citedRange %macro.phraseSeq;>
<!ATTLIST citedRange xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST citedRange
 %att.global.attributes;
 %att.pointing.attributes;
 %att.citing.attributes; >
<!--doc:(classification code) contains the classification code used for this text in some standard classification system. [2.4.3. The Text Classification] -->
<!ELEMENT classCode %macro.phraseSeq.limited;>
<!ATTLIST classCode xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST classCode
 %att.global.attributes;
scheme %teidata.pointer;  #REQUIRED >
<!--doc:(classification declarations) contains one or more taxonomies defining any classificatory codes used elsewhere in the text. [2.3.7. The Classification Declaration 2.3. The Encoding Description] -->
<!ELEMENT classDecl (taxonomy)+>
<!ATTLIST classDecl xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST classDecl
 %att.global.attributes; >
<!--doc:(climate) contains information about the physical climate of a place. [********. States, Traits, and Events] -->
<!ELEMENT climate (((precision)*,(%model.headLike;)*,((%model.pLike;)+|(%model.labelLike;)+),(%model.noteLike;|%model.biblLike;)*,(climate)*))>
<!ATTLIST climate xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST climate
 %att.global.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.naming.attributes;
 %att.typed.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(closer) groups together salutations, datelines, and similar phrases appearing as a final group at the end of a division, especially of a letter. [4.2.2. Openers and Closers 4.2. Elements Common to All Divisions] -->
<!ELEMENT closer (#PCDATA|_DUMMY_model.gLike|signed|dateline|salute|%model.phrase;|%model.global;)*>
<!ATTLIST closer xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST closer
 %att.global.attributes;
 %att.written.attributes;
 %att.cmc.attributes; >
<!--doc:(constitution) describes the internal composition of a text or text sample, for example as fragmentary, complete, etc. [16.2.1. The Text Description] -->
<!ELEMENT constitution %macro.phraseSeq.limited;>
<!ATTLIST constitution xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST constitution
 %att.global.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  "single"  >
<!--doc:defines how to calculate one unit of measure in terms of another. [2.3.9. The Unit Declaration] -->
<!ELEMENT conversion  EMPTY>
<!ATTLIST conversion xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST conversion
 %att.global.attributes;
 %att.datable.attributes;
 %att.formula.attributes;
 %att.locatable.attributes;
calendar CDATA  #IMPLIED
fromUnit %teidata.pointer;  #REQUIRED
toUnit %teidata.pointer;  #REQUIRED >
<!--doc:(correction) contains the correct form of a passage apparently erroneous in the copy text. [3.5.1. Apparent Errors] -->
<!ELEMENT corr %macro.paraContent;>
<!ATTLIST corr xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST corr
 %att.global.attributes;
 %att.editLike.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(correction principles) states how and under what circumstances corrections have been made in the text. [2.3.3. The Editorial Practices Declaration 16.3.2. Declarable Elements] -->
<!ELEMENT correction (%model.pLike;)+>
<!ATTLIST correction xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST correction
 %att.global.attributes;
 %att.declarable.attributes;
status (high|medium|low|unknown) #IMPLIED
method (silent|markup) "silent"  >
<!--doc:(correspondence action) contains a structured description of the place, the name of a person/organization and the date related to the sending/receiving of a message or any other action related to the correspondence. [2.4.6. Correspondence Description] -->
<!ELEMENT correspAction ((%model.correspActionPart;)+|(%model.pLike;)+)>
<!ATTLIST correspAction xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST correspAction
 %att.global.attributes;
 %att.typed.attribute.subtype;
 %att.sortable.attributes;
type %teidata.enumerated;  #IMPLIED >
<!--doc:(correspondence context) provides references to preceding or following correspondence related to this piece of correspondence. [2.4.6. Correspondence Description] -->
<!ELEMENT correspContext (%model.correspContextPart;)+>
<!ATTLIST correspContext xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST correspContext
 %att.global.attributes; >
<!--doc:(correspondence description) contains a description of the actions related to one act of correspondence. [2.4.6. Correspondence Description] -->
<!ELEMENT correspDesc ((%model.correspDescPart;)+|(%model.pLike;)+)>
<!ATTLIST correspDesc xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST correspDesc
 %att.declarable.attributes;
 %att.canonical.attributes;
 %att.global.attributes;
 %att.typed.attributes; >
<!--doc:(country) contains the name of a geo-political unit, such as a nation, country, colony, or commonwealth, larger than or administratively superior to a region and smaller than a bloc. [14.2.3. Place Names] -->
<!ELEMENT country %macro.phraseSeq;>
<!ATTLIST country xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST country
 %att.global.attributes;
 %att.naming.attributes;
 %att.typed.attributes;
 %att.datable.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(creation) contains information about the creation of a text. [2.4.1. Creation 2.4. The Profile Description] -->
<!ELEMENT creation (#PCDATA|%model.limitedPhrase;|listChange)*>
<!ATTLIST creation xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST creation
 %att.global.attributes;
 %att.datable.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(date) contains a date in any format. [3.6.4. Dates and Times 2.2.4. Publication, Distribution, Licensing, etc. 2.6. The Revision Description ********. Imprint, Size of a Document, and Reprint Information 16.2.3. The Setting Description 14.4. Dates] -->
<!ELEMENT date (#PCDATA|_DUMMY_model.gLike|%model.phrase;|%model.global;)*>
<!ATTLIST date xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST date
 %att.global.attributes;
 %att.canonical.attributes;
 %att.datable.attributes;
 %att.calendarSystem.attributes;
 %att.editLike.attributes;
 %att.dimensions.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(dateline) contains a brief description of the place, date, time, etc. of production of a letter, newspaper story, or other work, prefixed or suffixed to it as a kind of heading or trailer. [4.2.2. Openers and Closers] -->
<!ELEMENT dateline (#PCDATA|_DUMMY_model.gLike|%model.phrase;|%model.global;|docDate)*>
<!ATTLIST dateline xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST dateline
 %att.global.attributes;
 %att.cmc.attributes; >
<!--doc:(death) contains information about a person's death, such as its date and place. [16.2.2. The Participant Description] -->
<!ELEMENT death %macro.phraseSeq;>
<!ATTLIST death xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST death
 %att.datable.attributes;
 %att.dimensions.attributes;
 %att.editLike.attributes;
 %att.global.attributes;
 %att.naming.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED
calendar CDATA  #IMPLIED >
<!--doc:(deletion) contains a letter, word, or passage deleted, marked as deleted, or otherwise indicated as superfluous or spurious in the copy text by an author, scribe, or a previous annotator or corrector. [3.5.3. Additions, Deletions, and Omissions] -->
<!ELEMENT del %macro.paraContent;>
<!ATTLIST del xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST del
 %att.global.attributes;
 %att.transcriptional.attributes;
 %att.typed.attributes;
 %att.dimensions.attributes;
 %att.cmc.attributes; >
<!--doc:(derivation) describes the nature and extent of originality of this text. [16.2.1. The Text Description] -->
<!ELEMENT derivation %macro.phraseSeq.limited;>
<!ATTLIST derivation xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST derivation
 %att.global.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED >
<!--doc:(description) contains a short description of the purpose, function, or use of its parent element, or when the parent is a documentation element, describes or defines the object being documented.  [23.4.1. Description of Components] -->
<!ELEMENT desc %macro.limitedContent;>
<!ATTLIST desc xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST desc
 %att.global.attributes;
 %att.typed.attribute.subtype;
 %att.cmc.attributes;
type %teidata.enumerated;  #IMPLIED >
<!--doc:identifies any word or phrase which is regarded as linguistically distinct, for example as archaic, technical, dialectal, non-preferred, etc., or as forming part of a sublanguage. [*******. Other Linguistically Distinct Material] -->
<!ELEMENT distinct %macro.phraseSeq;>
<!ATTLIST distinct xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST distinct
 %att.global.attributes;
 %att.typed.attribute.subtype;
 %att.cmc.attributes;
type %teidata.enumerated;  #IMPLIED
time %teidata.text;  #IMPLIED
space %teidata.text;  #IMPLIED
social %teidata.text;  #IMPLIED >
<!--doc:(distributor) supplies the name of a person or other agency responsible for the distribution of a text. [2.2.4. Publication, Distribution, Licensing, etc.] -->
<!ELEMENT distributor %macro.phraseSeq;>
<!ATTLIST distributor xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST distributor
 %att.global.attributes;
 %att.canonical.attributes; >
<!--doc:(district) contains the name of any kind of subdivision of a settlement, such as a parish, ward, or other administrative or geographic unit. [14.2.3. Place Names] -->
<!ELEMENT district %macro.phraseSeq;>
<!ATTLIST district xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST district
 %att.global.attributes;
 %att.naming.attributes;
 %att.typed.attributes;
 %att.datable.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(text division) contains a subdivision of the front, body, or back of a text. [4.1. Divisions of the Body] -->
<!ELEMENT div (((%model.divTop;|%model.global;)*,((((%model.divLike;|%model.divGenLike;),(%model.global;)*)+|(((schemaSpec|%model.common;),(%model.global;)*)+,((%model.divLike;|%model.divGenLike;),(%model.global;)*)*)),((%model.divBottom;),(%model.global;)*)*)?))>
<!ATTLIST div xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST div
 %att.global.attributes;
 %att.divLike.attributes;
 %att.typed.attributes;
 %att.declaring.attributes;
 %att.written.attributes; >
<!--doc:(level-1 text division) contains a first-level subdivision of the front, body, or back of a text. [4.1.2. Numbered Divisions] -->
<!ELEMENT div1 (((%model.divTop;|%model.global;)*,((((%model.div2Like;|%model.divGenLike;),(%model.global;)*)+|(((schemaSpec|%model.common;),(%model.global;)*)+,((%model.div2Like;|%model.divGenLike;),(%model.global;)*)*)),((%model.divBottom;),(%model.global;)*)*)?))>
<!ATTLIST div1 xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST div1
 %att.global.attributes;
 %att.divLike.attributes;
 %att.typed.attributes;
 %att.declaring.attributes; >
<!--doc:(level-2 text division) contains a second-level subdivision of the front, body, or back of a text. [4.1.2. Numbered Divisions] -->
<!ELEMENT div2 (((%model.divTop;|%model.global;)*,((((%model.div3Like;|%model.divGenLike;),(%model.global;)*)+|(((schemaSpec|%model.common;),(%model.global;)*)+,((%model.div3Like;|%model.divGenLike;),(%model.global;)*)*)),((%model.divBottom;),(%model.global;)*)*)?))>
<!ATTLIST div2 xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST div2
 %att.global.attributes;
 %att.divLike.attributes;
 %att.typed.attributes;
 %att.declaring.attributes; >
<!--doc:(level-3 text division) contains a third-level subdivision of the front, body, or back of a text. [4.1.2. Numbered Divisions] -->
<!ELEMENT div3 (((%model.divTop;|%model.global;)*,((((%model.div4Like;|%model.divGenLike;),(%model.global;)*)+|(((schemaSpec|%model.common;),(%model.global;)*)+,((%model.div4Like;|%model.divGenLike;),(%model.global;)*)*)),((%model.divBottom;),(%model.global;)*)*)?))>
<!ATTLIST div3 xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST div3
 %att.global.attributes;
 %att.divLike.attributes;
 %att.typed.attributes;
 %att.declaring.attributes; >
<!--doc:(level-4 text division) contains a fourth-level subdivision of the front, body, or back of a text. [4.1.2. Numbered Divisions] -->
<!ELEMENT div4 (((%model.divTop;|%model.global;)*,((((%model.div5Like;|%model.divGenLike;),(%model.global;)*)+|(((schemaSpec|%model.common;),(%model.global;)*)+,((%model.div5Like;|%model.divGenLike;),(%model.global;)*)*)),((%model.divBottom;),(%model.global;)*)*)?))>
<!ATTLIST div4 xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST div4
 %att.global.attributes;
 %att.divLike.attributes;
 %att.typed.attributes;
 %att.declaring.attributes; >
<!--doc:(level-5 text division) contains a fifth-level subdivision of the front, body, or back of a text. [4.1.2. Numbered Divisions] -->
<!ELEMENT div5 (((%model.divTop;|%model.global;)*,((((%model.div6Like;|%model.divGenLike;),(%model.global;)*)+|(((schemaSpec|%model.common;),(%model.global;)*)+,((%model.div6Like;|%model.divGenLike;),(%model.global;)*)*)),((%model.divBottom;),(%model.global;)*)*)?))>
<!ATTLIST div5 xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST div5
 %att.global.attributes;
 %att.divLike.attributes;
 %att.typed.attributes;
 %att.declaring.attributes; >
<!--doc:(level-6 text division) contains a sixth-level subdivision of the front, body, or back of a text. [4.1.2. Numbered Divisions] -->
<!ELEMENT div6 (((%model.divTop;|%model.global;)*,((((%model.div7Like;|%model.divGenLike;),(%model.global;)*)+|(((schemaSpec|%model.common;),(%model.global;)*)+,((%model.div7Like;|%model.divGenLike;),(%model.global;)*)*)),((%model.divBottom;),(%model.global;)*)*)?))>
<!ATTLIST div6 xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST div6
 %att.global.attributes;
 %att.divLike.attributes;
 %att.typed.attributes;
 %att.declaring.attributes; >
<!--doc:(level-7 text division) contains the smallest possible subdivision of the front, body or back of a text, larger than a paragraph. [4.1.2. Numbered Divisions] -->
<!ELEMENT div7 (((%model.divTop;|%model.global;)*,(((schemaSpec|%model.common;),(%model.global;)*)+,((%model.divBottom;),(%model.global;)*)*)?))>
<!ATTLIST div7 xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST div7
 %att.global.attributes;
 %att.divLike.attributes;
 %att.typed.attributes;
 %att.declaring.attributes; >
<!--doc:(automatically generated text division) indicates the location at which a textual division generated automatically by a text-processing application is to appear. [3.9.2. Index Entries] -->
<!ELEMENT divGen (%model.headLike;)*>
<!ATTLIST divGen xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST divGen
 %att.global.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED >
<!--doc:(document author) contains the name of the author of the document, as given on the title page (often but not always contained in a byline). [4.6. Title Pages] -->
<!ELEMENT docAuthor %macro.phraseSeq;>
<!ATTLIST docAuthor xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST docAuthor
 %att.global.attributes;
 %att.canonical.attributes;
 %att.cmc.attributes; >
<!--doc:(document date) contains the date of a document, as given on a title page or in a dateline. [4.6. Title Pages] -->
<!ELEMENT docDate %macro.phraseSeq;>
<!ATTLIST docDate xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST docDate
 %att.global.attributes;
 %att.cmc.attributes;
 %att.datable.attributes;
 %att.calendarSystem.attributes; >
<!--doc:(document edition) contains an edition statement as presented on a title page of a document. [4.6. Title Pages] -->
<!ELEMENT docEdition %macro.paraContent;>
<!ATTLIST docEdition xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST docEdition
 %att.global.attributes; >
<!--doc:(document imprint) contains the imprint statement (place and date of publication, publisher name), as given (usually) at the foot of a title page. [4.6. Title Pages] -->
<!ELEMENT docImprint (#PCDATA|_DUMMY_model.gLike|%model.phrase;|pubPlace|docDate|publisher|%model.global;)*>
<!ATTLIST docImprint xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST docImprint
 %att.global.attributes; >
<!--doc:(document title) contains the title of a document, including all its constituents, as given on a title page. [4.6. Title Pages] -->
<!ELEMENT docTitle (((%model.global;)*,(titlePart,(%model.global;)*)+))>
<!ATTLIST docTitle xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST docTitle
 %att.global.attributes;
 %att.canonical.attributes; >
<!--doc:(domain of use) describes the most important social context in which the text was realized or for which it is intended, for example private vs. public, education, religion, etc. [16.2.1. The Text Description] -->
<!ELEMENT domain %macro.phraseSeq.limited;>
<!ATTLIST domain xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST domain
 %att.global.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED >
<!--doc:(edition) describes the particularities of one edition of a text. [2.2.2. The Edition Statement] -->
<!ELEMENT edition %macro.phraseSeq;>
<!ATTLIST edition xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST edition
 %att.global.attributes; >
<!--doc:(edition statement) groups information relating to one edition of a text. [2.2.2. The Edition Statement 2.2. The File Description] -->
<!ELEMENT editionStmt ((%model.pLike;)+|(edition,(%model.respLike;)*))>
<!ATTLIST editionStmt xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST editionStmt
 %att.global.attributes; >
<!--doc:contains a secondary statement of responsibility for a bibliographic item, for example the name of an individual, institution or organization, (or of several such) acting as editor, compiler, translator, etc. [********. Titles, Authors, and Editors] -->
<!ELEMENT editor %macro.phraseSeq;>
<!ATTLIST editor xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST editor
 %att.global.attributes;
 %att.naming.attributes;
 %att.datable.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(editorial practice declaration) provides details of editorial principles and practices applied during the encoding of a text. [2.3.3. The Editorial Practices Declaration 2.3. The Encoding Description 16.3.2. Declarable Elements] -->
<!ELEMENT editorialDecl (%model.pLike;|%model.editorialDeclPart;)+>
<!ATTLIST editorialDecl xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST editorialDecl
 %att.global.attributes;
 %att.declarable.attributes; >
<!--doc:(education) contains a description of the educational experience of a person. [16.2.2. The Participant Description] -->
<!ELEMENT education %macro.phraseSeq;>
<!ATTLIST education xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST education
 %att.global.attributes;
 %att.editLike.attributes;
 %att.datable.attributes;
 %att.naming.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED
calendar CDATA  #IMPLIED >
<!--doc:(deliberately marked omission) indicates a purposeful marking in the source document signalling that content has been omitted, and may also supply or describe the omitted content. [3.5.3. Additions, Deletions, and Omissions] -->
<!ELEMENT ellipsis ((metamark,((%model.descLike;)?),(supplied)?))>
<!ATTLIST ellipsis xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST ellipsis
 %att.global.attributes;
 %att.dimensions.attributes;
 %att.timed.attributes; >
<!--doc:(electronic mail address) contains an email address identifying a location to which email messages can be delivered. [3.6.2. Addresses] -->
<!ELEMENT email %macro.phraseSeq;>
<!ATTLIST email xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST email
 %att.global.attributes;
 %att.cmc.attributes; >
<!--doc:(emphasized) marks words or phrases which are stressed or emphasized for linguistic or rhetorical effect. [*******. Emphatic Words and Phrases 3.3.2. Emphasis, Foreign Words, and Unusual Language] -->
<!ELEMENT emph %macro.paraContent;>
<!ATTLIST emph xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST emph
 %att.global.attributes;
 %att.cmc.attributes; >
<!--doc:(encoding description) documents the relationship between an electronic text and the source or sources from which it was derived. [2.3. The Encoding Description 2.1.1. The TEI Header and Its Components] -->
<!ELEMENT encodingDesc (%model.encodingDescPart;|%model.pLike;)+>
<!ATTLIST encodingDesc xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST encodingDesc
 %att.global.attributes; >
<!--doc:(epigraph) contains a quotation, anonymous or attributed, appearing at the start or end of a section or on a title page. [4.2.3. Arguments, Epigraphs, and Postscripts 4.2. Elements Common to All Divisions 4.6. Title Pages] -->
<!ELEMENT epigraph (%model.common;|%model.global;)*>
<!ATTLIST epigraph xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST epigraph
 %att.global.attributes;
 %att.cmc.attributes; >
<!--doc:(event) contains data relating to anything of significance that happens in time. [14.3.1. Basic Principles] -->
<!ELEMENT event (((idno)*,(%model.headLike;)*,((%model.pLike;)+|(%model.labelLike;)+|(eventName)+),(%model.noteLike;|%model.biblLike;|linkGrp|link|idno|ptr)*,(%model.eventLike;)*,(%model.personLike;|listPerson)*,(%model.placeLike;|listPlace)*,(%model.objectLike;)*,(relation|listRelation)*))>
<!ATTLIST event xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST event
 %att.global.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.locatable.attributes;
 %att.naming.attributes;
 %att.sortable.attributes;
 %att.typed.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(name of an event) contains a proper noun or noun phrase used to refer to an event. [14.2.4. Event Names] -->
<!ELEMENT eventName %macro.phraseSeq;>
<!ATTLIST eventName xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST eventName
 %att.global.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.personal.attributes;
 %att.typed.attributes; >
<!--doc:(expansion) contains the expansion of an abbreviation. [3.6.5. Abbreviations and Their Expansions] -->
<!ELEMENT expan %macro.phraseSeq;>
<!ATTLIST expan xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST expan
 %att.global.attributes;
 %att.editLike.attributes;
 %att.cmc.attributes; >
<!--doc:(extent) describes the approximate size of a text stored on some carrier medium or of some other object, digital or non-digital, specified in any convenient units. [2.2.3. Type and Extent of File 2.2. The File Description ********. Imprint, Size of a Document, and Reprint Information 11.7.1. Object Description] -->
<!ELEMENT extent %macro.phraseSeq;>
<!ATTLIST extent xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST extent
 %att.global.attributes; >
<!--doc:(factuality) describes the extent to which the text may be regarded as imaginative or non-imaginative, that is, as describing a fictional or a non-fictional world. [16.2.1. The Text Description] -->
<!ELEMENT factuality %macro.phraseSeq.limited;>
<!ATTLIST factuality xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST factuality
 %att.global.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED >
<!--doc:(faith) specifies the faith, religion, or belief set of a person. [********. Personal Characteristics] -->
<!ELEMENT faith %macro.phraseSeq;>
<!ATTLIST faith xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST faith
 %att.global.attributes;
 %att.editLike.attributes;
 %att.datable.attributes;
 %att.canonical.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED
calendar CDATA  #IMPLIED >
<!--doc:(file description) contains a full bibliographic description of an electronic file. [2.2. The File Description 2.1.1. The TEI Header and Its Components] -->
<!ELEMENT fileDesc (((titleStmt,(editionStmt)?,(extent)?,publicationStmt,(seriesStmt)*,(notesStmt)?),(sourceDesc)+))>
<!ATTLIST fileDesc xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST fileDesc
 %att.global.attributes; >
<!--doc:(floating text) contains a single text of any kind, whether unitary or composite, which interrupts the text containing it at any point and after which the surrounding text resumes. [4.3.2. Floating Texts] -->
<!ELEMENT floatingText (((%model.global;)*,(front,(%model.global;)*)?,(body|group),(%model.global;)*,(back,(%model.global;)*)?))>
<!ATTLIST floatingText xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST floatingText
 %att.global.attributes;
 %att.declaring.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(floruit) contains information about a person's period of activity. [********. Personal Characteristics] -->
<!ELEMENT floruit %macro.phraseSeq;>
<!ATTLIST floruit xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST floruit
 %att.global.attributes;
 %att.datable.attributes;
 %att.dimensions.attributes;
 %att.editLike.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(foreign) identifies a word or phrase as belonging to some language other than that of the surrounding text. [3.3.2.1. Foreign Words or Expressions] -->
<!ELEMENT foreign %macro.phraseSeq;>
<!ATTLIST foreign xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST foreign
 %att.global.attributes;
 %att.cmc.attributes; >
<!--doc:(forename) contains a forename, given or baptismal name. [14.2.1. Personal Names] -->
<!ELEMENT forename %macro.phraseSeq;>
<!ATTLIST forename xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST forename
 %att.global.attributes;
 %att.personal.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(front matter) contains any prefatory matter (headers, abstracts, title page, prefaces, dedications, etc.) found at the start of a document, before the main body. [4.6. Title Pages 4. Default Text Structure] -->
<!ELEMENT front (((%model.frontPart;|%model.pLike;|%model.pLike.front;|%model.global;)*,((((%model.div1Like;),(%model.div1Like;|%model.frontPart;|%model.global;)*)|((%model.divLike;),(%model.divLike;|%model.frontPart;|%model.global;)*)),((%model.divBottom;),(%model.divBottom;|%model.global;)*)?)?))>
<!ATTLIST front xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST front
 %att.global.attributes;
 %att.declaring.attributes; >
<!--doc:(funding body) specifies the name of an individual, institution, or organization responsible for the funding of a project or text. [2.2.1. The Title Statement] -->
<!ELEMENT funder %macro.phraseSeq.limited;>
<!ATTLIST funder xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST funder
 %att.global.attributes;
 %att.canonical.attributes;
 %att.datable.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(gap) indicates a point where material has been omitted in a transcription, whether for editorial reasons described in the TEI header, as part of sampling practice, or because the material is illegible, invisible, or inaudible. [3.5.3. Additions, Deletions, and Omissions] -->
<!ELEMENT gap (%model.descLike;|_DUMMY_model.certLike)*>
<!ATTLIST gap xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST gap
 %att.global.attributes;
 %att.timed.attributes;
 %att.editLike.attributes;
 %att.dimensions.attributes;
 %att.cmc.attributes;
reason NMTOKENS  #IMPLIED
agent %teidata.enumerated;  #IMPLIED >
<!--doc:(gathering beginning) marks the beginning of a new gathering or quire in a transcribed codex. [3.11.3. Milestone
Elements] -->
<!ELEMENT gb  EMPTY>
<!ATTLIST gb xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST gb
 %att.global.attributes;
 %att.typed.attributes;
 %att.spanning.attributes;
 %att.breaking.attributes;
 %att.edition.attributes;
 %att.cmc.attributes; >
<!--doc:(generational name component) contains a name component used to distinguish otherwise similar names on the basis of the relative ages or generations of the persons named. [14.2.1. Personal Names] -->
<!ELEMENT genName %macro.phraseSeq;>
<!ATTLIST genName xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST genName
 %att.global.attributes;
 %att.personal.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(gender) specifies the gender identity of a person, persona, or character. [********. Personal Characteristics] -->
<!ELEMENT gender %macro.phraseSeq;>
<!ATTLIST gender xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST gender
 %att.global.attributes;
 %att.editLike.attributes;
 %att.datable.attributes;
 %att.typed.attributes;
calendar CDATA  #IMPLIED
value CDATA  #IMPLIED >
<!--doc:(geographical coordinates) contains any expression of a set of geographic coordinates, representing a point, line, or area on the surface of the earth in some notation. [14.3.4.1. Varieties of Location] -->
<!ELEMENT geo (#PCDATA)>
<!ATTLIST geo xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST geo
 %att.global.attributes;
 %att.declaring.attributes;
 %att.cmc.attributes; >
<!--doc:(geographic coordinates declaration) documents the notation and the datum used for geographic coordinates expressed as content of the geo element elsewhere within the document. [2.3.8. The Geographic Coordinates Declaration] -->
<!ELEMENT geoDecl %macro.phraseSeq;>
<!ATTLIST geoDecl xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST geoDecl
 %att.global.attributes;
 %att.declarable.attributes;
datum %teidata.enumerated;  "WGS84"  >
<!--doc:(geographical feature name) contains a common noun identifying some geographical feature contained within a geographic name, such as valley, mount, etc. [14.2.3. Place Names] -->
<!ELEMENT geogFeat %macro.phraseSeq;>
<!ATTLIST geogFeat xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST geogFeat
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.global.attributes;
 %att.naming.attributes;
 %att.typed.attributes;
 %att.dimensions.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(geographical name) identifies a name associated with some geographical feature such as Windrush Valley or Mount Sinai. [14.2.3. Place Names] -->
<!ELEMENT geogName %macro.phraseSeq;>
<!ATTLIST geogName xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST geogName
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.global.attributes;
 %att.naming.attributes;
 %att.typed.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(gloss) identifies a phrase or word used to provide a gloss or definition for some other word or phrase. [3.4.1. Terms and Glosses 23.4.1. Description of Components] -->
<!ELEMENT gloss %macro.phraseSeq;>
<!ATTLIST gloss xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST gloss
 %att.global.attributes;
 %att.declaring.attributes;
 %att.typed.attributes;
 %att.pointing.attributes;
 %att.cReferencing.attributes;
 %att.cmc.attributes; >
<!--doc:(graphic) indicates the location of a graphic or illustration, either forming part of a text, or providing an image of it. [3.10. Graphics and Other Non-textual Components 12.1. Digital Facsimiles] -->
<!ELEMENT graphic (%model.descLike;)*>
<!ATTLIST graphic xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST graphic
 %att.global.attributes;
 %att.media.attributes;
 %att.resourced.attributes;
 %att.declaring.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(group) contains the body of a composite text, grouping together a sequence of distinct texts (or groups of such texts) which are regarded as a unit for some purpose, for example the collected works of an author, a sequence of prose essays, etc. [4. Default Text Structure 4.3.1. Grouped Texts 16.1. Varieties of Composite Text] -->
<!ELEMENT group (((%model.divTop;|%model.global;)*,((text|group),(text|group|%model.global;)*),(%model.divBottom;)*))>
<!ATTLIST group xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST group
 %att.global.attributes;
 %att.declaring.attributes;
 %att.typed.attributes; >
<!--doc:(note on hand) describes a particular style or hand distinguished within a manuscript. [11.7.2. Writing, Decoration, and Other Notations] -->
<!ELEMENT handNote %macro.specialPara;>
<!ATTLIST handNote xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST handNote
 %att.global.attributes;
 %att.handFeatures.attributes; >
<!--doc:(heading) contains any type of heading, for example the title of a section, or the heading of a list, glossary, manuscript description, etc. [4.2.1. Headings and Trailers] -->
<!ELEMENT head (#PCDATA|lg|_DUMMY_model.gLike|%model.phrase;|%model.inter;|%model.lLike;|%model.global;)*>
<!ATTLIST head xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST head
 %att.global.attributes;
 %att.typed.attributes;
 %att.placement.attributes;
 %att.written.attributes;
 %att.cmc.attributes; >
<!--doc:(heading for list items) contains the heading for the item or gloss column in a glossary list or similar structured list. [3.8. Lists] -->
<!ELEMENT headItem %macro.phraseSeq;>
<!ATTLIST headItem xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST headItem
 %att.global.attributes; >
<!--doc:(heading for list labels) contains the heading for the label or term column in a glossary list or similar structured list. [3.8. Lists] -->
<!ELEMENT headLabel %macro.phraseSeq;>
<!ATTLIST headLabel xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST headLabel
 %att.global.attributes; >
<!--doc:(highlighted) marks a word or phrase as graphically distinct from the surrounding text, for reasons concerning which no claim is made. [*******. Emphatic Words and Phrases 3.3.2. Emphasis, Foreign Words, and Unusual Language] -->
<!ELEMENT hi %macro.paraContent;>
<!ATTLIST hi xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST hi
 %att.global.attributes;
 %att.written.attributes;
 %att.cmc.attributes; >
<!--doc:(hyphenation) summarizes the way in which hyphenation in a source text has been treated in an encoded version of it. [2.3.3. The Editorial Practices Declaration 16.3.2. Declarable Elements] -->
<!ELEMENT hyphenation (%model.pLike;)+>
<!ATTLIST hyphenation xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST hyphenation
 %att.global.attributes;
 %att.declarable.attributes;
eol (all|some|hard|none) "some"  >
<!--doc:(identifier) supplies any form of identifier used to identify some object, such as a bibliographic item, a person, a title, an organization, etc. in a standardized way. [14.3.1. Basic Principles 2.2.4. Publication, Distribution, Licensing, etc. 2.2.5. The Series Statement ********. Imprint, Size of a Document, and Reprint Information] -->
<!ELEMENT idno (#PCDATA|_DUMMY_model.gLike|idno)*>
<!ATTLIST idno xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST idno
 %att.global.attributes;
 %att.sortable.attributes;
 %att.datable.attributes;
 %att.typed.attribute.subtype;
 %att.cmc.attributes;
type %teidata.enumerated;  #IMPLIED
calendar CDATA  #IMPLIED >
<!--doc:(imprimatur) contains a formal statement authorizing the publication of a work, sometimes required to appear on a title page or its verso. [4.6. Title Pages] -->
<!ELEMENT imprimatur %macro.paraContent;>
<!ATTLIST imprimatur xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST imprimatur
 %att.global.attributes; >
<!--doc:groups information relating to the publication or distribution of a bibliographic item. [********. Imprint, Size of a Document, and Reprint Information] -->
<!ELEMENT imprint (((classCode|catRef)*,((%model.imprintPart;|%model.dateLike;),(respStmt)*,(%model.global;)*)+))>
<!ATTLIST imprint xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST imprint
 %att.global.attributes; >
<!--doc:(index entry) marks a location to be indexed for whatever purpose. [3.9.2. Index Entries] -->
<!ELEMENT index ((term,(index)?)*)*>
<!ATTLIST index xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST index
 %att.global.attributes;
 %att.spanning.attributes;
 %att.cmc.attributes;
indexName %teidata.name;  #IMPLIED >
<!--doc:(interaction) describes the extent, cardinality and nature of any interaction among those producing and experiencing the text, for example in the form of response or interjection, commentary, etc. [16.2.1. The Text Description] -->
<!ELEMENT interaction %macro.phraseSeq.limited;>
<!ATTLIST interaction xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST interaction
 %att.global.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED
active %teidata.enumerated;  #IMPLIED
passive %teidata.enumerated;  #IMPLIED >
<!--doc:(interpretation) describes the scope of any analytic or interpretive information added to the text in addition to the transcription. [2.3.3. The Editorial Practices Declaration] -->
<!ELEMENT interpretation (%model.pLike;)+>
<!ATTLIST interpretation xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST interpretation
 %att.global.attributes;
 %att.declarable.attributes; >
<!--doc:(item) contains one component of a list. [3.8. Lists 2.6. The Revision Description] -->
<!ELEMENT item %macro.specialPara;>
<!ATTLIST item xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST item
 %att.global.attributes;
 %att.sortable.attributes; >
<!--doc:(join) identifies a possibly fragmented segment of text, by pointing at the possibly discontiguous elements which compose it. [17.7. Aggregation] -->
<!ELEMENT join (%model.descLike;|_DUMMY_model.certLike)*>
<!ATTLIST join xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST join
 %att.global.attributes;
 %att.pointing.attributes;
 %att.typed.attributes;
 %att.cmc.attributes;
result %teidata.name;  #IMPLIED
scope (root|branches) "root"  >
<!--doc:(join group) groups a collection of join elements and possibly pointers. [17.7. Aggregation] -->
<!ELEMENT joinGrp (((equiv|gloss|%model.descLike;)*,(join|ptr)+))>
<!ATTLIST joinGrp xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST joinGrp
 %att.global.attributes;
 %att.pointing.group.attributes;
 %att.cmc.attributes;
result %teidata.name;  #IMPLIED >
<!--doc:(keywords) contains a list of keywords or phrases identifying the topic or nature of a text. [2.4.3. The Text Classification] -->
<!ELEMENT keywords ((term)+|list)>
<!ATTLIST keywords xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST keywords
 %att.global.attributes;
scheme %teidata.pointer;  #IMPLIED >
<!--doc:(verse line) contains a single, possibly incomplete, line of verse. [3.13.1. Core Tags for Verse 3.13. Passages of Verse or Drama 7.2.5. Speech Contents] -->
<!ELEMENT l (#PCDATA|_DUMMY_model.gLike|%model.phrase;|%model.inter;|%model.global;)*>
<!ATTLIST l xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST l
 %att.global.attributes;
 %att.fragmentable.attributes;
 %att.cmc.attributes; >
<!--doc:(label) contains any label or heading used to identify part of a text, typically but not exclusively in a list or glossary. [3.8. Lists] -->
<!ELEMENT label %macro.phraseSeq;>
<!ATTLIST label xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST label
 %att.global.attributes;
 %att.typed.attributes;
 %att.placement.attributes;
 %att.written.attributes;
 %att.cmc.attributes; >
<!--doc:(language knowledge) summarizes the state of a person's linguistic knowledge, either as prose or by a list of langKnown elements. [********. Personal Characteristics] -->
<!ELEMENT langKnowledge (((precision)*,(%model.pLike;|(langKnown)+)))>
<!ATTLIST langKnowledge xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST langKnowledge
 %att.global.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED
calendar CDATA  #IMPLIED
tags CDATA  #IMPLIED >
<!--doc:(language known) summarizes the state of a person's linguistic competence, i.e., knowledge of a single language. [16.2.2. The Participant Description] -->
<!ELEMENT langKnown %macro.phraseSeq.limited;>
<!ATTLIST langKnown xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST langKnown
 %att.global.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
calendar CDATA  #IMPLIED
tag %teidata.language;  #REQUIRED
level %teidata.word;  #IMPLIED >
<!--doc:(language usage) describes the languages, sublanguages, registers, dialects, etc. represented within a text. [2.4.2. Language Usage 2.4. The Profile Description 16.3.2. Declarable Elements] -->
<!ELEMENT langUsage ((%model.pLike;)+|(language)+)>
<!ATTLIST langUsage xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST langUsage
 %att.global.attributes;
 %att.declarable.attributes; >
<!--doc:(language) characterizes a single language or sublanguage used within a text. [2.4.2. Language Usage] -->
<!ELEMENT language %macro.phraseSeq.limited;>
<!ATTLIST language xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST language
 %att.global.attributes;
ident %teidata.language;  #REQUIRED
usage CDATA #IMPLIED >
<!--doc:(line beginning) marks the beginning of a new (typographic) line in some edition or version of a text. [3.11.3. Milestone
Elements 7.2.5. Speech Contents] -->
<!ELEMENT lb  EMPTY>
<!ATTLIST lb xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST lb
 %att.global.attributes;
 %att.typed.attributes;
 %att.edition.attributes;
 %att.spanning.attributes;
 %att.breaking.attributes;
 %att.cmc.attributes; >
<!--doc:(line group) contains one or more verse lines functioning as a formal unit, e.g. a stanza, refrain, verse paragraph, etc. [3.13.1. Core Tags for Verse 3.13. Passages of Verse or Drama 7.2.5. Speech Contents] -->
<!ELEMENT lg (((%model.divTop;|%model.global;)*,(%model.lLike;|%model.stageLike;|%model.labelLike;|%model.pPart.transcriptional;|lg),(%model.lLike;|%model.stageLike;|%model.labelLike;|%model.pPart.transcriptional;|%model.global;|lg)*,((%model.divBottom;),(%model.global;)*)*))>
<!ATTLIST lg xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST lg
 %att.global.attributes;
 %att.divLike.attributes;
 %att.typed.attributes;
 %att.declaring.attributes;
 %att.cmc.attributes; >
<!--doc:contains information about a licence or other legal agreement applicable to the text. [2.2.4. Publication, Distribution, Licensing, etc.] -->
<!ELEMENT licence %macro.specialPara;>
<!ATTLIST licence xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST licence
 %att.global.attributes;
 %att.pointing.attributes;
 %att.datable.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(link) defines an association or hypertextual link among elements or passages, of some type not more precisely specifiable by other elements. [17.1. Links] -->
<!ELEMENT link  EMPTY>
<!ATTLIST link xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST link
 %att.global.attributes;
 %att.pointing.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(link group) defines a collection of associations or hypertextual links. [17.1. Links] -->
<!ELEMENT linkGrp (((%model.descLike;)*,(link|ptr)+))>
<!ATTLIST linkGrp xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST linkGrp
 %att.global.attributes;
 %att.pointing.group.attributes;
 %att.cmc.attributes; >
<!--doc:(list) contains any sequence of items organized as a list. [3.8. Lists] -->
<!ELEMENT list (((%model.divTop;|%model.global;|(desc)*)*,((item,(%model.global;)*)+|((headLabel)?,(headItem)?,(label,(%model.global;)*,item,(%model.global;)*)+)),((%model.divBottom;),(%model.global;)*)*))>
<!ATTLIST list xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST list
 %att.global.attributes;
 %att.sortable.attributes;
 %att.typed.attribute.subtype;
 %att.cmc.attributes;
type %teidata.enumerated;  #IMPLIED >
<!--doc:contains a list of annotations, typically encoded as annotation, annotationBlock, or note, possibly organized with nested listAnnotation elements. [17.10. The standOff Container] -->
<!ELEMENT listAnnotation (((%model.headLike;)*,(%model.labelLike;)*,(%model.annotationLike;|listAnnotation)+))>
<!ATTLIST listAnnotation xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST listAnnotation
 %att.global.attributes;
 %att.typed.attributes;
 %att.notated.attributes;
 %att.declaring.attributes; >
<!--doc:(citation list) contains a list of bibliographic citations of any kind. [3.12.1. Methods of Encoding Bibliographic References and Lists of References 2.2.7. The Source Description 16.3.2. Declarable Elements] -->
<!ELEMENT listBibl (((%model.headLike;)*,(desc)*,(%model.milestoneLike;|relation|listRelation)*,((%model.biblLike;)+,(%model.milestoneLike;|relation|listRelation)*)+))>
<!ATTLIST listBibl xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST listBibl
 %att.global.attributes;
 %att.sortable.attributes;
 %att.declarable.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:groups a number of change descriptions associated with either the creation of a source text or the revision of an encoded text. [2.6. The Revision Description 12.7. Identifying Changes and Revisions] -->
<!ELEMENT listChange (((desc)*,(listChange|change)+))>
<!ATTLIST listChange xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST listChange
 %att.global.attributes;
 %att.sortable.attributes;
 %att.typed.attributes;
ordered %teidata.truthValue;  "true"  >
<!--doc:(list of events) contains a list of descriptions, each of which provides information about an identifiable event. [14.3.1. Basic Principles] -->
<!ELEMENT listEvent (((%model.headLike;)*,(desc)*,(relation|listRelation)*,((%model.eventLike;)+,(relation|listRelation)*)+))>
<!ATTLIST listEvent xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST listEvent
 %att.global.attributes;
 %att.typed.attributes;
 %att.declarable.attributes;
 %att.sortable.attributes;
 %att.cmc.attributes; >
<!--doc:(list of canonical names) contains a list of nyms, that is, standardized names for any thing. [14.3.7. Names and Nyms] -->
<!ELEMENT listNym (((%model.headLike;)*,(desc)*,(relation|listRelation)*,((nym|listNym)+,(relation|listRelation)*)+))>
<!ATTLIST listNym xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST listNym
 %att.global.attributes;
 %att.typed.attributes;
 %att.declarable.attributes;
 %att.sortable.attributes;
 %att.cmc.attributes; >
<!--doc:(list of objects) contains a list of descriptions, each of which provides information about an identifiable physical object. [14.3.6. Objects] -->
<!ELEMENT listObject (((%model.headLike;)*,(desc)*,(relation|listRelation)*,((%model.objectLike;)+,(relation|listRelation)*)+))>
<!ATTLIST listObject xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST listObject
 %att.global.attributes;
 %att.typed.attributes;
 %att.declarable.attributes;
 %att.sortable.attributes;
 %att.cmc.attributes; >
<!--doc:(list of organizations) contains a list of elements, each of which provides information about an identifiable organization. [14.2.2. Organizational Names] -->
<!ELEMENT listOrg (((%model.headLike;)*,(desc)*,(relation|listRelation)*,((org|listOrg)+,(relation|listRelation)*)+))>
<!ATTLIST listOrg xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST listOrg
 %att.global.attributes;
 %att.typed.attributes;
 %att.declarable.attributes;
 %att.sortable.attributes;
 %att.cmc.attributes; >
<!--doc:(list of persons) contains a list of descriptions, each of which provides information about an identifiable person or a group of people, for example the participants in a language interaction, or the people referred to in a historical source. [14.3.2. The Person Element 16.2. Contextual Information 2.4. The Profile Description 16.3.2. Declarable Elements] -->
<!ELEMENT listPerson (((%model.headLike;)*,(desc)*,(relation|listRelation)*,((%model.personLike;|listPerson)+,(relation|listRelation)*)+))>
<!ATTLIST listPerson xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST listPerson
 %att.global.attributes;
 %att.typed.attributes;
 %att.declarable.attributes;
 %att.sortable.attributes;
 %att.cmc.attributes; >
<!--doc:(list of places) contains a list of places, optionally followed by a list of relationships (other than containment) defined amongst them. [2.2.7. The Source Description 14.3.4. Places] -->
<!ELEMENT listPlace (((%model.headLike;)*,(desc)*,(relation|listRelation)*,((%model.placeLike;|listPlace)+,(relation|listRelation)*)+))>
<!ATTLIST listPlace xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST listPlace
 %att.global.attributes;
 %att.typed.attributes;
 %att.declarable.attributes;
 %att.sortable.attributes;
 %att.cmc.attributes; >
<!--doc:(list of prefix definitions) contains a list of definitions of prefixing schemes used in teidata.pointer values, showing how abbreviated URIs using each scheme may be expanded into full URIs. [17.2.3. Using Abbreviated Pointers] -->
<!ELEMENT listPrefixDef (((desc)*,(prefixDef|listPrefixDef)+))>
<!ATTLIST listPrefixDef xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST listPrefixDef
 %att.global.attributes; >
<!--doc:provides information about relationships identified amongst people, places, and organizations, either informally as prose or as formally expressed relation links. [********. Personal Relationships] -->
<!ELEMENT listRelation (((%model.headLike;)*,(desc)*,(%model.pLike;|(relation|listRelation)+)))>
<!ATTLIST listRelation xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST listRelation
 %att.global.attributes;
 %att.typed.attributes;
 %att.sortable.attributes;
 %att.cmc.attributes; >
<!--doc:contains a brief informal description of the kind of place concerned, for example: a room, a restaurant, a park bench, etc. [16.2.3. The Setting Description] -->
<!ELEMENT locale %macro.phraseSeq.limited;>
<!ATTLIST locale xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST locale
 %att.global.attributes; >
<!--doc:(location) defines the location of a place as a set of geographical coordinates, in terms of other named geo-political entities, or as an address. [14.3.4. Places] -->
<!ELEMENT location (precision|%model.labelLike;|%model.placeNamePart;|%model.offsetLike;|%model.measureLike;|%model.addressLike;|%model.noteLike;|%model.biblLike;)*>
<!ATTLIST location xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST location
 %att.global.attributes;
 %att.typed.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(measure) contains a word or phrase referring to some quantity of an object or commodity, usually comprising a number, a unit, and a commodity name. [3.6.3. Numbers and
Measures] -->
<!ELEMENT measure %macro.phraseSeq;>
<!ATTLIST measure xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST measure
 %att.global.attributes;
 %att.typed.attribute.subtype;
 %att.measurement.attributes;
 %att.cmc.attributes;
 %att.ranging.attributes;
type %teidata.enumerated;  #IMPLIED >
<!--doc:(measure group) contains a group of dimensional specifications which relate to the same object, for example the height and width of a manuscript page. [11.3.4. Dimensions] -->
<!ELEMENT measureGrp (#PCDATA|_DUMMY_model.gLike|%model.measureLike;)*>
<!ATTLIST measureGrp xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST measureGrp
 %att.global.attributes;
 %att.measurement.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:indicates the location of any form of external media such as an audio or video clip etc. [3.10. Graphics and Other Non-textual Components] -->
<!ELEMENT media (%model.descLike;)*>
<!ATTLIST media xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST media
 %att.typed.attributes;
 %att.global.attributes;
 %att.media.attribute.width;
 %att.media.attribute.height;
 %att.media.attribute.scale;
 %att.resourced.attributes;
 %att.declaring.attributes;
 %att.timed.attributes;
 %att.cmc.attributes;
mimeType CDATA  #REQUIRED >
<!--doc:contains the formalized descriptive title for a meeting or conference, for use in a bibliographic description for an item derived from such a meeting, or as a heading or preamble to publications emanating from it. [********. Titles, Authors, and Editors] -->
<!ELEMENT meeting %macro.limitedContent;>
<!ATTLIST meeting xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST meeting
 %att.global.attributes;
 %att.datable.attributes;
 %att.canonical.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:marks words or phrases mentioned, not used. [3.3.3. Quotation] -->
<!ELEMENT mentioned %macro.phraseSeq;>
<!ATTLIST mentioned xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST mentioned
 %att.global.attributes;
 %att.cmc.attributes; >
<!--doc:(milestone) marks a boundary point separating any kind of section of a text, typically but not necessarily indicating a point at which some part of a standard reference system changes, where the change is not represented by a structural element. [3.11.3. Milestone
Elements] -->
<!ELEMENT milestone  EMPTY>
<!ATTLIST milestone xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST milestone
 %att.global.attributes;
 %att.milestoneUnit.attributes;
 %att.typed.attributes;
 %att.edition.attributes;
 %att.spanning.attributes;
 %att.breaking.attributes;
 %att.cmc.attributes; >
<!--doc:(monographic level) contains bibliographic elements describing an item (e.g. a book or journal) published as an independent item (i.e. as a separate physical object). [********. Analytic, Monographic, and Series Levels] -->
<!ELEMENT monogr (((((author|editor|meeting|respStmt),(author|editor|meeting|respStmt)*,(title)+,(%model.ptrLike;|idno|textLang|editor|respStmt)*)|((title|%model.ptrLike;|idno)+,(textLang|author|editor|meeting|respStmt)*)|(authority,idno))?,(availability)*,(%model.noteLike;)*,(edition,(idno|%model.ptrLike;|editor|sponsor|funder|respStmt)*)*,imprint,(imprint|extent|biblScope)*))>
<!ATTLIST monogr xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST monogr
 %att.global.attributes; >
<!--doc:(name, proper noun) contains a proper noun or noun phrase. [3.6.1. Referring Strings] -->
<!ELEMENT name %macro.phraseSeq;>
<!ATTLIST name xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST name
 %att.global.attributes;
 %att.personal.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.typed.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(name link) contains a connecting phrase or link used within a name but not regarded as part of it, such as van der or of. [14.2.1. Personal Names] -->
<!ELEMENT nameLink %macro.phraseSeq;>
<!ATTLIST nameLink xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST nameLink
 %att.global.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(namespace) supplies the formal name of the namespace to which the elements documented by its children belong. [2.3.4. The Tagging Declaration] -->
<!ELEMENT namespace (tagUsage)+>
<!ATTLIST namespace xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST namespace
 %att.global.attributes;
name CDATA  #REQUIRED >
<!--doc:(nationality) contains an informal description of a person's present or past nationality or citizenship. [16.2.2. The Participant Description] -->
<!ELEMENT nationality %macro.phraseSeq;>
<!ATTLIST nationality xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST nationality
 %att.global.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.naming.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED
calendar CDATA  #IMPLIED >
<!--doc:(normalization) indicates the extent of normalization or regularization of the original source carried out in converting it to electronic form. [2.3.3. The Editorial Practices Declaration 16.3.2. Declarable Elements] -->
<!ELEMENT normalization (%model.pLike;)+>
<!ATTLIST normalization xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST normalization
 %att.global.attributes;
 %att.declarable.attributes;
method (silent|markup) "silent"  >
<!--doc:(note) contains a note or annotation. [3.9.1. Notes and Simple Annotation 2.2.6. The Notes Statement ********. Notes and Statement of Language ********. Notes within Entries] -->
<!ELEMENT note %macro.specialPara;>
<!ATTLIST note xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST note
 %att.global.attributes;
 %att.placement.attributes;
 %att.pointing.attributes;
 %att.typed.attributes;
 %att.written.attributes;
 %att.anchoring.attributes;
 %att.cmc.attributes; >
<!--doc:(note group) contains a group of notes [*******. Encoding Grouped Notes] -->
<!ELEMENT noteGrp (((desc)*,(note|noteGrp)+))>
<!ATTLIST noteGrp xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST noteGrp
 %att.global.attributes;
 %att.placement.attributes;
 %att.pointing.attributes;
 %att.typed.attributes;
 %att.written.attributes;
 %att.anchoring.attributes;
 %att.cmc.attributes; >
<!--doc:(notes statement) collects together any notes providing information about a text additional to that recorded in other parts of the bibliographic description. [2.2.6. The Notes Statement 2.2. The File Description] -->
<!ELEMENT notesStmt (%model.noteLike;|relatedItem)+>
<!ATTLIST notesStmt xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST notesStmt
 %att.global.attributes; >
<!--doc:(number) contains a number, written in any form. [3.6.3. Numbers and
Measures] -->
<!ELEMENT num %macro.phraseSeq;>
<!ATTLIST num xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST num
 %att.global.attributes;
 %att.typed.attribute.subtype;
 %att.ranging.attributes;
 %att.cmc.attributes;
type %teidata.enumerated;  #IMPLIED
value %teidata.numeric;  #IMPLIED >
<!--doc:(canonical name) contains the definition for a canonical name or name component of any kind. [14.3.7. Names and Nyms] -->
<!ELEMENT nym (((idno)*,(_DUMMY_model.entryPart)*,(%model.pLike;)*,(nym)*))>
<!ATTLIST nym xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST nym
 %att.global.attributes;
 %att.typed.attributes;
 %att.sortable.attributes;
parts CDATA  #IMPLIED >
<!--doc:contains a description of a single identifiable physical object. [14.3.6. Objects] -->
<!ELEMENT object (((objectIdentifier)+,(%model.headLike;)*,((%model.pLike;)+|(msContents|physDesc|history|additional)*),(%model.noteLike;|%model.biblLike;|linkGrp|link)*,(object)*))>
<!ATTLIST object xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST object
 %att.global.attributes;
 %att.sortable.attributes;
 %att.typed.attributes;
 %att.declaring.attributes;
 %att.docStatus.attributes;
 %att.canonical.attributes; >
<!--doc:(object identifier) groups one or more identifiers or pieces of locating information concerning a single object. [14.3.6. Objects] -->
<!ELEMENT objectIdentifier (%model.placeNamePart;|institution|repository|collection|idno|msName|objectName|altIdentifier|address)+>
<!ATTLIST objectIdentifier xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST objectIdentifier
 %att.global.attributes; >
<!--doc:(name of an object) contains a proper noun or noun phrase used to refer to an object. [14.2.5. Object Names] -->
<!ELEMENT objectName %macro.phraseSeq;>
<!ATTLIST objectName xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST objectName
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.global.attributes;
 %att.personal.attributes;
 %att.typed.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(occupation) contains an informal description of a person's trade, profession or occupation. [16.2.2. The Participant Description] -->
<!ELEMENT occupation %macro.specialPara;>
<!ATTLIST occupation xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST occupation
 %att.global.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.naming.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED
calendar CDATA  #IMPLIED
scheme %teidata.pointer;  #IMPLIED
code %teidata.pointer;  #IMPLIED >
<!--doc:(offset) marks that part of a relative temporal or spatial expression which indicates the direction of the offset between the two place names, dates, or times involved in the expression. [14.2.3. Place Names] -->
<!ELEMENT offset %macro.phraseSeq;>
<!ATTLIST offset xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST offset
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.global.attributes;
 %att.naming.attributes;
 %att.typed.attributes;
 %att.dimensions.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(opener) groups together dateline, byline, salutation, and similar phrases appearing as a preliminary group at the start of a division, especially of a letter. [4.2. Elements Common to All Divisions] -->
<!ELEMENT opener (#PCDATA|_DUMMY_model.gLike|%model.phrase;|argument|byline|dateline|epigraph|salute|signed|%model.global;)*>
<!ATTLIST opener xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST opener
 %att.global.attributes;
 %att.written.attributes;
 %att.cmc.attributes; >
<!--doc:(organization) provides information about an identifiable organization such as a business, a tribe, or any other grouping of people. [14.3.3. Organizational Data] -->
<!ELEMENT org (((%model.headLike;)*,((%model.pLike;)*|(%model.labelLike;|%model.nameLike;|%model.placeLike;|%model.orgPart;|%model.milestoneLike;)*),(%model.noteLike;|%model.biblLike;|linkGrp|link|ptr)*,(%model.personLike;)*))>
<!ATTLIST org xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST org
 %att.global.attributes;
 %att.typed.attributes;
 %att.editLike.attributes;
 %att.sortable.attributes;
role NMTOKENS  #IMPLIED >
<!--doc:(organization name) contains an organizational name. [14.2.2. Organizational Names] -->
<!ELEMENT orgName %macro.phraseSeq;>
<!ATTLIST orgName xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST orgName
 %att.global.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.personal.attributes;
 %att.typed.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(original form) contains a reading which is marked as following the original, rather than being normalized or corrected. [3.5.2. Regularization and
Normalization 13. Critical Apparatus] -->
<!ELEMENT orig %macro.paraContent;>
<!ATTLIST orig xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST orig
 %att.global.attributes;
 %att.cmc.attributes; >
<!--doc:(paragraph) marks paragraphs in prose. [3.1. Paragraphs 7.2.5. Speech Contents] -->
<!ELEMENT p %macro.paraContent;>
<!ATTLIST p xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST p
 %att.global.attributes;
 %att.declaring.attributes;
 %att.fragmentable.attributes;
 %att.written.attributes;
 %att.cmc.attributes; >
<!--doc:(participation description) describes the identifiable speakers, voices, or other participants in any kind of text or other persons named or otherwise referred to in a text, edition, or metadata. [16.2. Contextual Information] -->
<!ELEMENT particDesc ((%model.pLike;)+|(%model.personLike;|listPerson|listOrg)+)>
<!ATTLIST particDesc xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST particDesc
 %att.global.attributes;
 %att.declarable.attributes; >
<!--doc:(page beginning) marks the beginning of a new page in a paginated document. [3.11.3. Milestone
Elements] -->
<!ELEMENT pb  EMPTY>
<!ATTLIST pb xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST pb
 %att.global.attributes;
 %att.typed.attributes;
 %att.edition.attributes;
 %att.spanning.attributes;
 %att.breaking.attributes;
 %att.cmc.attributes; >
<!--doc:(personal name) contains a proper noun or proper-noun phrase referring to a person, possibly including one or more of the person's forenames, surnames, honorifics, added names, etc. [14.2.1. Personal Names] -->
<!ELEMENT persName %macro.phraseSeq;>
<!ATTLIST persName xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST persName
 %att.global.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.personal.attributes;
 %att.typed.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(personal pronouns) indicates the personal pronouns used, or assumed to be used, by the individual being described. [********. Personal Characteristics] -->
<!ELEMENT persPronouns %macro.phraseSeq;>
<!ATTLIST persPronouns xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST persPronouns
 %att.global.attributes;
 %att.datable.attributes;
 %att.typed.attributes;
 %att.cmc.attributes;
evidence %teidata.enumerated;  #IMPLIED
calendar CDATA  #IMPLIED
value NMTOKENS  #IMPLIED >
<!--doc:(person) provides information about an identifiable individual, for example a participant in a language interaction, or a person referred to in a historical source. [14.3.2. The Person Element 16.2.2. The Participant Description] -->
<!ELEMENT person ((%model.pLike;)+|(%model.personPart;|%model.global;|ptr)*)>
<!ATTLIST person xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST person
 %att.global.attributes;
 %att.editLike.attributes;
 %att.sortable.attributes;
role NMTOKENS  #IMPLIED
sex CDATA  #IMPLIED
gender CDATA  #IMPLIED
age %teidata.enumerated;  #IMPLIED >
<!--doc:(personal group) describes a group of individuals treated as a single person for analytic purposes. [16.2.2. The Participant Description] -->
<!ELEMENT personGrp ((%model.pLike;)+|(%model.personPart;|%model.global;)*)>
<!ATTLIST personGrp xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST personGrp
 %att.global.attributes;
 %att.sortable.attributes;
role %teidata.enumerated;  #IMPLIED
sex CDATA  #IMPLIED
gender CDATA  #IMPLIED
age %teidata.enumerated;  #IMPLIED
size CDATA  #IMPLIED >
<!--doc:provides information about one of the personalities identified for a given individual, where an individual has multiple personalities. [14.3.2. The Person Element] -->
<!ELEMENT persona ((%model.pLike;)+|(%model.personPart;|%model.global;)*)>
<!ATTLIST persona xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST persona
 %att.global.attributes;
 %att.editLike.attributes;
 %att.sortable.attributes;
role NMTOKENS  #IMPLIED
sex CDATA  #IMPLIED
gender CDATA  #IMPLIED
age %teidata.enumerated;  #IMPLIED >
<!--doc:(place) contains data about a geographic location [14.3.4. Places] -->
<!ELEMENT place (((%model.headLike;)*,((%model.pLike;)*|(%model.labelLike;|%model.placeStateLike;|%model.eventLike;|name)*),(%model.noteLike;|%model.biblLike;|idno|ptr|linkGrp|link)*,(%model.placeLike;|listPlace)*))>
<!ATTLIST place xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST place
 %att.global.attributes;
 %att.typed.attributes;
 %att.editLike.attributes;
 %att.sortable.attributes; >
<!--doc:(place name) contains an absolute or relative place name. [14.2.3. Place Names] -->
<!ELEMENT placeName %macro.phraseSeq;>
<!ATTLIST placeName xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST placeName
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.global.attributes;
 %att.personal.attributes;
 %att.typed.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(population) contains information about the population of a place. [********. States, Traits, and Events] -->
<!ELEMENT population (((precision)*,(%model.headLike;)*,(((%model.pLike;)+|(%model.labelLike;)+),(%model.noteLike;|%model.biblLike;)*)?,(population)*))>
<!ATTLIST population xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST population
 %att.global.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.naming.attributes;
 %att.typed.attributes;
 %att.dimensions.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(postal box or post office box) contains a number or other identifier for some postal delivery point other than a street address. [3.6.2. Addresses] -->
<!ELEMENT postBox (#PCDATA)>
<!ATTLIST postBox xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST postBox
 %att.global.attributes; >
<!--doc:(postal code) contains a numerical or alphanumeric code used as part of a postal address to simplify sorting or delivery of mail. [3.6.2. Addresses] -->
<!ELEMENT postCode (#PCDATA)>
<!ATTLIST postCode xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST postCode
 %att.global.attributes; >
<!--doc:contains a postscript, e.g. to a letter. [4.2. Elements Common to All Divisions] -->
<!ELEMENT postscript (((%model.global;|%model.divTopPart;)*,(%model.common;),(%model.global;|%model.common;)*,((%model.divBottomPart;),(%model.global;)*)*))>
<!ATTLIST postscript xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST postscript
 %att.global.attributes;
 %att.written.attributes;
 %att.cmc.attributes; >
<!--doc:(prefix definition) defines a prefixing scheme used in teidata.pointer values, showing how abbreviated URIs using the scheme may be expanded into full URIs. [17.2.3. Using Abbreviated Pointers] -->
<!ELEMENT prefixDef (%model.pLike;)*>
<!ATTLIST prefixDef xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST prefixDef
 %att.global.attributes;
 %att.patternReplacement.attributes;
ident %teidata.prefix;  #REQUIRED >
<!--doc:(preparedness) describes the extent to which a text may be regarded as prepared or spontaneous. [16.2.1. The Text Description] -->
<!ELEMENT preparedness %macro.phraseSeq.limited;>
<!ATTLIST preparedness xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST preparedness
 %att.global.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED >
<!--doc:(principal researcher) supplies the name of the principal researcher responsible for the creation of an electronic text. [2.2.1. The Title Statement] -->
<!ELEMENT principal %macro.phraseSeq.limited;>
<!ATTLIST principal xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST principal
 %att.global.attributes;
 %att.canonical.attributes;
 %att.datable.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(text-profile description) provides a detailed description of non-bibliographic aspects of a text, specifically the languages and sublanguages used, the situation in which it was produced, the participants and their setting. [2.4. The Profile Description 2.1.1. The TEI Header and Its Components] -->
<!ELEMENT profileDesc (%model.profileDescPart;)*>
<!ATTLIST profileDesc xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST profileDesc
 %att.global.attributes; >
<!--doc:(project description) describes in detail the aim or purpose for which an electronic file was encoded, together with any other relevant information concerning the process by which it was assembled or collected. [2.3.1. The Project Description 2.3. The Encoding Description 16.3.2. Declarable Elements] -->
<!ELEMENT projectDesc (%model.pLike;)+>
<!ATTLIST projectDesc xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST projectDesc
 %att.global.attributes;
 %att.declarable.attributes; >
<!--doc:(pointer) defines a pointer to another location. [3.7. Simple Links and Cross-References 17.1. Links] -->
<!ELEMENT ptr  EMPTY>
<!ATTLIST ptr xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST ptr
 %att.cReferencing.attributes;
 %att.declaring.attributes;
 %att.global.attributes;
 %att.internetMedia.attributes;
 %att.pointing.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(publication place) contains the name of the place where a bibliographic item was published. [********. Imprint, Size of a Document, and Reprint Information] -->
<!ELEMENT pubPlace %macro.phraseSeq;>
<!ATTLIST pubPlace xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST pubPlace
 %att.global.attributes;
 %att.naming.attributes; >
<!--doc:(publication statement) groups information concerning the publication or distribution of an electronic or other text. [2.2.4. Publication, Distribution, Licensing, etc. 2.2. The File Description] -->
<!ELEMENT publicationStmt (((%model.publicationStmtPart.agency;),(%model.publicationStmtPart.detail;)*)+|(%model.pLike;)+)>
<!ATTLIST publicationStmt xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST publicationStmt
 %att.global.attributes; >
<!--doc:(publisher) provides the name of the organization responsible for the publication or distribution of a bibliographic item. [********. Imprint, Size of a Document, and Reprint Information 2.2.4. Publication, Distribution, Licensing, etc.] -->
<!ELEMENT publisher %macro.phraseSeq;>
<!ATTLIST publisher xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST publisher
 %att.global.attributes;
 %att.canonical.attributes; >
<!--doc:specifies editorial practice adopted with respect to punctuation marks in the original. [2.3.3. The Editorial Practices Declaration 3.2. Treatment of Punctuation] -->
<!ELEMENT punctuation (%model.pLike;)*>
<!ATTLIST punctuation xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST punctuation
 %att.declarable.attributes;
 %att.global.attributes;
marks (none|some|all) #IMPLIED
placement (internal|external) #IMPLIED >
<!--doc:characterizes a single purpose or communicative function of the text. [16.2.1. The Text Description] -->
<!ELEMENT purpose %macro.phraseSeq.limited;>
<!ATTLIST purpose xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST purpose
 %att.global.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED
degree %teidata.certainty;  #IMPLIED >
<!--doc:(quoted) contains material which is distinguished from the surrounding text using quotation marks or a similar method, for any one of a variety of reasons including, but not limited to: direct speech or thought, technical terms or jargon, authorial distance, quotations from elsewhere, and passages that are mentioned but not used. [3.3.3. Quotation] -->
<!ELEMENT q %macro.specialPara;>
<!ATTLIST q xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST q
 %att.global.attributes;
 %att.ascribed.directed.attributes;
 %att.cmc.attributes;
type %teidata.enumerated;  #IMPLIED >
<!--doc:(quotation) specifies editorial practice adopted with respect to quotation marks in the original. [2.3.3. The Editorial Practices Declaration 16.3.2. Declarable Elements] -->
<!ELEMENT quotation (%model.pLike;)*>
<!ATTLIST quotation xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST quotation
 %att.global.attributes;
 %att.declarable.attributes;
marks (none|some|all) #IMPLIED >
<!--doc:(quotation) contains a phrase or passage attributed by the narrator or author to some agency external to the text. [3.3.3. Quotation 4.3.1. Grouped Texts] -->
<!ELEMENT quote %macro.specialPara;>
<!ATTLIST quote xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST quote
 %att.global.attributes;
 %att.typed.attributes;
 %att.notated.attributes;
 %att.cmc.attributes; >
<!--doc:(ruby base) contains the base text annotated by a ruby gloss. [3.4.2. Ruby Annotations] -->
<!ELEMENT rb %macro.phraseSeq;>
<!ATTLIST rb xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST rb
 %att.global.attributes;
 %att.typed.attributes; >
<!--doc:(reference) defines a reference to another location, possibly modified by additional text or comment. [3.7. Simple Links and Cross-References 17.1. Links] -->
<!ELEMENT ref %macro.paraContent;>
<!ATTLIST ref xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST ref
 %att.cReferencing.attributes;
 %att.declaring.attributes;
 %att.global.attributes;
 %att.internetMedia.attributes;
 %att.pointing.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(reference state) specifies one component of a canonical reference defined by the milestone method. [*******. Milestone Method 2.3.6. The Reference System Declaration] -->
<!ELEMENT refState  EMPTY>
<!ATTLIST refState xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST refState
 %att.global.attributes;
 %att.milestoneUnit.attributes;
 %att.edition.attributes;
length %teidata.count;  #IMPLIED
delim %teidata.text;  #IMPLIED >
<!--doc:(references declaration) specifies how canonical references are constructed for this text. [*******. Milestone Method 2.3. The Encoding Description 2.3.6. The Reference System Declaration] -->
<!ELEMENT refsDecl ((%model.pLike;)+|(citeStructure)+|(cRefPattern)+|(refState)+)>
<!ATTLIST refsDecl xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST refsDecl
 %att.global.attributes;
 %att.declarable.attributes; >
<!--doc:(regularization) contains a reading which has been regularized or normalized in some sense. [3.5.2. Regularization and
Normalization 13. Critical Apparatus] -->
<!ELEMENT reg %macro.paraContent;>
<!ATTLIST reg xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST reg
 %att.global.attributes;
 %att.editLike.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(region) contains the name of an administrative unit such as a state, province, or county, larger than a settlement, but smaller than a country. [14.2.3. Place Names] -->
<!ELEMENT region %macro.phraseSeq;>
<!ATTLIST region xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST region
 %att.global.attributes;
 %att.naming.attributes;
 %att.typed.attributes;
 %att.datable.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:contains or references some other bibliographic item which is related to the present one in some specified manner, for example as a constituent or alternative version of it. [********. Related Items] -->
<!ELEMENT relatedItem (%model.biblLike;|%model.ptrLike;)?>
<!ATTLIST relatedItem xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST relatedItem
 %att.global.attributes;
 %att.typed.attributes;
target %teidata.pointer;  #IMPLIED >
<!--doc:(relationship) describes any kind of relationship or linkage amongst a specified group of places, events, persons, objects or other items. [********. Personal Relationships] -->
<!ELEMENT relation (desc)?>
<!ATTLIST relation xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST relation
 %att.global.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.canonical.attributes;
 %att.sortable.attributes;
 %att.typed.attributes;
calendar CDATA  #IMPLIED
name %teidata.enumerated;  #IMPLIED
active CDATA  #IMPLIED
mutual CDATA  #IMPLIED
passive CDATA  #IMPLIED >
<!--doc:(rendition) supplies information about the rendition or appearance of one or more elements in the source text. [2.3.4. The Tagging Declaration] -->
<!ELEMENT rendition %macro.limitedContent;>
<!ATTLIST rendition xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST rendition
 %att.global.attributes;
 %att.styleDef.attributes;
scope %teidata.enumerated;  #IMPLIED
selector %teidata.text;  #IMPLIED >
<!--doc:(residence) describes a person's present or past places of residence. [16.2.2. The Participant Description] -->
<!ELEMENT residence %macro.phraseSeq;>
<!ATTLIST residence xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST residence
 %att.global.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.naming.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED
calendar CDATA  #IMPLIED >
<!--doc:(responsibility) contains a phrase describing the nature of a person's intellectual responsibility, or an organization's role in the production or distribution of a work. [********. Titles, Authors, and Editors 2.2.1. The Title Statement 2.2.2. The Edition Statement 2.2.5. The Series Statement] -->
<!ELEMENT resp %macro.phraseSeq.limited;>
<!ATTLIST resp xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST resp
 %att.global.attributes;
 %att.canonical.attributes;
 %att.datable.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(statement of responsibility) supplies a statement of responsibility for the intellectual content of a text, edition, recording, or series, where the specialized elements for authors, editors, etc. do not suffice or do not apply. May also be used to encode information about individuals or organizations which have played a role in the production or distribution of a bibliographic work. [********. Titles, Authors, and Editors 2.2.1. The Title Statement 2.2.2. The Edition Statement 2.2.5. The Series Statement] -->
<!ELEMENT respStmt (((((resp)+,(%model.nameLike.agent;)+)|((%model.nameLike.agent;)+,(resp)+)),(note)*))>
<!ATTLIST respStmt xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST respStmt
 %att.global.attributes;
 %att.canonical.attributes; >
<!--doc:(revision description) summarizes the revision history for a file. [2.6. The Revision Description 2.1.1. The TEI Header and Its Components] -->
<!ELEMENT revisionDesc ((list)+|(listChange)+|(change)+)>
<!ATTLIST revisionDesc xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST revisionDesc
 %att.global.attributes;
 %att.docStatus.attributes; >
<!--doc:(role name) contains a name component which indicates that the referent has a particular role or position in society, such as an official title or rank. [14.2.1. Personal Names] -->
<!ELEMENT roleName %macro.phraseSeq;>
<!ATTLIST roleName xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST roleName
 %att.global.attributes;
 %att.personal.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(referencing string) contains a general purpose name or referring string. [14.2.1. Personal Names 3.6.1. Referring Strings] -->
<!ELEMENT rs %macro.phraseSeq;>
<!ATTLIST rs xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST rs
 %att.global.attributes;
 %att.naming.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(ruby text) contains a ruby text, an annotation closely associated with a passage of the main text. [3.4.2. Ruby Annotations] -->
<!ELEMENT rt %macro.phraseSeq;>
<!ATTLIST rt xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST rt
 %att.global.attributes;
 %att.typed.attributes;
 %att.placement.attributes;
 %att.transcriptional.attributes;
target %teidata.pointer;  #IMPLIED
from %teidata.pointer;  #IMPLIED
to %teidata.pointer;  #IMPLIED >
<!--doc:(ruby container) contains a passage of base text along with its associated ruby gloss(es). [3.4.2. Ruby Annotations] -->
<!ELEMENT ruby ((rb,(rt)+))>
<!ATTLIST ruby xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST ruby
 %att.global.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(speech or thought) indicates passages thought or spoken aloud, whether explicitly indicated in the source or not, whether directly or indirectly reported, whether by real people or fictional characters. [3.3.3. Quotation] -->
<!ELEMENT said %macro.specialPara;>
<!ATTLIST said xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST said
 %att.global.attributes;
 %att.ascribed.directed.attributes;
 %att.cmc.attributes;
aloud %teidata.xTruthValue;  #IMPLIED
direct %teidata.xTruthValue;  "true"  >
<!--doc:(salutation) contains a salutation or greeting prefixed to a foreword, dedicatory epistle, or other division of a text, or the salutation in the closing of a letter, preface, etc. [4.2.2. Openers and Closers] -->
<!ELEMENT salute %macro.paraContent;>
<!ATTLIST salute xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST salute
 %att.global.attributes;
 %att.written.attributes;
 %att.cmc.attributes; >
<!--doc:(sampling declaration) contains a prose description of the rationale and methods used in selecting texts, or parts of a text, for inclusion in the resource. [2.3.2. The Sampling Declaration 2.3. The Encoding Description 16.3.2. Declarable Elements] -->
<!ELEMENT samplingDecl (%model.pLike;)+>
<!ATTLIST samplingDecl xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST samplingDecl
 %att.global.attributes;
 %att.declarable.attributes; >
<!--doc:(schema reference) describes or points to a related customization or schema file [2.3.10. The Schema Specification] -->
<!ELEMENT schemaRef (((%model.descLike;)?))>
<!ATTLIST schemaRef xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST schemaRef
 %att.global.attributes;
 %att.typed.attributes;
 %att.resourced.attributes;
key %teidata.xmlName;  #IMPLIED >
<!--doc:describes a particular script distinguished within the description of a manuscript or similar resource. [11.7.2. Writing, Decoration, and Other Notations] -->
<!ELEMENT scriptNote %macro.specialPara;>
<!ATTLIST scriptNote xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST scriptNote
 %att.global.attributes;
 %att.handFeatures.attributes; >
<!--doc:(arbitrary segment) represents any segmentation of text below the chunk level. [17.3. Blocks, Segments, and Anchors 6.2. Components of the Verse Line 7.2.5. Speech Contents] -->
<!ELEMENT seg %macro.paraContent;>
<!ATTLIST seg xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST seg
 %att.global.attributes;
 %att.segLike.attributes;
 %att.typed.attributes;
 %att.written.attributes;
 %att.notated.attributes;
 %att.cmc.attributes; >
<!--doc:(segmentation) describes the principles according to which the text has been segmented, for example into sentences, tone-units, graphemic strata, etc. [2.3.3. The Editorial Practices Declaration 16.3.2. Declarable Elements] -->
<!ELEMENT segmentation (%model.pLike;)+>
<!ATTLIST segmentation xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST segmentation
 %att.global.attributes;
 %att.declarable.attributes; >
<!--doc:(series information) contains information about the series in which a book or other bibliographic item has appeared. [********. Analytic, Monographic, and Series Levels] -->
<!ELEMENT series (#PCDATA|_DUMMY_model.gLike|title|%model.ptrLike;|editor|respStmt|biblScope|idno|textLang|%model.global;|availability)*>
<!ATTLIST series xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST series
 %att.global.attributes; >
<!--doc:(series statement) groups information about the series, if any, to which a publication belongs. [2.2.5. The Series Statement 2.2. The File Description] -->
<!ELEMENT seriesStmt ((%model.pLike;)+|((title)+,(editor|respStmt)*,(idno|biblScope)*))>
<!ATTLIST seriesStmt xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST seriesStmt
 %att.global.attributes;
 %att.declarable.attributes; >
<!--doc:describes one particular setting in which a language interaction takes place. [16.2.3. The Setting Description] -->
<!ELEMENT setting ((%model.pLike;)+|(%model.nameLike.agent;|%model.dateLike;|%model.settingPart;)*)>
<!ATTLIST setting xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST setting
 %att.global.attributes;
 %att.ascribed.attributes; >
<!--doc:(setting description) describes the setting or settings within which a language interaction takes place, or other places otherwise referred to in a text, edition, or metadata. [16.2. Contextual Information 2.4. The Profile Description] -->
<!ELEMENT settingDesc ((%model.pLike;)+|(setting|%model.placeLike;|listPlace)+)>
<!ATTLIST settingDesc xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST settingDesc
 %att.global.attributes;
 %att.declarable.attributes; >
<!--doc:(settlement) contains the name of a settlement such as a city, town, or village identified as a single geo-political or administrative unit. [14.2.3. Place Names] -->
<!ELEMENT settlement %macro.phraseSeq;>
<!ATTLIST settlement xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST settlement
 %att.global.attributes;
 %att.naming.attributes;
 %att.typed.attributes;
 %att.datable.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(sex) specifies the sex of an organism. [********. Personal Characteristics] -->
<!ELEMENT sex %macro.phraseSeq;>
<!ATTLIST sex xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST sex
 %att.global.attributes;
 %att.editLike.attributes;
 %att.datable.attributes;
 %att.typed.attributes;
calendar CDATA  #IMPLIED
value CDATA  #IMPLIED >
<!--doc:(Latin for thus or so) contains text reproduced although apparently incorrect or inaccurate. [3.5.1. Apparent Errors] -->
<!ELEMENT sic %macro.paraContent;>
<!ATTLIST sic xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST sic
 %att.global.attributes;
 %att.cmc.attributes; >
<!--doc:(signature) contains the closing salutation, etc., appended to a foreword, dedicatory epistle, or other division of a text. [4.2.2. Openers and Closers] -->
<!ELEMENT signed %macro.paraContent;>
<!ATTLIST signed xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST signed
 %att.global.attributes;
 %att.written.attributes;
 %att.cmc.attributes; >
<!--doc:(so called) contains a word or phrase for which the author or narrator indicates a disclaiming of responsibility, for example by the use of scare quotes or italics. [3.3.3. Quotation] -->
<!ELEMENT soCalled %macro.phraseSeq;>
<!ATTLIST soCalled xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST soCalled
 %att.global.attributes;
 %att.cmc.attributes; >
<!--doc:(socio-economic status) contains an informal description of a person's perceived social or economic status. [16.2.2. The Participant Description] -->
<!ELEMENT socecStatus %macro.phraseSeq;>
<!ATTLIST socecStatus xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST socecStatus
 %att.global.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.naming.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED
calendar CDATA  #IMPLIED
scheme %teidata.pointer;  #IMPLIED
code %teidata.pointer;  #IMPLIED >
<!--doc:(source description) describes the source(s) from which an electronic text was derived or generated, typically a bibliographic description in the case of a digitized text, or a phrase such as born digital for a text which has no previous existence. [2.2.7. The Source Description] -->
<!ELEMENT sourceDesc ((%model.pLike;)+|(%model.biblLike;|_DUMMY_model.sourceDescPart|%model.listLike;)+)>
<!ATTLIST sourceDesc xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST sourceDesc
 %att.global.attributes;
 %att.declarable.attributes; >
<!--doc:(speech) contains an individual speech in a performance text, or a passage presented as such in a prose or verse text. [3.13.2. Core Tags for Drama 3.13. Passages of Verse or Drama 7.2.2. Speeches and Speakers] -->
<!ELEMENT sp (((%model.global;)*,(speaker,(%model.global;)*)?,((lg|%model.lLike;|%model.pLike;|%model.listLike;|%model.stageLike;|%model.attributable;),((%model.global;)*|q))+))>
<!ATTLIST sp xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST sp
 %att.global.attributes;
 %att.ascribed.directed.attributes; >
<!--doc:contains a specialized form of heading or label, giving the name of one or more speakers in a dramatic text or fragment. [3.13.2. Core Tags for Drama] -->
<!ELEMENT speaker %macro.phraseSeq;>
<!ATTLIST speaker xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST speaker
 %att.global.attributes; >
<!--doc:(sponsor) specifies the name of a sponsoring organization or institution. [2.2.1. The Title Statement] -->
<!ELEMENT sponsor %macro.phraseSeq.limited;>
<!ATTLIST sponsor xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST sponsor
 %att.global.attributes;
 %att.canonical.attributes;
 %att.datable.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(stage direction) contains any kind of stage direction within a dramatic text or fragment. [3.13.2. Core Tags for Drama 3.13. Passages of Verse or Drama 7.2.4. Stage Directions] -->
<!ELEMENT stage %macro.specialPara;>
<!ATTLIST stage xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST stage
 %att.ascribed.directed.attributes;
 %att.global.attributes;
 %att.placement.attributes;
 %att.written.attributes;
 %att.cmc.attributes;
type NMTOKENS  #IMPLIED >
<!--doc:Functions as a container element for linked data, contextual information, and stand-off annotations embedded in a TEI document. [17.10. The standOff Container] -->
<!ELEMENT standOff (%model.standOffPart;)+>
<!ATTLIST standOff xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST standOff
 %att.global.attributes;
 %att.typed.attributes;
 %att.declaring.attributes; >
<!--doc:(state) contains a description of some status or quality attributed to a person, place, or organization often at some specific time or for a specific date range. [14.3.1. Basic Principles ********. Personal Characteristics] -->
<!ELEMENT state (((precision)*,((state)+|((%model.headLike;)*,(%model.pLike;)+,(%model.noteLike;|%model.biblLike;)*)|(%model.labelLike;|%model.noteLike;|%model.biblLike;)*)))>
<!ATTLIST state xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST state
 %att.global.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.typed.attributes;
 %att.naming.attributes;
 %att.dimensions.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(standard values) specifies the format used when standardized date or number values are supplied. [2.3.3. The Editorial Practices Declaration 16.3.2. Declarable Elements] -->
<!ELEMENT stdVals (%model.pLike;)+>
<!ATTLIST stdVals xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST stdVals
 %att.global.attributes;
 %att.declarable.attributes; >
<!--doc:contains a full street address including any name or number identifying a building as well as the name of the street or route on which it is located. [3.6.2. Addresses] -->
<!ELEMENT street %macro.phraseSeq;>
<!ATTLIST street xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST street
 %att.global.attributes; >
<!--doc:(style definition language declaration) specifies the name of the formal language in which style or renditional information is supplied elsewhere in the document. The specific version of the scheme may also be supplied. [2.3.5. The Default Style Definition Language Declaration] -->
<!ELEMENT styleDefDecl (%model.pLike;)*>
<!ATTLIST styleDefDecl xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST styleDefDecl
 %att.global.attributes;
 %att.declarable.attributes;
 %att.styleDef.attributes; >
<!--doc:(surname) contains a family (inherited) name, as opposed to a given, baptismal, or nick name. [14.2.1. Personal Names] -->
<!ELEMENT surname %macro.phraseSeq;>
<!ATTLIST surname xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST surname
 %att.global.attributes;
 %att.personal.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(element usage) documents the usage of a specific element within a specified document. [2.3.4. The Tagging Declaration] -->
<!ELEMENT tagUsage %macro.limitedContent;>
<!ATTLIST tagUsage xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST tagUsage
 %att.global.attributes;
 %att.datcat.attributes;
gi %teidata.name;  #REQUIRED
occurs %teidata.count;  #IMPLIED
withId %teidata.count;  #IMPLIED >
<!--doc:(tagging declaration) provides detailed information about the tagging applied to a document. [2.3.4. The Tagging Declaration 2.3. The Encoding Description] -->
<!ELEMENT tagsDecl (((rendition)*,(namespace)*))>
<!ATTLIST tagsDecl xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST tagsDecl
 %att.global.attributes;
partial %teidata.truthValue;  #IMPLIED >
<!--doc:(taxonomy) defines a typology either implicitly, by means of a bibliographic citation, or explicitly by a structured taxonomy. [2.3.7. The Classification Declaration] -->
<!ELEMENT taxonomy (((category|taxonomy)+|((%model.descLike;|equiv|gloss)+,(category|taxonomy)*))|((%model.biblLike;),(category|taxonomy)*))>
<!ATTLIST taxonomy xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST taxonomy
 %att.global.attributes;
 %att.datcat.attributes; >
<!--doc:(TEI corpus) contains the whole of a TEI encoded corpus, comprising a single corpus header and one or more TEI elements, each containing a single text header and a text. [4. Default Text Structure 16.1. Varieties of Composite Text] -->
<!ELEMENT teiCorpus ((teiHeader,(%model.resource;)*,(%model.describedResource;)+))>
<!ATTLIST teiCorpus xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST teiCorpus
 %att.global.attributes;
 %att.typed.attributes;
version %teidata.version;  #IMPLIED >
<!ATTLIST teiCorpus xsi:schemaLocation CDATA #IMPLIED
 xmlns:xsi CDATA #FIXED 'http://www.w3.org/2001/XMLSchema-instance'
 >
<!--doc:(TEI header) supplies descriptive and declarative metadata associated with a digital resource or set of resources. [2.1.1. The TEI Header and Its Components 16.1. Varieties of Composite Text] -->
<!ELEMENT teiHeader ((fileDesc,(%model.teiHeaderPart;)*,(revisionDesc)?))>
<!ATTLIST teiHeader xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST teiHeader
 %att.global.attributes; >
<!--doc:(term) contains a single-word, multi-word, or symbolic designation which is regarded as a technical term. [3.4.1. Terms and Glosses] -->
<!ELEMENT term %macro.phraseSeq;>
<!ATTLIST term xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST term
 %att.global.attributes;
 %att.declaring.attributes;
 %att.pointing.attributes;
 %att.typed.attributes;
 %att.canonical.attributes;
 %att.sortable.attributes;
 %att.cReferencing.attributes;
 %att.cmc.attributes; >
<!--doc:(terrain) contains information about the physical terrain of a place. [********. States, Traits, and Events] -->
<!ELEMENT terrain (((precision)*,(%model.headLike;)*,((%model.pLike;)+|(%model.labelLike;)+),(%model.noteLike;|%model.biblLike;)*,(terrain)*))>
<!ATTLIST terrain xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST terrain
 %att.global.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.naming.attributes;
 %att.typed.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(text) contains a single text of any kind, whether unitary or composite, for example a poem or drama, a collection of essays, a novel, a dictionary, or a corpus sample. [4. Default Text Structure 16.1. Varieties of Composite Text] -->
<!ELEMENT text (((%model.global;)*,(front,(%model.global;)*)?,(body|group),(%model.global;)*,(back,(%model.global;)*)?))>
<!ATTLIST text xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST text
 %att.global.attributes;
 %att.declaring.attributes;
 %att.typed.attributes;
 %att.written.attributes; >
<!--doc:(text classification) groups information which describes the nature or topic of a text in terms of a standard classification scheme, thesaurus, etc. [2.4.3. The Text Classification] -->
<!ELEMENT textClass (classCode|catRef|keywords)*>
<!ATTLIST textClass xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST textClass
 %att.global.attributes;
 %att.declarable.attributes; >
<!--doc:(text description) provides a description of a text in terms of its situational parameters. [16.2.1. The Text Description] -->
<!ELEMENT textDesc (((%model.textDescPart_sequence;),(purpose)+))>
<!ATTLIST textDesc xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST textDesc
 %att.global.attributes;
 %att.declarable.attributes; >
<!--doc:(text language) describes the languages and writing systems identified within the bibliographic work being described, rather than its description. [********. Imprint, Size of a Document, and Reprint Information 11.6.6. Languages and Writing Systems] -->
<!ELEMENT textLang %macro.specialPara;>
<!ATTLIST textLang xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST textLang
 %att.global.attributes;
mainLang %teidata.language;  #IMPLIED
otherLangs CDATA  #IMPLIED >
<!--doc:(time) contains a phrase defining a time of day in any format. [3.6.4. Dates and Times] -->
<!ELEMENT time (#PCDATA|_DUMMY_model.gLike|%model.phrase;|%model.global;)*>
<!ATTLIST time xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST time
 %att.global.attributes;
 %att.datable.attributes;
 %att.calendarSystem.attributes;
 %att.canonical.attributes;
 %att.editLike.attributes;
 %att.dimensions.attributes;
 %att.typed.attributes;
 %att.cmc.attributes; >
<!--doc:(timeline) provides a set of ordered points in time which can be linked to elements of a spoken text to create a temporal alignment of that text. [17.4.2. Placing Synchronous Events in Time] -->
<!ELEMENT timeline (when)+>
<!ATTLIST timeline xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST timeline
 %att.global.attributes;
 %att.cmc.attributes;
origin %teidata.pointer;  #IMPLIED
unit %teidata.enumerated;  #IMPLIED
interval %teidata.interval;  #IMPLIED >
<!--doc:(title) contains a title for any kind of work. [********. Titles, Authors, and Editors 2.2.1. The Title Statement 2.2.5. The Series Statement] -->
<!ELEMENT title %macro.paraContent;>
<!ATTLIST title xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST title
 %att.global.attributes;
 %att.typed.attribute.subtype;
 %att.canonical.attributes;
 %att.datable.attributes;
 %att.cmc.attributes;
type %teidata.enumerated;  #IMPLIED
calendar CDATA  #IMPLIED
level (a|m|j|s|u) #IMPLIED >
<!--doc:(title page) contains the title page of a text, appearing within the front or back matter. [4.6. Title Pages] -->
<!ELEMENT titlePage (((%model.global;)*,(%model.titlepagePart;),(%model.titlepagePart;|%model.global;)*))>
<!ATTLIST titlePage xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST titlePage
 %att.global.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  #IMPLIED >
<!--doc:(title part) contains a subsection or division of the title of a work, as indicated on a title page. [4.6. Title Pages] -->
<!ELEMENT titlePart %macro.paraContent;>
<!ATTLIST titlePart xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST titlePart
 %att.global.attributes;
 %att.typed.attribute.subtype;
type %teidata.enumerated;  "main"  >
<!--doc:(title statement) groups information about the title of a work and those responsible for its content. [2.2.1. The Title Statement 2.2. The File Description] -->
<!ELEMENT titleStmt (((title)+,(%model.respLike;)*))>
<!ATTLIST titleStmt xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST titleStmt
 %att.global.attributes; >
<!--doc:contains a closing title or footer appearing at the end of a division of a text. [4.2.4. Content of Textual Divisions 4.2. Elements Common to All Divisions] -->
<!ELEMENT trailer (#PCDATA|lg|_DUMMY_model.gLike|%model.phrase;|%model.inter;|%model.lLike;|%model.global;)*>
<!ATTLIST trailer xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST trailer
 %att.global.attributes;
 %att.typed.attributes;
 %att.placement.attributes;
 %att.written.attributes;
 %att.cmc.attributes; >
<!--doc:(trait) contains a description of some status or quality attributed to a person, place, or organization typically, but not necessarily, independent of the volition or action of the holder and usually not at some specific time or for a specific date range. [14.3.1. Basic Principles ********. Personal Characteristics] -->
<!ELEMENT trait (((precision)*,((trait)+|((%model.headLike;)*,(%model.pLike;)+,(%model.noteLike;|%model.biblLike;)*)|(%model.labelLike;|%model.noteLike;|%model.biblLike;)*)))>
<!ATTLIST trait xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST trait
 %att.global.attributes;
 %att.datable.attributes;
 %att.editLike.attributes;
 %att.naming.attributes;
 %att.typed.attributes;
 %att.dimensions.attributes;
 %att.cmc.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(unclear) contains a word, phrase, or passage which cannot be transcribed with certainty because it is illegible or inaudible in the source. [********. Damage, Illegibility, and Supplied Text 3.5.3. Additions, Deletions, and Omissions] -->
<!ELEMENT unclear %macro.paraContent;>
<!ATTLIST unclear xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST unclear
 %att.global.attributes;
 %att.editLike.attributes;
 %att.dimensions.attributes;
 %att.cmc.attributes;
reason NMTOKENS  #IMPLIED
agent %teidata.enumerated;  #IMPLIED >
<!--doc:contains a symbol, a word or a phrase referring to a unit of measurement in any kind of formal or informal system. [3.6.3. Numbers and
Measures] -->
<!ELEMENT unit %macro.phraseSeq;>
<!ATTLIST unit xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST unit
 %att.global.attributes;
 %att.typed.attributes;
 %att.measurement.attributes;
 %att.cmc.attributes; >
<!--doc:(unit declarations) provides information about units of measurement that are not members of the International System of Units. [2.3.9. The Unit Declaration] -->
<!ELEMENT unitDecl (unitDef)+>
<!ATTLIST unitDecl xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST unitDecl
 %att.canonical.attributes;
 %att.datable.attributes;
 %att.global.attributes;
calendar CDATA  #IMPLIED >
<!--doc:(unit definition) contains descriptive information related to a specific unit of measurement. [2.3.9. The Unit Declaration] -->
<!ELEMENT unitDef (%model.labelLike;|(%model.placeNamePart;)?|(conversion)?|(unit)?)+>
<!ATTLIST unitDef xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST unitDef
 %att.global.attributes;
 %att.datable.attributes;
 %att.canonical.attributes;
 %att.typed.attributes;
calendar CDATA  #IMPLIED >
<!--doc:indicates a point in time either relative to other elements in the same timeline tag, or absolutely. [17.4.2. Placing Synchronous Events in Time] -->
<!ELEMENT when  EMPTY>
<!ATTLIST when xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST when
 %att.global.attributes;
absolute %teidata.temporal.w3c;  #IMPLIED
unit %teidata.enumerated;  #IMPLIED
interval %teidata.interval;  #IMPLIED
since %teidata.pointer;  #IMPLIED >
<!--doc:(non-TEI metadata) provides a container element into which metadata in non-TEI formats may be placed. [2.5. Non-TEI Metadata] -->
<!ELEMENT xenoData (#PCDATA)*>
<!ATTLIST xenoData xmlns CDATA "http://www.tei-c.org/ns/1.0">
<!ATTLIST xenoData
 %att.global.attributes;
 %att.declarable.attributes;
 %att.typed.attributes; >
<!-- end elements -->
