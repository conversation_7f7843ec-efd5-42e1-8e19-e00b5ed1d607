@echo off
chcp 65001 >nul
echo.
echo 🖥️ OpenBB 客户端启动菜单
echo ========================
echo.
echo 请选择客户端类型:
echo.
echo 1. 🌐 Web浏览器客户端    - 在浏览器中使用API文档
echo 2. 🐍 Python客户端      - 功能完整的Python客户端
echo 3. 💻 命令行客户端      - 简单的CLI交互界面
echo 4. 📊 测试API连接      - 快速测试服务器连接
echo 5. ❌ 返回主菜单
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" goto web_client
if "%choice%"=="2" goto python_client
if "%choice%"=="3" goto cli_client
if "%choice%"=="4" goto test_api
if "%choice%"=="5" goto main_menu
goto invalid

:web_client
echo.
echo 🌐 启动Web浏览器客户端...
echo 正在打开API文档页面...
start http://127.0.0.1:6900/docs
echo.
echo ✅ 浏览器客户端已启动
echo 📋 您可以在浏览器中:
echo   - 查看所有可用的API端点
echo   - 直接测试API调用
echo   - 查看请求和响应格式
goto end

:python_client
echo.
echo 🐍 启动Python客户端...
call openbb_env_312\Scripts\activate.bat
python openbb_client.py
goto end

:cli_client
echo.
echo 💻 启动命令行客户端...
call openbb_env_312\Scripts\activate.bat
python openbb_cli_client.py
goto end

:test_api
echo.
echo 📊 测试API连接...
call openbb_env_312\Scripts\activate.bat
python -c "import requests; r=requests.get('http://127.0.0.1:6900/health'); print('✅ 服务器正常' if r.status_code==200 else '❌ 服务器异常'); print(f'响应: {r.text}')"
goto end

:main_menu
echo.
echo 🔄 返回主菜单...
call "OpenBB启动菜单.bat"
goto end

:invalid
echo.
echo ❌ 无效选择，请重新运行脚本
pause
goto end

:end
echo.
pause
