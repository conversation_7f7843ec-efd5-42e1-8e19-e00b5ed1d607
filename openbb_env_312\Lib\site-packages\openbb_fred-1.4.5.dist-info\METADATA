Metadata-Version: 2.3
Name: openbb-fred
Version: 1.4.5
Summary: FRED extension for OpenBB
License: AGPL-3.0-only
Author: OpenBB Team
Author-email: <EMAIL>
Requires-Python: >=3.9.21,<3.13
Classifier: License :: OSI Approved :: GNU Affero General Public License v3
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Dist: openbb-core (>=1.4.8,<2.0.0)
Description-Content-Type: text/markdown

# OpenBB FRED Provider

This extension integrates the [FRED](https://fred.stlouisfed.org/docs/api/fred/) data provider into the OpenBB Platform.

## Installation

To install the extension:

```bash
pip install openbb-fred
```

Documentation available [here](https://docs.openbb.co/platform/developer_guide/contributing).

