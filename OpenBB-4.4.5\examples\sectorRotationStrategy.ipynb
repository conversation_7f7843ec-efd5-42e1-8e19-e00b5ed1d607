{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["# **Sector Rotation Strategy Analysis with OpenBB**\n", "\n", "Sector rotation involves shifting investments across different sectors in the stock market, based on economic cycles or market performance expectations. This strategy seeks to maximize returns by focusing on sectors that are expected to perform better in the current market environment while reducing exposure to underperforming sectors.\n", "\n", "Author:<br>\n", "[<PERSON><PERSON><PERSON>](https://github.com/SanchitMahajan236)\n", "\n", "[![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/drive/1LpAMLrbOB0YxWxoZfA5AvL2bTCoUdP6_?usp=sharing)"], "metadata": {"id": "K_fd_9baXaH9"}}, {"cell_type": "code", "source": ["!pip install openbb -q"], "metadata": {"id": "9SiXPtRwW_lo"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from openbb import obb"], "metadata": {"id": "J7B1R7s10Bsa"}, "execution_count": 2, "outputs": []}, {"cell_type": "code", "source": ["sector_etfs = ['XLF', 'XLE', 'XLK', 'XLY', 'XLI', 'XLU', 'XLV']\n", "\n", "start_date = '2015-01-01'\n", "etf_dataframes = []\n", "\n", "for etf in sector_etfs:\n", "    try:\n", "        data = obb.etf.historical(\n", "            symbol=etf,\n", "            start_date=start_date,\n", "            provider=\"yfinance\"\n", "        ).to_df()\n", "        data['Symbol'] = etf\n", "        etf_dataframes.append(data)\n", "    except Exception as e:\n", "        print(f\"Failed to fetch data for {etf}: {str(e)}\")\n", "\n", "combined_etf_data = pd.concat(etf_dataframes)\n", "combined_etf_data = combined_etf_data.reset_index()\n", "\n", "combined_etf_data.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "MrRw8lT_zD11", "outputId": "74ca09b2-61d4-46eb-d165-1bb896287bfe"}, "execution_count": 18, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["         date       open       high        low      close    volume  \\\n", "0  2015-01-02  20.194963  20.227457  19.935013  20.089357  40511471   \n", "1  2015-01-05  19.959383  20.000000  19.618196  19.666937  50770502   \n", "2  2015-01-06  19.666937  19.731924  19.277012  19.366369  57454463   \n", "3  2015-01-07  19.528837  19.618196  19.415110  19.569456  36287049   \n", "4  2015-01-08  19.796913  19.910643  19.756296  19.861900  37995923   \n", "\n", "   split_ratio  dividend  capital_gains Symbol  \n", "0          0.0       0.0            0.0    XLF  \n", "1          0.0       0.0            0.0    XLF  \n", "2          0.0       0.0            0.0    XLF  \n", "3          0.0       0.0            0.0    XLF  \n", "4          0.0       0.0            0.0    XLF  "], "text/html": ["\n", "  <div id=\"df-380ed213-4096-4487-8d23-21c3942664ae\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>split_ratio</th>\n", "      <th>dividend</th>\n", "      <th>capital_gains</th>\n", "      <th>Symbol</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2015-01-02</td>\n", "      <td>20.194963</td>\n", "      <td>20.227457</td>\n", "      <td>19.935013</td>\n", "      <td>20.089357</td>\n", "      <td>40511471</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>XLF</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2015-01-05</td>\n", "      <td>19.959383</td>\n", "      <td>20.000000</td>\n", "      <td>19.618196</td>\n", "      <td>19.666937</td>\n", "      <td>50770502</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>XLF</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2015-01-06</td>\n", "      <td>19.666937</td>\n", "      <td>19.731924</td>\n", "      <td>19.277012</td>\n", "      <td>19.366369</td>\n", "      <td>57454463</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>XLF</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2015-01-07</td>\n", "      <td>19.528837</td>\n", "      <td>19.618196</td>\n", "      <td>19.415110</td>\n", "      <td>19.569456</td>\n", "      <td>36287049</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>XLF</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2015-01-08</td>\n", "      <td>19.796913</td>\n", "      <td>19.910643</td>\n", "      <td>19.756296</td>\n", "      <td>19.861900</td>\n", "      <td>37995923</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>XLF</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-380ed213-4096-4487-8d23-21c3942664ae')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-380ed213-4096-4487-8d23-21c3942664ae button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-380ed213-4096-4487-8d23-21c3942664ae');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-297a309a-23c6-4067-af11-892c789c6253\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-297a309a-23c6-4067-af11-892c789c6253')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-297a309a-23c6-4067-af11-892c789c6253 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "combined_etf_data", "summary": "{\n  \"name\": \"combined_etf_data\",\n  \"rows\": 17276,\n  \"fields\": [\n    {\n      \"column\": \"date\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": \"2015-01-02\",\n        \"max\": \"2024-10-22\",\n        \"num_unique_values\": 2468,\n        \"samples\": [\n          \"2021-10-21\",\n          \"2020-10-22\",\n          \"2021-10-05\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"open\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 42.24800514875072,\n        \"min\": 16.035743713378906,\n        \"max\": 238.0399932861328,\n        \"num_unique_values\": 9625,\n        \"samples\": [\n          42.88999938964844,\n          34.220001220703125,\n          65.73999786376953\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"high\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 42.55051741742228,\n        \"min\": 16.149471282958984,\n        \"max\": 238.13999938964844,\n        \"num_unique_values\": 9631,\n        \"samples\": [\n          102.91999816894531,\n          114.58999633789062,\n          208.8699951171875\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"low\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 41.914933532558486,\n        \"min\": 15.044678688049316,\n        \"max\": 234.57000732421875,\n        \"num_unique_values\": 9646,\n        \"samples\": [\n          73.04000091552734,\n          92.18000030517578,\n          60.400001525878906\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"close\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 42.25050625328991,\n        \"min\": 15.970754623413086,\n        \"max\": 237.67999267578125,\n        \"num_unique_values\": 9683,\n        \"samples\": [\n          84.13999938964844,\n          67.6500015258789,\n          129.8800048828125\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"volume\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 19683203,\n        \"min\": 972084,\n        \"max\": 268936600,\n        \"num_unique_values\": 16739,\n        \"samples\": [\n          20768200,\n          11922500,\n          9929200\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"split_ratio\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.009365618328134425,\n        \"min\": 0.0,\n        \"max\": 1.231,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          1.231,\n          0.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"dividend\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.05190708000326731,\n        \"min\": 0.0,\n        \"max\": 1.791,\n        \"num_unique_values\": 217,\n        \"samples\": [\n          0.481,\n          0.523\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"capital_gains\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.0,\n        \"min\": 0.0,\n        \"max\": 0.0,\n        \"num_unique_values\": 1,\n        \"samples\": [\n          0.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Symbol\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 7,\n        \"samples\": [\n          \"XLF\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 18}]}, {"cell_type": "code", "source": ["combined_etf_data['date'] = pd.to_datetime(combined_etf_data['date'])\n", "combined_etf_data = combined_etf_data[['date', 'close', 'Symbol']]\n", "\n", "pivoted_data = combined_etf_data.pivot_table(index='date', columns='Symbol', values='close')\n", "\n", "pivoted_data.ffill()\n", "\n", "pivoted_data.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 237}, "id": "GmOZPwENehus", "outputId": "d2d41ff6-2916-4c0e-8af3-ec199c2107b5"}, "execution_count": 20, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Symbol            XLE        XLF        XLI        XLK        XLU        XLV  \\\n", "date                                                                           \n", "2015-01-02  79.529999  20.089357  56.509998  41.270000  47.439999  68.629997   \n", "2015-01-05  76.239998  19.666937  55.189999  40.639999  46.860001  68.279999   \n", "2015-01-06  75.120003  19.366369  54.509998  40.150002  46.889999  68.050003   \n", "2015-01-07  75.279999  19.569456  54.919998  40.490002  47.349998  69.650002   \n", "2015-01-08  76.970001  19.861900  56.020000  41.380001  47.680000  70.839996   \n", "\n", "Symbol            XLY  \n", "date                   \n", "2015-01-02  71.629997  \n", "2015-01-05  70.260002  \n", "2015-01-06  69.559998  \n", "2015-01-07  70.660004  \n", "2015-01-08  71.720001  "], "text/html": ["\n", "  <div id=\"df-e6326fe4-83ff-42a5-ba53-4ec3db3ea5be\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Symbol</th>\n", "      <th>XLE</th>\n", "      <th>XLF</th>\n", "      <th>XLI</th>\n", "      <th>XLK</th>\n", "      <th>XLU</th>\n", "      <th>XLV</th>\n", "      <th>XLY</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2015-01-02</th>\n", "      <td>79.529999</td>\n", "      <td>20.089357</td>\n", "      <td>56.509998</td>\n", "      <td>41.270000</td>\n", "      <td>47.439999</td>\n", "      <td>68.629997</td>\n", "      <td>71.629997</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-05</th>\n", "      <td>76.239998</td>\n", "      <td>19.666937</td>\n", "      <td>55.189999</td>\n", "      <td>40.639999</td>\n", "      <td>46.860001</td>\n", "      <td>68.279999</td>\n", "      <td>70.260002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-06</th>\n", "      <td>75.120003</td>\n", "      <td>19.366369</td>\n", "      <td>54.509998</td>\n", "      <td>40.150002</td>\n", "      <td>46.889999</td>\n", "      <td>68.050003</td>\n", "      <td>69.559998</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-07</th>\n", "      <td>75.279999</td>\n", "      <td>19.569456</td>\n", "      <td>54.919998</td>\n", "      <td>40.490002</td>\n", "      <td>47.349998</td>\n", "      <td>69.650002</td>\n", "      <td>70.660004</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-08</th>\n", "      <td>76.970001</td>\n", "      <td>19.861900</td>\n", "      <td>56.020000</td>\n", "      <td>41.380001</td>\n", "      <td>47.680000</td>\n", "      <td>70.839996</td>\n", "      <td>71.720001</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-e6326fe4-83ff-42a5-ba53-4ec3db3ea5be')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-e6326fe4-83ff-42a5-ba53-4ec3db3ea5be button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-e6326fe4-83ff-42a5-ba53-4ec3db3ea5be');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-a01e26ba-2385-4e31-b306-4d715a27a689\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-a01e26ba-2385-4e31-b306-4d715a27a689')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-a01e26ba-2385-4e31-b306-4d715a27a689 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "pivoted_data", "summary": "{\n  \"name\": \"pivoted_data\",\n  \"rows\": 2468,\n  \"fields\": [\n    {\n      \"column\": \"date\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": \"2015-01-02 00:00:00\",\n        \"max\": \"2024-10-22 00:00:00\",\n        \"num_unique_values\": 2468,\n        \"samples\": [\n          \"2021-10-21 00:00:00\",\n          \"2020-10-22 00:00:00\",\n          \"2021-10-05 00:00:00\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"XLE\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 15.361481706892656,\n        \"min\": 23.56999969482422,\n        \"max\": 98.08000183105469,\n        \"num_unique_values\": 1960,\n        \"samples\": [\n          37.0,\n          65.43000030517578,\n          76.95999908447266\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"XLF\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 7.219406318667623,\n        \"min\": 15.970754623413086,\n        \"max\": 47.619998931884766,\n        \"num_unique_values\": 1570,\n        \"samples\": [\n          41.279998779296875,\n          37.20000076293945,\n          24.100000381469727\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"XLI\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 21.154135913625,\n        \"min\": 48.0099983215332,\n        \"max\": 139.27000427246094,\n        \"num_unique_values\": 1987,\n        \"samples\": [\n          50.22999954223633,\n          86.77999877929688,\n          79.5999984741211\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"XLK\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 53.9532902530375,\n        \"min\": 37.70000076293945,\n        \"max\": 237.67999267578125,\n        \"num_unique_values\": 2181,\n        \"samples\": [\n          46.540000915527344,\n          55.77000045776367,\n          100.69999694824219\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"XLU\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 9.276947418886984,\n        \"min\": 40.959999084472656,\n        \"max\": 82.20999908447266,\n        \"num_unique_values\": 1692,\n        \"samples\": [\n          62.83000183105469,\n          69.47000122070312,\n          65.55999755859375\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"XLV\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 26.535611953791335,\n        \"min\": 63.52000045776367,\n        \"max\": 157.24000549316406,\n        \"num_unique_values\": 2032,\n        \"samples\": [\n          84.93000030517578,\n          100.87999725341797,\n          83.66999816894531\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"XLY\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 39.463410347019355,\n        \"min\": 68.52999877929688,\n        \"max\": 211.4199981689453,\n        \"num_unique_values\": 2161,\n        \"samples\": [\n          117.79000091552734,\n          164.85000610351562,\n          123.05999755859375\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 20}]}, {"cell_type": "code", "source": ["def sector_rotation_strategy(etf_data, lookback_period=3, top_n=3):\n", "    \"\"\"\n", "    Implements a simple sector rotation strategy that invests in the top 'n' sector ETFs based on\n", "    past performance over a lookback period.\n", "\n", "    Parameters:\n", "    etf_data (DataFrame): ETF performance data\n", "    lookback_period (int): Number of months to look back for performance evaluation\n", "    top_n (int): Number of top sector ETFs to invest in\n", "\n", "    Returns:\n", "    DataFrame: Portfolio returns based on the sector rotation strategy\n", "    \"\"\"\n", "    monthly_returns = etf_data.resample('ME').last().pct_change()\n", "\n", "    portfolio_returns = pd.DataFrame(index=monthly_returns.index, columns=['Portfolio Return'])\n", "\n", "    for date in monthly_returns.index[lookback_period:]:\n", "        past_returns = monthly_returns.loc[date - pd.DateOffset(months=lookback_period):date].mean()\n", "        top_etfs = past_returns.nlargest(top_n).index\n", "\n", "        next_month_date = date + pd.DateOffset(months=1)\n", "\n", "        if next_month_date in monthly_returns.index:\n", "            next_month_return = monthly_returns.loc[next_month_date, top_etfs].mean()\n", "            portfolio_returns.loc[next_month_date, 'Portfolio Return'] = next_month_return\n", "\n", "    return portfolio_returns\n", "\n", "portfolio_returns = sector_rotation_strategy(pivoted_data, lookback_period=3, top_n=3)\n", "portfolio_returns.dropna(inplace=True)\n", "\n", "portfolio_returns.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 237}, "id": "otjBJjpYQPg4", "outputId": "d6e5a2fa-c6ad-4161-a91d-6e996c82a88c"}, "execution_count": 21, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["           Portfolio Return\n", "date                       \n", "2015-06-30        -0.016801\n", "2015-08-31        -0.071857\n", "2015-09-30          -0.0343\n", "2015-11-30        -0.005501\n", "2016-01-31        -0.048637"], "text/html": ["\n", "  <div id=\"df-5c2be4d3-7d68-4ca7-8276-0096841bdcc6\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Portfolio Return</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2015-06-30</th>\n", "      <td>-0.016801</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-08-31</th>\n", "      <td>-0.071857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-09-30</th>\n", "      <td>-0.0343</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-11-30</th>\n", "      <td>-0.005501</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2016-01-31</th>\n", "      <td>-0.048637</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-5c2be4d3-7d68-4ca7-8276-0096841bdcc6')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-5c2be4d3-7d68-4ca7-8276-0096841bdcc6 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-5c2be4d3-7d68-4ca7-8276-0096841bdcc6');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-fa33114f-4a7d-4444-92bf-08702c2844ae\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-fa33114f-4a7d-4444-92bf-08702c2844ae')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-fa33114f-4a7d-4444-92bf-08702c2844ae button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "portfolio_returns", "summary": "{\n  \"name\": \"portfolio_returns\",\n  \"rows\": 66,\n  \"fields\": [\n    {\n      \"column\": \"date\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": \"2015-06-30 00:00:00\",\n        \"max\": \"2024-09-30 00:00:00\",\n        \"num_unique_values\": 66,\n        \"samples\": [\n          \"2023-02-28 00:00:00\",\n          \"2024-04-30 00:00:00\",\n          \"2015-06-30 00:00:00\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Portfolio Return\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": -0.11089885315500358,\n        \"max\": 0.13650741296126326,\n        \"num_unique_values\": 66,\n        \"samples\": [\n          -0.03366778026314865,\n          -0.044884051999188435,\n          -0.0168010591099631\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 21}]}, {"cell_type": "code", "source": ["portfolio_returns['Cumulative Return'] = (1 + portfolio_returns['Portfolio Return']).cumprod()\n", "\n", "pivoted_data['Market Average'] = pivoted_data.mean(axis=1)\n", "market_returns = pivoted_data['Market Average'].resample('ME').last().pct_change()\n", "market_cumulative_return = (1 + market_returns).cumprod()\n", "\n", "plt.figure(figsize=(12, 7))\n", "plt.plot(portfolio_returns.index, portfolio_returns['Cumulative Return'], label='Sector Rotation Strategy', color='green')\n", "plt.plot(market_cumulative_return.index, market_cumulative_return, label='Market Average', color='blue')\n", "\n", "plt.title('Sector Rotation Strategy vs Market Average', fontsize=16, fontweight='bold')\n", "plt.xlabel('Date', fontsize=12)\n", "plt.ylabel('Cumulative Return', fontsize=12)\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 647}, "id": "r7GFlqfYQS2Z", "outputId": "207db95f-cc85-4c53-b2a4-4754561cd108"}, "execution_count": 22, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1200x700 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["final_strategy_return = portfolio_returns['Cumulative Return'].iloc[-1]\n", "\n", "final_market_return = market_cumulative_return.iloc[-1]\n", "\n", "print(f\"Final cumulative return of sector rotation strategy: {final_strategy_return:.2f}\")\n", "print(f\"Final cumulative return of market average: {final_market_return:.2f}\")\n", "\n", "strategy_daily_returns = portfolio_returns['Portfolio Return'].dropna()\n", "sharpe_ratio = (strategy_daily_returns.mean() / strategy_daily_returns.std()) * np.sqrt(12)\n", "print(f\"<PERSON> of the sector rotation strategy: {sharpe_ratio:.2f}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fyEmebirQVsc", "outputId": "40355f7c-145c-44e3-a532-3e45b467301d"}, "execution_count": 23, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Final cumulative return of sector rotation strategy: 1.49\n", "Final cumulative return of market average: 2.49\n", "<PERSON> of the sector rotation strategy: 0.54\n"]}]}]}