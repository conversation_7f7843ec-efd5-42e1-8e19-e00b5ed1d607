#!/usr/bin/env python3
"""
OpenBB API状态检查工具
检查所有API服务器的运行状态和功能
"""

import requests
import json
from datetime import datetime
import time


class APIStatusChecker:
    """API状态检查器"""
    
    def __init__(self):
        self.apis = {
            "美股API": {
                "url": "http://127.0.0.1:6900",
                "health": "/health",
                "test_endpoints": [
                    "/api/v1/equity/price/quote?symbol=AAPL"
                ]
            },
            "A股API": {
                "url": "http://127.0.0.1:6901", 
                "health": "/health",
                "test_endpoints": [
                    "/api/v1/astock/equity/price/quote?symbol=000001",
                    "/api/v1/astock/providers"
                ]
            },
            "免费API": {
                "url": "http://127.0.0.1:6902",
                "health": "/health", 
                "test_endpoints": [
                    "/api/v1/equity/price/quote?symbol=AAPL&provider=yahoo",
                    "/"
                ]
            },
            "Streamlit客户端": {
                "url": "http://localhost:8502",
                "health": "/",
                "test_endpoints": []
            }
        }
    
    def check_all_apis(self):
        """检查所有API状态"""
        print("🔍 OpenBB API状态检查")
        print("=" * 60)
        print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        results = {}
        
        for name, config in self.apis.items():
            print(f"📊 检查 {name}")
            print("-" * 40)
            
            result = self.check_single_api(name, config)
            results[name] = result
            
            # 显示结果
            if result["online"]:
                print(f"✅ 状态: 在线")
                print(f"⏱️ 响应时间: {result['response_time']:.2f}ms")
                
                if result["health_check"]:
                    print(f"💚 健康检查: 通过")
                else:
                    print(f"⚠️ 健康检查: 失败")
                
                # 显示端点测试结果
                if result["endpoint_tests"]:
                    passed = sum(1 for test in result["endpoint_tests"] if test["success"])
                    total = len(result["endpoint_tests"])
                    print(f"🧪 端点测试: {passed}/{total} 通过")
                    
                    for test in result["endpoint_tests"]:
                        status = "✅" if test["success"] else "❌"
                        print(f"   {status} {test['endpoint']}")
                        if not test["success"]:
                            print(f"      错误: {test['error']}")
            else:
                print(f"❌ 状态: 离线")
                print(f"🔴 错误: {result['error']}")
            
            print()
        
        # 生成总结
        self.print_summary(results)
        
        return results
    
    def check_single_api(self, name, config):
        """检查单个API"""
        result = {
            "online": False,
            "response_time": 0,
            "health_check": False,
            "endpoint_tests": [],
            "error": None
        }
        
        try:
            # 基础连接测试
            start_time = time.time()
            response = requests.get(f"{config['url']}{config['health']}", timeout=5)
            end_time = time.time()
            
            result["response_time"] = (end_time - start_time) * 1000
            
            if response.status_code == 200:
                result["online"] = True
                result["health_check"] = True
                
                # 测试具体端点
                for endpoint in config["test_endpoints"]:
                    test_result = self.test_endpoint(config["url"], endpoint)
                    result["endpoint_tests"].append(test_result)
            else:
                result["error"] = f"HTTP {response.status_code}"
                
        except requests.exceptions.ConnectionError:
            result["error"] = "连接被拒绝"
        except requests.exceptions.Timeout:
            result["error"] = "请求超时"
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def test_endpoint(self, base_url, endpoint):
        """测试具体端点"""
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            
            return {
                "endpoint": endpoint,
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "error": None if response.status_code == 200 else f"HTTP {response.status_code}"
            }
            
        except Exception as e:
            return {
                "endpoint": endpoint,
                "success": False,
                "status_code": None,
                "error": str(e)
            }
    
    def print_summary(self, results):
        """打印总结"""
        print("📋 状态总结")
        print("=" * 60)
        
        online_count = sum(1 for result in results.values() if result["online"])
        total_count = len(results)
        
        print(f"🌐 在线服务: {online_count}/{total_count}")
        
        if online_count == total_count:
            print("🎉 所有服务都在线运行！")
        elif online_count > 0:
            print("⚠️ 部分服务离线，请检查相关服务器")
        else:
            print("🔴 所有服务都离线，请启动服务器")
        
        print()
        print("🚀 服务地址:")
        for name, config in self.apis.items():
            status = "✅" if results[name]["online"] else "❌"
            print(f"   {status} {name}: {config['url']}")
        
        print()
        print("📖 API文档:")
        for name, config in self.apis.items():
            if results[name]["online"] and name != "Streamlit客户端":
                print(f"   📚 {name}: {config['url']}/docs")
    
    def get_api_info(self):
        """获取API详细信息"""
        print("📊 API详细信息")
        print("=" * 60)
        
        for name, config in self.apis.items():
            if name == "Streamlit客户端":
                continue
                
            print(f"\n🔗 {name}")
            try:
                response = requests.get(f"{config['url']}/", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    
                    print(f"   版本: {data.get('version', '未知')}")
                    print(f"   描述: {data.get('description', data.get('message', '无描述'))}")
                    
                    if 'endpoints' in data:
                        print(f"   端点数量: {len(data['endpoints'])}")
                    
                    if 'providers' in data:
                        print(f"   数据源: {', '.join(data['providers'])}")
                        
            except Exception as e:
                print(f"   ❌ 无法获取信息: {e}")


def main():
    """主函数"""
    checker = APIStatusChecker()
    
    print("🔍 OpenBB API生态系统状态检查")
    print("=" * 60)
    print()
    
    # 检查所有API状态
    results = checker.check_all_apis()
    
    # 获取API详细信息
    checker.get_api_info()
    
    print("\n" + "=" * 60)
    print("✅ 检查完成！")
    
    # 如果有离线服务，提供启动建议
    offline_services = [name for name, result in results.items() if not result["online"]]
    
    if offline_services:
        print(f"\n💡 启动离线服务的建议:")
        for service in offline_services:
            if service == "美股API":
                print("   - 运行: 启动API服务器.bat")
            elif service == "A股API":
                print("   - 运行: 启动A股API服务器.bat")
            elif service == "免费API":
                print("   - 运行: 启动免费API服务器.bat")
            elif service == "Streamlit客户端":
                print("   - 运行: 启动客户端.bat")


if __name__ == "__main__":
    main()
