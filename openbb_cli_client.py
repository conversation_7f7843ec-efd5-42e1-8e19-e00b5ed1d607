#!/usr/bin/env python3
"""
OpenBB API 命令行客户端
简单的命令行界面连接OpenBB API服务器
"""

import requests
import json
import sys
from datetime import datetime, timedelta


class OpenBBCLIClient:
    """OpenBB命令行客户端"""
    
    def __init__(self, base_url="http://127.0.0.1:6900"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
    def request(self, endpoint, params=None):
        """发送API请求"""
        try:
            url = f"{self.base_url}{endpoint}"
            response = self.session.get(url, params=params)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"错误: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"请求失败: {e}")
            return None
    
    def show_help(self):
        """显示帮助信息"""
        print("""
🚀 OpenBB API 命令行客户端

可用命令:
  stock <symbol>              - 获取股票历史数据
  quote <symbol>              - 获取股票实时报价
  news [keyword]              - 获取新闻 (可选关键词)
  crypto <symbol>             - 获取加密货币数据
  gdp [country]               - 获取GDP数据 (默认US)
  health                      - 检查服务器状态
  help                        - 显示此帮助
  exit                        - 退出客户端

示例:
  stock AAPL
  quote MSFT
  news Apple
  crypto BTC-USD
  gdp US
        """)
    
    def cmd_stock(self, symbol):
        """获取股票数据"""
        params = {
            "symbol": symbol.upper(),
            "provider": "yfinance"
        }
        
        data = self.request("/api/v1/equity/price/historical", params)
        
        if data and "results" in data:
            results = data["results"]
            if results:
                latest = results[-1] if isinstance(results, list) else results
                print(f"\n📈 {symbol.upper()} 股票数据:")
                print(f"日期: {latest.get('date', 'N/A')}")
                print(f"开盘: ${latest.get('open', 'N/A')}")
                print(f"最高: ${latest.get('high', 'N/A')}")
                print(f"最低: ${latest.get('low', 'N/A')}")
                print(f"收盘: ${latest.get('close', 'N/A')}")
                print(f"成交量: {latest.get('volume', 'N/A'):,}")
            else:
                print(f"❌ 未找到 {symbol} 的数据")
        else:
            print(f"❌ 获取 {symbol} 数据失败")
    
    def cmd_quote(self, symbol):
        """获取实时报价"""
        params = {
            "symbol": symbol.upper(),
            "provider": "yfinance"
        }
        
        data = self.request("/api/v1/equity/price/quote", params)
        
        if data and "results" in data:
            results = data["results"]
            quote = results[0] if isinstance(results, list) else results
            print(f"\n💰 {symbol.upper()} 实时报价:")
            print(f"价格: ${quote.get('price', 'N/A')}")
            print(f"变化: {quote.get('change', 'N/A')}")
            print(f"变化%: {quote.get('change_percent', 'N/A')}%")
        else:
            print(f"❌ 获取 {symbol} 报价失败")
    
    def cmd_news(self, keyword=""):
        """获取新闻"""
        params = {
            "limit": 5,
            "provider": "benzinga"
        }
        
        if keyword:
            params["query"] = keyword
        
        data = self.request("/api/v1/news/company", params)
        
        if data and "results" in data:
            news_list = data["results"]
            print(f"\n📰 新闻 ({len(news_list)} 条):")
            for i, article in enumerate(news_list[:5], 1):
                print(f"{i}. {article.get('title', 'N/A')}")
                print(f"   时间: {article.get('date', 'N/A')}")
                print()
        else:
            print("❌ 获取新闻失败")
    
    def cmd_crypto(self, symbol):
        """获取加密货币数据"""
        params = {
            "symbol": symbol.upper(),
            "provider": "yfinance"
        }
        
        data = self.request("/api/v1/crypto/price/historical", params)
        
        if data and "results" in data:
            results = data["results"]
            if results:
                latest = results[-1] if isinstance(results, list) else results
                print(f"\n₿ {symbol.upper()} 加密货币数据:")
                print(f"日期: {latest.get('date', 'N/A')}")
                print(f"价格: ${latest.get('close', 'N/A')}")
                print(f"成交量: {latest.get('volume', 'N/A'):,}")
            else:
                print(f"❌ 未找到 {symbol} 的数据")
        else:
            print(f"❌ 获取 {symbol} 数据失败")
    
    def cmd_gdp(self, country="US"):
        """获取GDP数据"""
        params = {
            "country": country.upper(),
            "provider": "fred"
        }
        
        data = self.request("/api/v1/economy/gdp", params)
        
        if data and "results" in data:
            results = data["results"]
            if results:
                latest = results[-1] if isinstance(results, list) else results
                print(f"\n🏛️ {country.upper()} GDP数据:")
                print(f"日期: {latest.get('date', 'N/A')}")
                print(f"GDP: {latest.get('value', 'N/A')}")
            else:
                print(f"❌ 未找到 {country} 的GDP数据")
        else:
            print(f"❌ 获取 {country} GDP数据失败")
    
    def cmd_health(self):
        """检查服务器健康状态"""
        data = self.request("/health")
        if data:
            print("✅ 服务器运行正常")
            print(f"状态: {data}")
        else:
            print("❌ 服务器连接失败")
    
    def run(self):
        """运行客户端"""
        print("🚀 OpenBB API 命令行客户端")
        print("=" * 40)
        print("输入 'help' 查看可用命令")
        print("输入 'exit' 退出客户端")
        print()
        
        # 检查服务器连接
        if self.request("/health"):
            print("✅ 已连接到OpenBB服务器")
        else:
            print("❌ 无法连接到OpenBB服务器")
            print("请确保服务器正在运行: http://127.0.0.1:6900")
            return
        
        while True:
            try:
                command = input("\nOpenBB> ").strip()
                
                if not command:
                    continue
                
                parts = command.split()
                cmd = parts[0].lower()
                args = parts[1:] if len(parts) > 1 else []
                
                if cmd == "exit":
                    print("👋 再见!")
                    break
                elif cmd == "help":
                    self.show_help()
                elif cmd == "stock":
                    if args:
                        self.cmd_stock(args[0])
                    else:
                        print("请提供股票代码: stock AAPL")
                elif cmd == "quote":
                    if args:
                        self.cmd_quote(args[0])
                    else:
                        print("请提供股票代码: quote MSFT")
                elif cmd == "news":
                    keyword = args[0] if args else ""
                    self.cmd_news(keyword)
                elif cmd == "crypto":
                    if args:
                        self.cmd_crypto(args[0])
                    else:
                        print("请提供加密货币代码: crypto BTC-USD")
                elif cmd == "gdp":
                    country = args[0] if args else "US"
                    self.cmd_gdp(country)
                elif cmd == "health":
                    self.cmd_health()
                else:
                    print(f"未知命令: {cmd}")
                    print("输入 'help' 查看可用命令")
                    
            except KeyboardInterrupt:
                print("\n👋 再见!")
                break
            except Exception as e:
                print(f"错误: {e}")


if __name__ == "__main__":
    client = OpenBBCLIClient()
    client.run()
