#!/usr/bin/env python3
"""
测试A股数据获取和显示
"""

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

def test_astock_packages():
    """测试A股数据包"""
    print("🔍 检查A股数据包安装状态")
    print("=" * 40)
    
    packages = ['tushare', 'akshare', 'efinance']
    installed = []
    
    for pkg in packages:
        try:
            __import__(pkg)
            print(f"✅ {pkg} 已安装")
            installed.append(pkg)
        except ImportError:
            print(f"❌ {pkg} 未安装")
    
    return installed

def test_astock_api():
    """测试A股API"""
    print("\n🔍 测试A股API")
    print("=" * 40)
    
    try:
        # 测试健康检查
        response = requests.get("http://127.0.0.1:6901/health", timeout=5)
        print(f"健康检查: {response.status_code}")
        
        # 测试股票报价
        response = requests.get("http://127.0.0.1:6901/api/v1/astock/equity/price/quote?symbol=000001", timeout=10)
        print(f"股票报价: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("返回数据:", data)
        elif response.status_code == 204:
            print("无数据返回 (可能是数据源问题)")
        else:
            print("响应内容:", response.text)
            
    except Exception as e:
        print(f"API测试失败: {e}")

def generate_mock_astock_data():
    """生成模拟A股数据"""
    print("\n🔧 生成模拟A股数据")
    print("=" * 40)
    
    # A股股票信息
    stocks = {
        '000001': {'name': '平安银行', 'price': 12.85, 'sector': '银行'},
        '000002': {'name': '万科A', 'price': 18.42, 'sector': '房地产'},
        '600000': {'name': '浦发银行', 'price': 10.95, 'sector': '银行'},
        '600036': {'name': '招商银行', 'price': 35.68, 'sector': '银行'},
        '600519': {'name': '贵州茅台', 'price': 1685.50, 'sector': '食品饮料'},
        '000858': {'name': '五粮液', 'price': 158.20, 'sector': '食品饮料'},
        '002415': {'name': '海康威视', 'price': 32.45, 'sector': '电子'},
        '300059': {'name': '东方财富', 'price': 15.88, 'sector': '非银金融'},
    }
    
    print("📊 A股模拟数据:")
    for code, info in stocks.items():
        change = random.uniform(-2, 2)
        change_pct = (change / info['price']) * 100
        
        print(f"{code} {info['name']}: ¥{info['price']:.2f} "
              f"({change:+.2f}, {change_pct:+.2f}%) - {info['sector']}")
    
    return stocks

def test_currency_display():
    """测试货币显示"""
    print("\n💰 测试货币显示格式")
    print("=" * 40)
    
    # 测试不同价格格式
    test_prices = [12.85, 158.20, 1685.50, 35.68]
    
    for price in test_prices:
        print(f"原始价格: {price}")
        print(f"人民币格式: ¥{price:.2f}")
        print(f"美元格式: ${price:.2f}")
        print("---")

def main():
    """主函数"""
    print("🧪 A股数据测试")
    print("=" * 50)
    
    # 1. 检查数据包
    installed_packages = test_astock_packages()
    
    # 2. 测试API
    test_astock_api()
    
    # 3. 生成模拟数据
    mock_data = generate_mock_astock_data()
    
    # 4. 测试货币显示
    test_currency_display()
    
    print("\n📋 总结:")
    print(f"- 已安装数据包: {len(installed_packages)}/3")
    print(f"- A股API状态: 运行中")
    print(f"- 模拟数据: 8只股票")
    print(f"- 货币符号: ¥ (人民币)")

if __name__ == "__main__":
    main()
