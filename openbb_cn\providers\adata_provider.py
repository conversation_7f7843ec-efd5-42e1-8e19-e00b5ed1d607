#!/usr/bin/env python3
"""
AData数据提供者
基于开源项目 https://github.com/1nchaos/adata
免费的A股数据接口，无需积分限制
"""

import pandas as pd
from typing import Optional
import logging

logger = logging.getLogger(__name__)


class AdataProvider:
    """AData数据提供者"""
    
    def __init__(self):
        """初始化AData提供者"""
        try:
            import adata
            self.adata = adata
            self.available = True
            logger.info("AData提供者初始化成功")
        except ImportError:
            self.adata = None
            self.available = False
            logger.warning("AData未安装，请运行: pip install adata")
        except Exception as e:
            self.adata = None
            self.available = False
            logger.error(f"AData初始化失败: {e}")
    
    def search(self, query: str, limit: int = 20) -> pd.DataFrame:
        """
        搜索股票
        
        Args:
            query: 搜索关键词（股票代码或名称）
            limit: 返回结果数量限制
            
        Returns:
            包含搜索结果的DataFrame
        """
        if not self.available:
            logger.warning("AData不可用")
            return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])
        
        try:
            logger.info(f"AData搜索: {query}")
            
            # 获取所有股票代码
            all_stocks = self.adata.stock.info.all_code()
            
            if all_stocks.empty:
                logger.warning("AData返回空数据")
                return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])
            
            # 转换列名以匹配我们的格式
            all_stocks = all_stocks.rename(columns={
                'stock_code': 'ts_code',
                'short_name': 'name'
            })
            
            # 添加缺失的列
            all_stocks['area'] = '未知'
            all_stocks['industry'] = '未知'
            all_stocks['list_date'] = '未知'
            
            # 转换股票代码格式
            def convert_code(row):
                code = row['ts_code']
                exchange = row.get('exchange', '')
                
                if '.' not in code:
                    if exchange == 'SH' or code.startswith('6'):
                        return f"{code}.SH"
                    elif exchange == 'SZ' or code.startswith(('0', '3')):
                        return f"{code}.SZ"
                return code
            
            all_stocks['ts_code'] = all_stocks.apply(convert_code, axis=1)
            
            # 搜索逻辑
            query = str(query).strip().upper()
            results = []
            
            for _, stock in all_stocks.iterrows():
                code = str(stock['ts_code']).upper()
                name = str(stock['name']).upper()
                
                # 精确匹配股票代码
                if query in code.replace('.', ''):
                    results.append(stock)
                # 模糊匹配股票名称
                elif query in name:
                    results.append(stock)
            
            result_df = pd.DataFrame(results)
            
            if not result_df.empty:
                result_df = result_df.head(limit)
                logger.info(f"AData搜索到 {len(result_df)} 个结果")
            else:
                logger.warning(f"AData未找到匹配的股票: {query}")
            
            return result_df
            
        except Exception as e:
            logger.error(f"AData搜索失败: {e}")
            return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])
    
    def daily(self, symbol: str, start: Optional[str] = None, end: Optional[str] = None) -> pd.DataFrame:
        """
        获取日线数据
        
        Args:
            symbol: 股票代码 (如: 000001.SZ)
            start: 开始日期 (YYYYMMDD)
            end: 结束日期 (YYYYMMDD)
            
        Returns:
            包含日线数据的DataFrame
        """
        if not self.available:
            logger.warning("AData不可用")
            return pd.DataFrame()
        
        try:
            # 转换股票代码格式 (去掉.SH/.SZ后缀)
            stock_code = symbol.split('.')[0]
            
            logger.info(f"AData获取日线数据: {stock_code}")
            
            # 转换日期格式
            start_date = None
            if start:
                start_date = f"{start[:4]}-{start[4:6]}-{start[6:8]}"
            
            # 获取行情数据
            market_df = self.adata.stock.market.get_market(
                stock_code=stock_code,
                k_type=1,  # 日K线
                start_date=start_date
            )
            
            if market_df.empty:
                logger.warning(f"AData未获取到数据: {stock_code}")
                return pd.DataFrame()
            
            # 转换列名和格式
            market_df = market_df.rename(columns={
                'trade_date': 'trade_date',
                'open': 'open',
                'high': 'high', 
                'low': 'low',
                'close': 'close',
                'volume': 'volume'
            })
            
            # 确保有必要的列
            required_columns = ['trade_date', 'open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in market_df.columns:
                    market_df[col] = 0
            
            # 添加provider标识
            market_df['provider'] = 'adata'
            
            # 转换日期格式
            if 'trade_date' in market_df.columns:
                market_df['trade_date'] = pd.to_datetime(market_df['trade_date']).dt.date
            
            # 选择需要的列
            result_df = market_df[['trade_date', 'open', 'high', 'low', 'close', 'volume', 'provider']]
            
            logger.info(f"AData获取到 {len(result_df)} 条数据")
            return result_df.sort_values('trade_date')
            
        except Exception as e:
            logger.error(f"AData获取日线数据失败: {e}")
            return pd.DataFrame()
    
    def get_current_market(self, symbols: list) -> pd.DataFrame:
        """
        获取实时行情
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            包含实时行情的DataFrame
        """
        if not self.available:
            logger.warning("AData不可用")
            return pd.DataFrame()
        
        try:
            # 转换股票代码格式
            stock_codes = [symbol.split('.')[0] for symbol in symbols]
            
            logger.info(f"AData获取实时行情: {stock_codes}")
            
            # 获取实时行情
            current_df = self.adata.stock.market.list_market_current(stock_codes)
            
            if current_df.empty:
                logger.warning("AData未获取到实时行情")
                return pd.DataFrame()
            
            # 转换格式
            current_df['provider'] = 'adata'
            
            logger.info(f"AData获取到 {len(current_df)} 只股票的实时行情")
            return current_df
            
        except Exception as e:
            logger.error(f"AData获取实时行情失败: {e}")
            return pd.DataFrame()


# 创建全局实例
try:
    adata_provider = AdataProvider()
except Exception as e:
    logger.error(f"AData提供者创建失败: {e}")
    adata_provider = None
