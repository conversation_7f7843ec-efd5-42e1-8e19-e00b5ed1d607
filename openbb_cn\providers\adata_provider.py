#!/usr/bin/env python3
"""
AData数据提供者 - 系统唯一数据源
基于开源项目 https://github.com/1nchaos/adata
免费的A股数据接口，支持全市场5000+只股票
数据来源：同花顺、东方财富、百度股市通、新浪财经、腾讯理财通
"""

import pandas as pd
from typing import Optional, List, Dict, Any
import logging
import time
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class AdataProvider:
    """AData数据提供者 - 系统主要数据源"""

    def __init__(self):
        """初始化AData提供者"""
        try:
            import adata
            self.adata = adata
            self.available = True

            # 缓存机制
            self._stock_cache = None
            self._cache_timestamp = None
            self._cache_duration = 3600  # 1小时缓存

            logger.info("✅ AData提供者初始化成功")
            logger.info("📊 数据源：同花顺、东方财富、百度股市通等")

        except ImportError:
            self.adata = None
            self.available = False
            logger.error("❌ AData未安装，请运行: pip install adata")
            raise RuntimeError("AData未安装，请先安装: pip install adata")
        except Exception as e:
            self.adata = None
            self.available = False
            logger.error(f"❌ AData初始化失败: {e}")
            raise RuntimeError(f"AData初始化失败: {e}")
    
    def _get_all_stocks(self) -> pd.DataFrame:
        """获取所有股票代码（带缓存）"""
        current_time = time.time()

        # 检查缓存是否有效
        if (self._stock_cache is not None and
            self._cache_timestamp is not None and
            (current_time - self._cache_timestamp) < self._cache_duration):
            logger.info("📋 使用缓存的股票数据")
            return self._stock_cache

        try:
            logger.info("📊 从AData获取最新股票列表...")
            all_stocks = self.adata.stock.info.all_code()

            if all_stocks.empty:
                logger.warning("⚠️ AData返回空数据")
                return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])

            # 数据处理和格式转换
            all_stocks = all_stocks.rename(columns={
                'stock_code': 'ts_code',
                'short_name': 'name'
            })

            # 转换股票代码格式
            def convert_code(row):
                code = row['ts_code']
                exchange = row.get('exchange', '')

                if '.' not in code:
                    if exchange == 'SH' or code.startswith('6'):
                        return f"{code}.SH"
                    elif exchange == 'SZ' or code.startswith(('0', '3')):
                        return f"{code}.SZ"
                    elif exchange == 'BJ' or code.startswith(('4', '8')):
                        return f"{code}.BJ"
                return code

            all_stocks['ts_code'] = all_stocks.apply(convert_code, axis=1)

            # 添加行业和地区信息（从股票代码推断）
            def get_market_info(code):
                if code.endswith('.SH'):
                    if code.startswith('6'):
                        return '上海', '主板'
                    elif code.startswith('688'):
                        return '上海', '科创板'
                elif code.endswith('.SZ'):
                    if code.startswith('000'):
                        return '深圳', '主板'
                    elif code.startswith('002'):
                        return '深圳', '中小板'
                    elif code.startswith('300'):
                        return '深圳', '创业板'
                elif code.endswith('.BJ'):
                    return '北京', '新三板'
                return '未知', '未知'

            all_stocks[['area', 'industry']] = all_stocks['ts_code'].apply(
                lambda x: pd.Series(get_market_info(x))
            )

            # 处理上市日期
            all_stocks['list_date'] = all_stocks.get('list_date', '未知')

            # 更新缓存
            self._stock_cache = all_stocks.copy()
            self._cache_timestamp = current_time

            logger.info(f"✅ 获取到 {len(all_stocks)} 只股票")
            return all_stocks

        except Exception as e:
            logger.error(f"❌ 获取股票列表失败: {e}")
            return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])

    def search(self, query: str, limit: int = 50) -> pd.DataFrame:
        """
        搜索股票 - 支持代码和名称模糊搜索，无API限制

        Args:
            query: 搜索关键词（股票代码或名称）
            limit: 返回结果数量限制（默认50，防止返回过多数据）

        Returns:
            包含搜索结果的DataFrame
        """
        if not self.available:
            logger.error("❌ AData不可用")
            return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])

        try:
            logger.info(f"🔍 AData搜索: '{query}'")

            # 获取所有股票数据
            all_stocks = self._get_all_stocks()

            if all_stocks.empty:
                return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])

            # 优化的搜索逻辑
            query = str(query).strip().upper()

            # 分类搜索结果，按相关性排序
            exact_code_matches = []      # 精确代码匹配
            exact_name_matches = []      # 精确名称匹配
            prefix_code_matches = []     # 代码前缀匹配
            prefix_name_matches = []     # 名称前缀匹配
            fuzzy_code_matches = []      # 代码模糊匹配
            fuzzy_name_matches = []      # 名称模糊匹配

            for _, stock in all_stocks.iterrows():
                code = str(stock['ts_code']).upper()
                name = str(stock['name']).upper()

                # 清理代码（去掉交易所后缀）
                clean_code = code.replace('.SH', '').replace('.SZ', '').replace('.BJ', '')

                # 精确匹配（最高优先级）
                if query == clean_code:
                    exact_code_matches.append(stock)
                elif query == name:
                    exact_name_matches.append(stock)
                # 前缀匹配（高优先级）
                elif clean_code.startswith(query):
                    prefix_code_matches.append(stock)
                elif name.startswith(query):
                    prefix_name_matches.append(stock)
                # 模糊匹配（低优先级）
                elif query in clean_code:
                    fuzzy_code_matches.append(stock)
                elif query in name:
                    fuzzy_name_matches.append(stock)

            # 按优先级合并结果
            all_results = (exact_code_matches + exact_name_matches +
                          prefix_code_matches + prefix_name_matches +
                          fuzzy_code_matches + fuzzy_name_matches)

            # 去重（保持顺序）
            seen_codes = set()
            unique_results = []
            for stock in all_results:
                if stock['ts_code'] not in seen_codes:
                    unique_results.append(stock)
                    seen_codes.add(stock['ts_code'])

            result_df = pd.DataFrame(unique_results)

            if not result_df.empty:
                # 应用限制
                result_df = result_df.head(limit)

                # 记录搜索统计
                total_matches = len(unique_results)
                returned_count = len(result_df)

                if total_matches > limit:
                    logger.info(f"✅ 找到 {total_matches} 个匹配结果，返回前 {returned_count} 个")
                else:
                    logger.info(f"✅ 找到 {returned_count} 个匹配结果")
            else:
                logger.warning(f"⚠️ 未找到匹配的股票: '{query}'")

            return result_df

        except Exception as e:
            logger.error(f"❌ AData搜索失败: {e}")
            return pd.DataFrame(columns=["ts_code", "name", "area", "industry", "list_date"])
    
    def daily(self, symbol: str, start: Optional[str] = None, end: Optional[str] = None) -> pd.DataFrame:
        """
        获取日线数据 - 支持前复权K线

        Args:
            symbol: 股票代码 (如: 000001.SZ)
            start: 开始日期 (YYYYMMDD)
            end: 结束日期 (YYYYMMDD)

        Returns:
            包含日线数据的DataFrame
        """
        if not self.available:
            logger.error("❌ AData不可用")
            return pd.DataFrame()

        try:
            # 转换股票代码格式 (去掉.SH/.SZ后缀)
            stock_code = symbol.split('.')[0]

            logger.info(f"📊 AData获取日线数据: {stock_code}")

            # 转换日期格式
            start_date = None
            end_date = None

            if start:
                start_date = f"{start[:4]}-{start[4:6]}-{start[6:8]}"
            else:
                # 默认获取最近2年数据
                start_date = (datetime.now() - timedelta(days=730)).strftime('%Y-%m-%d')

            if end:
                end_date = f"{end[:4]}-{end[4:6]}-{end[6:8]}"

            # 获取行情数据 - 使用前复权
            market_df = self.adata.stock.market.get_market(
                stock_code=stock_code,
                k_type=1,        # 日K线
                start_date=start_date,
                end_date=end_date,
                adjust_type=1    # 前复权
            )

            if market_df.empty:
                logger.warning(f"⚠️ {stock_code} 无历史数据")
                return pd.DataFrame()

            # 数据清洗和格式转换
            market_df = market_df.rename(columns={
                'trade_date': 'trade_date',
                'open': 'open',
                'high': 'high',
                'low': 'low',
                'close': 'close',
                'volume': 'volume',
                'amount': 'amount'
            })

            # 确保数据类型正确
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                if col in market_df.columns:
                    market_df[col] = pd.to_numeric(market_df[col], errors='coerce').fillna(0)

            # 添加provider标识
            market_df['provider'] = 'adata'

            # 转换日期格式
            if 'trade_date' in market_df.columns:
                market_df['trade_date'] = pd.to_datetime(market_df['trade_date']).dt.date

            # 数据验证
            market_df = market_df.dropna(subset=['open', 'high', 'low', 'close'])
            market_df = market_df[market_df['volume'] >= 0]  # 过滤异常数据

            # 选择需要的列
            result_columns = ['trade_date', 'open', 'high', 'low', 'close', 'volume', 'provider']
            result_df = market_df[result_columns]

            logger.info(f"✅ 获取到 {len(result_df)} 条日线数据")
            return result_df.sort_values('trade_date')

        except Exception as e:
            logger.error(f"❌ AData获取日线数据失败: {e}")
            return pd.DataFrame()
    
    def get_current_market(self, symbols: List[str]) -> pd.DataFrame:
        """
        获取实时行情

        Args:
            symbols: 股票代码列表

        Returns:
            包含实时行情的DataFrame
        """
        if not self.available:
            logger.error("❌ AData不可用")
            return pd.DataFrame()

        try:
            # 转换股票代码格式
            stock_codes = [symbol.split('.')[0] for symbol in symbols]

            logger.info(f"⚡ AData获取实时行情: {stock_codes}")

            # 获取实时行情
            current_df = self.adata.stock.market.list_market_current(stock_codes)

            if current_df.empty:
                logger.warning("⚠️ 未获取到实时行情")
                return pd.DataFrame()

            # 数据格式化
            current_df['provider'] = 'adata'

            # 确保数据类型正确
            numeric_columns = ['price', 'change', 'change_pct', 'volume', 'amount']
            for col in numeric_columns:
                if col in current_df.columns:
                    current_df[col] = pd.to_numeric(current_df[col], errors='coerce').fillna(0)

            logger.info(f"✅ 获取到 {len(current_df)} 只股票的实时行情")
            return current_df

        except Exception as e:
            logger.error(f"❌ AData获取实时行情失败: {e}")
            return pd.DataFrame()

    def get_market_minute(self, symbol: str) -> pd.DataFrame:
        """
        获取分时行情数据

        Args:
            symbol: 股票代码

        Returns:
            包含分时数据的DataFrame
        """
        if not self.available:
            logger.error("❌ AData不可用")
            return pd.DataFrame()

        try:
            stock_code = symbol.split('.')[0]
            logger.info(f"📈 AData获取分时数据: {stock_code}")

            # 获取分时数据
            minute_df = self.adata.stock.market.get_market_min(stock_code=stock_code)

            if minute_df.empty:
                logger.warning(f"⚠️ {stock_code} 无分时数据")
                return pd.DataFrame()

            minute_df['provider'] = 'adata'
            logger.info(f"✅ 获取到 {len(minute_df)} 条分时数据")
            return minute_df

        except Exception as e:
            logger.error(f"❌ AData获取分时数据失败: {e}")
            return pd.DataFrame()

    def get_dividend(self, symbol: str) -> pd.DataFrame:
        """
        获取分红信息

        Args:
            symbol: 股票代码

        Returns:
            包含分红信息的DataFrame
        """
        if not self.available:
            logger.error("❌ AData不可用")
            return pd.DataFrame()

        try:
            stock_code = symbol.split('.')[0]
            logger.info(f"💰 AData获取分红信息: {stock_code}")

            # 获取分红数据
            dividend_df = self.adata.stock.market.get_dividend(stock_code=stock_code)

            if dividend_df.empty:
                logger.warning(f"⚠️ {stock_code} 无分红信息")
                return pd.DataFrame()

            dividend_df['provider'] = 'adata'
            logger.info(f"✅ 获取到 {len(dividend_df)} 条分红记录")
            return dividend_df

        except Exception as e:
            logger.error(f"❌ AData获取分红信息失败: {e}")
            return pd.DataFrame()

    def get_financial_data(self, symbol: str) -> pd.DataFrame:
        """
        获取财务数据

        Args:
            symbol: 股票代码

        Returns:
            包含财务数据的DataFrame
        """
        if not self.available:
            logger.error("❌ AData不可用")
            return pd.DataFrame()

        try:
            stock_code = symbol.split('.')[0]
            logger.info(f"📊 AData获取财务数据: {stock_code}")

            # 获取核心财务数据
            finance_df = self.adata.stock.finance.get_core_index(stock_code=stock_code)

            if finance_df.empty:
                logger.warning(f"⚠️ {stock_code} 无财务数据")
                return pd.DataFrame()

            finance_df['provider'] = 'adata'
            logger.info(f"✅ 获取到 {len(finance_df)} 条财务数据")
            return finance_df

        except Exception as e:
            logger.error(f"❌ AData获取财务数据失败: {e}")
            return pd.DataFrame()


# 创建全局实例
try:
    adata_provider = AdataProvider()
    logger.info("🎉 AData提供者已成功创建并设为系统唯一数据源")
except Exception as e:
    logger.error(f"❌ AData提供者创建失败: {e}")
    adata_provider = None
    raise RuntimeError("系统初始化失败：无法创建AData提供者")
