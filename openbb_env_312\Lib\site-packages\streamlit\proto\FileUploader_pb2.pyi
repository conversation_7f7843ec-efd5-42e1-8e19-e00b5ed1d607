"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2024)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""
import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import streamlit.proto.LabelVisibilityMessage_pb2
import sys

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class FileUploader(google.protobuf.message.Message):
    """file_uploader widget"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    LABEL_FIELD_NUMBER: builtins.int
    TYPE_FIELD_NUMBER: builtins.int
    MAX_UPLOAD_SIZE_MB_FIELD_NUMBER: builtins.int
    MULTIPLE_FILES_FIELD_NUMBER: builtins.int
    HELP_FIELD_NUMBER: builtins.int
    FORM_ID_FIELD_NUMBER: builtins.int
    DISABLED_FIELD_NUMBER: builtins.int
    LABEL_VISIBILITY_FIELD_NUMBER: builtins.int
    id: builtins.str
    """The widget id"""
    label: builtins.str
    """Text to be displayed before the widget"""
    @property
    def type(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """Supported types: For example: ["png","jpg","img"]"""
    max_upload_size_mb: builtins.int
    """Max file size allowed by server config"""
    multiple_files: builtins.bool
    """If true, the widget accepts multiple files for upload."""
    help: builtins.str
    form_id: builtins.str
    disabled: builtins.bool
    @property
    def label_visibility(self) -> streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage: ...
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        label: builtins.str = ...,
        type: collections.abc.Iterable[builtins.str] | None = ...,
        max_upload_size_mb: builtins.int = ...,
        multiple_files: builtins.bool = ...,
        help: builtins.str = ...,
        form_id: builtins.str = ...,
        disabled: builtins.bool = ...,
        label_visibility: streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["label_visibility", b"label_visibility"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["disabled", b"disabled", "form_id", b"form_id", "help", b"help", "id", b"id", "label", b"label", "label_visibility", b"label_visibility", "max_upload_size_mb", b"max_upload_size_mb", "multiple_files", b"multiple_files", "type", b"type"]) -> None: ...

global___FileUploader = FileUploader
