{"version": 3, "file": "6491.4ec5e8e76fbff7d9698a.js?v=4ec5e8e76fbff7d9698a", "mappings": ";;;;;;;;;;;;;;AAE8B;AAGA;AAGA;AAgBA;;AAE9B;AACA;AACA,0BAA0B,qEAAM;AAChC,sBAAsB,gBAAgB,KAAK;AAC3C;AACA,GAAG;AACH;AACA,2BAA2B,qEAAM;AACjC,KAAK;AACL,UAAU;AACV,gBAAgB,m0BAAm0B;AACn1B,kBAAkB,ohBAAohB;AACtiB;AACA,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,wBAAwB;AACnD;AACA;AACA;AACA;AACA;AACA,0BAA0B,wBAAwB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,wBAAwB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,wBAAwB;AACrD;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,0BAA0B,qDAAqD,KAAK,QAAQ,gBAAgB,qDAAqD,iBAAiB,wEAAwE,KAAK,0HAA0H,uDAAuD,WAAW,gBAAgB,sDAAsD,KAAK,WAAW,mCAAmC,kCAAkC,KAAK,oIAAoI,IAAI,8KAA8K,IAAI,6JAA6J,IAAI,qJAAqJ,IAAI,qJAAqJ,IAAI,aAAa,IAAI,aAAa,qBAAqB,WAAW,oDAAoD,kGAAkG,mQAAmQ,kCAAkC,uCAAuC,aAAa,IAAI,4IAA4I,qCAAqC,iBAAiB,sBAAsB,aAAa,qBAAqB,iBAAiB,IAAI,iBAAiB,qBAAqB,iBAAiB,wEAAwE,aAAa,IAAI,aAAa,IAAI,0BAA0B,qBAAqB,aAAa,qBAAqB,aAAa,IAAI,0BAA0B,uDAAuD,4IAA4I,sCAAsC,iBAAiB,IAAI,aAAa,IAAI,aAAa;AACjuF,sBAAsB,6DAA6D;AACnF,gCAAgC,qEAAM;AACtC;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,KAAK;AACL,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ,iEAAiE;AACjE;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA,OAAO;AACP;AACA,8BAA8B,qEAAM;AACpC;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA,OAAO;AACP;AACA,iCAAiC,qEAAM;AACvC;AACA;AACA,OAAO;AACP;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,oCAAoC,qEAAM;AAC1C;AACA;AACA;AACA,OAAO;AACP;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,gBAAgB;AAChB;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,OAAO;AACP;AACA,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA,OAAO;AACP;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP,4DAA4D;AAC5D,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,iCAAiC,qEAAM;AACvC;AACA,OAAO;AACP;AACA,sCAAsC,qEAAM;AAC5C;AACA,OAAO;AACP,iBAAiB,0BAA0B;AAC3C,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,0BAA0B,qBAAqB,0NAA0N,eAAe,cAAc,4QAA4Q,8BAA8B,EAAE,sDAAsD,eAAe,EAAE,oQAAoQ;AAC75B,oBAAoB,gBAAgB,mKAAmK,YAAY,kKAAkK,sBAAsB,+JAA+J,iBAAiB,0KAA0K,2BAA2B,uCAAuC,iBAAiB,mCAAmC,iBAAiB,kCAAkC,aAAa,iCAAiC,iBAAiB,iCAAiC,cAAc,uCAAuC,eAAe;AACljC;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA,qEAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qEAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,qFAAsB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAC+B;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA,IAAI,qEAAM;AACV;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,EAAE,GAAG,qBAAqB,IAAI,EAAE,GAAG,gDAAgD;AAC1G;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,EAAE,GAAG,0BAA0B,IAAI,+BAA+B,GAAG,yBAAyB;AACnH;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,oBAAoB,GAAG,GAAG,IAAI,8CAA8C,GAAG,EAAE;AACxG;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,yBAAyB,GAAG,GAAG,IAAI,yBAAyB,GAAG,+BAA+B;AACnH;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,oBAAoB,GAAG,GAAG,IAAI,8CAA8C,GAAG,EAAE;AACxG;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,yBAAyB,GAAG,wFAAwF,IAAI,yBAAyB,GAAG,oHAAoH;AAC7R;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI,qEAAM;AACV;AACA;AACA;AACA;AACA,iBAAiB,wDAAS;AAC1B;AACA;AACA;AACA;AACA;AACA,iBAAiB,wDAAS;AAC1B,IAAI,8DAAG;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACiC;AACjC;AACA;AACA,IAAI,qEAAM;AACV;AACA;AACA;AACA;AACA,iBAAiB,0DAAW;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,0DAAW;AAC5B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qEAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;;AAEN;AAC0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qEAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,mDAAI;AACjB,MAAM;AACN,aAAa,mDAAI;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,eAAe;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qEAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,eAAe;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,yCAAyC,eAAe;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qEAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;;AAEN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,IAAI,qEAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,gDAAgD,oBAAoB;AACpE;AACA,iDAAiD,kCAAkC;AACnF;AACA,iDAAiD,gBAAgB;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,gDAAgD,oBAAoB;AACpE;AACA,iDAAiD,wBAAwB;AACzE;AACA,iDAAiD,gBAAgB;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI,qEAAM;AACV;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,gFAAiB;AACjD,iBAAiB,wEAAS;AAC1B,SAAS,4EAAa;AACtB;AACA,qEAAM;AACN;AACA,iBAAiB,wEAAS;AAC1B,SAAS,4EAAa;AACtB,IAAI,gFAAqB;AACzB;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,qEAAM;AACN;AACA,iBAAiB,wEAAS;AAC1B,SAAS,2EAAY;AACrB;AACA,qEAAM;AACN;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA,qEAAM;AACN;AACA,wBAAwB;AACxB;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA,qEAAM;AACN;AACA,wBAAwB;AACxB;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,UAAU;AAChC,yBAAyB,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA;AACA,sBAAsB,8EAAe;AACrC;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA,qEAAM;AACN;AACA;AACA;AACA,qEAAM;AACN,6BAA6B,qEAAM;AACnC,EAAE,oEAAK;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,aAAa;AACb,aAAa;AACb,iBAAiB;AACjB,iBAAiB;AACjB,mBAAmB;AACnB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA,wBAAwB,OAAO,IAAI,OAAO,WAAW,mBAAmB;AACxE;AACA,EAAE,qEAAM;AACR,EAAE,8DAAG;AACL,cAAc,8EAAgB;AAC9B;AACA;AACA,EAAE,+EAAgB;AAClB,6BAA6B,mBAAmB,EAAE,mBAAmB;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AAGE", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/xychartDiagram-VJFVF3MP.mjs"], "sourcesContent": ["import {\n  computeDimensionOfText\n} from \"./chunk-C3MQ5ANM.mjs\";\nimport {\n  cleanAndMerge\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-7B677QYD.mjs\";\nimport {\n  __name,\n  clear,\n  configureSvgSize,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getDiagramTitle,\n  getThemeVariables,\n  log,\n  sanitizeText,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/xychart/parser/xychart.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 10, 12, 14, 16, 18, 19, 21, 23], $V1 = [2, 6], $V2 = [1, 3], $V3 = [1, 5], $V4 = [1, 6], $V5 = [1, 7], $V6 = [1, 5, 10, 12, 14, 16, 18, 19, 21, 23, 34, 35, 36], $V7 = [1, 25], $V8 = [1, 26], $V9 = [1, 28], $Va = [1, 29], $Vb = [1, 30], $Vc = [1, 31], $Vd = [1, 32], $Ve = [1, 33], $Vf = [1, 34], $Vg = [1, 35], $Vh = [1, 36], $Vi = [1, 37], $Vj = [1, 43], $Vk = [1, 42], $Vl = [1, 47], $Vm = [1, 50], $Vn = [1, 10, 12, 14, 16, 18, 19, 21, 23, 34, 35, 36], $Vo = [1, 10, 12, 14, 16, 18, 19, 21, 23, 24, 26, 27, 28, 34, 35, 36], $Vp = [1, 10, 12, 14, 16, 18, 19, 21, 23, 24, 26, 27, 28, 34, 35, 36, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50], $Vq = [1, 64];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"eol\": 4, \"XYCHART\": 5, \"chartConfig\": 6, \"document\": 7, \"CHART_ORIENTATION\": 8, \"statement\": 9, \"title\": 10, \"text\": 11, \"X_AXIS\": 12, \"parseXAxis\": 13, \"Y_AXIS\": 14, \"parseYAxis\": 15, \"LINE\": 16, \"plotData\": 17, \"BAR\": 18, \"acc_title\": 19, \"acc_title_value\": 20, \"acc_descr\": 21, \"acc_descr_value\": 22, \"acc_descr_multiline_value\": 23, \"SQUARE_BRACES_START\": 24, \"commaSeparatedNumbers\": 25, \"SQUARE_BRACES_END\": 26, \"NUMBER_WITH_DECIMAL\": 27, \"COMMA\": 28, \"xAxisData\": 29, \"bandData\": 30, \"ARROW_DELIMITER\": 31, \"commaSeparatedTexts\": 32, \"yAxisData\": 33, \"NEWLINE\": 34, \"SEMI\": 35, \"EOF\": 36, \"alphaNum\": 37, \"STR\": 38, \"MD_STR\": 39, \"alphaNumToken\": 40, \"AMP\": 41, \"NUM\": 42, \"ALPHA\": 43, \"PLUS\": 44, \"EQUALS\": 45, \"MULT\": 46, \"DOT\": 47, \"BRKT\": 48, \"MINUS\": 49, \"UNDERSCORE\": 50, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 5: \"XYCHART\", 8: \"CHART_ORIENTATION\", 10: \"title\", 12: \"X_AXIS\", 14: \"Y_AXIS\", 16: \"LINE\", 18: \"BAR\", 19: \"acc_title\", 20: \"acc_title_value\", 21: \"acc_descr\", 22: \"acc_descr_value\", 23: \"acc_descr_multiline_value\", 24: \"SQUARE_BRACES_START\", 26: \"SQUARE_BRACES_END\", 27: \"NUMBER_WITH_DECIMAL\", 28: \"COMMA\", 31: \"ARROW_DELIMITER\", 34: \"NEWLINE\", 35: \"SEMI\", 36: \"EOF\", 38: \"STR\", 39: \"MD_STR\", 41: \"AMP\", 42: \"NUM\", 43: \"ALPHA\", 44: \"PLUS\", 45: \"EQUALS\", 46: \"MULT\", 47: \"DOT\", 48: \"BRKT\", 49: \"MINUS\", 50: \"UNDERSCORE\" },\n    productions_: [0, [3, 2], [3, 3], [3, 2], [3, 1], [6, 1], [7, 0], [7, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 3], [9, 2], [9, 3], [9, 2], [9, 2], [9, 1], [17, 3], [25, 3], [25, 1], [13, 1], [13, 2], [13, 1], [29, 1], [29, 3], [30, 3], [32, 3], [32, 1], [15, 1], [15, 2], [15, 1], [33, 3], [4, 1], [4, 1], [4, 1], [11, 1], [11, 1], [11, 1], [37, 1], [37, 2], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 5:\n          yy.setOrientation($$[$0]);\n          break;\n        case 9:\n          yy.setDiagramTitle($$[$0].text.trim());\n          break;\n        case 12:\n          yy.setLineData({ text: \"\", type: \"text\" }, $$[$0]);\n          break;\n        case 13:\n          yy.setLineData($$[$0 - 1], $$[$0]);\n          break;\n        case 14:\n          yy.setBarData({ text: \"\", type: \"text\" }, $$[$0]);\n          break;\n        case 15:\n          yy.setBarData($$[$0 - 1], $$[$0]);\n          break;\n        case 16:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 17:\n        case 18:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 19:\n          this.$ = $$[$0 - 1];\n          break;\n        case 20:\n          this.$ = [Number($$[$0 - 2]), ...$$[$0]];\n          break;\n        case 21:\n          this.$ = [Number($$[$0])];\n          break;\n        case 22:\n          yy.setXAxisTitle($$[$0]);\n          break;\n        case 23:\n          yy.setXAxisTitle($$[$0 - 1]);\n          break;\n        case 24:\n          yy.setXAxisTitle({ type: \"text\", text: \"\" });\n          break;\n        case 25:\n          yy.setXAxisBand($$[$0]);\n          break;\n        case 26:\n          yy.setXAxisRangeData(Number($$[$0 - 2]), Number($$[$0]));\n          break;\n        case 27:\n          this.$ = $$[$0 - 1];\n          break;\n        case 28:\n          this.$ = [$$[$0 - 2], ...$$[$0]];\n          break;\n        case 29:\n          this.$ = [$$[$0]];\n          break;\n        case 30:\n          yy.setYAxisTitle($$[$0]);\n          break;\n        case 31:\n          yy.setYAxisTitle($$[$0 - 1]);\n          break;\n        case 32:\n          yy.setYAxisTitle({ type: \"text\", text: \"\" });\n          break;\n        case 33:\n          yy.setYAxisRangeData(Number($$[$0 - 2]), Number($$[$0]));\n          break;\n        case 37:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 38:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 39:\n          this.$ = { text: $$[$0], type: \"markdown\" };\n          break;\n        case 40:\n          this.$ = $$[$0];\n          break;\n        case 41:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [o($V0, $V1, { 3: 1, 4: 2, 7: 4, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), { 1: [3] }, o($V0, $V1, { 4: 2, 7: 4, 3: 8, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), o($V0, $V1, { 4: 2, 7: 4, 6: 9, 3: 10, 5: $V2, 8: [1, 11], 34: $V3, 35: $V4, 36: $V5 }), { 1: [2, 4], 9: 12, 10: [1, 13], 12: [1, 14], 14: [1, 15], 16: [1, 16], 18: [1, 17], 19: [1, 18], 21: [1, 19], 23: [1, 20] }, o($V6, [2, 34]), o($V6, [2, 35]), o($V6, [2, 36]), { 1: [2, 1] }, o($V0, $V1, { 4: 2, 7: 4, 3: 21, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), { 1: [2, 3] }, o($V6, [2, 5]), o($V0, [2, 7], { 4: 22, 34: $V3, 35: $V4, 36: $V5 }), { 11: 23, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 39, 13: 38, 24: $Vj, 27: $Vk, 29: 40, 30: 41, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 45, 15: 44, 27: $Vl, 33: 46, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 49, 17: 48, 24: $Vm, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 52, 17: 51, 24: $Vm, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 20: [1, 53] }, { 22: [1, 54] }, o($Vn, [2, 18]), { 1: [2, 2] }, o($Vn, [2, 8]), o($Vn, [2, 9]), o($Vo, [2, 37], { 40: 55, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }), o($Vo, [2, 38]), o($Vo, [2, 39]), o($Vp, [2, 40]), o($Vp, [2, 42]), o($Vp, [2, 43]), o($Vp, [2, 44]), o($Vp, [2, 45]), o($Vp, [2, 46]), o($Vp, [2, 47]), o($Vp, [2, 48]), o($Vp, [2, 49]), o($Vp, [2, 50]), o($Vp, [2, 51]), o($Vn, [2, 10]), o($Vn, [2, 22], { 30: 41, 29: 56, 24: $Vj, 27: $Vk }), o($Vn, [2, 24]), o($Vn, [2, 25]), { 31: [1, 57] }, { 11: 59, 32: 58, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, o($Vn, [2, 11]), o($Vn, [2, 30], { 33: 60, 27: $Vl }), o($Vn, [2, 32]), { 31: [1, 61] }, o($Vn, [2, 12]), { 17: 62, 24: $Vm }, { 25: 63, 27: $Vq }, o($Vn, [2, 14]), { 17: 65, 24: $Vm }, o($Vn, [2, 16]), o($Vn, [2, 17]), o($Vp, [2, 41]), o($Vn, [2, 23]), { 27: [1, 66] }, { 26: [1, 67] }, { 26: [2, 29], 28: [1, 68] }, o($Vn, [2, 31]), { 27: [1, 69] }, o($Vn, [2, 13]), { 26: [1, 70] }, { 26: [2, 21], 28: [1, 71] }, o($Vn, [2, 15]), o($Vn, [2, 26]), o($Vn, [2, 27]), { 11: 59, 32: 72, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, o($Vn, [2, 33]), o($Vn, [2, 19]), { 25: 73, 27: $Vq }, { 26: [2, 28] }, { 26: [2, 20] }],\n    defaultActions: { 8: [2, 1], 10: [2, 3], 21: [2, 2], 72: [2, 28], 73: [2, 20] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            this.popState();\n            return 34;\n            break;\n          case 3:\n            this.popState();\n            return 34;\n            break;\n          case 4:\n            return 34;\n            break;\n          case 5:\n            break;\n          case 6:\n            return 10;\n            break;\n          case 7:\n            this.pushState(\"acc_title\");\n            return 19;\n            break;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 9:\n            this.pushState(\"acc_descr\");\n            return 21;\n            break;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 11:\n            this.pushState(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 14:\n            return 5;\n            break;\n          case 15:\n            return 8;\n            break;\n          case 16:\n            this.pushState(\"axis_data\");\n            return \"X_AXIS\";\n            break;\n          case 17:\n            this.pushState(\"axis_data\");\n            return \"Y_AXIS\";\n            break;\n          case 18:\n            this.pushState(\"axis_band_data\");\n            return 24;\n            break;\n          case 19:\n            return 31;\n            break;\n          case 20:\n            this.pushState(\"data\");\n            return 16;\n            break;\n          case 21:\n            this.pushState(\"data\");\n            return 18;\n            break;\n          case 22:\n            this.pushState(\"data_inner\");\n            return 24;\n            break;\n          case 23:\n            return 27;\n            break;\n          case 24:\n            this.popState();\n            return 26;\n            break;\n          case 25:\n            this.popState();\n            break;\n          case 26:\n            this.pushState(\"string\");\n            break;\n          case 27:\n            this.popState();\n            break;\n          case 28:\n            return \"STR\";\n            break;\n          case 29:\n            return 24;\n            break;\n          case 30:\n            return 26;\n            break;\n          case 31:\n            return 43;\n            break;\n          case 32:\n            return \"COLON\";\n            break;\n          case 33:\n            return 44;\n            break;\n          case 34:\n            return 28;\n            break;\n          case 35:\n            return 45;\n            break;\n          case 36:\n            return 46;\n            break;\n          case 37:\n            return 48;\n            break;\n          case 38:\n            return 50;\n            break;\n          case 39:\n            return 47;\n            break;\n          case 40:\n            return 41;\n            break;\n          case 41:\n            return 49;\n            break;\n          case 42:\n            return 42;\n            break;\n          case 43:\n            break;\n          case 44:\n            return 35;\n            break;\n          case 45:\n            return 36;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:(\\r?\\n))/i, /^(?:(\\r?\\n))/i, /^(?:[\\n\\r]+)/i, /^(?:%%[^\\n]*)/i, /^(?:title\\b)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:\\{)/i, /^(?:[^\\}]*)/i, /^(?:xychart-beta\\b)/i, /^(?:(?:vertical|horizontal))/i, /^(?:x-axis\\b)/i, /^(?:y-axis\\b)/i, /^(?:\\[)/i, /^(?:-->)/i, /^(?:line\\b)/i, /^(?:bar\\b)/i, /^(?:\\[)/i, /^(?:[+-]?(?:\\d+(?:\\.\\d+)?|\\.\\d+))/i, /^(?:\\])/i, /^(?:(?:`\\)                                    \\{ this\\.pushState\\(md_string\\); \\}\\n<md_string>\\(\\?:\\(\\?!`\"\\)\\.\\)\\+                  \\{ return MD_STR; \\}\\n<md_string>\\(\\?:`))/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:\\[)/i, /^(?:\\])/i, /^(?:[A-Za-z]+)/i, /^(?::)/i, /^(?:\\+)/i, /^(?:,)/i, /^(?:=)/i, /^(?:\\*)/i, /^(?:#)/i, /^(?:[\\_])/i, /^(?:\\.)/i, /^(?:&)/i, /^(?:-)/i, /^(?:[0-9]+)/i, /^(?:\\s+)/i, /^(?:;)/i, /^(?:$)/i],\n      conditions: { \"data_inner\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"data\": { \"rules\": [0, 1, 3, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 22, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"axis_band_data\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"axis_data\": { \"rules\": [0, 1, 2, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"acc_descr_multiline\": { \"rules\": [12, 13], \"inclusive\": false }, \"acc_descr\": { \"rules\": [10], \"inclusive\": false }, \"acc_title\": { \"rules\": [8], \"inclusive\": false }, \"title\": { \"rules\": [], \"inclusive\": false }, \"md_string\": { \"rules\": [], \"inclusive\": false }, \"string\": { \"rules\": [27, 28], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar xychart_default = parser;\n\n// src/diagrams/xychart/chartBuilder/interfaces.ts\nfunction isBarPlot(data) {\n  return data.type === \"bar\";\n}\n__name(isBarPlot, \"isBarPlot\");\nfunction isBandAxisData(data) {\n  return data.type === \"band\";\n}\n__name(isBandAxisData, \"isBandAxisData\");\nfunction isLinearAxisData(data) {\n  return data.type === \"linear\";\n}\n__name(isLinearAxisData, \"isLinearAxisData\");\n\n// src/diagrams/xychart/chartBuilder/textDimensionCalculator.ts\nvar TextDimensionCalculatorWithFont = class {\n  constructor(parentGroup) {\n    this.parentGroup = parentGroup;\n  }\n  static {\n    __name(this, \"TextDimensionCalculatorWithFont\");\n  }\n  getMaxDimension(texts, fontSize) {\n    if (!this.parentGroup) {\n      return {\n        width: texts.reduce((acc, cur) => Math.max(cur.length, acc), 0) * fontSize,\n        height: fontSize\n      };\n    }\n    const dimension = {\n      width: 0,\n      height: 0\n    };\n    const elem = this.parentGroup.append(\"g\").attr(\"visibility\", \"hidden\").attr(\"font-size\", fontSize);\n    for (const t of texts) {\n      const bbox = computeDimensionOfText(elem, 1, t);\n      const width = bbox ? bbox.width : t.length * fontSize;\n      const height = bbox ? bbox.height : fontSize;\n      dimension.width = Math.max(dimension.width, width);\n      dimension.height = Math.max(dimension.height, height);\n    }\n    elem.remove();\n    return dimension;\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts\nimport { scaleBand } from \"d3\";\n\n// src/diagrams/xychart/chartBuilder/components/axis/baseAxis.ts\nvar BAR_WIDTH_TO_TICK_WIDTH_RATIO = 0.7;\nvar MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL = 0.2;\nvar BaseAxis = class {\n  constructor(axisConfig, title, textDimensionCalculator, axisThemeConfig) {\n    this.axisConfig = axisConfig;\n    this.title = title;\n    this.textDimensionCalculator = textDimensionCalculator;\n    this.axisThemeConfig = axisThemeConfig;\n    this.boundingRect = { x: 0, y: 0, width: 0, height: 0 };\n    this.axisPosition = \"left\";\n    this.showTitle = false;\n    this.showLabel = false;\n    this.showTick = false;\n    this.showAxisLine = false;\n    this.outerPadding = 0;\n    this.titleTextHeight = 0;\n    this.labelTextHeight = 0;\n    this.range = [0, 10];\n    this.boundingRect = { x: 0, y: 0, width: 0, height: 0 };\n    this.axisPosition = \"left\";\n  }\n  static {\n    __name(this, \"BaseAxis\");\n  }\n  setRange(range) {\n    this.range = range;\n    if (this.axisPosition === \"left\" || this.axisPosition === \"right\") {\n      this.boundingRect.height = range[1] - range[0];\n    } else {\n      this.boundingRect.width = range[1] - range[0];\n    }\n    this.recalculateScale();\n  }\n  getRange() {\n    return [this.range[0] + this.outerPadding, this.range[1] - this.outerPadding];\n  }\n  setAxisPosition(axisPosition) {\n    this.axisPosition = axisPosition;\n    this.setRange(this.range);\n  }\n  getTickDistance() {\n    const range = this.getRange();\n    return Math.abs(range[0] - range[1]) / this.getTickValues().length;\n  }\n  getAxisOuterPadding() {\n    return this.outerPadding;\n  }\n  getLabelDimension() {\n    return this.textDimensionCalculator.getMaxDimension(\n      this.getTickValues().map((tick) => tick.toString()),\n      this.axisConfig.labelFontSize\n    );\n  }\n  recalculateOuterPaddingToDrawBar() {\n    if (BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() > this.outerPadding * 2) {\n      this.outerPadding = Math.floor(BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() / 2);\n    }\n    this.recalculateScale();\n  }\n  calculateSpaceIfDrawnHorizontally(availableSpace) {\n    let availableHeight = availableSpace.height;\n    if (this.axisConfig.showAxisLine && availableHeight > this.axisConfig.axisLineWidth) {\n      availableHeight -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.width;\n      this.outerPadding = Math.min(spaceRequired.width / 2, maxPadding);\n      const heightRequired = spaceRequired.height + this.axisConfig.labelPadding * 2;\n      this.labelTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableHeight >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableHeight -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension(\n        [this.title],\n        this.axisConfig.titleFontSize\n      );\n      const heightRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height - availableHeight;\n  }\n  calculateSpaceIfDrawnVertical(availableSpace) {\n    let availableWidth = availableSpace.width;\n    if (this.axisConfig.showAxisLine && availableWidth > this.axisConfig.axisLineWidth) {\n      availableWidth -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.height;\n      this.outerPadding = Math.min(spaceRequired.height / 2, maxPadding);\n      const widthRequired = spaceRequired.width + this.axisConfig.labelPadding * 2;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableWidth >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableWidth -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension(\n        [this.title],\n        this.axisConfig.titleFontSize\n      );\n      const widthRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width - availableWidth;\n    this.boundingRect.height = availableSpace.height;\n  }\n  calculateSpace(availableSpace) {\n    if (this.axisPosition === \"left\" || this.axisPosition === \"right\") {\n      this.calculateSpaceIfDrawnVertical(availableSpace);\n    } else {\n      this.calculateSpaceIfDrawnHorizontally(availableSpace);\n    }\n    this.recalculateScale();\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  getDrawableElementsForLeftAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const x = this.boundingRect.x + this.boundingRect.width - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"left-axis\", \"axisl-line\"],\n        data: [\n          {\n            path: `M ${x},${this.boundingRect.y} L ${x},${this.boundingRect.y + this.boundingRect.height} `,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"left-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.boundingRect.x + this.boundingRect.width - (this.showLabel ? this.axisConfig.labelPadding : 0) - (this.showTick ? this.axisConfig.tickLength : 0) - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          y: this.getScaleValue(tick),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"middle\",\n          horizontalPos: \"right\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const x = this.boundingRect.x + this.boundingRect.width - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"left-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${x},${this.getScaleValue(tick)} L ${x - this.axisConfig.tickLength},${this.getScaleValue(tick)}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"left-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.boundingRect.x + this.axisConfig.titlePadding,\n            y: this.boundingRect.y + this.boundingRect.height / 2,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 270,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElementsForBottomAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"bottom-axis\", \"axis-line\"],\n        data: [\n          {\n            path: `M ${this.boundingRect.x},${y} L ${this.boundingRect.x + this.boundingRect.width},${y}`,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"bottom-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y: this.boundingRect.y + this.axisConfig.labelPadding + (this.showTick ? this.axisConfig.tickLength : 0) + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"bottom-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${this.getScaleValue(tick)},${y} L ${this.getScaleValue(tick)},${y + this.axisConfig.tickLength}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"bottom-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.range[0] + (this.range[1] - this.range[0]) / 2,\n            y: this.boundingRect.y + this.boundingRect.height - this.axisConfig.titlePadding - this.titleTextHeight,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 0,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElementsForTopAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.boundingRect.height - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"top-axis\", \"axis-line\"],\n        data: [\n          {\n            path: `M ${this.boundingRect.x},${y} L ${this.boundingRect.x + this.boundingRect.width},${y}`,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"top-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y: this.boundingRect.y + (this.showTitle ? this.titleTextHeight + this.axisConfig.titlePadding * 2 : 0) + this.axisConfig.labelPadding,\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"top-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${this.getScaleValue(tick)},${y + this.boundingRect.height - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)} L ${this.getScaleValue(tick)},${y + this.boundingRect.height - this.axisConfig.tickLength - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"top-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.boundingRect.x + this.boundingRect.width / 2,\n            y: this.boundingRect.y + this.axisConfig.titlePadding,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 0,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElements() {\n    if (this.axisPosition === \"left\") {\n      return this.getDrawableElementsForLeftAxis();\n    }\n    if (this.axisPosition === \"right\") {\n      throw Error(\"Drawing of right axis is not implemented\");\n    }\n    if (this.axisPosition === \"bottom\") {\n      return this.getDrawableElementsForBottomAxis();\n    }\n    if (this.axisPosition === \"top\") {\n      return this.getDrawableElementsForTopAxis();\n    }\n    return [];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts\nvar BandAxis = class extends BaseAxis {\n  static {\n    __name(this, \"BandAxis\");\n  }\n  constructor(axisConfig, axisThemeConfig, categories, title, textDimensionCalculator) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.categories = categories;\n    this.scale = scaleBand().domain(this.categories).range(this.getRange());\n  }\n  setRange(range) {\n    super.setRange(range);\n  }\n  recalculateScale() {\n    this.scale = scaleBand().domain(this.categories).range(this.getRange()).paddingInner(1).paddingOuter(0).align(0.5);\n    log.trace(\"BandAxis axis final categories, range: \", this.categories, this.getRange());\n  }\n  getTickValues() {\n    return this.categories;\n  }\n  getScaleValue(value) {\n    return this.scale(value) ?? this.getRange()[0];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/linearAxis.ts\nimport { scaleLinear } from \"d3\";\nvar LinearAxis = class extends BaseAxis {\n  static {\n    __name(this, \"LinearAxis\");\n  }\n  constructor(axisConfig, axisThemeConfig, domain, title, textDimensionCalculator) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.domain = domain;\n    this.scale = scaleLinear().domain(this.domain).range(this.getRange());\n  }\n  getTickValues() {\n    return this.scale.ticks();\n  }\n  recalculateScale() {\n    const domain = [...this.domain];\n    if (this.axisPosition === \"left\") {\n      domain.reverse();\n    }\n    this.scale = scaleLinear().domain(domain).range(this.getRange());\n  }\n  getScaleValue(value) {\n    return this.scale(value);\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/index.ts\nfunction getAxis(data, axisConfig, axisThemeConfig, tmpSVGGroup2) {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup2);\n  if (isBandAxisData(data)) {\n    return new BandAxis(\n      axisConfig,\n      axisThemeConfig,\n      data.categories,\n      data.title,\n      textDimensionCalculator\n    );\n  }\n  return new LinearAxis(\n    axisConfig,\n    axisThemeConfig,\n    [data.min, data.max],\n    data.title,\n    textDimensionCalculator\n  );\n}\n__name(getAxis, \"getAxis\");\n\n// src/diagrams/xychart/chartBuilder/components/chartTitle.ts\nvar ChartTitle = class {\n  constructor(textDimensionCalculator, chartConfig, chartData, chartThemeConfig) {\n    this.textDimensionCalculator = textDimensionCalculator;\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.chartThemeConfig = chartThemeConfig;\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n    this.showChartTitle = false;\n  }\n  static {\n    __name(this, \"ChartTitle\");\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace) {\n    const titleDimension = this.textDimensionCalculator.getMaxDimension(\n      [this.chartData.title],\n      this.chartConfig.titleFontSize\n    );\n    const widthRequired = Math.max(titleDimension.width, availableSpace.width);\n    const heightRequired = titleDimension.height + 2 * this.chartConfig.titlePadding;\n    if (titleDimension.width <= widthRequired && titleDimension.height <= heightRequired && this.chartConfig.showTitle && this.chartData.title) {\n      this.boundingRect.width = widthRequired;\n      this.boundingRect.height = heightRequired;\n      this.showChartTitle = true;\n    }\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  getDrawableElements() {\n    const drawableElem = [];\n    if (this.showChartTitle) {\n      drawableElem.push({\n        groupTexts: [\"chart-title\"],\n        type: \"text\",\n        data: [\n          {\n            fontSize: this.chartConfig.titleFontSize,\n            text: this.chartData.title,\n            verticalPos: \"middle\",\n            horizontalPos: \"center\",\n            x: this.boundingRect.x + this.boundingRect.width / 2,\n            y: this.boundingRect.y + this.boundingRect.height / 2,\n            fill: this.chartThemeConfig.titleColor,\n            rotation: 0\n          }\n        ]\n      });\n    }\n    return drawableElem;\n  }\n};\nfunction getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2) {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup2);\n  return new ChartTitle(textDimensionCalculator, chartConfig, chartData, chartThemeConfig);\n}\n__name(getChartTitleComponent, \"getChartTitleComponent\");\n\n// src/diagrams/xychart/chartBuilder/components/plot/linePlot.ts\nimport { line } from \"d3\";\nvar LinePlot = class {\n  constructor(plotData, xAxis, yAxis, orientation, plotIndex2) {\n    this.plotData = plotData;\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n    this.orientation = orientation;\n    this.plotIndex = plotIndex2;\n  }\n  static {\n    __name(this, \"LinePlot\");\n  }\n  getDrawableElement() {\n    const finalData = this.plotData.data.map((d) => [\n      this.xAxis.getScaleValue(d[0]),\n      this.yAxis.getScaleValue(d[1])\n    ]);\n    let path;\n    if (this.orientation === \"horizontal\") {\n      path = line().y((d) => d[0]).x((d) => d[1])(finalData);\n    } else {\n      path = line().x((d) => d[0]).y((d) => d[1])(finalData);\n    }\n    if (!path) {\n      return [];\n    }\n    return [\n      {\n        groupTexts: [\"plot\", `line-plot-${this.plotIndex}`],\n        type: \"path\",\n        data: [\n          {\n            path,\n            strokeFill: this.plotData.strokeFill,\n            strokeWidth: this.plotData.strokeWidth\n          }\n        ]\n      }\n    ];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/plot/barPlot.ts\nvar BarPlot = class {\n  constructor(barData, boundingRect, xAxis, yAxis, orientation, plotIndex2) {\n    this.barData = barData;\n    this.boundingRect = boundingRect;\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n    this.orientation = orientation;\n    this.plotIndex = plotIndex2;\n  }\n  static {\n    __name(this, \"BarPlot\");\n  }\n  getDrawableElement() {\n    const finalData = this.barData.data.map((d) => [\n      this.xAxis.getScaleValue(d[0]),\n      this.yAxis.getScaleValue(d[1])\n    ]);\n    const barPaddingPercent = 0.05;\n    const barWidth = Math.min(this.xAxis.getAxisOuterPadding() * 2, this.xAxis.getTickDistance()) * (1 - barPaddingPercent);\n    const barWidthHalf = barWidth / 2;\n    if (this.orientation === \"horizontal\") {\n      return [\n        {\n          groupTexts: [\"plot\", `bar-plot-${this.plotIndex}`],\n          type: \"rect\",\n          data: finalData.map((data) => ({\n            x: this.boundingRect.x,\n            y: data[0] - barWidthHalf,\n            height: barWidth,\n            width: data[1] - this.boundingRect.x,\n            fill: this.barData.fill,\n            strokeWidth: 0,\n            strokeFill: this.barData.fill\n          }))\n        }\n      ];\n    }\n    return [\n      {\n        groupTexts: [\"plot\", `bar-plot-${this.plotIndex}`],\n        type: \"rect\",\n        data: finalData.map((data) => ({\n          x: data[0] - barWidthHalf,\n          y: data[1],\n          width: barWidth,\n          height: this.boundingRect.y + this.boundingRect.height - data[1],\n          fill: this.barData.fill,\n          strokeWidth: 0,\n          strokeFill: this.barData.fill\n        }))\n      }\n    ];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/plot/index.ts\nvar BasePlot = class {\n  constructor(chartConfig, chartData, chartThemeConfig) {\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.chartThemeConfig = chartThemeConfig;\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n  }\n  static {\n    __name(this, \"BasePlot\");\n  }\n  setAxes(xAxis, yAxis) {\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace) {\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height;\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  getDrawableElements() {\n    if (!(this.xAxis && this.yAxis)) {\n      throw Error(\"Axes must be passed to render Plots\");\n    }\n    const drawableElem = [];\n    for (const [i, plot] of this.chartData.plots.entries()) {\n      switch (plot.type) {\n        case \"line\":\n          {\n            const linePlot = new LinePlot(\n              plot,\n              this.xAxis,\n              this.yAxis,\n              this.chartConfig.chartOrientation,\n              i\n            );\n            drawableElem.push(...linePlot.getDrawableElement());\n          }\n          break;\n        case \"bar\":\n          {\n            const barPlot = new BarPlot(\n              plot,\n              this.boundingRect,\n              this.xAxis,\n              this.yAxis,\n              this.chartConfig.chartOrientation,\n              i\n            );\n            drawableElem.push(...barPlot.getDrawableElement());\n          }\n          break;\n      }\n    }\n    return drawableElem;\n  }\n};\nfunction getPlotComponent(chartConfig, chartData, chartThemeConfig) {\n  return new BasePlot(chartConfig, chartData, chartThemeConfig);\n}\n__name(getPlotComponent, \"getPlotComponent\");\n\n// src/diagrams/xychart/chartBuilder/orchestrator.ts\nvar Orchestrator = class {\n  constructor(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2) {\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.componentStore = {\n      title: getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2),\n      plot: getPlotComponent(chartConfig, chartData, chartThemeConfig),\n      xAxis: getAxis(\n        chartData.xAxis,\n        chartConfig.xAxis,\n        {\n          titleColor: chartThemeConfig.xAxisTitleColor,\n          labelColor: chartThemeConfig.xAxisLabelColor,\n          tickColor: chartThemeConfig.xAxisTickColor,\n          axisLineColor: chartThemeConfig.xAxisLineColor\n        },\n        tmpSVGGroup2\n      ),\n      yAxis: getAxis(\n        chartData.yAxis,\n        chartConfig.yAxis,\n        {\n          titleColor: chartThemeConfig.yAxisTitleColor,\n          labelColor: chartThemeConfig.yAxisLabelColor,\n          tickColor: chartThemeConfig.yAxisTickColor,\n          axisLineColor: chartThemeConfig.yAxisLineColor\n        },\n        tmpSVGGroup2\n      )\n    };\n  }\n  static {\n    __name(this, \"Orchestrator\");\n  }\n  calculateVerticalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor(availableWidth * this.chartConfig.plotReservedSpacePercent / 100);\n    let chartHeight = Math.floor(\n      availableHeight * this.chartConfig.plotReservedSpacePercent / 100\n    );\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight\n    });\n    plotY = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition(\"bottom\");\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableHeight -= spaceUsed.height;\n    this.componentStore.yAxis.setAxisPosition(\"left\");\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    plotX = spaceUsed.width;\n    availableWidth -= spaceUsed.width;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    this.componentStore.plot.setBoundingBoxXY({ x: plotX, y: plotY });\n    this.componentStore.xAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.xAxis.setBoundingBoxXY({ x: plotX, y: plotY + chartHeight });\n    this.componentStore.yAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.yAxis.setBoundingBoxXY({ x: 0, y: plotY });\n    if (this.chartData.plots.some((p) => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n  calculateHorizontalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let titleYEnd = 0;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor(availableWidth * this.chartConfig.plotReservedSpacePercent / 100);\n    let chartHeight = Math.floor(\n      availableHeight * this.chartConfig.plotReservedSpacePercent / 100\n    );\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight\n    });\n    titleYEnd = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition(\"left\");\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableWidth -= spaceUsed.width;\n    plotX = spaceUsed.width;\n    this.componentStore.yAxis.setAxisPosition(\"top\");\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableHeight -= spaceUsed.height;\n    plotY = titleYEnd + spaceUsed.height;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    this.componentStore.plot.setBoundingBoxXY({ x: plotX, y: plotY });\n    this.componentStore.yAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.yAxis.setBoundingBoxXY({ x: plotX, y: titleYEnd });\n    this.componentStore.xAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.xAxis.setBoundingBoxXY({ x: 0, y: plotY });\n    if (this.chartData.plots.some((p) => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n  calculateSpace() {\n    if (this.chartConfig.chartOrientation === \"horizontal\") {\n      this.calculateHorizontalSpace();\n    } else {\n      this.calculateVerticalSpace();\n    }\n  }\n  getDrawableElement() {\n    this.calculateSpace();\n    const drawableElem = [];\n    this.componentStore.plot.setAxes(this.componentStore.xAxis, this.componentStore.yAxis);\n    for (const component of Object.values(this.componentStore)) {\n      drawableElem.push(...component.getDrawableElements());\n    }\n    return drawableElem;\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/index.ts\nvar XYChartBuilder = class {\n  static {\n    __name(this, \"XYChartBuilder\");\n  }\n  static build(config, chartData, chartThemeConfig, tmpSVGGroup2) {\n    const orchestrator = new Orchestrator(config, chartData, chartThemeConfig, tmpSVGGroup2);\n    return orchestrator.getDrawableElement();\n  }\n};\n\n// src/diagrams/xychart/xychartDb.ts\nvar plotIndex = 0;\nvar tmpSVGGroup;\nvar xyChartConfig = getChartDefaultConfig();\nvar xyChartThemeConfig = getChartDefaultThemeConfig();\nvar xyChartData = getChartDefaultData();\nvar plotColorPalette = xyChartThemeConfig.plotColorPalette.split(\",\").map((color) => color.trim());\nvar hasSetXAxis = false;\nvar hasSetYAxis = false;\nfunction getChartDefaultThemeConfig() {\n  const defaultThemeVariables = getThemeVariables();\n  const config = getConfig();\n  return cleanAndMerge(defaultThemeVariables.xyChart, config.themeVariables.xyChart);\n}\n__name(getChartDefaultThemeConfig, \"getChartDefaultThemeConfig\");\nfunction getChartDefaultConfig() {\n  const config = getConfig();\n  return cleanAndMerge(\n    defaultConfig_default.xyChart,\n    config.xyChart\n  );\n}\n__name(getChartDefaultConfig, \"getChartDefaultConfig\");\nfunction getChartDefaultData() {\n  return {\n    yAxis: {\n      type: \"linear\",\n      title: \"\",\n      min: Infinity,\n      max: -Infinity\n    },\n    xAxis: {\n      type: \"band\",\n      title: \"\",\n      categories: []\n    },\n    title: \"\",\n    plots: []\n  };\n}\n__name(getChartDefaultData, \"getChartDefaultData\");\nfunction textSanitizer(text) {\n  const config = getConfig();\n  return sanitizeText(text.trim(), config);\n}\n__name(textSanitizer, \"textSanitizer\");\nfunction setTmpSVGG(SVGG) {\n  tmpSVGGroup = SVGG;\n}\n__name(setTmpSVGG, \"setTmpSVGG\");\nfunction setOrientation(orientation) {\n  if (orientation === \"horizontal\") {\n    xyChartConfig.chartOrientation = \"horizontal\";\n  } else {\n    xyChartConfig.chartOrientation = \"vertical\";\n  }\n}\n__name(setOrientation, \"setOrientation\");\nfunction setXAxisTitle(title) {\n  xyChartData.xAxis.title = textSanitizer(title.text);\n}\n__name(setXAxisTitle, \"setXAxisTitle\");\nfunction setXAxisRangeData(min, max) {\n  xyChartData.xAxis = { type: \"linear\", title: xyChartData.xAxis.title, min, max };\n  hasSetXAxis = true;\n}\n__name(setXAxisRangeData, \"setXAxisRangeData\");\nfunction setXAxisBand(categories) {\n  xyChartData.xAxis = {\n    type: \"band\",\n    title: xyChartData.xAxis.title,\n    categories: categories.map((c) => textSanitizer(c.text))\n  };\n  hasSetXAxis = true;\n}\n__name(setXAxisBand, \"setXAxisBand\");\nfunction setYAxisTitle(title) {\n  xyChartData.yAxis.title = textSanitizer(title.text);\n}\n__name(setYAxisTitle, \"setYAxisTitle\");\nfunction setYAxisRangeData(min, max) {\n  xyChartData.yAxis = { type: \"linear\", title: xyChartData.yAxis.title, min, max };\n  hasSetYAxis = true;\n}\n__name(setYAxisRangeData, \"setYAxisRangeData\");\nfunction setYAxisRangeFromPlotData(data) {\n  const minValue = Math.min(...data);\n  const maxValue = Math.max(...data);\n  const prevMinValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.min : Infinity;\n  const prevMaxValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.max : -Infinity;\n  xyChartData.yAxis = {\n    type: \"linear\",\n    title: xyChartData.yAxis.title,\n    min: Math.min(prevMinValue, minValue),\n    max: Math.max(prevMaxValue, maxValue)\n  };\n}\n__name(setYAxisRangeFromPlotData, \"setYAxisRangeFromPlotData\");\nfunction transformDataWithoutCategory(data) {\n  let retData = [];\n  if (data.length === 0) {\n    return retData;\n  }\n  if (!hasSetXAxis) {\n    const prevMinValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.min : Infinity;\n    const prevMaxValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.max : -Infinity;\n    setXAxisRangeData(Math.min(prevMinValue, 1), Math.max(prevMaxValue, data.length));\n  }\n  if (!hasSetYAxis) {\n    setYAxisRangeFromPlotData(data);\n  }\n  if (isBandAxisData(xyChartData.xAxis)) {\n    retData = xyChartData.xAxis.categories.map((c, i) => [c, data[i]]);\n  }\n  if (isLinearAxisData(xyChartData.xAxis)) {\n    const min = xyChartData.xAxis.min;\n    const max = xyChartData.xAxis.max;\n    const step = (max - min) / (data.length - 1);\n    const categories = [];\n    for (let i = min; i <= max; i += step) {\n      categories.push(`${i}`);\n    }\n    retData = categories.map((c, i) => [c, data[i]]);\n  }\n  return retData;\n}\n__name(transformDataWithoutCategory, \"transformDataWithoutCategory\");\nfunction getPlotColorFromPalette(plotIndex2) {\n  return plotColorPalette[plotIndex2 === 0 ? 0 : plotIndex2 % plotColorPalette.length];\n}\n__name(getPlotColorFromPalette, \"getPlotColorFromPalette\");\nfunction setLineData(title, data) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: \"line\",\n    strokeFill: getPlotColorFromPalette(plotIndex),\n    strokeWidth: 2,\n    data: plotData\n  });\n  plotIndex++;\n}\n__name(setLineData, \"setLineData\");\nfunction setBarData(title, data) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: \"bar\",\n    fill: getPlotColorFromPalette(plotIndex),\n    data: plotData\n  });\n  plotIndex++;\n}\n__name(setBarData, \"setBarData\");\nfunction getDrawableElem() {\n  if (xyChartData.plots.length === 0) {\n    throw Error(\"No Plot to render, please provide a plot with some data\");\n  }\n  xyChartData.title = getDiagramTitle();\n  return XYChartBuilder.build(xyChartConfig, xyChartData, xyChartThemeConfig, tmpSVGGroup);\n}\n__name(getDrawableElem, \"getDrawableElem\");\nfunction getChartThemeConfig() {\n  return xyChartThemeConfig;\n}\n__name(getChartThemeConfig, \"getChartThemeConfig\");\nfunction getChartConfig() {\n  return xyChartConfig;\n}\n__name(getChartConfig, \"getChartConfig\");\nvar clear2 = /* @__PURE__ */ __name(function() {\n  clear();\n  plotIndex = 0;\n  xyChartConfig = getChartDefaultConfig();\n  xyChartData = getChartDefaultData();\n  xyChartThemeConfig = getChartDefaultThemeConfig();\n  plotColorPalette = xyChartThemeConfig.plotColorPalette.split(\",\").map((color) => color.trim());\n  hasSetXAxis = false;\n  hasSetYAxis = false;\n}, \"clear\");\nvar xychartDb_default = {\n  getDrawableElem,\n  clear: clear2,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n  setOrientation,\n  setXAxisTitle,\n  setXAxisRangeData,\n  setXAxisBand,\n  setYAxisTitle,\n  setYAxisRangeData,\n  setLineData,\n  setBarData,\n  setTmpSVGG,\n  getChartThemeConfig,\n  getChartConfig\n};\n\n// src/diagrams/xychart/xychartRenderer.ts\nvar draw = /* @__PURE__ */ __name((txt, id, _version, diagObj) => {\n  const db = diagObj.db;\n  const themeConfig = db.getChartThemeConfig();\n  const chartConfig = db.getChartConfig();\n  function getDominantBaseLine(horizontalPos) {\n    return horizontalPos === \"top\" ? \"text-before-edge\" : \"middle\";\n  }\n  __name(getDominantBaseLine, \"getDominantBaseLine\");\n  function getTextAnchor(verticalPos) {\n    return verticalPos === \"left\" ? \"start\" : verticalPos === \"right\" ? \"end\" : \"middle\";\n  }\n  __name(getTextAnchor, \"getTextAnchor\");\n  function getTextTransformation(data) {\n    return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;\n  }\n  __name(getTextTransformation, \"getTextTransformation\");\n  log.debug(\"Rendering xychart chart\\n\" + txt);\n  const svg = selectSvgElement(id);\n  const group = svg.append(\"g\").attr(\"class\", \"main\");\n  const background = group.append(\"rect\").attr(\"width\", chartConfig.width).attr(\"height\", chartConfig.height).attr(\"class\", \"background\");\n  configureSvgSize(svg, chartConfig.height, chartConfig.width, true);\n  svg.attr(\"viewBox\", `0 0 ${chartConfig.width} ${chartConfig.height}`);\n  background.attr(\"fill\", themeConfig.backgroundColor);\n  db.setTmpSVGG(svg.append(\"g\").attr(\"class\", \"mermaid-tmp-group\"));\n  const shapes = db.getDrawableElem();\n  const groups = {};\n  function getGroup(gList) {\n    let elem = group;\n    let prefix = \"\";\n    for (const [i] of gList.entries()) {\n      let parent = group;\n      if (i > 0 && groups[prefix]) {\n        parent = groups[prefix];\n      }\n      prefix += gList[i];\n      elem = groups[prefix];\n      if (!elem) {\n        elem = groups[prefix] = parent.append(\"g\").attr(\"class\", gList[i]);\n      }\n    }\n    return elem;\n  }\n  __name(getGroup, \"getGroup\");\n  for (const shape of shapes) {\n    if (shape.data.length === 0) {\n      continue;\n    }\n    const shapeGroup = getGroup(shape.groupTexts);\n    switch (shape.type) {\n      case \"rect\":\n        shapeGroup.selectAll(\"rect\").data(shape.data).enter().append(\"rect\").attr(\"x\", (data) => data.x).attr(\"y\", (data) => data.y).attr(\"width\", (data) => data.width).attr(\"height\", (data) => data.height).attr(\"fill\", (data) => data.fill).attr(\"stroke\", (data) => data.strokeFill).attr(\"stroke-width\", (data) => data.strokeWidth);\n        break;\n      case \"text\":\n        shapeGroup.selectAll(\"text\").data(shape.data).enter().append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", (data) => data.fill).attr(\"font-size\", (data) => data.fontSize).attr(\"dominant-baseline\", (data) => getDominantBaseLine(data.verticalPos)).attr(\"text-anchor\", (data) => getTextAnchor(data.horizontalPos)).attr(\"transform\", (data) => getTextTransformation(data)).text((data) => data.text);\n        break;\n      case \"path\":\n        shapeGroup.selectAll(\"path\").data(shape.data).enter().append(\"path\").attr(\"d\", (data) => data.path).attr(\"fill\", (data) => data.fill ? data.fill : \"none\").attr(\"stroke\", (data) => data.strokeFill).attr(\"stroke-width\", (data) => data.strokeWidth);\n        break;\n    }\n  }\n}, \"draw\");\nvar xychartRenderer_default = {\n  draw\n};\n\n// src/diagrams/xychart/xychartDiagram.ts\nvar diagram = {\n  parser: xychart_default,\n  db: xychartDb_default,\n  renderer: xychartRenderer_default\n};\nexport {\n  diagram\n};\n"], "names": [], "sourceRoot": ""}