Metadata-Version: 2.3
Name: openbb-equity
Version: 1.4.0
Summary: Equity extension for OpenBB
License: AGPL-3.0-only
Author: OpenBB Team
Author-email: <EMAIL>
Requires-Python: >=3.9,<4.0
Classifier: License :: OSI Approved :: GNU Affero General Public License v3
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Dist: openbb-core (>=1.4.0,<2.0.0)
Description-Content-Type: text/markdown

# OpenBB Equity Extension

This extension provides equity market data tools for the OpenBB Platform.

Features of the Equity extension include:

- Access to various equity market data sources
- Sub-modules such as:
  - `calendar` for equity-specific events
  - `compare` for peer analysis
  - `darkpool` for dark pool shorts data
  - `discovery` for equity discovery
  - `estimates` for analyst estimates
  - `fundamental` for fundamental analysis
  - `options` for options
  - `ownership` for internal and external ownership
  - `price` for historical pricing data
  - `shorts` for shorts data

## Installation

To install the extension, run the following command in this folder:

```bash
pip install openbb-equity
```

Documentation available [here](https://docs.openbb.co/platform/developer_guide/contributing).

