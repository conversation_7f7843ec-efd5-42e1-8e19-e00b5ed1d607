{"title": "Notifications", "description": "Notifications settings.", "jupyter.lab.setting-icon": "ui-components:bell", "jupyter.lab.menus": {"main": [{"id": "jp-mainmenu-view", "items": [{"type": "separator", "rank": 9.9}, {"command": "apputils:display-notifications", "rank": 9.92}, {"type": "separator", "rank": 9.99}]}]}, "additionalProperties": false, "properties": {"checkForUpdates": {"title": "Check for JupyterLab updates", "description": "Whether to check for newer versions of JupyterLab or not. It requires `fetchNews` to be set to Always (`true`) to be active. If `true`, it will make a request to a website.", "type": "boolean", "default": true}, "doNotDisturbMode": {"title": "Silence all notifications", "description": "If `true`, no toast notifications will be automatically displayed.", "type": "boolean", "default": false}, "fetchNews": {"title": "Fetch official Jupyter news", "description": "Whether to fetch news from the Jupyter news feed. If Always (`true`), it will make a request to a website.", "type": "string", "oneOf": [{"const": "true", "title": "Always"}, {"const": "false", "title": "Never"}, {"const": "none", "title": "Ask (None)"}], "default": "none"}}, "type": "object"}