# The cool hackerman style

# LINES
# http://matplotlib.org/api/artist_api.html#module-matplotlib.lines
lines.linewidth: 1.5
lines.color: F5EFF3
lines.linestyle: -
lines.marker: None
lines.markeredgewidth: 0.5
lines.markersize: 4
lines.dash_joinstyle: miter
lines.dash_capstyle: butt
lines.solid_joinstyle: miter
lines.solid_capstyle: projecting
lines.antialiased: True

patch.edgecolor: F5EFF3
patch.facecolor: 00aaff
patch.linewidth: 1

# TEXT
# http://matplotlib.org/api/font_manager_api.html
font.family: sans-serif
font.sans-serif: <PERSON><PERSON><PERSON>, Deja V<PERSON>s, Arial, Helvetica
font.style: normal
font.variant: normal
font.weight: medium
font.stretch: normal
font.size: 16

text.color: F5EFF3

axes.spines.left: true
axes.spines.bottom: true
axes.spines.top: true
axes.spines.right: true
axes.linewidth: 0.4

axes.labelsize: small
axes.titlelocation: left

axes.facecolor: black
axes.edgecolor: F5EFF3
axes.labelcolor: F5EFF3
axes.grid: true
axes.grid.which: major

axes.prop_cycle: cycler('color', ['ffed00', 'ef7d00', 'e4003a', 'c13246', '822661', '48277c', '005ca9', '00aaff', '9b30d9', 'af005f', '5f00af', 'af87ff'])

xtick.color: F5EFF3
ytick.color: F5EFF3

xtick.major.size: 2
xtick.minor.size: 1
xtick.labelsize: small
xtick.alignment: center
xtick.major.width: 0.2

ytick.major.size: 2
ytick.minor.size: 1
ytick.labelsize: small

ytick.left: False
ytick.labelleft: False
ytick.right: True
ytick.labelright: True
ytick.major.width: 0.2

grid.color: F5EFF3
grid.linewidth: 0.4
grid.linestyle: :

legend.framealpha: 0.6
legend.frameon: true
legend.facecolor: black
legend.edgecolor: black
legend.scatterpoints: 3
legend.fontsize: small
legend.loc: upper left

figure.facecolor: black
figure.edgecolor: black
figure.subplot.hspace: 0.2

savefig.facecolor: black
savefig.edgecolor: black

### Boxplots
boxplot.boxprops.color: F5EFF3
boxplot.capprops.color: F5EFF3
boxplot.flierprops.color: F5EFF3
boxplot.flierprops.markeredgecolor: F5EFF3
boxplot.whiskerprops.color: F5EFF3
boxplot.medianprops.color: F5EFF3
boxplot.meanprops.markeredgecolor: F5EFF3
boxplot.meanprops.color: F5EFF3
