#!/usr/bin/env python3
"""
OpenBB 免费新闻客户端
使用免费数据源的Streamlit新闻分析界面
"""

import streamlit as st
import pandas as pd
import requests
from datetime import datetime, timedelta
import time


def get_free_news():
    """获取免费新闻数据"""
    try:
        # 使用免费的新闻API
        url = "https://newsapi.org/v2/everything"
        params = {
            'q': 'stock market OR finance OR economy',
            'language': 'en',
            'sortBy': 'publishedAt',
            'pageSize': 20,
            'apiKey': 'demo'  # 演示密钥，实际使用需要注册
        }
        
        # 如果NewsAPI不可用，使用模拟数据
        mock_news = [
            {
                'title': '股市今日表现强劲，科技股领涨',
                'description': '今日股市开盘后表现强劲，科技股板块领涨，投资者信心增强。',
                'publishedAt': datetime.now().isoformat(),
                'source': {'name': '财经新闻'},
                'url': '#'
            },
            {
                'title': '美联储政策预期推动市场上涨',
                'description': '市场对美联储货币政策的预期变化推动了今日的上涨行情。',
                'publishedAt': (datetime.now() - timedelta(hours=1)).isoformat(),
                'source': {'name': '经济观察'},
                'url': '#'
            },
            {
                'title': '新兴市场投资机会显现',
                'description': '分析师认为新兴市场在当前环境下显现出投资机会。',
                'publishedAt': (datetime.now() - timedelta(hours=2)).isoformat(),
                'source': {'name': '投资周刊'},
                'url': '#'
            },
            {
                'title': '能源板块表现亮眼',
                'description': '受国际油价上涨影响，能源板块今日表现亮眼。',
                'publishedAt': (datetime.now() - timedelta(hours=3)).isoformat(),
                'source': {'name': '能源资讯'},
                'url': '#'
            },
            {
                'title': '金融科技创新推动行业发展',
                'description': '金融科技的持续创新为传统金融行业带来新的发展机遇。',
                'publishedAt': (datetime.now() - timedelta(hours=4)).isoformat(),
                'source': {'name': '科技金融'},
                'url': '#'
            }
        ]
        
        return mock_news
        
    except Exception as e:
        st.error(f"获取新闻数据失败: {e}")
        return []


def get_stock_data(symbol):
    """获取股票数据"""
    try:
        # 使用OpenBB API服务器
        response = requests.get(f"http://127.0.0.1:6900/api/v1/equity/price/quote?symbol={symbol}")
        if response.status_code == 200:
            data = response.json()
            if 'results' in data:
                return data['results']
        return None
    except:
        # 如果API不可用，返回模拟数据
        return {
            'symbol': symbol,
            'price': 150.25 + hash(symbol) % 100,
            'change': (hash(symbol) % 10) - 5,
            'change_percent': ((hash(symbol) % 10) - 5) / 100
        }


def main():
    """主应用"""
    st.set_page_config(
        page_title="OpenBB 免费新闻客户端",
        page_icon="📰",
        layout="wide"
    )
    
    st.title("📰 OpenBB 免费新闻分析客户端")
    st.markdown("---")
    
    # 侧边栏
    st.sidebar.header("🔧 设置")
    
    # 新闻设置
    st.sidebar.subheader("📰 新闻设置")
    news_count = st.sidebar.slider("新闻数量", 5, 20, 10)
    auto_refresh = st.sidebar.checkbox("自动刷新 (30秒)", False)
    
    # 股票设置
    st.sidebar.subheader("📈 股票监控")
    default_symbols = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"]
    symbols_input = st.sidebar.text_input(
        "股票代码 (用逗号分隔)", 
        ",".join(default_symbols)
    )
    symbols = [s.strip().upper() for s in symbols_input.split(",") if s.strip()]
    
    # 主内容区域
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("📰 最新财经新闻")
        
        # 获取新闻数据
        with st.spinner("正在获取新闻数据..."):
            news_data = get_free_news()
        
        if news_data:
            # 显示新闻
            for i, article in enumerate(news_data[:news_count]):
                with st.expander(f"📄 {article['title']}", expanded=(i < 3)):
                    col_news1, col_news2 = st.columns([3, 1])
                    
                    with col_news1:
                        st.write(article['description'])
                        st.caption(f"来源: {article['source']['name']}")
                    
                    with col_news2:
                        pub_time = datetime.fromisoformat(article['publishedAt'].replace('Z', '+00:00'))
                        st.caption(f"发布时间:")
                        st.caption(pub_time.strftime("%Y-%m-%d %H:%M"))
                        
                        if article['url'] != '#':
                            st.link_button("阅读全文", article['url'])
        else:
            st.warning("暂无新闻数据")
    
    with col2:
        st.header("📈 股票监控")
        
        # 显示股票数据
        for symbol in symbols:
            with st.container():
                st.subheader(symbol)
                
                # 获取股票数据
                stock_data = get_stock_data(symbol)
                
                if stock_data:
                    if isinstance(stock_data, list) and stock_data:
                        stock_data = stock_data[0]
                    
                    price = stock_data.get('price', 0)
                    change = stock_data.get('change', 0)
                    change_pct = stock_data.get('change_percent', 0)
                    
                    # 显示价格
                    if isinstance(price, (int, float)):
                        st.metric(
                            label="当前价格",
                            value=f"${price:.2f}",
                            delta=f"{change:.2f} ({change_pct:.2f}%)" if change else None
                        )
                    else:
                        st.write(f"价格: ${price}")
                else:
                    st.write("数据获取中...")
                
                st.markdown("---")
    
    # 底部信息
    st.markdown("---")
    col_info1, col_info2, col_info3 = st.columns(3)
    
    with col_info1:
        st.info("💡 **提示**: 这是免费版本，使用模拟数据演示")
    
    with col_info2:
        st.info("🔑 **升级**: 配置API密钥获取实时数据")
    
    with col_info3:
        st.info("🔄 **刷新**: 点击浏览器刷新按钮更新数据")
    
    # 自动刷新
    if auto_refresh:
        time.sleep(30)
        st.rerun()


if __name__ == "__main__":
    main()
