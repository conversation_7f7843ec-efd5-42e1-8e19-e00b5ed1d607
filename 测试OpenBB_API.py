#!/usr/bin/env python3
"""
测试OpenBB API参数和调用
分析API需要的参数并进行实际调用
"""

import requests
import json
import time
from datetime import datetime, timedelta


class OpenBBAPITester:
    """OpenBB API测试器"""
    
    def __init__(self, base_url="http://127.0.0.1:6900"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
    
    def test_endpoint(self, endpoint, params=None, description=""):
        """测试API端点"""
        print(f"\n🔍 测试: {description}")
        print(f"端点: {endpoint}")
        print(f"参数: {params}")
        print("-" * 50)
        
        try:
            url = f"{self.base_url}{endpoint}"
            response = self.session.get(url, params=params, timeout=10)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print("✅ 成功!")
                    
                    # 显示数据结构
                    if isinstance(data, dict):
                        print(f"响应键: {list(data.keys())}")
                        if 'results' in data:
                            results = data['results']
                            if isinstance(results, list) and results:
                                print(f"结果数量: {len(results)}")
                                print(f"第一条数据键: {list(results[0].keys()) if results[0] else 'None'}")
                            elif isinstance(results, dict):
                                print(f"结果键: {list(results.keys())}")
                    
                    return data
                    
                except json.JSONDecodeError:
                    print("❌ JSON解析失败")
                    print(f"响应内容: {response.text[:200]}...")
                    
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text[:200]}...")
                
        except requests.exceptions.Timeout:
            print("⏰ 请求超时")
        except requests.exceptions.ConnectionError:
            print("🔌 连接错误")
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        return None
    
    def test_all_apis(self):
        """测试所有主要API"""
        print("🚀 OpenBB API 参数测试")
        print("=" * 60)
        
        # 1. 股票历史数据
        self.test_endpoint(
            "/api/v1/equity/price/historical",
            {"symbol": "AAPL", "provider": "yfinance"},
            "股票历史数据 - 基本参数"
        )
        
        # 2. 股票历史数据 - 带日期范围
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        end_date = datetime.now().strftime("%Y-%m-%d")
        
        self.test_endpoint(
            "/api/v1/equity/price/historical",
            {
                "symbol": "AAPL", 
                "provider": "yfinance",
                "start_date": start_date,
                "end_date": end_date
            },
            "股票历史数据 - 带日期范围"
        )
        
        # 3. 股票实时报价
        self.test_endpoint(
            "/api/v1/equity/price/quote",
            {"symbol": "AAPL", "provider": "yfinance"},
            "股票实时报价"
        )
        
        # 4. 多个股票报价
        self.test_endpoint(
            "/api/v1/equity/price/quote",
            {"symbol": "AAPL,MSFT,GOOGL", "provider": "yfinance"},
            "多个股票报价"
        )
        
        # 5. 新闻数据
        self.test_endpoint(
            "/api/v1/news/company",
            {"provider": "yfinance", "limit": 5},
            "新闻数据 - 无关键词"
        )
        
        # 6. 新闻数据 - 带关键词
        self.test_endpoint(
            "/api/v1/news/company",
            {"provider": "yfinance", "limit": 5, "symbol": "AAPL"},
            "新闻数据 - 带股票代码"
        )
        
        # 7. 经济数据 - GDP
        self.test_endpoint(
            "/api/v1/economy/gdp",
            {"provider": "fred", "country": "US"},
            "经济数据 - GDP"
        )
        
        # 8. 加密货币数据
        self.test_endpoint(
            "/api/v1/crypto/price/historical",
            {"symbol": "BTC-USD", "provider": "yfinance"},
            "加密货币历史数据"
        )
        
        # 9. ETF数据
        self.test_endpoint(
            "/api/v1/etf/price/historical",
            {"symbol": "SPY", "provider": "yfinance"},
            "ETF历史数据"
        )
        
        # 10. 财务报表 - 收入表
        self.test_endpoint(
            "/api/v1/equity/fundamental/income",
            {"symbol": "AAPL", "provider": "yfinance", "period": "annual"},
            "财务报表 - 收入表"
        )
        
        # 11. 财务报表 - 资产负债表
        self.test_endpoint(
            "/api/v1/equity/fundamental/balance",
            {"symbol": "AAPL", "provider": "yfinance", "period": "annual"},
            "财务报表 - 资产负债表"
        )
        
        # 12. 财务报表 - 现金流量表
        self.test_endpoint(
            "/api/v1/equity/fundamental/cash",
            {"symbol": "AAPL", "provider": "yfinance", "period": "annual"},
            "财务报表 - 现金流量表"
        )
        
        # 13. 技术指标 - 移动平均
        self.test_endpoint(
            "/api/v1/technical/sma",
            {"symbol": "AAPL", "provider": "yfinance", "window": 20},
            "技术指标 - 简单移动平均"
        )
        
        # 14. 期权数据
        self.test_endpoint(
            "/api/v1/derivatives/options/chains",
            {"symbol": "AAPL", "provider": "yfinance"},
            "期权链数据"
        )
        
        # 15. 指数数据
        self.test_endpoint(
            "/api/v1/index/price/historical",
            {"symbol": "^GSPC", "provider": "yfinance"},
            "指数历史数据 - S&P500"
        )
    
    def test_parameter_variations(self):
        """测试参数变化"""
        print("\n🔧 测试参数变化")
        print("=" * 60)
        
        # 测试不同的provider
        providers = ["yfinance", "fmp", "polygon", "alpha_vantage"]
        
        for provider in providers:
            self.test_endpoint(
                "/api/v1/equity/price/quote",
                {"symbol": "AAPL", "provider": provider},
                f"股票报价 - {provider} 提供商"
            )
            time.sleep(1)  # 避免请求过快
        
        # 测试不同的时间周期
        periods = ["1d", "5d", "1mo", "3mo", "6mo", "1y", "2y"]
        
        for period in periods:
            end_date = datetime.now()
            if period == "1d":
                start_date = end_date - timedelta(days=1)
            elif period == "5d":
                start_date = end_date - timedelta(days=5)
            elif period == "1mo":
                start_date = end_date - timedelta(days=30)
            elif period == "3mo":
                start_date = end_date - timedelta(days=90)
            elif period == "6mo":
                start_date = end_date - timedelta(days=180)
            elif period == "1y":
                start_date = end_date - timedelta(days=365)
            elif period == "2y":
                start_date = end_date - timedelta(days=730)
            
            self.test_endpoint(
                "/api/v1/equity/price/historical",
                {
                    "symbol": "AAPL", 
                    "provider": "yfinance",
                    "start_date": start_date.strftime("%Y-%m-%d"),
                    "end_date": end_date.strftime("%Y-%m-%d")
                },
                f"历史数据 - {period} 周期"
            )
            time.sleep(1)
    
    def generate_api_summary(self):
        """生成API使用总结"""
        print("\n📋 OpenBB API 使用总结")
        print("=" * 60)
        
        api_summary = {
            "股票数据": {
                "历史价格": {
                    "端点": "/api/v1/equity/price/historical",
                    "必需参数": ["symbol"],
                    "可选参数": ["provider", "start_date", "end_date", "interval"],
                    "默认provider": "yfinance"
                },
                "实时报价": {
                    "端点": "/api/v1/equity/price/quote", 
                    "必需参数": ["symbol"],
                    "可选参数": ["provider"],
                    "默认provider": "yfinance"
                }
            },
            "财务数据": {
                "收入表": {
                    "端点": "/api/v1/equity/fundamental/income",
                    "必需参数": ["symbol"],
                    "可选参数": ["provider", "period", "limit"],
                    "period选项": ["annual", "quarter"]
                },
                "资产负债表": {
                    "端点": "/api/v1/equity/fundamental/balance",
                    "必需参数": ["symbol"],
                    "可选参数": ["provider", "period", "limit"]
                },
                "现金流量表": {
                    "端点": "/api/v1/equity/fundamental/cash",
                    "必需参数": ["symbol"],
                    "可选参数": ["provider", "period", "limit"]
                }
            },
            "新闻数据": {
                "公司新闻": {
                    "端点": "/api/v1/news/company",
                    "必需参数": [],
                    "可选参数": ["provider", "symbol", "limit", "start_date", "end_date"]
                }
            },
            "经济数据": {
                "GDP": {
                    "端点": "/api/v1/economy/gdp",
                    "必需参数": [],
                    "可选参数": ["provider", "country", "start_date", "end_date"]
                }
            },
            "加密货币": {
                "历史价格": {
                    "端点": "/api/v1/crypto/price/historical",
                    "必需参数": ["symbol"],
                    "可选参数": ["provider", "start_date", "end_date"],
                    "symbol格式": "BTC-USD, ETH-USD"
                }
            }
        }
        
        for category, endpoints in api_summary.items():
            print(f"\n📊 {category}")
            print("-" * 30)
            
            for name, info in endpoints.items():
                print(f"  🔹 {name}")
                print(f"    端点: {info['端点']}")
                print(f"    必需: {info['必需参数']}")
                print(f"    可选: {info['可选参数']}")
                if '默认provider' in info:
                    print(f"    默认提供商: {info['默认provider']}")
                if 'period选项' in info:
                    print(f"    周期选项: {info['period选项']}")
                if 'symbol格式' in info:
                    print(f"    代码格式: {info['symbol格式']}")
                print()


def main():
    """主函数"""
    tester = OpenBBAPITester()
    
    # 测试所有API
    tester.test_all_apis()
    
    # 测试参数变化
    tester.test_parameter_variations()
    
    # 生成总结
    tester.generate_api_summary()
    
    print("\n🎉 API测试完成!")


if __name__ == "__main__":
    main()
