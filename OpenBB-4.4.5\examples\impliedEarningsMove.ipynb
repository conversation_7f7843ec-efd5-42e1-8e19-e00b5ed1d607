{"cells": [{"cell_type": "markdown", "metadata": {"id": "BzQ2PSUMb1O7"}, "source": ["# Calculating the Implied Earnings Move Using Options Prices\n", "\n", "Earnings day can be a pivotal moment for a company's share price. The confluence of expectations and reality is a tradable event, drawing crowds to the options market. Observing the surrounding action can provide insight into the consensus view on, and general sentiment of, the company.\n", "\n", "The cost of a straddle - the combined price of an at-the-money call and put - is a common way to gauge the near-term volatility. It's the market's expectation of the price band until expiration. While this includes time value, the isolated price of volatility will generally be higher for the expiry immediately following an earnings release.\n", "\n", "Have a look at companies that trade weekly options and are reporting a on Thursday. If they report after the close, the price of the one-day straddle at the bell will be the purest sample of information.\n", "\n", "The cells below will demonstrate how to get the data from free sources, using the OpenBB Platform."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "da3wLFHJaK1n"}, "outputs": [], "source": ["# If using in Google Colab, install the OpenBB library.\n", "\n", "#!pip install openbb[\"all\"]\n", "\n", "# Restart the runtime before the next block"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7xcKh78TaTot", "outputId": "1b00dbc7-21cd-421a-b191-b2e8685f491d"}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "from openbb import obb\n", "\n", "obb.user.preferences.output_type = \"dataframe\""]}, {"cell_type": "markdown", "metadata": {"id": "WyKBFJg-R_r2"}, "source": ["If the earnings date falls on an option expiry, contracts expiring that day will not provide exposure to the after-market earnings reports."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 711}, "id": "49D8bfFFPEwC", "outputId": "b2eebaaa-ef2c-456a-fca2-96f6bee41b2d"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>report_date</th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>eps_previous</th>\n", "      <th>eps_consensus</th>\n", "      <th>num_estimates</th>\n", "      <th>period_ending</th>\n", "      <th>previous_report_date</th>\n", "      <th>reporting_time</th>\n", "      <th>market_cap</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>166</th>\n", "      <td>2024-08-28</td>\n", "      <td>NVDA</td>\n", "      <td>NVIDIA Corporation</td>\n", "      <td>0.25</td>\n", "      <td>0.59</td>\n", "      <td>13.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-08-23</td>\n", "      <td>after-hours</td>\n", "      <td>3.160887e+12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-09-05</td>\n", "      <td>AVGO</td>\n", "      <td>Broadcom Inc.</td>\n", "      <td>0.95</td>\n", "      <td>0.95</td>\n", "      <td>10.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-08-31</td>\n", "      <td>after-hours</td>\n", "      <td>7.716866e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>167</th>\n", "      <td>2024-08-28</td>\n", "      <td>CRM</td>\n", "      <td>Salesforce, Inc.</td>\n", "      <td>1.63</td>\n", "      <td>1.73</td>\n", "      <td>16.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-08-30</td>\n", "      <td>after-hours</td>\n", "      <td>2.529962e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>261</th>\n", "      <td>2024-08-26</td>\n", "      <td>PDD</td>\n", "      <td>PDD Holdings Inc.</td>\n", "      <td>1.27</td>\n", "      <td>2.66</td>\n", "      <td>2.0</td>\n", "      <td>2024-06</td>\n", "      <td>2023-08-29</td>\n", "      <td>pre-market</td>\n", "      <td>2.007811e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>168</th>\n", "      <td>2024-08-28</td>\n", "      <td>RY</td>\n", "      <td>Royal Bank Of Canada</td>\n", "      <td>2.13</td>\n", "      <td>2.14</td>\n", "      <td>3.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-08-24</td>\n", "      <td>pre-market</td>\n", "      <td>1.597315e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>262</th>\n", "      <td>2024-08-26</td>\n", "      <td>BHP</td>\n", "      <td>BHP Group Limited</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>2024-06</td>\n", "      <td>NaN</td>\n", "      <td>after-hours</td>\n", "      <td>1.401966e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>114</th>\n", "      <td>2024-08-29</td>\n", "      <td>DELL</td>\n", "      <td>Dell Technologies Inc.</td>\n", "      <td>1.44</td>\n", "      <td>1.49</td>\n", "      <td>4.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-08-31</td>\n", "      <td>after-hours</td>\n", "      <td>7.922927e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>169</th>\n", "      <td>2024-08-28</td>\n", "      <td>CRWD</td>\n", "      <td>CrowdStrike Holdings, Inc.</td>\n", "      <td>0.06</td>\n", "      <td>0.23</td>\n", "      <td>14.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-08-30</td>\n", "      <td>after-hours</td>\n", "      <td>6.648856e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>225</th>\n", "      <td>2024-08-27</td>\n", "      <td>BMO</td>\n", "      <td>Bank Of Montreal</td>\n", "      <td>2.08</td>\n", "      <td>1.98</td>\n", "      <td>3.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-08-29</td>\n", "      <td>pre-market</td>\n", "      <td>6.319707e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>115</th>\n", "      <td>2024-08-29</td>\n", "      <td>MRVL</td>\n", "      <td>Marvell Technology, Inc.</td>\n", "      <td>0.18</td>\n", "      <td>0.13</td>\n", "      <td>13.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-08-24</td>\n", "      <td>after-hours</td>\n", "      <td>6.175190e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>226</th>\n", "      <td>2024-08-27</td>\n", "      <td>BNS</td>\n", "      <td>Bank of Nova Scotia (The)</td>\n", "      <td>1.30</td>\n", "      <td>1.18</td>\n", "      <td>4.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-08-29</td>\n", "      <td>pre-market</td>\n", "      <td>5.855210e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>116</th>\n", "      <td>2024-08-29</td>\n", "      <td>ADSK</td>\n", "      <td>Autodesk, Inc.</td>\n", "      <td>1.12</td>\n", "      <td>1.35</td>\n", "      <td>8.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-08-23</td>\n", "      <td>after-hours</td>\n", "      <td>5.444496e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>117</th>\n", "      <td>2024-08-29</td>\n", "      <td>CM</td>\n", "      <td>Canadian Imperial Bank of Commerce</td>\n", "      <td>1.14</td>\n", "      <td>1.28</td>\n", "      <td>4.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-08-31</td>\n", "      <td>pre-market</td>\n", "      <td>5.042232e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170</th>\n", "      <td>2024-08-28</td>\n", "      <td>HPQ</td>\n", "      <td>HP Inc.</td>\n", "      <td>0.86</td>\n", "      <td>0.86</td>\n", "      <td>4.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-08-29</td>\n", "      <td>after-hours</td>\n", "      <td>3.451380e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>227</th>\n", "      <td>2024-08-27</td>\n", "      <td>HEI</td>\n", "      <td>Heico Corporation</td>\n", "      <td>0.77</td>\n", "      <td>0.91</td>\n", "      <td>8.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-08-28</td>\n", "      <td>pre-market</td>\n", "      <td>3.387054e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>171</th>\n", "      <td>2024-08-28</td>\n", "      <td>VEEV</td>\n", "      <td>Veeva Systems Inc.</td>\n", "      <td>0.70</td>\n", "      <td>1.04</td>\n", "      <td>10.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-08-30</td>\n", "      <td>after-hours</td>\n", "      <td>3.256312e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>118</th>\n", "      <td>2024-08-29</td>\n", "      <td>LULU</td>\n", "      <td>lululemon athletica inc.</td>\n", "      <td>2.68</td>\n", "      <td>2.94</td>\n", "      <td>13.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-08-31</td>\n", "      <td>after-hours</td>\n", "      <td>3.184542e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>2024-09-03</td>\n", "      <td>ZS</td>\n", "      <td>Zscaler, Inc.</td>\n", "      <td>-0.17</td>\n", "      <td>-0.14</td>\n", "      <td>12.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-09-05</td>\n", "      <td>after-hours</td>\n", "      <td>3.030086e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>263</th>\n", "      <td>2024-08-26</td>\n", "      <td>TCOM</td>\n", "      <td>Trip.com Group Limited</td>\n", "      <td>0.60</td>\n", "      <td>0.65</td>\n", "      <td>2.0</td>\n", "      <td>2024-06</td>\n", "      <td>2023-09-04</td>\n", "      <td>after-hours</td>\n", "      <td>2.780128e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>172</th>\n", "      <td>2024-08-28</td>\n", "      <td>NTAP</td>\n", "      <td>NetApp, Inc.</td>\n", "      <td>0.84</td>\n", "      <td>1.15</td>\n", "      <td>8.0</td>\n", "      <td>2024-07</td>\n", "      <td>2023-08-23</td>\n", "      <td>after-hours</td>\n", "      <td>2.745601e+10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    report_date symbol                                name  eps_previous  \\\n", "166  2024-08-28   NVDA                  NVIDIA Corporation          0.25   \n", "0    2024-09-05   AVGO                       Broadcom Inc.          0.95   \n", "167  2024-08-28    CRM                    Salesforce, Inc.          1.63   \n", "261  2024-08-26    PDD                   PDD Holdings Inc.          1.27   \n", "168  2024-08-28     RY                Royal Bank Of Canada          2.13   \n", "262  2024-08-26    BHP                   BHP Group Limited           NaN   \n", "114  2024-08-29   DELL              Dell Technologies Inc.          1.44   \n", "169  2024-08-28   CRWD          CrowdStrike Holdings, Inc.          0.06   \n", "225  2024-08-27    BMO                    Bank Of Montreal          2.08   \n", "115  2024-08-29   MRVL            Marvell Technology, Inc.          0.18   \n", "226  2024-08-27    BNS           Bank of Nova Scotia (The)          1.30   \n", "116  2024-08-29   ADSK                      Autodesk, Inc.          1.12   \n", "117  2024-08-29     CM  Canadian Imperial Bank of Commerce          1.14   \n", "170  2024-08-28    HPQ                             HP Inc.          0.86   \n", "227  2024-08-27    HEI                   Heico Corporation          0.77   \n", "171  2024-08-28   VEEV                  Veeva Systems Inc.          0.70   \n", "118  2024-08-29   LULU            lululemon athletica inc.          2.68   \n", "88   2024-09-03     ZS                       Zscaler, Inc.         -0.17   \n", "263  2024-08-26   TCOM              Trip.com Group Limited          0.60   \n", "172  2024-08-28   NTAP                        NetApp, Inc.          0.84   \n", "\n", "     eps_consensus  num_estimates period_ending previous_report_date  \\\n", "166           0.59           13.0       2024-07           2023-08-23   \n", "0             0.95           10.0       2024-07           2023-08-31   \n", "167           1.73           16.0       2024-07           2023-08-30   \n", "261           2.66            2.0       2024-06           2023-08-29   \n", "168           2.14            3.0       2024-07           2023-08-24   \n", "262            NaN            1.0       2024-06                  NaN   \n", "114           1.49            4.0       2024-07           2023-08-31   \n", "169           0.23           14.0       2024-07           2023-08-30   \n", "225           1.98            3.0       2024-07           2023-08-29   \n", "115           0.13           13.0       2024-07           2023-08-24   \n", "226           1.18            4.0       2024-07           2023-08-29   \n", "116           1.35            8.0       2024-07           2023-08-23   \n", "117           1.28            4.0       2024-07           2023-08-31   \n", "170           0.86            4.0       2024-07           2023-08-29   \n", "227           0.91            8.0       2024-07           2023-08-28   \n", "171           1.04           10.0       2024-07           2023-08-30   \n", "118           2.94           13.0       2024-07           2023-08-31   \n", "88           -0.14           12.0       2024-07           2023-09-05   \n", "263           0.65            2.0       2024-06           2023-09-04   \n", "172           1.15            8.0       2024-07           2023-08-23   \n", "\n", "    reporting_time    market_cap  \n", "166    after-hours  3.160887e+12  \n", "0      after-hours  7.716866e+11  \n", "167    after-hours  2.529962e+11  \n", "261     pre-market  2.007811e+11  \n", "168     pre-market  1.597315e+11  \n", "262    after-hours  1.401966e+11  \n", "114    after-hours  7.922927e+10  \n", "169    after-hours  6.648856e+10  \n", "225     pre-market  6.319707e+10  \n", "115    after-hours  6.175190e+10  \n", "226     pre-market  5.855210e+10  \n", "116    after-hours  5.444496e+10  \n", "117     pre-market  5.042232e+10  \n", "170    after-hours  3.451380e+10  \n", "227     pre-market  3.387054e+10  \n", "171    after-hours  3.256312e+10  \n", "118    after-hours  3.184542e+10  \n", "88     after-hours  3.030086e+10  \n", "263    after-hours  2.780128e+10  \n", "172    after-hours  2.745601e+10  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Lookup some upcoming earnings dates and sort them by market cap.\n", "\n", "earnings_calendar = obb.equity.calendar.earnings(\n", "    start_date=(datetime.now() + timedelta(days=1)).date(),\n", "    end_date=(datetime.now() + timed<PERSON>ta(days=14)).date(),\n", "    provider=\"nasdaq\",\n", ")\n", "\n", "earnings_calendar.sort_values(by=[\"market_cap\", \"num_estimates\"], ascending=False).head(\n", "    20\n", ")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "IzmLloIfTQJo", "outputId": "1c08fa80-eaf2-4548-8c27-70c82fac79ea"}, "outputs": [{"data": {"text/plain": ["'Last Price: $124.7001'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["['2024-08-23', '2024-08-30', '2024-09-06']"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Get the options chains data.\n", "\n", "symbol = \"NVDA\"  # This will not be evergreen, change the symbol based on a stock above.\n", "\n", "obb.user.preferences.output_type = \"OBBject\"  # To use the built-in options chains methods, we need to set the output type to OBBject.\n", "\n", "options = obb.derivatives.options.chains(symbol, provider=\"cboe\")\n", "\n", "last_price = options.results.underlying_price[0]\n", "\n", "display(f\"Last Price: ${last_price}\")\n", "\n", "display(options.results.expirations[:3])"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cost of Straddle: $14.65\n", "Cost as a % of Share Price: 11.7482%\n", "Upper Breakeven Price: $139.65\n", "Lower Breakeven Price: $109.35\n", "Implied Daily Move: 1.3982%\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Symbol</th>\n", "      <td>NVDA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Underlying Price</th>\n", "      <td>124.7001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Expiration</th>\n", "      <td>2024-08-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DTE</th>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Strike 1</th>\n", "      <td>125.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Strike 2</th>\n", "      <td>124.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Strike 1 Premium</th>\n", "      <td>7.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Strike 2 Premium</th>\n", "      <td>7.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Cost</th>\n", "      <td>14.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Cost Per<PERSON></th>\n", "      <td>11.7482</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Breakeven Upper</th>\n", "      <td>139.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Breakeven Upper Percent</th>\n", "      <td>11.9887</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Breakeven Lower</th>\n", "      <td>109.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Breakeven Lower Percent</th>\n", "      <td>-12.3096</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON>it</th>\n", "      <td>inf</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON></th>\n", "      <td>-14.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Payoff Ratio</th>\n", "      <td>inf</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        Long Straddle\n", "Symbol                           NVDA\n", "Underlying Price             124.7001\n", "Expiration                 2024-08-30\n", "DTE                                 8\n", "Strike 1                        125.0\n", "Strike 2                        124.0\n", "Strike 1 Premium                 7.55\n", "Strike 2 Premium                  7.1\n", "Cost                            14.65\n", "Cost Percent                  11.7482\n", "Breakeven Upper                139.65\n", "Breakeven Upper Percent       11.9887\n", "Breakeven Lower                109.35\n", "Breakeven Lower Percent      -12.3096\n", "Max Profit                        inf\n", "Max Loss                       -14.65\n", "Payoff Ratio                      inf"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Use the straddle method of the results object to get the straddle data and then calculate the implied move.\n", "\n", "straddle = options.results.straddle(days=options.results.expirations[1])\n", "straddle_price = straddle.loc[\"Cost\"].values[0]\n", "days = straddle.loc[\"DTE\"].values[0]\n", "upper_price = straddle.loc[\"Breakeven Upper\"].values[0]\n", "lower_price = straddle.loc[\"Breakeven Lower\"].values[0]\n", "\n", "implied_move = ((1 + straddle_price / last_price) ** (1 / days) - 1) * 100\n", "\n", "display(\n", "    f\"Cost of Straddle: ${round(straddle_price, 2)}\"\n", "    f\"\\nCost as a % of Share Price: {round((straddle_price/last_price) * 100, 4)}%\"\n", "    f\"\\nUpper Breakeven Price: ${upper_price}\"\n", "    f\"\\nLower Breakeven Price: ${lower_price}\"\n", "    f\"\\nImplied Daily Move: {round(implied_move, 4)}%\\n\"\n", ")\n", "\n", "display(straddle)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 0}