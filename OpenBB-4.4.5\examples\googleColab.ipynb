{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"fc6a4747cea243f4bac6a19c43264fec": {"model_module": "@jupyter-widgets/controls", "model_name": "DropdownModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DropdownModel", "_options_labels": ["Total Open Interest", "Call Open Interest", "Put Open Interest", "Total Volume", "Call Volume", "Put Volume"], "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "DropdownView", "description": "", "description_tooltip": null, "disabled": false, "index": 0, "layout": "IPY_MODEL_bf10b4a3831f4e6595398f2d62a0f7b2", "style": "IPY_MODEL_6095af63918f4075a891fc950c9790d3"}}, "bf10b4a3831f4e6595398f2d62a0f7b2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6095af63918f4075a891fc950c9790d3": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "cells": [{"cell_type": "markdown", "source": ["# Installing the OpenBB Platform in Google Colab\n", "\n", "This notebook will install the OpenBB Platform, fetch some data and prepare it for display as a bar chart.\n", "\n", "Sign up for a free account here: https://my.openbb.co"], "metadata": {"id": "xIOXTKkqBReO"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "xvU65bhqKNns"}, "outputs": [], "source": ["# Install the OpenBB Platform with all available extensions.\n", "# Messages indicating package version conflicts at the end of installation can be safely ignored.\n", "\n", "!pip install openbb[all]\n", "\n", "# There is also a nightly distribution available, openbb-nightly"]}, {"cell_type": "code", "source": ["# Before running this cell, restart the runtime by selecting, \"Restart runtime\", from the \"Runtime\" menu.\n", "\n", "# Import statements - for many scenarios, the only import needed will be `from openbb import obb`\n", "from typing import Literal\n", "from IPython.display import display\n", "from IPython.display import clear_output\n", "import ipywidgets as widgets\n", "import pandas as pd\n", "import pandas_ta as ta\n", "from datetime import datetime\n", "from plotly import graph_objects as go\n", "\n", "from openbb import obb"], "metadata": {"id": "_69FIu9YKRhI"}, "execution_count": 98, "outputs": []}, {"cell_type": "code", "source": ["# Login to OpenBB Hub to retrieve stored API keys.\n", "# https://my.openbb.co/app/platform/pat\n", "# https://my.openbb.co/app/platform/api-keys\n", "\n", "obb.account.login(pat=\"replace with your PAT\")\n", "\n", "# This is not required"], "metadata": {"id": "1OLsZHDYMBSS"}, "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": ["# Verify that the credentials from <PERSON><PERSON> were loaded successfully.\n", "\n", "obb.user.credentials"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gJ7FwTC6MTzv", "outputId": "b9d4e888-7f3f-4756-b1a4-ae438e61c2b5"}, "execution_count": 4, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Credentials\n", "\n", "alpha_vantage_api_key: **********\n", "benzinga_api_key: None\n", "biztoc_api_key: None\n", "fmp_api_key: **********\n", "fred_api_key: **********\n", "intrinio_api_key: **********\n", "nasdaq_api_key: **********\n", "polygon_api_key: **********\n", "tiingo_token: None\n", "tradingeconomics_api_key: None"]}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "code", "source": ["# Set the output preference, if desired. The examples below use Pandas DataFrames.\n", "\n", "obb.user.preferences.output_type = \"dataframe\""], "metadata": {"id": "27JtqRAQ2HTb"}, "execution_count": 67, "outputs": []}, {"cell_type": "code", "source": ["# Get Some Data\n", "symbol = \"SPY\"\n", "\n", "options = obb.derivatives.options.chains(symbol, provider=\"cboe\")\n", "\n", "options"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 444}, "id": "GWI_60zD3M3l", "outputId": "47338fab-8ab5-467f-c26e-44148c848c63"}, "execution_count": 88, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["         contract_symbol  expiration  strike option_type  volume   open  \\\n", "0     SPY231130C00387000  2023-11-30   387.0        call     7.0  67.60   \n", "1     SPY231130P00387000  2023-11-30   387.0         put     2.0   0.01   \n", "2     SPY231130C00388000  2023-11-30   388.0        call     1.0  66.47   \n", "3     SPY231130P00388000  2023-11-30   388.0         put     1.0   0.01   \n", "4     SPY231130C00389000  2023-11-30   389.0        call     0.0   0.00   \n", "...                  ...         ...     ...         ...     ...    ...   \n", "8215  SPY260116P00670000  2026-01-16   670.0         put     0.0   0.00   \n", "8216  SPY260116C00675000  2026-01-16   675.0        call     0.0   0.00   \n", "8217  SPY260116P00675000  2026-01-16   675.0         put     0.0   0.00   \n", "8218  SPY260116C00680000  2026-01-16   680.0        call     0.0   0.00   \n", "8219  SPY260116P00680000  2026-01-16   680.0         put     0.0   0.00   \n", "\n", "      open_interest   high    low  implied_volatility  ...  last_trade_price  \\\n", "0               5.0  67.60  66.55              0.0000  ...             66.55   \n", "1             290.0   0.01   0.01              0.0000  ...              0.01   \n", "2               0.0  66.47  66.47              0.0000  ...             66.47   \n", "3               1.0   0.01   0.01              0.0000  ...              0.01   \n", "4               0.0   0.00   0.00              0.0000  ...              0.00   \n", "...             ...    ...    ...                 ...  ...               ...   \n", "8215            0.0   0.00   0.00              0.2367  ...            216.04   \n", "8216            0.0   0.00   0.00              0.1387  ...              0.00   \n", "8217            0.0   0.00   0.00              0.2385  ...              0.00   \n", "8218            0.0   0.00   0.00              0.1411  ...              0.00   \n", "8219            0.0   0.00   0.00              0.2432  ...            223.91   \n", "\n", "           tick  prev_close  change  change_percent     rho  \\\n", "0          down       67.69  -1.135           -1.68  0.0003   \n", "1     no_change        0.00   0.005          100.00  0.0000   \n", "2          down       66.68  -0.210           -0.31  0.0003   \n", "3     no_change        0.00   0.005          100.00  0.0000   \n", "4     no_change       65.69   0.000            0.00  0.0003   \n", "...         ...         ...     ...             ...     ...   \n", "8215       down      215.44   0.000            0.00 -0.3427   \n", "8216  no_change        2.50   0.000            0.00  0.4385   \n", "8217  no_change      220.50   0.000            0.00 -0.3453   \n", "8218  no_change        2.50   0.000            0.00  0.4078   \n", "8219       down      225.50   0.000            0.00 -0.3478   \n", "\n", "      last_trade_timestamp  dte     bid     ask  \n", "0      2023-11-30 12:27:59   -1   67.77   70.67  \n", "1      2023-11-30 09:30:12   -1    0.00    0.01  \n", "2      2023-11-30 09:42:00   -1   67.56   68.77  \n", "3      2023-11-30 10:16:04   -1    0.00    0.01  \n", "4                      NaT   -1   65.77   68.67  \n", "...                    ...  ...     ...     ...  \n", "8215   2023-11-20 12:31:47  777  211.56  216.50  \n", "8216                   NaT  777    0.00    5.00  \n", "8217                   NaT  777  216.50  221.50  \n", "8218                   NaT  777    0.00    5.00  \n", "8219   2023-11-27 14:07:53  777  221.50  226.50  \n", "\n", "[8220 rows x 27 columns]"], "text/html": ["\n", "  <div id=\"df-2a8d8e0e-044c-4e66-bd79-582b5ef51f80\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>contract_symbol</th>\n", "      <th>expiration</th>\n", "      <th>strike</th>\n", "      <th>option_type</th>\n", "      <th>volume</th>\n", "      <th>open</th>\n", "      <th>open_interest</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>implied_volatility</th>\n", "      <th>...</th>\n", "      <th>last_trade_price</th>\n", "      <th>tick</th>\n", "      <th>prev_close</th>\n", "      <th>change</th>\n", "      <th>change_percent</th>\n", "      <th>rho</th>\n", "      <th>last_trade_timestamp</th>\n", "      <th>dte</th>\n", "      <th>bid</th>\n", "      <th>ask</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SPY231130C00387000</td>\n", "      <td>2023-11-30</td>\n", "      <td>387.0</td>\n", "      <td>call</td>\n", "      <td>7.0</td>\n", "      <td>67.60</td>\n", "      <td>5.0</td>\n", "      <td>67.60</td>\n", "      <td>66.55</td>\n", "      <td>0.0000</td>\n", "      <td>...</td>\n", "      <td>66.55</td>\n", "      <td>down</td>\n", "      <td>67.69</td>\n", "      <td>-1.135</td>\n", "      <td>-1.68</td>\n", "      <td>0.0003</td>\n", "      <td>2023-11-30 12:27:59</td>\n", "      <td>-1</td>\n", "      <td>67.77</td>\n", "      <td>70.67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SPY231130P00387000</td>\n", "      <td>2023-11-30</td>\n", "      <td>387.0</td>\n", "      <td>put</td>\n", "      <td>2.0</td>\n", "      <td>0.01</td>\n", "      <td>290.0</td>\n", "      <td>0.01</td>\n", "      <td>0.01</td>\n", "      <td>0.0000</td>\n", "      <td>...</td>\n", "      <td>0.01</td>\n", "      <td>no_change</td>\n", "      <td>0.00</td>\n", "      <td>0.005</td>\n", "      <td>100.00</td>\n", "      <td>0.0000</td>\n", "      <td>2023-11-30 09:30:12</td>\n", "      <td>-1</td>\n", "      <td>0.00</td>\n", "      <td>0.01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SPY231130C00388000</td>\n", "      <td>2023-11-30</td>\n", "      <td>388.0</td>\n", "      <td>call</td>\n", "      <td>1.0</td>\n", "      <td>66.47</td>\n", "      <td>0.0</td>\n", "      <td>66.47</td>\n", "      <td>66.47</td>\n", "      <td>0.0000</td>\n", "      <td>...</td>\n", "      <td>66.47</td>\n", "      <td>down</td>\n", "      <td>66.68</td>\n", "      <td>-0.210</td>\n", "      <td>-0.31</td>\n", "      <td>0.0003</td>\n", "      <td>2023-11-30 09:42:00</td>\n", "      <td>-1</td>\n", "      <td>67.56</td>\n", "      <td>68.77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SPY231130P00388000</td>\n", "      <td>2023-11-30</td>\n", "      <td>388.0</td>\n", "      <td>put</td>\n", "      <td>1.0</td>\n", "      <td>0.01</td>\n", "      <td>1.0</td>\n", "      <td>0.01</td>\n", "      <td>0.01</td>\n", "      <td>0.0000</td>\n", "      <td>...</td>\n", "      <td>0.01</td>\n", "      <td>no_change</td>\n", "      <td>0.00</td>\n", "      <td>0.005</td>\n", "      <td>100.00</td>\n", "      <td>0.0000</td>\n", "      <td>2023-11-30 10:16:04</td>\n", "      <td>-1</td>\n", "      <td>0.00</td>\n", "      <td>0.01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SPY231130C00389000</td>\n", "      <td>2023-11-30</td>\n", "      <td>389.0</td>\n", "      <td>call</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>no_change</td>\n", "      <td>65.69</td>\n", "      <td>0.000</td>\n", "      <td>0.00</td>\n", "      <td>0.0003</td>\n", "      <td>NaT</td>\n", "      <td>-1</td>\n", "      <td>65.77</td>\n", "      <td>68.67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8215</th>\n", "      <td>SPY260116P00670000</td>\n", "      <td>2026-01-16</td>\n", "      <td>670.0</td>\n", "      <td>put</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.2367</td>\n", "      <td>...</td>\n", "      <td>216.04</td>\n", "      <td>down</td>\n", "      <td>215.44</td>\n", "      <td>0.000</td>\n", "      <td>0.00</td>\n", "      <td>-0.3427</td>\n", "      <td>2023-11-20 12:31:47</td>\n", "      <td>777</td>\n", "      <td>211.56</td>\n", "      <td>216.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8216</th>\n", "      <td>SPY260116C00675000</td>\n", "      <td>2026-01-16</td>\n", "      <td>675.0</td>\n", "      <td>call</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.1387</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>no_change</td>\n", "      <td>2.50</td>\n", "      <td>0.000</td>\n", "      <td>0.00</td>\n", "      <td>0.4385</td>\n", "      <td>NaT</td>\n", "      <td>777</td>\n", "      <td>0.00</td>\n", "      <td>5.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8217</th>\n", "      <td>SPY260116P00675000</td>\n", "      <td>2026-01-16</td>\n", "      <td>675.0</td>\n", "      <td>put</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.2385</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>no_change</td>\n", "      <td>220.50</td>\n", "      <td>0.000</td>\n", "      <td>0.00</td>\n", "      <td>-0.3453</td>\n", "      <td>NaT</td>\n", "      <td>777</td>\n", "      <td>216.50</td>\n", "      <td>221.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8218</th>\n", "      <td>SPY260116C00680000</td>\n", "      <td>2026-01-16</td>\n", "      <td>680.0</td>\n", "      <td>call</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.1411</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>no_change</td>\n", "      <td>2.50</td>\n", "      <td>0.000</td>\n", "      <td>0.00</td>\n", "      <td>0.4078</td>\n", "      <td>NaT</td>\n", "      <td>777</td>\n", "      <td>0.00</td>\n", "      <td>5.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8219</th>\n", "      <td>SPY260116P00680000</td>\n", "      <td>2026-01-16</td>\n", "      <td>680.0</td>\n", "      <td>put</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.2432</td>\n", "      <td>...</td>\n", "      <td>223.91</td>\n", "      <td>down</td>\n", "      <td>225.50</td>\n", "      <td>0.000</td>\n", "      <td>0.00</td>\n", "      <td>-0.3478</td>\n", "      <td>2023-11-27 14:07:53</td>\n", "      <td>777</td>\n", "      <td>221.50</td>\n", "      <td>226.50</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8220 rows × 27 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-2a8d8e0e-044c-4e66-bd79-582b5ef51f80')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-2a8d8e0e-044c-4e66-bd79-582b5ef51f80 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-2a8d8e0e-044c-4e66-bd79-582b5ef51f80');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-9fe9272b-1929-45e6-bb98-bf3f44b552b7\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-9fe9272b-1929-45e6-bb98-bf3f44b552b7')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-9fe9272b-1929-45e6-bb98-bf3f44b552b7 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"]}, "metadata": {}, "execution_count": 88}]}, {"cell_type": "code", "source": ["# Prepare A View - Volume and Open Interest by Expiration or Strike\n", "\n", "def filter_options_data(options, by: Literal[\"expiration\", \"strike\"] = \"expiration\"):\n", "    data = pd.DataFrame()\n", "    data[\"Total Open Interest\"] = options.groupby(by)[\"open_interest\"].sum()\n", "    data[\"Call Open Interest\"] = options[options[\"option_type\"] == \"call\"].groupby(by)[\"open_interest\"].sum()\n", "    data[\"Put Open Interest\"] = options[options[\"option_type\"] == \"put\"].groupby(by)[\"open_interest\"].sum()\n", "    data[\"Total Volume\"] = options.groupby(by)[\"volume\"].sum()\n", "    data[\"Call Volume\"] = options[options[\"option_type\"] == \"call\"].groupby(by)[\"volume\"].sum()\n", "    data[\"Put Volume\"] = options[options[\"option_type\"] == \"put\"].groupby(by)[\"volume\"].sum()\n", "\n", "    return data\n", "\n", "data = filter_options_data(options, \"strike\")\n", "\n", "data"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 455}, "id": "F-SpeJUi3l1k", "outputId": "ee703998-297f-4831-8aad-422e16a5b3eb"}, "execution_count": 101, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["        Total Open Interest  Call Open Interest  Put Open Interest  \\\n", "strike                                                               \n", "120.0               22821.0                74.0            22747.0   \n", "130.0                1506.0                19.0             1487.0   \n", "140.0                 488.0                 2.0              486.0   \n", "150.0               72684.0              2072.0            70612.0   \n", "155.0               12298.0                18.0            12280.0   \n", "...                     ...                 ...                ...   \n", "700.0               24945.0             24835.0              110.0   \n", "705.0                 478.0               478.0                0.0   \n", "710.0                1511.0              1511.0                0.0   \n", "715.0                 981.0               981.0                0.0   \n", "720.0              102823.0            102821.0                2.0   \n", "\n", "        Total Volume  Call Volume  Put Volume  \n", "strike                                         \n", "120.0           78.0          0.0        78.0  \n", "130.0           61.0          0.0        61.0  \n", "140.0            1.0          0.0         1.0  \n", "150.0           65.0          4.0        61.0  \n", "155.0           21.0          1.0        20.0  \n", "...              ...          ...         ...  \n", "700.0            2.0          2.0         0.0  \n", "705.0            0.0          0.0         0.0  \n", "710.0            3.0          1.0         2.0  \n", "715.0            0.0          0.0         0.0  \n", "720.0           36.0         36.0         0.0  \n", "\n", "[272 rows x 6 columns]"], "text/html": ["\n", "  <div id=\"df-02f2b63a-6a85-4d2c-b3bf-21981cfe56e6\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Total Open Interest</th>\n", "      <th>Call Open Interest</th>\n", "      <th>Put Open Interest</th>\n", "      <th>Total Volume</th>\n", "      <th>Call Volume</th>\n", "      <th>Put Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>strike</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>120.0</th>\n", "      <td>22821.0</td>\n", "      <td>74.0</td>\n", "      <td>22747.0</td>\n", "      <td>78.0</td>\n", "      <td>0.0</td>\n", "      <td>78.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>130.0</th>\n", "      <td>1506.0</td>\n", "      <td>19.0</td>\n", "      <td>1487.0</td>\n", "      <td>61.0</td>\n", "      <td>0.0</td>\n", "      <td>61.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>140.0</th>\n", "      <td>488.0</td>\n", "      <td>2.0</td>\n", "      <td>486.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>150.0</th>\n", "      <td>72684.0</td>\n", "      <td>2072.0</td>\n", "      <td>70612.0</td>\n", "      <td>65.0</td>\n", "      <td>4.0</td>\n", "      <td>61.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>155.0</th>\n", "      <td>12298.0</td>\n", "      <td>18.0</td>\n", "      <td>12280.0</td>\n", "      <td>21.0</td>\n", "      <td>1.0</td>\n", "      <td>20.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>700.0</th>\n", "      <td>24945.0</td>\n", "      <td>24835.0</td>\n", "      <td>110.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>705.0</th>\n", "      <td>478.0</td>\n", "      <td>478.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>710.0</th>\n", "      <td>1511.0</td>\n", "      <td>1511.0</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>715.0</th>\n", "      <td>981.0</td>\n", "      <td>981.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>720.0</th>\n", "      <td>102823.0</td>\n", "      <td>102821.0</td>\n", "      <td>2.0</td>\n", "      <td>36.0</td>\n", "      <td>36.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>272 rows × 6 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-02f2b63a-6a85-4d2c-b3bf-21981cfe56e6')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-02f2b63a-6a85-4d2c-b3bf-21981cfe56e6 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-02f2b63a-6a85-4d2c-b3bf-21981cfe56e6');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-77122ec2-11de-4cce-a82a-480f982b0f10\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-77122ec2-11de-4cce-a82a-480f982b0f10')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-77122ec2-11de-4cce-a82a-480f982b0f10 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"]}, "metadata": {}, "execution_count": 101}]}, {"cell_type": "code", "source": ["# Do not run this cell if you are following the example above.\n", "\n", "# In this scenario, \"data\" could be anything that would be displayed as a bar chart. Alternatively, it could be company fundamentals data.\n", "\n", "# Note: This requires a valid FMP API key\n", "\n", "\n", "symbol=\"AAPL\"\n", "\n", "data = obb.equity.fundamental.ratios(symbol, limit = 100, period=\"quarter\", provider=\"fmp\")\n", "\n", "data.index = data.index.strftime(\"%Y-%m-%d\")\n", "\n", "data"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 649}, "id": "2fAclYaEMVz5", "outputId": "10413c8a-253d-4aae-c837-644f8880923b"}, "execution_count": 93, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["           symbol period  current_ratio  quick_ratio  cash_ratio  \\\n", "date                                                               \n", "1998-12-26   AAPL     Q1       2.605795     2.352426    0.822776   \n", "1999-03-27   AAPL     Q2       2.650259     2.413212    0.879534   \n", "1999-06-26   AAPL     Q3       2.792723     2.600390    1.152047   \n", "1999-09-25   AAPL     Q4       2.766301     2.522272    0.856036   \n", "2000-01-01   AAPL     Q1       2.498219     2.316539    0.807125   \n", "...           ...    ...            ...          ...         ...   \n", "2022-09-24   AAPL     Q4       0.879356     0.709408    0.153563   \n", "2022-12-31   AAPL     Q1       0.938020     0.768724    0.149578   \n", "2023-04-01   AAPL     Q2       0.940354     0.764281    0.205597   \n", "2023-07-01   AAPL     Q3       0.981563     0.813585    0.227331   \n", "2023-09-30   AAPL     Q4       0.988012     0.843312    0.206217   \n", "\n", "            days_of_sales_outstanding  days_of_inventory_outstanding  \\\n", "date                                                                   \n", "1998-12-26                  48.052632                       1.832248   \n", "1999-03-27                  47.294118                       1.437445   \n", "1999-06-26                  51.758665                       0.557029   \n", "1999-09-25                  45.875749                       1.890756   \n", "2000-01-01                  34.263764                       0.777650   \n", "...                               ...                            ...   \n", "2022-09-24                  60.833315                       8.551997   \n", "2022-12-31                  41.622138                       9.185598   \n", "2023-04-01                  34.068392                      12.738933   \n", "2023-07-01                  43.115762                      14.577604   \n", "2023-09-30                  61.327069                      11.611542   \n", "\n", "            operating_cycle  days_of_payables_outstanding  \\\n", "date                                                        \n", "1998-12-26        49.884879                     48.004886   \n", "1999-03-27        48.731562                     63.167702   \n", "1999-06-26        52.315694                     63.023873   \n", "1999-09-25        47.766505                     76.764706   \n", "2000-01-01        35.041414                     60.864055   \n", "...                     ...                           ...   \n", "2022-09-24        69.385312                    110.859542   \n", "2022-12-31        50.807736                     78.007542   \n", "2023-04-01        46.807325                     73.118615   \n", "2023-07-01        57.693367                     92.607747   \n", "2023-09-30        72.938611                    114.833405   \n", "\n", "            cash_conversion_cycle  ...  price_earnings_ratio  \\\n", "date                               ...                         \n", "1998-12-26               1.879993  ...              8.732568   \n", "1999-03-27             -14.436140  ...              8.397059   \n", "1999-06-26             -10.708179  ...              7.484225   \n", "1999-09-25             -28.998201  ...             23.552897   \n", "2000-01-01             -25.822641  ...             22.618570   \n", "...                           ...  ...                   ...   \n", "2022-09-24             -41.474230  ...             29.094281   \n", "2022-12-31             -27.199806  ...             17.208993   \n", "2023-04-01             -26.311290  ...             26.938138   \n", "2023-07-01             -34.914381  ...             38.288645   \n", "2023-09-30             -41.894793  ...             29.085850   \n", "\n", "            price_to_free_cash_flows_ratio  \\\n", "date                                         \n", "1998-12-26                       24.355053   \n", "1999-03-27                       18.137648   \n", "1999-06-26                       75.027047   \n", "1999-09-25                       51.769733   \n", "2000-01-01                       49.423264   \n", "...                                    ...   \n", "2022-09-24                      115.723695   \n", "2022-12-31                       68.334817   \n", "2023-04-01                      101.516990   \n", "2023-07-01                      125.370206   \n", "2023-09-30                      137.421101   \n", "\n", "            price_to_operating_cash_flows_ratio  price_cash_flow_ratio  \\\n", "date                                                                     \n", "1998-12-26                            23.808976              23.808976   \n", "1999-03-27                            16.856551              16.856551   \n", "1999-06-26                            69.058986              69.058986   \n", "1999-09-25                            47.970120              47.970120   \n", "2000-01-01                            44.388186              44.388186   \n", "...                                         ...                    ...   \n", "2022-09-24                            99.948206              99.948206   \n", "2022-12-31                            60.724643              60.724643   \n", "2023-04-01                            91.152020              91.152020   \n", "2023-07-01                           115.423282             115.423282   \n", "2023-09-30                           123.658630             123.658630   \n", "\n", "            price_earnings_to_growth_ratio  price_sales_ratio  dividend_yield  \\\n", "date                                                                            \n", "1998-12-26                        0.203760           3.104913        0.000000   \n", "1999-03-27                       -0.699755           2.963668        0.000000   \n", "1999-06-26                        0.178003           3.900636        0.000000   \n", "1999-09-25                       -0.467319           7.827460        0.000000   \n", "2000-01-01                        0.350588           7.066493        0.000000   \n", "...                                    ...                ...             ...   \n", "2022-09-24                        3.879237          26.750498        0.001536   \n", "2022-12-31                        0.369993          17.625873        0.001825   \n", "2023-04-01                       -1.414252          27.450564        0.001402   \n", "2023-07-01                       -2.253139          37.224668        0.001264   \n", "2023-09-30                        1.846951          29.841774        0.001407   \n", "\n", "            enterprise_value_multiple  price_fair_value  calendarYear  \n", "date                                                                   \n", "1998-12-26                  28.650009          2.760999          1999  \n", "1999-03-27                  62.597153          2.084787          1999  \n", "1999-06-26                 209.281398          2.046192          1999  \n", "1999-09-25                  70.913430          3.369035          1999  \n", "2000-01-01                  58.063853          3.750973          2000  \n", "...                               ...               ...           ...  \n", "2022-09-24                  90.344514         47.589406          2022  \n", "2022-12-31                  55.366190         36.401387          2023  \n", "2023-04-01                  86.117046         41.882005          2023  \n", "2023-07-01                 135.913479         50.517075          2023  \n", "2023-09-30                  92.900618         42.975881          2023  \n", "\n", "[100 rows x 57 columns]"], "text/html": ["\n", "  <div id=\"df-7cf38376-8f1c-43b9-9d1a-578e0f267575\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>period</th>\n", "      <th>current_ratio</th>\n", "      <th>quick_ratio</th>\n", "      <th>cash_ratio</th>\n", "      <th>days_of_sales_outstanding</th>\n", "      <th>days_of_inventory_outstanding</th>\n", "      <th>operating_cycle</th>\n", "      <th>days_of_payables_outstanding</th>\n", "      <th>cash_conversion_cycle</th>\n", "      <th>...</th>\n", "      <th>price_earnings_ratio</th>\n", "      <th>price_to_free_cash_flows_ratio</th>\n", "      <th>price_to_operating_cash_flows_ratio</th>\n", "      <th>price_cash_flow_ratio</th>\n", "      <th>price_earnings_to_growth_ratio</th>\n", "      <th>price_sales_ratio</th>\n", "      <th>dividend_yield</th>\n", "      <th>enterprise_value_multiple</th>\n", "      <th>price_fair_value</th>\n", "      <th>calendarYear</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1998-12-26</th>\n", "      <td>AAPL</td>\n", "      <td>Q1</td>\n", "      <td>2.605795</td>\n", "      <td>2.352426</td>\n", "      <td>0.822776</td>\n", "      <td>48.052632</td>\n", "      <td>1.832248</td>\n", "      <td>49.884879</td>\n", "      <td>48.004886</td>\n", "      <td>1.879993</td>\n", "      <td>...</td>\n", "      <td>8.732568</td>\n", "      <td>24.355053</td>\n", "      <td>23.808976</td>\n", "      <td>23.808976</td>\n", "      <td>0.203760</td>\n", "      <td>3.104913</td>\n", "      <td>0.000000</td>\n", "      <td>28.650009</td>\n", "      <td>2.760999</td>\n", "      <td>1999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-03-27</th>\n", "      <td>AAPL</td>\n", "      <td>Q2</td>\n", "      <td>2.650259</td>\n", "      <td>2.413212</td>\n", "      <td>0.879534</td>\n", "      <td>47.294118</td>\n", "      <td>1.437445</td>\n", "      <td>48.731562</td>\n", "      <td>63.167702</td>\n", "      <td>-14.436140</td>\n", "      <td>...</td>\n", "      <td>8.397059</td>\n", "      <td>18.137648</td>\n", "      <td>16.856551</td>\n", "      <td>16.856551</td>\n", "      <td>-0.699755</td>\n", "      <td>2.963668</td>\n", "      <td>0.000000</td>\n", "      <td>62.597153</td>\n", "      <td>2.084787</td>\n", "      <td>1999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-06-26</th>\n", "      <td>AAPL</td>\n", "      <td>Q3</td>\n", "      <td>2.792723</td>\n", "      <td>2.600390</td>\n", "      <td>1.152047</td>\n", "      <td>51.758665</td>\n", "      <td>0.557029</td>\n", "      <td>52.315694</td>\n", "      <td>63.023873</td>\n", "      <td>-10.708179</td>\n", "      <td>...</td>\n", "      <td>7.484225</td>\n", "      <td>75.027047</td>\n", "      <td>69.058986</td>\n", "      <td>69.058986</td>\n", "      <td>0.178003</td>\n", "      <td>3.900636</td>\n", "      <td>0.000000</td>\n", "      <td>209.281398</td>\n", "      <td>2.046192</td>\n", "      <td>1999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-09-25</th>\n", "      <td>AAPL</td>\n", "      <td>Q4</td>\n", "      <td>2.766301</td>\n", "      <td>2.522272</td>\n", "      <td>0.856036</td>\n", "      <td>45.875749</td>\n", "      <td>1.890756</td>\n", "      <td>47.766505</td>\n", "      <td>76.764706</td>\n", "      <td>-28.998201</td>\n", "      <td>...</td>\n", "      <td>23.552897</td>\n", "      <td>51.769733</td>\n", "      <td>47.970120</td>\n", "      <td>47.970120</td>\n", "      <td>-0.467319</td>\n", "      <td>7.827460</td>\n", "      <td>0.000000</td>\n", "      <td>70.913430</td>\n", "      <td>3.369035</td>\n", "      <td>1999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2000-01-01</th>\n", "      <td>AAPL</td>\n", "      <td>Q1</td>\n", "      <td>2.498219</td>\n", "      <td>2.316539</td>\n", "      <td>0.807125</td>\n", "      <td>34.263764</td>\n", "      <td>0.777650</td>\n", "      <td>35.041414</td>\n", "      <td>60.864055</td>\n", "      <td>-25.822641</td>\n", "      <td>...</td>\n", "      <td>22.618570</td>\n", "      <td>49.423264</td>\n", "      <td>44.388186</td>\n", "      <td>44.388186</td>\n", "      <td>0.350588</td>\n", "      <td>7.066493</td>\n", "      <td>0.000000</td>\n", "      <td>58.063853</td>\n", "      <td>3.750973</td>\n", "      <td>2000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-24</th>\n", "      <td>AAPL</td>\n", "      <td>Q4</td>\n", "      <td>0.879356</td>\n", "      <td>0.709408</td>\n", "      <td>0.153563</td>\n", "      <td>60.833315</td>\n", "      <td>8.551997</td>\n", "      <td>69.385312</td>\n", "      <td>110.859542</td>\n", "      <td>-41.474230</td>\n", "      <td>...</td>\n", "      <td>29.094281</td>\n", "      <td>115.723695</td>\n", "      <td>99.948206</td>\n", "      <td>99.948206</td>\n", "      <td>3.879237</td>\n", "      <td>26.750498</td>\n", "      <td>0.001536</td>\n", "      <td>90.344514</td>\n", "      <td>47.589406</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-12-31</th>\n", "      <td>AAPL</td>\n", "      <td>Q1</td>\n", "      <td>0.938020</td>\n", "      <td>0.768724</td>\n", "      <td>0.149578</td>\n", "      <td>41.622138</td>\n", "      <td>9.185598</td>\n", "      <td>50.807736</td>\n", "      <td>78.007542</td>\n", "      <td>-27.199806</td>\n", "      <td>...</td>\n", "      <td>17.208993</td>\n", "      <td>68.334817</td>\n", "      <td>60.724643</td>\n", "      <td>60.724643</td>\n", "      <td>0.369993</td>\n", "      <td>17.625873</td>\n", "      <td>0.001825</td>\n", "      <td>55.366190</td>\n", "      <td>36.401387</td>\n", "      <td>2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-04-01</th>\n", "      <td>AAPL</td>\n", "      <td>Q2</td>\n", "      <td>0.940354</td>\n", "      <td>0.764281</td>\n", "      <td>0.205597</td>\n", "      <td>34.068392</td>\n", "      <td>12.738933</td>\n", "      <td>46.807325</td>\n", "      <td>73.118615</td>\n", "      <td>-26.311290</td>\n", "      <td>...</td>\n", "      <td>26.938138</td>\n", "      <td>101.516990</td>\n", "      <td>91.152020</td>\n", "      <td>91.152020</td>\n", "      <td>-1.414252</td>\n", "      <td>27.450564</td>\n", "      <td>0.001402</td>\n", "      <td>86.117046</td>\n", "      <td>41.882005</td>\n", "      <td>2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-07-01</th>\n", "      <td>AAPL</td>\n", "      <td>Q3</td>\n", "      <td>0.981563</td>\n", "      <td>0.813585</td>\n", "      <td>0.227331</td>\n", "      <td>43.115762</td>\n", "      <td>14.577604</td>\n", "      <td>57.693367</td>\n", "      <td>92.607747</td>\n", "      <td>-34.914381</td>\n", "      <td>...</td>\n", "      <td>38.288645</td>\n", "      <td>125.370206</td>\n", "      <td>115.423282</td>\n", "      <td>115.423282</td>\n", "      <td>-2.253139</td>\n", "      <td>37.224668</td>\n", "      <td>0.001264</td>\n", "      <td>135.913479</td>\n", "      <td>50.517075</td>\n", "      <td>2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-30</th>\n", "      <td>AAPL</td>\n", "      <td>Q4</td>\n", "      <td>0.988012</td>\n", "      <td>0.843312</td>\n", "      <td>0.206217</td>\n", "      <td>61.327069</td>\n", "      <td>11.611542</td>\n", "      <td>72.938611</td>\n", "      <td>114.833405</td>\n", "      <td>-41.894793</td>\n", "      <td>...</td>\n", "      <td>29.085850</td>\n", "      <td>137.421101</td>\n", "      <td>123.658630</td>\n", "      <td>123.658630</td>\n", "      <td>1.846951</td>\n", "      <td>29.841774</td>\n", "      <td>0.001407</td>\n", "      <td>92.900618</td>\n", "      <td>42.975881</td>\n", "      <td>2023</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>100 rows × 57 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-7cf38376-8f1c-43b9-9d1a-578e0f267575')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-7cf38376-8f1c-43b9-9d1a-578e0f267575 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-7cf38376-8f1c-43b9-9d1a-578e0f267575');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-40497031-c39d-486f-975c-a6b353d3d6f1\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-40497031-c39d-486f-975c-a6b353d3d6f1')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-40497031-c39d-486f-975c-a6b353d3d6f1 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"]}, "metadata": {}, "execution_count": 93}]}, {"cell_type": "code", "source": ["# Create a widget for selecting the data to display.\n", "\n", "clear_output(wait = False)\n", "\n", "data_choices = data.columns.tolist()\n", "data_selection = widgets.Dropdown(\n", "    options = data_choices,\n", "    value = None,\n", ")\n", "output = widgets.Output()\n", "\n", "\n", "def generate_figure(data, data_choice):\n", "    data = data[data[data_choice].notnull()]\n", "    fig = go.Figure()\n", "    fig.add_bar(\n", "        y = data[data_choice][data[data_choice] > 0].values,\n", "        x = data[data_choice][data[data_choice] > 0].index,\n", "        name = data_choice,\n", "        marker = dict(color = \"blue\"),\n", "    )\n", "    fig.add_bar(\n", "        y = data[data_choice][data[data_choice] < 0].values,\n", "        x = data[data_choice][data[data_choice] < 0].index,\n", "        name = data_choice,\n", "        marker = dict(color = \"red\")\n", "    )\n", "    fig.update_xaxes(type=\"category\")\n", "    fig.update_traces(width=0.98, selector=dict(type=\"bar\"))\n", "    fig.update_layout(\n", "        showlegend=False,\n", "        width=1400,\n", "        height=600,\n", "        title = dict(\n", "            text=f\"{symbol} {data_choice.replace('_', ' ').title()}\",\n", "            xanchor = \"center\",\n", "            x = 0.5,\n", "            font = dict(size = 20)\n", "        ),\n", "        barmode=\"overlay\",\n", "        bargap=0,\n", "        bargroupgap=0,\n", "        yaxis=dict(\n", "            ticklen=0,\n", "            showgrid=True,\n", "            tickfont=dict(size=14),\n", "        ),\n", "        xaxis=dict(\n", "            showgrid=False,\n", "            autorange=True,\n", "            tickangle=90,\n", "            tickfont=dict(size=11),\n", "        ),\n", "    )\n", "    return fig\n", "\n", "def on_value_change(change):\n", "    clear_output(wait = True)\n", "    display(data_selection)\n", "    with output:\n", "        data_selection.value\n", "\n", "data_selection.observe(on_value_change, names=\"value\")\n", "display(data_selection)\n", "\n", "# Select from the drop-down menu below."], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 49, "referenced_widgets": ["fc6a4747cea243f4bac6a19c43264fec", "bf10b4a3831f4e6595398f2d62a0f7b2", "6095af63918f4075a891fc950c9790d3"]}, "id": "jvvAtHfvMkXB", "outputId": "6155ecd8-5bec-4fb7-ef77-52b3bb325af7"}, "execution_count": 102, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Dropdown(options=('Total Open Interest', 'Call Open Interest', 'Put Open Interest', 'Total Volume', 'Call Volu…"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "fc6a4747cea243f4bac6a19c43264fec"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["# Play this cell to display the choice\n", "\n", "if data_selection.value is not None:\n", "\n", "    generate_figure(data, data_selection.value).show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 617}, "id": "J9D5Qq0sQOwH", "outputId": "91092fb7-c313-4305-eed2-00d6b5750a8b"}, "execution_count": 106, "outputs": [{"output_type": "display_data", "data": {"text/html": ["<html>\n", "<head><meta charset=\"utf-8\" /></head>\n", "<body>\n", "    <div>            <script src=\"https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.5/MathJax.js?config=TeX-AMS-MML_SVG\"></script><script type=\"text/javascript\">if (window.MathJax && window.MathJax.Hub && window.MathJax.Hub.Config) {window.MathJax.Hub.Config({SVG: {font: \"STIX-Web\"}});}</script>                <script type=\"text/javascript\">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>\n", "        <script charset=\"utf-8\" src=\"https://cdn.plot.ly/plotly-2.27.0.min.js\"></script>                <div id=\"bd364da0-3ff3-405a-a34f-67ffc2e881f9\" class=\"plotly-graph-div\" style=\"height:600px; width:1400px;\"></div>            <script type=\"text/javascript\">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"bd364da0-3ff3-405a-a34f-67ffc2e881f9\")) {                    Plotly.newPlot(                        \"bd364da0-3ff3-405a-a34f-67ffc2e881f9\",                        [{\"marker\":{\"color\":\"blue\"},\"name\":\"Total Open Interest\",\"x\":[120.0,130.0,140.0,150.0,155.0,160.0,165.0,170.0,175.0,180.0,185.0,190.0,195.0,200.0,205.0,210.0,215.0,220.0,225.0,230.0,235.0,240.0,245.0,250.0,255.0,260.0,265.0,270.0,275.0,280.0,285.0,290.0,295.0,300.0,305.0,310.0,315.0,320.0,325.0,326.0,327.0,328.0,329.0,330.0,331.0,332.0,333.0,334.0,335.0,336.0,337.0,338.0,339.0,340.0,341.0,342.0,343.0,344.0,345.0,346.0,347.0,348.0,349.0,350.0,351.0,352.0,353.0,354.0,355.0,356.0,357.0,358.0,359.0,360.0,361.0,362.0,363.0,364.0,365.0,366.0,367.0,368.0,369.0,370.0,371.0,372.0,373.0,374.0,375.0,376.0,377.0,378.0,379.0,380.0,381.0,382.0,383.0,384.0,385.0,386.0,387.0,388.0,389.0,390.0,391.0,392.0,393.0,394.0,395.0,396.0,397.0,398.0,399.0,400.0,401.0,402.0,403.0,404.0,405.0,406.0,407.0,407.5,408.0,409.0,410.0,411.0,412.0,412.5,413.0,414.0,415.0,416.0,417.0,417.5,418.0,419.0,420.0,421.0,422.0,422.5,423.0,424.0,425.0,426.0,427.0,427.5,428.0,429.0,430.0,431.0,432.0,432.5,433.0,434.0,435.0,436.0,437.0,437.5,438.0,439.0,440.0,441.0,442.0,442.5,443.0,444.0,445.0,446.0,447.0,447.5,448.0,449.0,450.0,451.0,452.0,452.5,453.0,454.0,455.0,456.0,457.0,457.5,458.0,459.0,460.0,461.0,462.0,462.5,463.0,464.0,465.0,466.0,467.0,468.0,469.0,470.0,471.0,472.0,473.0,474.0,475.0,476.0,477.0,478.0,479.0,480.0,481.0,482.0,483.0,484.0,485.0,486.0,487.0,488.0,489.0,490.0,491.0,492.0,493.0,494.0,495.0,496.0,497.0,498.0,499.0,500.0,501.0,505.0,510.0,515.0,520.0,525.0,530.0,535.0,540.0,545.0,550.0,555.0,560.0,565.0,570.0,575.0,580.0,585.0,590.0,595.0,600.0,605.0,610.0,615.0,620.0,625.0,630.0,635.0,640.0,645.0,650.0,655.0,660.0,665.0,670.0,675.0,680.0,685.0,690.0,695.0,700.0,705.0,710.0,715.0,720.0],\"y\":[22821.0,1506.0,488.0,72684.0,12298.0,10083.0,10038.0,8360.0,9993.0,37606.0,32521.0,29836.0,20895.0,85139.0,24243.0,36551.0,26012.0,56622.0,23829.0,33532.0,17942.0,60632.0,35338.0,56527.0,18032.0,30316.0,30396.0,50209.0,61207.0,37026.0,50770.0,45185.0,65071.0,159070.0,53484.0,60967.0,57744.0,129600.0,109020.0,2146.0,722.0,2169.0,1340.0,133922.0,1021.0,600.0,6250.0,3915.0,98360.0,2251.0,1611.0,1133.0,1853.0,233517.0,2456.0,2804.0,2161.0,1495.0,209334.0,3899.0,5856.0,2493.0,4623.0,351448.0,6703.0,3386.0,3971.0,9834.0,239268.0,11716.0,7837.0,10534.0,13891.0,346649.0,5110.0,6728.0,4857.0,6349.0,241408.0,7327.0,17883.0,5797.0,10619.0,343956.0,7276.0,10860.0,24680.0,6971.0,285228.0,34212.0,8126.0,18856.0,12456.0,396584.0,12257.0,13430.0,15293.0,8502.0,355884.0,28496.0,8962.0,18364.0,10199.0,387739.0,19228.0,26884.0,38312.0,18468.0,317318.0,17398.0,52484.0,43708.0,20780.0,837400.0,30090.0,68012.0,59031.0,37417.0,479691.0,36668.0,72792.0,6570.0,54367.0,33032.0,413051.0,52289.0,72313.0,5688.0,86823.0,85569.0,384574.0,99860.0,47672.0,2956.0,48813.0,60772.0,573979.0,58836.0,87094.0,7284.0,83241.0,86451.0,325378.0,54454.0,82792.0,8865.0,74976.0,58801.0,554627.0,78622.0,134847.0,9534.0,84599.0,98230.0,388402.0,79630.0,78821.0,16208.0,103767.0,85884.0,579060.0,84556.0,94083.0,9933.0,84010.0,107284.0,419216.0,104896.0,157843.0,13630.0,140469.0,116466.0,580880.0,103277.0,140617.0,419.0,135349.0,135063.0,464252.0,155421.0,151442.0,264.0,132704.0,86808.0,540118.0,70502.0,95487.0,464.0,47076.0,58790.0,216515.0,45784.0,43140.0,36199.0,24123.0,227538.0,41618.0,39258.0,24366.0,25858.0,149346.0,33728.0,20640.0,22863.0,13496.0,256224.0,17288.0,18526.0,13245.0,9592.0,78595.0,6223.0,10567.0,6595.0,4966.0,102532.0,7202.0,4953.0,14529.0,11969.0,59821.0,11719.0,3194.0,5977.0,5599.0,149694.0,6120.0,79019.0,83626.0,66910.0,100439.0,58070.0,82737.0,45110.0,49113.0,40460.0,101677.0,57058.0,41655.0,32809.0,32979.0,22414.0,52753.0,8495.0,8062.0,7546.0,82029.0,10335.0,6059.0,10055.0,3992.0,10612.0,12064.0,5544.0,1155.0,2855.0,11228.0,40848.0,72462.0,16886.0,38843.0,5235.0,4188.0,3506.0,2461.0,2047.0,24945.0,478.0,1511.0,981.0,102823.0],\"type\":\"bar\",\"width\":0.98},{\"marker\":{\"color\":\"red\"},\"name\":\"Total Open Interest\",\"x\":[],\"y\":[],\"type\":\"bar\",\"width\":0.98}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"type\":\"category\",\"tickfont\":{\"size\":11},\"showgrid\":false,\"autorange\":true,\"tickangle\":90},\"title\":{\"font\":{\"size\":20},\"text\":\"AAPL Total Open Interest\",\"xanchor\":\"center\",\"x\":0.5},\"yaxis\":{\"tickfont\":{\"size\":14},\"ticklen\":0,\"showgrid\":true},\"showlegend\":false,\"width\":1400,\"height\":600,\"barmode\":\"overlay\",\"bargap\":0,\"bargroupgap\":0},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('bd364da0-3ff3-405a-a34f-67ffc2e881f9');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                            </script>        </div>\n", "</body>\n", "</html>"]}, "metadata": {}}]}]}