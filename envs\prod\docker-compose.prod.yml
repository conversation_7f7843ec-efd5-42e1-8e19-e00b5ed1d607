version: "3.9"
services:
  db:
    image: postgres:16
    environment:
      POSTGRES_DB: openbb_cn
      POSTGRES_USER: openbb
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - db_data_prod:/var/lib/postgresql/data

  api:
    build: ../..
    env_file:
      - ../../.env
    environment:
      - DATABASE_URL=postgresql+psycopg2://openbb:${POSTGRES_PASSWORD}@db:5432/openbb_cn
      - APP_HOST=0.0.0.0
      - APP_PORT=6900
    depends_on:
      - db
    ports:
      - "6900:6900"
    command: ["uvicorn", "openbb_cn.api.app:app", "--host", "0.0.0.0", "--port", "6900"]

volumes:
  db_data_prod:

