{"port1201": "Singapore - Singapore", "port1114": "Rotterdam - The Netherlands", "port1188": "Shanghai - China", "port57": "Antwerp - Belgium", "port1065": "Busan - Korea", "port824": "Ningbo - China", "port474": "Hong Kong - Hong Kong SAR", "port786": "Nagoya Aichi - Japan", "port1417": "Yokohama - Japan", "port752": "Mizushima - Japan", "port541": "Kaohsiung - Taiwan Province of China", "port239": "Chiba - Japan", "port1297": "Tianjin Xin Gang - China", "port1338": "Ulsan - Korea", "port1069": "Qingdao - China", "port960": "Port Klang - Malaysia", "port581": "Kobe - Japan", "port1128": "Sakaisenboku - Japan", "port425": "Guangzhou - China", "port1291": "Saigon - Vietnam", "port514": "Tanjung Priok - Indonesia", "port1305": "Tokyo - Japan", "port432": "Gwangyang (Kwangyang) - Korea", "port862": "Osaka - Japan", "port45": "Amsterdam - The Netherlands", "port694": "Manila - Philippines", "port504": "Istanbul - Turkiye", "port1253": "Taicang (Suzhou) - China", "port1404": "Xiamen - China", "port846": "Oita - Japan", "port744": "<PERSON><PERSON> - United Arab Emirates", "port481": "Houston - United States", "port555": "Kawasaki - Japan", "port1416": "Yokkaichi - Japan", "port434": "Hai <PERSON>ong - Vietnam", "port1304": "Tokuyama - Japan", "port446": "Hamburg - Germany", "port273": "Dalian - China", "port494": "Incheon - Korea", "port548": "Kashima Ibaraki - Japan", "port776": "Mumbai-<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>) - India", "port111": "Bangkok - Thailand", "port1429": "Zhoushan - China", "port1237": "<PERSON><PERSON><PERSON> - Indonesia", "port1308": "Tomakomai - Japan", "port493": "Immingham - United Kingdom", "port31": "Algeciras - Spain", "port1378": "<PERSON><PERSON><PERSON><PERSON> (Kitakyushu) - Japan", "port1252": "Taichung - Taiwan Province of China", "port1073": "Qiwei - China", "port538": "Kanda Fukuoka - Japan", "port393": "Ghent - Belgium", "port439": "Hakata - Japan", "port1269": "Tanjung Pelepas - Malaysia", "port908": "Piraeus - Greece", "port1284": "Terneuzen - The Netherlands", "port1067": "Pyeongtaek - Korea", "port812": "New Orleans - United States", "port2027": "Yangshan (Shangai) - China", "port1160": "Santos - Brazil", "port1197": "<PERSON><PERSON> - Thailand", "port364": "<PERSON><PERSON><PERSON> Kagoshima - Japan", "port805": "Nemrut Bay - Turkiye", "port465": "Himeji - Japan", "port517": "Jiangyin - China", "port1424": "Zeebrugge - Belgium", "port420": "Gresik - Indonesia", "port1335": "Ube - Japan", "port635": "Las Palmas (de Gran Canaria) - Spain", "port1379": "Wakayama-Shimotsu - Japan", "port1195": "<PERSON><PERSON><PERSON><PERSON> - <PERSON>", "port365": "Funabashi - Japan", "port118": "Barcelona - Spain", "port2085": "Cat <PERSON> (Saigon New Port) - Vietnam", "port168": "Bremerhaven - Germany", "port833": "Novorossiysk - Russian Federation", "port463": "Higashiharima - Japan", "port1150": "Saint Petersburg - Russian Federation", "port2036": "Siam Seaport - Thailand", "port1370": "Vlissingen - The Netherlands", "port903": "Cai Mep - Vietnam", "port523": "Johor - Malaysia", "port644": "Lianyungang - China", "port154": "Bohai Bay - China", "port792": "Nantong - China", "port1369": "Vladivostok - Russian Federation", "port1265": "Tangier-Mediterranean - Morocco", "port701": "Map Ta Phut - Thailand", "port2028": "Mawan - China", "port1348": "Valencia - Spain", "port2037": "Diliskelesi - Turkiye", "port270": "Daesan - Korea", "port815": "New York-New Jersey - United States", "port254": "Colombo - Sri Lanka", "port664": "Los Angeles-Long Beach - United States", "port133": "Bayuquan - China", "port1180": "Sendaishiogama - Japan", "port985": "Le Havre - France", "port508": "Iwakuni - Japan", "port408": "Gothenburg - Sweden", "port518": "Jeddah - Saudi Arabia", "port1193": "Shimizu - Japan", "port1112": "Rostov-Na-Donu - Russian Federation", "port579": "Klaipeda - Lithuania", "port1189": "<PERSON><PERSON><PERSON> (Shenzhen) - China", "port238": "Keelung - Taiwan Province of China", "port260": "Constanta - Romania", "port387": "Genova - Italy", "port362": "Al Fujayrah - United Arab Emirates", "port1266": "Tangshan (Jingtang) - China", "port218": "Cartagena - Colombia", "port1071": "Qinzhou - China", "port2283": "Moerdijk - The Netherlands", "port735": "Mersin - Turkiye", "port2049": "Ambarli - Turkiye", "port1414": "Yantian - China", "port955": "Port Hedland - Australia", "port466": "Hiroshima - Japan", "port578": "Kisarazu - Japan", "port1182": "Serangoon Harbor - Singapore", "port823": "Niihama - Japan", "port23": "Alexandria - Egypt", "port1129": "Sakaide - Japan", "port1095": "Ust-Luga - Russian Federation", "port632": "Lanqiao Port - China", "port1105": "Rizhao - China", "port380": "Gdansk - Poland", "port1072": "Qinhuangdao - China", "port487": "IJmuiden - The Netherlands", "port655": "Livorno - Italy", "port1425": "Zhangjiangang (Suzhou) - China", "port2034": "Yarimca - Turkiye", "port1261": "Taipei - Taiwan Province of China", "port435": "Haifa - Israel", "port1100": "Riga - Latvia", "port777": "Mundra - India", "port1406": "Yakacik - Turkiye", "port370": "Fuzhou - China", "port433": "Hachinohe - Japan", "port1139": "Samsun - Turkiye", "port309": "Dumai - Indonesia", "port711": "Marsaxlokk - Malta", "port738": "Mikawa - Japan", "port1324": "Tsukumi - Japan", "port381": "Gdynia - Poland", "port311": "Durban - South Africa", "port1279": "Teesport - United Kingdom", "port1214": "South Louisiana - United States", "port220": "Casablanca - Morocco", "port1426": "Zhanjiang - China", "port572": "Kingston - Jamaica", "port1038": "Bilbao - Spain", "port307": "Dublin - Ireland", "port700": "Manzanillo - Panama", "port241": "Chittagong - Bangladesh", "port101": "Balboa - Panama", "port2112": "Zhuhai - China", "port1405": "Xiuyu - China", "port654": "Liverpool - United Kingdom", "port1170": "Savannah - United States", "port1005": "Porto Di Lido-Venezia - Italy", "port951": "Port Everglades - United States", "port915": "Pohang - Korea", "port73": "Ashdod - Israel", "port2200": "Taboguilla - Panama", "port102": "Balikpapan - Indonesia", "port1004": "Ravenna - Italy", "port999": "Leixoes - Portugal", "port816": "Newcastle - Australia", "port24": "Jubail - Saudi Arabia", "port658": "London - United Kingdom", "port1070": "Dongjiakou - China", "port174": "Brisbane - Australia", "port274": "Damietta - Egypt", "port125": "Batangas City - Philippines", "port1045": "Puerto Del Callao - Peru", "port310": "Dunkirk - France", "port1277": "Tarragona - Spain", "port1111": "Rostock - Germany", "port787": "Nakhodka - Russian Federation", "port1358": "Veracruz - Mexico", "port1427": "Zhapu - China", "port410": "<PERSON><PERSON><PERSON> - China", "port1246": "Szczecin - Poland", "port2025": "Khalifa Port - United Arab Emirates", "port885": "Paranagua - Brazil", "port626": "Lagos-Apapa-Tin Can Island - Nigeria", "port720": "Matsuyama - Japan", "port224": "Cebu - Philippines", "port528": "Kagoshima - Japan", "port1350": "Vancouver - Canada", "port1216": "Southampton - United Kingdom", "port1336": "Makassar - Indonesia", "port440": "Hakodate - Japan", "port540": "<PERSON><PERSON>yal (Kandla) - India", "port2189": "Pengerang - Malaysia", "port780": "Muroran - Japan", "port1103": "Rio De Janeiro - Brazil", "port729": "Melbourne - Australia", "port881": "Panjang - Indonesia", "port573": "Hull - United Kingdom", "port699": "Manzanillo - Mexico", "port822": "Niigata - Japan", "port1200": "Sines - Portugal", "port659": "London Gateway - United Kingdom", "port506": "Itaqui - Brazil", "port1318": "Trieste - Italy", "port1367": "Visakhapatnam - India", "port179": "Brunsbuettel - Germany", "port339": "Fang-Cheng - China", "port988": "Port of Sohar - Oman", "port1410": "Yantai - China", "port2043": "Borusan - Turkiye", "port714": "Marugame - Japan", "port426": "Guayaquil - Ecuador", "port575": "Kinuura - Japan", "port281": "Davao - Philippines", "port398": "Gladstone - Australia", "port1090": "<PERSON><PERSON> - Qatar", "port610": "<PERSON><PERSON> - Japan", "port760": "Mongstad - Norway", "port1062": "Penang - Malaysia", "port103": "Baltimore - United States", "port322": "Eleusis - Greece", "port231": "Charleston - United States", "port2010": "Azov - Russian Federation", "port264": "Corpus Christi - United States", "port484": "Huizhou - China", "port483": "Huelva - Spain", "port38": "Altamira - Mexico", "port1293": "Thessaloniki - Greece", "port653": "Lisbon - Portugal", "port141": "Belfast - United Kingdom", "port1341": "<PERSON><PERSON> - Iraq", "port1428": "Zhouliwang - China", "port603": "Kuantan New Port - Malaysia", "port395": "Gioia Tauro - Italy", "port219": "Cartagena - Spain", "port1280": "Tekirdag - Turkiye", "port875": "Palembang - Indonesia", "port342": "Fawley - United Kingdom", "port746": "Salalah - Oman", "port2017": "Moji - Japan", "port306": "Dubai - United Arab Emirates", "port67": "Aarhus - Denmark", "port597": "Kotka - Finland", "port1104": "Rio Grande - Brazil", "port2040": "Naha - Japan", "port1409": "Yangpu - China", "port1298": "Tilbury - United Kingdom", "port637": "<PERSON><PERSON><PERSON> - Mexico", "port611": "Kushiro - Japan", "port1178": "Sekupang - Indonesia", "port112": "Banjarmasin - Indonesia", "port850": "Onahama - Japan", "port1059": "Puerto San Martin - Argentina", "port928": "Porsgrunn - Norway", "port2044": "El Dekheila - Egypt", "port275": "Dammam - Saudi Arabia", "port1153": "Santa Cruz De Tenerife - Spain", "port193": "Burgas - Bulgaria", "port287": "Derince (Izmit-Kocaeli) - Turkiye", "port468": "Hitachi - Japan", "port1108": "Rosario - Argentina", "port213": "<PERSON><PERSON> - Vietnam", "port235": "Chennai (Madras) - India", "port228": "Changzhou - China", "port1099": "Richards Bay - South Africa", "port269": "<PERSON>g - Vietnam", "port272": "Dakar - Senegal", "port114": "Banten - Indonesia", "port749": "<PERSON><PERSON> Kawanoe - Japan", "port765": "Montoir - France", "port510": "Izmir - Turkiye", "port1203": "Sitrah - Bahrain", "port75": "Aspropyrgos - Greece", "port361": "Fremantle - Australia", "port795": "Napoli - Italy", "port2057": "Hibikinada - Japan", "port442": "Haldia - India", "port343": "Felixstowe - United Kingdom", "port521": "Jinzhou Wan - China", "port1086": "Yangon - Myanmar", "port1374": "Vostochnyy - Russian Federation", "port570": "King <PERSON>ahd Port - Saudi Arabia", "port779": "Murmansk - Russian Federation", "port1002": "Porto De Suape - Brazil", "port4": "Abidjan - Cote dIvoire", "port161": "Port Botany - Australia", "port747": "<PERSON> - United Arab Emirates", "port1403": "Xiagong - China", "port276": "Dampier - Australia", "port757": "Mombasa - Kenya", "port100": "Baku Port - Azerbaijan", "port986": "Rouen - France", "port15": "Ako - Japan", "port1093": "Rayong - Thailand", "port1271": "Tanjung Sekong - Indonesia", "port512": "Jabal Az Zannah-Ruways - United Arab Emirates", "port543": "Karachi - Pakistan", "port845": "Agioi Oil Terminal - Greece", "port319": "<PERSON><PERSON> - Morocco", "port1184": "Setubal - Portugal", "port657": "Lome - Togo", "port592": "<PERSON><PERSON> - Slovenia", "port1282": "Tema - Ghana", "port80": "Augusta - Italy", "port1159": "Puerto Barrios - Guatemala", "port567": "Kikuma - Japan", "port72": "Sharjah - United Arab Emirates", "port1036": "Puerto Cortes - Honduras", "port184": "Buenos Aires - Argentina", "port948": "Port Elizabeth - South Africa", "port1042": "Haina - Dominican Republic", "port1239": "<PERSON><PERSON> - Japan", "port601": "Kristiansund - Norway", "port582": "Copenhagen - Denmark", "port661": "Longkou Gang - China", "port2067": "Shuaiba - Kuwait", "port958": "Port Jerome - France", "port513": "Jacksonville - United States", "port944": "Port De Salvador - Brazil", "port132": "Beirut - Lebanon", "port1278": "Tauranga - New Zealand", "port883": "Paradip - India", "port385": "Gemlik - Turkiye", "port351": "Fos - France", "port2066": "Port of Virginia - United States", "port933": "Port Arthur - United States", "port2039": "Hazira - India", "port925": "Pontianak - Indonesia", "port1057": "Puerto Quetzal - Guatemala", "port294": "Djibouti - Djibouti", "port1331": "<PERSON><PERSON> <PERSON><PERSON> (Tuticorin) - India", "port1143": "San Juan - Puerto Rico", "port1326": "Tuapse - Russian Federation", "port1179": "Semarang - Indonesia", "port1349": "Valletta - Malta", "port2026": "Hamad Port - Qatar", "port139": "Belawan - Indonesia", "port736": "Miami - United States", "port2140": "Stade - Germany", "port917": "Point Lisas - Trinidad and Tobago", "port1052": "Puerto Moin - Costa Rica", "port681": "Mailiao - Taiwan Province of China", "port976": "Port Saint Louis Du Rhone - France", "port453": "Harbor Yeweiju - China", "port416": "Gravesend - United Kingdom", "port369": "Fushikitoyama - Japan", "port2019": "Dongguan - China", "port665": "Luanda - Angola", "port1396": "Wilhelmshaven - Germany", "port134": "Beaumont - United States", "port252": "Coatzacoalcos - Mexico", "port864": "Oslo - Norway", "port183": "Buenaventura - Colombia", "port667": "Lubeck-Travemunde - Germany", "port262": "Cork - Ireland", "port646": "Liepaja - Latvia", "port1190": "Shibushi - Japan", "port1155": "Santander - Spain", "port1363": "Vigo - Spain", "port22": "Al Hamriyah LPG Terminal - United Arab Emirates", "port826": "Norfolk - United States", "port766": "Montreal - Canada", "port357": "Freeport - The Bahamas", "port811": "New Mangalore - India", "port764": "Montevideo - Uruguay", "port503": "Iskenderun - Turkiye", "port754": "Mobile - United States", "port390": "Georgetown - Guyana", "port1015": "Skoldvik - Finland", "port1415": "Yingkou - China", "port167": "Bremen - Germany", "port2020": "Nanjing - China", "port1342": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "port740": "Milford Haven - United Kingdom", "port70": "Arzew - Algeria", "port458": "Hay Point - Australia", "port1281": "Teluk Bayur - Indonesia", "port267": "Ceuta - Spain", "port1191": "Shidao Newport - China", "port839": "Oakland - United States", "port120": "Barranquilla - Colombia", "port84": "Bristol - United Kingdom", "port394": "Gijon - Spain", "port1135": "Salerno - Italy", "port109": "Bandirma - Turkiye", "port187": "Vanino - Russian Federation", "port647": "Limassol - Cyprus", "port317": "Castellon - Spain", "port1225": "Stavanger - Norway", "port981": "Port Walcott - Australia", "port990": "Port-De-Bouc - France", "port1120": "Sagunto - Spain", "port443": "Halifax - Canada", "port358": "Freeport - United States", "port401": "Golcuk - Turkiye", "port450": "Hanko - Finland", "port2046": "Vila <PERSON>de - Brazil", "port32": "Alger - Algeria", "port901": "Philadelphia - United States", "port202": "Cagayan de Oro - Philippines", "port1035": "Colon - Panama", "port1309": "Donghae - Korea", "port1327": "Tuban - Indonesia", "port1037": "Puerto Cristobal - Panama", "port781": "Muuga-Port of Tallinn - Estonia", "port323": "Emden - Germany", "port775": "<PERSON> - Pakistan", "port583": "Cochin (Kochi) - India", "port1058": "Puerto San Antonio - Chile", "port268": "Cuxhaven - Germany", "port1273": "Tanjunguban - Indonesia", "port51": "Caucedo - Dominican Republic", "port147": "Bergen - Norway", "port785": "Nagasaki - Japan", "port375": "Gaogang - China", "port82": "Aveiro - Portugal", "port16": "Aktau Port - Kazakhstan", "port1020": "Primorsk - Russian Federation", "port328": "Esbjerg - Denmark", "port165": "Brake - Germany", "port376": "Garston - United Kingdom", "port589": "Komatsushima - Japan", "port1263": "Tananger - Norway", "port14": "Akita - Japan", "port215": "Cape Town - South Africa", "port919": "Point Noire - Republic of Congo", "port1328": "Tubarao - Brazil", "port2064": "Nordenham - Germany", "port460": "Helsinki - Finland", "port175": "Brofjorden - Sweden", "port526": "Juaymah - Saudi Arabia", "port106": "<PERSON><PERSON> - Iran", "port1390": "Wenzhou - China", "port1385": "Weifang Port - China", "port79": "Auckland - New Zealand", "port299": "Douala - Cameroon", "port356": "Fredrikstad - Norway", "port265": "Cotonou - Benin", "port827": "Norrkoping - Sweden", "port1259": "Tampa - United States", "port750": "Misrata - Libya", "port728": "Mejillones - Chile", "port5": "Abu Dhabi - United Arab Emirates", "port354": "Fredericia - Denmark", "port965": "Port Louis - Mauritius", "port459": "Helsingborg - Sweden", "port2045": "Porto de Itaguai - Brazil", "port584": "Kochi - Japan", "port599": "Krishnapatnam Port - India", "port151": "Bitung - Indonesia", "port278": "<PERSON> - Tanzania", "port1092": "Rauma - Finland", "port1006": "Malamocco - Italy", "port907": "Pipavav - India", "port622": "La Spezia - Italy", "port1413": "Yeysk - Russian Federation", "port1248": "Tacoma - United States", "port40": "Amagasaki - Japan", "port192": "Port Said - Egypt", "port349": "Floro - Norway", "port1044": "Pasajes - Spain", "port19": "Al Aqabah - Jordan", "port156": "Bontang LNG Terminal - Indonesia", "port702": "Maputo - Mozambique", "port1053": "Puerto Montt - Chile", "port502": "Ishinomaki - Japan", "port509": "Izhevskoye - Russian Federation", "port1268": "<PERSON><PERSON><PERSON> - Indonesia", "port617": "La Coruna - Spain", "port496": "Indonesia Morowali Industrial Park - Indonesia", "port1386": "Weihai - China", "port1232": "Subic Bay - Philippines", "port1283": "Temryuk - Russian Federation", "port471": "Hofu - Japan", "port489": "Chornomorsk - Ukraine", "port30": "Alesund - Norway", "port203": "Cagliari - Italy", "port1075": "Quebec - Canada", "port2118": "Luoyuan - China", "port535": "Sihanoukville - Cambodia", "port527": "Gebze - Turkiye", "port505": "Itajai - Brazil", "port2031": "King <PERSON> - Saudi Arabia", "port1016": "Poti - Georgia", "port42": "Port Ambon - Indonesia", "port210": "Campana - Argentina", "port532": "Kalundborg - Denmark", "port731": "Merak Mas Terminal - Indonesia", "port163": "Botas Natural Gas Terminal - Turkiye", "port415": "Grangemouth - United Kingdom", "port245": "Cilacap - Indonesia", "port649": "Limetree Bay - United States Virgin Islands", "port843": "Odesa - Ukraine", "port1398": "Wilmington, DE - United States", "port546": "Karlshamn - Sweden", "port959": "Port Kembla - Australia", "port1185": "Sevilla - Spain", "port1275": "Taranto - Italy", "port534": "Kamarajar Port - India", "port2021": "Jiaxing - China", "port989": "Port of Spain - Trinidad and Tobago", "port2114": "Guangao - China", "port690": "Malmo - Sweden", "port1242": "Swinoujscie - Poland", "port332": "Gibraltar - Gibraltar", "port182": "Buco - Philippines", "port33": "Aliaga - Turkiye", "port280": "Darwin - Australia", "port1330": "Turkmenbashi Port - Turkmenistan", "port3": "Aberdeen - United Kingdom", "port970": "Port Moresby - Papua New Guinea", "port1411": "Yatsushiro - Japan", "port285": "Delfzijl - The Netherlands", "port29": "Aalborg - Denmark", "port530": "Kaliningrad - Russian Federation", "port201": "Cadiz - Spain", "port537": "Kanazawa - Japan", "port620": "La Pallice - France", "port149": "Bintulu Port - Malaysia", "port1285": "Texas City - United States", "port62": "Aratu - Brazil", "port327": "Eregli - Turkiye", "port1250": "Tagonoura - Japan", "port594": "Korsakov - Russian Federation", "port271": "Dahej - India", "port1357": "Ventspils - Latvia", "port90": "<PERSON><PERSON> (Ventanas) - Chile", "port529": "Kakinada - India", "port1048": "Bahia Blanca - Argentina", "port1164": "Port of Sabah - Malaysia", "port1260": "Tampico - Mexico", "port1329": "Tunis - Tunisia", "port1074": "Quanzhou - China", "port386": "<PERSON> - Philippines", "port227": "Changshu (Suzhou) - China", "port1419": "Pivdennyi - Ukraine", "port1010": "Porto Pecem - Brazil", "port1154": "Santa Marta - Colombia", "port1183": "Sete - France", "port922": "Pointe A Pitre - Guadeloupe", "port1091": "<PERSON><PERSON> - Saudi Arabia", "port608": "Gunsan - Korea", "port1418": "Yokosuka - Japan", "port902": "Philipsburg - Sint Maarten", "port207": "Calcutta - India", "port856": "Oran - Algeria", "port1162": "Sao Sebastiao - Brazil", "port679": "Magdalla - India", "port966": "Port Lyttelton - New Zealand", "port1171": "Savona - Italy", "port1401": "Wismar - Germany", "port629": "Lake Charles - United States", "port6": "Acajutla - El Salvador", "port2035": "Itapoa - Brazil", "port1167": "Sarroch Oil Terminal - Italy", "port1137": "Samarinda - Indonesia", "port448": "Hamina - Finland", "port672": "Lumut - Malaysia", "port473": "<PERSON> - <PERSON>", "port712": "Marseilles - France", "port1127": "Sakai - Japan", "port726": "Medway - United Kingdom", "port884": "Paramaribo - Suriname", "port2252": "<PERSON> - United States", "port605": "Kudamatsu - Japan", "port853": "<PERSON><PERSON><PERSON> - Japan", "port753": "<PERSON> Rana - Norway", "port739": "Milazzo - Italy", "port1209": "Sodertalje - Sweden", "port10": "Agadir - Morocco", "port1236": "Duluth - United States", "port490": "Imabari - Japan", "port171": "Bridgetown - Barbados", "port1219": "Saint John - Canada", "port1343": "Uno - Japan", "port1078": "<PERSON><PERSON> - Vietnam", "port25": "Shuwaikh - Kuwait", "port511": "Jaigad Port - India", "port876": "Palm Beach - United States", "port1288": "Thathong - Thailand", "port1399": "Wilmington, NC - United States", "port624": "Lae - Papua New Guinea", "port76": "Astrakhan Port - Russian Federation", "port373": "Galveston - United States", "port258": "Conakry - Guinea", "port934": "Port Au Prince - Haiti", "port54": "Port Akdeniz - Turkiye", "port2063": "Ngqura - South Africa", "port444": "Halmstad - Sweden", "port892": "Pelabuhan Sungai Udang - Malaysia", "port316": "Eemshaven - The Netherlands", "port852": "Onoda - Japan", "port1023": "Progreso Yucatan - Mexico", "port1009": "Porto Grande - Cabo Verde", "port1258": "Tallinn - Estonia", "port233": "<PERSON> - United States Virgin Islands", "port1355": "Varna - Bulgaria", "port424": "Guangxi Beibu Gulf Port - China", "port137": "Beira - Mozambique", "port423": "Guangdong Yangjiang Port - China", "port1321": "Tromso - Norway", "port350": "Fort De France - Martinique", "port1175": "Seattle - United States", "port2116": "Dafeng - China", "port798": "Nassau - The Bahamas", "port689": "Male - Maldives", "port1247": "Tachibana - Japan", "port969": "Skikda (Port Methanier) - Algeria", "port1115": "Rouge River - United States", "port68": "Arkhangelsk - Russian Federation", "port978": "Taman - Russian Federation", "port2113": "Gulei - China", "port1098": "Reykjavik - Iceland", "port1119": "Saganoseki - Japan", "port277": "Dandong - China", "port472": "Himekawa - Japan", "port586": "Kokkola - Finland", "port480": "Hososhima - Japan", "port1387": "Weipa - Australia", "port1166": "Sarnia - Canada", "port41": "Amamapare - Indonesia", "port360": "Freetown - Sierra Leone", "port138": "Bejaia - Algeria", "port1196": "Shui Dong - China", "port83": "Aviles - Spain", "port719": "Matsusaka - Japan", "port828": "El Sokhna - Egypt", "port378": "Vysotsk - Russian Federation", "port737": "Midia - Romania", "port921": "Pointe A Pierre - Trinidad and Tobago", "port794": "Napier - New Zealand", "port842": "Odense - Denmark", "port1133": "Saldanha Bay - South Africa", "port1368": "Vitoria - Brazil", "port421": "Grimsby - United Kingdom", "port920": "Richmond, CA - United States", "port501": "Ishikari Bay New Port - Japan", "port1315": "Townsville - Australia", "port1032": "Puerto Caldera - Costa Rica", "port832": "Noumea - New Caledonia", "port977": "Port Sudan - Sudan", "port379": "Gavle - Sweden", "port1187": "Shantou - China", "port993": "Portland - United States", "port600": "Kristiansand - Norway", "port222": "Catania - Italy", "port1322": "Trondheim - Norway", "port447": "Hamilton - Canada", "port221": "Castries - St.  Lucia", "port2029": "Dachan Bay - China", "port338": "Famagusta - Turkiye", "port92": "San Vicente - Chile", "port1211": "Songkhla - Thailand", "port1381": "Walvis Bay - Namibia", "port682": "Maizuru - Japan", "port1430": "Zonguldak - Turkiye", "port692": "Maloy - Norway", "port874": "Paldiski - Estonia", "port302": "Drammen - Norway", "port723": "Mazatlan - Mexico", "port706": "Marina Di Carrara - Italy", "port110": "Benghazi - Libya", "port1021": "<PERSON> Canada", "port743": "<PERSON> - Kuwait", "port291": "Vassiliko - Cyprus", "port383": "Geelong - Australia", "port162": "Botas - Turkiye", "port868": "Oxelosund - Sweden", "port158": "Bordeaux - France", "port783": "Naantali - Finland", "port108": "Bandar Khomeini - Iran", "port1199": "Sikka - India", "port1353": "Varberg - Sweden", "port2001": "Izmail - Ukraine", "port169": "Brevik - Norway", "port2282": "Uusikaupunki - Finland", "port715": "Masan - Korea", "port2003": "Olvia - Ukraine", "port1325": "Tsuruga - Japan", "port693": "Manaus - Brazil", "port43": "Amirabad Port - Iran", "port34": "Alicante - Spain", "port498": "Ipswich - United Kingdom", "port556": "Kemaman Harbor - Malaysia", "port123": "Basuo - China", "port89": "Bahia De Valparaiso - Chile", "port2181": "Saiki - Japan", "port180": "Brunswick - United States", "port1249": "Taganrog - Russian Federation", "port814": "New Westminster - Canada", "port127": "Batumi - Georgia", "port924": "Ponta Delgada - Portugal", "port1356": "Vasteras - Sweden", "port1373": "Volos - Greece", "port321": "Al Adabiyah - Egypt", "port1138": "Sampit - Indonesia", "port950": "Port Est - Reunion", "port2133": "Sillamae - Estonia", "port1218": "Split - Croatia", "port1274": "Tripoli - Lebanon", "port973": "Port Owendo - Gabon", "port547": "Karsto - Norway", "port476": "Honolulu - United States", "port774": "Muara Harbor - Brunei Darussalam", "port2002": "Reni - Ukraine", "port698": "Pori - Finland", "port119": "Bari - Italy", "port533": "Kamaishi - Japan", "port844": "Ofunato - Japan", "port1316": "Trabzon - Turkiye", "port585": "Koge - Denmark", "port1001": "Porto De Mucuripe - Brazil", "port1311": "Tornio - Finland", "port49": "Ancona - Italy", "port1254": "Takamatsu - Japan", "port684": "Makhachkala - Russian Federation", "port500": "Heraklion - Greece", "port893": "<PERSON><PERSON><PERSON><PERSON> - Indonesia", "port558": "Kendari - Indonesia", "port1397": "Willemstad - Curacao", "port59": "Aomori - Japan", "port945": "Port Dickson - Malaysia", "port733": "Sfax - Tunisia", "port1085": "Randers - Denmark", "port1101": "Rijeka - Croatia", "port536": "Kamsar - Guinea", "port1221": "Port of Saint Johns - Antigua and Barbuda", "port44": "Ampenan - Indonesia", "port190": "Bunbury - Australia", "port1295": "Thunder Bay - Canada", "port452": "Hannan - Japan", "port542": "Karabiga - Turkiye", "port247": "Civitavecchia - Italy", "port938": "Port Cartier - Canada", "port1334": "Tynemouth - United Kingdom", "port2184": "Mokpo - Korea", "port756": "Mohammedia - Morocco", "port686": "Malaga - Spain", "port159": "Boston - United States", "port609": "Kupang - Indonesia", "port831": "Nouakchott - Mauritania", "port554": "Kavkaz Oil Terminal - Russian Federation", "port1422": "Zarate - Argentina", "port88": "Bahia De <PERSON>rani - Peru", "port574": "Kingstown - St.  Vincent and the Grenadines", "port297": "Donggala - Indonesia", "port2055": "Iloilo - Philippines", "port817": "Newport - United Kingdom", "port782": "Mykolaiv - Ukraine", "port382": "Gebig - Brazil", "port905": "Pichilingue - Mexico", "port359": "Frederiksvaerk - Denmark", "port2174": "Namikata - Japan", "port595": "Kota Baru - Indonesia", "port53": "Annaba - Algeria", "port1118": "Safi - Morocco", "port1107": "Rorvik - Norway", "port345": "Figueira Da Foz - Portugal", "port261": "Corinto - Nicaragua", "port932": "Port Aransas - United States", "port1083": "Vado Ligure - Italy", "port619": "La Libertad - Ecuador", "port1303": "Tokachi - Japan", "port1388": "Wellington - New Zealand", "port427": "Guaymas - Mexico", "port1084": "Brest - France", "port873": "Paita - Peru", "port1224": "Stagen - Indonesia", "port1081": "Rabigh - Saudi Arabia", "port998": "Porto Da Praia - Cabo Verde", "port818": "Newport News - United States", "port717": "Matadi - Democratic Republic of the Congo", "port131": "Bayonne - France", "port588": "Kolding - Denmark", "port404": "Goole - United Kingdom", "port1161": "Sao Francisco - Brazil", "port889": "Patras - Greece", "port58": "Anzali - Iran", "port296": "Djupviken - Norway", "port1061": "<PERSON><PERSON><PERSON> - Indonesia", "port9": "Aden - Yemen", "port670": "Lulea - Sweden", "port1014": "Portsmouth Harbour - United Kingdom", "port1332": "Tuxpan - Mexico", "port391": "Geraldton - Australia", "port1151": "Sanshandaocun - China", "port1289": "Thamesport - United Kingdom", "port914": "Plymouth - United Kingdom", "port2004": "Kherson - Ukraine", "port2267": "Longview - United States", "port1255": "Takoradi - Ghana", "port888": "Pascagoula - United States", "port867": "Oulu - Finland", "port1146": "San Pedro - Cote dIvoire", "port1181": "Sept Iles - Canada", "port882": "Papeete - French Polynesia", "port1393": "Whangerei - New Zealand", "port1270": "Tanjung <PERSON> - Indonesia", "port229": "Chaozhou - China", "port173": "Brindisi - Italy", "port293": "Djen-Djen - Algeria", "port2047": "Rades - Tunisia", "port1054": "Puerto Nuevo - Colombia", "port344": "Ferrol - Spain", "port196": "Bushehr - Iran", "port607": "Kunda - Estonia", "port621": "La Plata - Argentina", "port830": "Nouadhibou - Mauritania", "port1407": "Yalova - Turkiye", "port956": "Port Hueneme - United States", "port2041": "O<PERSON>ezaki - Japan", "port1301": "Toamasina - Madagascar", "port1029": "Puerto Cabello - Venezuela", "port1208": "Slagentangen - Norway", "port438": "Haimen - China", "port770": "Moss - Sweden", "port1008": "Palermo - Italy", "port857": "Oranjestad - Bonaire,  Saint Eustatius and Saba", "port784": "Nacala - Mozambique", "port929": "Port Adelaide - Australia", "port115": "Bizerte - Tunisia", "port290": "Dhamra Port - India", "port128": "Baubau - Indonesia", "port1384": "Waterford - Ireland", "port604": "Kuching - Malaysia", "port0": "Abbot Point - Australia", "port1227": "Stenungsund - Sweden", "port1351": "Vancouver - United States", "port160": "Boston - United Kingdom", "port1240": "Suva - Fiji", "port704": "Mariel - Cuba", "port677": "<PERSON><PERSON> - Brazil", "port1360": "Victoria - Malaysia", "port2129": "<PERSON><PERSON> - Egypt", "port793": "Naoetsu - Japan", "port1228": "Stockholm - Sweden", "port564": "Kiel - Germany", "port437": "Haikou - China", "port709": "Mormugao - India", "port1223": "Saint Georges - Grenada", "port633": "Larnaca - Cyprus", "port326": "Ensenada - Mexico", "port2215": "Sabetta - Russian Federation", "port1213": "Sousse - Tunisia", "port748": "Tripoli - Libya", "port1079": "Raahe - Finland", "port456": "Haroysundet - Norway", "port552": "Kavala - Greece", "port636": "Lautoka - Fiji", "port2038": "Kattupalli - India", "port758": "Monfalcone - Italy", "port983": "Port of Can Tho - Vietnam", "port557": "Kemi - Finland", "port866": "Otaru - Japan", "port1": "Aabenraa - Denmark", "port685": "Malabo - Equatorial Guinea", "port696": "Manokwari Road - Indonesia", "port819": "<PERSON><PERSON> - Vietnam", "port1375": "Vung Tau - Vietnam", "port126": "Baton Rouge - United States", "port810": "New Holland - United Kingdom", "port2258": "Nueva Palmira - Uruguay", "port1313": "Toros <PERSON> Tu<PERSON>", "port804": "Nelson - New Zealand", "port2284": "Jeju - Korea", "port566": "Kiire - Japan", "port155": "Bonny - Nigeria", "port1130": "Sakato - Japan", "port2216": "Petropavlovsk-Kamchatskiy - Russian Federation", "port2011": "Otago - New Zealand", "port56": "Antofagasta - Chile", "port984": "Duqm - Oman", "port13": "Ajman - United Arab Emirates", "port1382": "Warren Point - United Kingdom", "port422": "Guaiba Island Terminal - Brazil", "port2033": "Port of Yeosu - Korea", "port2175": "Sasebo - Japan", "port256": "<PERSON><PERSON><PERSON><PERSON> Head - Canada", "port61": "Apra Harbor - Guam", "port486": "Husum - Sweden", "port967": "Port Manatee - United States", "port634": "Larvik - Norway", "port445": "Hamada - Japan", "port545": "Karatsu - Japan", "port2156": "Padang - Indonesia", "port707": "Mariupol - Ukraine", "port531": "Kalmar - Sweden", "port457": "Haydarpasa - Turkiye", "port851": "Onne - Nigeria", "port2214": "Slavyanka - Russian Federation", "port35": "Almeria - Spain", "port146": "Berbera - Somalia", "port1299": "Timaru - New Zealand", "port348": "Flekkefjord - Norway", "port1310": "Topolobampo - Mexico", "port1333": "Two Harbors - United States", "port1018": "Praia da Vitoria - Portugal", "port1231": "Stura - Norway", "port1172": "Suakin - Sudan", "port140": "Belem - Brazil", "port145": "Benoa - Indonesia", "port946": "Port Dover - Canada", "port69": "Arrecife - Spain", "port18": "Al-hudaydah - Yemen", "port1235": "Sundsvall - Sweden", "port1077": "Quequen - Argentina", "port2209": "Calaca - Philippines", "port286": "Delta Terminal - Turkiye", "port660": "Londonderry - United Kingdom", "port708": "Markenes - Norway", "port1140": "San Diego - United States", "port1204": "Skagen - Denmark", "port263": "Coronel - Chile", "port499": "Iquique - Chile", "port353": "<PERSON> - Ireland", "port1256": "Takuma - Japan", "port105": "Bandar-E Pars Terminal - Iran", "port1082": "Rada De Arica - Chile", "port909": "Pitea - Sweden", "port678": "Hambantota - Sri Lanka", "port1089": "Ras <PERSON> - Saudi Arabia", "port871": "<PERSON><PERSON><PERSON> - (Orangestad) - Aruba", "port1230": "<PERSON><PERSON> - Sweden", "port1339": "<PERSON><PERSON> - Sweden", "port1205": "Skelleftehamn - Sweden", "port1041": "Garrucha - Spain", "port1047": "Puerto Ilo - Peru", "port561": "<PERSON><PERSON> al Fakkan - United Arab Emirates", "port1145": "San Nicolas - Argentina", "port371": "Gabes - Tunisia", "port39": "Alvika - Norway", "port144": "Benicia - United States", "port1046": "Puerto Del Rosario - Spain", "port107": "Bandar Abbas - Iran", "port1365": "Villagarcia De Arosa - Spain", "port2237": "Das Island - United Arab Emirates", "port462": "Heysham - United Kingdom", "port869": "PLTU Semen Tonasa - Indonesia", "port249": "Cleveland - United States", "port887": "Parnu - Estonia", "port492": "Imbituba - Brazil", "port2253": "Kalama - United States", "port772": "Motril - Spain", "port1003": "Chioggia - Italy", "port926": "Poole - United Kingdom", "port1147": "San Pedro De Macoris - Dominican Republic", "port2111": "Qushan Island - China", "port488": "Iligan - Philippines", "port1366": "Villanueva - Philippines", "port1296": "Thyboron - Denmark", "port1364": "Vikanes - Norway", "port312": "Durres - Albania", "port1210": "Solvesborg - Sweden", "port813": "New Plymouth - New Zealand", "port971": "Port Nador - Morocco", "port674": "Macau - Macao SAR", "port1306": "Toledo - United States", "port891": "<PERSON><PERSON><PERSON><PERSON> Sansakan - Malaysia", "port246": "Cirebon - Indonesia", "port1142": "San Giorgio - Italy", "port363": "Fukui - Japan", "port1012": "Portocel - Brazil", "port1383": "Warri - Nigeria", "port2179": "Chofu - Japan", "port2226": "Landskrona - Sweden", "port1136": "Salina Cruz - Mexico", "port1421": "Zanzibar - Tanzania", "port1290": "Thamshamm - Norway", "port1432": "Punta Arenas - Chile", "port497": "Inkoo - Finland", "port2192": "Tawau - Malaysia", "port515": "Jakobstad - Finland", "port454": "Harlingen - The Netherlands", "port652": "Lirquen - Chile", "port257": "Comodoro Rivadavia - Argentina", "port2262": "<PERSON> - Vietnam", "port562": "Khoms - Libya", "port1287": "Tg. <PERSON> - Indonesia", "port1013": "Piombino - Italy", "port992": "Portland - Australia", "port954": "Port Harcourt - Nigeria", "port2173": "Henza Island - Japan", "port1158": "Santo Domingo - Dominican Republic", "port368": "Turku - Finland", "port116": "Bar - Montenegro", "port367": "Funchal - Portugal", "port796": "Narvik - Norway", "port48": "Anchorage - United States", "port790": "Nanao - Japan", "port994": "Portland Harbour - United Kingdom", "port550": "<PERSON><PERSON>en - Finland", "port1022": "Probolinggo - Indonesia", "port208": "Caleta Patillos - Chile", "port943": "Port De Kribi - Cameroon", "port762": "Monrovia - Liberia", "port771": "Mostaganem - Algeria", "port1169": "Sauda - Norway", "port315": "East London - South Africa", "port389": "Georgetown - Cayman Islands", "port419": "Grenaa - Denmark", "port759": "Mongla - Bangladesh", "port335": "Falkenberg - Sweden", "port837": "Nyborg - Denmark", "port242": "Chiwan - China", "port153": "Bluff Harbor - New Zealand", "port236": "Cherry Point - United States", "port516": "Jayapura - Indonesia", "port2008": "Romportmet - Romania", "port1323": "Tsingeli - Greece", "port1217": "Soyo Angola LNG Terminal - Angola", "port455": "Harmac - Canada", "port1194": "Shoreham Harbour - United Kingdom", "port2005": "Giurgiulesti Port - Moldova", "port2183": "<PERSON><PERSON> (Geoje) - Korea", "port244": "Cienfuegos - Cuba", "port854": "Onslow - Australia", "port1024": "Providence - United States", "port1124": "Saint-Malo - France", "port1106": "Road Harbor - British Virgin Islands", "port1110": "Roseau - Dominica", "port618": "La Guaira - Venezuela", "port931": "Port Angeles - Canada", "port2229": "Anping - Taiwan Province of China", "port1431": "Palma de Mallorca - Spain", "port1212": "Souda (Chania) - Greece", "port1173": "Saida - Lebanon", "port877": "Palmeira - Cabo Verde", "port251": "Clifton Pier - The Bahamas", "port177": "Brownsville - United States", "port841": "Odda - Norway", "port1229": "Stockton - United States", "port1000": "Porto De Maceio - Brazil", "port705": "Marin - Spain", "port2167": "Santa Panagia - Italy", "port191": "Safaga - Egypt", "port1027": "Puerto Bolivar - Colombia", "port1028": "Puerto Bolivar - Ecuador", "port2117": "Lushun - China", "port879": "Panama City - United States", "port467": "Hirtshals - Denmark", "port963": "Port Lavaca - United States", "port778": "Mogadishu - Somalia", "port104": "Baltiysk - Russian Federation", "port406": "Gorgon LNG - Australia", "port1241": "Swanport - United States", "port2115": "Luanjiakou - China", "port284": "Degrad Des Cannes - French Guiana", "port46": "Amuay (Bahia De Amuay) - Venezuela", "port1055": "Puerto Plata - Dominican Republic", "port1080": "Rabaul - Papua New Guinea", "port136": "Beihai - China", "port663": "Lorient - France", "port11": "Ahus - Sweden", "port86": "La Habana - Cuba", "port1156": "Santiago De Cuba - Cuba", "port656": "Lobito - Angola", "port2125": "Stigsnaes - Denmark", "port952": "Port Gentil - Gabon", "port266": "Covenas Offshore Term. - Colombia", "port482": "Hualien - Taiwan Province of China", "port475": "Honiara - Solomon Islands", "port87": "Bahia De Las Minas - Panama", "port2032": "Posorja - Ecuador", "port449": "Hammerfest - Norway", "port1149": "Sandusky - United States", "port838": "Nynashamn - Sweden", "port178": "Bruges - Belgium", "port894": "PelabuhanRatuCoalPowerPlant - Indonesia", "port411": "Gove - Australia", "port2278": "San Andres - Colombia", "port407": "Gorontalo - Indonesia", "port288": "Devonport - Australia", "port314": "EL Segundo - United States", "port71": "As Suways - Egypt", "port122": "Basseterre - St.  Kitts and Nevis", "port1168": "Sassnitz - Germany", "port64": "Ardalstangen - Norway", "port1132": "Salaverry - Peru", "port303": "Drogheda - Ireland", "port675": "Mackay - Australia", "port1376": "Vyborg - Russian Federation", "port912": "Ploce - Croatia", "port212": "Canaveral Harbor - United States", "port282": "Davisville Depot - United States", "port791": "Nantes - France", "port206": "Calais - France", "port569": "Kimbe - Papua New Guinea", "port195": "Burns Harbor - United States", "port331": "Esperance - Australia", "port836": "Nukualofa - Tonga", "port1025": "Providenciales - Turks and Caicos Islands", "port217": "Cardiff - United Kingdom", "port615": "<PERSON> (Port Alfred) - Canada", "port2178": "Mutsure - Japan", "port614": "LNG Tangguh - Indonesia", "port355": "Frederikshavn - Denmark", "port60": "Apia - Samoa", "port1220": "<PERSON> - Canada", "port2068": "Stralsund - Germany", "port2207": "Isabel - Philippines", "port429": "Gulluk - Turkiye", "port524": "Jose Terminal - Venezuela", "port47": "Anacortes - United States", "port630": "Lakselv - Norway", "port763": "Montego Bay - Jamaica", "port2170": "Gaeta - Italy", "port1192": "Shijing <PERSON>xiang - China", "port1063": "<PERSON><PERSON> - Venezuela", "port651": "Lingkas - Indonesia", "port329": "Eskifjordhur - Iceland", "port1420": "Zamboanga - Philippines", "port829": "Noshiro - Japan", "port113": "Banjul - The Gambia", "port1043": "Puerto De Hencan - Honduras", "port2203": "La Pampilla - Peru", "port722": "Maumere - Indonesia", "port1346": "Vaasa - Finland", "port897": "Penglai - China", "port571": "Kings Lynn - United Kingdom", "port495": "Indiana Harbor - United States", "port142": "Belize City - Belize", "port2018": "Ronne - Denmark", "port1152": "Santa Cruz De La Palma - Spain", "port642": "Les Sables D Olonne - France", "port1031": "Puerto Caldera - Chile", "port1011": "Porto Torres - Italy", "port697": "Manta - Ecuador", "port150": "Bissau - Guinea-Bissau", "port732": "Merauke - Indonesia", "port628": "<PERSON><PERSON> - Malaysia", "port2233": "Canakkale - Turkiye", "port194": "<PERSON><PERSON> - Australia", "port204": "Cairns - Australia", "port1234": "Sunderland - United Kingdom", "port995": "Portland, ME - United States", "port85": "Az Zawiyah - Libya", "port2092": "Romano - Albania", "port2223": "San Ciprian - Spain", "port417": "Great Yarmouth - United Kingdom", "port93": "<PERSON><PERSON> - Canada", "port1068": "Qalhat LNG Terminal - Oman", "port26": "Latakia - Syria", "port809": "New Haven - United States", "port997": "Porto Alegre - Brazil", "port834": "Nowshahr Port - Iran", "port1267": "<PERSON>jung <PERSON>ete - Indonesia", "port124": "Bata - Equatorial Guinea", "port1116": "Rumoi - Japan", "port1226": "Steinkjer - Norway", "port727": "Medway City - United Kingdom", "port1126": "Saipan - Northern Mariana Islands", "port861": "Ortona - Italy", "port1344": "Uturoa - French Polynesia", "port1361": "Victoria - Seychelles", "port95": "Marigot - <PERSON>", "port400": "Glomfjord - Norway", "port1312": "Toronto - Canada", "port2180": "Kin - Japan", "port2176": "Kanokawa - Japan", "port469": "Hobart - Australia", "port1019": "Presque Isle - United States", "port27": "Al Mukalla - Yemen", "port1377": "Waingapu - Indonesia", "port596": "Kota Kinabalu - Malaysia", "port99": "Bakar - Croatia", "port399": "Glasgow - United Kingdom", "port1408": "Yanbu - Saudi Arabia", "port91": "Bahia San Nicolas - Peru", "port972": "Noro - Solomon Islands", "port563": "Khorramshahr - Iran", "port940": "Bell Bay - Australia", "port1026": "Puerto Bayovar - Peru", "port237": "Chester - United States", "port2230": "Suao - Taiwan Province of China", "port859": "Or<PERSON> - Greece", "port2042": "<PERSON> - Vietnam", "port2202": "Pisco - Peru", "port587": "Koko - Nigeria", "port2153": "Karimun - Indonesia", "port2277": "Chacabuco - Chile", "port2158": "Pulau Sambu - Indonesia", "port1007": "Oristano - Italy", "port2159": "Kuala Tanjung - Indonesia", "port259": "Conchan Oil Terminal - Peru", "port205": "Calabar - Nigeria", "port2163": "Luwuk - Indonesia", "port741": "Milner Bay - Australia", "port485": "Husum - Germany", "port2256": "Dutch Harbor - United States", "port12": "Aiyion - Greece", "port872": "Pago Pago Harbor - American Samoa", "port788": "Namibe - Angola", "port2152": "Port Blair - India", "port2009": "Braila - Romania", "port1131": "Sal Rei - Cabo Verde", "port2070": "Wallhamn - Sweden", "port135": "Berdyansk - Ukraine", "port848": "Olbia - Italy", "port631": "Lamu - Kenya", "port916": "Point Fortin - Trinidad and Tobago", "port1165": "Sapele - Nigeria", "port982": "Port de Longoni - Mayotte", "port2099": "Itacoatiara - Brazil", "port1096": "Rethymnon - Greece", "port1039": "Carboneras - Spain", "port1109": "Rosarito - Mexico", "port428": "Gulfport - United States", "port2101": "Point Tupper - Canada", "port2142": "Lavrion - Greece", "port863": "Smalandshamnar - Sweden", "port1272": "<PERSON><PERSON><PERSON> - Indonesia", "port899": "Peterhead - United Kingdom", "port910": "Guayanilla - Puerto Rico", "port1144": "San Miguel De Cozumel - Mexico", "port28": "Albany - Australia", "port211": "Canaport (St. John) - Canada", "port377": "Gary - United States", "port491": "Imatra - Finland", "port17": "Akureyri - Iceland", "port1222": "St Nazaire - France", "port2051": "Cam Pha - Vietnam", "port2135": "Fuglafjordur - Faroe Islands", "port117": "Barbors Point - United States", "port441": "Halden - Norway", "port639": "Leith - United Kingdom", "port214": "Cap Haitien - Haiti", "port767": "Morehead City - United States", "port662": "Longyan Port - China", "port1148": "Sandnessjoen - Norway", "port65": "Ardal - Norway", "port797": "Nasipit Port - Philippines", "port1257": "Talara - Peru", "port1243": "Sydney - Australia", "port2236": "<PERSON><PERSON> - United Arab Emirates", "port478": "Horta - Portugal", "port598": "Kralendijk - Bonaire,  Saint Eust<PERSON> and Saba", "port98": "Bajo Grande - Venezuela", "port2177": "Nakagusuku - Japan", "port1094": "Recife - Brazil", "port980": "Port Vila - Vanuatu", "port396": "Gisborne - New Zealand", "port606": "Kulevi Oil Terminal - Georgia", "port2248": "Rodeo - United States", "port676": "Madang - Papua New Guinea", "port1423": "Zarzis - Tunisia", "port1362": "Vieux Fort - St.  Lucia", "port320": "El Palito - Venezuela", "port166": "Brasil Port - Brazil", "port392": "Ghazaouet - Algeria", "port718": "Matanzas - Cuba", "port789": "Namsos - Norway", "port949": "Port Esquivel - Jamaica", "port2235": "Zirku Island - United Arab Emirates", "port308": "Dudinka - Russian Federation", "port1049": "Puerto La Cruz - Venezuela", "port519": "Shoaiba - Saudi Arabia", "port1319": "Trincomalee - Sri Lanka", "port2257": "<PERSON><PERSON> - Uruguay", "port638": "Saint Brieuc - France", "port325": "Ende - Indonesia", "port577": "Kirteh Oil Terminal - Malaysia", "port1340": "<PERSON><PERSON> - United Arab Emirates", "port298": "Dos Bocas Terminal - Mexico", "port66": "Argentia - Canada", "port544": "Karaikal - India", "port734": "Marsa Tobruk - Libya", "port384": "Gela - Italy", "port240": "<PERSON><PERSON>qui <PERSON> - Panama", "port648": "Limerick - Ireland", "port181": "Buchanan - Liberia", "port2147": "<PERSON> Nicolas - Greece", "port304": "Duba - Saudi Arabia", "port2060": "<PERSON> - Bahrain", "port2213": "Mangalia - Romania", "port1207": "Skutskar - Sweden", "port248": "Clarkson - Canada", "port1391": "Western Port - Australia", "port479": "Horten - Norway", "port292": "Dili - Timor-Leste", "port2048": "Almirante - Panama", "port1412": "Yawatahama - Japan", "port337": "Falmouth Harbour - United Kingdom", "port1320": "Trois Rivieres - Canada", "port234": "Chatham - United Kingdom", "port333": "Everett - United States", "port979": "Port Vale Knights - Cabo Verde", "port209": "Calumet Harbor - United States", "port683": "Majuro - Marshall Islands", "port1066": "Put Put - Papua New Guinea", "port507": "Inverness - United Kingdom", "port232": "Charlestown - St.  Kitts and Nevis", "port2247": "Delaware - United States", "port1392": "Wewak Harbor - Papua New Guinea", "port347": "Finnsnes - Norway", "port1245": "Sydney - United States", "port230": "Cha<PERSON>o <PERSON> - <PERSON>", "port430": "Gustavia - Saint-Barthel<PERSON>y", "port374": "Galway - Ireland", "port2022": "Rosslare - Ireland", "port2122": "Zadar - Croatia", "port1317": "Trelleborg - Sweden", "port886": "Parepare - Indonesia", "port200": "Cabedelo - Brazil", "port855": "Oostende - Belgium", "port477": "Hopa - Turkiye", "port52": "Anguilla - Anguilla", "port942": "Caen - France", "port352": "Fourchon - United States", "port2231": "Tanga - Tanzania", "port7": "Acapulco - Mexico", "port1206": "Skhira - Tunisia", "port418": "Green Bay - United States", "port2075": "Orkney - United Kingdom", "port1345": "Uwajima - Japan", "port858": "Ormoc - Philippines", "port1064": "Punta Lobitos - Peru", "port301": "Dover - United Kingdom", "port223": "Moa - Cuba", "port1294": "Thevenard - Australia", "port253": "Coles Bay Oil Terminal - Sint Maarten", "port559": "Kerch - Ukraine", "port745": "Port Sultan Qaboos - Oman", "port2141": "Pachi - Greece", "port716": "Masao - Philippines", "port742": "Milwaukee - United States", "port751": "Massawa - Eritrea", "port769": "Moroni - Comoros", "port1102": "Rio Bueno - Jamaica", "port2086": "Cherbourg - France", "port1033": "Puerto Castilla - Honduras", "port2016": "Eilat - Israel", "port2190": "<PERSON><PERSON><PERSON> - Malaysia", "port2155": "Sungai Pakning - Indonesia", "port553": "Kavieng Harbor - Papua New Guinea", "port2266": "Fairless Hills - United States", "port996": "Portmouth - United States", "port2171": "Torre Annunziata - Italy", "port2205": "Mollendo - Peru", "port520": "Jimenez - Philippines", "port346": "Finnart Oil Terminal - United Kingdom", "port896": "Pemba - Mozambique", "port2095": "Del Guazu - Argentina", "port37": "Port of Alotau - Papua New Guinea", "port835": "Nuevitas Bay - Cuba", "port226": "Chabahar - Iran", "port650": "Linden - Guyana", "port2087": "Dundee - United Kingdom", "port2225": "Bashayer - Sudan", "port1402": "Workington - United Kingdom", "port2": "Aberdeen - United States", "port1040": "Puerto De Chimbote - Peru", "port773": "Moutsamoudu - Comoros", "port2053": "Chicago - United States", "port2195": "<PERSON>e <PERSON>ny - New Caledonia", "port680": "Majunga - Madagascar", "port130": "Bay City - United States", "port1050": "Puerto Madryn - Argentina", "port1198": "Sibolga - Indonesia", "port878": "Palua - Venezuela", "port148": "Big Creek - Belize", "port2007": "Galati - Romania", "port2088": "Rosyth - United Kingdom", "port2107": "Guayacan - Chile", "port1056": "Puerto Princesa - Philippines", "port551": "<PERSON><PERSON><PERSON> - Finland", "port2208": "Amlan - Philippines", "port2281": "Guanta - Venezuela", "port964": "Port Lincoln - Australia", "port157": "Bosaso - Somalia", "port2268": "Ashtabula - United States", "port1186": "Shala Pristan - Russian Federation", "port1400": "Wisbech - United Kingdom", "port1262": "<PERSON><PERSON> - Indonesia", "port21": "<PERSON><PERSON> (Al Burayqah) - Libya", "port1395": "Whyalla - Australia", "port2164": "Kharg Island - Iran", "port2168": "Falconara - Italy", "port2143": "<PERSON>lkis - Greece", "port305": "Duba Bulk Plant Tanker Terminal - Saudi Arabia", "port340": "Faradofay - Madagascar", "port1034": "Puerto Chanaral - Chile", "port974": "Port Pirie - Australia", "port807": "New Amsterdam - Guyana", "port991": "Port-La-Nouvelle - France", "port2100": "Limboh - Cameroon", "port2265": "Conneaut - United States", "port904": "Phuket - Thailand", "port668": "Luderitz Bay - Namibia", "port860": "Oro Bay - Papua New Guinea", "port590": "Kondopoga - Russian Federation", "port1117": "Al Ruwais - Qatar", "port2238": "Hound Point - United Kingdom", "port436": "Haifeng power - China", "port820": "Nice - France", "port164": "<PERSON><PERSON> - Montserrat", "port2287": "Zhongshan - China", "port849": "Omisalj - Croatia", "port2232": "Mtwara - Tanzania", "port2241": "Cromarty - United Kingdom", "port1286": "Tg Mani - Malaysia", "port2270": "Monroe - United States", "port2251": "Nikiski - United States", "port2269": "Fairport - United States", "port761": "Monopoli - Italy", "port939": "Ajaccio - France", "port669": "Luganville (Santo) - Vanuatu", "port2078": "Alexandroupoli - Greece", "port2219": "Salif - Yemen", "port801": "Navlakhi - India", "port2014": "Holyhead - United Kingdom", "port2024": "Larne - United Kingdom", "port1302": "Tocopilla - Chile", "port930": "Port Alma - Australia", "port402": "Golfito - Costa Rica", "port927": "Pohnpei - Micronesia", "port643": "Levuka - Fiji", "port1174": "Searsport - United States", "port2015": "Trapani - Italy", "port2151": "Karwar - India", "port1307": "Toliara - Madagascar", "port289": "Dhaka - Bangladesh", "port2106": "Talcahuano - Chile", "port55": "Antisranana - Madagascar", "port721": "<PERSON> - The Bahamas", "port688": "Malakal - Palau", "port330": "Esmeraldas - Ecuador", "port318": "El Guamache - Venezuela", "port1371": "Vlora - Albania", "port77": "Atapupu - Indonesia", "port74": "Ashkelon - Israel", "port2073": "Kismayo - Somalia", "port710": "<PERSON><PERSON><PERSON> (<PERSON>a <PERSON>) - Libya", "port549": "Kasim Terminal - Indonesia", "port847": "Okaram - Turkmenistan", "port2144": "<PERSON> - Greece", "port2254": "Albany - United States", "port923": "Pomalaa - Indonesia", "port324": "Empire - United States", "port412": "Grand Haven - United States", "port806": "Nepoui - New Caledonia", "port1141": "San Francisco - United States", "port2052": "Cape Flattery - Australia", "port2169": "Vasto - Italy", "port176": "Broome - Australia", "port906": "Picton - New Zealand", "port152": "Blanglancang - Indonesia", "port388": "Georgetown - The Bahamas", "port525": "Joutseno - Finland", "port225": "Chaguaramas - Trinidad and Tobago", "port2054": "Coquimbo - Chile", "port522": "Joensuu - Finland", "port1352": "Vanimo - Papua New Guinea", "port799": "Natal - Brazil", "port1337": "Uleelheue - Indonesia", "port890": "Payra - Bangladesh", "port2246": "Campbeltown - United Kingdom", "port172": "Brighton - Trinidad and Tobago", "port2280": "Guaranao - Venezuela", "port1276": "Betio (Tarawa) - Kiribati", "port1060": "Puerto Yabucoa - Puerto Rico", "port870": "Paagoumene - New Caledonia", "port941": "Port De Aracaju - Brazil", "port703": "Maracaibo - Venezuela", "port255": "Yap - Micronesia", "port197": "Butinge Oil Terminal - Lithuania", "port687": "Malau - Fiji", "port2224": "Galle - Sri Lanka", "port2146": "Psakhna - Greece", "port1125": "Saint-Marc - Haiti", "port2165": "Lavan - Iran", "port2134": "Runavik - Faroe Islands", "port2136": "Berre - France", "port2182": "Owase - Japan", "port666": "Luba - Equatorial Guinea", "port2191": "Kunak - Malaysia", "port464": "Hilo - United States", "port2187": "Aamchit - Lebanon", "port2227": "Tartous - Syria", "port2196": "Puerto Sandino - Nicaragua", "port129": "Bautino - Kazakhstan", "port1372": "Voh - New Caledonia", "port671": "Lumut - Brunei Darussalam", "port2259": "<PERSON><PERSON> - Uruguay", "port2274": "Madero - Mexico", "port63": "Arawak - Barbados", "port1314": "Torrevieja - Spain", "port755": "Chuuk - Micronesia", "port2198": "Haugesund - Norway", "port2204": "Eten - Peru", "port2273": "Fernandina - United States", "port186": "Buka - Papua New Guinea", "port185": "Buffington - United States", "port414": "Grand Turk - Turks and Caicos Islands", "port640": "Kosrae - Micronesia", "port2185": "Jinhae - Korea", "port121": "Barry - United Kingdom", "port962": "Port Latta - Australia", "port803": "Neiafu - Tonga", "port2030": "Qianhai Bay - China", "port2108": "Hadera - Israel", "port2186": "Chekka - Lebanon", "port2160": "Bula - Indonesia", "port2013": "Puerto Limon - Costa Rica", "port2161": "Kijing - Indonesia", "port470": "Hobro - Denmark", "port625": "Lafarge - Indonesia", "port2059": "Melilla - Spain", "port2096": "Wyndham - Australia", "port2275": "Deseado - Argentina", "port1017": "Pozzuoli - Italy", "port81": "Avatiu - Cook Islands", "port2069": "Sullom Voe - United Kingdom", "port2062": "Nanaimo - Canada", "port2261": "La Salina - Venezuela", "port334": "Ez Zueitina - Libya", "port2089": "Kirkcaldy - United Kingdom", "port2123": "Pula - Croatia", "port2103": "Gaspe - Canada", "port2162": "Amurang - Indonesia", "port279": "Daru - Papua New Guinea", "port937": "Port Bonython - Australia", "port2076": "Escravos (Oil Terminal) - Nigeria", "port612": "Ebeye - Marshall Islands", "port2104": "Charlottetown - Canada", "port2120": "Banana - Democratic Republic of the Congo", "port2218": "Shuqaiq - Saudi Arabia", "port591": "<PERSON><PERSON>n - Australia", "port2149": "Helguvik - Iceland", "port2288": "Ushuaia - Argentina", "port1380": "Wallaroo - Australia", "port2006": "Tulcea - Romania", "port2137": "Antifer - France", "port2260": "Catia La Mar - Venezuela", "port1238": "Surigao City - Philippines", "port800": "Nauru - Nauru", "port2139": "Supsa - Georgia", "port2166": "Tarbert - Ireland", "port2206": "Supe - Peru", "port539": "Kandalaksha - Russian Federation", "port935": "Channel-Port aux Basques - Canada", "port2249": "Erie - United States", "port821": "Nicholls Town - The Bahamas", "port2061": "<PERSON><PERSON><PERSON>n - Australia", "port2212": "Ponce - Puerto Rico", "port2138": "Gamba - Gabon", "port2210": "Balayan - Philippines", "port2240": "Douglas - United Kingdom", "port198": "Butuan City - Philippines", "port1051": "Puerto Miranda - Venezuela", "port840": "<PERSON>cho <PERSON> - Jamaica", "port366": "Funafuti - Tuvalu", "port2091": "Methil - United Kingdom", "port2211": "Porto Santo - Portugal", "port2222": "Mahon - Spain", "port565": "Kieta - Papua New Guinea", "port936": "Bayside Charlotte - Canada", "port1163": "Sao Tome - Sao Tome and Principe", "port2127": "Barahona - Dominican Republic", "port2194": "<PERSON> - Morocco", "port2245": "Ras Isa Terminal - Yemen", "port2239": "Flotta - United Kingdom", "port898": "<PERSON><PERSON><PERSON> (Manzanillo) - Dominican Republic", "port673": "Macae - Brazil", "port50": "Andoany - Madagascar", "port403": "Gomen - New Caledonia", "port953": "Port Giles - Australia", "port2126": "La Romana - Dominican Republic", "port1088": "<PERSON><PERSON> - Saudi Arabia", "port1354": "Varkaus - Finland", "port2119": "Tumaco - Colombia", "port2276": "San Antonio Este - Argentina", "port405": "Goose Bay - Canada", "port1113": "Rota - Northern Mariana Islands", "port691": "Malongo - Angola", "port2201": "Bialla - Papua New Guinea", "port2102": "Lewisporte - Canada", "port2065": "Okrika - Nigeria", "port2094": "Punta Loyola - Argentina", "port2128": "Puerto Viejo - Dominican Republic", "port2272": "Chancay - Peru", "port1202": "Sint Nicolaas Baai - Aruba", "port1300": "Tinian - Northern Mariana Islands", "port2074": "Jazan - Saudi Arabia", "port2084": "Rennell Island - Solomon Islands", "port94": "Baie <PERSON>oua - New Caledonia", "port2199": "Kirkenes - Norway", "port2221": "Rota - Spain", "port802": "Ndora - Solomon Islands", "port576": "Ki<PERSON><PERSON>ti - <PERSON><PERSON><PERSON><PERSON>", "port2285": "Seogwipo - Korea", "port97": "<PERSON><PERSON> - New Caledonia", "port283": "De Kastri - Russian Federation", "port461": "Hera port - Timor-Leste", "port913": "Montserrat - Montserrat", "port2110": "Pulandian - China", "port2150": "Vizhinjam - India", "port961": "Kyaukpyu - Myanmar", "port2121": "Punta Morales - Costa Rica", "port431": "Gwadar - Pakistan", "port2197": "Honningsvag - Norway", "port2124": "Dhekelia - Cyprus", "port1123": "Saint-Laurent-du-Maroni - French Guiana", "port808": "New Bedford - United States", "port1177": "Sekondi - Ghana", "port2077": "Bilhorod-Dnistrovskyi - Ukraine", "port724": "Mbulo - Solomon Islands", "port189": "Bullen <PERSON>ai - Curacao", "port2264": "Rudum Terminal - Yemen", "port2243": "Loch Striven - United Kingdom", "port730": "Menominee - United States", "port2056": "Karumba - Australia", "port2090": "Burntisland - United Kingdom", "port2105": "Holyrood - Canada", "port2172": "Port Kaiser - Jamaica", "port20": "Al Basrah - Iraq", "port336": "Falmouth - Jamaica", "port2050": "<PERSON><PERSON> - Brazil", "port2080": "Ringgi - Solomon Islands", "port2082": "Nabouwalu - Fiji", "port2145": "Atherinolakos - Greece", "port2271": "Garacad - Somalia", "port295": "Djupivogur - Iceland", "port143": "Bellingham - United States", "port170": "Bridgeport - United States", "port1176": "Seghe - Solomon Islands", "port2131": "Zeit Bay - Egypt", "port2157": "Sabang - Indonesia", "port216": "Caracas Bay - Curacao", "port2081": "Savusavu - Fiji", "port2263": "Ash Shihr - Yemen", "port2255": "Manchester - United States", "port2058": "<PERSON><PERSON>ton - Australia", "port2250": "Key West - United States", "port2234": "Feodosiya - Ukraine", "port2286": "Wando - Korea", "port1233": "Sundaomen Wuz - China", "port918": "Point Murat - Australia", "port695": "Manitowoc - United States", "port1030": "Puerto Cabezas - Nicaragua", "port2012": "<PERSON><PERSON><PERSON> - Bahrain", "port2072": "Port of Skadovsk - Ukraine", "port2083": "Lughughi - Solomon Islands", "port2217": "<PERSON><PERSON> - Saudi Arabia", "port2093": "Rosales - Argentina", "port2148": "Hvalfjordur - Iceland", "port2193": "Bijela - Montenegro", "port2154": "Muntok - Indonesia", "port865": "Dunedin - New Zealand", "port372": "Galeota Point Terminal - Trinidad and Tobago", "port2023": "Bantry Bay - Ireland", "port2071": "Ust-Danube - Ukraine", "port2079": "Gizo - Solomon Islands", "port2097": "South Riding Point - The Bahamas", "port2220": "Mossel Bay - South Africa", "port2098": "Guamare - Brazil", "port2130": "Ras <PERSON> - Egypt", "port2132": "<PERSON><PERSON> - Egypt", "port2188": "Derna - Libya", "port2228": "Baniyas - Syria", "port2109": "Mokha - Yemen", "port2242": "Macduff - United Kingdom", "port2244": "Faslane - United Kingdom", "port2279": "Cumarebo - Venezuela"}