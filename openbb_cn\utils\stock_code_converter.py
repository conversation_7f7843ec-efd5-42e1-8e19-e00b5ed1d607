#!/usr/bin/env python3
"""
股票代码智能转换器
自动将6位股票代码转换为带交易所后缀的完整代码
"""

import re
from typing import List, Optional, Tuple


class StockCodeConverter:
    """股票代码智能转换器"""
    
    def __init__(self):
        """初始化转换规则"""
        # 上海交易所规则 (600xxx, 601xxx, 603xxx, 605xxx, 688xxx等)
        self.sh_patterns = [
            r'^60[0-9]{4}$',  # 600xxx, 601xxx, 602xxx, 603xxx, 605xxx等
            r'^688[0-9]{3}$',  # 688xxx 科创板
            r'^900[0-9]{3}$',  # 900xxx B股
            r'^730[0-9]{3}$',  # 730xxx 新股申购
            r'^580[0-9]{3}$',  # 580xxx 权证
        ]
        
        # 深圳交易所规则 (000xxx, 001xxx, 002xxx, 003xxx, 300xxx等)
        self.sz_patterns = [
            r'^00[0-9]{4}$',   # 000xxx, 001xxx, 002xxx, 003xxx 主板/中小板
            r'^30[0-9]{4}$',   # 300xxx 创业板
            r'^200[0-9]{3}$',  # 200xxx B股
            r'^080[0-9]{3}$',  # 080xxx 配股权证
            r'^031[0-9]{3}$',  # 031xxx 权证
        ]
        
        # 编译正则表达式
        self.sh_compiled = [re.compile(pattern) for pattern in self.sh_patterns]
        self.sz_compiled = [re.compile(pattern) for pattern in self.sz_patterns]
    
    def detect_exchange(self, code: str) -> Optional[str]:
        """
        检测股票代码所属交易所
        
        Args:
            code: 6位股票代码
            
        Returns:
            'SH' for 上海交易所, 'SZ' for 深圳交易所, None if unknown
        """
        if not code or len(code) != 6 or not code.isdigit():
            return None
        
        # 检查上海交易所
        for pattern in self.sh_compiled:
            if pattern.match(code):
                return 'SH'
        
        # 检查深圳交易所
        for pattern in self.sz_compiled:
            if pattern.match(code):
                return 'SZ'
        
        return None
    
    def convert_to_tushare_code(self, code: str) -> Optional[str]:
        """
        将6位股票代码转换为Tushare格式 (XXXXXX.SH/SZ)
        
        Args:
            code: 股票代码，可以是6位数字或已包含后缀
            
        Returns:
            完整的Tushare格式代码，如 '000001.SZ', '600519.SH'
        """
        # 清理输入
        code = str(code).strip().upper()
        
        # 如果已经包含后缀，直接返回
        if '.' in code and (code.endswith('.SH') or code.endswith('.SZ')):
            return code
        
        # 移除可能的后缀，只保留6位数字
        if '.' in code:
            code = code.split('.')[0]
        
        # 验证是否为6位数字
        if not code.isdigit() or len(code) != 6:
            return None
        
        # 检测交易所
        exchange = self.detect_exchange(code)
        if exchange:
            return f"{code}.{exchange}"
        
        return None
    
    def convert_multiple_codes(self, codes: List[str]) -> List[Tuple[str, Optional[str]]]:
        """
        批量转换股票代码
        
        Args:
            codes: 股票代码列表
            
        Returns:
            (原始代码, 转换后代码) 的元组列表
        """
        results = []
        for code in codes:
            converted = self.convert_to_tushare_code(code)
            results.append((code, converted))
        return results
    
    def get_display_code(self, tushare_code: str) -> str:
        """
        从Tushare格式代码获取显示用的6位代码
        
        Args:
            tushare_code: Tushare格式代码，如 '000001.SZ'
            
        Returns:
            6位显示代码，如 '000001'
        """
        if '.' in tushare_code:
            return tushare_code.split('.')[0]
        return tushare_code
    
    def is_valid_stock_code(self, code: str) -> bool:
        """
        验证是否为有效的股票代码
        
        Args:
            code: 股票代码
            
        Returns:
            True if valid, False otherwise
        """
        return self.convert_to_tushare_code(code) is not None
    
    def get_exchange_name(self, code: str) -> Optional[str]:
        """
        获取交易所中文名称
        
        Args:
            code: 股票代码
            
        Returns:
            交易所中文名称
        """
        exchange = self.detect_exchange(code.split('.')[0] if '.' in code else code)
        if exchange == 'SH':
            return '上海证券交易所'
        elif exchange == 'SZ':
            return '深圳证券交易所'
        return None


# 创建全局实例
stock_converter = StockCodeConverter()


def convert_stock_code(code: str) -> Optional[str]:
    """
    便捷函数：转换股票代码
    
    Args:
        code: 股票代码
        
    Returns:
        Tushare格式代码
    """
    return stock_converter.convert_to_tushare_code(code)


def get_display_code(tushare_code: str) -> str:
    """
    便捷函数：获取显示代码
    
    Args:
        tushare_code: Tushare格式代码
        
    Returns:
        6位显示代码
    """
    return stock_converter.get_display_code(tushare_code)


# 测试函数
def test_converter():
    """测试转换器功能"""
    test_cases = [
        "000001",  # 平安银行 -> 000001.SZ
        "600519",  # 贵州茅台 -> 600519.SH
        "000858",  # 五粮液 -> 000858.SZ
        "600507",  # 方大特钢 -> 600507.SH
        "300015",  # 爱尔眼科 -> 300015.SZ
        "688001",  # 华兴源创 -> 688001.SH
        "000001.SZ",  # 已有后缀
        "invalid",  # 无效代码
    ]
    
    print("🔍 股票代码转换器测试")
    print("=" * 50)
    
    for code in test_cases:
        converted = convert_stock_code(code)
        exchange = stock_converter.detect_exchange(code.split('.')[0] if '.' in code else code)
        exchange_name = stock_converter.get_exchange_name(code)
        
        print(f"输入: {code:<12} -> 输出: {converted or 'None':<12} 交易所: {exchange_name or 'Unknown'}")


if __name__ == "__main__":
    test_converter()
