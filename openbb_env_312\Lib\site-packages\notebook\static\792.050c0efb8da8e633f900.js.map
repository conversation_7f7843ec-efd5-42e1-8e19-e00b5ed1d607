{"version": 3, "file": "792.050c0efb8da8e633f900.js?v=050c0efb8da8e633f900", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,GAAG,0BAA0B,GAAG,uBAAuB,GAAG,qBAAqB,GAAG,gBAAgB;AACjH,mBAAmB,mBAAO,CAAC,KAAc;AACzC;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA,kEAAkE,UAAU;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,UAAU;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA,0BAA0B;AAC1B,WAAW,qBAAM;AACjB,IAAI,qBAAM;AACV;AACA,KAAK,qBAAM;AACX,IAAI,qBAAM;AACV;AACA,aAAa;AACb,gBAAgB,qBAAM;AACtB;AACA;AACA,eAAe,GAAG,qBAAM;AACxB;;;;;;;;ACvFa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,cAAc,GAAG,eAAe,GAAG,cAAc,GAAG,mBAAmB,GAAG,oBAAoB,GAAG,eAAe;AAChH,kBAAkB,mBAAO,CAAC,KAAa;AACvC,mBAAmB,mBAAO,CAAC,KAAc;AACzC,mBAAmB,mBAAO,CAAC,KAAc;AACzC,2CAA0C,EAAE,qCAAqC,gCAAgC,EAAC;AAClH,gDAA+C,EAAE,qCAAqC,qCAAqC,EAAC;AAC5H,wBAAwB,mBAAO,CAAC,KAAyB;AACzD,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4EAA4E,iBAAiB;AAC7F;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,4EAA4E,iBAAiB;AAC7F;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA,4EAA4E,iBAAiB;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,SAAS;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2FAA2F;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,8BAA8B,cAAc,KAAK;AAClD,eAAe;AACf;AACA;AACA;AACA;AACA,SAAS;AACT,kBAAkB;AAClB,wBAAwB;AACxB,oBAAoB;AACpB;AACA;AACA,mCAAmC,2FAA2F;AAC9H;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA,2FAA2F,UAAU;AACrG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA,cAAc;AACd;;;;;;;ACzNa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe,GAAG,oBAAoB;AACtC,kBAAkB,mBAAO,CAAC,KAAa;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA,uCAAuC;AACvC,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,4EAA4E,UAAU;AACtF;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wGAAwG,wBAAwB;AAChI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,yDAAyD;AACzD;AACA,sDAAsD,kCAAkC;AACxF;AACA;AACA;AACA,oEAAoE,0BAA0B;AAC9F;AACA;AACA,+CAA+C,8DAA8D;AAC7G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,2BAA2B;AACrE,4CAA4C,0EAA0E;AACtH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C;AAC5C,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,UAAU;AAC/E;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,mEAAmE,UAAU;AAC7E;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD;AACzD,6DAA6D,2BAA2B;AACxF,wCAAwC,wBAAwB;AAChE,wCAAwC,+BAA+B;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA,4EAA4E,iBAAiB;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2EAA2E,UAAU;AACrF;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,eAAe;AACf;;;;;;;ACtTa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,4BAA4B,GAAG,eAAe,GAAG,sBAAsB,GAAG,mBAAmB;AAC7F,yBAAyB,mBAAO,CAAC,KAAqB;AACtD,qBAAqB,mBAAO,CAAC,KAAiB;AAC9C,oCAAoC,mBAAO,CAAC,KAAgB;AAC5D,kBAAkB,mBAAO,CAAC,KAA+B;AACzD,mBAAmB,mBAAO,CAAC,KAAgC;AAC3D,kBAAkB,mBAAO,CAAC,KAA+B;AACzD,mBAAmB,mBAAO,CAAC,KAAqB;AAChD,mBAAmB,mBAAO,CAAC,IAA0B;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA,wEAAwE,iBAAiB;AACzF;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,2CAA2C,oBAAoB;AAC/D,4BAA4B,wDAAwD,WAAW,oBAAoB,4CAA4C;AAC/J", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/components/global.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/components/loader.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/components/package.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/input/tex/require/RequireConfiguration.js"], "sourcesContent": ["\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MathJax = exports.combineWithMathJax = exports.combineDefaults = exports.combineConfig = exports.isObject = void 0;\nvar version_js_1 = require(\"./version.js\");\nfunction isObject(x) {\n    return typeof x === 'object' && x !== null;\n}\nexports.isObject = isObject;\nfunction combineConfig(dst, src) {\n    var e_1, _a;\n    try {\n        for (var _b = __values(Object.keys(src)), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var id = _c.value;\n            if (id === '__esModule')\n                continue;\n            if (isObject(dst[id]) && isObject(src[id]) &&\n                !(src[id] instanceof Promise)) {\n                combineConfig(dst[id], src[id]);\n            }\n            else if (src[id] !== null && src[id] !== undefined) {\n                dst[id] = src[id];\n            }\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n    return dst;\n}\nexports.combineConfig = combineConfig;\nfunction combineDefaults(dst, name, src) {\n    var e_2, _a;\n    if (!dst[name]) {\n        dst[name] = {};\n    }\n    dst = dst[name];\n    try {\n        for (var _b = __values(Object.keys(src)), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var id = _c.value;\n            if (isObject(dst[id]) && isObject(src[id])) {\n                combineDefaults(dst, id, src[id]);\n            }\n            else if (dst[id] == null && src[id] != null) {\n                dst[id] = src[id];\n            }\n        }\n    }\n    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_2) throw e_2.error; }\n    }\n    return dst;\n}\nexports.combineDefaults = combineDefaults;\nfunction combineWithMathJax(config) {\n    return combineConfig(exports.MathJax, config);\n}\nexports.combineWithMathJax = combineWithMathJax;\nif (typeof global.MathJax === 'undefined') {\n    global.MathJax = {};\n}\nif (!global.MathJax.version) {\n    global.MathJax = {\n        version: version_js_1.VERSION,\n        _: {},\n        config: global.MathJax\n    };\n}\nexports.MathJax = global.MathJax;\n//# sourceMappingURL=global.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar e_1, _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CONFIG = exports.MathJax = exports.Loader = exports.PathFilters = exports.PackageError = exports.Package = void 0;\nvar global_js_1 = require(\"./global.js\");\nvar package_js_1 = require(\"./package.js\");\nvar package_js_2 = require(\"./package.js\");\nObject.defineProperty(exports, \"Package\", { enumerable: true, get: function () { return package_js_2.Package; } });\nObject.defineProperty(exports, \"PackageError\", { enumerable: true, get: function () { return package_js_2.PackageError; } });\nvar FunctionList_js_1 = require(\"../util/FunctionList.js\");\nexports.PathFilters = {\n    source: function (data) {\n        if (exports.CONFIG.source.hasOwnProperty(data.name)) {\n            data.name = exports.CONFIG.source[data.name];\n        }\n        return true;\n    },\n    normalize: function (data) {\n        var name = data.name;\n        if (!name.match(/^(?:[a-z]+:\\/)?\\/|[a-z]:\\\\|\\[/i)) {\n            data.name = '[mathjax]/' + name.replace(/^\\.\\//, '');\n        }\n        if (data.addExtension && !name.match(/\\.[^\\/]+$/)) {\n            data.name += '.js';\n        }\n        return true;\n    },\n    prefix: function (data) {\n        var match;\n        while ((match = data.name.match(/^\\[([^\\]]*)\\]/))) {\n            if (!exports.CONFIG.paths.hasOwnProperty(match[1]))\n                break;\n            data.name = exports.CONFIG.paths[match[1]] + data.name.substr(match[0].length);\n        }\n        return true;\n    }\n};\nvar Loader;\n(function (Loader) {\n    var VERSION = global_js_1.MathJax.version;\n    Loader.versions = new Map();\n    function ready() {\n        var e_2, _a;\n        var names = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            names[_i] = arguments[_i];\n        }\n        if (names.length === 0) {\n            names = Array.from(package_js_1.Package.packages.keys());\n        }\n        var promises = [];\n        try {\n            for (var names_1 = __values(names), names_1_1 = names_1.next(); !names_1_1.done; names_1_1 = names_1.next()) {\n                var name_1 = names_1_1.value;\n                var extension = package_js_1.Package.packages.get(name_1) || new package_js_1.Package(name_1, true);\n                promises.push(extension.promise);\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (names_1_1 && !names_1_1.done && (_a = names_1.return)) _a.call(names_1);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return Promise.all(promises);\n    }\n    Loader.ready = ready;\n    function load() {\n        var e_3, _a;\n        var names = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            names[_i] = arguments[_i];\n        }\n        if (names.length === 0) {\n            return Promise.resolve();\n        }\n        var promises = [];\n        var _loop_1 = function (name_2) {\n            var extension = package_js_1.Package.packages.get(name_2);\n            if (!extension) {\n                extension = new package_js_1.Package(name_2);\n                extension.provides(exports.CONFIG.provides[name_2]);\n            }\n            extension.checkNoLoad();\n            promises.push(extension.promise.then(function () {\n                if (!exports.CONFIG.versionWarnings)\n                    return;\n                if (extension.isLoaded && !Loader.versions.has(package_js_1.Package.resolvePath(name_2))) {\n                    console.warn(\"No version information available for component \".concat(name_2));\n                }\n            }));\n        };\n        try {\n            for (var names_2 = __values(names), names_2_1 = names_2.next(); !names_2_1.done; names_2_1 = names_2.next()) {\n                var name_2 = names_2_1.value;\n                _loop_1(name_2);\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (names_2_1 && !names_2_1.done && (_a = names_2.return)) _a.call(names_2);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        package_js_1.Package.loadAll();\n        return Promise.all(promises);\n    }\n    Loader.load = load;\n    function preLoad() {\n        var e_4, _a;\n        var names = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            names[_i] = arguments[_i];\n        }\n        try {\n            for (var names_3 = __values(names), names_3_1 = names_3.next(); !names_3_1.done; names_3_1 = names_3.next()) {\n                var name_3 = names_3_1.value;\n                var extension = package_js_1.Package.packages.get(name_3);\n                if (!extension) {\n                    extension = new package_js_1.Package(name_3, true);\n                    extension.provides(exports.CONFIG.provides[name_3]);\n                }\n                extension.loaded();\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (names_3_1 && !names_3_1.done && (_a = names_3.return)) _a.call(names_3);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n    }\n    Loader.preLoad = preLoad;\n    function defaultReady() {\n        if (typeof exports.MathJax.startup !== 'undefined') {\n            exports.MathJax.config.startup.ready();\n        }\n    }\n    Loader.defaultReady = defaultReady;\n    function getRoot() {\n        var root = __dirname + '/../../es5';\n        if (typeof document !== 'undefined') {\n            var script = document.currentScript || document.getElementById('MathJax-script');\n            if (script) {\n                root = script.src.replace(/\\/[^\\/]*$/, '');\n            }\n        }\n        return root;\n    }\n    Loader.getRoot = getRoot;\n    function checkVersion(name, version, _type) {\n        Loader.versions.set(package_js_1.Package.resolvePath(name), VERSION);\n        if (exports.CONFIG.versionWarnings && version !== VERSION) {\n            console.warn(\"Component \".concat(name, \" uses \").concat(version, \" of MathJax; version in use is \").concat(VERSION));\n            return true;\n        }\n        return false;\n    }\n    Loader.checkVersion = checkVersion;\n    Loader.pathFilters = new FunctionList_js_1.FunctionList();\n    Loader.pathFilters.add(exports.PathFilters.source, 0);\n    Loader.pathFilters.add(exports.PathFilters.normalize, 10);\n    Loader.pathFilters.add(exports.PathFilters.prefix, 20);\n})(Loader = exports.Loader || (exports.Loader = {}));\nexports.MathJax = global_js_1.MathJax;\nif (typeof exports.MathJax.loader === 'undefined') {\n    (0, global_js_1.combineDefaults)(exports.MathJax.config, 'loader', {\n        paths: {\n            mathjax: Loader.getRoot()\n        },\n        source: {},\n        dependencies: {},\n        provides: {},\n        load: [],\n        ready: Loader.defaultReady.bind(Loader),\n        failed: function (error) { return console.log(\"MathJax(\".concat(error.package || '?', \"): \").concat(error.message)); },\n        require: null,\n        pathFilters: [],\n        versionWarnings: true\n    });\n    (0, global_js_1.combineWithMathJax)({\n        loader: Loader\n    });\n    try {\n        for (var _b = __values(exports.MathJax.config.loader.pathFilters), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var filter = _c.value;\n            if (Array.isArray(filter)) {\n                Loader.pathFilters.add(filter[0], filter[1]);\n            }\n            else {\n                Loader.pathFilters.add(filter);\n            }\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n}\nexports.CONFIG = exports.MathJax.config.loader;\n//# sourceMappingURL=loader.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Package = exports.PackageError = void 0;\nvar loader_js_1 = require(\"./loader.js\");\nvar PackageError = (function (_super) {\n    __extends(PackageError, _super);\n    function PackageError(message, name) {\n        var _this = _super.call(this, message) || this;\n        _this.package = name;\n        return _this;\n    }\n    return PackageError;\n}(Error));\nexports.PackageError = PackageError;\nvar Package = (function () {\n    function Package(name, noLoad) {\n        if (noLoad === void 0) { noLoad = false; }\n        this.isLoaded = false;\n        this.isLoading = false;\n        this.hasFailed = false;\n        this.dependents = [];\n        this.dependencies = [];\n        this.dependencyCount = 0;\n        this.provided = [];\n        this.name = name;\n        this.noLoad = noLoad;\n        Package.packages.set(name, this);\n        this.promise = this.makePromise(this.makeDependencies());\n    }\n    Object.defineProperty(Package.prototype, \"canLoad\", {\n        get: function () {\n            return this.dependencyCount === 0 && !this.noLoad && !this.isLoading && !this.hasFailed;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Package.resolvePath = function (name, addExtension) {\n        if (addExtension === void 0) { addExtension = true; }\n        var data = { name: name, original: name, addExtension: addExtension };\n        loader_js_1.Loader.pathFilters.execute(data);\n        return data.name;\n    };\n    Package.loadAll = function () {\n        var e_1, _a;\n        try {\n            for (var _b = __values(this.packages.values()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var extension = _c.value;\n                if (extension.canLoad) {\n                    extension.load();\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    Package.prototype.makeDependencies = function () {\n        var e_2, _a;\n        var promises = [];\n        var map = Package.packages;\n        var noLoad = this.noLoad;\n        var name = this.name;\n        var dependencies = [];\n        if (loader_js_1.CONFIG.dependencies.hasOwnProperty(name)) {\n            dependencies.push.apply(dependencies, __spreadArray([], __read(loader_js_1.CONFIG.dependencies[name]), false));\n        }\n        else if (name !== 'core') {\n            dependencies.push('core');\n        }\n        try {\n            for (var dependencies_1 = __values(dependencies), dependencies_1_1 = dependencies_1.next(); !dependencies_1_1.done; dependencies_1_1 = dependencies_1.next()) {\n                var dependent = dependencies_1_1.value;\n                var extension = map.get(dependent) || new Package(dependent, noLoad);\n                if (this.dependencies.indexOf(extension) < 0) {\n                    extension.addDependent(this, noLoad);\n                    this.dependencies.push(extension);\n                    if (!extension.isLoaded) {\n                        this.dependencyCount++;\n                        promises.push(extension.promise);\n                    }\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (dependencies_1_1 && !dependencies_1_1.done && (_a = dependencies_1.return)) _a.call(dependencies_1);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return promises;\n    };\n    Package.prototype.makePromise = function (promises) {\n        var _this = this;\n        var promise = new Promise((function (resolve, reject) {\n            _this.resolve = resolve;\n            _this.reject = reject;\n        }));\n        var config = (loader_js_1.CONFIG[this.name] || {});\n        if (config.ready) {\n            promise = promise.then(function (_name) { return config.ready(_this.name); });\n        }\n        if (promises.length) {\n            promises.push(promise);\n            promise = Promise.all(promises).then(function (names) { return names.join(', '); });\n        }\n        if (config.failed) {\n            promise.catch(function (message) { return config.failed(new PackageError(message, _this.name)); });\n        }\n        return promise;\n    };\n    Package.prototype.load = function () {\n        if (!this.isLoaded && !this.isLoading && !this.noLoad) {\n            this.isLoading = true;\n            var url = Package.resolvePath(this.name);\n            if (loader_js_1.CONFIG.require) {\n                this.loadCustom(url);\n            }\n            else {\n                this.loadScript(url);\n            }\n        }\n    };\n    Package.prototype.loadCustom = function (url) {\n        var _this = this;\n        try {\n            var result = loader_js_1.CONFIG.require(url);\n            if (result instanceof Promise) {\n                result.then(function () { return _this.checkLoad(); })\n                    .catch(function (err) { return _this.failed('Can\\'t load \"' + url + '\"\\n' + err.message.trim()); });\n            }\n            else {\n                this.checkLoad();\n            }\n        }\n        catch (err) {\n            this.failed(err.message);\n        }\n    };\n    Package.prototype.loadScript = function (url) {\n        var _this = this;\n        var script = document.createElement('script');\n        script.src = url;\n        script.charset = 'UTF-8';\n        script.onload = function (_event) { return _this.checkLoad(); };\n        script.onerror = function (_event) { return _this.failed('Can\\'t load \"' + url + '\"'); };\n        document.head.appendChild(script);\n    };\n    Package.prototype.loaded = function () {\n        var e_3, _a, e_4, _b;\n        this.isLoaded = true;\n        this.isLoading = false;\n        try {\n            for (var _c = __values(this.dependents), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var dependent = _d.value;\n                dependent.requirementSatisfied();\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        try {\n            for (var _e = __values(this.provided), _f = _e.next(); !_f.done; _f = _e.next()) {\n                var provided = _f.value;\n                provided.loaded();\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n        this.resolve(this.name);\n    };\n    Package.prototype.failed = function (message) {\n        this.hasFailed = true;\n        this.isLoading = false;\n        this.reject(new PackageError(message, this.name));\n    };\n    Package.prototype.checkLoad = function () {\n        var _this = this;\n        var config = (loader_js_1.CONFIG[this.name] || {});\n        var checkReady = config.checkReady || (function () { return Promise.resolve(); });\n        checkReady().then(function () { return _this.loaded(); })\n            .catch(function (message) { return _this.failed(message); });\n    };\n    Package.prototype.requirementSatisfied = function () {\n        if (this.dependencyCount) {\n            this.dependencyCount--;\n            if (this.canLoad) {\n                this.load();\n            }\n        }\n    };\n    Package.prototype.provides = function (names) {\n        var e_5, _a;\n        if (names === void 0) { names = []; }\n        try {\n            for (var names_1 = __values(names), names_1_1 = names_1.next(); !names_1_1.done; names_1_1 = names_1.next()) {\n                var name_1 = names_1_1.value;\n                var provided = Package.packages.get(name_1);\n                if (!provided) {\n                    if (!loader_js_1.CONFIG.dependencies[name_1]) {\n                        loader_js_1.CONFIG.dependencies[name_1] = [];\n                    }\n                    loader_js_1.CONFIG.dependencies[name_1].push(name_1);\n                    provided = new Package(name_1, true);\n                    provided.isLoading = true;\n                }\n                this.provided.push(provided);\n            }\n        }\n        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n        finally {\n            try {\n                if (names_1_1 && !names_1_1.done && (_a = names_1.return)) _a.call(names_1);\n            }\n            finally { if (e_5) throw e_5.error; }\n        }\n    };\n    Package.prototype.addDependent = function (extension, noLoad) {\n        this.dependents.push(extension);\n        if (!noLoad) {\n            this.checkNoLoad();\n        }\n    };\n    Package.prototype.checkNoLoad = function () {\n        var e_6, _a;\n        if (this.noLoad) {\n            this.noLoad = false;\n            try {\n                for (var _b = __values(this.dependencies), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var dependency = _c.value;\n                    dependency.checkNoLoad();\n                }\n            }\n            catch (e_6_1) { e_6 = { error: e_6_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_6) throw e_6.error; }\n            }\n        }\n    };\n    Package.packages = new Map();\n    return Package;\n}());\nexports.Package = Package;\n//# sourceMappingURL=package.js.map", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.RequireConfiguration = exports.options = exports.RequireMethods = exports.RequireLoad = void 0;\nvar Configuration_js_1 = require(\"../Configuration.js\");\nvar SymbolMap_js_1 = require(\"../SymbolMap.js\");\nvar TexError_js_1 = __importDefault(require(\"../TexError.js\"));\nvar global_js_1 = require(\"../../../components/global.js\");\nvar package_js_1 = require(\"../../../components/package.js\");\nvar loader_js_1 = require(\"../../../components/loader.js\");\nvar mathjax_js_1 = require(\"../../../mathjax.js\");\nvar Options_js_1 = require(\"../../../util/Options.js\");\nvar MJCONFIG = global_js_1.MathJax.config;\nfunction RegisterExtension(jax, name) {\n    var _a;\n    var require = jax.parseOptions.options.require;\n    var required = jax.parseOptions.packageData.get('require').required;\n    var extension = name.substr(require.prefix.length);\n    if (required.indexOf(extension) < 0) {\n        required.push(extension);\n        RegisterDependencies(jax, loader_js_1.CONFIG.dependencies[name]);\n        var handler = Configuration_js_1.ConfigurationHandler.get(extension);\n        if (handler) {\n            var options_1 = MJCONFIG[name] || {};\n            if (handler.options && Object.keys(handler.options).length === 1 && handler.options[extension]) {\n                options_1 = (_a = {}, _a[extension] = options_1, _a);\n            }\n            jax.configuration.add(extension, jax, options_1);\n            var configured = jax.parseOptions.packageData.get('require').configured;\n            if (handler.preprocessors.length && !configured.has(extension)) {\n                configured.set(extension, true);\n                mathjax_js_1.mathjax.retryAfter(Promise.resolve());\n            }\n        }\n    }\n}\nfunction RegisterDependencies(jax, names) {\n    var e_1, _a;\n    if (names === void 0) { names = []; }\n    var prefix = jax.parseOptions.options.require.prefix;\n    try {\n        for (var names_1 = __values(names), names_1_1 = names_1.next(); !names_1_1.done; names_1_1 = names_1.next()) {\n            var name_1 = names_1_1.value;\n            if (name_1.substr(0, prefix.length) === prefix) {\n                RegisterExtension(jax, name_1);\n            }\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (names_1_1 && !names_1_1.done && (_a = names_1.return)) _a.call(names_1);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n}\nfunction RequireLoad(parser, name) {\n    var options = parser.options.require;\n    var allow = options.allow;\n    var extension = (name.substr(0, 1) === '[' ? '' : options.prefix) + name;\n    var allowed = (allow.hasOwnProperty(extension) ? allow[extension] :\n        allow.hasOwnProperty(name) ? allow[name] : options.defaultAllow);\n    if (!allowed) {\n        throw new TexError_js_1.default('BadRequire', 'Extension \"%1\" is not allowed to be loaded', extension);\n    }\n    if (package_js_1.Package.packages.has(extension)) {\n        RegisterExtension(parser.configuration.packageData.get('require').jax, extension);\n    }\n    else {\n        mathjax_js_1.mathjax.retryAfter(loader_js_1.Loader.load(extension));\n    }\n}\nexports.RequireLoad = RequireLoad;\nfunction config(_config, jax) {\n    jax.parseOptions.packageData.set('require', {\n        jax: jax,\n        required: __spreadArray([], __read(jax.options.packages), false),\n        configured: new Map()\n    });\n    var options = jax.parseOptions.options.require;\n    var prefix = options.prefix;\n    if (prefix.match(/[^_a-zA-Z0-9]/)) {\n        throw Error('Illegal characters used in \\\\require prefix');\n    }\n    if (!loader_js_1.CONFIG.paths[prefix]) {\n        loader_js_1.CONFIG.paths[prefix] = '[mathjax]/input/tex/extensions';\n    }\n    options.prefix = '[' + prefix + ']/';\n}\nexports.RequireMethods = {\n    Require: function (parser, name) {\n        var required = parser.GetArgument(name);\n        if (required.match(/[^_a-zA-Z0-9]/) || required === '') {\n            throw new TexError_js_1.default('BadPackageName', 'Argument for %1 is not a valid package name', name);\n        }\n        RequireLoad(parser, required);\n    }\n};\nexports.options = {\n    require: {\n        allow: (0, Options_js_1.expandable)({\n            base: false,\n            'all-packages': false,\n            autoload: false,\n            configmacros: false,\n            tagformat: false,\n            setoptions: false\n        }),\n        defaultAllow: true,\n        prefix: 'tex'\n    }\n};\nnew SymbolMap_js_1.CommandMap('require', { require: 'Require' }, exports.RequireMethods);\nexports.RequireConfiguration = Configuration_js_1.Configuration.create('require', { handler: { macro: ['require'] }, config: config, options: exports.options });\n//# sourceMappingURL=RequireConfiguration.js.map"], "names": [], "sourceRoot": ""}