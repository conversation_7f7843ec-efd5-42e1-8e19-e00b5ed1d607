@echo off
echo 🎉 Python 3.12 + OpenBB 环境激活
echo ================================

call openbb_env_312\Scripts\activate.bat

echo ✅ Python 3.12 OpenBB环境已激活！
echo.
echo 📊 环境信息:
python --version
echo OpenBB Core: 已安装
echo OpenBB扩展: yfinance, equity, economy, news
echo.
echo 💡 快速测试:
echo python -c "from openbb_core.app.static.app_factory import create_app; print('✅ OpenBB导入成功')"
echo.
echo 🚀 开始使用:
echo - 运行测试: python test_openbb_312.py
echo - 开始分析: python your_script.py
echo - 退出环境: deactivate
echo.

cmd /k
