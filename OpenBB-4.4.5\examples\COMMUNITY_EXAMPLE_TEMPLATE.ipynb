{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Instructions for Contributors\n", "\n", "Welcome to this example notebook for OpenBB! Please follow the steps below:\n", "\n", "1. **Fill in the details**: Customize the second cell with the name of the notebook, your GitHub profile link, and a brief description of what your notebook demonstrates.\n", "2. **Add Your Code**: Make sure to include clean and commented code sections throughout the notebook.\n", "3. **Test Before Submitting**: Run all cells to ensure the notebook functions as expected.\n", "4. **Keep it Simple and Clear**: Make your explanations and code as clear as possible for others to follow.\n", "5. **Run in Colab Button**: Ensure the \"Run in Colab\" button links properly to the notebook. You can test it by clicking the button and verifying it loads your notebook.\n", "\n", "Please refer to the documentation at [OpenBB Documentation](https://docs.openbb.co/) for additional guidance.\n", "\n", "Remove this cell before submitting your notebook."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## [Notebook Name]\n", "\n", "#### Description\n", "[Briefly describe what this notebook demonstrates, e.g., \"This notebook demonstrates how to backtest a momentum trading strategy using OpenBB's historical data.\"]\n", "\n", "#### Author\n", "[Your Name](https://github.com/[YourGitHubUsername])\n", "\n", "[![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/OpenBB-Finance/OpenBB/blob/develop/examples/[Notebook_Name].ipynb)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you are running this notebook in Colab, you can run the following command to install the OpenBB Platform:\n", "\n", "```python\n", "!pip install openbb\n", "```\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from openbb import obb"]}], "metadata": {"kernelspec": {"display_name": "obb", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}