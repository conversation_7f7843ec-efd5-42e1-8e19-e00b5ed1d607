[tool.poetry]
name = "openbb-tmx"
version = "1.3.2"
description = "Unofficial TMX data provider extension for the OpenBB Platform - Public Canadian markets data for Python and Fast API."
authors = ["OpenBB <<EMAIL>>"]
license = "AGPL-3.0-only"
readme = "README.md"
packages = [{ include = "openbb_tmx" }]

[tool.poetry.dependencies]
python = ">=3.9.21,<3.13"
aiohttp-client-cache = "^0.11.0"
aiosqlite = "^0.20.0"
random-user-agent = "^1.0.1"
exchange-calendars = "^4.5.4"
openbb-core = "^1.4.6"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.plugins."openbb_provider_extension"]
tmx = "openbb_tmx:tmx_provider"
