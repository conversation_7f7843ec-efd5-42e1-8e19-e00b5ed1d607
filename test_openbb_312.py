#!/usr/bin/env python3
"""
Python 3.12 + OpenBB 环境测试脚本
验证Python 3.12环境中的OpenBB安装是否完全正常
"""

import sys
import os
from datetime import datetime


def print_header():
    """打印测试头部信息"""
    print("🎉 Python 3.12 + OpenBB 环境测试")
    print("=" * 50)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)


def test_python_version():
    """测试Python版本"""
    print("\n🐍 Python版本检查")
    print("-" * 30)
    
    version_info = sys.version_info
    version_str = f"{version_info.major}.{version_info.minor}.{version_info.micro}"
    
    print(f"✅ Python版本: {version_str}")
    
    if version_info.major == 3 and version_info.minor == 12:
        print("✅ Python 3.12版本确认")
        return True
    else:
        print(f"⚠️ 期望Python 3.12，实际版本: {version_str}")
        return False


def test_virtual_environment():
    """测试虚拟环境"""
    print("\n📦 虚拟环境检查")
    print("-" * 30)
    
    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 运行在虚拟环境中")
        print(f"   虚拟环境路径: {sys.prefix}")
        
        # 检查是否是我们创建的环境
        if "openbb_env_312" in sys.prefix:
            print("✅ 确认为OpenBB Python 3.12环境")
            return True
        else:
            print("⚠️ 不是预期的OpenBB环境")
            return False
    else:
        print("❌ 不在虚拟环境中")
        return False


def test_core_libraries():
    """测试核心库"""
    print("\n📚 核心库测试")
    print("-" * 30)
    
    libraries = [
        ("pandas", "数据处理"),
        ("numpy", "数值计算"),
        ("requests", "HTTP请求"),
        ("aiohttp", "异步HTTP"),
        ("fastapi", "Web框架"),
        ("pydantic", "数据验证"),
    ]
    
    success_count = 0
    for lib_name, description in libraries:
        try:
            __import__(lib_name)
            print(f"✅ {lib_name}: {description}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {lib_name}: 导入失败 - {e}")
    
    print(f"\n📊 核心库测试结果: {success_count}/{len(libraries)}")
    return success_count == len(libraries)


def test_openbb_components():
    """测试OpenBB组件"""
    print("\n🔧 OpenBB组件测试")
    print("-" * 30)
    
    components = [
        ("openbb_core", "OpenBB核心"),
        ("openbb_yfinance", "Yahoo Finance扩展"),
        ("openbb_equity", "股票数据扩展"),
        ("openbb_economy", "经济数据扩展"),
        ("openbb_news", "新闻数据扩展"),
        ("yfinance", "Yahoo Finance库"),
    ]
    
    success_count = 0
    for comp_name, description in components:
        try:
            __import__(comp_name)
            print(f"✅ {comp_name}: {description}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {comp_name}: 导入失败 - {e}")
    
    print(f"\n📊 OpenBB组件测试结果: {success_count}/{len(components)}")
    return success_count >= 5  # 至少5个组件成功


def test_openbb_functionality():
    """测试OpenBB核心功能"""
    print("\n⚙️ OpenBB功能测试")
    print("-" * 30)
    
    try:
        from openbb_core.app.static.app_factory import create_app
        
        # 创建OpenBB应用
        app = create_app()
        print("✅ OpenBB应用创建成功")
        
        # 检查应用类型
        app_type = type(app).__name__
        print(f"✅ 应用类型: {app_type}")
        
        # 检查可用方法
        methods = [method for method in dir(app) if not method.startswith('_')]
        print(f"✅ 可用方法数量: {len(methods)}")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenBB功能测试失败: {e}")
        return False


def test_data_processing():
    """测试数据处理功能"""
    print("\n📊 数据处理测试")
    print("-" * 30)
    
    try:
        import pandas as pd
        import numpy as np
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=10, freq='D')
        data = pd.DataFrame({
            'Date': dates,
            'Price': np.random.uniform(100, 200, 10),
            'Volume': np.random.randint(1000, 10000, 10)
        })
        
        print(f"✅ 测试数据创建成功 - {len(data)}行")
        
        # 基本统计
        mean_price = data['Price'].mean()
        print(f"✅ 数据统计计算成功 - 平均价格: {mean_price:.2f}")
        
        # 数据导出测试
        csv_file = "test_data_312.csv"
        data.to_csv(csv_file, index=False)
        print(f"✅ 数据导出成功: {csv_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据处理测试失败: {e}")
        return False


def test_yfinance_basic():
    """测试yfinance基本功能"""
    print("\n📈 YFinance基本测试")
    print("-" * 30)
    
    try:
        import yfinance as yf
        
        # 创建ticker对象（不进行网络请求）
        ticker = yf.Ticker("AAPL")
        print("✅ YFinance Ticker对象创建成功")
        
        # 检查ticker属性
        if hasattr(ticker, 'ticker'):
            print(f"✅ Ticker符号: {ticker.ticker}")
        
        print("✅ YFinance基本功能正常")
        return True
        
    except Exception as e:
        print(f"❌ YFinance测试失败: {e}")
        return False


def test_package_versions():
    """测试包版本信息"""
    print("\n📋 包版本信息")
    print("-" * 30)
    
    packages = [
        "openbb_core",
        "pandas", 
        "numpy",
        "yfinance",
        "fastapi",
    ]
    
    for package in packages:
        try:
            module = __import__(package)
            version = getattr(module, '__version__', '未知')
            print(f"✅ {package}: {version}")
        except ImportError:
            print(f"❌ {package}: 未安装")
        except Exception as e:
            print(f"⚠️ {package}: 版本获取失败")
    
    return True


def generate_final_report(results):
    """生成最终测试报告"""
    print("\n" + "=" * 50)
    print("📋 Python 3.12 + OpenBB 测试报告")
    print("=" * 50)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\n📊 详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！Python 3.12 + OpenBB环境完美配置！")
        print("\n💡 下一步建议:")
        print("1. 开始您的金融数据分析项目")
        print("2. 尝试获取真实的股票数据")
        print("3. 探索OpenBB的高级功能")
        print("4. 安装更多OpenBB扩展")
    else:
        print("\n⚠️ 部分测试失败，但核心功能可用")
        print("\n🔧 建议:")
        print("1. 检查失败的组件")
        print("2. 重新安装有问题的包")
        print("3. 确认网络连接正常")
    
    return passed_tests == total_tests


def main():
    """主测试函数"""
    print_header()
    
    # 执行所有测试
    test_results = {}
    
    test_results["Python版本"] = test_python_version()
    test_results["虚拟环境"] = test_virtual_environment()
    test_results["核心库"] = test_core_libraries()
    test_results["OpenBB组件"] = test_openbb_components()
    test_results["OpenBB功能"] = test_openbb_functionality()
    test_results["数据处理"] = test_data_processing()
    test_results["YFinance基本功能"] = test_yfinance_basic()
    test_results["包版本信息"] = test_package_versions()
    
    # 生成最终报告
    success = generate_final_report(test_results)
    
    return success


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)
