../../doc/pycurl/AUTHORS,sha256=51S08-Axa7reBn98B0hl-BhK62h4mPu2sqdZ48ieGLA,5234
../../doc/pycurl/COPYING-LGPL,sha256=Hn5rrlpb3jLxrlp8N6CC0asDz4k1T3-TasQL6eOaZTE,27032
../../doc/pycurl/COPYING-MIT,sha256=Dz86EtF_jkZn6ChrtUnnAQZVvO_cCKR0AUl6l_o7wnQ,1210
../../doc/pycurl/ChangeLog,sha256=1RgMTYncMWxSVchKWzvsTo76CyfJX2WynH__cWDBCP8,65696
../../doc/pycurl/INSTALL.rst,sha256=-vvj6gsbKG3wHkNHpMPigZ3Wy6nBHxfFtcz9iMsrZMI,12523
../../doc/pycurl/README.rst,sha256=NfrdM_JLlj1a2reZ9f4nYPxGfBCvcCunYlJJJsB7db4,6692
../../doc/pycurl/RELEASE-NOTES.rst,sha256=4DpGpujZTdnWEQHmDHQkUT0ijkC8nFjY7qTwEbJ2u10,9144
../../doc/pycurl/examples/__pycache__/basicfirst.cpython-312.pyc,,
../../doc/pycurl/examples/__pycache__/file_upload.cpython-312.pyc,,
../../doc/pycurl/examples/__pycache__/linksys.cpython-312.pyc,,
../../doc/pycurl/examples/__pycache__/multi-socket_action-select.cpython-312.pyc,,
../../doc/pycurl/examples/__pycache__/opensocketexception.cpython-312.pyc,,
../../doc/pycurl/examples/__pycache__/retriever-multi.cpython-312.pyc,,
../../doc/pycurl/examples/__pycache__/retriever.cpython-312.pyc,,
../../doc/pycurl/examples/__pycache__/sfquery.cpython-312.pyc,,
../../doc/pycurl/examples/__pycache__/smtp.cpython-312.pyc,,
../../doc/pycurl/examples/__pycache__/ssh_keyfunction.cpython-312.pyc,,
../../doc/pycurl/examples/__pycache__/xmlrpc_curl.cpython-312.pyc,,
../../doc/pycurl/examples/basicfirst.py,sha256=bIdhtx77rEdatLq5D8-1vZQxsixiMaPcfzWC5WcHhyc,574
../../doc/pycurl/examples/file_upload.py,sha256=J_Wg7JQglCyCcsh4yhO4vw0b9T70bKbX1qdx7qzXejg,1224
../../doc/pycurl/examples/linksys.py,sha256=IqTpZUPVXbjJgC8ISSn1deotQYpv5rr3UaLOZ_0l5ac,22339
../../doc/pycurl/examples/multi-socket_action-select.py,sha256=qunevbnIWe1Pez7gB4tySAGw8_NyZTYUHDr42ZRD9HA,8197
../../doc/pycurl/examples/opensocketexception.py,sha256=a49ddnQSyMqjQSvKohuUrhxomZJo_ceF0_vMB8Y8G9U,891
../../doc/pycurl/examples/quickstart/__pycache__/file_upload_buffer.cpython-312.pyc,,
../../doc/pycurl/examples/quickstart/__pycache__/file_upload_real.cpython-312.pyc,,
../../doc/pycurl/examples/quickstart/__pycache__/file_upload_real_fancy.cpython-312.pyc,,
../../doc/pycurl/examples/quickstart/__pycache__/follow_redirect.cpython-312.pyc,,
../../doc/pycurl/examples/quickstart/__pycache__/form_post.cpython-312.pyc,,
../../doc/pycurl/examples/quickstart/__pycache__/get.cpython-312.pyc,,
../../doc/pycurl/examples/quickstart/__pycache__/get_python2.cpython-312.pyc,,
../../doc/pycurl/examples/quickstart/__pycache__/get_python2_https.cpython-312.pyc,,
../../doc/pycurl/examples/quickstart/__pycache__/get_python3.cpython-312.pyc,,
../../doc/pycurl/examples/quickstart/__pycache__/get_python3_https.cpython-312.pyc,,
../../doc/pycurl/examples/quickstart/__pycache__/put_buffer.cpython-312.pyc,,
../../doc/pycurl/examples/quickstart/__pycache__/put_file.cpython-312.pyc,,
../../doc/pycurl/examples/quickstart/__pycache__/response_headers.cpython-312.pyc,,
../../doc/pycurl/examples/quickstart/__pycache__/response_info.cpython-312.pyc,,
../../doc/pycurl/examples/quickstart/__pycache__/write_file.cpython-312.pyc,,
../../doc/pycurl/examples/quickstart/file_upload_buffer.py,sha256=nxwTJ32cZc10lj9SpB4rP4iyT9_M6XC2sNslnoH-uUk,329
../../doc/pycurl/examples/quickstart/file_upload_real.py,sha256=7yjJhKJ2PINu7BeNLuQW5cigr6zOw8T_1pNtChR_joE,309
../../doc/pycurl/examples/quickstart/file_upload_real_fancy.py,sha256=db4liQ-gk_GgP9jM_l6Vvt-y5nSYo4W5s2G2rgWqo4c,505
../../doc/pycurl/examples/quickstart/follow_redirect.py,sha256=DvoONbOsEh1nbAxbGxDRyCg4aV5WY9qXDyrNm6LFQ8k,263
../../doc/pycurl/examples/quickstart/form_post.py,sha256=FAcxBbJ0JikK4uz47mV8teiKcle1PqQLn8-ODmUUv9w,589
../../doc/pycurl/examples/quickstart/get.py,sha256=BdtfdLwgHIcgUo6LZbSi6da0CIvybbSVBN3Ygvbuzdo,607
../../doc/pycurl/examples/quickstart/get_python2.py,sha256=C4FyRoL4J6JPn4gThYuMDtJd_KHoM9_D1tt7F8gI_h8,467
../../doc/pycurl/examples/quickstart/get_python2_https.py,sha256=6tRofpiYtZ0_-FmIR3YpfvKk7ngPkLWFYPYOC8EtBvg,520
../../doc/pycurl/examples/quickstart/get_python3.py,sha256=y3BSrMQAAWdjKRau6La46IHxug8JovjechdJPZRSatg,422
../../doc/pycurl/examples/quickstart/get_python3_https.py,sha256=NzDceuLReq79hZNaJ4bLciUCUhfy7zeVobDv1prl-E8,475
../../doc/pycurl/examples/quickstart/put_buffer.py,sha256=GJACEKozE-H-nfYdb7zfE0Fzq4PjEfLhUWC82MusXEQ,494
../../doc/pycurl/examples/quickstart/put_file.py,sha256=MddoSaysv0pLhwD19hVy1VhZh0wJ5Pn1-2tX6DBKq2I,317
../../doc/pycurl/examples/quickstart/response_headers.py,sha256=tdTO2xKIx7aT3uZN9CDvq6ApWRTXiwspKMun3OCwZFs,2165
../../doc/pycurl/examples/quickstart/response_info.py,sha256=1Jc5zaOGfLB_O2aj5t8yW4PcWw-jJTlwlpAaeeeHrOQ,521
../../doc/pycurl/examples/quickstart/write_file.py,sha256=89I0uRB6HV5j4w0Ul7re6DiRxvga0yqgZXkxOHzRli0,371
../../doc/pycurl/examples/retriever-multi.py,sha256=D2N4jXV8zmHWgcpCz3fJalbdVoqNOyuikkVlH7D6RP8,3482
../../doc/pycurl/examples/retriever.py,sha256=TLClMbcbxAS9pz53BBtueVDUKufhfFPvyan_ghuKYT0,2768
../../doc/pycurl/examples/sfquery.py,sha256=OU0BVsu0KMwiXuh3hrbiZbqzbs8P2vBtzbyd2_B1Orw,2522
../../doc/pycurl/examples/smtp.py,sha256=9zGU0cY20WJrGyc0JaD9uRKeKZlna_RJx6y82UzHoaU,1145
../../doc/pycurl/examples/ssh_keyfunction.py,sha256=qAVWo_kj4Ti4S-dtcGf7caaSFIoNgXnt4NrMA4Va2S8,317
../../doc/pycurl/examples/xmlrpc_curl.py,sha256=0_ic103Biy9MQ1bxEPNxkaI0c2yxnqBPlFZ6qFPeRrY,2256
brotlicommon-43f6fd7f2af8c42052e79c3234b2c97d.dll,sha256=OZOVH0kLuVWiI9pyCic7sU1nu7eTQbhnPiZAkIl9YzY,137728
brotlidec-7c04bb5c1c603e26391e5701c3c77b32.dll,sha256=1rjTIl88ztbTPOIeqsjEMF8mmrCOXryCSQV52LNSbEM,51200
curl/__init__.py,sha256=OA4NTy4W6ukGXlSU56m5Sm3G3lxB2dZPe3dhintFddY,7975
curl/__pycache__/__init__.cpython-312.pyc,,
libcrypto-3-x64-a2205973769166fefb116e91e5d3d5f5.dll,sha256=SNWk1FYd7oOM0Tbbnr0d3TtwGCokyUsnmt9lrZuc2YQ,4842496
libcurl-b0b6abe44684cde1adf42de936617924.dll,sha256=WEDYkiR5Qjgrm-uCvyMGKbvELJU8lCzoHuEY7AdO-94,727040
libssh2-8dc5970555b02475bceab1c1654deebd.dll,sha256=1Ws8I3lWeJFNj8mL_T0r7x4XcK2Q0MA3sg95xacpZ_s,263680
libssl-3-x64-7540684b8e34d93282a7ec1669675648.dll,sha256=XyUUQU5yYDpxmX2NvfRoVTRuZ8VSUTzsWutr_xuFrl0,829440
nghttp2-0afc6cc02a2ff04291c787bf1842e865.dll,sha256=C0lTIgW0ZYABfeuAhzeFGFwfhEE8ecPOAUkJmvoW7hI,162304
pycurl-7.45.6.dist-info/AUTHORS,sha256=51S08-Axa7reBn98B0hl-BhK62h4mPu2sqdZ48ieGLA,5234
pycurl-7.45.6.dist-info/COPYING-LGPL,sha256=Hn5rrlpb3jLxrlp8N6CC0asDz4k1T3-TasQL6eOaZTE,27032
pycurl-7.45.6.dist-info/COPYING-MIT,sha256=Dz86EtF_jkZn6ChrtUnnAQZVvO_cCKR0AUl6l_o7wnQ,1210
pycurl-7.45.6.dist-info/DELVEWHEEL,sha256=dBR8eBI_cR9FKzFltHiJdXYJXheDhGCSWpVYGpHs6go,452
pycurl-7.45.6.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pycurl-7.45.6.dist-info/METADATA,sha256=EFWvNrVBNpTSCw24u4AGwHYrKFDUROzt62fkuGN0lIc,4729
pycurl-7.45.6.dist-info/RECORD,,
pycurl-7.45.6.dist-info/WHEEL,sha256=GNSMLKsI4JcODC8iIUwfCC2mP5aj3pHj8CNqlVH-mAI,101
pycurl-7.45.6.dist-info/top_level.txt,sha256=nGOR8naDoonDhRxux0vjGbA45NpodXw1NT2dpl-FJEI,12
pycurl.cp312-win_amd64.pyd,sha256=0kgBSEOvny3bvOR6LvJ4DvhdRFyJN0t37KWvPzuKcqI,142848
zlib1-2b926745f501cc2241ee3c6167ccd73c.dll,sha256=t2IcnvtS5qYL2Lm90REsFSoVEDJeXtxEal4YhT6GrB0,90624
