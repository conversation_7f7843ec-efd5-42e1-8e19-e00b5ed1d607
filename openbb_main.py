#!/usr/bin/env python3
"""
OpenBB 正式金融分析系统
专业级股票分析和投资组合管理工具
"""

import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from openbb_core.app.static.app_factory import create_app


class OpenBBAnalyzer:
    """OpenBB 专业金融分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.app = create_app()
        self.data_cache = {}
        print("🚀 OpenBB 金融分析系统已启动")
        print("=" * 50)
    
    def get_stock_data(self, symbol, period="3mo", use_cache=True):
        """
        获取股票数据
        
        Args:
            symbol: 股票代码
            period: 时间周期 (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
            use_cache: 是否使用缓存
        """
        cache_key = f"{symbol}_{period}"
        
        if use_cache and cache_key in self.data_cache:
            print(f"📋 使用缓存数据: {symbol}")
            return self.data_cache[cache_key]
        
        try:
            import yfinance as yf
            
            print(f"📈 获取 {symbol} 数据 ({period})...")
            time.sleep(1)  # 避免API限制
            
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period)
            
            if not data.empty:
                # 缓存数据
                self.data_cache[cache_key] = data
                print(f"✅ 成功获取 {len(data)} 条数据")
                return data
            else:
                print(f"⚠️ {symbol} 数据为空")
                return None
                
        except Exception as e:
            print(f"❌ 获取 {symbol} 数据失败: {e}")
            return None
    
    def get_stock_info(self, symbol):
        """获取股票基本信息"""
        try:
            import yfinance as yf
            
            print(f"📊 获取 {symbol} 基本信息...")
            time.sleep(1)
            
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            if info:
                return {
                    'symbol': info.get('symbol', symbol),
                    'name': info.get('longName', 'N/A'),
                    'sector': info.get('sector', 'N/A'),
                    'industry': info.get('industry', 'N/A'),
                    'market_cap': info.get('marketCap', 0),
                    'current_price': info.get('currentPrice', 0),
                    'pe_ratio': info.get('trailingPE', 0),
                    'dividend_yield': info.get('dividendYield', 0),
                }
            return None
            
        except Exception as e:
            print(f"❌ 获取 {symbol} 信息失败: {e}")
            return None
    
    def technical_analysis(self, symbol, period="6mo"):
        """技术分析"""
        print(f"\n🔍 {symbol} 技术分析")
        print("-" * 40)
        
        data = self.get_stock_data(symbol, period)
        if data is None:
            return None
        
        # 计算技术指标
        data = data.copy()
        data['MA_5'] = data['Close'].rolling(window=5).mean()
        data['MA_20'] = data['Close'].rolling(window=20).mean()
        data['MA_50'] = data['Close'].rolling(window=50).mean()
        
        # RSI指标
        data['RSI'] = self._calculate_rsi(data['Close'])
        
        # MACD指标
        data['MACD'], data['MACD_Signal'] = self._calculate_macd(data['Close'])
        
        # 布林带
        data['BB_Upper'], data['BB_Lower'] = self._calculate_bollinger_bands(data['Close'])
        
        # 分析结果
        latest = data.iloc[-1]
        
        print(f"📊 当前价格: ${latest['Close']:.2f}")
        print(f"📈 5日均线: ${latest['MA_5']:.2f}")
        print(f"📈 20日均线: ${latest['MA_20']:.2f}")
        print(f"📈 50日均线: ${latest['MA_50']:.2f}")
        print(f"📊 RSI: {latest['RSI']:.1f}")
        print(f"📊 MACD: {latest['MACD']:.3f}")
        
        # 生成交易信号
        signals = self._generate_signals(data)
        print(f"\n🎯 交易信号:")
        for signal in signals:
            print(f"   {signal}")
        
        return data
    
    def portfolio_analysis(self, symbols, weights=None):
        """投资组合分析"""
        print(f"\n📊 投资组合分析")
        print("-" * 40)
        print(f"股票组合: {', '.join(symbols)}")
        
        if weights is None:
            weights = [1/len(symbols)] * len(symbols)
        
        portfolio_data = {}
        
        # 获取所有股票数据
        for symbol in symbols:
            data = self.get_stock_data(symbol, period="1y")
            if data is not None:
                portfolio_data[symbol] = data
        
        if not portfolio_data:
            print("❌ 无法获取投资组合数据")
            return None
        
        # 计算收益率
        returns = {}
        for symbol, data in portfolio_data.items():
            returns[symbol] = data['Close'].pct_change().dropna()
        
        returns_df = pd.DataFrame(returns)
        
        # 投资组合收益率
        portfolio_returns = (returns_df * weights).sum(axis=1)
        
        # 计算统计指标
        stats = {
            'annual_return': portfolio_returns.mean() * 252,
            'annual_volatility': portfolio_returns.std() * np.sqrt(252),
            'sharpe_ratio': (portfolio_returns.mean() * 252) / (portfolio_returns.std() * np.sqrt(252)),
            'max_drawdown': self._calculate_max_drawdown(portfolio_returns),
            'var_95': portfolio_returns.quantile(0.05),
            'var_99': portfolio_returns.quantile(0.01)
        }
        
        print(f"📈 年化收益率: {stats['annual_return']*100:.2f}%")
        print(f"📊 年化波动率: {stats['annual_volatility']*100:.2f}%")
        print(f"⚡ 夏普比率: {stats['sharpe_ratio']:.2f}")
        print(f"📉 最大回撤: {stats['max_drawdown']*100:.2f}%")
        print(f"⚠️ 95% VaR: {stats['var_95']*100:.2f}%")
        print(f"⚠️ 99% VaR: {stats['var_99']*100:.2f}%")
        
        return stats, returns_df, portfolio_data
    
    def market_screening(self, symbols_list):
        """市场筛选"""
        print(f"\n🔍 市场筛选分析")
        print("-" * 40)
        
        results = []
        
        for symbol in symbols_list:
            try:
                info = self.get_stock_info(symbol)
                data = self.get_stock_data(symbol, period="1mo")
                
                if info and data is not None:
                    # 计算简单指标
                    price_change = ((data['Close'].iloc[-1] - data['Close'].iloc[0]) / data['Close'].iloc[0]) * 100
                    avg_volume = data['Volume'].mean()
                    
                    results.append({
                        'Symbol': symbol,
                        'Name': info['name'][:30],
                        'Price': info['current_price'],
                        'Change_1M': price_change,
                        'PE_Ratio': info['pe_ratio'],
                        'Market_Cap': info['market_cap'],
                        'Avg_Volume': avg_volume
                    })
                    
            except Exception as e:
                print(f"⚠️ {symbol} 筛选失败: {e}")
                continue
        
        if results:
            df = pd.DataFrame(results)
            print(f"✅ 筛选完成，共 {len(df)} 只股票")
            return df
        else:
            print("❌ 筛选失败")
            return None
    
    def save_analysis(self, data, filename):
        """保存分析结果"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if isinstance(data, dict):
                # 保存多个数据表
                with pd.ExcelWriter(f"{filename}_{timestamp}.xlsx") as writer:
                    for sheet_name, df in data.items():
                        if isinstance(df, pd.DataFrame):
                            df.to_excel(writer, sheet_name=sheet_name)
            else:
                # 保存单个数据表
                if isinstance(data, pd.DataFrame):
                    data.to_excel(f"{filename}_{timestamp}.xlsx")
                    data.to_csv(f"{filename}_{timestamp}.csv")
            
            print(f"✅ 分析结果已保存: {filename}_{timestamp}")
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    def _calculate_rsi(self, prices, window=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, prices, fast=12, slow=26, signal=9):
        """计算MACD指标"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        return macd, macd_signal
    
    def _calculate_bollinger_bands(self, prices, window=20, num_std=2):
        """计算布林带"""
        ma = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper = ma + (std * num_std)
        lower = ma - (std * num_std)
        return upper, lower
    
    def _calculate_max_drawdown(self, returns):
        """计算最大回撤"""
        cumulative = (1 + returns).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdown = (cumulative - rolling_max) / rolling_max
        return drawdown.min()
    
    def _generate_signals(self, data):
        """生成交易信号"""
        signals = []
        latest = data.iloc[-1]
        
        # 价格与均线关系
        if latest['Close'] > latest['MA_20']:
            signals.append("🟢 价格在20日均线上方 (看涨)")
        else:
            signals.append("🔴 价格在20日均线下方 (看跌)")
        
        # RSI信号
        if latest['RSI'] > 70:
            signals.append("🔴 RSI超买 (>70)")
        elif latest['RSI'] < 30:
            signals.append("🟢 RSI超卖 (<30)")
        else:
            signals.append("🟡 RSI中性 (30-70)")
        
        # MACD信号
        if latest['MACD'] > latest['MACD_Signal']:
            signals.append("🟢 MACD金叉 (看涨)")
        else:
            signals.append("🔴 MACD死叉 (看跌)")
        
        return signals


def main():
    """主程序"""
    print("🎯 OpenBB 专业金融分析系统")
    print("=" * 50)
    
    # 初始化分析器
    analyzer = OpenBBAnalyzer()
    
    # 示例分析
    print("\n📈 示例1: 单股票技术分析")
    tech_data = analyzer.technical_analysis("AAPL", period="6mo")
    
    print("\n📊 示例2: 投资组合分析")
    portfolio_symbols = ["AAPL", "MSFT", "GOOGL", "TSLA"]
    portfolio_weights = [0.3, 0.3, 0.2, 0.2]
    
    try:
        stats, returns, portfolio_data = analyzer.portfolio_analysis(
            portfolio_symbols, portfolio_weights
        )
        
        # 保存分析结果
        if tech_data is not None and stats is not None:
            analysis_results = {
                "Technical_Analysis": tech_data,
                "Portfolio_Returns": returns,
                "Portfolio_Stats": pd.DataFrame([stats])
            }
            analyzer.save_analysis(analysis_results, "openbb_analysis")
        
    except Exception as e:
        print(f"⚠️ 投资组合分析遇到限制: {e}")
        print("建议稍后重试或配置API密钥")
    
    print("\n🔍 示例3: 市场筛选")
    screening_symbols = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"]
    
    try:
        screening_results = analyzer.market_screening(screening_symbols)
        if screening_results is not None:
            print("\n📋 筛选结果:")
            print(screening_results.to_string(index=False))
            
    except Exception as e:
        print(f"⚠️ 市场筛选遇到限制: {e}")
    
    print("\n" + "=" * 50)
    print("✅ OpenBB 分析完成！")
    print("\n💡 使用提示:")
    print("1. 修改股票代码进行自定义分析")
    print("2. 调整时间周期获取不同数据")
    print("3. 配置API密钥获得更好服务")
    print("4. 查看生成的Excel和CSV文件")


if __name__ == "__main__":
    main()
