#!/usr/bin/env python3
"""
A股数据源集成
优先使用Tushare Pro，备选AKShare和东方财富API
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


class AStockDataProvider:
    """A股数据提供商 - 多数据源集成"""
    
    def __init__(self, tushare_token=None):
        """
        初始化A股数据提供商
        
        Args:
            tushare_token: Tushare Pro API Token
        """
        self.tushare_token = tushare_token
        self.providers = []
        
        # 初始化数据源
        self._init_tushare()
        self._init_akshare()
        self._init_efinance()
        
        print(f"🚀 A股数据源初始化完成")
        print(f"可用数据源: {', '.join(self.providers)}")
    
    def _init_tushare(self):
        """初始化Tushare Pro"""
        try:
            import tushare as ts
            
            if self.tushare_token:
                ts.set_token(self.tushare_token)
                self.ts_pro = ts.pro_api()
                self.providers.append("Tushare Pro")
                print("✅ Tushare Pro 初始化成功")
            else:
                # 使用免费接口
                self.ts = ts
                self.providers.append("Tushare Free")
                print("✅ Tushare 免费版初始化成功")
                
        except ImportError:
            print("❌ Tushare 未安装")
        except Exception as e:
            print(f"⚠️ Tushare 初始化失败: {e}")
    
    def _init_akshare(self):
        """初始化AKShare"""
        try:
            import akshare as ak
            self.ak = ak
            self.providers.append("AKShare")
            print("✅ AKShare 初始化成功")
        except ImportError:
            print("❌ AKShare 未安装")
        except Exception as e:
            print(f"⚠️ AKShare 初始化失败: {e}")
    
    def _init_efinance(self):
        """初始化东方财富"""
        try:
            import efinance as ef
            self.ef = ef
            self.providers.append("东方财富")
            print("✅ 东方财富 初始化成功")
        except ImportError:
            print("❌ 东方财富 未安装")
        except Exception as e:
            print(f"⚠️ 东方财富 初始化失败: {e}")
    
    def get_stock_basic(self):
        """获取股票基本信息"""
        print("📋 获取A股基本信息...")
        
        # 优先使用Tushare Pro
        if hasattr(self, 'ts_pro'):
            try:
                data = self.ts_pro.stock_basic(exchange='', list_status='L')
                print("✅ 使用Tushare Pro获取股票列表")
                return data
            except Exception as e:
                print(f"⚠️ Tushare Pro失败: {e}")
        
        # 备选AKShare
        if hasattr(self, 'ak'):
            try:
                data = self.ak.stock_info_a_code_name()
                print("✅ 使用AKShare获取股票列表")
                return data
            except Exception as e:
                print(f"⚠️ AKShare失败: {e}")
        
        # 备选东方财富
        if hasattr(self, 'ef'):
            try:
                data = self.ef.stock.get_base_info()
                print("✅ 使用东方财富获取股票列表")
                return data
            except Exception as e:
                print(f"⚠️ 东方财富失败: {e}")
        
        print("❌ 所有数据源都失败")
        return None
    
    def get_stock_data(self, symbol, start_date=None, end_date=None, period="daily"):
        """
        获取股票历史数据
        
        Args:
            symbol: 股票代码 (如 '000001.SZ' 或 '000001')
            start_date: 开始日期
            end_date: 结束日期
            period: 周期 ('daily', 'weekly', 'monthly')
        """
        print(f"📈 获取 {symbol} 历史数据...")
        
        # 标准化股票代码
        clean_symbol = self._normalize_symbol(symbol)
        
        # 优先使用Tushare Pro
        if hasattr(self, 'ts_pro'):
            try:
                data = self._get_tushare_data(clean_symbol, start_date, end_date)
                if data is not None and not data.empty:
                    print("✅ 使用Tushare Pro获取数据")
                    return data
            except Exception as e:
                print(f"⚠️ Tushare Pro失败: {e}")
        
        # 备选AKShare
        if hasattr(self, 'ak'):
            try:
                data = self._get_akshare_data(clean_symbol, start_date, end_date)
                if data is not None and not data.empty:
                    print("✅ 使用AKShare获取数据")
                    return data
            except Exception as e:
                print(f"⚠️ AKShare失败: {e}")
        
        # 备选东方财富
        if hasattr(self, 'ef'):
            try:
                data = self._get_efinance_data(clean_symbol, start_date, end_date)
                if data is not None and not data.empty:
                    print("✅ 使用东方财富获取数据")
                    return data
            except Exception as e:
                print(f"⚠️ 东方财富失败: {e}")
        
        print("❌ 所有数据源都失败")
        return None
    
    def get_realtime_data(self, symbols):
        """
        获取实时行情数据
        
        Args:
            symbols: 股票代码列表
        """
        print(f"💰 获取实时行情: {symbols}")
        
        if isinstance(symbols, str):
            symbols = [symbols]
        
        # 优先使用东方财富 (实时数据较好)
        if hasattr(self, 'ef'):
            try:
                data = []
                for symbol in symbols:
                    clean_symbol = self._normalize_symbol(symbol)
                    quote = self.ef.stock.get_quote_history(clean_symbol, klt=1)
                    if quote is not None and not quote.empty:
                        latest = quote.iloc[-1]
                        data.append({
                            'symbol': symbol,
                            'name': latest.get('股票名称', ''),
                            'price': latest.get('收盘', 0),
                            'change': latest.get('涨跌额', 0),
                            'change_pct': latest.get('涨跌幅', 0),
                            'volume': latest.get('成交量', 0),
                            'turnover': latest.get('成交额', 0)
                        })
                
                if data:
                    print("✅ 使用东方财富获取实时数据")
                    return pd.DataFrame(data)
                    
            except Exception as e:
                print(f"⚠️ 东方财富失败: {e}")
        
        # 备选AKShare
        if hasattr(self, 'ak'):
            try:
                data = []
                for symbol in symbols:
                    clean_symbol = self._normalize_symbol(symbol)
                    quote = self.ak.stock_zh_a_spot_em()
                    if quote is not None and not quote.empty:
                        stock_data = quote[quote['代码'] == clean_symbol.split('.')[0]]
                        if not stock_data.empty:
                            row = stock_data.iloc[0]
                            data.append({
                                'symbol': symbol,
                                'name': row.get('名称', ''),
                                'price': row.get('最新价', 0),
                                'change': row.get('涨跌额', 0),
                                'change_pct': row.get('涨跌幅', 0),
                                'volume': row.get('成交量', 0),
                                'turnover': row.get('成交额', 0)
                            })
                
                if data:
                    print("✅ 使用AKShare获取实时数据")
                    return pd.DataFrame(data)
                    
            except Exception as e:
                print(f"⚠️ AKShare失败: {e}")
        
        print("❌ 获取实时数据失败")
        return None
    
    def get_financial_data(self, symbol, report_type="income"):
        """
        获取财务数据
        
        Args:
            symbol: 股票代码
            report_type: 报表类型 ('income', 'balance', 'cashflow')
        """
        print(f"💼 获取 {symbol} 财务数据 ({report_type})")
        
        clean_symbol = self._normalize_symbol(symbol)
        
        # 优先使用Tushare Pro
        if hasattr(self, 'ts_pro'):
            try:
                if report_type == "income":
                    data = self.ts_pro.income(ts_code=clean_symbol, period='20231231')
                elif report_type == "balance":
                    data = self.ts_pro.balancesheet(ts_code=clean_symbol, period='20231231')
                elif report_type == "cashflow":
                    data = self.ts_pro.cashflow(ts_code=clean_symbol, period='20231231')
                
                if data is not None and not data.empty:
                    print("✅ 使用Tushare Pro获取财务数据")
                    return data
                    
            except Exception as e:
                print(f"⚠️ Tushare Pro失败: {e}")
        
        # 备选AKShare
        if hasattr(self, 'ak'):
            try:
                symbol_code = clean_symbol.split('.')[0]
                
                if report_type == "income":
                    data = self.ak.stock_financial_analysis_indicator(symbol=symbol_code)
                elif report_type == "balance":
                    data = self.ak.stock_balance_sheet_by_report_em(symbol=symbol_code)
                elif report_type == "cashflow":
                    data = self.ak.stock_cash_flow_sheet_by_report_em(symbol=symbol_code)
                
                if data is not None and not data.empty:
                    print("✅ 使用AKShare获取财务数据")
                    return data
                    
            except Exception as e:
                print(f"⚠️ AKShare失败: {e}")
        
        print("❌ 获取财务数据失败")
        return None
    
    def _normalize_symbol(self, symbol):
        """标准化股票代码"""
        if '.' in symbol:
            return symbol
        
        # 根据代码判断交易所
        if symbol.startswith('6'):
            return f"{symbol}.SH"  # 上交所
        elif symbol.startswith(('0', '3')):
            return f"{symbol}.SZ"  # 深交所
        else:
            return f"{symbol}.SH"  # 默认上交所
    
    def _get_tushare_data(self, symbol, start_date, end_date):
        """使用Tushare获取数据"""
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
        else:
            start_date = start_date.replace('-', '')
        
        if not end_date:
            end_date = datetime.now().strftime('%Y%m%d')
        else:
            end_date = end_date.replace('-', '')
        
        data = self.ts_pro.daily(ts_code=symbol, start_date=start_date, end_date=end_date)
        
        if data is not None and not data.empty:
            # 标准化列名
            data = data.rename(columns={
                'trade_date': 'date',
                'open': 'open',
                'high': 'high', 
                'low': 'low',
                'close': 'close',
                'vol': 'volume'
            })
            data['date'] = pd.to_datetime(data['date'])
            data = data.sort_values('date')
            data.set_index('date', inplace=True)
        
        return data
    
    def _get_akshare_data(self, symbol, start_date, end_date):
        """使用AKShare获取数据"""
        symbol_code = symbol.split('.')[0]
        
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
        else:
            start_date = start_date.replace('-', '')
        
        if not end_date:
            end_date = datetime.now().strftime('%Y%m%d')
        else:
            end_date = end_date.replace('-', '')
        
        data = self.ak.stock_zh_a_hist(symbol=symbol_code, period="daily", 
                                      start_date=start_date, end_date=end_date)
        
        if data is not None and not data.empty:
            # 标准化列名
            data = data.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '最高': 'high',
                '最低': 'low', 
                '收盘': 'close',
                '成交量': 'volume'
            })
            data['date'] = pd.to_datetime(data['date'])
            data = data.sort_values('date')
            data.set_index('date', inplace=True)
        
        return data
    
    def _get_efinance_data(self, symbol, start_date, end_date):
        """使用东方财富获取数据"""
        symbol_code = symbol.split('.')[0]
        
        data = self.ef.stock.get_quote_history(symbol_code)
        
        if data is not None and not data.empty:
            # 标准化列名
            data = data.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '最高': 'high',
                '最低': 'low',
                '收盘': 'close',
                '成交量': 'volume'
            })
            
            data['date'] = pd.to_datetime(data['date'])
            
            # 过滤日期范围
            if start_date:
                start_dt = pd.to_datetime(start_date)
                data = data[data['date'] >= start_dt]
            
            if end_date:
                end_dt = pd.to_datetime(end_date)
                data = data[data['date'] <= end_dt]
            
            data = data.sort_values('date')
            data.set_index('date', inplace=True)
        
        return data


def test_a_stock_data():
    """测试A股数据源"""
    print("🧪 测试A股数据源")
    print("=" * 50)
    
    # 初始化数据提供商
    provider = AStockDataProvider()
    
    # 测试股票列表
    print("\n📋 测试股票基本信息")
    basic_info = provider.get_stock_basic()
    if basic_info is not None:
        print(f"获取到 {len(basic_info)} 只股票信息")
        print(basic_info.head())
    
    # 测试历史数据
    print("\n📈 测试历史数据")
    symbols = ['000001', '000002', '600000']
    
    for symbol in symbols:
        data = provider.get_stock_data(symbol, start_date='2024-01-01', end_date='2024-12-31')
        if data is not None:
            print(f"{symbol}: 获取到 {len(data)} 条历史数据")
            print(data.tail(3))
        time.sleep(1)  # 避免请求过快
    
    # 测试实时数据
    print("\n💰 测试实时数据")
    realtime = provider.get_realtime_data(['000001', '000002', '600000'])
    if realtime is not None:
        print("实时行情:")
        print(realtime)
    
    # 测试财务数据
    print("\n💼 测试财务数据")
    financial = provider.get_financial_data('000001', 'income')
    if financial is not None:
        print("财务数据:")
        print(financial.head())
    
    print("\n🎉 A股数据源测试完成!")


if __name__ == "__main__":
    test_a_stock_data()
