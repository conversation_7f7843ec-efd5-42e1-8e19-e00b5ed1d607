{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Calculating the USD Liquidity Index with the OpenBB Platform\n", "\n", "This popular indicator is made from a simple subtraction of three FRED series that are published every Wednesday, and is often overlayed with risk assets like the S&P 500 Index or Bitcoin.  The OpenBB SDK is well suited for this task, let's take a look to create this index.\n", "\n", "The formula is defined as:\n", "\n", "```console\n", "WALCL (All Liabilities) – WLRRAL (RRP) – WDTGAL (TGA)\n", "```\n", "\n", "To get these data series, we will use the `openbb-fred` data extension and the `economy` module.  First thing is to import the Python interface, and we will also import Pandas to conduct some DataFrame operations."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from openbb import obb\n", "from pandas import DataFrame"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are two `fred` functions in the `openbb-economy` router:\n", "\n", "- `obb.economy.fred_search()`\n", "- `obb.economy.fred_series()`"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["OBBject\n", "\n", "id: 066c7874-2012-7189-8000-1e79898a8a3c\n", "results: [{'date': datetime.date(2002, 12, 18), 'WALCL': 719542.0, 'WLRRAL': 21905....\n", "provider: fred\n", "warnings: None\n", "chart: None\n", "extra: {'results_metadata': {'WALCL': {'title': 'Assets: Total Assets: Total Assets..."]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["data = obb.economy.fred_series([\"WALCL\", \"WLRRAL\", \"WDTGAL\", \"SP500\"])\n", "\n", "data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There is metadata from each series in the warnings of the response object.  It can be recovered as a JSON dictionary."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['WALCL', 'WLRRAL', 'WDTGAL', 'SP500'])"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'Assets: Total Assets: Total Assets (Less Eliminations from Consolidation): Wednesday Level'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'Millions of U.S. Dollars'"]}, "metadata": {}, "output_type": "display_data"}], "source": ["metadata = data.extra[\"results_metadata\"]\n", "\n", "display(metadata.keys())\n", "display(metadata[\"WALCL\"].get(\"title\"))\n", "display(metadata[\"WALCL\"].get(\"units\"))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Querying FRED\n", "\n", "If we didn't already know the ID for the series, we can search with:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>series_id</th>\n", "      <th>title</th>\n", "      <th>observation_start</th>\n", "      <th>observation_end</th>\n", "      <th>frequency</th>\n", "      <th>frequency_short</th>\n", "      <th>units</th>\n", "      <th>units_short</th>\n", "      <th>seasonal_adjustment</th>\n", "      <th>seasonal_adjustment_short</th>\n", "      <th>last_updated</th>\n", "      <th>popularity</th>\n", "      <th>group_popularity</th>\n", "      <th>notes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>WALCL</td>\n", "      <td>Assets: Total Assets: Total Assets (Less Elimi...</td>\n", "      <td>2002-12-18</td>\n", "      <td>2024-08-14</td>\n", "      <td>Weekly, As of Wednesday</td>\n", "      <td>W</td>\n", "      <td>Millions of U.S. Dollars</td>\n", "      <td>Mil. of U.S. $</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2024-08-15 15:37:22-05:00</td>\n", "      <td>94</td>\n", "      <td>94</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>H41RESPPALDKNWW</td>\n", "      <td>Assets: Liquidity and Credit Facilities: Loans...</td>\n", "      <td>2002-12-18</td>\n", "      <td>2024-08-14</td>\n", "      <td>Weekly</td>\n", "      <td>W</td>\n", "      <td>Millions of U.S. Dollars</td>\n", "      <td>Mil. of U.S. $</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2024-08-15 15:37:01-05:00</td>\n", "      <td>76</td>\n", "      <td>76</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>TREAST</td>\n", "      <td>Assets: Securities Held Outright: U.S. Treasur...</td>\n", "      <td>2002-12-18</td>\n", "      <td>2024-08-14</td>\n", "      <td>Weekly, As of Wednesday</td>\n", "      <td>W</td>\n", "      <td>Millions of U.S. Dollars</td>\n", "      <td>Mil. of U.S. $</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2024-08-15 15:36:54-05:00</td>\n", "      <td>71</td>\n", "      <td>71</td>\n", "      <td>The total face value of U.S. Treasury securiti...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         series_id                                              title  \\\n", "0            WALCL  Assets: Total Assets: Total Assets (Less Elimi...   \n", "1  H41RESPPALDKNWW  Assets: Liquidity and Credit Facilities: Loans...   \n", "2           TREAST  Assets: Securities Held Outright: U.S. Treasur...   \n", "\n", "  observation_start observation_end                frequency frequency_short  \\\n", "0        2002-12-18      2024-08-14  Weekly, As of Wednesday               W   \n", "1        2002-12-18      2024-08-14                   Weekly               W   \n", "2        2002-12-18      2024-08-14  Weekly, As of Wednesday               W   \n", "\n", "                      units     units_short      seasonal_adjustment  \\\n", "0  Millions of U.S. Dollars  Mil. of U.S. $  Not Seasonally Adjusted   \n", "1  Millions of U.S. Dollars  Mil. of U.S. $  Not Seasonally Adjusted   \n", "2  Millions of U.S. Dollars  Mil. of U.S. $  Not Seasonally Adjusted   \n", "\n", "  seasonal_adjustment_short               last_updated  popularity  \\\n", "0                       NSA  2024-08-15 15:37:22-05:00          94   \n", "1                       NSA  2024-08-15 15:37:01-05:00          76   \n", "2                       NSA  2024-08-15 15:36:54-05:00          71   \n", "\n", "   group_popularity                                              notes  \n", "0                94                                                NaN  \n", "1                76                                                NaN  \n", "2                71  The total face value of U.S. Treasury securiti...  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# The first result is the series we are looking for as the starting value.\n", "\n", "obb.economy.fred_search(\"Wednesday Levels\").to_df().head(3)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>series_id</th>\n", "      <th>title</th>\n", "      <th>observation_start</th>\n", "      <th>observation_end</th>\n", "      <th>frequency</th>\n", "      <th>frequency_short</th>\n", "      <th>units</th>\n", "      <th>units_short</th>\n", "      <th>seasonal_adjustment</th>\n", "      <th>seasonal_adjustment_short</th>\n", "      <th>last_updated</th>\n", "      <th>notes</th>\n", "      <th>popularity</th>\n", "      <th>group_popularity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>WLRRAL</td>\n", "      <td>Liabilities and Capital: Liabilities: Reverse ...</td>\n", "      <td>2002-12-18</td>\n", "      <td>2024-08-14</td>\n", "      <td>Weekly, As of Wednesday</td>\n", "      <td>W</td>\n", "      <td>Millions of U.S. Dollars</td>\n", "      <td>Mil. of U.S. $</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2024-08-15 15:37:49-05:00</td>\n", "      <td>Reverse repurchase agreements are transactions...</td>\n", "      <td>63</td>\n", "      <td>63</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>WLRRAFOIAL</td>\n", "      <td>Liabilities and Capital: Liabilities: Reverse ...</td>\n", "      <td>2002-12-18</td>\n", "      <td>2024-08-14</td>\n", "      <td>Weekly, As of Wednesday</td>\n", "      <td>W</td>\n", "      <td>Millions of U.S. Dollars</td>\n", "      <td>Mil. of U.S. $</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2024-08-15 15:37:36-05:00</td>\n", "      <td>Reverse repurchase agreements are transactions...</td>\n", "      <td>40</td>\n", "      <td>40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>WLRRAOL</td>\n", "      <td>Liabilities and Capital: Liabilities: Reverse ...</td>\n", "      <td>2002-12-18</td>\n", "      <td>2024-08-14</td>\n", "      <td>Weekly, As of Wednesday</td>\n", "      <td>W</td>\n", "      <td>Millions of U.S. Dollars</td>\n", "      <td>Mil. of U.S. $</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2024-08-15 15:37:40-05:00</td>\n", "      <td>NaN</td>\n", "      <td>29</td>\n", "      <td>29</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    series_id                                              title  \\\n", "0      WLRRAL  Liabilities and Capital: Liabilities: Reverse ...   \n", "1  WLRRAFOIAL  Liabilities and Capital: Liabilities: Reverse ...   \n", "2     WLRRAOL  Liabilities and Capital: Liabilities: Reverse ...   \n", "\n", "  observation_start observation_end                frequency frequency_short  \\\n", "0        2002-12-18      2024-08-14  Weekly, As of Wednesday               W   \n", "1        2002-12-18      2024-08-14  Weekly, As of Wednesday               W   \n", "2        2002-12-18      2024-08-14  Weekly, As of Wednesday               W   \n", "\n", "                      units     units_short      seasonal_adjustment  \\\n", "0  Millions of U.S. Dollars  Mil. of U.S. $  Not Seasonally Adjusted   \n", "1  Millions of U.S. Dollars  Mil. of U.S. $  Not Seasonally Adjusted   \n", "2  Millions of U.S. Dollars  Mil. of U.S. $  Not Seasonally Adjusted   \n", "\n", "  seasonal_adjustment_short              last_updated  \\\n", "0                       NSA 2024-08-15 15:37:49-05:00   \n", "1                       NSA 2024-08-15 15:37:36-05:00   \n", "2                       NSA 2024-08-15 15:37:40-05:00   \n", "\n", "                                               notes  popularity  \\\n", "0  Reverse repurchase agreements are transactions...          63   \n", "1  Reverse repurchase agreements are transactions...          40   \n", "2                                                NaN          29   \n", "\n", "   group_popularity  \n", "0                63  \n", "1                40  \n", "2                29  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Adding \"Reverse Repo\" to the search returns the second series in the equation, as the first result.\n", "\n", "obb.economy.fred_search(\"Wednesday Levels Reverse Repo\").to_df().head(3)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>series_id</th>\n", "      <th>title</th>\n", "      <th>observation_start</th>\n", "      <th>observation_end</th>\n", "      <th>frequency</th>\n", "      <th>frequency_short</th>\n", "      <th>units</th>\n", "      <th>units_short</th>\n", "      <th>seasonal_adjustment</th>\n", "      <th>seasonal_adjustment_short</th>\n", "      <th>last_updated</th>\n", "      <th>notes</th>\n", "      <th>popularity</th>\n", "      <th>group_popularity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>WDTGAL</td>\n", "      <td>Liabilities and Capital: Liabilities: Deposits...</td>\n", "      <td>2002-12-18</td>\n", "      <td>2024-08-14</td>\n", "      <td>Weekly, As of Wednesday</td>\n", "      <td>W</td>\n", "      <td>Millions of U.S. Dollars</td>\n", "      <td>Mil. of U.S. $</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2024-08-15 15:38:33-05:00</td>\n", "      <td>This account is the primary operational accoun...</td>\n", "      <td>64</td>\n", "      <td>64</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>D2WLTGAL</td>\n", "      <td>Liabilities and Capital: Liabilities: Deposits...</td>\n", "      <td>2002-12-18</td>\n", "      <td>2024-08-14</td>\n", "      <td>Weekly, As of Wednesday</td>\n", "      <td>W</td>\n", "      <td>Millions of U.S. Dollars</td>\n", "      <td>Mil. of U.S. $</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2024-08-15 15:38:37-05:00</td>\n", "      <td>NaN</td>\n", "      <td>60</td>\n", "      <td>60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>WLDLCL</td>\n", "      <td>Liabilities and Capital: Liabilities: Deposits...</td>\n", "      <td>2002-12-18</td>\n", "      <td>2024-08-14</td>\n", "      <td>Weekly, As of Wednesday</td>\n", "      <td>W</td>\n", "      <td>Millions of U.S. Dollars</td>\n", "      <td>Mil. of U.S. $</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2024-08-15 15:36:57-05:00</td>\n", "      <td>This item is the sum of \"Term deposits held by...</td>\n", "      <td>27</td>\n", "      <td>27</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  series_id                                              title  \\\n", "0    WDTGAL  Liabilities and Capital: Liabilities: Deposits...   \n", "1  D2WLTGAL  Liabilities and Capital: Liabilities: Deposits...   \n", "2    WLDLCL  Liabilities and Capital: Liabilities: Deposits...   \n", "\n", "  observation_start observation_end                frequency frequency_short  \\\n", "0        2002-12-18      2024-08-14  Weekly, As of Wednesday               W   \n", "1        2002-12-18      2024-08-14  Weekly, As of Wednesday               W   \n", "2        2002-12-18      2024-08-14  Weekly, As of Wednesday               W   \n", "\n", "                      units     units_short      seasonal_adjustment  \\\n", "0  Millions of U.S. Dollars  Mil. of U.S. $  Not Seasonally Adjusted   \n", "1  Millions of U.S. Dollars  Mil. of U.S. $  Not Seasonally Adjusted   \n", "2  Millions of U.S. Dollars  Mil. of U.S. $  Not Seasonally Adjusted   \n", "\n", "  seasonal_adjustment_short              last_updated  \\\n", "0                       NSA 2024-08-15 15:38:33-05:00   \n", "1                       NSA 2024-08-15 15:38:37-05:00   \n", "2                       NSA 2024-08-15 15:36:57-05:00   \n", "\n", "                                               notes  popularity  \\\n", "0  This account is the primary operational accoun...          64   \n", "1                                                NaN          60   \n", "2  This item is the sum of \"Term deposits held by...          27   \n", "\n", "   group_popularity  \n", "0                64  \n", "1                60  \n", "2                27  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Refining the search for the Treasury General Account, returns the final series in the equation, as the first result.\n", "\n", "obb.economy.fred_search(\"Wednesday Levels Treasury General\").to_df().head(3)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>series_id</th>\n", "      <th>title</th>\n", "      <th>observation_start</th>\n", "      <th>observation_end</th>\n", "      <th>frequency</th>\n", "      <th>frequency_short</th>\n", "      <th>units</th>\n", "      <th>units_short</th>\n", "      <th>seasonal_adjustment</th>\n", "      <th>seasonal_adjustment_short</th>\n", "      <th>last_updated</th>\n", "      <th>notes</th>\n", "      <th>popularity</th>\n", "      <th>group_popularity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SP500</td>\n", "      <td>S&amp;P 500</td>\n", "      <td>2014-08-22</td>\n", "      <td>2024-08-21</td>\n", "      <td>Daily, Close</td>\n", "      <td>D</td>\n", "      <td>Index</td>\n", "      <td>Index</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2024-08-21 19:21:03-05:00</td>\n", "      <td>The observations for the S&amp;P 500 represent the...</td>\n", "      <td>83</td>\n", "      <td>83</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  series_id    title observation_start observation_end     frequency  \\\n", "0     SP500  S&P 500        2014-08-22      2024-08-21  Daily, Close   \n", "\n", "  frequency_short  units units_short      seasonal_adjustment  \\\n", "0               D  Index       Index  Not Seasonally Adjusted   \n", "\n", "  seasonal_adjustment_short              last_updated  \\\n", "0                       NSA 2024-08-21 19:21:03-05:00   \n", "\n", "                                               notes  popularity  \\\n", "0  The observations for the S&P 500 represent the...          83   \n", "\n", "   group_popularity  \n", "0                83  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Several major equity indices are published to FRED, S&P 500 is one of them.\n", "\n", "obb.economy.fred_search(\"SP500\").to_df().head(2)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["By looking at the descriptions, we can confirm that all three Federal Reserve series are numbers as `Millions of USD`.  If they were not all equivalent, some adjustments would need to be made before applying the equation."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["'WALCL: Millions of U.S. Dollars'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'WLRRAL: Millions of U.S. Dollars'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'WDTGAL: Millions of U.S. Dollars'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'SP500: Index'"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for id in metadata:\n", "    display(f\"{id}: {metadata[id]['units']}\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Inspecting the time series element shows that the S&P 500 data (as published to FRED) does not extend as far back as the others.  Let's drop the NaN values and start the time series at a common starting point, which is approximately ten years ago."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WALCL</th>\n", "      <th>WLRRAL</th>\n", "      <th>WDTGAL</th>\n", "      <th>SP500</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2002-12-18</th>\n", "      <td>719542.0</td>\n", "      <td>21905.0</td>\n", "      <td>6595.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2002-12-25</th>\n", "      <td>732059.0</td>\n", "      <td>20396.0</td>\n", "      <td>4662.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2003-01-01</th>\n", "      <td>730994.0</td>\n", "      <td>21091.0</td>\n", "      <td>4420.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2003-01-08</th>\n", "      <td>723762.0</td>\n", "      <td>18709.0</td>\n", "      <td>5490.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               WALCL   WLRRAL  WDTGAL  SP500\n", "date                                        \n", "2002-12-18  719542.0  21905.0  6595.0    NaN\n", "2002-12-25  732059.0  20396.0  4662.0    NaN\n", "2003-01-01  730994.0  21091.0  4420.0    NaN\n", "2003-01-08  723762.0  18709.0  5490.0    NaN"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WALCL</th>\n", "      <th>WLRRAL</th>\n", "      <th>WDTGAL</th>\n", "      <th>SP500</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-08-27</th>\n", "      <td>4413736.0</td>\n", "      <td>282002.0</td>\n", "      <td>29547.0</td>\n", "      <td>2000.12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-09-03</th>\n", "      <td>4415587.0</td>\n", "      <td>250306.0</td>\n", "      <td>21036.0</td>\n", "      <td>2000.72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-09-10</th>\n", "      <td>4421408.0</td>\n", "      <td>267602.0</td>\n", "      <td>31872.0</td>\n", "      <td>1995.69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-09-17</th>\n", "      <td>4449588.0</td>\n", "      <td>252224.0</td>\n", "      <td>123965.0</td>\n", "      <td>2001.57</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                WALCL    WLRRAL    WDTGAL    SP500\n", "date                                              \n", "2014-08-27  4413736.0  282002.0   29547.0  2000.12\n", "2014-09-03  4415587.0  250306.0   21036.0  2000.72\n", "2014-09-10  4421408.0  267602.0   31872.0  1995.69\n", "2014-09-17  4449588.0  252224.0  123965.0  2001.57"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(data.to_df().head(4))\n", "display(data.to_df().dropna().head(4))\n", "\n", "# We'll create a new DataFrame object with the dropped rows.\n", "liquidity_index = DataFrame(data.to_df().dropna())"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Applying the formula will simply be a matter of subtracting the first, three, columns."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WALCL</th>\n", "      <th>WLRRAL</th>\n", "      <th>WDTGAL</th>\n", "      <th>SP500</th>\n", "      <th>USD Liquidity Index</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-07-24</th>\n", "      <td>7205455.0</td>\n", "      <td>805967.0</td>\n", "      <td>767419.0</td>\n", "      <td>5427.13</td>\n", "      <td>5632069.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-07-31</th>\n", "      <td>7178391.0</td>\n", "      <td>813261.0</td>\n", "      <td>854001.0</td>\n", "      <td>5522.30</td>\n", "      <td>5511129.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-07</th>\n", "      <td>7175256.0</td>\n", "      <td>681881.0</td>\n", "      <td>785233.0</td>\n", "      <td>5199.50</td>\n", "      <td>5708142.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-14</th>\n", "      <td>7177688.0</td>\n", "      <td>722198.0</td>\n", "      <td>788823.0</td>\n", "      <td>5455.21</td>\n", "      <td>5666667.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                WALCL    WLRRAL    WDTGAL    SP500  USD Liquidity Index\n", "date                                                                   \n", "2024-07-24  7205455.0  805967.0  767419.0  5427.13            5632069.0\n", "2024-07-31  7178391.0  813261.0  854001.0  5522.30            5511129.0\n", "2024-08-07  7175256.0  681881.0  785233.0  5199.50            5708142.0\n", "2024-08-14  7177688.0  722198.0  788823.0  5455.21            5666667.0"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["liquidity_index[\"USD Liquidity Index\"] = (\n", "    liquidity_index[\"WALCL\"] - liquidity_index[\"WLRRAL\"] - liquidity_index[\"WDTGAL\"]\n", ")\n", "\n", "liquidity_index.tail(4)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Now that there are two items to compare, let's draw it!"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"name": "USD Liquidity Index (Billions)", "type": "scatter", "x": ["2014-08-27", "2014-09-03", "2014-09-10", "2014-09-17", "2014-09-24", "2014-10-01", "2014-10-08", "2014-10-15", "2014-10-22", "2014-10-29", "2014-11-05", "2014-11-12", "2014-11-19", "2014-11-26", "2014-12-03", "2014-12-10", "2014-12-17", "2014-12-24", "2014-12-31", "2015-01-07", "2015-01-14", "2015-01-21", "2015-01-28", "2015-02-04", "2015-02-11", "2015-02-18", "2015-02-25", "2015-03-04", "2015-03-11", "2015-03-18", "2015-03-25", "2015-04-01", "2015-04-08", "2015-04-15", "2015-04-22", "2015-04-29", "2015-05-06", "2015-05-13", "2015-05-20", "2015-05-27", "2015-06-03", "2015-06-10", "2015-06-17", "2015-06-24", "2015-07-01", "2015-07-08", "2015-07-15", "2015-07-22", "2015-07-29", "2015-08-05", "2015-08-12", "2015-08-19", "2015-08-26", "2015-09-02", "2015-09-09", "2015-09-16", "2015-09-23", "2015-09-30", "2015-10-07", "2015-10-14", "2015-10-21", "2015-10-28", "2015-11-04", "2015-11-11", "2015-11-18", "2015-11-25", "2015-12-02", "2015-12-09", "2015-12-16", "2015-12-23", "2015-12-30", "2016-01-06", "2016-01-13", "2016-01-20", "2016-01-27", "2016-02-03", "2016-02-10", "2016-02-17", "2016-02-24", "2016-03-02", "2016-03-09", "2016-03-16", "2016-03-23", "2016-03-30", "2016-04-06", "2016-04-13", "2016-04-20", "2016-04-27", "2016-05-04", "2016-05-11", "2016-05-18", "2016-05-25", "2016-06-01", "2016-06-08", "2016-06-15", "2016-06-22", "2016-06-29", "2016-07-06", "2016-07-13", "2016-07-20", "2016-07-27", "2016-08-03", "2016-08-10", "2016-08-17", "2016-08-24", "2016-08-31", "2016-09-07", "2016-09-14", "2016-09-21", "2016-09-28", "2016-10-05", "2016-10-12", "2016-10-19", "2016-10-26", "2016-11-02", "2016-11-09", "2016-11-16", "2016-11-23", "2016-11-30", "2016-12-07", "2016-12-14", "2016-12-21", "2016-12-28", "2017-01-04", "2017-01-11", "2017-01-18", "2017-01-25", "2017-02-01", "2017-02-08", "2017-02-15", "2017-02-22", "2017-03-01", "2017-03-08", "2017-03-15", "2017-03-22", "2017-03-29", "2017-04-05", "2017-04-12", "2017-04-19", "2017-04-26", "2017-05-03", "2017-05-10", "2017-05-17", "2017-05-24", "2017-05-31", "2017-06-07", "2017-06-14", "2017-06-21", "2017-06-28", "2017-07-05", "2017-07-12", "2017-07-19", "2017-07-26", "2017-08-02", "2017-08-09", "2017-08-16", "2017-08-23", "2017-08-30", "2017-09-06", "2017-09-13", "2017-09-20", "2017-09-27", "2017-10-04", "2017-10-11", "2017-10-18", "2017-10-25", "2017-11-01", "2017-11-08", "2017-11-15", "2017-11-22", "2017-11-29", "2017-12-06", "2017-12-13", "2017-12-20", "2017-12-27", "2018-01-03", "2018-01-10", "2018-01-17", "2018-01-24", "2018-01-31", "2018-02-07", "2018-02-14", "2018-02-21", "2018-02-28", "2018-03-07", "2018-03-14", "2018-03-21", "2018-03-28", "2018-04-04", "2018-04-11", "2018-04-18", "2018-04-25", "2018-05-02", "2018-05-09", "2018-05-16", "2018-05-23", "2018-05-30", "2018-06-06", "2018-06-13", "2018-06-20", "2018-06-27", "2018-07-11", "2018-07-18", "2018-07-25", "2018-08-01", "2018-08-08", "2018-08-15", "2018-08-22", "2018-08-29", "2018-09-05", "2018-09-12", "2018-09-19", "2018-09-26", "2018-10-03", "2018-10-10", "2018-10-17", "2018-10-24", "2018-10-31", "2018-11-07", "2018-11-14", "2018-11-21", "2018-11-28", "2018-12-12", "2018-12-19", "2018-12-26", "2019-01-02", "2019-01-09", "2019-01-16", "2019-01-23", "2019-01-30", "2019-02-06", "2019-02-13", "2019-02-20", "2019-02-27", "2019-03-06", "2019-03-13", "2019-03-20", "2019-03-27", "2019-04-03", "2019-04-10", "2019-04-17", "2019-04-24", "2019-05-01", "2019-05-08", "2019-05-15", "2019-05-22", "2019-05-29", "2019-06-05", "2019-06-12", "2019-06-19", "2019-06-26", "2019-07-03", "2019-07-10", "2019-07-17", "2019-07-24", "2019-07-31", "2019-08-07", "2019-08-14", "2019-08-21", "2019-08-28", "2019-09-04", "2019-09-11", "2019-09-18", "2019-09-25", "2019-10-02", "2019-10-09", "2019-10-16", "2019-10-23", "2019-10-30", "2019-11-06", "2019-11-13", "2019-11-20", "2019-11-27", "2019-12-04", "2019-12-11", "2019-12-18", "2020-01-08", "2020-01-15", "2020-01-22", "2020-01-29", "2020-02-05", "2020-02-12", "2020-02-19", "2020-02-26", "2020-03-04", "2020-03-11", "2020-03-18", "2020-03-25", "2020-04-01", "2020-04-08", "2020-04-15", "2020-04-22", "2020-04-29", "2020-05-06", "2020-05-13", "2020-05-20", "2020-05-27", "2020-06-03", "2020-06-10", "2020-06-17", "2020-06-24", "2020-07-01", "2020-07-08", "2020-07-15", "2020-07-22", "2020-07-29", "2020-08-05", "2020-08-12", "2020-08-19", "2020-08-26", "2020-09-02", "2020-09-09", "2020-09-16", "2020-09-23", "2020-09-30", "2020-10-07", "2020-10-14", "2020-10-21", "2020-10-28", "2020-11-04", "2020-11-11", "2020-11-18", "2020-11-25", "2020-12-02", "2020-12-09", "2020-12-16", "2020-12-23", "2020-12-30", "2021-01-06", "2021-01-13", "2021-01-20", "2021-01-27", "2021-02-03", "2021-02-10", "2021-02-17", "2021-02-24", "2021-03-03", "2021-03-10", "2021-03-17", "2021-03-24", "2021-03-31", "2021-04-07", "2021-04-14", "2021-04-21", "2021-04-28", "2021-05-05", "2021-05-12", "2021-05-19", "2021-05-26", "2021-06-02", "2021-06-09", "2021-06-16", "2021-06-23", "2021-06-30", "2021-07-07", "2021-07-14", "2021-07-21", "2021-07-28", "2021-08-04", "2021-08-11", "2021-08-18", "2021-08-25", "2021-09-01", "2021-09-08", "2021-09-15", "2021-09-22", "2021-09-29", "2021-10-06", "2021-10-13", "2021-10-20", "2021-10-27", "2021-11-03", "2021-11-10", "2021-11-17", "2021-11-24", "2021-12-01", "2021-12-08", "2021-12-15", "2021-12-22", "2021-12-29", "2022-01-05", "2022-01-12", "2022-01-19", "2022-01-26", "2022-02-02", "2022-02-09", "2022-02-16", "2022-02-23", "2022-03-02", "2022-03-09", "2022-03-16", "2022-03-23", "2022-03-30", "2022-04-06", "2022-04-13", "2022-04-20", "2022-04-27", "2022-05-04", "2022-05-11", "2022-05-18", "2022-05-25", "2022-06-01", "2022-06-08", "2022-06-15", "2022-06-22", "2022-06-29", "2022-07-06", "2022-07-13", "2022-07-20", "2022-07-27", "2022-08-03", "2022-08-10", "2022-08-17", "2022-08-24", "2022-08-31", "2022-09-07", "2022-09-14", "2022-09-21", "2022-09-28", "2022-10-05", "2022-10-12", "2022-10-19", "2022-10-26", "2022-11-02", "2022-11-09", "2022-11-16", "2022-11-23", "2022-11-30", "2022-12-07", "2022-12-14", "2022-12-21", "2022-12-28", "2023-01-04", "2023-01-11", "2023-01-18", "2023-01-25", "2023-02-01", "2023-02-08", "2023-02-15", "2023-02-22", "2023-03-01", "2023-03-08", "2023-03-15", "2023-03-22", "2023-03-29", "2023-04-05", "2023-04-12", "2023-04-19", "2023-04-26", "2023-05-03", "2023-05-10", "2023-05-17", "2023-05-24", "2023-05-31", "2023-06-07", "2023-06-14", "2023-06-21", "2023-06-28", "2023-07-05", "2023-07-12", "2023-07-19", "2023-07-26", "2023-08-02", "2023-08-09", "2023-08-16", "2023-08-23", "2023-08-30", "2023-09-06", "2023-09-13", "2023-09-20", "2023-09-27", "2023-10-04", "2023-10-11", "2023-10-18", "2023-10-25", "2023-11-01", "2023-11-08", "2023-11-15", "2023-11-22", "2023-11-29", "2023-12-06", "2023-12-13", "2023-12-20", "2023-12-27", "2024-01-03", "2024-01-10", "2024-01-17", "2024-01-24", "2024-01-31", "2024-02-07", "2024-02-14", "2024-02-21", "2024-02-28", "2024-03-06", "2024-03-13", "2024-03-20", "2024-03-27", "2024-04-03", "2024-04-10", "2024-04-17", "2024-04-24", "2024-05-01", "2024-05-08", "2024-05-15", "2024-05-22", "2024-05-29", "2024-06-05", "2024-06-12", "2024-06-26", "2024-07-03", "2024-07-10", "2024-07-17", "2024-07-24", "2024-07-31", "2024-08-07", "2024-08-14"], "y": [4102.187, 4144.245, 4121.934, 4073.399, 4078.421, 4012.4, 4092.228, 4151.798, 4121.104, 4131.405, 4157.786, 4182.317, 4146.125, 4162.557, 4166.703, 4192.705, 4100.917, 4032.718, 3764.371, 4081.724, 4152.628, 4036.391, 4024.263, 4077.448, 4122.345, 4126.7, 4133.84, 4157.969, 4196.743, 4118.713, 4130.431, 4062.577, 4194.673, 4146.164, 4013.232, 3976.186, 4008.639, 4052.027, 3998.375, 4021.763, 4038.675, 4068.888, 3974.999, 3964.877, 3917.138, 4005.792, 4061.875, 4051.105, 4039.591, 4038.524, 4053.065, 4097.238, 4124.082, 4121.722, 4173.905, 4071.802, 4028.746, 3644.314, 4116.362, 4184.718, 4112.987, 4108.943, 4137.203, 4118.564, 4060.497, 4031.081, 3990.453, 4020.86, 3916.688, 3840.806, 3663.222, 3803.904, 3898.352, 3847.117, 3835.271, 3878.591, 3927.745, 3942.533, 3986.258, 3957.672, 4001.353, 3917.364, 3907.595, 3821.24, 3952.025, 4003.265, 3884.078, 3835.053, 3876.681, 3902.98, 3883.581, 3892.296, 3899.026, 3953.391, 3895.925, 3815.527, 3716.123, 3834.185, 3865.019, 3825.519, 3839.234, 3909.857, 3895.763, 3901.357, 3893.099, 3746.431, 3901.463, 3912.344, 3685.035, 3597.573, 3580.611, 3691.723, 3626.521, 3649.261, 3666.255, 3726.284, 3722.376, 3704.74, 3561.582, 3681.038, 3716.821, 3521.903, 3504.869, 3558.055, 3668.574, 3680.965, 3689.539, 3722.873, 3794.851, 3816.589, 3815.002, 3904.745, 3940.426, 3969.149, 3928.273, 3895.62, 3966.491, 4007.902, 3887.632, 3836.224, 3835.218, 3860.11, 3886.636, 3880.477, 3767.924, 3907.954, 3906.258, 3802.895, 3760.885, 3815.49, 3884.65, 3894.961, 3937.954, 3939.862, 3986.261, 4029.519, 3988.084, 3996.775, 4022.889, 4031.235, 3893.803, 3845.429, 3896.449, 3943.833, 3925.32, 3928.122, 3954.187, 3996.979, 4036.675, 4015.582, 3965.263, 4011.307, 4015.863, 3926.851, 3875.403, 3889.771, 3949.199, 3921.666, 3890.651, 3832.955, 3937.984, 3985.618, 3922.673, 3916.553, 3975.116, 3933.819, 3853.1, 3836.403, 3820.243, 3850.676, 3787.212, 3731.405, 3714.996, 3772.35, 3752.957, 3777.667, 3749.253, 3769.144, 3802.28, 3692.081, 3682.479, 3725.355, 3691.355, 3688.363, 3709.69, 3708.95, 3661.271, 3649.622, 3641.227, 3654.407, 3683.569, 3574.204, 3595.907, 3594.376, 3650.744, 3594.431, 3586.452, 3536.605, 3594.941, 3597.095, 3537.998, 3537.519, 3530.207, 3435.362, 3462.58, 3399.368, 3446.679, 3397.675, 3391.208, 3379.058, 3424.224, 3407.047, 3395.238, 3457.218, 3518.391, 3493.695, 3407.127, 3418.424, 3415.674, 3434.746, 3306.78, 3258.957, 3251.108, 3286.785, 3305.855, 3326.616, 3337.016, 3368.292, 3412.563, 3300.68, 3313.719, 3295.435, 3331.317, 3319.311, 3342.813, 3293.73, 3364.332, 3356.496, 3332.201, 3323.08, 3284.103, 3291.842, 3216.52, 3261.364, 3322.028, 3361.457, 3302.177, 3287.483, 3329.546, 3372.763, 3395.625, 3371.784, 3412.058, 3449.869, 3528.698, 3494.498, 3523.781, 3534.135, 3468.847, 3467.689, 3510.959, 3554.519, 3523.555, 3551.054, 3623.503, 3706.299, 4032.912, 4510.274, 4810.247, 4921.475, 5232.148, 5333.845, 5310.591, 5312.946, 5514.905, 5577.312, 5526.443, 5487.701, 5423.553, 5303.922, 5275.367, 5125.057, 5071.452, 4993.92, 4972.91, 4939.545, 5026.152, 5103.788, 5162.902, 5169.894, 5153.005, 5236.116, 5173.184, 5227.077, 5069.217, 5198.672, 5307.661, 5290.333, 5291.374, 5345.874, 5408.838, 5505.919, 5534.54, 5479.638, 5548.04, 5555.859, 5622.207, 5540.074, 5521.078, 5543.92, 5569.979, 5580.971, 5571.936, 5653.45, 5786.79, 5945.092, 5933.947, 6075.878, 6398.489, 6453.27, 6214.86, 6481.841, 6589.566, 6514.715, 6465.784, 6476.048, 6537.578, 6539.894, 6454.196, 6450.71, 6557.836, 6555.383, 6312.137, 5965.69, 6323.543, 6433.142, 6487.358, 6464.374, 6512.845, 6599.414, 6643.546, 6669.433, 6675.943, 6746.074, 6728.426, 6653.113, 6571.889, 6630.388, 6750.86, 6661.211, 6598.202, 6630.846, 6678.719, 6681.706, 6781.914, 6761.977, 6752.153, 6770.493, 6610.064, 6552.676, 6535.346, 6436.554, 6323.864, 6329.675, 6256.373, 6291.099, 6300.31, 6260.221, 6443.312, 6515.133, 6468.142, 6331.45, 6339.568, 6416.37, 6351.619, 5884.048, 5890.217, 5877.007, 5847.914, 5837.55, 5854.346, 5903.935, 5830.048, 5740.879, 5673.148, 5665.461, 5763.258, 5834.287, 5748.39, 5790.236, 5860.203, 5872.299, 5842.636, 5826.88, 5627.898, 5779.582, 5688.073, 5515.148, 5495.358, 5592.077, 5595.109, 5531.65, 5597.399, 5544.181, 5569.456, 5677.344, 5698.432, 5555.279, 5652.333, 5689.869, 5567.127, 5514.208, 5565.926, 5614.355, 5608.277, 5482.731, 5518.413, 5520.6, 5582.56, 5459.526, 5488.058, 5470.202, 5938.531, 5883.947, 5910.642, 5892.293, 5851.516, 5661.825, 5627.754, 5674.734, 5730.031, 5789.801, 5775.776, 5721.665, 5803.749, 5816.395, 5699.685, 5660.482, 5660.128, 5631.662, 5678.426, 5626.472, 5673.429, 5679.527, 5664.247, 5610.521, 5622.291, 5719.888, 5725.781, 5557.666, 5574.813, 5644.808, 5709.302, 5643.131, 5656.998, 5717.141, 5753.375, 5877.042, 5846.128, 5788.419, 5885.179, 5956.716, 5867.14, 5834.143, 5851.585, 5917.71, 5963.874, 5882.002, 5787.04, 5919.546, 5896.422, 5878.299, 5891.334, 5984.913, 5938.903, 5856.277, 5839.733, 5905.094, 5966.442, 5679.934, 5663.796, 5673.967, 5681.635, 5770.954, 5723.774, 5743.697, 5806.528, 5776.006, 5607.191, 5664.604, 5680.508, 5651.427, 5632.069, 5511.129, 5708.142, 5666.667], "yaxis": "y"}, {"name": "S&P 500 Index", "type": "scatter", "x": ["2014-08-27", "2014-09-03", "2014-09-10", "2014-09-17", "2014-09-24", "2014-10-01", "2014-10-08", "2014-10-15", "2014-10-22", "2014-10-29", "2014-11-05", "2014-11-12", "2014-11-19", "2014-11-26", "2014-12-03", "2014-12-10", "2014-12-17", "2014-12-24", "2014-12-31", "2015-01-07", "2015-01-14", "2015-01-21", "2015-01-28", "2015-02-04", "2015-02-11", "2015-02-18", "2015-02-25", "2015-03-04", "2015-03-11", "2015-03-18", "2015-03-25", "2015-04-01", "2015-04-08", "2015-04-15", "2015-04-22", "2015-04-29", "2015-05-06", "2015-05-13", "2015-05-20", "2015-05-27", "2015-06-03", "2015-06-10", "2015-06-17", "2015-06-24", "2015-07-01", "2015-07-08", "2015-07-15", "2015-07-22", "2015-07-29", "2015-08-05", "2015-08-12", "2015-08-19", "2015-08-26", "2015-09-02", "2015-09-09", "2015-09-16", "2015-09-23", "2015-09-30", "2015-10-07", "2015-10-14", "2015-10-21", "2015-10-28", "2015-11-04", "2015-11-11", "2015-11-18", "2015-11-25", "2015-12-02", "2015-12-09", "2015-12-16", "2015-12-23", "2015-12-30", "2016-01-06", "2016-01-13", "2016-01-20", "2016-01-27", "2016-02-03", "2016-02-10", "2016-02-17", "2016-02-24", "2016-03-02", "2016-03-09", "2016-03-16", "2016-03-23", "2016-03-30", "2016-04-06", "2016-04-13", "2016-04-20", "2016-04-27", "2016-05-04", "2016-05-11", "2016-05-18", "2016-05-25", "2016-06-01", "2016-06-08", "2016-06-15", "2016-06-22", "2016-06-29", "2016-07-06", "2016-07-13", "2016-07-20", "2016-07-27", "2016-08-03", "2016-08-10", "2016-08-17", "2016-08-24", "2016-08-31", "2016-09-07", "2016-09-14", "2016-09-21", "2016-09-28", "2016-10-05", "2016-10-12", "2016-10-19", "2016-10-26", "2016-11-02", "2016-11-09", "2016-11-16", "2016-11-23", "2016-11-30", "2016-12-07", "2016-12-14", "2016-12-21", "2016-12-28", "2017-01-04", "2017-01-11", "2017-01-18", "2017-01-25", "2017-02-01", "2017-02-08", "2017-02-15", "2017-02-22", "2017-03-01", "2017-03-08", "2017-03-15", "2017-03-22", "2017-03-29", "2017-04-05", "2017-04-12", "2017-04-19", "2017-04-26", "2017-05-03", "2017-05-10", "2017-05-17", "2017-05-24", "2017-05-31", "2017-06-07", "2017-06-14", "2017-06-21", "2017-06-28", "2017-07-05", "2017-07-12", "2017-07-19", "2017-07-26", "2017-08-02", "2017-08-09", "2017-08-16", "2017-08-23", "2017-08-30", "2017-09-06", "2017-09-13", "2017-09-20", "2017-09-27", "2017-10-04", "2017-10-11", "2017-10-18", "2017-10-25", "2017-11-01", "2017-11-08", "2017-11-15", "2017-11-22", "2017-11-29", "2017-12-06", "2017-12-13", "2017-12-20", "2017-12-27", "2018-01-03", "2018-01-10", "2018-01-17", "2018-01-24", "2018-01-31", "2018-02-07", "2018-02-14", "2018-02-21", "2018-02-28", "2018-03-07", "2018-03-14", "2018-03-21", "2018-03-28", "2018-04-04", "2018-04-11", "2018-04-18", "2018-04-25", "2018-05-02", "2018-05-09", "2018-05-16", "2018-05-23", "2018-05-30", "2018-06-06", "2018-06-13", "2018-06-20", "2018-06-27", "2018-07-11", "2018-07-18", "2018-07-25", "2018-08-01", "2018-08-08", "2018-08-15", "2018-08-22", "2018-08-29", "2018-09-05", "2018-09-12", "2018-09-19", "2018-09-26", "2018-10-03", "2018-10-10", "2018-10-17", "2018-10-24", "2018-10-31", "2018-11-07", "2018-11-14", "2018-11-21", "2018-11-28", "2018-12-12", "2018-12-19", "2018-12-26", "2019-01-02", "2019-01-09", "2019-01-16", "2019-01-23", "2019-01-30", "2019-02-06", "2019-02-13", "2019-02-20", "2019-02-27", "2019-03-06", "2019-03-13", "2019-03-20", "2019-03-27", "2019-04-03", "2019-04-10", "2019-04-17", "2019-04-24", "2019-05-01", "2019-05-08", "2019-05-15", "2019-05-22", "2019-05-29", "2019-06-05", "2019-06-12", "2019-06-19", "2019-06-26", "2019-07-03", "2019-07-10", "2019-07-17", "2019-07-24", "2019-07-31", "2019-08-07", "2019-08-14", "2019-08-21", "2019-08-28", "2019-09-04", "2019-09-11", "2019-09-18", "2019-09-25", "2019-10-02", "2019-10-09", "2019-10-16", "2019-10-23", "2019-10-30", "2019-11-06", "2019-11-13", "2019-11-20", "2019-11-27", "2019-12-04", "2019-12-11", "2019-12-18", "2020-01-08", "2020-01-15", "2020-01-22", "2020-01-29", "2020-02-05", "2020-02-12", "2020-02-19", "2020-02-26", "2020-03-04", "2020-03-11", "2020-03-18", "2020-03-25", "2020-04-01", "2020-04-08", "2020-04-15", "2020-04-22", "2020-04-29", "2020-05-06", "2020-05-13", "2020-05-20", "2020-05-27", "2020-06-03", "2020-06-10", "2020-06-17", "2020-06-24", "2020-07-01", "2020-07-08", "2020-07-15", "2020-07-22", "2020-07-29", "2020-08-05", "2020-08-12", "2020-08-19", "2020-08-26", "2020-09-02", "2020-09-09", "2020-09-16", "2020-09-23", "2020-09-30", "2020-10-07", "2020-10-14", "2020-10-21", "2020-10-28", "2020-11-04", "2020-11-11", "2020-11-18", "2020-11-25", "2020-12-02", "2020-12-09", "2020-12-16", "2020-12-23", "2020-12-30", "2021-01-06", "2021-01-13", "2021-01-20", "2021-01-27", "2021-02-03", "2021-02-10", "2021-02-17", "2021-02-24", "2021-03-03", "2021-03-10", "2021-03-17", "2021-03-24", "2021-03-31", "2021-04-07", "2021-04-14", "2021-04-21", "2021-04-28", "2021-05-05", "2021-05-12", "2021-05-19", "2021-05-26", "2021-06-02", "2021-06-09", "2021-06-16", "2021-06-23", "2021-06-30", "2021-07-07", "2021-07-14", "2021-07-21", "2021-07-28", "2021-08-04", "2021-08-11", "2021-08-18", "2021-08-25", "2021-09-01", "2021-09-08", "2021-09-15", "2021-09-22", "2021-09-29", "2021-10-06", "2021-10-13", "2021-10-20", "2021-10-27", "2021-11-03", "2021-11-10", "2021-11-17", "2021-11-24", "2021-12-01", "2021-12-08", "2021-12-15", "2021-12-22", "2021-12-29", "2022-01-05", "2022-01-12", "2022-01-19", "2022-01-26", "2022-02-02", "2022-02-09", "2022-02-16", "2022-02-23", "2022-03-02", "2022-03-09", "2022-03-16", "2022-03-23", "2022-03-30", "2022-04-06", "2022-04-13", "2022-04-20", "2022-04-27", "2022-05-04", "2022-05-11", "2022-05-18", "2022-05-25", "2022-06-01", "2022-06-08", "2022-06-15", "2022-06-22", "2022-06-29", "2022-07-06", "2022-07-13", "2022-07-20", "2022-07-27", "2022-08-03", "2022-08-10", "2022-08-17", "2022-08-24", "2022-08-31", "2022-09-07", "2022-09-14", "2022-09-21", "2022-09-28", "2022-10-05", "2022-10-12", "2022-10-19", "2022-10-26", "2022-11-02", "2022-11-09", "2022-11-16", "2022-11-23", "2022-11-30", "2022-12-07", "2022-12-14", "2022-12-21", "2022-12-28", "2023-01-04", "2023-01-11", "2023-01-18", "2023-01-25", "2023-02-01", "2023-02-08", "2023-02-15", "2023-02-22", "2023-03-01", "2023-03-08", "2023-03-15", "2023-03-22", "2023-03-29", "2023-04-05", "2023-04-12", "2023-04-19", "2023-04-26", "2023-05-03", "2023-05-10", "2023-05-17", "2023-05-24", "2023-05-31", "2023-06-07", "2023-06-14", "2023-06-21", "2023-06-28", "2023-07-05", "2023-07-12", "2023-07-19", "2023-07-26", "2023-08-02", "2023-08-09", "2023-08-16", "2023-08-23", "2023-08-30", "2023-09-06", "2023-09-13", "2023-09-20", "2023-09-27", "2023-10-04", "2023-10-11", "2023-10-18", "2023-10-25", "2023-11-01", "2023-11-08", "2023-11-15", "2023-11-22", "2023-11-29", "2023-12-06", "2023-12-13", "2023-12-20", "2023-12-27", "2024-01-03", "2024-01-10", "2024-01-17", "2024-01-24", "2024-01-31", "2024-02-07", "2024-02-14", "2024-02-21", "2024-02-28", "2024-03-06", "2024-03-13", "2024-03-20", "2024-03-27", "2024-04-03", "2024-04-10", "2024-04-17", "2024-04-24", "2024-05-01", "2024-05-08", "2024-05-15", "2024-05-22", "2024-05-29", "2024-06-05", "2024-06-12", "2024-06-26", "2024-07-03", "2024-07-10", "2024-07-17", "2024-07-24", "2024-07-31", "2024-08-07", "2024-08-14"], "y": [2000.12, 2000.72, 1995.69, 2001.57, 1998.3, 1946.16, 1968.89, 1862.49, 1927.11, 1982.3, 2023.57, 2038.25, 2048.72, 2072.83, 2074.33, 2026.14, 2012.89, 2081.88, 2058.9, 2025.9, 2011.27, 2032.12, 2002.16, 2041.51, 2068.53, 2099.68, 2113.86, 2098.53, 2040.24, 2099.5, 2061.05, 2059.69, 2081.9, 2106.63, 2107.96, 2106.85, 2080.15, 2098.48, 2125.85, 2123.48, 2114.07, 2105.2, 2100.44, 2108.58, 2077.42, 2046.68, 2107.4, 2114.15, 2108.57, 2099.84, 2086.05, 2079.61, 1940.51, 1948.86, 1942.04, 1995.31, 1938.76, 1920.03, 1995.83, 1994.24, 2018.94, 2090.35, 2102.31, 2075, 2083.58, 2088.87, 2079.51, 2047.62, 2073.07, 2064.29, 2063.36, 1990.26, 1890.28, 1859.33, 1882.95, 1912.53, 1851.86, 1926.82, 1929.8, 1986.45, 1989.26, 2027.22, 2036.71, 2063.95, 2066.66, 2082.42, 2102.4, 2095.15, 2051.12, 2064.46, 2047.63, 2090.54, 2099.33, 2119.12, 2071.5, 2085.45, 2070.77, 2099.73, 2152.43, 2173.02, 2166.58, 2163.79, 2175.49, 2182.22, 2175.44, 2170.95, 2186.16, 2125.77, 2163.12, 2171.37, 2159.73, 2139.18, 2144.29, 2139.43, 2097.94, 2163.26, 2176.94, 2204.72, 2198.81, 2241.35, 2253.28, 2265.18, 2249.92, 2270.75, 2275.32, 2271.89, 2298.37, 2279.55, 2294.67, 2349.25, 2362.82, 2395.96, 2362.98, 2385.26, 2348.45, 2361.13, 2352.95, 2344.93, 2338.17, 2387.45, 2388.13, 2399.63, 2357.03, 2404.39, 2411.8, 2433.14, 2437.92, 2435.61, 2440.69, 2432.54, 2443.25, 2473.83, 2477.83, 2477.57, 2474.02, 2468.11, 2444.04, 2457.59, 2465.54, 2498.37, 2508.24, 2507.04, 2537.74, 2555.24, 2561.26, 2557.15, 2579.36, 2594.38, 2564.62, 2597.08, 2626.07, 2629.27, 2662.85, 2679.25, 2682.62, 2713.06, 2748.23, 2802.56, 2837.54, 2823.81, 2681.66, 2698.63, 2701.33, 2713.83, 2726.8, 2749.48, 2711.93, 2605, 2644.69, 2642.19, 2708.64, 2639.4, 2635.67, 2697.79, 2722.46, 2733.29, 2724.01, 2772.35, 2775.63, 2767.32, 2699.63, 2774.02, 2815.62, 2846.07, 2813.36, 2857.7, 2818.37, 2861.82, 2914.04, 2888.6, 2888.92, 2907.95, 2905.97, 2925.51, 2785.68, 2809.21, 2656.1, 2711.74, 2813.89, 2701.58, 2649.93, 2743.79, 2651.07, 2506.96, 2467.7, 2510.03, 2584.96, 2616.1, 2638.7, 2681.05, 2731.61, 2753.03, 2784.7, 2792.38, 2771.45, 2810.92, 2824.23, 2805.37, 2873.4, 2888.21, 2900.45, 2927.25, 2923.73, 2879.42, 2850.96, 2856.27, 2783.02, 2826.15, 2879.84, 2926.46, 2913.78, 2995.82, 2993.07, 2984.42, 3019.56, 2980.38, 2883.98, 2840.6, 2924.43, 2887.94, 2937.78, 3000.93, 3006.73, 2984.87, 2887.61, 2919.4, 2989.69, 3004.52, 3046.77, 3076.78, 3094.04, 3108.46, 3153.63, 3112.76, 3141.63, 3191.14, 3253.05, 3289.29, 3321.75, 3273.4, 3334.69, 3379.45, 3386.15, 3116.39, 3130.12, 2741.38, 2398.1, 2475.56, 2470.5, 2749.98, 2783.36, 2799.31, 2939.51, 2848.42, 2820, 2971.61, 3036.13, 3122.87, 3190.14, 3113.49, 3050.33, 3115.86, 3169.94, 3226.56, 3276.02, 3258.44, 3327.77, 3380.35, 3374.85, 3478.73, 3580.84, 3398.96, 3385.49, 3236.92, 3363, 3419.45, 3488.67, 3435.56, 3271.03, 3443.44, 3572.66, 3567.79, 3629.65, 3669.01, 3672.82, 3701.17, 3690.01, 3732.04, 3748.14, 3809.84, 3851.85, 3750.77, 3830.17, 3909.88, 3931.33, 3925.43, 3819.72, 3898.81, 3974.12, 3889.14, 3972.89, 4079.95, 4124.66, 4173.42, 4183.18, 4167.59, 4063.04, 4115.68, 4195.99, 4208.12, 4219.55, 4223.7, 4241.84, 4297.5, 4358.13, 4374.3, 4358.69, 4400.64, 4402.66, 4447.7, 4400.27, 4496.19, 4524.09, 4514.07, 4480.7, 4395.64, 4359.46, 4363.55, 4363.8, 4536.19, 4551.68, 4660.57, 4646.71, 4688.67, 4701.46, 4513.04, 4701.21, 4709.85, 4696.56, 4793.06, 4700.58, 4726.35, 4532.76, 4349.93, 4589.38, 4587.18, 4475.01, 4225.5, 4386.54, 4277.88, 4357.86, 4456.24, 4602.45, 4481.15, 4446.59, 4459.45, 4183.96, 4300.17, 3935.18, 3923.68, 3978.73, 4101.23, 4115.77, 3789.99, 3759.89, 3818.83, 3845.08, 3801.78, 3959.9, 4023.61, 4155.17, 4210.24, 4274.04, 4140.77, 3955, 3979.87, 3946.01, 3789.93, 3719.04, 3783.28, 3577.03, 3695.16, 3830.6, 3759.69, 3748.57, 3958.79, 4027.26, 4080.11, 3933.92, 3995.32, 3878.44, 3783.22, 3852.97, 3969.61, 3928.86, 4016.22, 4119.21, 4117.86, 4147.6, 3991.05, 3951.39, 3992.01, 3891.93, 3936.97, 4027.81, 4090.38, 4091.95, 4154.52, 4055.99, 4090.75, 4137.64, 4158.77, 4115.24, 4179.83, 4267.52, 4372.59, 4365.69, 4376.86, 4446.82, 4472.16, 4565.72, 4566.75, 4513.39, 4467.71, 4404.33, 4436.01, 4514.87, 4465.48, 4467.44, 4402.2, 4274.51, 4263.75, 4376.95, 4314.6, 4186.77, 4237.86, 4382.78, 4502.88, 4556.62, 4550.58, 4549.34, 4707.09, 4698.35, 4781.58, 4704.81, 4783.45, 4739.21, 4868.55, 4845.65, 4995.06, 5000.62, 4981.8, 5069.76, 5104.76, 5165.31, 5224.62, 5248.49, 5211.49, 5160.64, 5022.21, 5071.63, 5018.39, 5187.67, 5308.15, 5307.01, 5266.95, 5354.03, 5421.03, 5477.9, 5537.02, 5633.91, 5588.27, 5427.13, 5522.3, 5199.5, 5455.21], "yaxis": "y2"}], "layout": {"autosize": true, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "USD Liquidity Index vs. S&P 500 Index", "x": 0.5, "y": 0.9}, "yaxis": {"position": 0, "showgrid": false, "side": "left", "title": {"font": {"size": 12}, "text": "USD Liquidity Index (Billions)"}}, "yaxis2": {"overlaying": "y", "position": 1, "side": "right", "title": {"font": {"size": 12}, "text": "S&P 500 Index"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.graph_objects as go\n", "\n", "fig = go.Figure()\n", "\n", "fig.add_scatter(\n", "    x=liquidity_index.index,\n", "    y=liquidity_index[\"USD Liquidity Index\"] / 1000,\n", "    name=\"USD Liquidity Index (Billions)\",\n", "    yaxis=\"y1\",\n", ")\n", "\n", "fig.add_scatter(\n", "    x=liquidity_index.index,\n", "    y=liquidity_index[\"SP500\"],\n", "    name=\"S&P 500 Index\",\n", "    yaxis=\"y2\",\n", ")\n", "\n", "fig.update_layout(\n", "    yaxis=dict(\n", "        title=\"USD Liquidity Index (Billions)\",\n", "        side=\"left\",\n", "        position=0,\n", "        titlefont=dict(size=12),\n", "        showgrid=False,\n", "    ),\n", "    yaxis2=dict(\n", "        title=\"S&P 500 Index\",\n", "        side=\"right\",\n", "        overlaying=\"y\",\n", "        position=1,\n", "        titlefont=dict(size=12),\n", "    ),\n", "    title=\"USD Liquidity Index vs. S&P 500 Index\",\n", "    title_y=0.90,\n", "    title_x=0.5,\n", "    autosize=True,\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["To draw them both on the same y-axis, they will need to be normalized.  There are several methods for normalizing a series, the fourth function in the block below paramaterizes a few of them, making it easy to A/B them."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USD Liquidity Index</th>\n", "      <th>SP500</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-07-31</th>\n", "      <td>0.643578</td>\n", "      <td>0.970490</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-07</th>\n", "      <td>0.698835</td>\n", "      <td>0.885139</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-14</th>\n", "      <td>0.687202</td>\n", "      <td>0.952750</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            USD Liquidity Index     SP500\n", "date                                     \n", "2024-07-31             0.643578  0.970490\n", "2024-08-07             0.698835  0.885139\n", "2024-08-14             0.687202  0.952750"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["y_axis = liquidity_index[[\"USD Liquidity Index\", \"SP500\"]]\n", "\n", "\n", "def absolute_maximum_scale(series):\n", "    return series / series.abs().max()\n", "\n", "\n", "def min_max_scaling(series):\n", "    return (series - series.min()) / (series.max() - series.min())\n", "\n", "\n", "def z_score_standardization(series):\n", "    return (series - series.mean()) / series.std()\n", "\n", "\n", "methods = {\n", "    \"z\": z_score_standardization,\n", "    \"m\": min_max_scaling,\n", "    \"a\": absolute_maximum_scale,\n", "}\n", "\n", "\n", "def normalize(data: DataFrame, method: str = \"z\") -> DataFrame:\n", "    for col in data.columns:\n", "        data.loc[:, col] = methods[f\"{method}\"](data.loc[:, col])\n", "\n", "    return data\n", "\n", "\n", "normalized = normalize(y_axis, method=\"m\")\n", "\n", "normalized.tail(3)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Now they can be easily plotted using the built-in `DataFrame.plot` method."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "variable=USD Liquidity Index<br>date=%{x}<br>value=%{y}<extra></extra>", "legendgroup": "USD Liquidity Index", "line": {"color": "#636efa", "dash": "solid"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "USD Liquidity Index", "showlegend": true, "type": "scattergl", "x": ["2014-08-27", "2014-09-03", "2014-09-10", "2014-09-17", "2014-09-24", "2014-10-01", "2014-10-08", "2014-10-15", "2014-10-22", "2014-10-29", "2014-11-05", "2014-11-12", "2014-11-19", "2014-11-26", "2014-12-03", "2014-12-10", "2014-12-17", "2014-12-24", "2014-12-31", "2015-01-07", "2015-01-14", "2015-01-21", "2015-01-28", "2015-02-04", "2015-02-11", "2015-02-18", "2015-02-25", "2015-03-04", "2015-03-11", "2015-03-18", "2015-03-25", "2015-04-01", "2015-04-08", "2015-04-15", "2015-04-22", "2015-04-29", "2015-05-06", "2015-05-13", "2015-05-20", "2015-05-27", "2015-06-03", "2015-06-10", "2015-06-17", "2015-06-24", "2015-07-01", "2015-07-08", "2015-07-15", "2015-07-22", "2015-07-29", "2015-08-05", "2015-08-12", "2015-08-19", "2015-08-26", "2015-09-02", "2015-09-09", "2015-09-16", "2015-09-23", "2015-09-30", "2015-10-07", "2015-10-14", "2015-10-21", "2015-10-28", "2015-11-04", "2015-11-11", "2015-11-18", "2015-11-25", "2015-12-02", "2015-12-09", "2015-12-16", "2015-12-23", "2015-12-30", "2016-01-06", "2016-01-13", "2016-01-20", "2016-01-27", "2016-02-03", "2016-02-10", "2016-02-17", "2016-02-24", "2016-03-02", "2016-03-09", "2016-03-16", "2016-03-23", "2016-03-30", "2016-04-06", "2016-04-13", "2016-04-20", "2016-04-27", "2016-05-04", "2016-05-11", "2016-05-18", "2016-05-25", "2016-06-01", "2016-06-08", "2016-06-15", "2016-06-22", "2016-06-29", "2016-07-06", "2016-07-13", "2016-07-20", "2016-07-27", "2016-08-03", "2016-08-10", "2016-08-17", "2016-08-24", "2016-08-31", "2016-09-07", "2016-09-14", "2016-09-21", "2016-09-28", "2016-10-05", "2016-10-12", "2016-10-19", "2016-10-26", "2016-11-02", "2016-11-09", "2016-11-16", "2016-11-23", "2016-11-30", "2016-12-07", "2016-12-14", "2016-12-21", "2016-12-28", "2017-01-04", "2017-01-11", "2017-01-18", "2017-01-25", "2017-02-01", "2017-02-08", "2017-02-15", "2017-02-22", "2017-03-01", "2017-03-08", "2017-03-15", "2017-03-22", "2017-03-29", "2017-04-05", "2017-04-12", "2017-04-19", "2017-04-26", "2017-05-03", "2017-05-10", "2017-05-17", "2017-05-24", "2017-05-31", "2017-06-07", "2017-06-14", "2017-06-21", "2017-06-28", "2017-07-05", "2017-07-12", "2017-07-19", "2017-07-26", "2017-08-02", "2017-08-09", "2017-08-16", "2017-08-23", "2017-08-30", "2017-09-06", "2017-09-13", "2017-09-20", "2017-09-27", "2017-10-04", "2017-10-11", "2017-10-18", "2017-10-25", "2017-11-01", "2017-11-08", "2017-11-15", "2017-11-22", "2017-11-29", "2017-12-06", "2017-12-13", "2017-12-20", "2017-12-27", "2018-01-03", "2018-01-10", "2018-01-17", "2018-01-24", "2018-01-31", "2018-02-07", "2018-02-14", "2018-02-21", "2018-02-28", "2018-03-07", "2018-03-14", "2018-03-21", "2018-03-28", "2018-04-04", "2018-04-11", "2018-04-18", "2018-04-25", "2018-05-02", "2018-05-09", "2018-05-16", "2018-05-23", "2018-05-30", "2018-06-06", "2018-06-13", "2018-06-20", "2018-06-27", "2018-07-11", "2018-07-18", "2018-07-25", "2018-08-01", "2018-08-08", "2018-08-15", "2018-08-22", "2018-08-29", "2018-09-05", "2018-09-12", "2018-09-19", "2018-09-26", "2018-10-03", "2018-10-10", "2018-10-17", "2018-10-24", "2018-10-31", "2018-11-07", "2018-11-14", "2018-11-21", "2018-11-28", "2018-12-12", "2018-12-19", "2018-12-26", "2019-01-02", "2019-01-09", "2019-01-16", "2019-01-23", "2019-01-30", "2019-02-06", "2019-02-13", "2019-02-20", "2019-02-27", "2019-03-06", "2019-03-13", "2019-03-20", "2019-03-27", "2019-04-03", "2019-04-10", "2019-04-17", "2019-04-24", "2019-05-01", "2019-05-08", "2019-05-15", "2019-05-22", "2019-05-29", "2019-06-05", "2019-06-12", "2019-06-19", "2019-06-26", "2019-07-03", "2019-07-10", "2019-07-17", "2019-07-24", "2019-07-31", "2019-08-07", "2019-08-14", "2019-08-21", "2019-08-28", "2019-09-04", "2019-09-11", "2019-09-18", "2019-09-25", "2019-10-02", "2019-10-09", "2019-10-16", "2019-10-23", "2019-10-30", "2019-11-06", "2019-11-13", "2019-11-20", "2019-11-27", "2019-12-04", "2019-12-11", "2019-12-18", "2020-01-08", "2020-01-15", "2020-01-22", "2020-01-29", "2020-02-05", "2020-02-12", "2020-02-19", "2020-02-26", "2020-03-04", "2020-03-11", "2020-03-18", "2020-03-25", "2020-04-01", "2020-04-08", "2020-04-15", "2020-04-22", "2020-04-29", "2020-05-06", "2020-05-13", "2020-05-20", "2020-05-27", "2020-06-03", "2020-06-10", "2020-06-17", "2020-06-24", "2020-07-01", "2020-07-08", "2020-07-15", "2020-07-22", "2020-07-29", "2020-08-05", "2020-08-12", "2020-08-19", "2020-08-26", "2020-09-02", "2020-09-09", "2020-09-16", "2020-09-23", "2020-09-30", "2020-10-07", "2020-10-14", "2020-10-21", "2020-10-28", "2020-11-04", "2020-11-11", "2020-11-18", "2020-11-25", "2020-12-02", "2020-12-09", "2020-12-16", "2020-12-23", "2020-12-30", "2021-01-06", "2021-01-13", "2021-01-20", "2021-01-27", "2021-02-03", "2021-02-10", "2021-02-17", "2021-02-24", "2021-03-03", "2021-03-10", "2021-03-17", "2021-03-24", "2021-03-31", "2021-04-07", "2021-04-14", "2021-04-21", "2021-04-28", "2021-05-05", "2021-05-12", "2021-05-19", "2021-05-26", "2021-06-02", "2021-06-09", "2021-06-16", "2021-06-23", "2021-06-30", "2021-07-07", "2021-07-14", "2021-07-21", "2021-07-28", "2021-08-04", "2021-08-11", "2021-08-18", "2021-08-25", "2021-09-01", "2021-09-08", "2021-09-15", "2021-09-22", "2021-09-29", "2021-10-06", "2021-10-13", "2021-10-20", "2021-10-27", "2021-11-03", "2021-11-10", "2021-11-17", "2021-11-24", "2021-12-01", "2021-12-08", "2021-12-15", "2021-12-22", "2021-12-29", "2022-01-05", "2022-01-12", "2022-01-19", "2022-01-26", "2022-02-02", "2022-02-09", "2022-02-16", "2022-02-23", "2022-03-02", "2022-03-09", "2022-03-16", "2022-03-23", "2022-03-30", "2022-04-06", "2022-04-13", "2022-04-20", "2022-04-27", "2022-05-04", "2022-05-11", "2022-05-18", "2022-05-25", "2022-06-01", "2022-06-08", "2022-06-15", "2022-06-22", "2022-06-29", "2022-07-06", "2022-07-13", "2022-07-20", "2022-07-27", "2022-08-03", "2022-08-10", "2022-08-17", "2022-08-24", "2022-08-31", "2022-09-07", "2022-09-14", "2022-09-21", "2022-09-28", "2022-10-05", "2022-10-12", "2022-10-19", "2022-10-26", "2022-11-02", "2022-11-09", "2022-11-16", "2022-11-23", "2022-11-30", "2022-12-07", "2022-12-14", "2022-12-21", "2022-12-28", "2023-01-04", "2023-01-11", "2023-01-18", "2023-01-25", "2023-02-01", "2023-02-08", "2023-02-15", "2023-02-22", "2023-03-01", "2023-03-08", "2023-03-15", "2023-03-22", "2023-03-29", "2023-04-05", "2023-04-12", "2023-04-19", "2023-04-26", "2023-05-03", "2023-05-10", "2023-05-17", "2023-05-24", "2023-05-31", "2023-06-07", "2023-06-14", "2023-06-21", "2023-06-28", "2023-07-05", "2023-07-12", "2023-07-19", "2023-07-26", "2023-08-02", "2023-08-09", "2023-08-16", "2023-08-23", "2023-08-30", "2023-09-06", "2023-09-13", "2023-09-20", "2023-09-27", "2023-10-04", "2023-10-11", "2023-10-18", "2023-10-25", "2023-11-01", "2023-11-08", "2023-11-15", "2023-11-22", "2023-11-29", "2023-12-06", "2023-12-13", "2023-12-20", "2023-12-27", "2024-01-03", "2024-01-10", "2024-01-17", "2024-01-24", "2024-01-31", "2024-02-07", "2024-02-14", "2024-02-21", "2024-02-28", "2024-03-06", "2024-03-13", "2024-03-20", "2024-03-27", "2024-04-03", "2024-04-10", "2024-04-17", "2024-04-24", "2024-05-01", "2024-05-08", "2024-05-15", "2024-05-22", "2024-05-29", "2024-06-05", "2024-06-12", "2024-06-26", "2024-07-03", "2024-07-10", "2024-07-17", "2024-07-24", "2024-07-31", "2024-08-07", "2024-08-14"], "xaxis": "x", "y": [0.24840648747375466, 0.26020265922924646, 0.25394500579739576, 0.24033220451933224, 0.24174074450116873, 0.22322357641259283, 0.24561324779253008, 0.2623210786802244, 0.25371221245113446, 0.2566013742099751, 0.26400055646023973, 0.27088086197486166, 0.2607299501822239, 0.26533869749037553, 0.26650154232603745, 0.2737944249639731, 0.24805028560658374, 0.22892224533950525, 0.1536579127019342, 0.2426671498297243, 0.2625538720264857, 0.22995242601518934, 0.22655083842066262, 0.2414678433856118, 0.2540602805748818, 0.25528174445797575, 0.2572843281836453, 0.2640518831859817, 0.27492697861723, 0.25304159932955517, 0.25632819262050704, 0.23729691585277812, 0.2743463976211325, 0.2607408886647591, 0.22345693070667647, 0.21306649419390958, 0.2221687140327268, 0.23433791609005905, 0.219289929808599, 0.2258496536427671, 0.2305930284282747, 0.23906698670609755, 0.21273357166136478, 0.20989461473262142, 0.19650507068783982, 0.2213702048076594, 0.23710002316714507, 0.23407931914397118, 0.2308499425308956, 0.2305506768676898, 0.23462904800984127, 0.2470184220874327, 0.2545474637585636, 0.2538855453282302, 0.26852151543419883, 0.23988428768321257, 0.22780820296438486, 0.1199850563500135, 0.2523822051644222, 0.2715542798355525, 0.25143560571426327, 0.2503013692175395, 0.2582275619468704, 0.25299980871679256, 0.23671353011756904, 0.22846310954693927, 0.2170680154844037, 0.22559638570099125, 0.19637885742781863, 0.17509593610131166, 0.12528825706219285, 0.16474588783175156, 0.19123608779282178, 0.1768660069546311, 0.173543513003051, 0.18569364283442447, 0.19948005746349493, 0.20362770566170246, 0.21589142742709502, 0.20787380020272656, 0.22012518111602813, 0.19656845779176157, 0.19382850815365707, 0.1696081835555902, 0.20628996402641617, 0.22066144723416262, 0.18723260318494955, 0.1734823696904185, 0.1851579376641123, 0.19253412105366194, 0.1870932076511039, 0.18953753778684768, 0.19142512720894242, 0.20667309138905826, 0.1905553776104408, 0.1680058361011434, 0.1401256074363731, 0.17323891833553318, 0.1818870509121853, 0.17080833142143617, 0.17465503111297095, 0.19446294014069693, 0.19050994083683317, 0.1920789118958522, 0.18976275833750772, 0.148626210735756, 0.19210864213043496, 0.1951604787577474, 0.1314062344862868, 0.10687542526856779, 0.1021180267875023, 0.13328204400411287, 0.11499458404877554, 0.12137256078851313, 0.12613893443473567, 0.14297550284765162, 0.14187941080284536, 0.13693297290565923, 0.09678088873207281, 0.13028518026338745, 0.1403213782263615, 0.08565196441122636, 0.08087437180855749, 0.09579165724741781, 0.12678935343471157, 0.1302647056678729, 0.13266948898214334, 0.14201880633669098, 0.16220675751403632, 0.1683036993947934, 0.16785858729778533, 0.19302915750685617, 0.20303674713089212, 0.21109279928108926, 0.19962814768858644, 0.19046983306753756, 0.21034729962523077, 0.22196200476020322, 0.1882294074652058, 0.17381080464038476, 0.1735286478857596, 0.18051020448230967, 0.18795005544969223, 0.18622261663086884, 0.1546544365082793, 0.19392919828776287, 0.19345351453443854, 0.16446288965539294, 0.1526801806476367, 0.16799545856643053, 0.18739303426213205, 0.1902850007600843, 0.20234341562250904, 0.2028785598449989, 0.21589226884882848, 0.22802500929771016, 0.2164035727888699, 0.21884117155074587, 0.22616546726673126, 0.22850630252925763, 0.1899602119709631, 0.17639256699259606, 0.19070234593988772, 0.20399232174620813, 0.19879990822893626, 0.19958579612800156, 0.2068963486223402, 0.21889838822862215, 0.23003208060595828, 0.22411604439789823, 0.21000287766232847, 0.2229170184276969, 0.2241948575669337, 0.1992293137869195, 0.1847994920056521, 0.1888293411611732, 0.20549734475348305, 0.1977750565575642, 0.18907615820299242, 0.1728939354248086, 0.2023518298398438, 0.21571192412395376, 0.1980574937861005, 0.1963409934498123, 0.21276638710897028, 0.20118365599986987, 0.17854408236509065, 0.17386100947048208, 0.16932855106616548, 0.17786421360444316, 0.16006421730669879, 0.1444118097466928, 0.13980951333849778, 0.15589581403906552, 0.15045658347997445, 0.1573870938246937, 0.14941770811304445, 0.15499661467989231, 0.16429039820003064, 0.1333824536643075, 0.13068934316936642, 0.14271494258418566, 0.13317882960480665, 0.1323396516626213, 0.13832131876589235, 0.13811376807163528, 0.12474105246152319, 0.12147381187044125, 0.1191192333862681, 0.12281587953533327, 0.13099505973252887, 0.10032103043871168, 0.10640815573257822, 0.10597875017459502, 0.12178850359876076, 0.1059941762397087, 0.10375627490257738, 0.0897754918530743, 0.1061372179343994, 0.10674135873903418, 0.09016619201131769, 0.0900318450078729, 0.0879810197694841, 0.061379471665684075, 0.06901341057958812, 0.05128409370745561, 0.06455359491826149, 0.05080925137586477, 0.048995426592404655, 0.045587668571832454, 0.05825555324320398, 0.05343785287123948, 0.050125736454372224, 0.06750950946795782, 0.08466694003523874, 0.07774035632527569, 0.053460290784132135, 0.05662880455848638, 0.055857501302801316, 0.061206699736410616, 0.025315575221139655, 0.011902471367820779, 0.009701031639140023, 0.01970749936753133, 0.025056136853318315, 0.030879055722873826, 0.03379598439891916, 0.04256808644430321, 0.05498494696518814, 0.0236046843630746, 0.027261783690666445, 0.022133598699049812, 0.03219756357922855, 0.028830193801863132, 0.035421891661903286, 0.02165539068052507, 0.04145740975611672, 0.03925961618828101, 0.032445502516692405, 0.029887299973018408, 0.01895526833780502, 0.021125855936258375, 0, 0.012577572071978581, 0.029592241418479978, 0.04065104726153687, 0.024024553808078435, 0.01990327015751976, 0.03170084428256737, 0.04382208530109155, 0.05023427985799045, 0.04354750134206767, 0.05484330764005325, 0.06544830669485617, 0.08755778463754636, 0.07796557687593574, 0.08617869441638147, 0.08908272129251353, 0.0707711405808166, 0.0704463517916954, 0.08258245792751095, 0.0947999014975624, 0.08611530731245971, 0.09382805939539922, 0.11414811378490007, 0.13737023173315488, 0.22897665727826993, 0.3628642444565734, 0.44699884500843384, 0.47819539719873877, 0.565331068599992, 0.5938544239430481, 0.5873322836129752, 0.5879927996737527, 0.6446370302973529, 0.662140565671003, 0.6478731382842962, 0.637007018018205, 0.6190151775652284, 0.585461803099461, 0.5774528705663385, 0.5352948369801486, 0.520260032972513, 0.49851432969259496, 0.4926215728191611, 0.4832635607733675, 0.5075545647970463, 0.5293294373637247, 0.5459093721479309, 0.5478704457347491, 0.5431335218491982, 0.5664439890794678, 0.5487932049024596, 0.5639087853965088, 0.5196331737810744, 0.5559419239500599, 0.5865104950532817, 0.5816504431207322, 0.581942416462248, 0.5972282446203702, 0.6148880039625354, 0.6421166917316852, 0.6501441355429443, 0.6347455568725364, 0.65393053334358, 0.656123558854926, 0.6747324419124506, 0.6516962781672937, 0.6463683957509324, 0.6527749808296082, 0.6600838504804799, 0.6631668197119308, 0.6606327379246165, 0.6834952883187665, 0.7208936796326016, 0.765293260716768, 0.7621673789769097, 0.801975321661505, 0.892459290614165, 0.9078239319413226, 0.8409561467820947, 0.9158373520570237, 0.9460514041365414, 0.9250576514124386, 0.9113337824655564, 0.9142125666896842, 0.9314701264432487, 0.932119704021491, 0.9080836507830551, 0.9071059187287576, 0.9371519669354915, 0.9364639644314204, 0.8682398074378316, 0.7710704623388046, 0.8714388928685021, 0.9021785530575303, 0.9173847266248836, 0.9109383142508233, 0.9245331651985728, 0.9488135112136274, 0.9611913858608614, 0.9684520139990138, 0.9702778991606538, 0.9899478150240899, 0.9849980114399699, 0.963874679768912, 0.9410934668089979, 0.9575009101378417, 0.9912901631628931, 0.966145957501471, 0.9484735768333037, 0.9576293671891521, 0.9710564947380289, 0.9718942703106586, 1, 0.9944081916332389, 0.9916528159300206, 0.9967967074606622, 0.9518005583674624, 0.9357047215539153, 0.9308441086735435, 0.903135530042402, 0.8715289249939838, 0.8731587588917241, 0.8525994602560054, 0.8623391972948852, 0.8649226424905635, 0.8536787238661422, 0.9050309727340092, 0.9251748895073026, 0.9119951399480675, 0.87365660008403, 0.8759334872948124, 0.8974744446195848, 0.8793134783981799, 0.7481720112840264, 0.7499022548419614, 0.7461971944755614, 0.7380373669782357, 0.7351305353629921, 0.7398413751748054, 0.7537497959552296, 0.7330264200814833, 0.7080168418974172, 0.6890200634207608, 0.6868640604656877, 0.7142935675552267, 0.7342153489908829, 0.7101234814441265, 0.7218601927304528, 0.741484110872459, 0.7448767233018286, 0.7365570256751428, 0.732137878730934, 0.6763286189408519, 0.7188720236809732, 0.693206136544797, 0.6447051854577642, 0.6391546067559434, 0.6662817629692539, 0.6671321598678855, 0.6493335659396969, 0.6677744451244378, 0.6528481845204205, 0.6599371626249441, 0.6901969319519806, 0.6961115657904848, 0.6559608839864542, 0.6831819989600028, 0.6937098676892371, 0.6592839388858567, 0.6444415399812755, 0.6589470897185556, 0.6725301607620364, 0.6708254403300168, 0.6356130626797487, 0.6456209327776958, 0.6462343292213988, 0.6636124927567613, 0.6291046655713226, 0.6371071472044885, 0.6320990050468476, 0.7634530713856589, 0.7481436834189994, 0.7556309344773677, 0.7504845186815258, 0.7390476340062276, 0.6858442573247164, 0.6762882306976452, 0.6894648950438577, 0.704974260909173, 0.7217381865790989, 0.7178045399751052, 0.7026278161684235, 0.7256502366919336, 0.7291971097724403, 0.6964629996011661, 0.685467580862031, 0.6853682930974809, 0.6773843227424514, 0.6905004047238538, 0.6759286631435404, 0.6890988765897963, 0.690809206500039, 0.6865235651375416, 0.6714548237866559, 0.6747560017209879, 0.7021294140282953, 0.7037822467867506, 0.6566303752123889, 0.6614396613670186, 0.6810714327785372, 0.6991603172047746, 0.680601078029525, 0.6844904097555558, 0.7013589521943437, 0.7115216438912502, 0.746207011062452, 0.7375364405729072, 0.7213505716338783, 0.7484892272775463, 0.7685534894600708, 0.7434297583941635, 0.7341749607476762, 0.7390669867060976, 0.7576133240814339, 0.7705611217161413, 0.7475981616617967, 0.7209637981103911, 0.7581282741823204, 0.751642595460698, 0.7465595667687779, 0.7502155442007251, 0.7764620123329988, 0.7635574076806098, 0.7403829702972519, 0.7357428099110505, 0.754074865218262, 0.7712813787199956, 0.6909233593818804, 0.6863970714036093, 0.6892497715539994, 0.6914004455047604, 0.7164520947755003, 0.7032193356470561, 0.708807217379061, 0.7264296736910423, 0.7178690489746715, 0.670520845662499, 0.6866236943238251, 0.6910843514068852, 0.6829278895964934, 0.6774984756242929, 0.6435779608088195, 0.698834967467831, 0.6872023120025444], "yaxis": "y"}, {"hovertemplate": "variable=SP500<br>date=%{x}<br>value=%{y}<extra></extra>", "legendgroup": "SP500", "line": {"color": "#EF553B", "dash": "solid"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "SP500", "showlegend": true, "type": "scattergl", "x": ["2014-08-27", "2014-09-03", "2014-09-10", "2014-09-17", "2014-09-24", "2014-10-01", "2014-10-08", "2014-10-15", "2014-10-22", "2014-10-29", "2014-11-05", "2014-11-12", "2014-11-19", "2014-11-26", "2014-12-03", "2014-12-10", "2014-12-17", "2014-12-24", "2014-12-31", "2015-01-07", "2015-01-14", "2015-01-21", "2015-01-28", "2015-02-04", "2015-02-11", "2015-02-18", "2015-02-25", "2015-03-04", "2015-03-11", "2015-03-18", "2015-03-25", "2015-04-01", "2015-04-08", "2015-04-15", "2015-04-22", "2015-04-29", "2015-05-06", "2015-05-13", "2015-05-20", "2015-05-27", "2015-06-03", "2015-06-10", "2015-06-17", "2015-06-24", "2015-07-01", "2015-07-08", "2015-07-15", "2015-07-22", "2015-07-29", "2015-08-05", "2015-08-12", "2015-08-19", "2015-08-26", "2015-09-02", "2015-09-09", "2015-09-16", "2015-09-23", "2015-09-30", "2015-10-07", "2015-10-14", "2015-10-21", "2015-10-28", "2015-11-04", "2015-11-11", "2015-11-18", "2015-11-25", "2015-12-02", "2015-12-09", "2015-12-16", "2015-12-23", "2015-12-30", "2016-01-06", "2016-01-13", "2016-01-20", "2016-01-27", "2016-02-03", "2016-02-10", "2016-02-17", "2016-02-24", "2016-03-02", "2016-03-09", "2016-03-16", "2016-03-23", "2016-03-30", "2016-04-06", "2016-04-13", "2016-04-20", "2016-04-27", "2016-05-04", "2016-05-11", "2016-05-18", "2016-05-25", "2016-06-01", "2016-06-08", "2016-06-15", "2016-06-22", "2016-06-29", "2016-07-06", "2016-07-13", "2016-07-20", "2016-07-27", "2016-08-03", "2016-08-10", "2016-08-17", "2016-08-24", "2016-08-31", "2016-09-07", "2016-09-14", "2016-09-21", "2016-09-28", "2016-10-05", "2016-10-12", "2016-10-19", "2016-10-26", "2016-11-02", "2016-11-09", "2016-11-16", "2016-11-23", "2016-11-30", "2016-12-07", "2016-12-14", "2016-12-21", "2016-12-28", "2017-01-04", "2017-01-11", "2017-01-18", "2017-01-25", "2017-02-01", "2017-02-08", "2017-02-15", "2017-02-22", "2017-03-01", "2017-03-08", "2017-03-15", "2017-03-22", "2017-03-29", "2017-04-05", "2017-04-12", "2017-04-19", "2017-04-26", "2017-05-03", "2017-05-10", "2017-05-17", "2017-05-24", "2017-05-31", "2017-06-07", "2017-06-14", "2017-06-21", "2017-06-28", "2017-07-05", "2017-07-12", "2017-07-19", "2017-07-26", "2017-08-02", "2017-08-09", "2017-08-16", "2017-08-23", "2017-08-30", "2017-09-06", "2017-09-13", "2017-09-20", "2017-09-27", "2017-10-04", "2017-10-11", "2017-10-18", "2017-10-25", "2017-11-01", "2017-11-08", "2017-11-15", "2017-11-22", "2017-11-29", "2017-12-06", "2017-12-13", "2017-12-20", "2017-12-27", "2018-01-03", "2018-01-10", "2018-01-17", "2018-01-24", "2018-01-31", "2018-02-07", "2018-02-14", "2018-02-21", "2018-02-28", "2018-03-07", "2018-03-14", "2018-03-21", "2018-03-28", "2018-04-04", "2018-04-11", "2018-04-18", "2018-04-25", "2018-05-02", "2018-05-09", "2018-05-16", "2018-05-23", "2018-05-30", "2018-06-06", "2018-06-13", "2018-06-20", "2018-06-27", "2018-07-11", "2018-07-18", "2018-07-25", "2018-08-01", "2018-08-08", "2018-08-15", "2018-08-22", "2018-08-29", "2018-09-05", "2018-09-12", "2018-09-19", "2018-09-26", "2018-10-03", "2018-10-10", "2018-10-17", "2018-10-24", "2018-10-31", "2018-11-07", "2018-11-14", "2018-11-21", "2018-11-28", "2018-12-12", "2018-12-19", "2018-12-26", "2019-01-02", "2019-01-09", "2019-01-16", "2019-01-23", "2019-01-30", "2019-02-06", "2019-02-13", "2019-02-20", "2019-02-27", "2019-03-06", "2019-03-13", "2019-03-20", "2019-03-27", "2019-04-03", "2019-04-10", "2019-04-17", "2019-04-24", "2019-05-01", "2019-05-08", "2019-05-15", "2019-05-22", "2019-05-29", "2019-06-05", "2019-06-12", "2019-06-19", "2019-06-26", "2019-07-03", "2019-07-10", "2019-07-17", "2019-07-24", "2019-07-31", "2019-08-07", "2019-08-14", "2019-08-21", "2019-08-28", "2019-09-04", "2019-09-11", "2019-09-18", "2019-09-25", "2019-10-02", "2019-10-09", "2019-10-16", "2019-10-23", "2019-10-30", "2019-11-06", "2019-11-13", "2019-11-20", "2019-11-27", "2019-12-04", "2019-12-11", "2019-12-18", "2020-01-08", "2020-01-15", "2020-01-22", "2020-01-29", "2020-02-05", "2020-02-12", "2020-02-19", "2020-02-26", "2020-03-04", "2020-03-11", "2020-03-18", "2020-03-25", "2020-04-01", "2020-04-08", "2020-04-15", "2020-04-22", "2020-04-29", "2020-05-06", "2020-05-13", "2020-05-20", "2020-05-27", "2020-06-03", "2020-06-10", "2020-06-17", "2020-06-24", "2020-07-01", "2020-07-08", "2020-07-15", "2020-07-22", "2020-07-29", "2020-08-05", "2020-08-12", "2020-08-19", "2020-08-26", "2020-09-02", "2020-09-09", "2020-09-16", "2020-09-23", "2020-09-30", "2020-10-07", "2020-10-14", "2020-10-21", "2020-10-28", "2020-11-04", "2020-11-11", "2020-11-18", "2020-11-25", "2020-12-02", "2020-12-09", "2020-12-16", "2020-12-23", "2020-12-30", "2021-01-06", "2021-01-13", "2021-01-20", "2021-01-27", "2021-02-03", "2021-02-10", "2021-02-17", "2021-02-24", "2021-03-03", "2021-03-10", "2021-03-17", "2021-03-24", "2021-03-31", "2021-04-07", "2021-04-14", "2021-04-21", "2021-04-28", "2021-05-05", "2021-05-12", "2021-05-19", "2021-05-26", "2021-06-02", "2021-06-09", "2021-06-16", "2021-06-23", "2021-06-30", "2021-07-07", "2021-07-14", "2021-07-21", "2021-07-28", "2021-08-04", "2021-08-11", "2021-08-18", "2021-08-25", "2021-09-01", "2021-09-08", "2021-09-15", "2021-09-22", "2021-09-29", "2021-10-06", "2021-10-13", "2021-10-20", "2021-10-27", "2021-11-03", "2021-11-10", "2021-11-17", "2021-11-24", "2021-12-01", "2021-12-08", "2021-12-15", "2021-12-22", "2021-12-29", "2022-01-05", "2022-01-12", "2022-01-19", "2022-01-26", "2022-02-02", "2022-02-09", "2022-02-16", "2022-02-23", "2022-03-02", "2022-03-09", "2022-03-16", "2022-03-23", "2022-03-30", "2022-04-06", "2022-04-13", "2022-04-20", "2022-04-27", "2022-05-04", "2022-05-11", "2022-05-18", "2022-05-25", "2022-06-01", "2022-06-08", "2022-06-15", "2022-06-22", "2022-06-29", "2022-07-06", "2022-07-13", "2022-07-20", "2022-07-27", "2022-08-03", "2022-08-10", "2022-08-17", "2022-08-24", "2022-08-31", "2022-09-07", "2022-09-14", "2022-09-21", "2022-09-28", "2022-10-05", "2022-10-12", "2022-10-19", "2022-10-26", "2022-11-02", "2022-11-09", "2022-11-16", "2022-11-23", "2022-11-30", "2022-12-07", "2022-12-14", "2022-12-21", "2022-12-28", "2023-01-04", "2023-01-11", "2023-01-18", "2023-01-25", "2023-02-01", "2023-02-08", "2023-02-15", "2023-02-22", "2023-03-01", "2023-03-08", "2023-03-15", "2023-03-22", "2023-03-29", "2023-04-05", "2023-04-12", "2023-04-19", "2023-04-26", "2023-05-03", "2023-05-10", "2023-05-17", "2023-05-24", "2023-05-31", "2023-06-07", "2023-06-14", "2023-06-21", "2023-06-28", "2023-07-05", "2023-07-12", "2023-07-19", "2023-07-26", "2023-08-02", "2023-08-09", "2023-08-16", "2023-08-23", "2023-08-30", "2023-09-06", "2023-09-13", "2023-09-20", "2023-09-27", "2023-10-04", "2023-10-11", "2023-10-18", "2023-10-25", "2023-11-01", "2023-11-08", "2023-11-15", "2023-11-22", "2023-11-29", "2023-12-06", "2023-12-13", "2023-12-20", "2023-12-27", "2024-01-03", "2024-01-10", "2024-01-17", "2024-01-24", "2024-01-31", "2024-02-07", "2024-02-14", "2024-02-21", "2024-02-28", "2024-03-06", "2024-03-13", "2024-03-20", "2024-03-27", "2024-04-03", "2024-04-10", "2024-04-17", "2024-04-24", "2024-05-01", "2024-05-08", "2024-05-15", "2024-05-22", "2024-05-29", "2024-06-05", "2024-06-12", "2024-06-26", "2024-07-03", "2024-07-10", "2024-07-17", "2024-07-24", "2024-07-31", "2024-08-07", "2024-08-14"], "xaxis": "x", "y": [0.03920096244100421, 0.03935960656257853, 0.0380296400100475, 0.0395843524014754, 0.03871974193889559, 0.02493356777409082, 0.030943535913063074, 0.002810645020557663, 0.019896616914107428, 0.03448923203024816, 0.04540130352586561, 0.04928279636704964, 0.05205113628852075, 0.05842598590711387, 0.058822596211049565, 0.04608082917994215, 0.04257743816184349, 0.06081886807419262, 0.05474279821789775, 0.04601737153131243, 0.042149099033592916, 0.04766198225829907, 0.03974035245435681, 0.05014476276093655, 0.05728903636916495, 0.06552531034756282, 0.06927459975410166, 0.06522124244787887, 0.049808966036937664, 0.06547771711109057, 0.05531127298687227, 0.05495167964463721, 0.060824156211578426, 0.0673629380891316, 0.0677145992252879, 0.06742110760037545, 0.060361444190320114, 0.0652080221044143, 0.07244483811689427, 0.07181819383667591, 0.06933012519665267, 0.06698483626604616, 0.06572625956822362, 0.06787853148424797, 0.05963961343715714, 0.05151174627516827, 0.06756653137848526, 0.0693512777461959, 0.06787588741555513, 0.06556761544664937, 0.06192144471913388, 0.060218664480903274, 0.02343966896259967, 0.02564746632117502, 0.023844211472614073, 0.037929165399717094, 0.02297695694134136, 0.01802461627953096, 0.03806665697174813, 0.037646250049576314, 0.04417709972105079, 0.0630583942570828, 0.06622070041379675, 0.058999748813474195, 0.061268359751986363, 0.0626670720905329, 0.06019222379397425, 0.05176028873230126, 0.05848944355574365, 0.056167951243373315, 0.05592205285493323, 0.03659391070980026, 0.010158511918139651, 0.0019751193135997746, 0.00822040956624057, 0.01604156475985248, 0, 0.019819938922013204, 0.02060787139249879, 0.03558652053780361, 0.0363295038405098, 0.04636638859877583, 0.048875609788342336, 0.05607805290781452, 0.05679459552359169, 0.06096164778360946, 0.06624449703203295, 0.06432754722967707, 0.052685712774817886, 0.05621290041115271, 0.05176293280099422, 0.063108631562248, 0.06543276794331117, 0.07066537988656944, 0.05807432477095757, 0.0617628005975595, 0.05788130775637553, 0.06553853069102739, 0.0794727727026348, 0.08491691014132549, 0.08321412990309489, 0.0824764347377745, 0.08556999510847288, 0.0873494533387977, 0.08555677476500843, 0.08436958792189418, 0.08839121640380215, 0.07242368556735106, 0.08229928213534987, 0.0844806388069962, 0.08140294284845523, 0.07596938168453614, 0.07732050078661044, 0.07603548340185876, 0.06506524239499746, 0.08233629909705062, 0.08595338506894412, 0.09329860789783316, 0.09173596330032655, 0.10298383151994289, 0.1061382054705782, 0.10928464721513463, 0.1052497983897622, 0.11075739347708255, 0.11196573286974001, 0.11105881730807365, 0.11806031120688515, 0.11308417392683869, 0.11708200579051048, 0.131513332716384, 0.13510133393265564, 0.14386377758094157, 0.13514363903174206, 0.14103462407953365, 0.13130180722095158, 0.13465448632355473, 0.13249163813275866, 0.13037109504104916, 0.12858370460464566, 0.1416136751232797, 0.14179347179439727, 0.1448341507912376, 0.13357041815946386, 0.14609272748906016, 0.14805198239050257, 0.153694424981161, 0.15495828981636947, 0.1543475099483085, 0.15569069684430406, 0.15353578085958675, 0.15636757842968763, 0.16445314049259, 0.16551076796975187, 0.1654420221837364, 0.1645033777977552, 0.1629407332002486, 0.15657645985642707, 0.16015917293531293, 0.16226120754617207, 0.17094168506497798, 0.17355138086487484, 0.17323409262172632, 0.18135138350894353, 0.18597850372152663, 0.18757023307465537, 0.1864835208418715, 0.19235599740881273, 0.1963273885855555, 0.18845864015547123, 0.19704128713263971, 0.20470644227337031, 0.20555254425509975, 0.21443132692587352, 0.21876759958223715, 0.219658650731746, 0.22770719583294774, 0.2370063854258934, 0.2513716106344443, 0.26062056292222474, 0.25699025660686664, 0.21940482013722715, 0.22389180470908637, 0.2246057032561706, 0.2279107891223014, 0.23134014621699878, 0.23733689401250646, 0.2274084160706495, 0.1991353895374202, 0.20962969817955873, 0.20896868100633256, 0.22653851747068388, 0.20823098584101218, 0.20724474821855876, 0.22366970293888236, 0.23019262040427813, 0.23305614679869383, 0.2306024510516784, 0.24338387911317935, 0.24425113364445214, 0.24205391256064837, 0.22415621157837684, 0.24382543858489444, 0.2548247643473777, 0.26287595351727244, 0.2542272048227813, 0.26595100540712047, 0.2555518832379265, 0.26704036170859724, 0.2808476884229452, 0.2741211776681958, 0.2742057878663688, 0.27923745058896626, 0.27871392498777114, 0.2838804352137069, 0.24690842268082122, 0.2531299163152259, 0.2126465805581629, 0.22735817876548428, 0.25436734046350523, 0.2246718049734932, 0.2110151901746407, 0.2358324189262437, 0.21131661400563193, 0.1732129400721831, 0.16283232638383943, 0.17402466916090487, 0.1938366758768393, 0.20207030578654434, 0.2080459010325088, 0.21924353194696006, 0.2326119432582859, 0.23827553839848767, 0.24664930394891657, 0.2486799487050674, 0.24314591293081791, 0.2535820520617126, 0.25710130749196863, 0.25211459393715047, 0.2701021932549808, 0.2740180589891726, 0.2772543990692878, 0.2843405031662723, 0.28340979098636987, 0.2716939226081094, 0.26416890310810276, 0.2655729035840351, 0.24620510040850863, 0.25760896868100636, 0.2718049734932114, 0.2841316217395328, 0.28077894263692976, 0.3024708821935194, 0.30174376330297065, 0.29945664388360815, 0.308747901270475, 0.29838844013167465, 0.2728996179320739, 0.26142964794225354, 0.28359487579487314, 0.27394666913446414, 0.28712470749990093, 0.3038220012955936, 0.30535556113747836, 0.2995756269747888, 0.2738594148675983, 0.28226490924234215, 0.3008500680847689, 0.3047712219563464, 0.31594241218386854, 0.32387726233127545, 0.3284409248952288, 0.3322536719503973, 0.3441969302362476, 0.33339062148834636, 0.34102404780476203, 0.3541148319033328, 0.37048426118110556, 0.3800663661241919, 0.3886490131013604, 0.37586494097116646, 0.392070437989979, 0.4039052894594201, 0.4056768154836663, 0.33435041842387064, 0.3379807247392287, 0.23519519837125372, 0.14442960828122314, 0.1649105643764625, 0.16357266561785277, 0.2374690974471517, 0.2462949987440674, 0.25051228830925026, 0.2875821313837734, 0.263497309660105, 0.25598286643487, 0.2960695918879973, 0.31312912309461804, 0.33606377493687284, 0.35385042503404235, 0.33358363850292827, 0.3168837006385426, 0.33421028278314674, 0.34850940627437504, 0.36348012321360107, 0.37655768696870745, 0.37190941420658113, 0.390240742454489, 0.40414325564178155, 0.402689017860684, 0.43015560344257747, 0.45715418886582676, 0.40906386747927714, 0.4055023069499345, 0.36621937837945034, 0.39955579645959205, 0.41448156423103866, 0.4327838077233247, 0.4187411588953081, 0.3752382966909481, 0.420824685025317, 0.4549913406750307, 0.45370367922158616, 0.47005988815589433, 0.480466942531167, 0.4814743327031637, 0.4889702674475483, 0.4860194867862668, 0.4971325075025449, 0.5013894580981214, 0.5177033619333431, 0.5288110945122354, 0.5020848481643553, 0.5230787535860182, 0.5441546251371612, 0.5498261524834416, 0.5482661519546277, 0.5203157018019328, 0.5412276410941156, 0.5611401224203805, 0.5386708266680768, 0.5608149019711531, 0.5891223013973903, 0.600943932523367, 0.61383641146997, 0.616417022514245, 0.6122949194220066, 0.5846511812376886, 0.5985695588371387, 0.6198040745098558, 0.6230113298343491, 0.6260335003503392, 0.6271307888578945, 0.6319271294668236, 0.6466440158115309, 0.6626750042966117, 0.6669504633730385, 0.6628230721434142, 0.6739149403101494, 0.674449042186116, 0.6863579275789585, 0.673817109768512, 0.699179016670853, 0.7065559683240572, 0.7039066114937665, 0.6950833542655438, 0.672592905963697, 0.663026665432768, 0.664108089528166, 0.6641741912454887, 0.7097552914424716, 0.7138509538477811, 0.7426422178448195, 0.7389775386364539, 0.7500720508718817, 0.7534538147301068, 0.7036342724183975, 0.7533877130127842, 0.7556721883634538, 0.7521582210705836, 0.7776734839571133, 0.7532211366851311, 0.7600349017067465, 0.7088483758808055, 0.66050686796843, 0.7238190928200315, 0.7232373977075925, 0.6935788791792812, 0.6276067212226174, 0.6701868034531537, 0.641456353036052, 0.6626036144419032, 0.688615962242699, 0.7272748906016578, 0.6952023373567244, 0.6860644359540462, 0.6894647082931215, 0.6166232598722915, 0.6473499821525364, 0.5508441189302097, 0.5478034399333693, 0.5623590380878095, 0.5947488795758913, 0.598593355455375, 0.5124548855779273, 0.5044962388122843, 0.5200803796882643, 0.527021060007139, 0.515572242566862, 0.55738025673907, 0.5742256183815655, 0.6090109861054191, 0.6235718723972449, 0.6404410306579765, 0.6052035271876365, 0.5560846630795468, 0.5626604619188007, 0.5537076453246257, 0.5124390211657699, 0.49369521820176887, 0.5106807154849884, 0.4561467986938301, 0.48738118216311255, 0.5231924485398131, 0.5044433574384263, 0.5015031530519163, 0.5570867651141577, 0.5751907034544759, 0.5891646064964767, 0.5505109662749039, 0.5667455480493383, 0.5358416731666689, 0.5106648510728308, 0.5291072302058407, 0.5599476474398805, 0.549173067516294, 0.5722716516175089, 0.599502915085734, 0.5991459658121918, 0.6070094261048904, 0.5656165307174682, 0.5551301542814081, 0.565870361311987, 0.5394085218333972, 0.5513174072262397, 0.5753361272325854, 0.59188006504409, 0.5922951838288759, 0.6088391216403803, 0.5827871128091908, 0.5919778955857273, 0.6043759336867573, 0.6099628508348649, 0.5984532198146508, 0.615531259502122, 0.6387170978702028, 0.6664983276265518, 0.6646739202284475, 0.667627344958422, 0.6861252495339829, 0.6928253196018033, 0.7175632262926191, 0.7178355653679883, 0.7037268148226492, 0.6916487090334608, 0.6748906016578311, 0.6832670112769531, 0.704118136989199, 0.6910590817149429, 0.6915773191787522, 0.6743274150262424, 0.6405653018865431, 0.6377202839729776, 0.6676511415766582, 0.6511653732763979, 0.6173662431749979, 0.6308747901270475, 0.6691926336246216, 0.7009478986264064, 0.7151571237820759, 0.7135601062915615, 0.7132322417736414, 0.7549424254042121, 0.7526315093666135, 0.7746380930976587, 0.7543395777422299, 0.7751325339432319, 0.7634351740458217, 0.7976335585198504, 0.7915786412130986, 0.8310836715537872, 0.832553773747042, 0.8275776364669956, 0.8508348646897848, 0.860089105114951, 0.8760989410504887, 0.891780912468106, 0.8980923044380693, 0.8883092502743221, 0.8748641609709021, 0.8382623180550232, 0.8513293055353579, 0.8372522838143336, 0.8820110786478234, 0.9138668182599383, 0.9135653944289474, 0.9029732552451712, 0.9259978054229848, 0.943713065665446, 0.9587498843219946, 0.974381618434447, 1, 0.9879324704855834, 0.9453259475681179, 0.9704895493184914, 0.8851390119115295, 0.9527504924577941], "yaxis": "y"}], "layout": {"legend": {"title": {"text": "variable"}, "tracegroupgap": 0}, "margin": {"t": 60}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "date"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "value"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "\n", "pd.options.plotting.backend = \"plotly\"\n", "normalized.plot()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you just want to visualize the results, this is a fast way of doing it. However, titles and other customizatons clean things up."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"name": "USD Liquidity Index", "type": "scatter", "x": ["2014-08-27", "2014-09-03", "2014-09-10", "2014-09-17", "2014-09-24", "2014-10-01", "2014-10-08", "2014-10-15", "2014-10-22", "2014-10-29", "2014-11-05", "2014-11-12", "2014-11-19", "2014-11-26", "2014-12-03", "2014-12-10", "2014-12-17", "2014-12-24", "2014-12-31", "2015-01-07", "2015-01-14", "2015-01-21", "2015-01-28", "2015-02-04", "2015-02-11", "2015-02-18", "2015-02-25", "2015-03-04", "2015-03-11", "2015-03-18", "2015-03-25", "2015-04-01", "2015-04-08", "2015-04-15", "2015-04-22", "2015-04-29", "2015-05-06", "2015-05-13", "2015-05-20", "2015-05-27", "2015-06-03", "2015-06-10", "2015-06-17", "2015-06-24", "2015-07-01", "2015-07-08", "2015-07-15", "2015-07-22", "2015-07-29", "2015-08-05", "2015-08-12", "2015-08-19", "2015-08-26", "2015-09-02", "2015-09-09", "2015-09-16", "2015-09-23", "2015-09-30", "2015-10-07", "2015-10-14", "2015-10-21", "2015-10-28", "2015-11-04", "2015-11-11", "2015-11-18", "2015-11-25", "2015-12-02", "2015-12-09", "2015-12-16", "2015-12-23", "2015-12-30", "2016-01-06", "2016-01-13", "2016-01-20", "2016-01-27", "2016-02-03", "2016-02-10", "2016-02-17", "2016-02-24", "2016-03-02", "2016-03-09", "2016-03-16", "2016-03-23", "2016-03-30", "2016-04-06", "2016-04-13", "2016-04-20", "2016-04-27", "2016-05-04", "2016-05-11", "2016-05-18", "2016-05-25", "2016-06-01", "2016-06-08", "2016-06-15", "2016-06-22", "2016-06-29", "2016-07-06", "2016-07-13", "2016-07-20", "2016-07-27", "2016-08-03", "2016-08-10", "2016-08-17", "2016-08-24", "2016-08-31", "2016-09-07", "2016-09-14", "2016-09-21", "2016-09-28", "2016-10-05", "2016-10-12", "2016-10-19", "2016-10-26", "2016-11-02", "2016-11-09", "2016-11-16", "2016-11-23", "2016-11-30", "2016-12-07", "2016-12-14", "2016-12-21", "2016-12-28", "2017-01-04", "2017-01-11", "2017-01-18", "2017-01-25", "2017-02-01", "2017-02-08", "2017-02-15", "2017-02-22", "2017-03-01", "2017-03-08", "2017-03-15", "2017-03-22", "2017-03-29", "2017-04-05", "2017-04-12", "2017-04-19", "2017-04-26", "2017-05-03", "2017-05-10", "2017-05-17", "2017-05-24", "2017-05-31", "2017-06-07", "2017-06-14", "2017-06-21", "2017-06-28", "2017-07-05", "2017-07-12", "2017-07-19", "2017-07-26", "2017-08-02", "2017-08-09", "2017-08-16", "2017-08-23", "2017-08-30", "2017-09-06", "2017-09-13", "2017-09-20", "2017-09-27", "2017-10-04", "2017-10-11", "2017-10-18", "2017-10-25", "2017-11-01", "2017-11-08", "2017-11-15", "2017-11-22", "2017-11-29", "2017-12-06", "2017-12-13", "2017-12-20", "2017-12-27", "2018-01-03", "2018-01-10", "2018-01-17", "2018-01-24", "2018-01-31", "2018-02-07", "2018-02-14", "2018-02-21", "2018-02-28", "2018-03-07", "2018-03-14", "2018-03-21", "2018-03-28", "2018-04-04", "2018-04-11", "2018-04-18", "2018-04-25", "2018-05-02", "2018-05-09", "2018-05-16", "2018-05-23", "2018-05-30", "2018-06-06", "2018-06-13", "2018-06-20", "2018-06-27", "2018-07-11", "2018-07-18", "2018-07-25", "2018-08-01", "2018-08-08", "2018-08-15", "2018-08-22", "2018-08-29", "2018-09-05", "2018-09-12", "2018-09-19", "2018-09-26", "2018-10-03", "2018-10-10", "2018-10-17", "2018-10-24", "2018-10-31", "2018-11-07", "2018-11-14", "2018-11-21", "2018-11-28", "2018-12-12", "2018-12-19", "2018-12-26", "2019-01-02", "2019-01-09", "2019-01-16", "2019-01-23", "2019-01-30", "2019-02-06", "2019-02-13", "2019-02-20", "2019-02-27", "2019-03-06", "2019-03-13", "2019-03-20", "2019-03-27", "2019-04-03", "2019-04-10", "2019-04-17", "2019-04-24", "2019-05-01", "2019-05-08", "2019-05-15", "2019-05-22", "2019-05-29", "2019-06-05", "2019-06-12", "2019-06-19", "2019-06-26", "2019-07-03", "2019-07-10", "2019-07-17", "2019-07-24", "2019-07-31", "2019-08-07", "2019-08-14", "2019-08-21", "2019-08-28", "2019-09-04", "2019-09-11", "2019-09-18", "2019-09-25", "2019-10-02", "2019-10-09", "2019-10-16", "2019-10-23", "2019-10-30", "2019-11-06", "2019-11-13", "2019-11-20", "2019-11-27", "2019-12-04", "2019-12-11", "2019-12-18", "2020-01-08", "2020-01-15", "2020-01-22", "2020-01-29", "2020-02-05", "2020-02-12", "2020-02-19", "2020-02-26", "2020-03-04", "2020-03-11", "2020-03-18", "2020-03-25", "2020-04-01", "2020-04-08", "2020-04-15", "2020-04-22", "2020-04-29", "2020-05-06", "2020-05-13", "2020-05-20", "2020-05-27", "2020-06-03", "2020-06-10", "2020-06-17", "2020-06-24", "2020-07-01", "2020-07-08", "2020-07-15", "2020-07-22", "2020-07-29", "2020-08-05", "2020-08-12", "2020-08-19", "2020-08-26", "2020-09-02", "2020-09-09", "2020-09-16", "2020-09-23", "2020-09-30", "2020-10-07", "2020-10-14", "2020-10-21", "2020-10-28", "2020-11-04", "2020-11-11", "2020-11-18", "2020-11-25", "2020-12-02", "2020-12-09", "2020-12-16", "2020-12-23", "2020-12-30", "2021-01-06", "2021-01-13", "2021-01-20", "2021-01-27", "2021-02-03", "2021-02-10", "2021-02-17", "2021-02-24", "2021-03-03", "2021-03-10", "2021-03-17", "2021-03-24", "2021-03-31", "2021-04-07", "2021-04-14", "2021-04-21", "2021-04-28", "2021-05-05", "2021-05-12", "2021-05-19", "2021-05-26", "2021-06-02", "2021-06-09", "2021-06-16", "2021-06-23", "2021-06-30", "2021-07-07", "2021-07-14", "2021-07-21", "2021-07-28", "2021-08-04", "2021-08-11", "2021-08-18", "2021-08-25", "2021-09-01", "2021-09-08", "2021-09-15", "2021-09-22", "2021-09-29", "2021-10-06", "2021-10-13", "2021-10-20", "2021-10-27", "2021-11-03", "2021-11-10", "2021-11-17", "2021-11-24", "2021-12-01", "2021-12-08", "2021-12-15", "2021-12-22", "2021-12-29", "2022-01-05", "2022-01-12", "2022-01-19", "2022-01-26", "2022-02-02", "2022-02-09", "2022-02-16", "2022-02-23", "2022-03-02", "2022-03-09", "2022-03-16", "2022-03-23", "2022-03-30", "2022-04-06", "2022-04-13", "2022-04-20", "2022-04-27", "2022-05-04", "2022-05-11", "2022-05-18", "2022-05-25", "2022-06-01", "2022-06-08", "2022-06-15", "2022-06-22", "2022-06-29", "2022-07-06", "2022-07-13", "2022-07-20", "2022-07-27", "2022-08-03", "2022-08-10", "2022-08-17", "2022-08-24", "2022-08-31", "2022-09-07", "2022-09-14", "2022-09-21", "2022-09-28", "2022-10-05", "2022-10-12", "2022-10-19", "2022-10-26", "2022-11-02", "2022-11-09", "2022-11-16", "2022-11-23", "2022-11-30", "2022-12-07", "2022-12-14", "2022-12-21", "2022-12-28", "2023-01-04", "2023-01-11", "2023-01-18", "2023-01-25", "2023-02-01", "2023-02-08", "2023-02-15", "2023-02-22", "2023-03-01", "2023-03-08", "2023-03-15", "2023-03-22", "2023-03-29", "2023-04-05", "2023-04-12", "2023-04-19", "2023-04-26", "2023-05-03", "2023-05-10", "2023-05-17", "2023-05-24", "2023-05-31", "2023-06-07", "2023-06-14", "2023-06-21", "2023-06-28", "2023-07-05", "2023-07-12", "2023-07-19", "2023-07-26", "2023-08-02", "2023-08-09", "2023-08-16", "2023-08-23", "2023-08-30", "2023-09-06", "2023-09-13", "2023-09-20", "2023-09-27", "2023-10-04", "2023-10-11", "2023-10-18", "2023-10-25", "2023-11-01", "2023-11-08", "2023-11-15", "2023-11-22", "2023-11-29", "2023-12-06", "2023-12-13", "2023-12-20", "2023-12-27", "2024-01-03", "2024-01-10", "2024-01-17", "2024-01-24", "2024-01-31", "2024-02-07", "2024-02-14", "2024-02-21", "2024-02-28", "2024-03-06", "2024-03-13", "2024-03-20", "2024-03-27", "2024-04-03", "2024-04-10", "2024-04-17", "2024-04-24", "2024-05-01", "2024-05-08", "2024-05-15", "2024-05-22", "2024-05-29", "2024-06-05", "2024-06-12", "2024-06-26", "2024-07-03", "2024-07-10", "2024-07-17", "2024-07-24", "2024-07-31", "2024-08-07", "2024-08-14"], "y": [0.24840648747375466, 0.26020265922924646, 0.25394500579739576, 0.24033220451933224, 0.24174074450116873, 0.22322357641259283, 0.24561324779253008, 0.2623210786802244, 0.25371221245113446, 0.2566013742099751, 0.26400055646023973, 0.27088086197486166, 0.2607299501822239, 0.26533869749037553, 0.26650154232603745, 0.2737944249639731, 0.24805028560658374, 0.22892224533950525, 0.1536579127019342, 0.2426671498297243, 0.2625538720264857, 0.22995242601518934, 0.22655083842066262, 0.2414678433856118, 0.2540602805748818, 0.25528174445797575, 0.2572843281836453, 0.2640518831859817, 0.27492697861723, 0.25304159932955517, 0.25632819262050704, 0.23729691585277812, 0.2743463976211325, 0.2607408886647591, 0.22345693070667647, 0.21306649419390958, 0.2221687140327268, 0.23433791609005905, 0.219289929808599, 0.2258496536427671, 0.2305930284282747, 0.23906698670609755, 0.21273357166136478, 0.20989461473262142, 0.19650507068783982, 0.2213702048076594, 0.23710002316714507, 0.23407931914397118, 0.2308499425308956, 0.2305506768676898, 0.23462904800984127, 0.2470184220874327, 0.2545474637585636, 0.2538855453282302, 0.26852151543419883, 0.23988428768321257, 0.22780820296438486, 0.1199850563500135, 0.2523822051644222, 0.2715542798355525, 0.25143560571426327, 0.2503013692175395, 0.2582275619468704, 0.25299980871679256, 0.23671353011756904, 0.22846310954693927, 0.2170680154844037, 0.22559638570099125, 0.19637885742781863, 0.17509593610131166, 0.12528825706219285, 0.16474588783175156, 0.19123608779282178, 0.1768660069546311, 0.173543513003051, 0.18569364283442447, 0.19948005746349493, 0.20362770566170246, 0.21589142742709502, 0.20787380020272656, 0.22012518111602813, 0.19656845779176157, 0.19382850815365707, 0.1696081835555902, 0.20628996402641617, 0.22066144723416262, 0.18723260318494955, 0.1734823696904185, 0.1851579376641123, 0.19253412105366194, 0.1870932076511039, 0.18953753778684768, 0.19142512720894242, 0.20667309138905826, 0.1905553776104408, 0.1680058361011434, 0.1401256074363731, 0.17323891833553318, 0.1818870509121853, 0.17080833142143617, 0.17465503111297095, 0.19446294014069693, 0.19050994083683317, 0.1920789118958522, 0.18976275833750772, 0.148626210735756, 0.19210864213043496, 0.1951604787577474, 0.1314062344862868, 0.10687542526856779, 0.1021180267875023, 0.13328204400411287, 0.11499458404877554, 0.12137256078851313, 0.12613893443473567, 0.14297550284765162, 0.14187941080284536, 0.13693297290565923, 0.09678088873207281, 0.13028518026338745, 0.1403213782263615, 0.08565196441122636, 0.08087437180855749, 0.09579165724741781, 0.12678935343471157, 0.1302647056678729, 0.13266948898214334, 0.14201880633669098, 0.16220675751403632, 0.1683036993947934, 0.16785858729778533, 0.19302915750685617, 0.20303674713089212, 0.21109279928108926, 0.19962814768858644, 0.19046983306753756, 0.21034729962523077, 0.22196200476020322, 0.1882294074652058, 0.17381080464038476, 0.1735286478857596, 0.18051020448230967, 0.18795005544969223, 0.18622261663086884, 0.1546544365082793, 0.19392919828776287, 0.19345351453443854, 0.16446288965539294, 0.1526801806476367, 0.16799545856643053, 0.18739303426213205, 0.1902850007600843, 0.20234341562250904, 0.2028785598449989, 0.21589226884882848, 0.22802500929771016, 0.2164035727888699, 0.21884117155074587, 0.22616546726673126, 0.22850630252925763, 0.1899602119709631, 0.17639256699259606, 0.19070234593988772, 0.20399232174620813, 0.19879990822893626, 0.19958579612800156, 0.2068963486223402, 0.21889838822862215, 0.23003208060595828, 0.22411604439789823, 0.21000287766232847, 0.2229170184276969, 0.2241948575669337, 0.1992293137869195, 0.1847994920056521, 0.1888293411611732, 0.20549734475348305, 0.1977750565575642, 0.18907615820299242, 0.1728939354248086, 0.2023518298398438, 0.21571192412395376, 0.1980574937861005, 0.1963409934498123, 0.21276638710897028, 0.20118365599986987, 0.17854408236509065, 0.17386100947048208, 0.16932855106616548, 0.17786421360444316, 0.16006421730669879, 0.1444118097466928, 0.13980951333849778, 0.15589581403906552, 0.15045658347997445, 0.1573870938246937, 0.14941770811304445, 0.15499661467989231, 0.16429039820003064, 0.1333824536643075, 0.13068934316936642, 0.14271494258418566, 0.13317882960480665, 0.1323396516626213, 0.13832131876589235, 0.13811376807163528, 0.12474105246152319, 0.12147381187044125, 0.1191192333862681, 0.12281587953533327, 0.13099505973252887, 0.10032103043871168, 0.10640815573257822, 0.10597875017459502, 0.12178850359876076, 0.1059941762397087, 0.10375627490257738, 0.0897754918530743, 0.1061372179343994, 0.10674135873903418, 0.09016619201131769, 0.0900318450078729, 0.0879810197694841, 0.061379471665684075, 0.06901341057958812, 0.05128409370745561, 0.06455359491826149, 0.05080925137586477, 0.048995426592404655, 0.045587668571832454, 0.05825555324320398, 0.05343785287123948, 0.050125736454372224, 0.06750950946795782, 0.08466694003523874, 0.07774035632527569, 0.053460290784132135, 0.05662880455848638, 0.055857501302801316, 0.061206699736410616, 0.025315575221139655, 0.011902471367820779, 0.009701031639140023, 0.01970749936753133, 0.025056136853318315, 0.030879055722873826, 0.03379598439891916, 0.04256808644430321, 0.05498494696518814, 0.0236046843630746, 0.027261783690666445, 0.022133598699049812, 0.03219756357922855, 0.028830193801863132, 0.035421891661903286, 0.02165539068052507, 0.04145740975611672, 0.03925961618828101, 0.032445502516692405, 0.029887299973018408, 0.01895526833780502, 0.021125855936258375, 0, 0.012577572071978581, 0.029592241418479978, 0.04065104726153687, 0.024024553808078435, 0.01990327015751976, 0.03170084428256737, 0.04382208530109155, 0.05023427985799045, 0.04354750134206767, 0.05484330764005325, 0.06544830669485617, 0.08755778463754636, 0.07796557687593574, 0.08617869441638147, 0.08908272129251353, 0.0707711405808166, 0.0704463517916954, 0.08258245792751095, 0.0947999014975624, 0.08611530731245971, 0.09382805939539922, 0.11414811378490007, 0.13737023173315488, 0.22897665727826993, 0.3628642444565734, 0.44699884500843384, 0.47819539719873877, 0.565331068599992, 0.5938544239430481, 0.5873322836129752, 0.5879927996737527, 0.6446370302973529, 0.662140565671003, 0.6478731382842962, 0.637007018018205, 0.6190151775652284, 0.585461803099461, 0.5774528705663385, 0.5352948369801486, 0.520260032972513, 0.49851432969259496, 0.4926215728191611, 0.4832635607733675, 0.5075545647970463, 0.5293294373637247, 0.5459093721479309, 0.5478704457347491, 0.5431335218491982, 0.5664439890794678, 0.5487932049024596, 0.5639087853965088, 0.5196331737810744, 0.5559419239500599, 0.5865104950532817, 0.5816504431207322, 0.581942416462248, 0.5972282446203702, 0.6148880039625354, 0.6421166917316852, 0.6501441355429443, 0.6347455568725364, 0.65393053334358, 0.656123558854926, 0.6747324419124506, 0.6516962781672937, 0.6463683957509324, 0.6527749808296082, 0.6600838504804799, 0.6631668197119308, 0.6606327379246165, 0.6834952883187665, 0.7208936796326016, 0.765293260716768, 0.7621673789769097, 0.801975321661505, 0.892459290614165, 0.9078239319413226, 0.8409561467820947, 0.9158373520570237, 0.9460514041365414, 0.9250576514124386, 0.9113337824655564, 0.9142125666896842, 0.9314701264432487, 0.932119704021491, 0.9080836507830551, 0.9071059187287576, 0.9371519669354915, 0.9364639644314204, 0.8682398074378316, 0.7710704623388046, 0.8714388928685021, 0.9021785530575303, 0.9173847266248836, 0.9109383142508233, 0.9245331651985728, 0.9488135112136274, 0.9611913858608614, 0.9684520139990138, 0.9702778991606538, 0.9899478150240899, 0.9849980114399699, 0.963874679768912, 0.9410934668089979, 0.9575009101378417, 0.9912901631628931, 0.966145957501471, 0.9484735768333037, 0.9576293671891521, 0.9710564947380289, 0.9718942703106586, 1, 0.9944081916332389, 0.9916528159300206, 0.9967967074606622, 0.9518005583674624, 0.9357047215539153, 0.9308441086735435, 0.903135530042402, 0.8715289249939838, 0.8731587588917241, 0.8525994602560054, 0.8623391972948852, 0.8649226424905635, 0.8536787238661422, 0.9050309727340092, 0.9251748895073026, 0.9119951399480675, 0.87365660008403, 0.8759334872948124, 0.8974744446195848, 0.8793134783981799, 0.7481720112840264, 0.7499022548419614, 0.7461971944755614, 0.7380373669782357, 0.7351305353629921, 0.7398413751748054, 0.7537497959552296, 0.7330264200814833, 0.7080168418974172, 0.6890200634207608, 0.6868640604656877, 0.7142935675552267, 0.7342153489908829, 0.7101234814441265, 0.7218601927304528, 0.741484110872459, 0.7448767233018286, 0.7365570256751428, 0.732137878730934, 0.6763286189408519, 0.7188720236809732, 0.693206136544797, 0.6447051854577642, 0.6391546067559434, 0.6662817629692539, 0.6671321598678855, 0.6493335659396969, 0.6677744451244378, 0.6528481845204205, 0.6599371626249441, 0.6901969319519806, 0.6961115657904848, 0.6559608839864542, 0.6831819989600028, 0.6937098676892371, 0.6592839388858567, 0.6444415399812755, 0.6589470897185556, 0.6725301607620364, 0.6708254403300168, 0.6356130626797487, 0.6456209327776958, 0.6462343292213988, 0.6636124927567613, 0.6291046655713226, 0.6371071472044885, 0.6320990050468476, 0.7634530713856589, 0.7481436834189994, 0.7556309344773677, 0.7504845186815258, 0.7390476340062276, 0.6858442573247164, 0.6762882306976452, 0.6894648950438577, 0.704974260909173, 0.7217381865790989, 0.7178045399751052, 0.7026278161684235, 0.7256502366919336, 0.7291971097724403, 0.6964629996011661, 0.685467580862031, 0.6853682930974809, 0.6773843227424514, 0.6905004047238538, 0.6759286631435404, 0.6890988765897963, 0.690809206500039, 0.6865235651375416, 0.6714548237866559, 0.6747560017209879, 0.7021294140282953, 0.7037822467867506, 0.6566303752123889, 0.6614396613670186, 0.6810714327785372, 0.6991603172047746, 0.680601078029525, 0.6844904097555558, 0.7013589521943437, 0.7115216438912502, 0.746207011062452, 0.7375364405729072, 0.7213505716338783, 0.7484892272775463, 0.7685534894600708, 0.7434297583941635, 0.7341749607476762, 0.7390669867060976, 0.7576133240814339, 0.7705611217161413, 0.7475981616617967, 0.7209637981103911, 0.7581282741823204, 0.751642595460698, 0.7465595667687779, 0.7502155442007251, 0.7764620123329988, 0.7635574076806098, 0.7403829702972519, 0.7357428099110505, 0.754074865218262, 0.7712813787199956, 0.6909233593818804, 0.6863970714036093, 0.6892497715539994, 0.6914004455047604, 0.7164520947755003, 0.7032193356470561, 0.708807217379061, 0.7264296736910423, 0.7178690489746715, 0.670520845662499, 0.6866236943238251, 0.6910843514068852, 0.6829278895964934, 0.6774984756242929, 0.6435779608088195, 0.698834967467831, 0.6872023120025444]}, {"name": "S&P 500 Index", "type": "scatter", "x": ["2014-08-27", "2014-09-03", "2014-09-10", "2014-09-17", "2014-09-24", "2014-10-01", "2014-10-08", "2014-10-15", "2014-10-22", "2014-10-29", "2014-11-05", "2014-11-12", "2014-11-19", "2014-11-26", "2014-12-03", "2014-12-10", "2014-12-17", "2014-12-24", "2014-12-31", "2015-01-07", "2015-01-14", "2015-01-21", "2015-01-28", "2015-02-04", "2015-02-11", "2015-02-18", "2015-02-25", "2015-03-04", "2015-03-11", "2015-03-18", "2015-03-25", "2015-04-01", "2015-04-08", "2015-04-15", "2015-04-22", "2015-04-29", "2015-05-06", "2015-05-13", "2015-05-20", "2015-05-27", "2015-06-03", "2015-06-10", "2015-06-17", "2015-06-24", "2015-07-01", "2015-07-08", "2015-07-15", "2015-07-22", "2015-07-29", "2015-08-05", "2015-08-12", "2015-08-19", "2015-08-26", "2015-09-02", "2015-09-09", "2015-09-16", "2015-09-23", "2015-09-30", "2015-10-07", "2015-10-14", "2015-10-21", "2015-10-28", "2015-11-04", "2015-11-11", "2015-11-18", "2015-11-25", "2015-12-02", "2015-12-09", "2015-12-16", "2015-12-23", "2015-12-30", "2016-01-06", "2016-01-13", "2016-01-20", "2016-01-27", "2016-02-03", "2016-02-10", "2016-02-17", "2016-02-24", "2016-03-02", "2016-03-09", "2016-03-16", "2016-03-23", "2016-03-30", "2016-04-06", "2016-04-13", "2016-04-20", "2016-04-27", "2016-05-04", "2016-05-11", "2016-05-18", "2016-05-25", "2016-06-01", "2016-06-08", "2016-06-15", "2016-06-22", "2016-06-29", "2016-07-06", "2016-07-13", "2016-07-20", "2016-07-27", "2016-08-03", "2016-08-10", "2016-08-17", "2016-08-24", "2016-08-31", "2016-09-07", "2016-09-14", "2016-09-21", "2016-09-28", "2016-10-05", "2016-10-12", "2016-10-19", "2016-10-26", "2016-11-02", "2016-11-09", "2016-11-16", "2016-11-23", "2016-11-30", "2016-12-07", "2016-12-14", "2016-12-21", "2016-12-28", "2017-01-04", "2017-01-11", "2017-01-18", "2017-01-25", "2017-02-01", "2017-02-08", "2017-02-15", "2017-02-22", "2017-03-01", "2017-03-08", "2017-03-15", "2017-03-22", "2017-03-29", "2017-04-05", "2017-04-12", "2017-04-19", "2017-04-26", "2017-05-03", "2017-05-10", "2017-05-17", "2017-05-24", "2017-05-31", "2017-06-07", "2017-06-14", "2017-06-21", "2017-06-28", "2017-07-05", "2017-07-12", "2017-07-19", "2017-07-26", "2017-08-02", "2017-08-09", "2017-08-16", "2017-08-23", "2017-08-30", "2017-09-06", "2017-09-13", "2017-09-20", "2017-09-27", "2017-10-04", "2017-10-11", "2017-10-18", "2017-10-25", "2017-11-01", "2017-11-08", "2017-11-15", "2017-11-22", "2017-11-29", "2017-12-06", "2017-12-13", "2017-12-20", "2017-12-27", "2018-01-03", "2018-01-10", "2018-01-17", "2018-01-24", "2018-01-31", "2018-02-07", "2018-02-14", "2018-02-21", "2018-02-28", "2018-03-07", "2018-03-14", "2018-03-21", "2018-03-28", "2018-04-04", "2018-04-11", "2018-04-18", "2018-04-25", "2018-05-02", "2018-05-09", "2018-05-16", "2018-05-23", "2018-05-30", "2018-06-06", "2018-06-13", "2018-06-20", "2018-06-27", "2018-07-11", "2018-07-18", "2018-07-25", "2018-08-01", "2018-08-08", "2018-08-15", "2018-08-22", "2018-08-29", "2018-09-05", "2018-09-12", "2018-09-19", "2018-09-26", "2018-10-03", "2018-10-10", "2018-10-17", "2018-10-24", "2018-10-31", "2018-11-07", "2018-11-14", "2018-11-21", "2018-11-28", "2018-12-12", "2018-12-19", "2018-12-26", "2019-01-02", "2019-01-09", "2019-01-16", "2019-01-23", "2019-01-30", "2019-02-06", "2019-02-13", "2019-02-20", "2019-02-27", "2019-03-06", "2019-03-13", "2019-03-20", "2019-03-27", "2019-04-03", "2019-04-10", "2019-04-17", "2019-04-24", "2019-05-01", "2019-05-08", "2019-05-15", "2019-05-22", "2019-05-29", "2019-06-05", "2019-06-12", "2019-06-19", "2019-06-26", "2019-07-03", "2019-07-10", "2019-07-17", "2019-07-24", "2019-07-31", "2019-08-07", "2019-08-14", "2019-08-21", "2019-08-28", "2019-09-04", "2019-09-11", "2019-09-18", "2019-09-25", "2019-10-02", "2019-10-09", "2019-10-16", "2019-10-23", "2019-10-30", "2019-11-06", "2019-11-13", "2019-11-20", "2019-11-27", "2019-12-04", "2019-12-11", "2019-12-18", "2020-01-08", "2020-01-15", "2020-01-22", "2020-01-29", "2020-02-05", "2020-02-12", "2020-02-19", "2020-02-26", "2020-03-04", "2020-03-11", "2020-03-18", "2020-03-25", "2020-04-01", "2020-04-08", "2020-04-15", "2020-04-22", "2020-04-29", "2020-05-06", "2020-05-13", "2020-05-20", "2020-05-27", "2020-06-03", "2020-06-10", "2020-06-17", "2020-06-24", "2020-07-01", "2020-07-08", "2020-07-15", "2020-07-22", "2020-07-29", "2020-08-05", "2020-08-12", "2020-08-19", "2020-08-26", "2020-09-02", "2020-09-09", "2020-09-16", "2020-09-23", "2020-09-30", "2020-10-07", "2020-10-14", "2020-10-21", "2020-10-28", "2020-11-04", "2020-11-11", "2020-11-18", "2020-11-25", "2020-12-02", "2020-12-09", "2020-12-16", "2020-12-23", "2020-12-30", "2021-01-06", "2021-01-13", "2021-01-20", "2021-01-27", "2021-02-03", "2021-02-10", "2021-02-17", "2021-02-24", "2021-03-03", "2021-03-10", "2021-03-17", "2021-03-24", "2021-03-31", "2021-04-07", "2021-04-14", "2021-04-21", "2021-04-28", "2021-05-05", "2021-05-12", "2021-05-19", "2021-05-26", "2021-06-02", "2021-06-09", "2021-06-16", "2021-06-23", "2021-06-30", "2021-07-07", "2021-07-14", "2021-07-21", "2021-07-28", "2021-08-04", "2021-08-11", "2021-08-18", "2021-08-25", "2021-09-01", "2021-09-08", "2021-09-15", "2021-09-22", "2021-09-29", "2021-10-06", "2021-10-13", "2021-10-20", "2021-10-27", "2021-11-03", "2021-11-10", "2021-11-17", "2021-11-24", "2021-12-01", "2021-12-08", "2021-12-15", "2021-12-22", "2021-12-29", "2022-01-05", "2022-01-12", "2022-01-19", "2022-01-26", "2022-02-02", "2022-02-09", "2022-02-16", "2022-02-23", "2022-03-02", "2022-03-09", "2022-03-16", "2022-03-23", "2022-03-30", "2022-04-06", "2022-04-13", "2022-04-20", "2022-04-27", "2022-05-04", "2022-05-11", "2022-05-18", "2022-05-25", "2022-06-01", "2022-06-08", "2022-06-15", "2022-06-22", "2022-06-29", "2022-07-06", "2022-07-13", "2022-07-20", "2022-07-27", "2022-08-03", "2022-08-10", "2022-08-17", "2022-08-24", "2022-08-31", "2022-09-07", "2022-09-14", "2022-09-21", "2022-09-28", "2022-10-05", "2022-10-12", "2022-10-19", "2022-10-26", "2022-11-02", "2022-11-09", "2022-11-16", "2022-11-23", "2022-11-30", "2022-12-07", "2022-12-14", "2022-12-21", "2022-12-28", "2023-01-04", "2023-01-11", "2023-01-18", "2023-01-25", "2023-02-01", "2023-02-08", "2023-02-15", "2023-02-22", "2023-03-01", "2023-03-08", "2023-03-15", "2023-03-22", "2023-03-29", "2023-04-05", "2023-04-12", "2023-04-19", "2023-04-26", "2023-05-03", "2023-05-10", "2023-05-17", "2023-05-24", "2023-05-31", "2023-06-07", "2023-06-14", "2023-06-21", "2023-06-28", "2023-07-05", "2023-07-12", "2023-07-19", "2023-07-26", "2023-08-02", "2023-08-09", "2023-08-16", "2023-08-23", "2023-08-30", "2023-09-06", "2023-09-13", "2023-09-20", "2023-09-27", "2023-10-04", "2023-10-11", "2023-10-18", "2023-10-25", "2023-11-01", "2023-11-08", "2023-11-15", "2023-11-22", "2023-11-29", "2023-12-06", "2023-12-13", "2023-12-20", "2023-12-27", "2024-01-03", "2024-01-10", "2024-01-17", "2024-01-24", "2024-01-31", "2024-02-07", "2024-02-14", "2024-02-21", "2024-02-28", "2024-03-06", "2024-03-13", "2024-03-20", "2024-03-27", "2024-04-03", "2024-04-10", "2024-04-17", "2024-04-24", "2024-05-01", "2024-05-08", "2024-05-15", "2024-05-22", "2024-05-29", "2024-06-05", "2024-06-12", "2024-06-26", "2024-07-03", "2024-07-10", "2024-07-17", "2024-07-24", "2024-07-31", "2024-08-07", "2024-08-14"], "y": [0.03920096244100421, 0.03935960656257853, 0.0380296400100475, 0.0395843524014754, 0.03871974193889559, 0.02493356777409082, 0.030943535913063074, 0.002810645020557663, 0.019896616914107428, 0.03448923203024816, 0.04540130352586561, 0.04928279636704964, 0.05205113628852075, 0.05842598590711387, 0.058822596211049565, 0.04608082917994215, 0.04257743816184349, 0.06081886807419262, 0.05474279821789775, 0.04601737153131243, 0.042149099033592916, 0.04766198225829907, 0.03974035245435681, 0.05014476276093655, 0.05728903636916495, 0.06552531034756282, 0.06927459975410166, 0.06522124244787887, 0.049808966036937664, 0.06547771711109057, 0.05531127298687227, 0.05495167964463721, 0.060824156211578426, 0.0673629380891316, 0.0677145992252879, 0.06742110760037545, 0.060361444190320114, 0.0652080221044143, 0.07244483811689427, 0.07181819383667591, 0.06933012519665267, 0.06698483626604616, 0.06572625956822362, 0.06787853148424797, 0.05963961343715714, 0.05151174627516827, 0.06756653137848526, 0.0693512777461959, 0.06787588741555513, 0.06556761544664937, 0.06192144471913388, 0.060218664480903274, 0.02343966896259967, 0.02564746632117502, 0.023844211472614073, 0.037929165399717094, 0.02297695694134136, 0.01802461627953096, 0.03806665697174813, 0.037646250049576314, 0.04417709972105079, 0.0630583942570828, 0.06622070041379675, 0.058999748813474195, 0.061268359751986363, 0.0626670720905329, 0.06019222379397425, 0.05176028873230126, 0.05848944355574365, 0.056167951243373315, 0.05592205285493323, 0.03659391070980026, 0.010158511918139651, 0.0019751193135997746, 0.00822040956624057, 0.01604156475985248, 0, 0.019819938922013204, 0.02060787139249879, 0.03558652053780361, 0.0363295038405098, 0.04636638859877583, 0.048875609788342336, 0.05607805290781452, 0.05679459552359169, 0.06096164778360946, 0.06624449703203295, 0.06432754722967707, 0.052685712774817886, 0.05621290041115271, 0.05176293280099422, 0.063108631562248, 0.06543276794331117, 0.07066537988656944, 0.05807432477095757, 0.0617628005975595, 0.05788130775637553, 0.06553853069102739, 0.0794727727026348, 0.08491691014132549, 0.08321412990309489, 0.0824764347377745, 0.08556999510847288, 0.0873494533387977, 0.08555677476500843, 0.08436958792189418, 0.08839121640380215, 0.07242368556735106, 0.08229928213534987, 0.0844806388069962, 0.08140294284845523, 0.07596938168453614, 0.07732050078661044, 0.07603548340185876, 0.06506524239499746, 0.08233629909705062, 0.08595338506894412, 0.09329860789783316, 0.09173596330032655, 0.10298383151994289, 0.1061382054705782, 0.10928464721513463, 0.1052497983897622, 0.11075739347708255, 0.11196573286974001, 0.11105881730807365, 0.11806031120688515, 0.11308417392683869, 0.11708200579051048, 0.131513332716384, 0.13510133393265564, 0.14386377758094157, 0.13514363903174206, 0.14103462407953365, 0.13130180722095158, 0.13465448632355473, 0.13249163813275866, 0.13037109504104916, 0.12858370460464566, 0.1416136751232797, 0.14179347179439727, 0.1448341507912376, 0.13357041815946386, 0.14609272748906016, 0.14805198239050257, 0.153694424981161, 0.15495828981636947, 0.1543475099483085, 0.15569069684430406, 0.15353578085958675, 0.15636757842968763, 0.16445314049259, 0.16551076796975187, 0.1654420221837364, 0.1645033777977552, 0.1629407332002486, 0.15657645985642707, 0.16015917293531293, 0.16226120754617207, 0.17094168506497798, 0.17355138086487484, 0.17323409262172632, 0.18135138350894353, 0.18597850372152663, 0.18757023307465537, 0.1864835208418715, 0.19235599740881273, 0.1963273885855555, 0.18845864015547123, 0.19704128713263971, 0.20470644227337031, 0.20555254425509975, 0.21443132692587352, 0.21876759958223715, 0.219658650731746, 0.22770719583294774, 0.2370063854258934, 0.2513716106344443, 0.26062056292222474, 0.25699025660686664, 0.21940482013722715, 0.22389180470908637, 0.2246057032561706, 0.2279107891223014, 0.23134014621699878, 0.23733689401250646, 0.2274084160706495, 0.1991353895374202, 0.20962969817955873, 0.20896868100633256, 0.22653851747068388, 0.20823098584101218, 0.20724474821855876, 0.22366970293888236, 0.23019262040427813, 0.23305614679869383, 0.2306024510516784, 0.24338387911317935, 0.24425113364445214, 0.24205391256064837, 0.22415621157837684, 0.24382543858489444, 0.2548247643473777, 0.26287595351727244, 0.2542272048227813, 0.26595100540712047, 0.2555518832379265, 0.26704036170859724, 0.2808476884229452, 0.2741211776681958, 0.2742057878663688, 0.27923745058896626, 0.27871392498777114, 0.2838804352137069, 0.24690842268082122, 0.2531299163152259, 0.2126465805581629, 0.22735817876548428, 0.25436734046350523, 0.2246718049734932, 0.2110151901746407, 0.2358324189262437, 0.21131661400563193, 0.1732129400721831, 0.16283232638383943, 0.17402466916090487, 0.1938366758768393, 0.20207030578654434, 0.2080459010325088, 0.21924353194696006, 0.2326119432582859, 0.23827553839848767, 0.24664930394891657, 0.2486799487050674, 0.24314591293081791, 0.2535820520617126, 0.25710130749196863, 0.25211459393715047, 0.2701021932549808, 0.2740180589891726, 0.2772543990692878, 0.2843405031662723, 0.28340979098636987, 0.2716939226081094, 0.26416890310810276, 0.2655729035840351, 0.24620510040850863, 0.25760896868100636, 0.2718049734932114, 0.2841316217395328, 0.28077894263692976, 0.3024708821935194, 0.30174376330297065, 0.29945664388360815, 0.308747901270475, 0.29838844013167465, 0.2728996179320739, 0.26142964794225354, 0.28359487579487314, 0.27394666913446414, 0.28712470749990093, 0.3038220012955936, 0.30535556113747836, 0.2995756269747888, 0.2738594148675983, 0.28226490924234215, 0.3008500680847689, 0.3047712219563464, 0.31594241218386854, 0.32387726233127545, 0.3284409248952288, 0.3322536719503973, 0.3441969302362476, 0.33339062148834636, 0.34102404780476203, 0.3541148319033328, 0.37048426118110556, 0.3800663661241919, 0.3886490131013604, 0.37586494097116646, 0.392070437989979, 0.4039052894594201, 0.4056768154836663, 0.33435041842387064, 0.3379807247392287, 0.23519519837125372, 0.14442960828122314, 0.1649105643764625, 0.16357266561785277, 0.2374690974471517, 0.2462949987440674, 0.25051228830925026, 0.2875821313837734, 0.263497309660105, 0.25598286643487, 0.2960695918879973, 0.31312912309461804, 0.33606377493687284, 0.35385042503404235, 0.33358363850292827, 0.3168837006385426, 0.33421028278314674, 0.34850940627437504, 0.36348012321360107, 0.37655768696870745, 0.37190941420658113, 0.390240742454489, 0.40414325564178155, 0.402689017860684, 0.43015560344257747, 0.45715418886582676, 0.40906386747927714, 0.4055023069499345, 0.36621937837945034, 0.39955579645959205, 0.41448156423103866, 0.4327838077233247, 0.4187411588953081, 0.3752382966909481, 0.420824685025317, 0.4549913406750307, 0.45370367922158616, 0.47005988815589433, 0.480466942531167, 0.4814743327031637, 0.4889702674475483, 0.4860194867862668, 0.4971325075025449, 0.5013894580981214, 0.5177033619333431, 0.5288110945122354, 0.5020848481643553, 0.5230787535860182, 0.5441546251371612, 0.5498261524834416, 0.5482661519546277, 0.5203157018019328, 0.5412276410941156, 0.5611401224203805, 0.5386708266680768, 0.5608149019711531, 0.5891223013973903, 0.600943932523367, 0.61383641146997, 0.616417022514245, 0.6122949194220066, 0.5846511812376886, 0.5985695588371387, 0.6198040745098558, 0.6230113298343491, 0.6260335003503392, 0.6271307888578945, 0.6319271294668236, 0.6466440158115309, 0.6626750042966117, 0.6669504633730385, 0.6628230721434142, 0.6739149403101494, 0.674449042186116, 0.6863579275789585, 0.673817109768512, 0.699179016670853, 0.7065559683240572, 0.7039066114937665, 0.6950833542655438, 0.672592905963697, 0.663026665432768, 0.664108089528166, 0.6641741912454887, 0.7097552914424716, 0.7138509538477811, 0.7426422178448195, 0.7389775386364539, 0.7500720508718817, 0.7534538147301068, 0.7036342724183975, 0.7533877130127842, 0.7556721883634538, 0.7521582210705836, 0.7776734839571133, 0.7532211366851311, 0.7600349017067465, 0.7088483758808055, 0.66050686796843, 0.7238190928200315, 0.7232373977075925, 0.6935788791792812, 0.6276067212226174, 0.6701868034531537, 0.641456353036052, 0.6626036144419032, 0.688615962242699, 0.7272748906016578, 0.6952023373567244, 0.6860644359540462, 0.6894647082931215, 0.6166232598722915, 0.6473499821525364, 0.5508441189302097, 0.5478034399333693, 0.5623590380878095, 0.5947488795758913, 0.598593355455375, 0.5124548855779273, 0.5044962388122843, 0.5200803796882643, 0.527021060007139, 0.515572242566862, 0.55738025673907, 0.5742256183815655, 0.6090109861054191, 0.6235718723972449, 0.6404410306579765, 0.6052035271876365, 0.5560846630795468, 0.5626604619188007, 0.5537076453246257, 0.5124390211657699, 0.49369521820176887, 0.5106807154849884, 0.4561467986938301, 0.48738118216311255, 0.5231924485398131, 0.5044433574384263, 0.5015031530519163, 0.5570867651141577, 0.5751907034544759, 0.5891646064964767, 0.5505109662749039, 0.5667455480493383, 0.5358416731666689, 0.5106648510728308, 0.5291072302058407, 0.5599476474398805, 0.549173067516294, 0.5722716516175089, 0.599502915085734, 0.5991459658121918, 0.6070094261048904, 0.5656165307174682, 0.5551301542814081, 0.565870361311987, 0.5394085218333972, 0.5513174072262397, 0.5753361272325854, 0.59188006504409, 0.5922951838288759, 0.6088391216403803, 0.5827871128091908, 0.5919778955857273, 0.6043759336867573, 0.6099628508348649, 0.5984532198146508, 0.615531259502122, 0.6387170978702028, 0.6664983276265518, 0.6646739202284475, 0.667627344958422, 0.6861252495339829, 0.6928253196018033, 0.7175632262926191, 0.7178355653679883, 0.7037268148226492, 0.6916487090334608, 0.6748906016578311, 0.6832670112769531, 0.704118136989199, 0.6910590817149429, 0.6915773191787522, 0.6743274150262424, 0.6405653018865431, 0.6377202839729776, 0.6676511415766582, 0.6511653732763979, 0.6173662431749979, 0.6308747901270475, 0.6691926336246216, 0.7009478986264064, 0.7151571237820759, 0.7135601062915615, 0.7132322417736414, 0.7549424254042121, 0.7526315093666135, 0.7746380930976587, 0.7543395777422299, 0.7751325339432319, 0.7634351740458217, 0.7976335585198504, 0.7915786412130986, 0.8310836715537872, 0.832553773747042, 0.8275776364669956, 0.8508348646897848, 0.860089105114951, 0.8760989410504887, 0.891780912468106, 0.8980923044380693, 0.8883092502743221, 0.8748641609709021, 0.8382623180550232, 0.8513293055353579, 0.8372522838143336, 0.8820110786478234, 0.9138668182599383, 0.9135653944289474, 0.9029732552451712, 0.9259978054229848, 0.943713065665446, 0.9587498843219946, 0.974381618434447, 1, 0.9879324704855834, 0.9453259475681179, 0.9704895493184914, 0.8851390119115295, 0.9527504924577941]}], "layout": {"autosize": true, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "USD Liquidity Index vs. S&P 500 Index (Normalized)", "x": 0.5, "y": 0.9}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = go.Figure()\n", "\n", "fig.add_scatter(\n", "    x=normalized.index, y=normalized[\"USD Liquidity Index\"], name=\"USD Liquidity Index\"\n", ")\n", "\n", "fig.add_scatter(x=normalized.index, y=normalized[\"SP500\"], name=\"S&P 500 Index\")\n", "\n", "fig.update_layout(\n", "    title=\"USD Liquidity Index vs. S&P 500 Index (Normalized)\",\n", "    title_y=0.90,\n", "    title_x=0.5,\n", "    autosize=True,\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The combinations are endless and we love seeing your creations, tag us on social media with your custom indexes and indicators."]}], "metadata": {"kernelspec": {"display_name": "obb", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}