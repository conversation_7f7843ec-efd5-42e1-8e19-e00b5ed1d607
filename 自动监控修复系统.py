#!/usr/bin/env python3
"""
OpenBB项目自动监控修复系统
自动检测、修复问题并重启服务
"""

import subprocess
import time
import requests
import psutil
import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import threading
import signal


class OpenBBMonitor:
    """OpenBB项目监控器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.python_exe = self.project_root / "openbb_env_312" / "Scripts" / "python.exe"
        self.services = {
            "astock_api": {
                "port": 6901,
                "script": "openbb_astock_api.py",
                "args": ["--host", "127.0.0.1", "--port", "6901"],
                "health_url": "http://127.0.0.1:6901/health",
                "process": None,
                "restart_count": 0,
                "last_restart": None
            },
            "free_api": {
                "port": 6902,
                "script": "openbb_free_api_server.py",
                "args": [],
                "health_url": "http://127.0.0.1:6902/health",
                "process": None,
                "restart_count": 0,
                "last_restart": None
            },
            "streamlit": {
                "port": 8502,
                "script": None,
                "args": ["-m", "streamlit", "run", "OpenBB完整客户端.py", "--server.port", "8502"],
                "health_url": "http://localhost:8502",
                "process": None,
                "restart_count": 0,
                "last_restart": None
            }
        }
        
        self.setup_logging()
        self.running = True
        self.check_interval = 30  # 30秒检查一次
        self.max_restarts = 5  # 最大重启次数
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('openbb_monitor.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def check_port_available(self, port: int) -> bool:
        """检查端口是否可用"""
        for conn in psutil.net_connections():
            if conn.laddr.port == port:
                return False
        return True
    
    def kill_process_on_port(self, port: int) -> bool:
        """杀死占用端口的进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'connections']):
                try:
                    for conn in proc.info['connections'] or []:
                        if conn.laddr.port == port:
                            self.logger.info(f"杀死占用端口{port}的进程: {proc.info['name']} (PID: {proc.info['pid']})")
                            proc.kill()
                            time.sleep(2)
                            return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            self.logger.error(f"杀死端口{port}进程失败: {e}")
        return False
    
    def check_service_health(self, service_name: str) -> bool:
        """检查服务健康状态"""
        service = self.services[service_name]
        
        try:
            response = requests.get(service["health_url"], timeout=5)
            if response.status_code == 200:
                return True
        except requests.exceptions.RequestException:
            pass
        
        return False
    
    def start_service(self, service_name: str) -> bool:
        """启动服务"""
        service = self.services[service_name]
        
        try:
            # 检查端口是否被占用
            if not self.check_port_available(service["port"]):
                self.logger.warning(f"端口{service['port']}被占用，尝试清理...")
                self.kill_process_on_port(service["port"])
                time.sleep(3)
            
            # 构建启动命令
            if service["script"]:
                cmd = [str(self.python_exe), service["script"]] + service["args"]
            else:
                cmd = [str(self.python_exe)] + service["args"]
            
            self.logger.info(f"启动{service_name}: {' '.join(cmd)}")
            
            # 启动进程
            process = subprocess.Popen(
                cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )
            
            service["process"] = process
            service["last_restart"] = datetime.now()
            service["restart_count"] += 1
            
            # 等待服务启动
            time.sleep(10)
            
            # 检查服务是否正常启动
            if self.check_service_health(service_name):
                self.logger.info(f"✅ {service_name} 启动成功")
                return True
            else:
                self.logger.error(f"❌ {service_name} 启动失败 - 健康检查未通过")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 启动{service_name}失败: {e}")
            return False
    
    def stop_service(self, service_name: str) -> bool:
        """停止服务"""
        service = self.services[service_name]
        
        try:
            if service["process"] and service["process"].poll() is None:
                self.logger.info(f"停止{service_name}...")
                
                if os.name == 'nt':
                    # Windows
                    service["process"].send_signal(signal.CTRL_BREAK_EVENT)
                else:
                    # Unix/Linux
                    service["process"].terminate()
                
                # 等待进程结束
                try:
                    service["process"].wait(timeout=10)
                except subprocess.TimeoutExpired:
                    service["process"].kill()
                    service["process"].wait()
                
                service["process"] = None
                self.logger.info(f"✅ {service_name} 已停止")
                return True
            
        except Exception as e:
            self.logger.error(f"❌ 停止{service_name}失败: {e}")
        
        return False
    
    def restart_service(self, service_name: str) -> bool:
        """重启服务"""
        service = self.services[service_name]
        
        # 检查重启次数限制
        if service["restart_count"] >= self.max_restarts:
            self.logger.error(f"❌ {service_name} 重启次数超过限制({self.max_restarts})")
            return False
        
        self.logger.info(f"🔄 重启{service_name}...")
        
        # 停止服务
        self.stop_service(service_name)
        time.sleep(5)
        
        # 启动服务
        return self.start_service(service_name)
    
    def check_and_fix_dependencies(self) -> bool:
        """检查并修复依赖问题"""
        try:
            # 检查Python环境
            if not self.python_exe.exists():
                self.logger.error(f"❌ Python环境不存在: {self.python_exe}")
                return False
            
            # 检查关键文件
            required_files = [
                "openbb_astock_api.py",
                "openbb_free_api_server.py", 
                "OpenBB完整客户端.py"
            ]
            
            for file in required_files:
                if not (self.project_root / file).exists():
                    self.logger.error(f"❌ 关键文件缺失: {file}")
                    return False
            
            # 检查依赖包
            try:
                result = subprocess.run(
                    [str(self.python_exe), "-c", "import pandas, numpy, requests, streamlit, fastapi"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode != 0:
                    self.logger.warning("⚠️ 依赖包检查失败，尝试重新安装...")
                    self.install_dependencies()
                    
            except subprocess.TimeoutExpired:
                self.logger.warning("⚠️ 依赖检查超时")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 依赖检查失败: {e}")
            return False
    
    def install_dependencies(self) -> bool:
        """安装依赖"""
        try:
            self.logger.info("📦 安装依赖包...")
            
            packages = [
                "pandas", "numpy", "requests", "streamlit", 
                "fastapi", "uvicorn", "yfinance", "plotly"
            ]
            
            for package in packages:
                result = subprocess.run(
                    [str(self.python_exe), "-m", "pip", "install", package],
                    capture_output=True,
                    text=True,
                    timeout=60
                )
                
                if result.returncode == 0:
                    self.logger.info(f"✅ {package} 安装成功")
                else:
                    self.logger.error(f"❌ {package} 安装失败: {result.stderr}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 安装依赖失败: {e}")
            return False
    
    def monitor_services(self):
        """监控所有服务"""
        self.logger.info("🚀 开始监控OpenBB服务...")
        
        # 首次启动所有服务
        for service_name in self.services:
            if not self.check_service_health(service_name):
                self.start_service(service_name)
        
        # 持续监控
        while self.running:
            try:
                self.logger.info("🔍 检查服务状态...")
                
                for service_name, service in self.services.items():
                    # 检查进程是否还在运行
                    if service["process"] and service["process"].poll() is not None:
                        self.logger.warning(f"⚠️ {service_name} 进程已退出")
                        service["process"] = None
                    
                    # 检查服务健康状态
                    if not self.check_service_health(service_name):
                        self.logger.warning(f"⚠️ {service_name} 健康检查失败")
                        
                        # 尝试重启
                        if self.restart_service(service_name):
                            self.logger.info(f"✅ {service_name} 重启成功")
                        else:
                            self.logger.error(f"❌ {service_name} 重启失败")
                    else:
                        self.logger.info(f"✅ {service_name} 运行正常")
                
                # 等待下次检查
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                self.logger.info("🛑 收到停止信号")
                self.running = False
                break
            except Exception as e:
                self.logger.error(f"❌ 监控过程出错: {e}")
                time.sleep(10)
    
    def stop_all_services(self):
        """停止所有服务"""
        self.logger.info("🛑 停止所有服务...")
        
        for service_name in self.services:
            self.stop_service(service_name)
    
    def get_status_report(self) -> Dict:
        """获取状态报告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "services": {}
        }
        
        for service_name, service in self.services.items():
            report["services"][service_name] = {
                "running": self.check_service_health(service_name),
                "port": service["port"],
                "restart_count": service["restart_count"],
                "last_restart": service["last_restart"].isoformat() if service["last_restart"] else None
            }
        
        return report


def main():
    """主函数"""
    monitor = OpenBBMonitor()
    
    def signal_handler(signum, frame):
        monitor.logger.info("🛑 收到停止信号，正在关闭...")
        monitor.running = False
        monitor.stop_all_services()
        sys.exit(0)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 检查依赖
        if not monitor.check_and_fix_dependencies():
            monitor.logger.error("❌ 依赖检查失败，退出")
            return
        
        # 开始监控
        monitor.monitor_services()
        
    except Exception as e:
        monitor.logger.error(f"❌ 监控系统异常: {e}")
    finally:
        monitor.stop_all_services()


if __name__ == "__main__":
    main()
