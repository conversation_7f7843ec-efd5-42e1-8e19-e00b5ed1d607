"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[2634],{72634:(t,e,l)=>{l.r(e),l.d(e,{default:()=>i});var n=l(66845),s=l(40937),a=l(40864);const r=t=>s.Z.sanitize(t,{USE_PROFILES:{html:!0},FORCE_BODY:!0});function i(t){let{element:e,width:l}=t;const{body:s}=e,[i,u]=(0,n.useState)(r(s)),d=(0,n.useRef)(null);return(0,n.useEffect)((()=>{r(s)!==i&&u(r(s))}),[s]),(0,n.useEffect)((()=>{var t,e;0===(null===(t=d.current)||void 0===t?void 0:t.clientHeight)&&1===(null===(e=d.current.parentElement)||void 0===e?void 0:e.childElementCount)&&d.current.parentElement.classList.add("empty-html")})),(0,a.jsx)(a.Fragment,{children:i&&(0,a.jsx)("div",{className:"stHtml","data-testid":"stHtml",ref:d,style:{width:l},dangerouslySetInnerHTML:{__html:i}})})}}}]);