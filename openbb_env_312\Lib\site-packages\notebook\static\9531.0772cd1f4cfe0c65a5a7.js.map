{"version": 3, "file": "9531.0772cd1f4cfe0c65a5a7.js?v=0772cd1f4cfe0c65a5a7", "mappings": ";;;;;;;AAAa;;AAEb,QAAQ,mBAAO,CAAC,KAAW;AAC3B,IAAI,IAAqC;AACzC,EAAE,SAAkB;AACpB,EAAE,yBAAmB;AACrB,EAAE,KAAK,UAkBN", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/react-dom/client.js"], "sourcesContent": ["'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n"], "names": [], "sourceRoot": ""}