(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[8536],{72394:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>W});var o=i(66845),s=i(70145),n=i(97365),a=i(62813),r=i.n(a),c=i(3717),h=i(25621),l=i(92627),p=i(88766),d=i(699),m=i(96260),g=i(24002),u=i(29378),w=i(80248),x=i(87072),b=i(12879),S=i(47203),k=i(61355),y=i(56264),v=i(19754),j=i(62622),f=i(63765),T=i(13005),V=i.n(T),C=i(82309),Z=i(40864);const E=t=>{let{error:e,width:i,deltaType:o}=t;return e instanceof z?(0,Z.jsx)(C.Z,{width:i,name:"No Mapbox token provided",message:(0,Z.jsxs)(Z.Fragment,{children:[(0,Z.jsxs)("p",{children:["To use ",(0,Z.jsxs)("code",{children:["st.",o]})," or ",(0,Z.jsx)("code",{children:"st.map"})," you need to set up a Mapbox access token."]}),(0,Z.jsxs)("p",{children:["To get a token, create an account at"," ",(0,Z.jsx)("a",{href:"https://mapbox.com",children:"https://mapbox.com"}),". It's free for moderate usage levels!"]}),(0,Z.jsxs)("p",{children:["Once you have a token, just set it using the Streamlit config option ",(0,Z.jsx)("code",{children:"mapbox.token"})," and don't forget to restart your Streamlit server at this point if it's still running, then reload this tab."]}),(0,Z.jsxs)("p",{children:["See"," ",(0,Z.jsx)("a",{href:"https://docs.streamlit.io/library/advanced-features/configuration#view-all-configuration-options",children:"our documentation"})," ","for more info on how to set config options."]})]})}):e instanceof P?(0,Z.jsx)(C.Z,{width:i,name:"Error fetching Streamlit Mapbox token",message:(0,Z.jsxs)(Z.Fragment,{children:[(0,Z.jsx)("p",{children:"This app requires an internet connection."}),(0,Z.jsx)("p",{children:"Please check your connection and try again."}),(0,Z.jsxs)("p",{children:["If you think this is a bug, please file bug report"," ",(0,Z.jsx)("a",{href:"https://github.com/streamlit/streamlit/issues/new/choose",children:"here"}),"."]})]})}):(0,Z.jsx)(C.Z,{width:i,name:"Error fetching Streamlit Mapbox token",message:e.message})};var F=i(18080),J=i(16295),O=i(72012),M=i(66694);class z extends Error{}class P extends Error{}const D="https://data.streamlit.io/tokens.json",I="mapbox",N=t=>e=>{class i extends o.PureComponent{constructor(i){super(i),this.context=void 0,this.initMapboxToken=async()=>{try{const t=await F.Z.get(D),{[I]:e}=t.data;if(!e)throw new Error("Missing token ".concat(I));this.setState({mapboxToken:e,isFetching:!1})}catch(t){const e=(0,f.b)(t);throw this.setState({mapboxTokenError:e,isFetching:!1}),new P("".concat(e.message," (").concat(D,")"))}},this.render=()=>{const{mapboxToken:i,mapboxTokenError:o,isFetching:s}=this.state,{width:n}=this.props;return o?(0,Z.jsx)(E,{width:n,error:o,deltaType:t}):s?(0,Z.jsx)(O.O,{element:J.Od.create({style:J.Od.SkeletonStyle.ELEMENT})}):(0,Z.jsx)(e,{...this.props,mapboxToken:i,width:n})},this.state={isFetching:!0,mapboxToken:void 0,mapboxTokenError:void 0}}componentDidMount(){const t=this.props.element.mapboxToken||this.context.libConfig.mapboxToken;t?this.setState({mapboxToken:t,isFetching:!1}):this.initMapboxToken()}}return i.displayName="withMapboxToken(".concat(e.displayName||e.name,")"),i.contextType=M.E,V()(i,e)};var L=i(1515);const _=(0,L.Z)("div",{target:"e1az0zs51"})((t=>{let{width:e,height:i,theme:o}=t;return{marginTop:o.spacing.sm,position:"relative",height:i,width:e}}),""),B=(0,L.Z)("div",{target:"e1az0zs50"})((t=>{let{theme:e}=t;return{position:"absolute",right:"2.625rem",top:e.spacing.md,zIndex:1,"button:not(:disabled)":{background:e.colors.bgColor,"& + button":{borderTopColor:e.colors.secondaryBg},"& span":{filter:(0,l.Iy)(e)?"":"invert(100%)"}}}}),"");i(79259);const A={classes:{...u,...b,...x,...S,CartoLayer:p.Z},functions:{colorBins:d.Z,colorCategories:m.Z,colorContinuous:g.Z}};(0,v.fh)([k.w,y.E]);const G=new w.Z({configuration:A});class q extends o.PureComponent{constructor(){super(...arguments),this.state={viewState:{bearing:0,pitch:0,zoom:11},initialized:!1,initialViewState:{},id:void 0,pydeckJson:void 0,isFullScreen:!1,isLightTheme:(0,l.Iy)(this.props.theme)},this.componentDidMount=()=>{this.setState({initialized:!0})},this.createTooltip=t=>{const{element:e}=this.props;if(!t||!t.object||!e.tooltip)return!1;const i=n.Z.parse(e.tooltip);return i.html?i.html=this.interpolate(t,i.html):i.text=this.interpolate(t,i.text),i},this.interpolate=(t,e)=>{const i=e.match(/{(.*?)}/g);return i&&i.forEach((i=>{const o=i.substring(1,i.length-1);t.object.hasOwnProperty(o)?e=e.replace(i,t.object[o]):t.object.hasOwnProperty("properties")&&t.object.properties.hasOwnProperty(o)&&(e=e.replace(i,t.object.properties[o]))})),e},this.onViewStateChange=t=>{let{viewState:e}=t;this.setState({viewState:e})}}static getDerivedStateFromProps(t,e){const i=q.getDeckObject(t,e);if(!r()(i.initialViewState,e.initialViewState)){const t=Object.keys(i.initialViewState).reduce(((t,o)=>i.initialViewState[o]===e.initialViewState[o]?t:{...t,[o]:i.initialViewState[o]}),{});return{viewState:{...e.viewState,...t},initialViewState:i.initialViewState}}return null}render(){const t=q.getDeckObject(this.props,this.state),{viewState:e}=this.state;return(0,Z.jsx)(_,{className:"stDeckGlJsonChart",width:t.initialViewState.width,height:t.initialViewState.height,"data-testid":"stDeckGlJsonChart",children:(0,Z.jsxs)(s.Z,{viewState:e,onViewStateChange:this.onViewStateChange,height:t.initialViewState.height,width:t.initialViewState.width,layers:this.state.initialized?t.layers:[],getTooltip:this.createTooltip,ContextProvider:c.X$.Provider,controller:!0,children:[(0,Z.jsx)(c.Z3,{height:t.initialViewState.height,width:t.initialViewState.width,mapStyle:t.mapStyle&&("string"===typeof t.mapStyle?t.mapStyle:t.mapStyle[0]),mapboxApiAccessToken:this.props.element.mapboxToken||this.props.mapboxToken}),(0,Z.jsx)(B,{children:(0,Z.jsx)(c.Pv,{className:"zoomButton",showCompass:!1})})]})})}}q.getDeckObject=(t,e)=>{var i,o;const{element:s,width:a,height:r,theme:c,isFullScreen:h}=t,p=null!==h&&void 0!==h&&h;var d,m,g;(s.id===e.id&&e.isFullScreen===p&&e.isLightTheme===(0,l.Iy)(c)||(e.pydeckJson=n.Z.parse(s.json),e.id=s.id),null!==(i=e.pydeckJson)&&void 0!==i&&i.mapStyle||(e.pydeckJson.mapStyle="mapbox://styles/mapbox/".concat((0,l.Iy)(c)?"light":"dark","-v9")),h)?Object.assign(null===(d=e.pydeckJson)||void 0===d?void 0:d.initialViewState,{width:a,height:r}):(null!==(m=e.pydeckJson)&&void 0!==m&&null!==(g=m.initialViewState)&&void 0!==g&&g.height||(e.pydeckJson.initialViewState.height=500),s.useContainerWidth&&(e.pydeckJson.initialViewState.width=a));return e.isFullScreen=h,e.isLightTheme=(0,l.Iy)(c),null===(o=e.pydeckJson)||void 0===o||delete o.views,G.convert(e.pydeckJson)};const W=(0,h.b)(N("st.pydeck_chart")((0,j.Z)(q)))},72709:()=>{},72672:()=>{}}]);