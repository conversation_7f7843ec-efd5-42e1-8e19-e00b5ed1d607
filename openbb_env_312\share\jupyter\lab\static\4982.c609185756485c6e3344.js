"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[4982],{4982:(e,t,n)=>{n.d(t,{diagram:()=>U});var i=n(97366);var r=n(20778);var s=n(57590);var a=n(68232);var o=n(76261);var c=n(96049);var l=n(93113);var h=n(75905);var u=n(63170);var g=n(77470);var d=n(48750);var p=function(){var e=(0,h.K2)((function(e,t,n,i){for(n=n||{},i=e.length;i--;n[e[i]]=t);return n}),"o"),t=[1,4],n=[1,13],i=[1,12],r=[1,15],s=[1,16],a=[1,20],o=[1,19],c=[6,7,8],l=[1,26],u=[1,24],g=[1,25],d=[6,7,11],p=[1,31],y=[6,7,11,24],f=[1,6,13,16,17,20,23],b=[1,35],k=[1,36],m=[1,6,7,11,13,16,17,20,23],_=[1,38];var E={trace:(0,h.K2)((function e(){}),"trace"),yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,KANBAN:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,shapeData:15,ICON:16,CLASS:17,nodeWithId:18,nodeWithoutId:19,NODE_DSTART:20,NODE_DESCR:21,NODE_DEND:22,NODE_ID:23,SHAPE_DATA:24,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"KANBAN",11:"EOF",13:"SPACELIST",16:"ICON",17:"CLASS",20:"NODE_DSTART",21:"NODE_DESCR",22:"NODE_DEND",23:"NODE_ID",24:"SHAPE_DATA"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,3],[12,2],[12,2],[12,2],[12,1],[12,2],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[19,3],[18,1],[18,4],[15,2],[15,1]],performAction:(0,h.K2)((function e(t,n,i,r,s,a,o){var c=a.length-1;switch(s){case 6:case 7:return r;break;case 8:r.getLogger().trace("Stop NL ");break;case 9:r.getLogger().trace("Stop EOF ");break;case 11:r.getLogger().trace("Stop NL2 ");break;case 12:r.getLogger().trace("Stop EOF2 ");break;case 15:r.getLogger().info("Node: ",a[c-1].id);r.addNode(a[c-2].length,a[c-1].id,a[c-1].descr,a[c-1].type,a[c]);break;case 16:r.getLogger().info("Node: ",a[c].id);r.addNode(a[c-1].length,a[c].id,a[c].descr,a[c].type);break;case 17:r.getLogger().trace("Icon: ",a[c]);r.decorateNode({icon:a[c]});break;case 18:case 23:r.decorateNode({class:a[c]});break;case 19:r.getLogger().trace("SPACELIST");break;case 20:r.getLogger().trace("Node: ",a[c-1].id);r.addNode(0,a[c-1].id,a[c-1].descr,a[c-1].type,a[c]);break;case 21:r.getLogger().trace("Node: ",a[c].id);r.addNode(0,a[c].id,a[c].descr,a[c].type);break;case 22:r.decorateNode({icon:a[c]});break;case 27:r.getLogger().trace("node found ..",a[c-2]);this.$={id:a[c-1],descr:a[c-1],type:r.getType(a[c-2],a[c])};break;case 28:this.$={id:a[c],descr:a[c],type:0};break;case 29:r.getLogger().trace("node found ..",a[c-3]);this.$={id:a[c-3],descr:a[c-1],type:r.getType(a[c-2],a[c])};break;case 30:this.$=a[c-1]+a[c];break;case 31:this.$=a[c];break}}),"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],8:t},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:t},{6:n,7:[1,10],9:9,12:11,13:i,14:14,16:r,17:s,18:17,19:18,20:a,23:o},e(c,[2,3]),{1:[2,2]},e(c,[2,4]),e(c,[2,5]),{1:[2,6],6:n,12:21,13:i,14:14,16:r,17:s,18:17,19:18,20:a,23:o},{6:n,9:22,12:11,13:i,14:14,16:r,17:s,18:17,19:18,20:a,23:o},{6:l,7:u,10:23,11:g},e(d,[2,24],{18:17,19:18,14:27,16:[1,28],17:[1,29],20:a,23:o}),e(d,[2,19]),e(d,[2,21],{15:30,24:p}),e(d,[2,22]),e(d,[2,23]),e(y,[2,25]),e(y,[2,26]),e(y,[2,28],{20:[1,32]}),{21:[1,33]},{6:l,7:u,10:34,11:g},{1:[2,7],6:n,12:21,13:i,14:14,16:r,17:s,18:17,19:18,20:a,23:o},e(f,[2,14],{7:b,11:k}),e(m,[2,8]),e(m,[2,9]),e(m,[2,10]),e(d,[2,16],{15:37,24:p}),e(d,[2,17]),e(d,[2,18]),e(d,[2,20],{24:_}),e(y,[2,31]),{21:[1,39]},{22:[1,40]},e(f,[2,13],{7:b,11:k}),e(m,[2,11]),e(m,[2,12]),e(d,[2,15],{24:_}),e(y,[2,30]),{22:[1,41]},e(y,[2,27]),e(y,[2,29])],defaultActions:{2:[2,1],6:[2,2]},parseError:(0,h.K2)((function e(t,n){if(n.recoverable){this.trace(t)}else{var i=new Error(t);i.hash=n;throw i}}),"parseError"),parse:(0,h.K2)((function e(t){var n=this,i=[0],r=[],s=[null],a=[],o=this.table,c="",l=0,u=0,g=0,d=2,p=1;var y=a.slice.call(arguments,1);var f=Object.create(this.lexer);var b={yy:{}};for(var k in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,k)){b.yy[k]=this.yy[k]}}f.setInput(t,b.yy);b.yy.lexer=f;b.yy.parser=this;if(typeof f.yylloc=="undefined"){f.yylloc={}}var m=f.yylloc;a.push(m);var _=f.options&&f.options.ranges;if(typeof b.yy.parseError==="function"){this.parseError=b.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function E(e){i.length=i.length-2*e;s.length=s.length-e;a.length=a.length-e}(0,h.K2)(E,"popStack");function S(){var e;e=r.pop()||f.lex()||p;if(typeof e!=="number"){if(e instanceof Array){r=e;e=r.pop()}e=n.symbols_[e]||e}return e}(0,h.K2)(S,"lex");var v,N,D,x,L,I,O={},C,A,w,K;while(true){D=i[i.length-1];if(this.defaultActions[D]){x=this.defaultActions[D]}else{if(v===null||typeof v=="undefined"){v=S()}x=o[D]&&o[D][v]}if(typeof x==="undefined"||!x.length||!x[0]){var $="";K=[];for(C in o[D]){if(this.terminals_[C]&&C>d){K.push("'"+this.terminals_[C]+"'")}}if(f.showPosition){$="Parse error on line "+(l+1)+":\n"+f.showPosition()+"\nExpecting "+K.join(", ")+", got '"+(this.terminals_[v]||v)+"'"}else{$="Parse error on line "+(l+1)+": Unexpected "+(v==p?"end of input":"'"+(this.terminals_[v]||v)+"'")}this.parseError($,{text:f.match,token:this.terminals_[v]||v,line:f.yylineno,loc:m,expected:K})}if(x[0]instanceof Array&&x.length>1){throw new Error("Parse Error: multiple actions possible at state: "+D+", token: "+v)}switch(x[0]){case 1:i.push(v);s.push(f.yytext);a.push(f.yylloc);i.push(x[1]);v=null;if(!N){u=f.yyleng;c=f.yytext;l=f.yylineno;m=f.yylloc;if(g>0){g--}}else{v=N;N=null}break;case 2:A=this.productions_[x[1]][1];O.$=s[s.length-A];O._$={first_line:a[a.length-(A||1)].first_line,last_line:a[a.length-1].last_line,first_column:a[a.length-(A||1)].first_column,last_column:a[a.length-1].last_column};if(_){O._$.range=[a[a.length-(A||1)].range[0],a[a.length-1].range[1]]}I=this.performAction.apply(O,[c,u,l,b.yy,x[1],s,a].concat(y));if(typeof I!=="undefined"){return I}if(A){i=i.slice(0,-1*A*2);s=s.slice(0,-1*A);a=a.slice(0,-1*A)}i.push(this.productions_[x[1]][0]);s.push(O.$);a.push(O._$);w=o[i[i.length-2]][i[i.length-1]];i.push(w);break;case 3:return true}}return true}),"parse")};var S=function(){var e={EOF:1,parseError:(0,h.K2)((function e(t,n){if(this.yy.parser){this.yy.parser.parseError(t,n)}else{throw new Error(t)}}),"parseError"),setInput:(0,h.K2)((function(e,t){this.yy=t||this.yy||{};this._input=e;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this}),"setInput"),input:(0,h.K2)((function(){var e=this._input[0];this.yytext+=e;this.yyleng++;this.offset++;this.match+=e;this.matched+=e;var t=e.match(/(?:\r\n?|\n).*/g);if(t){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return e}),"input"),unput:(0,h.K2)((function(e){var t=e.length;var n=e.split(/(?:\r\n?|\n)/g);this._input=e+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-t);this.offset-=t;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(n.length-1){this.yylineno-=n.length-1}var r=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:n?(n.length===i.length?this.yylloc.first_column:0)+i[i.length-n.length].length-n[0].length:this.yylloc.first_column-t};if(this.options.ranges){this.yylloc.range=[r[0],r[0]+this.yyleng-t]}this.yyleng=this.yytext.length;return this}),"unput"),more:(0,h.K2)((function(){this._more=true;return this}),"more"),reject:(0,h.K2)((function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this}),"reject"),less:(0,h.K2)((function(e){this.unput(this.match.slice(e))}),"less"),pastInput:(0,h.K2)((function(){var e=this.matched.substr(0,this.matched.length-this.match.length);return(e.length>20?"...":"")+e.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,h.K2)((function(){var e=this.match;if(e.length<20){e+=this._input.substr(0,20-e.length)}return(e.substr(0,20)+(e.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,h.K2)((function(){var e=this.pastInput();var t=new Array(e.length+1).join("-");return e+this.upcomingInput()+"\n"+t+"^"}),"showPosition"),test_match:(0,h.K2)((function(e,t){var n,i,r;if(this.options.backtrack_lexer){r={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){r.yylloc.range=this.yylloc.range.slice(0)}}i=e[0].match(/(?:\r\n?|\n).*/g);if(i){this.yylineno+=i.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+e[0].length};this.yytext+=e[0];this.match+=e[0];this.matches=e;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(e[0].length);this.matched+=e[0];n=this.performAction.call(this,this.yy,this,t,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(n){return n}else if(this._backtrack){for(var s in r){this[s]=r[s]}return false}return false}),"test_match"),next:(0,h.K2)((function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var e,t,n,i;if(!this._more){this.yytext="";this.match=""}var r=this._currentRules();for(var s=0;s<r.length;s++){n=this._input.match(this.rules[r[s]]);if(n&&(!t||n[0].length>t[0].length)){t=n;i=s;if(this.options.backtrack_lexer){e=this.test_match(n,r[s]);if(e!==false){return e}else if(this._backtrack){t=false;continue}else{return false}}else if(!this.options.flex){break}}}if(t){e=this.test_match(t,r[i]);if(e!==false){return e}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}}),"next"),lex:(0,h.K2)((function e(){var t=this.next();if(t){return t}else{return this.lex()}}),"lex"),begin:(0,h.K2)((function e(t){this.conditionStack.push(t)}),"begin"),popState:(0,h.K2)((function e(){var t=this.conditionStack.length-1;if(t>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}}),"popState"),_currentRules:(0,h.K2)((function e(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}}),"_currentRules"),topState:(0,h.K2)((function e(t){t=this.conditionStack.length-1-Math.abs(t||0);if(t>=0){return this.conditionStack[t]}else{return"INITIAL"}}),"topState"),pushState:(0,h.K2)((function e(t){this.begin(t)}),"pushState"),stateStackSize:(0,h.K2)((function e(){return this.conditionStack.length}),"stateStackSize"),options:{"case-insensitive":true},performAction:(0,h.K2)((function e(t,n,i,r){var s=r;switch(i){case 0:this.pushState("shapeData");n.yytext="";return 24;break;case 1:this.pushState("shapeDataStr");return 24;break;case 2:this.popState();return 24;break;case 3:const e=/\n\s*/g;n.yytext=n.yytext.replace(e,"<br/>");return 24;break;case 4:return 24;break;case 5:this.popState();break;case 6:t.getLogger().trace("Found comment",n.yytext);return 6;break;case 7:return 8;break;case 8:this.begin("CLASS");break;case 9:this.popState();return 17;break;case 10:this.popState();break;case 11:t.getLogger().trace("Begin icon");this.begin("ICON");break;case 12:t.getLogger().trace("SPACELINE");return 6;break;case 13:return 7;break;case 14:return 16;break;case 15:t.getLogger().trace("end icon");this.popState();break;case 16:t.getLogger().trace("Exploding node");this.begin("NODE");return 20;break;case 17:t.getLogger().trace("Cloud");this.begin("NODE");return 20;break;case 18:t.getLogger().trace("Explosion Bang");this.begin("NODE");return 20;break;case 19:t.getLogger().trace("Cloud Bang");this.begin("NODE");return 20;break;case 20:this.begin("NODE");return 20;break;case 21:this.begin("NODE");return 20;break;case 22:this.begin("NODE");return 20;break;case 23:this.begin("NODE");return 20;break;case 24:return 13;break;case 25:return 23;break;case 26:return 11;break;case 27:this.begin("NSTR2");break;case 28:return"NODE_DESCR";break;case 29:this.popState();break;case 30:t.getLogger().trace("Starting NSTR");this.begin("NSTR");break;case 31:t.getLogger().trace("description:",n.yytext);return"NODE_DESCR";break;case 32:this.popState();break;case 33:this.popState();t.getLogger().trace("node end ))");return"NODE_DEND";break;case 34:this.popState();t.getLogger().trace("node end )");return"NODE_DEND";break;case 35:this.popState();t.getLogger().trace("node end ...",n.yytext);return"NODE_DEND";break;case 36:this.popState();t.getLogger().trace("node end ((");return"NODE_DEND";break;case 37:this.popState();t.getLogger().trace("node end (-");return"NODE_DEND";break;case 38:this.popState();t.getLogger().trace("node end (-");return"NODE_DEND";break;case 39:this.popState();t.getLogger().trace("node end ((");return"NODE_DEND";break;case 40:this.popState();t.getLogger().trace("node end ((");return"NODE_DEND";break;case 41:t.getLogger().trace("Long description:",n.yytext);return 21;break;case 42:t.getLogger().trace("Long description:",n.yytext);return 21;break}}),"anonymous"),rules:[/^(?:@\{)/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^\"]+)/i,/^(?:[^}^"]+)/i,/^(?:\})/i,/^(?:\s*%%.*)/i,/^(?:kanban\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}@]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{shapeDataEndBracket:{rules:[],inclusive:false},shapeDataStr:{rules:[2,3],inclusive:false},shapeData:{rules:[1,4,5],inclusive:false},CLASS:{rules:[9,10],inclusive:false},ICON:{rules:[14,15],inclusive:false},NSTR2:{rules:[28,29],inclusive:false},NSTR:{rules:[31,32],inclusive:false},NODE:{rules:[27,30,33,34,35,36,37,38,39,40,41,42],inclusive:false},INITIAL:{rules:[0,6,7,8,11,12,13,16,17,18,19,20,21,22,23,24,25,26],inclusive:true}}};return e}();E.lexer=S;function v(){this.yy={}}(0,h.K2)(v,"Parser");v.prototype=E;E.Parser=v;return new v}();p.parser=p;var y=p;var f=[];var b=[];var k=0;var m={};var _=(0,h.K2)((()=>{f=[];b=[];k=0;m={}}),"clear");var E=(0,h.K2)((e=>{if(f.length===0){return null}const t=f[0].level;let n=null;for(let i=f.length-1;i>=0;i--){if(f[i].level===t&&!n){n=f[i]}if(f[i].level<t){throw new Error('Items without section detected, found section ("'+f[i].label+'")')}}if(e===n?.level){return null}return n}),"getSection");var S=(0,h.K2)((function(){return b}),"getSections");var v=(0,h.K2)((function(){const e=[];const t=[];const n=S();const i=(0,h.D7)();for(const r of n){const e={id:r.id,label:(0,h.jZ)(r.label??"",i),isGroup:true,ticket:r.ticket,shape:"kanbanSection",level:r.level,look:i.look};t.push(e);const n=f.filter((e=>e.parentId===r.id));for(const s of n){const e={id:s.id,parentId:r.id,label:(0,h.jZ)(s.label??"",i),isGroup:false,ticket:s?.ticket,priority:s?.priority,assigned:s?.assigned,icon:s?.icon,shape:"kanbanItem",level:s.level,rx:5,ry:5,cssStyles:["text-align: left"]};t.push(e)}}return{nodes:t,edges:e,other:{},config:(0,h.D7)()}}),"getData");var N=(0,h.K2)(((e,t,n,r,s)=>{const a=(0,h.D7)();let o=a.mindmap?.padding??h.UI.mindmap.padding;switch(r){case D.ROUNDED_RECT:case D.RECT:case D.HEXAGON:o*=2}const c={id:(0,h.jZ)(t,a)||"kbn"+k++,level:e,label:(0,h.jZ)(n,a),width:a.mindmap?.maxNodeWidth??h.UI.mindmap.maxNodeWidth,padding:o,isGroup:false};if(s!==void 0){let e;if(!s.includes("\n")){e="{\n"+s+"\n}"}else{e=s+"\n"}const t=(0,i.H)(e,{schema:i.r});if(t.shape&&(t.shape!==t.shape.toLowerCase()||t.shape.includes("_"))){throw new Error(`No such shape: ${t.shape}. Shape names should be lowercase.`)}if(t?.shape&&t.shape==="kanbanItem"){c.shape=t?.shape}if(t?.label){c.label=t?.label}if(t?.icon){c.icon=t?.icon.toString()}if(t?.assigned){c.assigned=t?.assigned.toString()}if(t?.ticket){c.ticket=t?.ticket.toString()}if(t?.priority){c.priority=t?.priority}}const l=E(e);if(l){c.parentId=l.id||"kbn"+k++}else{b.push(c)}f.push(c)}),"addNode");var D={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6};var x=(0,h.K2)(((e,t)=>{h.Rm.debug("In get type",e,t);switch(e){case"[":return D.RECT;case"(":return t===")"?D.ROUNDED_RECT:D.CLOUD;case"((":return D.CIRCLE;case")":return D.CLOUD;case"))":return D.BANG;case"{{":return D.HEXAGON;default:return D.DEFAULT}}),"getType");var L=(0,h.K2)(((e,t)=>{m[e]=t}),"setElementForId");var I=(0,h.K2)((e=>{if(!e){return}const t=(0,h.D7)();const n=f[f.length-1];if(e.icon){n.icon=(0,h.jZ)(e.icon,t)}if(e.class){n.cssClasses=(0,h.jZ)(e.class,t)}}),"decorateNode");var O=(0,h.K2)((e=>{switch(e){case D.DEFAULT:return"no-border";case D.RECT:return"rect";case D.ROUNDED_RECT:return"rounded-rect";case D.CIRCLE:return"circle";case D.CLOUD:return"cloud";case D.BANG:return"bang";case D.HEXAGON:return"hexgon";default:return"no-border"}}),"type2Str");var C=(0,h.K2)((()=>h.Rm),"getLogger");var A=(0,h.K2)((e=>m[e]),"getElementById");var w={clear:_,addNode:N,getSections:S,getData:v,nodeType:D,getType:x,setElementForId:L,decorateNode:I,type2Str:O,getLogger:C,getElementById:A};var K=w;var $=(0,h.K2)((async(e,t,n,i)=>{h.Rm.debug("Rendering kanban diagram\n"+e);const s=i.db;const a=s.getData();const o=(0,h.D7)();o.htmlLabels=false;const c=(0,l.D)(t);const u=c.append("g");u.attr("class","sections");const g=c.append("g");g.attr("class","items");const d=a.nodes.filter((e=>e.isGroup));let p=0;const y=10;const f=[];let b=25;for(const l of d){const e=o?.kanban?.sectionWidth||200;p=p+1;l.x=e*p+(p-1)*y/2;l.width=e;l.y=0;l.height=e*3;l.rx=5;l.ry=5;l.cssClasses=l.cssClasses+" section-"+p;const t=await(0,r.U)(u,l);b=Math.max(b,t?.labelBBox?.height);f.push(t)}let k=0;for(const l of d){const e=f[k];k=k+1;const t=o?.kanban?.sectionWidth||200;const n=-t*3/2+b;let i=n;const s=a.nodes.filter((e=>e.parentId===l.id));for(const a of s){if(a.isGroup){throw new Error("Groups within groups are not allowed in Kanban diagrams")}a.x=l.x;a.width=t-1.5*y;const e=await(0,r.on)(g,a,{config:o});const n=e.node().getBBox();a.y=i+n.height/2;await(0,r.U_)(a);i=a.y+n.height/2+y/2}const c=e.cluster.select("rect");const h=Math.max(i-n+3*y,50)+(b-25);c.attr("height",h)}(0,h.ot)(void 0,c,o.mindmap?.padding??h.UI.kanban.padding,o.mindmap?.useMaxWidth??h.UI.kanban.useMaxWidth)}),"draw");var T={draw:$};var R=(0,h.K2)((e=>{let t="";for(let i=0;i<e.THEME_COLOR_LIMIT;i++){e["lineColor"+i]=e["lineColor"+i]||e["cScaleInv"+i];if((0,u.A)(e["lineColor"+i])){e["lineColor"+i]=(0,g.A)(e["lineColor"+i],20)}else{e["lineColor"+i]=(0,d.A)(e["lineColor"+i],20)}}const n=(0,h.K2)(((t,n)=>e.darkMode?(0,d.A)(t,n):(0,g.A)(t,n)),"adjuster");for(let i=0;i<e.THEME_COLOR_LIMIT;i++){const r=""+(17-3*i);t+=`\n    .section-${i-1} rect, .section-${i-1} path, .section-${i-1} circle, .section-${i-1} polygon, .section-${i-1} path  {\n      fill: ${n(e["cScale"+i],10)};\n      stroke: ${n(e["cScale"+i],10)};\n\n    }\n    .section-${i-1} text {\n     fill: ${e["cScaleLabel"+i]};\n    }\n    .node-icon-${i-1} {\n      font-size: 40px;\n      color: ${e["cScaleLabel"+i]};\n    }\n    .section-edge-${i-1}{\n      stroke: ${e["cScale"+i]};\n    }\n    .edge-depth-${i-1}{\n      stroke-width: ${r};\n    }\n    .section-${i-1} line {\n      stroke: ${e["cScaleInv"+i]} ;\n      stroke-width: 3;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${e.background};\n    stroke: ${e.nodeBorder};\n    stroke-width: 1px;\n  }\n\n  .kanban-ticket-link {\n    fill: ${e.background};\n    stroke: ${e.nodeBorder};\n    text-decoration: underline;\n  }\n    `}return t}),"genSections");var P=(0,h.K2)((e=>`\n  .edge {\n    stroke-width: 3;\n  }\n  ${R(e)}\n  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {\n    fill: ${e.git0};\n  }\n  .section-root text {\n    fill: ${e.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .cluster-label, .label {\n    color: ${e.textColor};\n    fill: ${e.textColor};\n    }\n  .kanban-label {\n    dy: 1em;\n    alignment-baseline: middle;\n    text-anchor: middle;\n    dominant-baseline: middle;\n    text-align: center;\n  }\n`),"getStyles");var B=P;var U={db:K,renderer:T,parser:y,styles:B}}}]);