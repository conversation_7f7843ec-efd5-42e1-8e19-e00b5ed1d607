#!/usr/bin/env python3
"""
OpenBB 完整功能客户端
包含股票分析、财务报表、投资组合、新闻、经济数据等所有功能
支持美股(OpenBB) + A股(Tushare Pro/AKShare/东方财富)
"""

import streamlit as st
import pandas as pd
import numpy as np
import requests
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import time
import warnings
warnings.filterwarnings('ignore')

# 尝试导入A股数据源
try:
    from A股数据源 import AStockDataProvider
    A_STOCK_AVAILABLE = True
except ImportError:
    A_STOCK_AVAILABLE = False


class OpenBBClient:
    """OpenBB API客户端"""
    
    def __init__(self, base_url="http://127.0.0.1:6900"):
        self.base_url = base_url.rstrip('/')
    
    def get_stock_historical(self, symbol, period="1y"):
        """获取股票历史数据"""
        try:
            response = requests.get(f"{self.base_url}/api/v1/equity/price/historical?symbol={symbol}")
            if response.status_code == 200:
                data = response.json()
                if 'results' in data and data['results']:
                    df = pd.DataFrame(data['results'])
                    if 'date' in df.columns:
                        df['date'] = pd.to_datetime(df['date'])
                        df.set_index('date', inplace=True)
                    return df
        except:
            pass
        
        # 模拟数据
        dates = pd.date_range(end=datetime.now(), periods=252, freq='D')
        np.random.seed(hash(symbol) % 1000)
        prices = 100 + np.cumsum(np.random.randn(252) * 0.02)
        
        return pd.DataFrame({
            'open': prices * (1 + np.random.randn(252) * 0.01),
            'high': prices * (1 + np.abs(np.random.randn(252)) * 0.02),
            'low': prices * (1 - np.abs(np.random.randn(252)) * 0.02),
            'close': prices,
            'volume': np.random.randint(1000000, 10000000, 252)
        }, index=dates)
    
    def get_stock_quote(self, symbol):
        """获取股票实时报价"""
        try:
            response = requests.get(f"{self.base_url}/api/v1/equity/price/quote?symbol={symbol}")
            if response.status_code == 200:
                data = response.json()
                if 'results' in data:
                    return data['results']
        except:
            pass
        
        # 模拟数据
        base_price = 100 + hash(symbol) % 200
        change = (hash(symbol) % 20) - 10
        return {
            'symbol': symbol,
            'price': base_price,
            'change': change,
            'change_percent': change / base_price * 100,
            'volume': hash(symbol) % 10000000 + 1000000
        }


def main():
    """主应用"""
    st.set_page_config(
        page_title="OpenBB 完整客户端",
        page_icon="📊",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # 初始化客户端
    client = OpenBBClient()

    # 初始化A股数据源
    a_stock_client = None
    if A_STOCK_AVAILABLE:
        try:
            a_stock_client = AStockDataProvider()
        except Exception as e:
            st.sidebar.warning(f"A股数据源初始化失败: {e}")

    # 侧边栏导航
    st.sidebar.title("🚀 OpenBB 金融分析平台")
    st.sidebar.markdown("---")

    # 市场选择
    market = st.sidebar.selectbox(
        "选择市场",
        ["🇺🇸 美股 (OpenBB)", "🇨🇳 A股 (Tushare/AKShare)", "🌍 全球市场"]
    )

    # 主功能选择
    page = st.sidebar.selectbox(
        "选择功能模块",
        [
            "📈 股票分析",
            "📊 投资组合",
            "📰 新闻资讯",
            "💰 财务报表",
            "🏛️ 经济数据",
            "🔍 市场筛选",
            "📋 技术指标",
            "⚙️ 系统设置"
        ]
    )
    
    # 根据选择显示不同页面
    if page == "📈 股票分析":
        show_stock_analysis(client, a_stock_client, market)
    elif page == "📊 投资组合":
        show_portfolio_analysis(client, a_stock_client, market)
    elif page == "📰 新闻资讯":
        show_news_analysis(client)
    elif page == "💰 财务报表":
        show_financial_statements(client, a_stock_client, market)
    elif page == "🏛️ 经济数据":
        show_economic_data(client)
    elif page == "🔍 市场筛选":
        show_market_screening(client, a_stock_client, market)
    elif page == "📋 技术指标":
        show_technical_analysis(client, a_stock_client, market)
    elif page == "⚙️ 系统设置":
        show_system_settings(client)


def show_stock_analysis(client, a_stock_client, market):
    """股票分析页面"""
    st.title("📈 股票分析")
    st.markdown("---")

    # 显示当前市场
    st.info(f"当前市场: {market}")

    # 输入区域
    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        if "🇨🇳 A股" in market:
            symbol = st.text_input("股票代码", value="000001", key="stock_symbol",
                                 help="输入6位数字代码，如: 000001, 600000")
        else:
            symbol = st.text_input("股票代码", value="AAPL", key="stock_symbol").upper()

    with col2:
        period = st.selectbox("时间周期", ["1mo", "3mo", "6mo", "1y", "2y"], index=3)

    with col3:
        if st.button("🔍 分析", key="analyze_stock"):
            st.rerun()
    
    if symbol:
        # 获取数据
        with st.spinner(f"正在获取 {symbol} 数据..."):
            if "🇨🇳 A股" in market and a_stock_client:
                # 使用A股数据源
                quote_data = a_stock_client.get_realtime_data([symbol])

                # 计算日期范围
                end_date = datetime.now()
                if period == "1mo":
                    start_date = end_date - timedelta(days=30)
                elif period == "3mo":
                    start_date = end_date - timedelta(days=90)
                elif period == "6mo":
                    start_date = end_date - timedelta(days=180)
                elif period == "1y":
                    start_date = end_date - timedelta(days=365)
                elif period == "2y":
                    start_date = end_date - timedelta(days=730)
                else:
                    start_date = end_date - timedelta(days=365)

                historical_data = a_stock_client.get_stock_data(
                    symbol,
                    start_date=start_date.strftime("%Y-%m-%d"),
                    end_date=end_date.strftime("%Y-%m-%d")
                )
            else:
                # 使用美股数据源
                quote_data = client.get_stock_quote(symbol)
                historical_data = client.get_stock_historical(symbol, period)
        
        # 显示实时报价
        if quote_data is not None:
            col1, col2, col3, col4 = st.columns(4)

            # 处理A股和美股数据格式差异
            if "🇨🇳 A股" in market and isinstance(quote_data, pd.DataFrame):
                if not quote_data.empty:
                    quote = quote_data.iloc[0]
                    currency = "¥"
                    price = quote.get('price', 0)
                    change = quote.get('change', 0)
                    change_pct = quote.get('change_pct', 0)
                    volume = quote.get('volume', 0)
                    name = quote.get('name', symbol)

                    st.subheader(f"{name} ({symbol})")
                else:
                    price = change = change_pct = volume = 0
                    currency = "¥"
            else:
                # 美股数据格式
                if isinstance(quote_data, list) and quote_data:
                    quote_data = quote_data[0]

                currency = "$"
                price = quote_data.get('price', 0)
                change = quote_data.get('change', 0)
                change_pct = quote_data.get('change_percent', 0)
                volume = quote_data.get('volume', 0)

            with col1:
                st.metric("当前价格", f"{currency}{price:.2f}")

            with col2:
                st.metric("涨跌", f"{currency}{change:.2f}", f"{change:.2f}")

            with col3:
                st.metric("涨跌幅", f"{change_pct:.2f}%", f"{change_pct:.2f}%")

            with col4:
                if volume > 100000000:  # 大于1亿
                    volume_str = f"{volume/100000000:.1f}亿"
                elif volume > 10000:  # 大于1万
                    volume_str = f"{volume/10000:.1f}万"
                else:
                    volume_str = f"{volume:,.0f}"
                st.metric("成交量", volume_str)
        
        # 显示价格图表
        if historical_data is not None and not historical_data.empty:
            st.subheader("📊 价格走势")
            
            fig = go.Figure()
            
            fig.add_trace(go.Candlestick(
                x=historical_data.index,
                open=historical_data['open'],
                high=historical_data['high'],
                low=historical_data['low'],
                close=historical_data['close'],
                name=symbol
            ))
            
            fig.update_layout(
                title=f"{symbol} 价格走势",
                yaxis_title="价格 ($)",
                xaxis_title="日期",
                height=500
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # 技术指标
            st.subheader("📋 技术指标")
            
            # 计算移动平均线
            historical_data['MA20'] = historical_data['close'].rolling(20).mean()
            historical_data['MA50'] = historical_data['close'].rolling(50).mean()
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**移动平均线**")
                current_price = historical_data['close'].iloc[-1]
                ma20 = historical_data['MA20'].iloc[-1]
                ma50 = historical_data['MA50'].iloc[-1]
                
                st.write(f"20日均线: ${ma20:.2f}")
                st.write(f"50日均线: ${ma50:.2f}")
                
                if current_price > ma20:
                    st.success("价格在20日均线上方 📈")
                else:
                    st.error("价格在20日均线下方 📉")
            
            with col2:
                st.write("**价格统计**")
                st.write(f"最高价: ${historical_data['high'].max():.2f}")
                st.write(f"最低价: ${historical_data['low'].min():.2f}")
                st.write(f"平均价: ${historical_data['close'].mean():.2f}")
                
                volatility = historical_data['close'].pct_change().std() * np.sqrt(252) * 100
                st.write(f"年化波动率: {volatility:.1f}%")


def show_portfolio_analysis(client):
    """投资组合分析页面"""
    st.title("📊 投资组合分析")
    st.markdown("---")
    
    # 输入区域
    st.subheader("🔧 配置投资组合")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        symbols_input = st.text_input(
            "股票代码 (用逗号分隔)", 
            value="AAPL,MSFT,GOOGL,AMZN,TSLA",
            key="portfolio_symbols"
        )
        symbols = [s.strip().upper() for s in symbols_input.split(",") if s.strip()]
    
    with col2:
        equal_weight = st.checkbox("等权重分配", value=True)
    
    if symbols:
        # 权重设置
        if not equal_weight:
            st.subheader("⚖️ 权重分配")
            weights = []
            cols = st.columns(len(symbols))
            
            for i, symbol in enumerate(symbols):
                with cols[i]:
                    weight = st.slider(f"{symbol}", 0.0, 1.0, 1.0/len(symbols), 0.05, key=f"weight_{symbol}")
                    weights.append(weight)
            
            # 标准化权重
            total_weight = sum(weights)
            if total_weight > 0:
                weights = [w/total_weight for w in weights]
        else:
            weights = [1/len(symbols)] * len(symbols)
        
        # 分析按钮
        if st.button("📊 分析投资组合", key="analyze_portfolio"):
            with st.spinner("正在分析投资组合..."):
                # 获取所有股票数据
                portfolio_data = {}
                for symbol in symbols:
                    data = client.get_stock_historical(symbol, "1y")
                    if data is not None:
                        portfolio_data[symbol] = data['close']
                
                if portfolio_data:
                    # 创建价格DataFrame
                    prices_df = pd.DataFrame(portfolio_data)
                    returns_df = prices_df.pct_change().dropna()
                    
                    # 计算投资组合收益率
                    portfolio_returns = (returns_df * weights).sum(axis=1)
                    
                    # 显示结果
                    st.subheader("📈 投资组合表现")
                    
                    col1, col2, col3, col4 = st.columns(4)
                    
                    with col1:
                        annual_return = portfolio_returns.mean() * 252 * 100
                        st.metric("年化收益率", f"{annual_return:.2f}%")
                    
                    with col2:
                        annual_vol = portfolio_returns.std() * np.sqrt(252) * 100
                        st.metric("年化波动率", f"{annual_vol:.2f}%")
                    
                    with col3:
                        sharpe_ratio = annual_return / annual_vol if annual_vol > 0 else 0
                        st.metric("夏普比率", f"{sharpe_ratio:.2f}")
                    
                    with col4:
                        total_return = (1 + portfolio_returns).prod() - 1
                        st.metric("总收益率", f"{total_return*100:.2f}%")
                    
                    # 权重饼图
                    st.subheader("🥧 投资组合权重")
                    
                    fig_pie = px.pie(
                        values=weights,
                        names=symbols,
                        title="投资组合权重分配"
                    )
                    st.plotly_chart(fig_pie, use_container_width=True)
                    
                    # 累计收益图
                    st.subheader("📊 累计收益对比")
                    
                    cumulative_returns = (1 + returns_df).cumprod()
                    portfolio_cumulative = (1 + portfolio_returns).cumprod()
                    
                    fig_returns = go.Figure()
                    
                    # 添加个股收益线
                    for symbol in symbols:
                        fig_returns.add_trace(go.Scatter(
                            x=cumulative_returns.index,
                            y=cumulative_returns[symbol],
                            name=symbol,
                            line=dict(width=1, dash='dot')
                        ))
                    
                    # 添加投资组合收益线
                    fig_returns.add_trace(go.Scatter(
                        x=portfolio_cumulative.index,
                        y=portfolio_cumulative,
                        name="投资组合",
                        line=dict(width=3, color='red')
                    ))
                    
                    fig_returns.update_layout(
                        title="累计收益对比",
                        yaxis_title="累计收益",
                        xaxis_title="日期",
                        height=500
                    )
                    
                    st.plotly_chart(fig_returns, use_container_width=True)


def show_news_analysis(client):
    """新闻分析页面"""
    st.title("📰 新闻资讯")
    st.markdown("---")
    
    # 模拟新闻数据
    news_data = [
        {
            'title': '科技股强势反弹，AI概念股领涨',
            'summary': '今日科技股表现强劲，人工智能相关概念股领涨市场...',
            'time': '2小时前',
            'source': '财经新闻'
        },
        {
            'title': '美联储政策预期推动市场情绪',
            'summary': '市场对美联储货币政策的预期变化影响了投资者情绪...',
            'time': '4小时前',
            'source': '经济观察'
        },
        {
            'title': '新能源汽车销量创新高',
            'summary': '本月新能源汽车销量数据显示持续增长态势...',
            'time': '6小时前',
            'source': '行业报告'
        }
    ]
    
    for i, news in enumerate(news_data):
        with st.expander(f"📄 {news['title']}", expanded=(i < 2)):
            col1, col2 = st.columns([3, 1])
            
            with col1:
                st.write(news['summary'])
            
            with col2:
                st.caption(f"来源: {news['source']}")
                st.caption(f"时间: {news['time']}")


def show_financial_statements(client):
    """财务报表页面"""
    st.title("💰 财务报表")
    st.markdown("---")
    
    symbol = st.text_input("公司代码", value="AAPL", key="financial_symbol").upper()
    
    if symbol:
        st.subheader(f"📊 {symbol} 财务数据")
        
        # 模拟财务数据
        financial_data = {
            '营业收入': [365.8, 394.3, 383.3, 365.8],
            '净利润': [94.7, 99.8, 97.0, 94.7],
            '总资产': [352.8, 365.7, 381.2, 352.8],
            '股东权益': [63.1, 65.3, 67.4, 63.1]
        }
        
        years = ['2021', '2022', '2023', '2024']
        
        df = pd.DataFrame(financial_data, index=years)
        
        # 显示表格
        st.dataframe(df, use_container_width=True)
        
        # 显示图表
        fig = px.bar(
            df.T,
            title=f"{symbol} 财务指标趋势",
            labels={'index': '财务指标', 'value': '金额 (十亿美元)'}
        )
        
        st.plotly_chart(fig, use_container_width=True)


def show_economic_data(client):
    """经济数据页面"""
    st.title("🏛️ 经济数据")
    st.markdown("---")
    
    # 模拟经济数据
    st.subheader("📊 主要经济指标")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("GDP增长率", "2.4%", "0.1%")
    
    with col2:
        st.metric("通胀率", "3.2%", "-0.2%")
    
    with col3:
        st.metric("失业率", "3.7%", "-0.1%")
    
    with col4:
        st.metric("利率", "5.25%", "0%")


def show_market_screening(client):
    """市场筛选页面"""
    st.title("🔍 市场筛选")
    st.markdown("---")
    
    # 筛选条件
    col1, col2, col3 = st.columns(3)
    
    with col1:
        min_price = st.number_input("最低价格", value=0.0)
    
    with col2:
        max_price = st.number_input("最高价格", value=1000.0)
    
    with col3:
        min_volume = st.number_input("最小成交量", value=0)
    
    # 模拟筛选结果
    screening_data = {
        '代码': ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA'],
        '价格': [150.25, 280.50, 2650.00, 3200.00, 220.75],
        '涨跌幅': [2.5, -1.2, 0.8, 1.5, -3.2],
        '成交量': [50000000, 30000000, 1500000, 2000000, 40000000]
    }
    
    df = pd.DataFrame(screening_data)
    st.dataframe(df, use_container_width=True)


def show_technical_analysis(client):
    """技术分析页面"""
    st.title("📋 技术指标")
    st.markdown("---")
    
    symbol = st.text_input("股票代码", value="AAPL", key="tech_symbol").upper()
    
    if symbol:
        data = client.get_stock_historical(symbol, "6mo")
        
        if data is not None:
            # 计算技术指标
            data['SMA20'] = data['close'].rolling(20).mean()
            data['SMA50'] = data['close'].rolling(50).mean()
            
            # RSI
            delta = data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
            rs = gain / loss
            data['RSI'] = 100 - (100 / (1 + rs))
            
            # 显示图表
            fig = go.Figure()
            
            fig.add_trace(go.Scatter(
                x=data.index,
                y=data['close'],
                name='收盘价',
                line=dict(color='blue')
            ))
            
            fig.add_trace(go.Scatter(
                x=data.index,
                y=data['SMA20'],
                name='20日均线',
                line=dict(color='orange')
            ))
            
            fig.add_trace(go.Scatter(
                x=data.index,
                y=data['SMA50'],
                name='50日均线',
                line=dict(color='red')
            ))
            
            fig.update_layout(
                title=f"{symbol} 技术分析",
                yaxis_title="价格",
                height=500
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # RSI图表
            fig_rsi = go.Figure()
            fig_rsi.add_trace(go.Scatter(
                x=data.index,
                y=data['RSI'],
                name='RSI',
                line=dict(color='purple')
            ))
            
            fig_rsi.add_hline(y=70, line_dash="dash", line_color="red", annotation_text="超买线")
            fig_rsi.add_hline(y=30, line_dash="dash", line_color="green", annotation_text="超卖线")
            
            fig_rsi.update_layout(
                title="RSI指标",
                yaxis_title="RSI",
                height=300
            )
            
            st.plotly_chart(fig_rsi, use_container_width=True)


def show_system_settings(client):
    """系统设置页面"""
    st.title("⚙️ 系统设置")
    st.markdown("---")
    
    st.subheader("🔧 API配置")
    
    st.text_input("OpenBB API地址", value="http://127.0.0.1:6900")
    
    st.subheader("🔑 API密钥")
    st.text_input("Benzinga API Key", type="password")
    st.text_input("FMP API Key", type="password")
    st.text_input("Alpha Vantage API Key", type="password")
    
    if st.button("💾 保存设置"):
        st.success("设置已保存！")
    
    st.subheader("ℹ️ 系统信息")
    st.info("OpenBB Platform 4.4.5")
    st.info("Streamlit 客户端版本 1.0")


if __name__ == "__main__":
    main()
