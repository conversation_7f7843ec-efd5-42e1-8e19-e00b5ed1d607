#!/usr/bin/env python3
"""
OpenBB创新技术指标系统集成模块
将创新指标集成到现有OpenBB系统中
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import requests
from datetime import datetime, timedelta
from 创新技术指标 import InnovativeIndicators


class OpenBBInnovativeIntegration:
    """OpenBB创新指标集成类"""
    
    def __init__(self, astock_api_url="http://127.0.0.1:6901", us_api_url="http://127.0.0.1:6900"):
        self.astock_api_url = astock_api_url
        self.us_api_url = us_api_url
    
    def get_stock_data(self, symbol, market="CN", days=365):
        """获取股票数据"""
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
        
        try:
            if market == "CN":
                # A股数据
                response = requests.get(
                    f"{self.astock_api_url}/api/v1/astock/equity/price/historical",
                    params={
                        "symbol": symbol,
                        "start_date": start_date,
                        "end_date": end_date
                    }
                )
            else:
                # 美股数据
                response = requests.get(
                    f"{self.us_api_url}/api/v1/equity/price/historical",
                    params={
                        "symbol": symbol,
                        "start_date": start_date,
                        "end_date": end_date
                    }
                )
            
            if response.status_code == 200:
                data = response.json()
                if 'results' in data and data['results']:
                    df = pd.DataFrame(data['results'])
                    df['date'] = pd.to_datetime(df['date'])
                    df.set_index('date', inplace=True)
                    
                    # 确保列名正确
                    required_columns = ['open', 'high', 'low', 'close', 'volume']
                    for col in required_columns:
                        if col not in df.columns:
                            df[col] = 0
                    
                    return df[required_columns]
            
        except Exception as e:
            st.error(f"获取数据失败: {e}")
        
        return None
    
    def create_innovative_dashboard(self):
        """创建创新指标仪表板"""
        st.title("🚀 OpenBB创新技术指标分析")
        st.markdown("---")
        
        # 侧边栏配置
        st.sidebar.header("📊 指标配置")
        
        # 股票选择
        col1, col2 = st.sidebar.columns(2)
        with col1:
            market = st.selectbox("市场", ["🇨🇳 A股", "🇺🇸 美股"])
        with col2:
            if "🇨🇳 A股" in market:
                symbol = st.text_input("股票代码", value="000001", help="输入6位A股代码")
                market_code = "CN"
            else:
                symbol = st.text_input("股票代码", value="AAPL", help="输入美股代码")
                market_code = "US"
        
        # 时间范围
        days = st.sidebar.slider("数据天数", 90, 730, 365)
        
        # 指标选择
        st.sidebar.subheader("🎯 选择指标")
        selected_indicators = st.sidebar.multiselect(
            "创新指标",
            ["DMFI", "AISI", "MVCI", "TAFI", "FMSI"],
            default=["DMFI", "AISI", "MVCI"]
        )
        
        if st.sidebar.button("🔍 开始分析"):
            self.run_analysis(symbol, market_code, days, selected_indicators)
    
    def run_analysis(self, symbol, market, days, selected_indicators):
        """运行分析"""
        with st.spinner(f"正在获取 {symbol} 数据并计算指标..."):
            # 获取数据
            data = self.get_stock_data(symbol, market, days)
            
            if data is None or data.empty:
                st.error("无法获取股票数据，请检查股票代码或网络连接")
                return
            
            # 计算指标
            indicators_calc = InnovativeIndicators(data)
            all_indicators = indicators_calc.calculate_all_indicators()
            
            # 生成信号
            signals = indicators_calc.generate_signals(all_indicators)
            
            # 显示结果
            self.display_results(data, all_indicators, signals, selected_indicators, symbol)
    
    def display_results(self, data, indicators, signals, selected_indicators, symbol):
        """显示分析结果"""
        
        # 1. 股票基本信息
        st.subheader(f"📈 {symbol} 股票分析")
        
        col1, col2, col3, col4 = st.columns(4)
        
        latest_price = data['close'].iloc[-1]
        price_change = data['close'].iloc[-1] - data['close'].iloc[-2]
        price_change_pct = (price_change / data['close'].iloc[-2]) * 100
        
        with col1:
            st.metric("最新价格", f"${latest_price:.2f}", f"{price_change:+.2f}")
        with col2:
            st.metric("涨跌幅", f"{price_change_pct:+.2f}%")
        with col3:
            st.metric("成交量", f"{data['volume'].iloc[-1]:,.0f}")
        with col4:
            latest_signal = signals['Final_Signal'].iloc[-1]
            signal_color = "🟢" if latest_signal == "BUY" else "🔴" if latest_signal == "SELL" else "🟡"
            st.metric("综合信号", f"{signal_color} {latest_signal}")
        
        # 2. 价格图表
        st.subheader("📊 价格走势与指标")
        
        # 创建子图
        fig = make_subplots(
            rows=len(selected_indicators) + 1, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.05,
            subplot_titles=['价格走势'] + [f'{ind} 指标' for ind in selected_indicators],
            row_heights=[0.4] + [0.6/len(selected_indicators)] * len(selected_indicators)
        )
        
        # 价格图
        fig.add_trace(
            go.Candlestick(
                x=data.index,
                open=data['open'],
                high=data['high'],
                low=data['low'],
                close=data['close'],
                name='价格'
            ),
            row=1, col=1
        )
        
        # 指标图
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
        for i, indicator in enumerate(selected_indicators):
            if indicator in indicators:
                fig.add_trace(
                    go.Scatter(
                        x=indicators[indicator].index,
                        y=indicators[indicator],
                        mode='lines',
                        name=indicator,
                        line=dict(color=colors[i % len(colors)], width=2)
                    ),
                    row=i+2, col=1
                )
                
                # 添加信号线
                fig.add_hline(y=0, line_dash="dash", line_color="gray", row=i+2, col=1)
                fig.add_hline(y=50, line_dash="dot", line_color="red", row=i+2, col=1)
                fig.add_hline(y=-50, line_dash="dot", line_color="green", row=i+2, col=1)
        
        fig.update_layout(
            title=f"{symbol} 创新技术指标分析",
            height=800,
            showlegend=True
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # 3. 指标详细分析
        st.subheader("🔍 指标详细分析")
        
        tabs = st.tabs(selected_indicators + ["综合信号"])
        
        for i, indicator in enumerate(selected_indicators):
            with tabs[i]:
                self.display_indicator_analysis(indicator, indicators[indicator], data)
        
        # 综合信号分析
        with tabs[-1]:
            self.display_signal_analysis(signals, data)
        
        # 4. 交易建议
        st.subheader("💡 交易建议")
        self.generate_trading_advice(indicators, signals, selected_indicators)
    
    def display_indicator_analysis(self, indicator_name, indicator_data, price_data):
        """显示单个指标分析"""
        
        indicator_info = {
            "DMFI": {
                "name": "动态资金流强度指标",
                "description": "结合价格、成交量、波动率和市场情绪的资金流指标",
                "interpretation": {
                    "买入": "> 30",
                    "卖出": "< -30", 
                    "中性": "-30 到 30"
                }
            },
            "AISI": {
                "name": "自适应智能情绪指标",
                "description": "使用机器学习动态调整权重的市场情绪指标",
                "interpretation": {
                    "买入": "> 40",
                    "卖出": "< -40",
                    "中性": "-40 到 40"
                }
            },
            "MVCI": {
                "name": "多维度价值收敛指标", 
                "description": "结合价格、成交量、波动率的多维度收敛分析",
                "interpretation": {
                    "买入": "> 20",
                    "卖出": "< -20",
                    "中性": "-20 到 20"
                }
            },
            "TAFI": {
                "name": "时间自适应趋势指标",
                "description": "根据市场状态自动调整计算周期的趋势指标",
                "interpretation": {
                    "买入": "> 25",
                    "卖出": "< -25",
                    "中性": "-25 到 25"
                }
            },
            "FMSI": {
                "name": "基本面-技术面综合指标",
                "description": "将基本面因子与技术面指标有机结合",
                "interpretation": {
                    "买入": "> 35",
                    "卖出": "< -35",
                    "中性": "-35 到 35"
                }
            }
        }
        
        info = indicator_info.get(indicator_name, {})
        
        st.write(f"**{info.get('name', indicator_name)}**")
        st.write(info.get('description', ''))
        
        # 当前值
        current_value = indicator_data.iloc[-1]
        st.metric("当前值", f"{current_value:.2f}")
        
        # 解读
        interpretation = info.get('interpretation', {})
        if interpretation:
            st.write("**信号解读:**")
            for signal, condition in interpretation.items():
                st.write(f"- {signal}: {condition}")
        
        # 统计信息
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("最大值", f"{indicator_data.max():.2f}")
        with col2:
            st.metric("最小值", f"{indicator_data.min():.2f}")
        with col3:
            st.metric("平均值", f"{indicator_data.mean():.2f}")
    
    def display_signal_analysis(self, signals, price_data):
        """显示综合信号分析"""
        
        # 信号统计
        signal_counts = signals['Final_Signal'].value_counts()
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("买入信号", signal_counts.get('BUY', 0))
        with col2:
            st.metric("卖出信号", signal_counts.get('SELL', 0))
        with col3:
            st.metric("持有信号", signal_counts.get('HOLD', 0))
        
        # 最近信号
        st.write("**最近10个交易日信号:**")
        recent_signals = signals[['Final_Signal', 'Composite_Signal']].tail(10)
        st.dataframe(recent_signals)
        
        # 信号强度分布
        fig = px.histogram(
            signals, x='Composite_Signal',
            title="信号强度分布",
            nbins=50
        )
        st.plotly_chart(fig, use_container_width=True)
    
    def generate_trading_advice(self, indicators, signals, selected_indicators):
        """生成交易建议"""
        
        latest_signal = signals['Final_Signal'].iloc[-1]
        composite_score = signals['Composite_Signal'].iloc[-1]
        
        # 基于综合信号给出建议
        if latest_signal == "BUY" and composite_score > 0.7:
            st.success("🟢 **强烈买入建议**")
            st.write("多个指标显示强烈买入信号，建议积极建仓")
        elif latest_signal == "BUY":
            st.info("🟡 **谨慎买入建议**")
            st.write("指标显示买入信号，但强度一般，建议小仓位试探")
        elif latest_signal == "SELL" and composite_score < -0.7:
            st.error("🔴 **强烈卖出建议**")
            st.write("多个指标显示强烈卖出信号，建议减仓或止损")
        elif latest_signal == "SELL":
            st.warning("🟠 **谨慎卖出建议**")
            st.write("指标显示卖出信号，建议减少仓位")
        else:
            st.info("⚪ **持有建议**")
            st.write("指标显示中性信号，建议继续观望")
        
        # 风险提示
        st.warning("⚠️ **风险提示**: 以上建议仅供参考，投资有风险，请根据自身情况谨慎决策")


def main():
    """主函数"""
    st.set_page_config(
        page_title="OpenBB创新技术指标",
        page_icon="🚀",
        layout="wide"
    )
    
    integration = OpenBBInnovativeIntegration()
    integration.create_innovative_dashboard()


if __name__ == "__main__":
    main()
