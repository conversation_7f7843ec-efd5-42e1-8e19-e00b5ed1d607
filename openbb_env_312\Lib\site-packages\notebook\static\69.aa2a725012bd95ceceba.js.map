{"version": 3, "file": "69.aa2a725012bd95ceceba.js?v=aa2a725012bd95ceceba", "mappings": ";;;;;;;;;;AAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,+BAA+B;AAC/B,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM,gCAAgC,KAAK;AAC3C;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/spreadsheet.js"], "sourcesContent": ["export const spreadsheet = {\n  name: \"spreadsheet\",\n\n  startState: function () {\n    return {\n      stringType: null,\n      stack: []\n    };\n  },\n  token: function (stream, state) {\n    if (!stream) return;\n\n    //check for state changes\n    if (state.stack.length === 0) {\n      //strings\n      if ((stream.peek() == '\"') || (stream.peek() == \"'\")) {\n        state.stringType = stream.peek();\n        stream.next(); // Skip quote\n        state.stack.unshift(\"string\");\n      }\n    }\n\n    //return state\n    //stack has\n    switch (state.stack[0]) {\n    case \"string\":\n      while (state.stack[0] === \"string\" && !stream.eol()) {\n        if (stream.peek() === state.stringType) {\n          stream.next(); // Skip quote\n          state.stack.shift(); // Clear flag\n        } else if (stream.peek() === \"\\\\\") {\n          stream.next();\n          stream.next();\n        } else {\n          stream.match(/^.[^\\\\\\\"\\']*/);\n        }\n      }\n      return \"string\";\n\n    case \"characterClass\":\n      while (state.stack[0] === \"characterClass\" && !stream.eol()) {\n        if (!(stream.match(/^[^\\]\\\\]+/) || stream.match(/^\\\\./)))\n          state.stack.shift();\n      }\n      return \"operator\";\n    }\n\n    var peek = stream.peek();\n\n    //no stack\n    switch (peek) {\n    case \"[\":\n      stream.next();\n      state.stack.unshift(\"characterClass\");\n      return \"bracket\";\n    case \":\":\n      stream.next();\n      return \"operator\";\n    case \"\\\\\":\n      if (stream.match(/\\\\[a-z]+/)) return \"string.special\";\n      else {\n        stream.next();\n        return \"atom\";\n      }\n    case \".\":\n    case \",\":\n    case \";\":\n    case \"*\":\n    case \"-\":\n    case \"+\":\n    case \"^\":\n    case \"<\":\n    case \"/\":\n    case \"=\":\n      stream.next();\n      return \"atom\";\n    case \"$\":\n      stream.next();\n      return \"builtin\";\n    }\n\n    if (stream.match(/\\d+/)) {\n      if (stream.match(/^\\w+/)) return \"error\";\n      return \"number\";\n    } else if (stream.match(/^[a-zA-Z_]\\w*/)) {\n      if (stream.match(/(?=[\\(.])/, false)) return \"keyword\";\n      return \"variable\";\n    } else if ([\"[\", \"]\", \"(\", \")\", \"{\", \"}\"].indexOf(peek) != -1) {\n      stream.next();\n      return \"bracket\";\n    } else if (!stream.eatSpace()) {\n      stream.next();\n    }\n    return null;\n  }\n};\n"], "names": [], "sourceRoot": ""}