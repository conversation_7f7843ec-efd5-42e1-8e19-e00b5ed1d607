version: "3.9"
services:
  db:
    image: postgres:16
    environment:
      POSTGRES_DB: openbb_cn
      POSTGRES_USER: openbb
      POSTGRES_PASSWORD: openbb
    ports:
      - "5432:5432"
    volumes:
      - db_data_dev:/var/lib/postgresql/data

  api:
    build: ../..
    env_file:
      - ../../.env
    environment:
      - DATABASE_URL=postgresql+psycopg2://openbb:openbb@db:5432/openbb_cn
    depends_on:
      - db
    ports:
      - "6900:6900"
    volumes:
      - ../..:/app
    command: ["uvicorn", "openbb_cn.api.app:app", "--host", "0.0.0.0", "--port", "6900", "--reload"]

volumes:
  db_data_dev:

