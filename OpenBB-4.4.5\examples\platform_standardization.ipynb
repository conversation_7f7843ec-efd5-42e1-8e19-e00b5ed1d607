{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How The OpenBB Platform Works"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from openbb import obb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["obb"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["/news\n", "    company\n", "    world\n", "    "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.news"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on method world in module openbb.package.news:\n", "\n", "world(limit: Annotated[int, OpenBBField(description='The number of data entries to return. The number of articles to return.')] = 2500, start_date: Annotated[Union[datetime.date, NoneType, str], OpenBBField(description='Start date of the data, in YYYY-MM-DD format.')] = None, end_date: Annotated[Union[datetime.date, NoneType, str], OpenBBField(description='End date of the data, in YYYY-MM-DD format.')] = None, provider: Annotated[Optional[Literal['benzinga', 'biztoc', 'fmp', 'intrinio', 'tiingo']], OpenBBField(description='The provider to use, by default None. If None, the priority list configured in the settings is used. Default priority: benzinga, biztoc, fmp, intrinio, tiingo.')] = None, **kwargs) -> openbb_core.app.model.obbject.OBBject method of openbb.package.news.ROUTER_news instance\n", "    World News. Global news data.\n", "\n", "    Parameters\n", "    ----------\n", "    limit : int\n", "        The number of data entries to return. The number of articles to return.\n", "    start_date : Union[date, None, str]\n", "        Start date of the data, in YYYY-MM-DD format.\n", "    end_date : Union[date, None, str]\n", "        End date of the data, in YYYY-MM-DD format.\n", "    provider : Optional[Literal['benzinga', 'biztoc', 'fmp', 'intrinio', 'tiingo']]\n", "        The provider to use, by default None. If None, the priority list configured in the settings is used. Default priority: benzinga, biztoc, fmp, intrinio, tiingo.\n", "    date : Optional[datetime.date]\n", "        A specific date to get data for. (provider: ben<PERSON><PERSON>)\n", "    display : Literal['headline', 'abstract', 'full']\n", "        Specify headline only (headline), headline + teaser (abstract), or headline + full body (full). (provider: ben<PERSON><PERSON>)\n", "    updated_since : Optional[int]\n", "        Number of seconds since the news was updated. (provider: ben<PERSON><PERSON>)\n", "    published_since : Optional[int]\n", "        Number of seconds since the news was published. (provider: ben<PERSON><PERSON>)\n", "    sort : Literal['id', 'created', 'updated']\n", "        Key to sort the news by. (provider: ben<PERSON><PERSON>)\n", "    order : Literal['asc', 'desc']\n", "        Order to sort the news by. (provider: ben<PERSON><PERSON>)\n", "    isin : Optional[str]\n", "        The ISIN of the news to retrieve. (provider: ben<PERSON><PERSON>)\n", "    cusip : Optional[str]\n", "        The CUSIP of the news to retrieve. (provider: ben<PERSON>a)\n", "    channels : Optional[str]\n", "        Channels of the news to retrieve. (provider: ben<PERSON><PERSON>)\n", "    topics : Optional[str]\n", "        Topics of the news to retrieve. (provider: ben<PERSON><PERSON>)\n", "    authors : Optional[str]\n", "        Authors of the news to retrieve. (provider: ben<PERSON><PERSON>)\n", "    content_types : Optional[str]\n", "        Content types of the news to retrieve. (provider: ben<PERSON><PERSON>)\n", "    term : Optional[str]\n", "        Search term to filter articles by. This overrides all other filters. (provider: biztoc)\n", "    source : Optional[Union[str, Literal['yahoo', 'moody', 'moody_us_news', 'moody_us_press_releases']]]\n", "        Filter by a specific publisher. Only valid when filter is set to source. (provider: biztoc);\n", "        The source of the news article. (provider: intrinio);\n", "        A comma-separated list of the domains requested. (provider: tiingo)\n", "    sentiment : Optional[Literal['positive', 'neutral', 'negative']]\n", "        Return news only from this source. (provider: intrinio)\n", "    language : Optional[str]\n", "        Filter by language. Unsupported for yahoo source. (provider: intrinio)\n", "    topic : Optional[str]\n", "        Filter by topic. Unsupported for yahoo source. (provider: intrinio)\n", "    word_count_greater_than : Optional[int]\n", "        News stories will have a word count greater than this value. Unsupported for yahoo source. (provider: intrinio)\n", "    word_count_less_than : Optional[int]\n", "        News stories will have a word count less than this value. Unsupported for yahoo source. (provider: intrinio)\n", "    is_spam : Optional[bool]\n", "        Filter whether it is marked as spam or not. Unsupported for yahoo source. (provider: intrinio)\n", "    business_relevance_greater_than : Optional[float]\n", "        News stories will have a business relevance score more than this value. Unsupported for yahoo source. Value is a decimal between 0 and 1. (provider: intrinio)\n", "    business_relevance_less_than : Optional[float]\n", "        News stories will have a business relevance score less than this value. Unsupported for yahoo source. Value is a decimal between 0 and 1. (provider: intrinio)\n", "    offset : Optional[int]\n", "        Page offset, used in conjunction with limit. (provider: tiingo)\n", "\n", "    Returns\n", "    -------\n", "    OBBject\n", "        results : List[WorldNews]\n", "            Serializable results.\n", "        provider : Optional[Literal['benzinga', 'biztoc', 'fmp', 'intrinio', 'tiingo']]\n", "            Provider name.\n", "        warnings : Optional[List[Warning_]]\n", "            List of warnings.\n", "        chart : Optional[Chart]\n", "            Chart object.\n", "        extra : Dict[str, Any]\n", "            Extra info.\n", "\n", "    WorldNews\n", "    ---------\n", "    date : datetime\n", "        The date of the data. The published date of the article.\n", "    title : str\n", "        Title of the article.\n", "    images : Optional[List[Dict[str, str]]]\n", "        Images associated with the article.\n", "    text : Optional[str]\n", "        Text/body of the article.\n", "    url : Optional[str]\n", "        URL to the article.\n", "    id : Optional[str]\n", "        Article ID. (provider: benzinga, intrinio)\n", "    author : Optional[str]\n", "        Author of the news. (provider: ben<PERSON><PERSON>)\n", "    teaser : Optional[str]\n", "        Teaser of the news. (provider: ben<PERSON><PERSON>)\n", "    channels : Optional[str]\n", "        Channels associated with the news. (provider: ben<PERSON>a)\n", "    stocks : Optional[str]\n", "        Stocks associated with the news. (provider: ben<PERSON>a)\n", "    tags : Optional[Union[str, List[str]]]\n", "        Tags associated with the news. (provider: benzinga, biztoc, tiingo)\n", "    updated : Optional[datetime]\n", "        Updated date of the news. (provider: benzinga)\n", "    score : Optional[float]\n", "        Search relevance score for the article. (provider: biztoc)\n", "    site : Optional[str]\n", "        News source. (provider: fmp, tiingo)\n", "    source : Optional[str]\n", "        The source of the news article. (provider: intrinio)\n", "    summary : Optional[str]\n", "        The summary of the news article. (provider: intrinio)\n", "    topics : Optional[str]\n", "        The topics related to the news article. (provider: intrinio)\n", "    word_count : Optional[int]\n", "        The word count of the news article. (provider: intrinio)\n", "    business_relevance : Optional[float]\n", "            How strongly correlated the news article is to the business (provider: intrinio)\n", "    sentiment : Optional[str]\n", "        The sentiment of the news article - i.e, negative, positive. (provider: intrinio)\n", "    sentiment_confidence : Optional[float]\n", "        The confidence score of the sentiment rating. (provider: intrinio)\n", "    language : Optional[str]\n", "        The language of the news article. (provider: intrinio)\n", "    spam : Optional[bool]\n", "        Whether the news article is spam. (provider: intrinio)\n", "    copyright : Optional[str]\n", "        The copyright notice of the news article. (provider: intrinio)\n", "    company : Optional[IntrinioCompany]\n", "        The Intrinio Company object. Contains details company reference data. (provider: intrinio)\n", "    security : Optional[IntrinioSecurity]\n", "        The Intrinio Security object. Contains the security details related to the news article. (provider: intrinio)\n", "    symbols : Optional[str]\n", "        Ticker tagged in the fetched news. (provider: tiingo)\n", "    article_id : Optional[int]\n", "        Unique ID of the news article. (provider: tiingo)\n", "    crawl_date : Optional[datetime]\n", "        Date the news article was crawled. (provider: tiingo)\n", "\n", "    Examples\n", "    --------\n", "    >>> from openbb import obb\n", "    >>> obb.news.world(provider='fmp')\n", "    >>> obb.news.world(limit=100, provider='intrinio')\n", "    >>> # Get news on the specified dates.\n", "    >>> obb.news.world(start_date='2024-02-01', end_date='2024-02-07', provider='intrinio')\n", "    >>> # Display the headlines of the news.\n", "    >>> obb.news.world(display='headline', provider='benzinga')\n", "    >>> # Get news by topics.\n", "    >>> obb.news.world(topics='finance', provider='benzinga')\n", "    >>> # Get news by source using 'tingo' as provider.\n", "    >>> obb.news.world(provider='tiingo', source='bloomberg')\n", "    >>> # Filter aticles by term using 'biztoc' as provider.\n", "    >>> obb.news.world(provider='biztoc', term='apple')\n", "\n"]}], "source": ["help(obb.news.world)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Uniform interface allows switching between providers"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>date</th>\n", "      <th>2024-08-22 18:02:00+00:00</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>title</th>\n", "      <td>Natural Grocers® Teams Up With Local Artist, S...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>text</th>\n", "      <td>Natural Grocers®, the leading family-operated ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>url</th>\n", "      <td>https://finance.yahoo.com/news/natural-grocers...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>source</th>\n", "      <td>yahoo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>id</th>\n", "      <td>new_DDGR2v</td>\n", "    </tr>\n", "    <tr>\n", "      <th>company</th>\n", "      <td>{'id': 'com_g4Q8NX', 'ticker': 'NGVC', 'name':...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["date                             2024-08-22 18:02:00+00:00\n", "title    Natural Grocers® Teams Up With Local Artist, S...\n", "text     Natural Grocers®, the leading family-operated ...\n", "url      https://finance.yahoo.com/news/natural-grocers...\n", "source                                               yahoo\n", "id                                              new_DDGR2v\n", "company  {'id': 'com_g4Q8NX', 'ticker': 'NGVC', 'name':..."]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.news.world(limit=1, provider=\"intrinio\").to_df().T"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>date</th>\n", "      <th>2024-08-22 14:46:33-04:00</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>title</th>\n", "      <td>Behind the Scenes of <PERSON><PERSON><PERSON> H<PERSON>'s Latest Opt...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>images</th>\n", "      <td>[{'size': 'thumb', 'url': 'https://cdn.benzing...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>text</th>\n", "      <td>&lt;p&gt;Whales with a lot of money to spend have ta...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>url</th>\n", "      <td>https://www.benzinga.com/insights/options/24/0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>id</th>\n", "      <td>40515079</td>\n", "    </tr>\n", "    <tr>\n", "      <th>author</th>\n", "      <td>Benzinga Insights</td>\n", "    </tr>\n", "    <tr>\n", "      <th>teaser</th>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>channels</th>\n", "      <td>Options,Markets</td>\n", "    </tr>\n", "    <tr>\n", "      <th>stocks</th>\n", "      <td>VRT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>tags</th>\n", "      <td>BZI-UOA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>updated</th>\n", "      <td>2024-08-22 14:46:33-04:00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["date                              2024-08-22 14:46:33-04:00\n", "title     Behind the Scenes of <PERSON><PERSON><PERSON>'s Latest Opt...\n", "images    [{'size': 'thumb', 'url': 'https://cdn.benzing...\n", "text      <p>Whales with a lot of money to spend have ta...\n", "url       https://www.benzinga.com/insights/options/24/0...\n", "id                                                 40515079\n", "author                                    <PERSON><PERSON>s\n", "teaser                                                     \n", "channels                                    Options,Markets\n", "stocks                                                  VRT\n", "tags                                                BZI-UOA\n", "updated                           2024-08-22 14:46:33-04:00"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.news.world(limit=1, provider=\"benzinga\").to_df().T"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "\n", "\n", "---\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Standardization of input and output schemas is done with Pydantic models"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### This is a standard model"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'date': FieldInfo(annotation=datetime, required=True, alias_priority=1, validation_alias='date', serialization_alias='date', description='The date of the data. The published date of the article.'),\n", " 'title': FieldInfo(annotation=str, required=True, alias_priority=1, validation_alias='title', serialization_alias='title', description='Title of the article.'),\n", " 'images': FieldInfo(annotation=Union[List[Dict[str, str]], NoneType], required=False, default=None, alias_priority=1, validation_alias='images', serialization_alias='images', description='Images associated with the article.'),\n", " 'text': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='text', serialization_alias='text', description='Text/body of the article.'),\n", " 'url': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='url', serialization_alias='url', description='URL to the article.')}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from openbb_core.provider.standard_models.world_news import WorldNewsData\n", "\n", "WorldNewsData.__fields__"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### These are provider models"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from openbb_intrinio.models.world_news import IntrinioWorldNewsData\n", "from openbb_benzinga.models.world_news import BenzingaWorldNewsData"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'date': FieldInfo(annotation=datetime, required=True, alias_priority=1, validation_alias='date', serialization_alias='date', description='The date of the data. The published date of the article.'),\n", " 'title': FieldInfo(annotation=str, required=True, alias_priority=1, validation_alias='title', serialization_alias='title', description='Title of the article.'),\n", " 'images': FieldInfo(annotation=Union[List[Dict[str, str]], NoneType], required=False, default=None, alias_priority=1, validation_alias='images', serialization_alias='images', description='Images associated with the article.'),\n", " 'text': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='text', serialization_alias='text', description='Text/body of the article.'),\n", " 'url': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='url', serialization_alias='url', description='URL to the article.'),\n", " 'source': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='source', serialization_alias='source', description='The source of the news article.'),\n", " 'summary': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='summary', serialization_alias='summary', description='The summary of the news article.'),\n", " 'topics': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='topics', serialization_alias='topics', description='The topics related to the news article.'),\n", " 'word_count': FieldInfo(annotation=Union[int, NoneType], required=False, default=None, alias_priority=1, validation_alias='wordCount', serialization_alias='word_count', description='The word count of the news article.'),\n", " 'business_relevance': FieldInfo(annotation=Union[float, NoneType], required=False, default=None, alias_priority=1, validation_alias='businessRelevance', serialization_alias='business_relevance', description=' \\tHow strongly correlated the news article is to the business'),\n", " 'sentiment': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='sentiment', serialization_alias='sentiment', description='The sentiment of the news article - i.e, negative, positive.'),\n", " 'sentiment_confidence': FieldInfo(annotation=Union[float, NoneType], required=False, default=None, alias_priority=1, validation_alias='sentimentConfidence', serialization_alias='sentiment_confidence', description='The confidence score of the sentiment rating.'),\n", " 'language': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='language', serialization_alias='language', description='The language of the news article.'),\n", " 'spam': FieldInfo(annotation=Union[bool, NoneType], required=False, default=None, alias_priority=1, validation_alias='spam', serialization_alias='spam', description='Whether the news article is spam.'),\n", " 'copyright': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='copyright', serialization_alias='copyright', description='The copyright notice of the news article.'),\n", " 'id': FieldInfo(annotation=str, required=True, alias_priority=1, validation_alias='id', serialization_alias='id', description='Article ID.'),\n", " 'company': FieldInfo(annotation=Union[IntrinioCompany, NoneType], required=False, default=None, alias_priority=1, validation_alias='company', serialization_alias='company', description='The Intrinio Company object. Contains details company reference data.'),\n", " 'security': FieldInfo(annotation=Union[IntrinioSecurity, NoneType], required=False, default=None, alias_priority=1, validation_alias='security', serialization_alias='security', description='The Intrinio Security object. Contains the security details related to the news article.')}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["IntrinioWorldNewsData.__fields__"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'date': FieldInfo(annotation=datetime, required=True, alias_priority=1, validation_alias='date', serialization_alias='date', description='The date of the data. The published date of the article.'),\n", " 'title': FieldInfo(annotation=str, required=True, alias_priority=1, validation_alias='title', serialization_alias='title', description='Title of the article.'),\n", " 'images': FieldInfo(annotation=Union[List[Dict[str, str]], NoneType], required=False, default=None, alias_priority=1, validation_alias='images', serialization_alias='images', description='Images associated with the article.'),\n", " 'text': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='text', serialization_alias='text', description='Text/body of the article.'),\n", " 'url': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='url', serialization_alias='url', description='URL to the article.'),\n", " 'id': FieldInfo(annotation=str, required=True, alias_priority=1, validation_alias='id', serialization_alias='id', description='Article ID.'),\n", " 'author': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='author', serialization_alias='author', description='Author of the news.'),\n", " 'teaser': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='teaser', serialization_alias='teaser', description='Teaser of the news.'),\n", " 'channels': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='channels', serialization_alias='channels', description='Channels associated with the news.'),\n", " 'stocks': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='stocks', serialization_alias='stocks', description='Stocks associated with the news.'),\n", " 'tags': FieldInfo(annotation=Union[str, NoneType], required=False, default=None, alias_priority=1, validation_alias='tags', serialization_alias='tags', description='Tags associated with the news.'),\n", " 'updated': FieldInfo(annotation=Union[datetime, NoneType], required=False, default=None, alias_priority=1, validation_alias='updated', serialization_alias='updated', description='Updated date of the news.')}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["BenzingaWorldNewsData.__fields__"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Inheritance, field mapping and quality assurance"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Provider models inherit from Standard Models"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["issubclass(BenzingaWorldNewsData, WorldNewsData)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Provider models use aliases to map to standard fields"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'date': 'created', 'text': 'body', 'images': 'image'}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["BenzingaWorldNewsData.__alias_dict__"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Provider models implement field validation"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'date_validate': Decorator(cls_ref='openbb_intrinio.models.world_news.IntrinioWorldNewsData:140298041431344', cls_var_name='date_validate', func=<bound method IntrinioWorldNewsData.date_validate of <class 'openbb_intrinio.models.world_news.IntrinioWorldNewsData'>>, shim=None, info=FieldValidatorDecoratorInfo(fields=('publication_date',), mode='before', check_fields=False)),\n", " 'topics_validate': Decorator(cls_ref='openbb_intrinio.models.world_news.IntrinioWorldNewsData:140298041431344', cls_var_name='topics_validate', func=<bound method IntrinioWorldNewsData.topics_validate of <class 'openbb_intrinio.models.world_news.IntrinioWorldNewsData'>>, shim=None, info=FieldValidatorDecoratorInfo(fields=('topics',), mode='before', check_fields=False)),\n", " 'copyright_validate': Decorator(cls_ref='openbb_intrinio.models.world_news.IntrinioWorldNewsData:140298041431344', cls_var_name='copyright_validate', func=<bound method IntrinioWorldNewsData.copyright_validate of <class 'openbb_intrinio.models.world_news.IntrinioWorldNewsData'>>, shim=None, info=FieldValidatorDecoratorInfo(fields=('copyright',), mode='before', check_fields=False))}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["IntrinioWorldNewsData.__dict__[\"__pydantic_decorators__\"].field_validators"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'date_validate': Decorator(cls_ref='openbb_benzinga.models.world_news.BenzingaWorldNewsData:140297991464784', cls_var_name='date_validate', func=<bound method BenzingaWorldNewsData.date_validate of <class 'openbb_benzinga.models.world_news.BenzingaWorldNewsData'>>, shim=None, info=FieldValidatorDecoratorInfo(fields=('date', 'updated'), mode='before', check_fields=False)),\n", " 'list_validate': Decorator(cls_ref='openbb_benzinga.models.world_news.BenzingaWorldNewsData:140297991464784', cls_var_name='list_validate', func=<bound method BenzingaWorldNewsData.list_validate of <class 'openbb_benzinga.models.world_news.BenzingaWorldNewsData'>>, shim=None, info=FieldValidatorDecoratorInfo(fields=('stocks', 'channels', 'tags'), mode='before', check_fields=False)),\n", " 'id_validate': Decorator(cls_ref='openbb_benzinga.models.world_news.BenzingaWorldNewsData:140297991464784', cls_var_name='id_validate', func=<bound method BenzingaWorldNewsData.id_validate of <class 'openbb_benzinga.models.world_news.BenzingaWorldNewsData'>>, shim=None, info=FieldValidatorDecoratorInfo(fields=('id', 'text', 'teaser', 'title', 'author'), mode='before', check_fields=False)),\n", " 'empty_list': Decorator(cls_ref='openbb_benzinga.models.world_news.BenzingaWorldNewsData:140297991464784', cls_var_name='empty_list', func=<bound method BenzingaWorldNewsData.empty_list of <class 'openbb_benzinga.models.world_news.BenzingaWorldNewsData'>>, shim=None, info=FieldValidatorDecoratorInfo(fields=('images',), mode='before', check_fields=False))}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["BenzingaWorldNewsData.__dict__[\"__pydantic_decorators__\"].field_validators"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Example:\n", "\n", "```python\n", "@field_validator(\"date\")\n", "def date_validate(cls, v):\n", "    \"\"\"Return the date as a datetime object.\"\"\"\n", "    return datetime.strptime(v, \"%a, %d %b %Y %H:%M:%S %z\")\n", "```\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Modularity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["obb"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Each extension and provider integration is a separate python package"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip list | grep openbb"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Install/Uninstall a provider as python packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip uninstall openbb-yfinance"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To learn more about how it works, here are a few links to the [documentation](https://docs.openbb.co/platform):\n", "\n", "- [Architecture. Data, Query Parameters and Fetchers.](https://docs.openbb.co/platform/developer_guide/architecture_overview)\n", "- [Integrating a new provider.](https://docs.openbb.co/platform/user_guides/add_data_provider_extension)\n", "- [Building standalone extensions.](https://docs.openbb.co/platform/getting_started/create_new_provider_extension)\n", "- and more in the [Development](https://docs.openbb.co/platform/developer_guide) section of the docs...\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}