from __future__ import annotations

import math
import random
from dataclasses import dataclass
from typing import List, Dict, Tuple

import numpy as np
import pandas as pd

from ..sdk.obb import obb
from ..providers.tushare_provider import TushareProvider


def _load_ohlcv(symbol: str, start: str | None, end: str | None) -> pd.DataFrame:
    df = obb.equity.price.historical(symbol=symbol, start=start, end=end)
    return df.reset_index(drop=True)


def _features(df: pd.DataFrame) -> pd.DataFrame:
    x = df.copy()
    x["ret5"] = x["close"].pct_change(5)
    x["ret20"] = x["close"].pct_change(20)
    x["vol20"] = x["close"].pct_change().rolling(20).std()
    x["mom"] = (x["close"] / x["close"].shift(20)) - 1
    x["rsi14"] = _rsi(x["close"], 14)
    x = x.dropna()
    return x


def _rsi(close: pd.Series, window: int) -> pd.Series:
    d = close.diff()
    up = d.clip(lower=0).rolling(window).mean()
    dn = (-d.clip(upper=0)).rolling(window).mean()
    rs = up / dn.replace(0, np.nan)
    return 100 - 100 / (1 + rs)


# =============== Genetic Algorithm for factor weighting ===============

@dataclass
class GAConfig:
    population_size: int = 30
    generations: int = 30
    crossover_rate: float = 0.9
    mutation_rate: float = 0.1
    elite: int = 2


def _fitness(weights: np.ndarray, x: pd.DataFrame) -> float:
    # weights align to columns: [mom, rsi14, ret5, ret20, vol20]
    cols = ["mom", "rsi14", "ret5", "ret20", "vol20"]
    w = weights / (np.linalg.norm(weights) + 1e-9)
    score = np.tanh((x[cols] * w).sum(axis=1))
    ret = x["close"].pct_change().shift(-1)
    pnl = (score * ret).dropna()
    if pnl.std(ddof=0) == 0 or len(pnl) < 10:
        return -1e9
    sharpe = pnl.mean() / (pnl.std(ddof=0) + 1e-9) * math.sqrt(252)
    return float(sharpe)


def ga_optimize(symbol: str, start: str | None, end: str | None, config: GAConfig | None = None) -> Dict:
    cfg = config or GAConfig()
    df = _load_ohlcv(symbol, start, end)
    x = _features(df)
    if x.empty:
        return {"symbol": symbol, "best": [], "fitness": None}
    n = 5
    pop = [np.random.uniform(-1, 1, size=n) for _ in range(cfg.population_size)]
    fit = [ _fitness(ind, x) for ind in pop ]
    for _ in range(cfg.generations):
        new_pop: List[np.ndarray] = []
        elite_idx = np.argsort(fit)[-cfg.elite:]
        for i in elite_idx:
            new_pop.append(pop[i].copy())
        while len(new_pop) < cfg.population_size:
            a, b = _tournament(pop, fit), _tournament(pop, fit)
            c1, c2 = _crossover(a, b, cfg.crossover_rate)
            _mutate(c1, cfg.mutation_rate)
            _mutate(c2, cfg.mutation_rate)
            new_pop.extend([c1, c2])
        pop = new_pop[:cfg.population_size]
        fit = [ _fitness(ind, x) for ind in pop ]
    bi = int(np.argmax(fit))
    best = pop[bi]
    return {"symbol": symbol, "best": best.tolist(), "fitness": float(fit[bi])}


def _tournament(pop: List[np.ndarray], fit: List[float], k: int = 3) -> np.ndarray:
    idx = np.random.choice(len(pop), size=k, replace=False)
    best = idx[np.argmax([fit[i] for i in idx])]
    return pop[best].copy()


def _crossover(a: np.ndarray, b: np.ndarray, rate: float) -> Tuple[np.ndarray, np.ndarray]:
    if random.random() > rate:
        return a.copy(), b.copy()
    point = random.randint(1, len(a) - 1)
    c1 = np.concatenate([a[:point], b[point:]])
    c2 = np.concatenate([b[:point], a[point:]])
    return c1, c2


def _mutate(ind: np.ndarray, rate: float):
    for i in range(len(ind)):
        if random.random() < rate:
            ind[i] += np.random.normal(0, 0.2)


# =============== Particle Swarm Optimization for stock selection ===============

@dataclass
class PSOConfig:
    swarm_size: int = 30
    iterations: int = 40
    w: float = 0.7
    c1: float = 1.4
    c2: float = 1.4


def pso_select(symbols: List[str], start: str | None, end: str | None, top_n: int = 10, cfg: PSOConfig | None = None) -> Dict:
    cfg = cfg or PSOConfig()
    # Build feature matrix at last date for each symbol
    feats = []
    keep_symbols = []
    for s in symbols:
        try:
            x = _features(_load_ohlcv(s, start, end))
            if x.empty:
                continue
            last = x.iloc[-1]
            feats.append([last["mom"], last["rsi14"], last["ret5"], last["ret20"], last["vol20"]])
            keep_symbols.append(s)
        except Exception:
            continue
    if not feats:
        return {"selected": []}
    X = np.array(feats)
    # PSO searches for weights maximizing next-day return proxy: higher mom, lower vol, RSI mid-high
    def objective(w: np.ndarray) -> float:
        w = w / (np.linalg.norm(w) + 1e-9)
        score = X @ w
        # prefer RSI 40-70, penalize extreme
        rsi_idx = 1
        score -= 0.001 * (np.abs(X[:, rsi_idx] - 55))
        # prefer lower vol
        score -= 0.5 * X[:, 4]
        return float(np.mean(score))

    dim = 5
    pos = np.random.uniform(-1, 1, size=(cfg.swarm_size, dim))
    vel = np.zeros_like(pos)
    pbest = pos.copy()
    pbest_fit = np.array([objective(pi) for pi in pos])
    g_idx = int(np.argmax(pbest_fit))
    gbest = pbest[g_idx].copy()
    gbest_fit = float(pbest_fit[g_idx])
    for _ in range(cfg.iterations):
        for i in range(cfg.swarm_size):
            r1, r2 = np.random.rand(dim), np.random.rand(dim)
            vel[i] = cfg.w * vel[i] + cfg.c1 * r1 * (pbest[i] - pos[i]) + cfg.c2 * r2 * (gbest - pos[i])
            pos[i] = pos[i] + vel[i]
            f = objective(pos[i])
            if f > pbest_fit[i]:
                pbest[i], pbest_fit[i] = pos[i].copy(), f
                if f > gbest_fit:
                    gbest, gbest_fit = pos[i].copy(), f
    # score all symbols with best weights
    w = gbest / (np.linalg.norm(gbest) + 1e-9)
    score = (X @ w) - 0.001 * (np.abs(X[:, 1] - 55)) - 0.5 * X[:, 4]
    order = np.argsort(score)[::-1]
    selected = [keep_symbols[i] for i in order[:top_n]]
    return {"weights": gbest.tolist(), "fitness": gbest_fit, "selected": selected}

