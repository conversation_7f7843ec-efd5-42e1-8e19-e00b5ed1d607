#!/usr/bin/env python3
"""
智能模拟炒股系统
集成AI经验学习和算法优化
"""

import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import pandas as pd
import numpy as np

from ..ai.experience_engine import ai_experience, TradingExperience
from ..quant.ga_pso import pso_select, ga_optimize


@dataclass
class Position:
    """持仓信息"""
    symbol: str
    quantity: int
    avg_price: float
    current_price: float
    market_value: float
    pnl: float
    pnl_percent: float
    buy_date: str


@dataclass
class Trade:
    """交易记录"""
    trade_id: str
    symbol: str
    action: str  # 'buy' or 'sell'
    price: float
    quantity: int
    amount: float
    timestamp: str
    strategy: str
    commission: float


class SmartTradingSystem:
    """智能交易系统"""
    
    def __init__(self, initial_cash: float = 100000.0):
        self.initial_cash = initial_cash
        self.cash = initial_cash
        self.positions: Dict[str, Position] = {}
        self.trades: List[Trade] = []
        self.commission_rate = 0.0005  # 0.05% 手续费
        
        # 策略配置
        self.pso_enabled = False
        self.ga_enabled = False
        self.rebalance_freq = "W-FRI"  # 每周五调仓
        self.last_rebalance = None
        
        # 初始化数据库
        self._init_database()
    
    def _init_database(self):
        """初始化交易数据库"""
        conn = sqlite3.connect("smart_trading.db")
        cursor = conn.cursor()
        
        # 持仓表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS positions (
                symbol TEXT PRIMARY KEY,
                quantity INTEGER,
                avg_price REAL,
                buy_date TEXT
            )
        ''')
        
        # 交易记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                trade_id TEXT PRIMARY KEY,
                symbol TEXT,
                action TEXT,
                price REAL,
                quantity INTEGER,
                amount REAL,
                timestamp TEXT,
                strategy TEXT,
                commission REAL
            )
        ''')
        
        # 账户状态表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS account_status (
                date TEXT PRIMARY KEY,
                cash REAL,
                total_value REAL,
                pnl REAL,
                pnl_percent REAL
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def enable_pso(self, enabled: bool):
        """启用/禁用粒子群选股"""
        self.pso_enabled = enabled
        if enabled:
            print("✅ 粒子群选股已启用")
        else:
            print("❌ 粒子群选股已禁用")
    
    def enable_ga(self, enabled: bool):
        """启用/禁用遗传算法优化"""
        self.ga_enabled = enabled
        if enabled:
            print("✅ 遗传算法优化已启用")
        else:
            print("❌ 遗传算法优化已禁用")
    
    def set_rebalance_frequency(self, freq: str):
        """设置调仓频率"""
        self.rebalance_freq = freq
        print(f"📅 调仓频率设置为: {freq}")
    
    def get_current_price(self, symbol: str) -> float:
        """获取当前价格（模拟）"""
        try:
            # 这里应该调用实际的价格API
            # 暂时返回模拟价格
            import random
            base_price = 10.0 + hash(symbol) % 100
            return base_price * (0.95 + random.random() * 0.1)
        except:
            return 10.0
    
    def detect_market_condition(self, symbol: str) -> str:
        """检测市场状况"""
        try:
            # 简化的市场状况检测
            # 实际应该基于技术指标
            import random
            conditions = ["bull", "bear", "sideways"]
            return random.choice(conditions)
        except:
            return "sideways"
    
    def get_technical_indicators(self, symbol: str) -> Dict:
        """获取技术指标"""
        try:
            # 简化的技术指标
            # 实际应该调用真实的指标API
            import random
            return {
                "rsi": 30 + random.random() * 40,
                "macd": -1 + random.random() * 2,
                "pe": 10 + random.random() * 20,
                "pb": 1 + random.random() * 3
            }
        except:
            return {}
    
    def buy_stock(self, symbol: str, amount: float, strategy: str = "manual") -> Dict:
        """买入股票"""
        current_price = self.get_current_price(symbol)
        commission = amount * self.commission_rate
        total_cost = amount + commission
        
        if total_cost > self.cash:
            return {
                "success": False,
                "message": f"资金不足，需要¥{total_cost:.2f}，可用¥{self.cash:.2f}"
            }
        
        quantity = int(amount / current_price)
        actual_amount = quantity * current_price
        actual_commission = actual_amount * self.commission_rate
        total_actual_cost = actual_amount + actual_commission
        
        # 执行买入
        self.cash -= total_actual_cost
        
        # 更新持仓
        if symbol in self.positions:
            pos = self.positions[symbol]
            total_quantity = pos.quantity + quantity
            total_cost = pos.avg_price * pos.quantity + actual_amount
            new_avg_price = total_cost / total_quantity
            
            self.positions[symbol] = Position(
                symbol=symbol,
                quantity=total_quantity,
                avg_price=new_avg_price,
                current_price=current_price,
                market_value=total_quantity * current_price,
                pnl=(current_price - new_avg_price) * total_quantity,
                pnl_percent=(current_price - new_avg_price) / new_avg_price * 100,
                buy_date=pos.buy_date
            )
        else:
            self.positions[symbol] = Position(
                symbol=symbol,
                quantity=quantity,
                avg_price=current_price,
                current_price=current_price,
                market_value=quantity * current_price,
                pnl=0,
                pnl_percent=0,
                buy_date=datetime.now().strftime("%Y-%m-%d")
            )
        
        # 记录交易
        trade = Trade(
            trade_id=f"T{datetime.now().strftime('%Y%m%d%H%M%S')}",
            symbol=symbol,
            action="buy",
            price=current_price,
            quantity=quantity,
            amount=actual_amount,
            timestamp=datetime.now().isoformat(),
            strategy=strategy,
            commission=actual_commission
        )
        
        self.trades.append(trade)
        self._save_trade(trade)
        
        # 记录AI经验
        market_condition = self.detect_market_condition(symbol)
        indicators = self.get_technical_indicators(symbol)
        
        experience = TradingExperience(
            trade_id=trade.trade_id,
            symbol=symbol,
            action="buy",
            price=current_price,
            quantity=quantity,
            timestamp=trade.timestamp,
            strategy_used=strategy,
            market_condition=market_condition,
            technical_indicators=indicators
        )
        
        ai_experience.record_trade(experience)
        
        return {
            "success": True,
            "message": f"成功买入{symbol} {quantity}股，价格¥{current_price:.2f}，手续费¥{actual_commission:.2f}",
            "trade": asdict(trade),
            "position": asdict(self.positions[symbol])
        }
    
    def sell_stock(self, symbol: str, quantity: int = None, strategy: str = "manual") -> Dict:
        """卖出股票"""
        if symbol not in self.positions:
            return {
                "success": False,
                "message": f"未持有{symbol}"
            }
        
        pos = self.positions[symbol]
        if quantity is None:
            quantity = pos.quantity
        
        if quantity > pos.quantity:
            return {
                "success": False,
                "message": f"持仓不足，持有{pos.quantity}股，要卖出{quantity}股"
            }
        
        current_price = self.get_current_price(symbol)
        amount = quantity * current_price
        commission = amount * self.commission_rate
        net_amount = amount - commission
        
        # 执行卖出
        self.cash += net_amount
        
        # 更新持仓
        if quantity == pos.quantity:
            # 全部卖出
            pnl = (current_price - pos.avg_price) * quantity
            del self.positions[symbol]
        else:
            # 部分卖出
            remaining_quantity = pos.quantity - quantity
            self.positions[symbol] = Position(
                symbol=symbol,
                quantity=remaining_quantity,
                avg_price=pos.avg_price,
                current_price=current_price,
                market_value=remaining_quantity * current_price,
                pnl=(current_price - pos.avg_price) * remaining_quantity,
                pnl_percent=(current_price - pos.avg_price) / pos.avg_price * 100,
                buy_date=pos.buy_date
            )
            pnl = (current_price - pos.avg_price) * quantity
        
        # 记录交易
        trade = Trade(
            trade_id=f"T{datetime.now().strftime('%Y%m%d%H%M%S')}",
            symbol=symbol,
            action="sell",
            price=current_price,
            quantity=quantity,
            amount=amount,
            timestamp=datetime.now().isoformat(),
            strategy=strategy,
            commission=commission
        )
        
        self.trades.append(trade)
        self._save_trade(trade)
        
        # 更新AI经验（卖出时记录结果）
        buy_trades = [t for t in self.trades if t.symbol == symbol and t.action == "buy"]
        if buy_trades:
            # 找到对应的买入交易，更新经验
            buy_trade = buy_trades[-1]  # 最近的买入
            
            holding_days = (datetime.now() - datetime.fromisoformat(buy_trade.timestamp)).days
            return_rate = pnl / (pos.avg_price * quantity)
            success = pnl > 0
            
            # 查找并更新对应的经验记录
            for exp in ai_experience.experiences:
                if exp.trade_id == buy_trade.trade_id:
                    exp.result_pnl = pnl
                    exp.result_return = return_rate
                    exp.holding_days = holding_days
                    exp.success = success
                    ai_experience.record_trade(exp)  # 重新保存
                    break
        
        return {
            "success": True,
            "message": f"成功卖出{symbol} {quantity}股，价格¥{current_price:.2f}，盈亏¥{pnl:.2f}",
            "trade": asdict(trade),
            "pnl": pnl,
            "return_rate": (current_price - pos.avg_price) / pos.avg_price * 100
        }
    
    def auto_rebalance(self) -> Dict:
        """自动调仓"""
        if not self.pso_enabled:
            return {"success": False, "message": "粒子群选股未启用"}
        
        # 获取自选股列表
        universe = list(self.positions.keys()) if self.positions else ["000001.SZ", "600519.SH"]
        
        try:
            # 使用PSO选股
            result = pso_select(universe, top_n=5)
            selected = result.get("selected", [])
            
            if not selected:
                return {"success": False, "message": "PSO选股无结果"}
            
            # 卖出不在选股列表中的股票
            for symbol in list(self.positions.keys()):
                if symbol not in selected:
                    self.sell_stock(symbol, strategy="pso_rebalance")
            
            # 等权买入选中的股票
            available_cash = self.cash
            per_stock_amount = available_cash / len(selected)
            
            results = []
            for symbol in selected:
                if per_stock_amount > 1000:  # 最小买入金额
                    result = self.buy_stock(symbol, per_stock_amount, strategy="pso_rebalance")
                    results.append(result)
            
            self.last_rebalance = datetime.now()
            
            return {
                "success": True,
                "message": f"自动调仓完成，选中{len(selected)}只股票",
                "selected": selected,
                "trades": results
            }
            
        except Exception as e:
            return {"success": False, "message": f"自动调仓失败: {e}"}
    
    def get_ai_advice(self, symbol: str) -> Dict:
        """获取AI建议"""
        market_condition = self.detect_market_condition(symbol)
        indicators = self.get_technical_indicators(symbol)
        
        advice = ai_experience.get_ai_recommendation(symbol, indicators, market_condition)
        
        return advice
    
    def get_account_summary(self) -> Dict:
        """获取账户摘要"""
        # 更新所有持仓的当前价格
        total_market_value = 0
        for symbol, pos in self.positions.items():
            current_price = self.get_current_price(symbol)
            pos.current_price = current_price
            pos.market_value = pos.quantity * current_price
            pos.pnl = (current_price - pos.avg_price) * pos.quantity
            pos.pnl_percent = (current_price - pos.avg_price) / pos.avg_price * 100
            total_market_value += pos.market_value
        
        total_value = self.cash + total_market_value
        total_pnl = total_value - self.initial_cash
        total_return = total_pnl / self.initial_cash * 100
        
        return {
            "cash": self.cash,
            "market_value": total_market_value,
            "total_value": total_value,
            "total_pnl": total_pnl,
            "total_return": total_return,
            "positions": [asdict(pos) for pos in self.positions.values()],
            "recent_trades": [asdict(trade) for trade in self.trades[-10:]],
            "strategy_performance": ai_experience.get_strategy_performance()
        }
    
    def _save_trade(self, trade: Trade):
        """保存交易记录到数据库"""
        conn = sqlite3.connect("smart_trading.db")
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO trades VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            trade.trade_id, trade.symbol, trade.action, trade.price,
            trade.quantity, trade.amount, trade.timestamp, trade.strategy, trade.commission
        ))
        
        conn.commit()
        conn.close()


# 全局智能交易系统实例
smart_trading = SmartTradingSystem()
