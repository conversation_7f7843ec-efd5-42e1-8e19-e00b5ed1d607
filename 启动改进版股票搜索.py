#!/usr/bin/env python3
"""
启动改进版股票搜索系统
- 删除了加载按钮，自动加载
- 智能搜索中国股市
- 支持股票名称和代码输入
"""

import subprocess
import time
import requests
import os
from pathlib import Path


def check_service(url, name):
    """检查服务是否运行"""
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            print(f"✅ {name} 运行正常")
            return True
    except:
        pass
    print(f"❌ {name} 未运行")
    return False


def start_openbb_cn_api():
    """启动OpenBB-CN API服务"""
    print("🚀 启动OpenBB-CN API服务...")

    # 检查Python环境
    project_root = Path.cwd()
    python_exe = project_root / "openbb_env_312" / "Scripts" / "python.exe"

    if not python_exe.exists():
        print(f"❌ Python环境不存在: {python_exe}")
        print("请确保已创建虚拟环境 openbb_env_312")
        return False

    try:
        # 检查是否已经运行
        if check_service("http://127.0.0.1:6900", "OpenBB-CN API"):
            return True

        # 启动服务
        cmd = [str(python_exe), "-m", "uvicorn", "openbb_cn.api.app:app", "--host", "127.0.0.1", "--port", "6900", "--reload"]
        
        process = subprocess.Popen(
            cmd,
            cwd=Path.cwd(),
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
        )
        
        print(f"✅ OpenBB-CN API 启动成功 (PID: {process.pid})")
        
        # 等待服务启动
        print("⏳ 等待服务启动...")
        for i in range(30):  # 最多等待30秒
            time.sleep(1)
            if check_service("http://127.0.0.1:6900", "OpenBB-CN API"):
                print("🎉 OpenBB-CN API 启动并验证成功")
                return True
            print(f"⏳ 等待中... ({i+1}/30)")
        
        print("⚠️ 服务启动但验证失败")
        return False
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 改进版股票搜索系统启动器")
    print("=" * 60)
    print("✨ 新功能:")
    print("  - 🔍 智能搜索：输入股票名称或代码自动搜索")
    print("  - ⚡ 自动加载：无需点击加载按钮")
    print("  - 🇨🇳 中国股市：自动匹配上海/深圳交易所")
    print("  - 💡 实时反馈：输入即搜索，智能防抖")
    print("=" * 60)
    
    # 启动OpenBB-CN API
    if start_openbb_cn_api():
        print("\n🌐 服务地址:")
        print("- API文档: http://127.0.0.1:6900/docs")
        print("- 股票搜索界面: http://127.0.0.1:6900/")
        print("- 高级界面: http://127.0.0.1:6900/ui")
        
        print("\n💡 使用说明:")
        print("1. 打开浏览器访问: http://127.0.0.1:6900/ui")
        print("2. 在搜索框输入:")
        print("   - 股票名称: 平安银行、贵州茅台、五粮液")
        print("   - 6位代码: 000001、600519、000858")
        print("   - 完整代码: 000001.SZ、600519.SH")
        print("3. 系统会自动搜索并加载第一个匹配结果")
        print("4. 支持实时搜索，输入即可看到结果")
        
        print("\n🎯 测试建议:")
        test_cases = [
            "平安银行",
            "000001", 
            "贵州茅台",
            "600519",
            "五粮液",
            "000858.SZ"
        ]
        
        print("尝试搜索以下内容:")
        for i, case in enumerate(test_cases, 1):
            print(f"  {i}. {case}")
        
        print("\n🔧 技术改进:")
        print("- ✅ 删除加载按钮，搜索即加载")
        print("- ✅ 智能代码匹配(.SH/.SZ自动添加)")
        print("- ✅ 中文名称模糊搜索")
        print("- ✅ 实时搜索防抖(300-600ms)")
        print("- ✅ 搜索结果排序优化")
        print("- ✅ 用户体验改进(加载状态、错误提示)")
        
        print("\n按 Ctrl+C 停止服务")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务...")
    
    else:
        print("\n❌ 服务启动失败")
        print("\n🔧 可能的解决方案:")
        print("1. 检查端口6900是否被占用")
        print("2. 确保已安装所需依赖: pip install -r requirements.txt")
        print("3. 检查Tushare Token配置")
        print("4. 查看详细错误信息")


if __name__ == "__main__":
    main()
