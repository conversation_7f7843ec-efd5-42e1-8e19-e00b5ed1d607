interactions:
- request:
    body: null
    headers:
      Accept: '*/*'
      Accept-Encoding: gzip, deflate, br, zstd
      Connection: keep-alive
      Cookie: MOCK_COOKIE
    method: GET
    uri: https://ca.yahoo.com/?p=us
  response:
    body:
      encoding: utf-8
      string: !!binary |
        TU9DS19SRVNQT05TRQ==
    headers:
      Age: '0'
      Cache-Control: no-store, no-cache, max-age=0, private
      Content-Encoding: gzip
      Content-Security-Policy: MOCK_CSP
      Content-Type: text/html; charset=utf-8
      Date: Tue, 24 Jun 2025 18:05:42 GMT
      Expect-Ct: MOCK_EXPECT_CT
      Expires: '-1'
      Referrer-Policy: no-referrer-when-downgrade
      Server: ATS
      Strict-Transport-Security: max-age=31536000
      X-Content-Type-Options: nosniff
      X-Envoy-Upstream-Service-Time: '52'
      X-Frame-Options: SAMEORIGIN
      X-Xss-Protection: 1; mode=block
    status:
      code: 200
      message: ''
- request:
    body: null
    headers:
      Accept: '*/*'
      Accept-Encoding: gzip, deflate, br, zstd
      Connection: keep-alive
      Cookie: MOCK_COOKIE
    method: GET
    uri: https://query1.finance.yahoo.com/v1/test/getcrumb
  response:
    body:
      encoding: utf-8
      string: !!binary |
        QWZ1eks1WXBnRmo=
    headers:
      Age: '0'
      Cache-Control: private, max-age=60, stale-while-revalidate=30
      Content-Length: '11'
      Content-Type: text/plain;charset=utf-8
      Date: Tue, 24 Jun 2025 18:05:43 GMT
      Referrer-Policy: no-referrer-when-downgrade
      Server: ATS
      Strict-Transport-Security: max-age=31536000
      Vary: Origin,Accept-Encoding
      X-Content-Type-Options: nosniff
      X-Envoy-Decorator-Operation: MOCK_OPERATION
      X-Envoy-Upstream-Service-Time: '0'
      X-Frame-Options: SAMEORIGIN
      X-Xss-Protection: 1; mode=block
      Y-Rid: MOCK_RID
    status:
      code: 200
      message: ''
- request:
    body: null
    headers:
      Accept: '*/*'
      Accept-Encoding: gzip, deflate, br, zstd
      Connection: keep-alive
      Cookie: MOCK_COOKIE
    method: GET
    uri: https://query2.finance.yahoo.com/v10/finance/quoteSummary/AAPL?modules=financialData%2CquoteType%2CdefaultKeyStatistics%2CassetProfile%2CsummaryDetail&corsDomain=MOCK_CORS&formatted=false&symbol=AAPL&crumb=MOCK_CRUMB
  response:
    body:
      encoding: utf-8
      string: !!binary |
        eyJxdW90ZVN1bW1hcnkiOnsicmVzdWx0IjpbeyJhc3NldFByb2ZpbGUiOnsiYWRkcmVzczEiOiJP
        bmUgQXBwbGUgUGFyayBXYXkiLCJjaXR5IjoiQ3VwZXJ0aW5vIiwic3RhdGUiOiJDQSIsInppcCI6
        ****************************************************************************
        Iiwid2Vic2l0ZSI6Imh0dHBzOi8vd3d3LmFwcGxlLmNvbSIsImluZHVzdHJ5IjoiQ29uc3VtZXIg
        RWxlY3Ryb25pY3MiLCJpbmR1c3RyeUtleSI6ImNvbnN1bWVyLWVsZWN0cm9uaWNzIiwiaW5kdXN0
        cnlEaXNwIjoiQ29uc3VtZXIgRWxlY3Ryb25pY3MiLCJzZWN0b3IiOiJUZWNobm9sb2d5Iiwic2Vj
        dG9yS2V5IjoidGVjaG5vbG9neSIsInNlY3RvckRpc3AiOiJUZWNobm9sb2d5IiwibG9uZ0J1c2lu
        ZXNzU3VtbWFyeSI6IkFwcGxlIEluYy4gZGVzaWducywgbWFudWZhY3R1cmVzLCBhbmQgbWFya2V0
        cyBzbWFydHBob25lcywgcGVyc29uYWwgY29tcHV0ZXJzLCB0YWJsZXRzLCB3ZWFyYWJsZXMsIGFu
        ZCBhY2Nlc3NvcmllcyB3b3JsZHdpZGUuIFRoZSBjb21wYW55IG9mZmVycyBpUGhvbmUsIGEgbGlu
        ZSBvZiBzbWFydHBob25lczsgTWFjLCBhIGxpbmUgb2YgcGVyc29uYWwgY29tcHV0ZXJzOyBpUGFk
        LCBhIGxpbmUgb2YgbXVsdGktcHVycG9zZSB0YWJsZXRzOyBhbmQgd2VhcmFibGVzLCBob21lLCBh
        bmQgYWNjZXNzb3JpZXMgY29tcHJpc2luZyBBaXJQb2RzLCBBcHBsZSBUViwgQXBwbGUgV2F0Y2gs
        IEJlYXRzIHByb2R1Y3RzLCBhbmQgSG9tZVBvZC4gSXQgYWxzbyBwcm92aWRlcyBBcHBsZUNhcmUg
        c3VwcG9ydCBhbmQgY2xvdWQgc2VydmljZXM7IGFuZCBvcGVyYXRlcyB2YXJpb3VzIHBsYXRmb3Jt
        cywgaW5jbHVkaW5nIHRoZSBBcHAgU3RvcmUgdGhhdCBhbGxvdyBjdXN0b21lcnMgdG8gZGlzY292
        ZXIgYW5kIGRvd25sb2FkIGFwcGxpY2F0aW9ucyBhbmQgZGlnaXRhbCBjb250ZW50LCBzdWNoIGFz
        IGJvb2tzLCBtdXNpYywgdmlkZW8sIGdhbWVzLCBhbmQgcG9kY2FzdHMsIGFzIHdlbGwgYXMgYWR2
        ZXJ0aXNpbmcgc2VydmljZXMgaW5jbHVkZSB0aGlyZC1wYXJ0eSBsaWNlbnNpbmcgYXJyYW5nZW1l
        bnRzIGFuZCBpdHMgb3duIGFkdmVydGlzaW5nIHBsYXRmb3Jtcy4gSW4gYWRkaXRpb24sIHRoZSBj
        b21wYW55IG9mZmVycyB2YXJpb3VzIHN1YnNjcmlwdGlvbi1iYXNlZCBzZXJ2aWNlcywgc3VjaCBh
        cyBBcHBsZSBBcmNhZGUsIGEgZ2FtZSBzdWJzY3JpcHRpb24gc2VydmljZTsgQXBwbGUgRml0bmVz
        cyssIGEgcGVyc29uYWxpemVkIGZpdG5lc3Mgc2VydmljZTsgQXBwbGUgTXVzaWMsIHdoaWNoIG9m
        ZmVycyB1c2VycyBhIGN1cmF0ZWQgbGlzdGVuaW5nIGV4cGVyaWVuY2Ugd2l0aCBvbi1kZW1hbmQg
        cmFkaW8gc3RhdGlvbnM7IEFwcGxlIE5ld3MrLCBhIHN1YnNjcmlwdGlvbiBuZXdzIGFuZCBtYWdh
        emluZSBzZXJ2aWNlOyBBcHBsZSBUVissIHdoaWNoIG9mZmVycyBleGNsdXNpdmUgb3JpZ2luYWwg
        Y29udGVudDsgQXBwbGUgQ2FyZCwgYSBjby1icmFuZGVkIGNyZWRpdCBjYXJkOyBhbmQgQXBwbGUg
        UGF5LCBhIGNhc2hsZXNzIHBheW1lbnQgc2VydmljZSwgYXMgd2VsbCBhcyBsaWNlbnNlcyBpdHMg
        aW50ZWxsZWN0dWFsIHByb3BlcnR5LiBUaGUgY29tcGFueSBzZXJ2ZXMgY29uc3VtZXJzLCBhbmQg
        c21hbGwgYW5kIG1pZC1zaXplZCBidXNpbmVzc2VzOyBhbmQgdGhlIGVkdWNhdGlvbiwgZW50ZXJw
        cmlzZSwgYW5kIGdvdmVybm1lbnQgbWFya2V0cy4gSXQgZGlzdHJpYnV0ZXMgdGhpcmQtcGFydHkg
        YXBwbGljYXRpb25zIGZvciBpdHMgcHJvZHVjdHMgdGhyb3VnaCB0aGUgQXBwIFN0b3JlLiBUaGUg
        Y29tcGFueSBhbHNvIHNlbGxzIGl0cyBwcm9kdWN0cyB0aHJvdWdoIGl0cyByZXRhaWwgYW5kIG9u
        bGluZSBzdG9yZXMsIGFuZCBkaXJlY3Qgc2FsZXMgZm9yY2U7IGFuZCB0aGlyZC1wYXJ0eSBjZWxs
        dWxhciBuZXR3b3JrIGNhcnJpZXJzLCB3aG9sZXNhbGVycywgcmV0YWlsZXJzLCBhbmQgcmVzZWxs
        ZXJzLiBBcHBsZSBJbmMuIHdhcyBmb3VuZGVkIGluIDE5NzYgYW5kIGlzIGhlYWRxdWFydGVyZWQg
        aW4gQ3VwZXJ0aW5vLCBDYWxpZm9ybmlhLiIsImZ1bGxUaW1lRW1wbG95ZWVzIjoxNjQwMDAsImNv
        bXBhbnlPZmZpY2VycyI6W3sibWF4QWdlIjoxLCJuYW1lIjoiTXIuIFRpbW90aHkgRC4gQ29vayIs
        ImFnZSI6NjMsInRpdGxlIjoiQ0VPICYgRGlyZWN0b3IiLCJ5ZWFyQm9ybiI6MTk2MSwiZmlzY2Fs
        WWVhciI6MjAyNCwidG90YWxQYXkiOnsicmF3IjoxNjUyMDg1NiwiZm10IjoiMTYuNTJNIiwibG9u
        Z0ZtdCI6IjE2LDUyMCw4NTYifSwiZXhlcmNpc2VkVmFsdWUiOnsicmF3IjowLCJmbXQiOm51bGws
        ImxvbmdGbXQiOiIwIn0sInVuZXhlcmNpc2VkVmFsdWUiOnsicmF3IjowLCJmbXQiOm51bGwsImxv
        bmdGbXQiOiIwIn19LHsibWF4QWdlIjoxLCJuYW1lIjoiTXIuIEplZmZyZXkgRS4gV2lsbGlhbXMi
        LCJhZ2UiOjYwLCJ0aXRsZSI6IkNoaWVmIE9wZXJhdGluZyBPZmZpY2VyIiwieWVhckJvcm4iOjE5
        NjQsImZpc2NhbFllYXIiOjIwMjQsInRvdGFsUGF5Ijp7InJhdyI6NTAyMDczNywiZm10IjoiNS4w
        Mk0iLCJsb25nRm10IjoiNSwwMjAsNzM3In0sImV4ZXJjaXNlZFZhbHVlIjp7InJhdyI6MCwiZm10
        IjpudWxsLCJsb25nRm10IjoiMCJ9LCJ1bmV4ZXJjaXNlZFZhbHVlIjp7InJhdyI6MCwiZm10Ijpu
        dWxsLCJsb25nRm10IjoiMCJ9fSx7Im1heEFnZSI6MSwibmFtZSI6Ik1zLiBLYXRoZXJpbmUgTC4g
        QWRhbXMiLCJhZ2UiOjYwLCJ0aXRsZSI6IlNlbmlvciBWUCwgR2VuZXJhbCBDb3Vuc2VsICYgU2Vj
        cmV0YXJ5IiwieWVhckJvcm4iOjE5NjQsImZpc2NhbFllYXIiOjIwMjQsInRvdGFsUGF5Ijp7InJh
        dyI6NTAyMjE4MiwiZm10IjoiNS4wMk0iLCJsb25nRm10IjoiNSwwMjIsMTgyIn0sImV4ZXJjaXNl
        ZFZhbHVlIjp7InJhdyI6MCwiZm10IjpudWxsLCJsb25nRm10IjoiMCJ9LCJ1bmV4ZXJjaXNlZFZh
        bHVlIjp7InJhdyI6MCwiZm10IjpudWxsLCJsb25nRm10IjoiMCJ9fSx7Im1heEFnZSI6MSwibmFt
        ZSI6Ik1zLiBEZWlyZHJlICBPJ0JyaWVuIiwiYWdlIjo1NywidGl0bGUiOiJDaGllZiBQZW9wbGUg
        T2ZmaWNlciAmIFNlbmlvciBWUCBvZiBSZXRhaWwiLCJ5ZWFyQm9ybiI6MTk2NywiZmlzY2FsWWVh
        ciI6MjAyNCwidG90YWxQYXkiOnsicmF3Ijo1MDIyMTgyLCJmbXQiOiI1LjAyTSIsImxvbmdGbXQi
        OiI1LDAyMiwxODIifSwiZXhlcmNpc2VkVmFsdWUiOnsicmF3IjowLCJmbXQiOm51bGwsImxvbmdG
        bXQiOiIwIn0sInVuZXhlcmNpc2VkVmFsdWUiOnsicmF3IjowLCJmbXQiOm51bGwsImxvbmdGbXQi
        OiIwIn19LHsibWF4QWdlIjoxLCJuYW1lIjoiTXIuIEtldmFuICBQYXJla2giLCJhZ2UiOjUyLCJ0
        aXRsZSI6IlNlbmlvciBWUCAmIENGTyIsInllYXJCb3JuIjoxOTcyLCJmaXNjYWxZZWFyIjoyMDI0
        LCJleGVyY2lzZWRWYWx1ZSI6eyJyYXciOjAsImZtdCI6bnVsbCwibG9uZ0ZtdCI6IjAifSwidW5l
        eGVyY2lzZWRWYWx1ZSI6eyJyYXciOjAsImZtdCI6bnVsbCwibG9uZ0ZtdCI6IjAifX0seyJtYXhB
        Z2UiOjEsIm5hbWUiOiJNci4gQ2hyaXMgIEtvbmRvIiwidGl0bGUiOiJTZW5pb3IgRGlyZWN0b3Ig
        b2YgQ29ycG9yYXRlIEFjY291bnRpbmciLCJmaXNjYWxZZWFyIjoyMDI0LCJleGVyY2lzZWRWYWx1
        ZSI6eyJyYXciOjAsImZtdCI6bnVsbCwibG9uZ0ZtdCI6IjAifSwidW5leGVyY2lzZWRWYWx1ZSI6
        eyJyYXciOjAsImZtdCI6bnVsbCwibG9uZ0ZtdCI6IjAifX0seyJtYXhBZ2UiOjEsIm5hbWUiOiJT
        dWhhc2luaSAgQ2hhbmRyYW1vdWxpIiwidGl0bGUiOiJEaXJlY3RvciBvZiBJbnZlc3RvciBSZWxh
        dGlvbnMiLCJmaXNjYWxZZWFyIjoyMDI0LCJleGVyY2lzZWRWYWx1ZSI6eyJyYXciOjAsImZtdCI6
        bnVsbCwibG9uZ0ZtdCI6IjAifSwidW5leGVyY2lzZWRWYWx1ZSI6eyJyYXciOjAsImZtdCI6bnVs
        bCwibG9uZ0ZtdCI6IjAifX0seyJtYXhBZ2UiOjEsIm5hbWUiOiJNcy4gS3Jpc3RpbiBIdWd1ZXQg
        UXVheWxlIiwidGl0bGUiOiJWaWNlIFByZXNpZGVudCBvZiBXb3JsZHdpZGUgQ29tbXVuaWNhdGlv
        bnMiLCJmaXNjYWxZZWFyIjoyMDI0LCJleGVyY2lzZWRWYWx1ZSI6eyJyYXciOjAsImZtdCI6bnVs
        bCwibG9uZ0ZtdCI6IjAifSwidW5leGVyY2lzZWRWYWx1ZSI6eyJyYXciOjAsImZtdCI6bnVsbCwi
        bG9uZ0ZtdCI6IjAifX0seyJtYXhBZ2UiOjEsIm5hbWUiOiJNci4gR3JlZyAgSm9zd2lhayIsInRp
        dGxlIjoiU2VuaW9yIFZpY2UgUHJlc2lkZW50IG9mIFdvcmxkd2lkZSBNYXJrZXRpbmciLCJmaXNj
        YWxZZWFyIjoyMDI0LCJleGVyY2lzZWRWYWx1ZSI6eyJyYXciOjAsImZtdCI6bnVsbCwibG9uZ0Zt
        dCI6IjAifSwidW5leGVyY2lzZWRWYWx1ZSI6eyJyYXciOjAsImZtdCI6bnVsbCwibG9uZ0ZtdCI6
        IjAifX0seyJtYXhBZ2UiOjEsIm5hbWUiOiJNci4gQWRyaWFuICBQZXJpY2EiLCJhZ2UiOjUwLCJ0
        aXRsZSI6IlZpY2UgUHJlc2lkZW50IG9mIENvcnBvcmF0ZSBEZXZlbG9wbWVudCIsInllYXJCb3Ju
        IjoxOTc0LCJmaXNjYWxZZWFyIjoyMDI0LCJleGVyY2lzZWRWYWx1ZSI6eyJyYXciOjAsImZtdCI6
        bnVsbCwibG9uZ0ZtdCI6IjAifSwidW5leGVyY2lzZWRWYWx1ZSI6eyJyYXciOjAsImZtdCI6bnVs
        bCwibG9uZ0ZtdCI6IjAifX1dLCJhdWRpdFJpc2siOjcsImJvYXJkUmlzayI6MSwiY29tcGVuc2F0
        aW9uUmlzayI6Mywic2hhcmVIb2xkZXJSaWdodHNSaXNrIjoxLCJvdmVyYWxsUmlzayI6MSwiZ292
        ZXJuYW5jZUVwb2NoRGF0ZSI6MTc0ODczNjAwMCwiY29tcGVuc2F0aW9uQXNPZkVwb2NoRGF0ZSI6
        MTczNTYwMzIwMCwiaXJXZWJzaXRlIjoiaHR0cDovL2ludmVzdG9yLmFwcGxlLmNvbS8iLCJleGVj
        dXRpdmVUZWFtIjpbXSwibWF4QWdlIjo4NjQwMH0sInN1bW1hcnlEZXRhaWwiOnsibWF4QWdlIjox
        LCJwcmljZUhpbnQiOjIsInByZXZpb3VzQ2xvc2UiOjIwMS41LCJvcGVuIjoyMDIuNTksImRheUxv
        dyI6MjAwLjIsImRheUhpZ2giOjIwMy40NCwicmVndWxhck1hcmtldFByZXZpb3VzQ2xvc2UiOjIw
        MS41LCJyZWd1bGFyTWFya2V0T3BlbiI6MjAyLjU5LCJyZWd1bGFyTWFya2V0RGF5TG93IjoyMDAu
        MiwicmVndWxhck1hcmtldERheUhpZ2giOjIwMy40NCwiZGl2aWRlbmRSYXRlIjoxLjA0LCJkaXZp
        ZGVuZFlpZWxkIjowLjAwNTIsImV4RGl2aWRlbmREYXRlIjoxNzQ3MDA4MDAwLCJwYXlvdXRSYXRp
        byI6MC4xNTU4LCJmaXZlWWVhckF2Z0RpdmlkZW5kWWllbGQiOjAuNTYsImJldGEiOjEuMjExLCJ0
        cmFpbGluZ1BFIjozMS40MTkxMjgsImZvcndhcmRQRSI6MjQuMzExMDcsInZvbHVtZSI6MzEzNzI4
        NzMsInJlZ3VsYXJNYXJrZXRWb2x1bWUiOjMxMzcyODczLCJhdmVyYWdlVm9sdW1lIjo2MTM1NjI3
        NSwiYXZlcmFnZVZvbHVtZTEwZGF5cyI6NTYzNTU3NjAsImF2ZXJhZ2VEYWlseVZvbHVtZTEwRGF5
        Ijo1NjM1NTc2MCwiYmlkIjoyMDEuODQsImFzayI6MjAyLjE3LCJiaWRTaXplIjozMDAsImFza1Np
        emUiOjMwMCwibWFya2V0Q2FwIjozMDE3NDA0ODQxOTg0LCJmaWZ0eVR3b1dlZWtMb3ciOjE2OS4y
        MSwiZmlmdHlUd29XZWVrSGlnaCI6MjYwLjEsInByaWNlVG9TYWxlc1RyYWlsaW5nMTJNb250aHMi
        OjcuNTM2NjE2LCJmaWZ0eURheUF2ZXJhZ2UiOjIwMi40MDgsInR3b0h1bmRyZWREYXlBdmVyYWdl
        IjoyMjMuNzM4NTYsInRyYWlsaW5nQW5udWFsRGl2aWRlbmRSYXRlIjoxLjAsInRyYWlsaW5nQW5u
        dWFsRGl2aWRlbmRZaWVsZCI6MC4wMDQ5NjI3NzksImN1cnJlbmN5IjoiVVNEIiwiZnJvbUN1cnJl
        bmN5IjpudWxsLCJ0b0N1cnJlbmN5IjpudWxsLCJsYXN0TWFya2V0IjpudWxsLCJjb2luTWFya2V0
        Q2FwTGluayI6bnVsbCwiYWxnb3JpdGhtIjpudWxsLCJ0cmFkZWFibGUiOmZhbHNlfSwiZGVmYXVs
        dEtleVN0YXRpc3RpY3MiOnsibWF4QWdlIjoxLCJwcmljZUhpbnQiOjIsImVudGVycHJpc2VWYWx1
        ZSI6MzA1OTI1NjkxODAxNiwiZm9yd2FyZFBFIjoyNS43MTAzODQsInByb2ZpdE1hcmdpbnMiOjAu
        MjQzMDEsImZsb2F0U2hhcmVzIjoxNDkxMTQ4MDYwNCwic2hhcmVzT3V0c3RhbmRpbmciOjE0OTM1
        Nzk5ODA4LCJzaGFyZXNTaG9ydCI6OTQ4Mjg0NDMsInNoYXJlc1Nob3J0UHJpb3JNb250aCI6MTA4
        NTk4NzY3LCJzaGFyZXNTaG9ydFByZXZpb3VzTW9udGhEYXRlIjoxNzQ1OTcxMjAwLCJkYXRlU2hv
        cnRJbnRlcmVzdCI6MTc0ODU2MzIwMCwic2hhcmVzUGVyY2VudFNoYXJlc091dCI6MC4wMDYzLCJo
        ZWxkUGVyY2VudEluc2lkZXJzIjowLjAyMDg1LCJoZWxkUGVyY2VudEluc3RpdHV0aW9ucyI6MC42
        Mjg5NSwic2hvcnRSYXRpbyI6MS42Nywic2hvcnRQZXJjZW50T2ZGbG9hdCI6MC4wMDY0LCJiZXRh
        IjoxLjIxMSwiaW1wbGllZFNoYXJlc091dHN0YW5kaW5nIjoxNDkzNTc5OTgwOCwiY2F0ZWdvcnki
        Om51bGwsImJvb2tWYWx1ZSI6NC40NzEsInByaWNlVG9Cb29rIjo0NS4xODU2NCwiZnVuZEZhbWls
        eSI6bnVsbCwibGVnYWxUeXBlIjpudWxsLCJsYXN0RmlzY2FsWWVhckVuZCI6MTcyNzQ4MTYwMCwi
        bmV4dEZpc2NhbFllYXJFbmQiOjE3NTkwMTc2MDAsIm1vc3RSZWNlbnRRdWFydGVyIjoxNzQzMjA2
        NDAwLCJlYXJuaW5nc1F1YXJ0ZXJseUdyb3d0aCI6MC4wNDgsIm5ldEluY29tZVRvQ29tbW9uIjo5
        NzI5NDAwMDEyOCwidHJhaWxpbmdFcHMiOjYuNDMsImZvcndhcmRFcHMiOjguMzEsImxhc3RTcGxp
        dEZhY3RvciI6IjQ6MSIsImxhc3RTcGxpdERhdGUiOjE1OTg4MzIwMDAsImVudGVycHJpc2VUb1Jl
        dmVudWUiOjcuNjQxLCJlbnRlcnByaXNlVG9FYml0ZGEiOjIyLjAzLCI1MldlZWtDaGFuZ2UiOi0w
        LjAzNjIwNzk3NCwiU2FuZFA1MldlZWtDaGFuZ2UiOjAuMTAxNjM0NjIsImxhc3REaXZpZGVuZFZh
        bHVlIjowLjI2LCJsYXN0RGl2aWRlbmREYXRlIjoxNzQ3MDA4MDAwLCJsYXRlc3RTaGFyZUNsYXNz
        IjpudWxsLCJsZWFkSW52ZXN0b3IiOm51bGx9LCJxdW90ZVR5cGUiOnsiZXhjaGFuZ2UiOiJOTVMi
        LCJxdW90ZVR5cGUiOiJFUVVJVFkiLCJzeW1ib2wiOiJBQVBMIiwidW5kZXJseWluZ1N5bWJvbCI6
        IkFBUEwiLCJzaG9ydE5hbWUiOiJBcHBsZSBJbmMuIiwibG9uZ05hbWUiOiJBcHBsZSBJbmMuIiwi
        Zmlyc3RUcmFkZURhdGVFcG9jaFV0YyI6MzQ1NDc5NDAwLCJ0aW1lWm9uZUZ1bGxOYW1lIjoiQW1l
        cmljYS9OZXdfWW9yayIsInRpbWVab25lU2hvcnROYW1lIjoiRURUIiwidXVpZCI6IjhiMTBlNGFl
        LTllZWItMzY4NC05MjFhLTlhYjI3ZTRkODdhYSIsIm1lc3NhZ2VCb2FyZElkIjoiZmlubWJfMjQ5
        MzciLCJnbXRPZmZTZXRNaWxsaXNlY29uZHMiOi0xNDQwMDAwMCwibWF4QWdlIjoxfSwiZmluYW5j
        aWFsRGF0YSI6eyJtYXhBZ2UiOjg2NDAwLCJjdXJyZW50UHJpY2UiOjIwMi4wMjUsInRhcmdldEhp
        Z2hQcmljZSI6MzAwLjAsInRhcmdldExvd1ByaWNlIjoxNzAuNjIsInRhcmdldE1lYW5QcmljZSI6
        MjI4Ljg1MzI2LCJ0YXJnZXRNZWRpYW5QcmljZSI6MjMyLjUsInJlY29tbWVuZGF0aW9uTWVhbiI6
        Mi4xMDg3LCJyZWNvbW1lbmRhdGlvbktleSI6ImJ1eSIsIm51bWJlck9mQW5hbHlzdE9waW5pb25z
        Ijo0MCwidG90YWxDYXNoIjo0ODQ5Nzk5OTg3MiwidG90YWxDYXNoUGVyU2hhcmUiOjMuMjQ3LCJl
        Yml0ZGEiOjEzODg2NTk5OTg3MiwidG90YWxEZWJ0Ijo5ODE4NjAwMjQzMiwicXVpY2tSYXRpbyI6
        ****************************************************************************
        YnRUb0VxdWl0eSI6MTQ2Ljk5NCwicmV2ZW51ZVBlclNoYXJlIjoyNi40NTUsInJldHVybk9uQXNz
        ZXRzIjowLjIzODA5OTk5LCJyZXR1cm5PbkVxdWl0eSI6MS4zODAxNSwiZ3Jvc3NQcm9maXRzIjox
        ODY2OTkwMDU5NTIsImZyZWVDYXNoZmxvdyI6OTcyNTE1MDAwMzIsIm9wZXJhdGluZ0Nhc2hmbG93
        IjoxMDk1NTU5OTg3MjAsImVhcm5pbmdzR3Jvd3RoIjowLjA3OCwicmV2ZW51ZUdyb3d0aCI6MC4w
        NTEsImdyb3NzTWFyZ2lucyI6MC40NjYzMiwiZWJpdGRhTWFyZ2lucyI6MC4zNDY4NSwib3BlcmF0
        aW5nTWFyZ2lucyI6MC4zMTAyODk5OCwicHJvZml0TWFyZ2lucyI6MC4yNDMwMSwiZmluYW5jaWFs
        Q3VycmVuY3kiOiJVU0QifX1dLCJlcnJvciI6bnVsbH19
    headers:
      Age: '0'
      Cache-Control: public, max-age=1, stale-while-revalidate=9
      Content-Encoding: gzip
      Content-Length: '3119'
      Content-Type: application/json;charset=utf-8
      Date: Tue, 24 Jun 2025 18:05:43 GMT
      Referrer-Policy: no-referrer-when-downgrade
      Server: ATS
      Strict-Transport-Security: max-age=31536000
      Vary: Origin,Accept-Encoding
      X-Content-Type-Options: nosniff
      X-Envoy-Decorator-Operation: MOCK_OPERATION
      X-Envoy-Upstream-Service-Time: '5'
      X-Frame-Options: SAMEORIGIN
      X-Xss-Protection: 1; mode=block
      Y-Rid: MOCK_RID
    status:
      code: 200
      message: ''
- request:
    body: null
    headers:
      Accept: '*/*'
      Accept-Encoding: gzip, deflate, br, zstd
      Connection: keep-alive
      Cookie: MOCK_COOKIE
    method: GET
    uri: https://query1.finance.yahoo.com/v7/finance/quote?symbols=AAPL&formatted=false&crumb=MOCK_CRUMB
  response:
    body:
      encoding: utf-8
      string: !!binary |
        eyJxdW90ZVJlc3BvbnNlIjp7InJlc3VsdCI6W3sibGFuZ3VhZ2UiOiJlbi1VUyIsInJlZ2lvbiI6
        IlVTIiwicXVvdGVUeXBlIjoiRVFVSVRZIiwidHlwZURpc3AiOiJFcXVpdHkiLCJxdW90ZVNvdXJj
        ZU5hbWUiOiJOYXNkYXEgUmVhbCBUaW1lIFByaWNlIiwidHJpZ2dlcmFibGUiOnRydWUsImN1c3Rv
        bVByaWNlQWxlcnRDb25maWRlbmNlIjoiSElHSCIsInByaWNlSGludCI6MiwicmVndWxhck1hcmtl
        dENoYW5nZSI6MC41MjQ5OTM5LCJyZWd1bGFyTWFya2V0RGF5SGlnaCI6MjAzLjQ0LCJyZWd1bGFy
        TWFya2V0RGF5UmFuZ2UiOiIyMDAuMiAtIDIwMy40NCIsInJlZ3VsYXJNYXJrZXREYXlMb3ciOjIw
        MC4yLCJyZWd1bGFyTWFya2V0Vm9sdW1lIjozMTM3Mjg3MywicmVndWxhck1hcmtldFByZXZpb3Vz
        Q2xvc2UiOjIwMS41LCJiaWQiOjIwMS44NCwiYXNrIjoyMDIuMTcsImJpZFNpemUiOjMsImFza1Np
        emUiOjMsImZ1bGxFeGNoYW5nZU5hbWUiOiJOYXNkYXFHUyIsImZpbmFuY2lhbEN1cnJlbmN5Ijoi
        VVNEIiwicmVndWxhck1hcmtldE9wZW4iOjIwMi41OSwiYXZlcmFnZURhaWx5Vm9sdW1lM01vbnRo
        Ijo2MTM1NjI3NSwiYXZlcmFnZURhaWx5Vm9sdW1lMTBEYXkiOjU2MzU1NzYwLCJmaWZ0eVR3b1dl
        ZWtMb3dDaGFuZ2UiOjMyLjgxNDk4NywiZmlmdHlUd29XZWVrTG93Q2hhbmdlUGVyY2VudCI6MC4x
        OTM5MzA1NCwiZmlmdHlUd29XZWVrUmFuZ2UiOiIxNjkuMjEgLSAyNjAuMSIsImZpZnR5VHdvV2Vl
        a0hpZ2hDaGFuZ2UiOi01OC4wNzUwMTIsImZpZnR5VHdvV2Vla0hpZ2hDaGFuZ2VQZXJjZW50Ijot
        MC4yMjMyNzk1NSwiZmlmdHlUd29XZWVrTG93IjoxNjkuMjEsImZpZnR5VHdvV2Vla0hpZ2giOjI2
        MC4xLCJmaWZ0eVR3b1dlZWtDaGFuZ2VQZXJjZW50IjotMy42MjA3OTc0LCJkaXZpZGVuZERhdGUi
        OjE3NDcyNjcyMDAsImVhcm5pbmdzVGltZXN0YW1wIjoxNzQ2MTMxNDAwLCJlYXJuaW5nc1RpbWVz
        dGFtcFN0YXJ0IjoxNzUzODczMTQwLCJlYXJuaW5nc1RpbWVzdGFtcEVuZCI6MTc1NDMwODgwMCwi
        ZWFybmluZ3NDYWxsVGltZXN0YW1wU3RhcnQiOjE3NDYxMzMyMDAsImVhcm5pbmdzQ2FsbFRpbWVz
        dGFtcEVuZCI6MTc0NjEzMzIwMCwiaXNFYXJuaW5nc0RhdGVFc3RpbWF0ZSI6dHJ1ZSwidHJhaWxp
        bmdBbm51YWxEaXZpZGVuZFJhdGUiOjEuMCwidHJhaWxpbmdQRSI6MzEuNDE5MTI4LCJkaXZpZGVu
        ZFJhdGUiOjEuMDQsInRyYWlsaW5nQW5udWFsRGl2aWRlbmRZaWVsZCI6MC4wMDQ5NjI3NzksImRp
        dmlkZW5kWWllbGQiOjAuNTIsImVwc1RyYWlsaW5nVHdlbHZlTW9udGhzIjo2LjQzLCJlcHNGb3J3
        YXJkIjo4LjMxLCJlcHNDdXJyZW50WWVhciI6Ny4xODk1NiwicHJpY2VFcHNDdXJyZW50WWVhciI6
        MjguMDk5NzcxLCJzaGFyZXNPdXRzdGFuZGluZyI6MTQ5MzU3OTk4MDgsImJvb2tWYWx1ZSI6NC40
        NzEsImZpZnR5RGF5QXZlcmFnZSI6MjAyLjQwOCwiZmlmdHlEYXlBdmVyYWdlQ2hhbmdlIjotMC4z
        ODMwMTA4NiwiZmlmdHlEYXlBdmVyYWdlQ2hhbmdlUGVyY2VudCI6LTAuMDAxODkyMjcxMywidHdv
        SHVuZHJlZERheUF2ZXJhZ2UiOjIyMy43Mzg1NiwidHdvSHVuZHJlZERheUF2ZXJhZ2VDaGFuZ2Ui
        Oi0yMS43MTM1NjIsInR3b0h1bmRyZWREYXlBdmVyYWdlQ2hhbmdlUGVyY2VudCI6LTAuMDk3MDQ4
        ODIsIm1hcmtldENhcCI6MzAxNzQwNDg0MTk4NCwiZm9yd2FyZFBFIjoyNC4zMTEwNywicHJpY2VU
        b0Jvb2siOjQ1LjE4NTY0LCJzb3VyY2VJbnRlcnZhbCI6MTUsImV4Y2hhbmdlRGF0YURlbGF5ZWRC
        eSI6MCwiYXZlcmFnZUFuYWx5c3RSYXRpbmciOiIyLjEgLSBCdXkiLCJjdXJyZW5jeSI6IlVTRCIs
        InRyYWRlYWJsZSI6ZmFsc2UsImNyeXB0b1RyYWRlYWJsZSI6ZmFsc2UsInJlZ3VsYXJNYXJrZXRD
        aGFuZ2VQZXJjZW50IjowLjI2MDU0Mjg3LCJyZWd1bGFyTWFya2V0UHJpY2UiOjIwMi4wMjUsInNo
        b3J0TmFtZSI6IkFwcGxlIEluYy4iLCJsb25nTmFtZSI6IkFwcGxlIEluYy4iLCJjb3Jwb3JhdGVB
        Y3Rpb25zIjpbXSwicmVndWxhck1hcmtldFRpbWUiOjE3NTA3ODgzNDIsImV4Y2hhbmdlIjoiTk1T
        IiwibWVzc2FnZUJvYXJkSWQiOiJmaW5tYl8yNDkzNyIsImV4Y2hhbmdlVGltZXpvbmVOYW1lIjoi
        QW1lcmljYS9OZXdfWW9yayIsImV4Y2hhbmdlVGltZXpvbmVTaG9ydE5hbWUiOiJFRFQiLCJnbXRP
        ZmZTZXRNaWxsaXNlY29uZHMiOi0xNDQwMDAwMCwibWFya2V0IjoidXNfbWFya2V0IiwiZXNnUG9w
        dWxhdGVkIjpmYWxzZSwibWFya2V0U3RhdGUiOiJSRUdVTEFSIiwiaGFzUHJlUG9zdE1hcmtldERh
        dGEiOnRydWUsImZpcnN0VHJhZGVEYXRlTWlsbGlzZWNvbmRzIjozNDU0Nzk0MDAwMDAsImRpc3Bs
        YXlOYW1lIjoiQXBwbGUiLCJzeW1ib2wiOiJBQVBMIn1dLCJlcnJvciI6bnVsbH19
    headers:
      Age: '0'
      Cache-Control: public, max-age=1, stale-while-revalidate=9
      Content-Encoding: gzip
      Content-Length: '1127'
      Content-Type: application/json;charset=utf-8
      Date: Tue, 24 Jun 2025 18:05:43 GMT
      Referrer-Policy: no-referrer-when-downgrade
      Server: ATS
      Strict-Transport-Security: max-age=31536000
      Vary: Origin,Accept-Encoding
      X-Content-Type-Options: nosniff
      X-Envoy-Decorator-Operation: MOCK_OPERATION
      X-Envoy-Upstream-Service-Time: '5'
      X-Frame-Options: SAMEORIGIN
      X-Xss-Protection: 1; mode=block
      Y-Rid: MOCK_RID
    status:
      code: 200
      message: ''
- request:
    body: null
    headers:
      Accept: '*/*'
      Accept-Encoding: gzip, deflate, br, zstd
      Connection: keep-alive
      Cookie: MOCK_COOKIE
    method: GET
    uri: https://query1.finance.yahoo.com/ws/fundamentals-timeseries/v1/finance/timeseries/AAPL?symbol=AAPL&type=trailingPegRatio&period1=MOCK_PERIOD_1&period2=MOCK_PERIOD_2&crumb=MOCK_CRUMB
  response:
    body:
      encoding: utf-8
      string: !!binary |
        eyJ0aW1lc2VyaWVzIjp7InJlc3VsdCI6W3sibWV0YSI6eyJzeW1ib2wiOlsiQUFQTCJdLCJ0eXBl
        IjpbInRyYWlsaW5nUGVnUmF0aW8iXX0sInRpbWVzdGFtcCI6WzE3NTA2MzY4MDBdLCJ0cmFpbGlu
        Z1BlZ1JhdGlvIjpbeyJkYXRhSWQiOjE0MDIxLCJhc09mRGF0ZSI6IjIwMjUtMDYtMjMiLCJwZXJp
        b2RUeXBlIjoiVFRNIiwicmVwb3J0ZWRWYWx1ZSI6eyJyYXciOjEuODI5NSwiZm10IjoiMS44MyJ9
        fV19XSwiZXJyb3IiOm51bGx9fQ==
    headers:
      Age: '1'
      Cache-Control: public, max-age=60, stale-while-revalidate=30
      Content-Length: '247'
      Content-Type: application/json;charset=utf-8
      Date: Tue, 24 Jun 2025 18:05:44 GMT
      Referrer-Policy: no-referrer-when-downgrade
      Server: ATS
      Strict-Transport-Security: max-age=31536000
      Vary: Origin,Accept-Encoding
      X-Content-Type-Options: nosniff
      X-Envoy-Decorator-Operation: MOCK_OPERATION
      X-Envoy-Upstream-Service-Time: '2'
      X-Frame-Options: SAMEORIGIN
      X-Xss-Protection: 1; mode=block
      Y-Rid: MOCK_RID
    status:
      code: 200
      message: ''
