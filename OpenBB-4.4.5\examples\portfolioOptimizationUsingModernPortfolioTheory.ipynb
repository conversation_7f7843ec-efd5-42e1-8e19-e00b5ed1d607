{"cells": [{"cell_type": "markdown", "metadata": {"id": "HRMC-0N0sSjJ"}, "source": ["## Portfolio Optimization Using Modern Portfolio Theory\n", "\n", "#### Description\n", "This notebook utilizes OpenBB’s data for portfolio optimization based on MPT principles. We would be optimizing a portfolio of top 10 crypto assets, using the daily close data from 1st october 2023 to 1st october 2024.\n", "\n", "The portfolio optimization would be done using the mean-variance approach. The mean-variance approach helps determine the optimal allocation of assets in a portfolio to minimize overall risk while maximizing expected returns.\n", "\n", "#### Author\n", "[<PERSON>](https://github.com/ambroseikpele)\n", "\n", "[![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/OpenBB-Finance/OpenBB/blob/develop/examples/[Notebook_Name].ipynb)"]}, {"cell_type": "markdown", "source": ["### Introduction"], "metadata": {"id": "-Ih2c6xRxklu"}}, {"cell_type": "markdown", "source": ["Modern Portfolio Theory (MPT) is a mathematical framework for constructing a portfolio of assets to maximize expected return based on a given level of risk. In this notebook, we will implement MPT to construct an optimal portfolio with minimum volatility using a selection of the top cryptocurrencies as our assets. The notebook will fetch historical price data for these assets, calculate the portfolio's expected return and risk, and visualize the optimal portfolio based on risk-return trade-offs."], "metadata": {"id": "mWd0TC0DxZm-"}}, {"cell_type": "markdown", "source": ["Install external packages"], "metadata": {"id": "_rMPfEH2KJjA"}}, {"cell_type": "code", "source": ["!pip install openbb\n", "!pip install PyPortfolioOpt"], "metadata": {"id": "iexQsZ1XvYa8"}, "execution_count": 17, "outputs": []}, {"cell_type": "markdown", "source": ["Import necessary packages"], "metadata": {"id": "CH4i_WQGRmG-"}}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "B8m_9BassSjK"}, "outputs": [], "source": ["from openbb import obb\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "from pypfopt import EfficientFrontier\n", "from pypfopt import CovarianceShrinkage, CLA, expected_returns"]}, {"cell_type": "markdown", "source": ["### Define the assets and fetch the data"], "metadata": {"id": "w3XB7egmzlmI"}}, {"cell_type": "markdown", "source": ["Fetch the daily data of the top crypto currencies for a period of one year using openbb"], "metadata": {"id": "SyF2ROKDyKdJ"}}, {"cell_type": "code", "source": ["top_crypto= ['ADA-USD', 'BNB-USD', 'BTC-USD', 'DOT-USD', 'ETH-USD', 'LTC-USD','MATIC-USD', 'SOL-USD', 'TRX-USD', 'XRP-USD']\n", "\n", "ohlc_data= obb.crypto.price.historical(top_crypto, provider=\"yfinance\", interval='1d', start_date='2023-10-01', end_date='2024-10-01').to_df()\n", "ohlc_data"], "metadata": {"id": "C3UwCtY8vDaQ", "colab": {"base_uri": "https://localhost:8080/", "height": 455}, "outputId": "fd4a3544-32f9-49c1-d7ad-bca9efa88baa"}, "execution_count": 2, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                    open          high           low         close  \\\n", "date                                                                 \n", "2023-10-01      0.254043      0.267471      0.254019      0.265895   \n", "2023-10-01    214.800323    219.133835    213.506516    218.047134   \n", "2023-10-01  26967.396484  28047.238281  26965.093750  27983.750000   \n", "2023-10-01      4.105477      4.279937      4.103880      4.261009   \n", "2023-10-01   1671.161499   1750.595703   1670.082153   1733.810425   \n", "...                  ...           ...           ...           ...   \n", "2024-09-30     69.314308     69.321297     66.454277     66.820450   \n", "2024-09-30      0.421419      0.421627      0.394318      0.395917   \n", "2024-09-30    158.632416    159.508926    152.019836    152.618469   \n", "2024-09-30      0.156474      0.156746      0.154867      0.155915   \n", "2024-09-30      0.641945      0.652411      0.610951      0.611492   \n", "\n", "                  volume     symbol  \n", "date                                 \n", "2023-10-01  1.650882e+08    ADA-USD  \n", "2023-10-01  3.874081e+08    BNB-USD  \n", "2023-10-01  9.503917e+09    BTC-USD  \n", "2023-10-01  8.294334e+07    DOT-USD  \n", "2023-10-01  5.054880e+09    ETH-USD  \n", "...                  ...        ...  \n", "2024-09-30  3.003743e+08    LTC-USD  \n", "2024-09-30  3.730373e+07  MATIC-USD  \n", "2024-09-30  2.376781e+09    SOL-USD  \n", "2024-09-30  3.565544e+08    TRX-USD  \n", "2024-09-30  2.051369e+09    XRP-USD  \n", "\n", "[3660 rows x 6 columns]"], "text/html": ["\n", "  <div id=\"df-879083e7-465e-4e83-b820-f0b469f7dbb7\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>symbol</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-10-01</th>\n", "      <td>0.254043</td>\n", "      <td>0.267471</td>\n", "      <td>0.254019</td>\n", "      <td>0.265895</td>\n", "      <td>1.650882e+08</td>\n", "      <td>ADA-USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-10-01</th>\n", "      <td>214.800323</td>\n", "      <td>219.133835</td>\n", "      <td>213.506516</td>\n", "      <td>218.047134</td>\n", "      <td>3.874081e+08</td>\n", "      <td>BNB-USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-10-01</th>\n", "      <td>26967.396484</td>\n", "      <td>28047.238281</td>\n", "      <td>26965.093750</td>\n", "      <td>27983.750000</td>\n", "      <td>9.503917e+09</td>\n", "      <td>BTC-USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-10-01</th>\n", "      <td>4.105477</td>\n", "      <td>4.279937</td>\n", "      <td>4.103880</td>\n", "      <td>4.261009</td>\n", "      <td>8.294334e+07</td>\n", "      <td>DOT-USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-10-01</th>\n", "      <td>1671.161499</td>\n", "      <td>1750.595703</td>\n", "      <td>1670.082153</td>\n", "      <td>1733.810425</td>\n", "      <td>5.054880e+09</td>\n", "      <td>ETH-USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-30</th>\n", "      <td>69.314308</td>\n", "      <td>69.321297</td>\n", "      <td>66.454277</td>\n", "      <td>66.820450</td>\n", "      <td>3.003743e+08</td>\n", "      <td>LTC-USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-30</th>\n", "      <td>0.421419</td>\n", "      <td>0.421627</td>\n", "      <td>0.394318</td>\n", "      <td>0.395917</td>\n", "      <td>3.730373e+07</td>\n", "      <td>MATIC-USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-30</th>\n", "      <td>158.632416</td>\n", "      <td>159.508926</td>\n", "      <td>152.019836</td>\n", "      <td>152.618469</td>\n", "      <td>2.376781e+09</td>\n", "      <td>SOL-USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-30</th>\n", "      <td>0.156474</td>\n", "      <td>0.156746</td>\n", "      <td>0.154867</td>\n", "      <td>0.155915</td>\n", "      <td>3.565544e+08</td>\n", "      <td>TRX-USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-30</th>\n", "      <td>0.641945</td>\n", "      <td>0.652411</td>\n", "      <td>0.610951</td>\n", "      <td>0.611492</td>\n", "      <td>2.051369e+09</td>\n", "      <td>XRP-USD</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3660 rows × 6 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-879083e7-465e-4e83-b820-f0b469f7dbb7')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-879083e7-465e-4e83-b820-f0b469f7dbb7 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-879083e7-465e-4e83-b820-f0b469f7dbb7');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-77723f97-51e0-4904-a713-496c5851eefa\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-77723f97-51e0-4904-a713-496c5851eefa')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-77723f97-51e0-4904-a713-496c5851eefa button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_1a79b784-8bf7-4605-b63a-f0373ecdb69c\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('ohlc_data')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_1a79b784-8bf7-4605-b63a-f0373ecdb69c button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('ohlc_data');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "ohlc_data", "summary": "{\n  \"name\": \"ohlc_data\",\n  \"rows\": 3660,\n  \"fields\": [\n    {\n      \"column\": \"date\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": \"2023-10-01\",\n        \"max\": \"2024-09-30\",\n        \"num_unique_values\": 366,\n        \"samples\": [\n          \"2024-04-11\",\n          \"2023-11-03\",\n          \"2023-10-16\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"open\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16639.836555374353,\n        \"min\": 0.08486499637365341,\n        \"max\": 73079.375,\n        \"num_unique_values\": 3659,\n        \"samples\": [\n          227.13491821289062,\n          0.28950101137161255,\n          0.4867730140686035\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"high\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16973.343411209262,\n        \"min\": 0.08571500331163406,\n        \"max\": 73750.0703125,\n        \"num_unique_values\": 3660,\n        \"samples\": [\n          229.19149780273438,\n          0.29728201031684875,\n          0.4899919927120209\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"low\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16300.377275687548,\n        \"min\": 0.0847959965467453,\n        \"max\": 71334.09375,\n        \"num_unique_values\": 3655,\n        \"samples\": [\n          2419.36279296875,\n          177.46127319335938,\n          0.5218260288238525\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"close\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16663.48894012965,\n        \"min\": 0.08486700057983398,\n        \"max\": 73083.5,\n        \"num_unique_values\": 3655,\n        \"samples\": [\n          2487.515625,\n          1.0434010028839111,\n          0.5468699932098389\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"volume\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 10083764369.937248,\n        \"min\": 33682820.0,\n        \"max\": 108991085584.0,\n        \"num_unique_values\": 3660,\n        \"samples\": [\n          282899321.0,\n          146280893.0,\n          385469444.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"symbol\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 10,\n        \"samples\": [\n          \"TRX-USD\",\n          \"BNB-USD\",\n          \"LTC-USD\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 2}]}, {"cell_type": "markdown", "source": ["Select the close data from ohlc_data for each crypto currency"], "metadata": {"id": "diAUqkPi5BYe"}}, {"cell_type": "code", "source": ["close_symbol= ohlc_data[['close', 'symbol']]\n", "\n", "# Setting the symbol as the second index level\n", "close_symbol = close_symbol.set_index('symbol', append= True)\n", "\n", "# Unstack 'symbol' to make each unique symbol a separate column\n", "close_symbol_unstacked= close_symbol.unstack(level='symbol')\n", "\n", "# Flatten the column headers\n", "close_symbol_unstacked.columns = close_symbol_unstacked.columns.get_level_values(1)\n", "\n", "prices= close_symbol_unstacked\n", "\n", "prices"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 455}, "id": "PXNs59lSzMqv", "outputId": "1bced258-fac0-4be7-c433-e6c08e40afd6"}, "execution_count": 3, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["symbol       ADA-USD     BNB-USD       BTC-USD   DOT-USD      ETH-USD  \\\n", "date                                                                    \n", "2023-10-01  0.265895  218.047134  27983.750000  4.261009  1733.810425   \n", "2023-10-02  0.259513  214.757935  27530.785156  4.123762  1663.627563   \n", "2023-10-03  0.261028  213.435944  27429.978516  4.074247  1656.685669   \n", "2023-10-04  0.259315  213.413086  27799.394531  4.047316  1647.838135   \n", "2023-10-05  0.260149  210.679672  27415.912109  4.022738  1611.476440   \n", "...              ...         ...           ...       ...          ...   \n", "2024-09-26  0.401882  596.776917  65181.019531  4.816284  2632.199951   \n", "2024-09-27  0.402328  607.867004  65790.664062  4.892055  2695.900635   \n", "2024-09-28  0.401052  601.567200  65887.648438  4.805896  2677.539062   \n", "2024-09-29  0.397597  596.411194  65635.304688  4.768428  2659.346924   \n", "2024-09-30  0.373214  567.260071  63329.500000  4.437086  2603.062744   \n", "\n", "symbol        LTC-USD  MATIC-USD     SOL-USD   TRX-USD   XRP-USD  \n", "date                                                              \n", "2023-10-01  68.233315   0.568532   23.836487  0.090118  0.524204  \n", "2023-10-02  66.011124   0.547513   23.371700  0.087566  0.512832  \n", "2023-10-03  65.493515   0.566308   23.552694  0.090858  0.538387  \n", "2023-10-04  64.452065   0.563369   23.144787  0.088999  0.532931  \n", "2023-10-05  64.858765   0.546018   22.694141  0.088276  0.523366  \n", "...               ...        ...         ...       ...       ...  \n", "2024-09-26  68.518311   0.424973  155.576096  0.153201  0.590421  \n", "2024-09-27  71.188202   0.433774  157.749939  0.155170  0.588927  \n", "2024-09-28  70.003967   0.423559  156.912430  0.155068  0.614801  \n", "2024-09-29  69.314423   0.421419  158.629166  0.156474  0.641947  \n", "2024-09-30  66.820450   0.395917  152.618469  0.155915  0.611492  \n", "\n", "[366 rows x 10 columns]"], "text/html": ["\n", "  <div id=\"df-55e65dcb-ed1c-4498-9030-f9d3ef8f4ff4\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>symbol</th>\n", "      <th>ADA-USD</th>\n", "      <th>BNB-USD</th>\n", "      <th>BTC-USD</th>\n", "      <th>DOT-USD</th>\n", "      <th>ETH-USD</th>\n", "      <th>LTC-USD</th>\n", "      <th>MATIC-USD</th>\n", "      <th>SOL-USD</th>\n", "      <th>TRX-USD</th>\n", "      <th>XRP-USD</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-10-01</th>\n", "      <td>0.265895</td>\n", "      <td>218.047134</td>\n", "      <td>27983.750000</td>\n", "      <td>4.261009</td>\n", "      <td>1733.810425</td>\n", "      <td>68.233315</td>\n", "      <td>0.568532</td>\n", "      <td>23.836487</td>\n", "      <td>0.090118</td>\n", "      <td>0.524204</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-10-02</th>\n", "      <td>0.259513</td>\n", "      <td>214.757935</td>\n", "      <td>27530.785156</td>\n", "      <td>4.123762</td>\n", "      <td>1663.627563</td>\n", "      <td>66.011124</td>\n", "      <td>0.547513</td>\n", "      <td>23.371700</td>\n", "      <td>0.087566</td>\n", "      <td>0.512832</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-10-03</th>\n", "      <td>0.261028</td>\n", "      <td>213.435944</td>\n", "      <td>27429.978516</td>\n", "      <td>4.074247</td>\n", "      <td>1656.685669</td>\n", "      <td>65.493515</td>\n", "      <td>0.566308</td>\n", "      <td>23.552694</td>\n", "      <td>0.090858</td>\n", "      <td>0.538387</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-10-04</th>\n", "      <td>0.259315</td>\n", "      <td>213.413086</td>\n", "      <td>27799.394531</td>\n", "      <td>4.047316</td>\n", "      <td>1647.838135</td>\n", "      <td>64.452065</td>\n", "      <td>0.563369</td>\n", "      <td>23.144787</td>\n", "      <td>0.088999</td>\n", "      <td>0.532931</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-10-05</th>\n", "      <td>0.260149</td>\n", "      <td>210.679672</td>\n", "      <td>27415.912109</td>\n", "      <td>4.022738</td>\n", "      <td>1611.476440</td>\n", "      <td>64.858765</td>\n", "      <td>0.546018</td>\n", "      <td>22.694141</td>\n", "      <td>0.088276</td>\n", "      <td>0.523366</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-26</th>\n", "      <td>0.401882</td>\n", "      <td>596.776917</td>\n", "      <td>65181.019531</td>\n", "      <td>4.816284</td>\n", "      <td>2632.199951</td>\n", "      <td>68.518311</td>\n", "      <td>0.424973</td>\n", "      <td>155.576096</td>\n", "      <td>0.153201</td>\n", "      <td>0.590421</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-27</th>\n", "      <td>0.402328</td>\n", "      <td>607.867004</td>\n", "      <td>65790.664062</td>\n", "      <td>4.892055</td>\n", "      <td>2695.900635</td>\n", "      <td>71.188202</td>\n", "      <td>0.433774</td>\n", "      <td>157.749939</td>\n", "      <td>0.155170</td>\n", "      <td>0.588927</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-28</th>\n", "      <td>0.401052</td>\n", "      <td>601.567200</td>\n", "      <td>65887.648438</td>\n", "      <td>4.805896</td>\n", "      <td>2677.539062</td>\n", "      <td>70.003967</td>\n", "      <td>0.423559</td>\n", "      <td>156.912430</td>\n", "      <td>0.155068</td>\n", "      <td>0.614801</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-29</th>\n", "      <td>0.397597</td>\n", "      <td>596.411194</td>\n", "      <td>65635.304688</td>\n", "      <td>4.768428</td>\n", "      <td>2659.346924</td>\n", "      <td>69.314423</td>\n", "      <td>0.421419</td>\n", "      <td>158.629166</td>\n", "      <td>0.156474</td>\n", "      <td>0.641947</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-30</th>\n", "      <td>0.373214</td>\n", "      <td>567.260071</td>\n", "      <td>63329.500000</td>\n", "      <td>4.437086</td>\n", "      <td>2603.062744</td>\n", "      <td>66.820450</td>\n", "      <td>0.395917</td>\n", "      <td>152.618469</td>\n", "      <td>0.155915</td>\n", "      <td>0.611492</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>366 rows × 10 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-55e65dcb-ed1c-4498-9030-f9d3ef8f4ff4')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-55e65dcb-ed1c-4498-9030-f9d3ef8f4ff4 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-55e65dcb-ed1c-4498-9030-f9d3ef8f4ff4');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-bd05183d-4f63-495d-9ee7-ed7d9c0b8354\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-bd05183d-4f63-495d-9ee7-ed7d9c0b8354')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-bd05183d-4f63-495d-9ee7-ed7d9c0b8354 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_3fba15ae-1b60-4e81-ae38-43bb9af2ff04\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('close_symbol_unstacked')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_3fba15ae-1b60-4e81-ae38-43bb9af2ff04 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('close_symbol_unstacked');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "close_symbol_unstacked", "summary": "{\n  \"name\": \"close_symbol_unstacked\",\n  \"rows\": 366,\n  \"fields\": [\n    {\n      \"column\": \"date\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": \"2023-10-01\",\n        \"max\": \"2024-09-30\",\n        \"num_unique_values\": 366,\n        \"samples\": [\n          \"2024-04-11\",\n          \"2023-11-03\",\n          \"2023-10-16\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"ADA-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.12241864696655712,\n        \"min\": 0.2434529960155487,\n        \"max\": 0.7741900086402893,\n        \"num_unique_values\": 365,\n        \"samples\": [\n          0.5035750269889832,\n          0.32902100682258606,\n          0.25156301259994507\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"BNB-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 152.5214407182818,\n        \"min\": 205.2294158935547,\n        \"max\": 710.4640502929688,\n        \"num_unique_values\": 366,\n        \"samples\": [\n          604.893798828125,\n          230.60597229003906,\n          214.82395935058594\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"BTC-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 13053.33386869586,\n        \"min\": 26756.798828125,\n        \"max\": 73083.5,\n        \"num_unique_values\": 366,\n        \"samples\": [\n          70060.609375,\n          34732.32421875,\n          28519.466796875\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"DOT-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1.71868220447839,\n        \"min\": 3.6488780975341797,\n        \"max\": 11.542901992797852,\n        \"num_unique_values\": 366,\n        \"samples\": [\n          8.379441261291504,\n          4.6154937744140625,\n          3.7862110137939453\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"ETH-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 645.7801836761455,\n        \"min\": 1539.6124267578125,\n        \"max\": 4066.445068359375,\n        \"num_unique_values\": 366,\n        \"samples\": [\n          3505.247802734375,\n          1832.795166015625,\n          1600.5343017578125\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"LTC-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 9.82303289832029,\n        \"min\": 55.983909606933594,\n        \"max\": 109.25897216796875,\n        \"num_unique_values\": 366,\n        \"samples\": [\n          98.68910217285156,\n          69.49114227294922,\n          63.337162017822266\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"MATIC-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.20641913692628033,\n        \"min\": 0.3659299910068512,\n        \"max\": 1.2714049816131592,\n        \"num_unique_values\": 366,\n        \"samples\": [\n          0.8783450126647949,\n          0.6719430088996887,\n          0.5341209769248962\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"SOL-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 46.98292860601651,\n        \"min\": 21.300268173217773,\n        \"max\": 202.87413024902344,\n        \"num_unique_values\": 366,\n        \"samples\": [\n          172.5763702392578,\n          39.51976013183594,\n          23.98295783996582\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"TRX-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.018051530692952913,\n        \"min\": 0.08486700057983398,\n        \"max\": 0.16655699908733368,\n        \"num_unique_values\": 362,\n        \"samples\": [\n          0.13199299573898315,\n          0.09731300175189972,\n          0.08891399949789047\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"XRP-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.05616593942864053,\n        \"min\": 0.4198229908943176,\n        \"max\": 0.7180359959602356,\n        \"num_unique_values\": 366,\n        \"samples\": [\n          0.6088799834251404,\n          0.6130020022392273,\n          0.49797698855400085\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 3}]}, {"cell_type": "markdown", "source": ["### <PERSON><PERSON> Portfolio's Expected Returns"], "metadata": {"id": "PbL5KdtRyn9C"}}, {"cell_type": "markdown", "source": ["The expected returns serve as the basis for evaluating different asset combinations and determining the optimal portfolio allocation. In the next steps, these returns will be used along with the covariance matrix to analyze risk-return profiles."], "metadata": {"id": "i-IM_cAUywwL"}}, {"cell_type": "markdown", "source": ["$$return=\\frac{Σ\\space r_{i}}{N}⋅365$$\n", "\n", "where $r_i$ is the daily return of a particular asset and $N$ is the number of days in the data, we multiply by 365 so as to annualize the result"], "metadata": {"id": "Ktrk_3fHbmS2"}}, {"cell_type": "code", "source": ["# Expected returns of crypto assets using the mean historical return.\n", "assets_expected_returns = expected_returns.mean_historical_return(prices, frequency=365, compounding=False)\n", "assets_expected_returns"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 429}, "id": "Mx_MLbe4mxfa", "outputId": "310d6922-1a13-458e-b5a3-a066f4ddc7f6"}, "execution_count": 4, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["symbol\n", "ADA-USD      0.604022\n", "BNB-USD      1.121563\n", "BTC-USD      0.952337\n", "DOT-USD      0.319752\n", "ETH-USD      0.591286\n", "LTC-USD      0.173647\n", "MATIC-USD   -0.058596\n", "SOL-USD      2.288214\n", "TRX-USD      0.616572\n", "XRP-USD      0.344980\n", "dtype: float64"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>ADA-USD</th>\n", "      <td>0.604022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BNB-USD</th>\n", "      <td>1.121563</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BTC-USD</th>\n", "      <td>0.952337</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DOT-USD</th>\n", "      <td>0.319752</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ETH-USD</th>\n", "      <td>0.591286</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LTC-USD</th>\n", "      <td>0.173647</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MATIC-USD</th>\n", "      <td>-0.058596</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SOL-USD</th>\n", "      <td>2.288214</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TRX-USD</th>\n", "      <td>0.616572</td>\n", "    </tr>\n", "    <tr>\n", "      <th>XRP-USD</th>\n", "      <td>0.344980</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div><br><label><b>dtype:</b> float64</label>"]}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "markdown", "source": ["The frequency parameter indicates the number of trading periods in a year. For crypto daily data, it is set to 365, since crypto can be traded everyday.\n", "\n", "The compounding parameter calculate returns using simple or compounded growth. Setting compounding=False results in simple annualized returns. If compounding=True, the function would compute geometric (compounded) returns, which consider reinvested returns over time."], "metadata": {"id": "eBDVKkQZ2Wx3"}}, {"cell_type": "markdown", "source": ["### Calculate the Covariance Matrix Using the Ledoit-Wolf Shrinkage Estimator"], "metadata": {"id": "1gvka-on5JDC"}}, {"cell_type": "markdown", "source": ["The covariance matrix represents the relationship between the returns of the assets. It measures how returns of one asset(e.g btc) vary in relation to another(e.g eth), which is essential for understanding the overall risk of a portfolio. Assets with high positive covariance tend to move in the same direction, while those with negative covariance move in opposite directions."], "metadata": {"id": "fT9f1AWN5l1L"}}, {"cell_type": "markdown", "source": ["The formula below uses an example of btc and eth to express how covariance is calculated:\n", "\n", "$$Cov({r_{btc}, r_{eth} })= \\frac{Σ(r_{btc}-\\bar r_{btc})(r_{eth}-\\bar r_{eth})}{N}$$\n", "\n", "Where $r$ is the daily returns of the assets and $\\bar r$ is the average daily returns of the assets."], "metadata": {"id": "69Uyh-rPUWD3"}}, {"cell_type": "code", "source": ["# Covariance matrix of crypto assets using the Ledoit-Wolf shrinkage method.\n", "covariance = CovarianceShrinkage(prices).ledoit_wolf()\n", "covariance"], "metadata": {"id": "S_sH-1sq6bnI", "colab": {"base_uri": "https://localhost:8080/", "height": 394}, "outputId": "c1e9855e-c6c7-4dbd-8f66-50ec26743425"}, "execution_count": 5, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["symbol      ADA-USD   BNB-USD   BTC-USD   DOT-USD   ETH-USD   LTC-USD  \\\n", "symbol                                                                  \n", "ADA-USD    0.366706  0.150286  0.180182  0.304589  0.203891  0.205121   \n", "BNB-USD    0.150286  0.231405  0.122723  0.150846  0.139246  0.109135   \n", "BTC-USD    0.180182  0.122723  0.190093  0.176476  0.172781  0.127462   \n", "DOT-USD    0.304589  0.150846  0.176476  0.386458  0.207296  0.193352   \n", "ETH-USD    0.203891  0.139246  0.172781  0.207296  0.259972  0.160428   \n", "LTC-USD    0.205121  0.109135  0.127462  0.193352  0.160428  0.270809   \n", "MATIC-USD  0.278896  0.173927  0.170640  0.288592  0.225087  0.191384   \n", "SOL-USD    0.297128  0.172109  0.222148  0.333958  0.232898  0.191960   \n", "TRX-USD    0.083817  0.047007  0.055094  0.086146  0.063453  0.055784   \n", "XRP-USD    0.194349  0.093584  0.120736  0.192491  0.133452  0.158367   \n", "\n", "symbol     MATIC-USD   SOL-USD   TRX-USD   XRP-USD  \n", "symbol                                              \n", "ADA-USD     0.278896  0.297128  0.083817  0.194349  \n", "BNB-USD     0.173927  0.172109  0.047007  0.093584  \n", "BTC-USD     0.170640  0.222148  0.055094  0.120736  \n", "DOT-USD     0.288592  0.333958  0.086146  0.192491  \n", "ETH-USD     0.225087  0.232898  0.063453  0.133452  \n", "LTC-USD     0.191384  0.191960  0.055784  0.158367  \n", "MATIC-USD   0.415755  0.290945  0.075620  0.181063  \n", "SOL-USD     0.290945  0.595302  0.095612  0.185187  \n", "TRX-USD     0.075620  0.095612  0.098694  0.057615  \n", "XRP-USD     0.181063  0.185187  0.057615  0.267912  "], "text/html": ["\n", "  <div id=\"df-5f529475-f8af-468d-a026-fa8af71ee37b\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>symbol</th>\n", "      <th>ADA-USD</th>\n", "      <th>BNB-USD</th>\n", "      <th>BTC-USD</th>\n", "      <th>DOT-USD</th>\n", "      <th>ETH-USD</th>\n", "      <th>LTC-USD</th>\n", "      <th>MATIC-USD</th>\n", "      <th>SOL-USD</th>\n", "      <th>TRX-USD</th>\n", "      <th>XRP-USD</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>ADA-USD</th>\n", "      <td>0.366706</td>\n", "      <td>0.150286</td>\n", "      <td>0.180182</td>\n", "      <td>0.304589</td>\n", "      <td>0.203891</td>\n", "      <td>0.205121</td>\n", "      <td>0.278896</td>\n", "      <td>0.297128</td>\n", "      <td>0.083817</td>\n", "      <td>0.194349</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BNB-USD</th>\n", "      <td>0.150286</td>\n", "      <td>0.231405</td>\n", "      <td>0.122723</td>\n", "      <td>0.150846</td>\n", "      <td>0.139246</td>\n", "      <td>0.109135</td>\n", "      <td>0.173927</td>\n", "      <td>0.172109</td>\n", "      <td>0.047007</td>\n", "      <td>0.093584</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BTC-USD</th>\n", "      <td>0.180182</td>\n", "      <td>0.122723</td>\n", "      <td>0.190093</td>\n", "      <td>0.176476</td>\n", "      <td>0.172781</td>\n", "      <td>0.127462</td>\n", "      <td>0.170640</td>\n", "      <td>0.222148</td>\n", "      <td>0.055094</td>\n", "      <td>0.120736</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DOT-USD</th>\n", "      <td>0.304589</td>\n", "      <td>0.150846</td>\n", "      <td>0.176476</td>\n", "      <td>0.386458</td>\n", "      <td>0.207296</td>\n", "      <td>0.193352</td>\n", "      <td>0.288592</td>\n", "      <td>0.333958</td>\n", "      <td>0.086146</td>\n", "      <td>0.192491</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ETH-USD</th>\n", "      <td>0.203891</td>\n", "      <td>0.139246</td>\n", "      <td>0.172781</td>\n", "      <td>0.207296</td>\n", "      <td>0.259972</td>\n", "      <td>0.160428</td>\n", "      <td>0.225087</td>\n", "      <td>0.232898</td>\n", "      <td>0.063453</td>\n", "      <td>0.133452</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LTC-USD</th>\n", "      <td>0.205121</td>\n", "      <td>0.109135</td>\n", "      <td>0.127462</td>\n", "      <td>0.193352</td>\n", "      <td>0.160428</td>\n", "      <td>0.270809</td>\n", "      <td>0.191384</td>\n", "      <td>0.191960</td>\n", "      <td>0.055784</td>\n", "      <td>0.158367</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MATIC-USD</th>\n", "      <td>0.278896</td>\n", "      <td>0.173927</td>\n", "      <td>0.170640</td>\n", "      <td>0.288592</td>\n", "      <td>0.225087</td>\n", "      <td>0.191384</td>\n", "      <td>0.415755</td>\n", "      <td>0.290945</td>\n", "      <td>0.075620</td>\n", "      <td>0.181063</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SOL-USD</th>\n", "      <td>0.297128</td>\n", "      <td>0.172109</td>\n", "      <td>0.222148</td>\n", "      <td>0.333958</td>\n", "      <td>0.232898</td>\n", "      <td>0.191960</td>\n", "      <td>0.290945</td>\n", "      <td>0.595302</td>\n", "      <td>0.095612</td>\n", "      <td>0.185187</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TRX-USD</th>\n", "      <td>0.083817</td>\n", "      <td>0.047007</td>\n", "      <td>0.055094</td>\n", "      <td>0.086146</td>\n", "      <td>0.063453</td>\n", "      <td>0.055784</td>\n", "      <td>0.075620</td>\n", "      <td>0.095612</td>\n", "      <td>0.098694</td>\n", "      <td>0.057615</td>\n", "    </tr>\n", "    <tr>\n", "      <th>XRP-USD</th>\n", "      <td>0.194349</td>\n", "      <td>0.093584</td>\n", "      <td>0.120736</td>\n", "      <td>0.192491</td>\n", "      <td>0.133452</td>\n", "      <td>0.158367</td>\n", "      <td>0.181063</td>\n", "      <td>0.185187</td>\n", "      <td>0.057615</td>\n", "      <td>0.267912</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-5f529475-f8af-468d-a026-fa8af71ee37b')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-5f529475-f8af-468d-a026-fa8af71ee37b button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-5f529475-f8af-468d-a026-fa8af71ee37b');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-5bce963a-ca3c-40af-967f-704174cef78a\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-5bce963a-ca3c-40af-967f-704174cef78a')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-5bce963a-ca3c-40af-967f-704174cef78a button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_3138c98b-6be9-498c-a3ac-ffe3a42a51b9\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('covariance')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_3138c98b-6be9-498c-a3ac-ffe3a42a51b9 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('covariance');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "covariance", "summary": "{\n  \"name\": \"covariance\",\n  \"rows\": 10,\n  \"fields\": [\n    {\n      \"column\": \"symbol\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 10,\n        \"samples\": [\n          \"TRX-USD\",\n          \"BNB-USD\",\n          \"LTC-USD\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"ADA-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.08419792941723192,\n        \"min\": 0.0838165711742476,\n        \"max\": 0.36670604147106206,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          0.0838165711742476,\n          0.15028626508263157,\n          0.20512138740953018\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"BNB-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.05033753143602621,\n        \"min\": 0.047006792196747846,\n        \"max\": 0.2314051405350048,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          0.047006792196747846,\n          0.2314051405350048,\n          0.1091350550550174\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"BTC-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.04753744415710665,\n        \"min\": 0.05509418774013776,\n        \"max\": 0.22214831696745932,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          0.05509418774013776,\n          0.12272305327668202,\n          0.1274618190134865\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"DOT-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.09269535390671813,\n        \"min\": 0.08614584593812935,\n        \"max\": 0.3864581165922623,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          0.08614584593812935,\n          0.15084590749143012,\n          0.19335241933484426\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"ETH-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.05810745048757783,\n        \"min\": 0.06345275246548801,\n        \"max\": 0.25997209569152796,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          0.06345275246548801,\n          0.139245809134133,\n          0.16042795625973533\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"LTC-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.05919478941890474,\n        \"min\": 0.05578366873261023,\n        \"max\": 0.27080940309104257,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          0.05578366873261023,\n          0.1091350550550174,\n          0.27080940309104257\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"MATIC-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.09336165890150132,\n        \"min\": 0.07561993114423143,\n        \"max\": 0.4157545421078801,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          0.07561993114423143,\n          0.17392703381920863,\n          0.1913835647642417\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"SOL-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.1363595110476115,\n        \"min\": 0.09561239112443014,\n        \"max\": 0.5953019208397278,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          0.09561239112443014,\n          0.17210936103624314,\n          0.1919600059080584\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"TRX-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.01848799121692438,\n        \"min\": 0.047006792196747846,\n        \"max\": 0.09869415860549288,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          0.09869415860549288,\n          0.047006792196747846,\n          0.05578366873261023\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"XRP-USD\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.05970145542480502,\n        \"min\": 0.057614883985101284,\n        \"max\": 0.2679123979532293,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          0.057614883985101284,\n          0.09358410593039336,\n          0.15836736522902278\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 5}]}, {"cell_type": "markdown", "source": ["**Led<PERSON>-<PERSON>**:is a technique used to improve the estimation of covariance matrices, especially when dealing with high-dimensional data (like multiple crypto assets) relative to the number of observations."], "metadata": {"id": "91CYiL0a6XaY"}}, {"cell_type": "markdown", "source": ["### Portfolio Optimization using Critical Line Algorithm (CLA)"], "metadata": {"id": "apy1KgWNPBew"}}, {"cell_type": "markdown", "source": ["Portfolio risk, also known as portfolio volatility, is determined by calculating the variance of the returns of the assets. The variance is calculated using the below equation:\n", "\n", "$$ \\sigma^2= W⋅Cov⋅W^T $$\n", "\n", "Where $W$ is the weights of the asstes and $Cov$ is the covariance of the returns of the assets in the portfolio."], "metadata": {"id": "rBxNCT2b6oCH"}}, {"cell_type": "markdown", "source": ["**The Critical Line Algorithm (CLA)** optimizes asset weights in a portfolio by calculating the risk and expected return for various combinations of these weights. It systematically varies the weights assigned to each asset, assessing how each combination affects overall portfolio performance. This process helps identify efficient portfolios that maximize expected returns for a given level of risk or minimize risk for a desired return."], "metadata": {"id": "xgfgtZldOGE6"}}, {"cell_type": "code", "source": ["# Create a Critical Line Algorithm (CLA) object using the calculated expected returns and covariance matrix.\n", "cla = CLA(assets_expected_returns, covariance)"], "metadata": {"id": "NarHAjA25b61"}, "execution_count": 6, "outputs": []}, {"cell_type": "markdown", "source": ["**The Efficient Frontier** is a curve showing optimal portfolios with the best risk-return tradeoffs. It's important because it helps us identify portfolios that maximize return for a given risk level or minimize risk for a desired return."], "metadata": {"id": "Cgz_DmDcPXpN"}}, {"cell_type": "code", "source": ["# Calculate the efficient frontier, obtaining the returns, volatility, and weights for various portfolios.\n", "(returns, volatility, weights) = cla.efficient_frontier()\n", "\n", "efficient_frontier_portfolios= pd.DataFrame([returns, volatility, weights]).T\n", "efficient_frontier_portfolios.columns=['returns', 'volatility', 'weights']\n", "\n", "efficient_frontier_portfolios"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 424}, "id": "VMD3zD-p9AfE", "outputId": "c43b28ee-757d-447a-f128-6cda567b1b1b"}, "execution_count": 7, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["     returns volatility                                            weights\n", "0   2.288214   0.771558  [[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0....\n", "1   2.288214   0.771558  [[0.0], [8.540177112501205e-18], [0.0], [0.0],...\n", "2   2.288214   0.771558  [[0.0], [1.708035422500241e-17], [0.0], [0.0],...\n", "3   2.288214   0.771558  [[0.0], [2.5620531337503614e-17], [0.0], [0.0]...\n", "4   2.288214   0.771558  [[0.0], [3.416070845000482e-17], [0.0], [0.0],...\n", "..       ...        ...                                                ...\n", "74  0.699686   0.289411  [[0.0], [0.14064237710852637], [0.110488877515...\n", "75  0.695988   0.289372  [[0.0], [0.1384244599412712], [0.1077026598376...\n", "76  0.692291   0.289344  [[0.0], [0.136206542774016], [0.10491644215937...\n", "77  0.688593   0.289328  [[0.0], [0.1339886256067608], [0.1021302244811...\n", "78  0.684895   0.289322  [[0.0], [0.1317707084395056], [0.0993440068028...\n", "\n", "[79 rows x 3 columns]"], "text/html": ["\n", "  <div id=\"df-3f1accf5-9e79-486d-8406-613a71d5af36\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>returns</th>\n", "      <th>volatility</th>\n", "      <th>weights</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2.288214</td>\n", "      <td>0.771558</td>\n", "      <td>[[0.0], [0.0], [0.0], [0.0], [0.0], [0.0], [0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.288214</td>\n", "      <td>0.771558</td>\n", "      <td>[[0.0], [8.540177112501205e-18], [0.0], [0.0],...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2.288214</td>\n", "      <td>0.771558</td>\n", "      <td>[[0.0], [1.708035422500241e-17], [0.0], [0.0],...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2.288214</td>\n", "      <td>0.771558</td>\n", "      <td>[[0.0], [2.5620531337503614e-17], [0.0], [0.0]...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2.288214</td>\n", "      <td>0.771558</td>\n", "      <td>[[0.0], [3.416070845000482e-17], [0.0], [0.0],...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>74</th>\n", "      <td>0.699686</td>\n", "      <td>0.289411</td>\n", "      <td>[[0.0], [0.14064237710852637], [0.110488877515...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>0.695988</td>\n", "      <td>0.289372</td>\n", "      <td>[[0.0], [0.1384244599412712], [0.1077026598376...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>0.692291</td>\n", "      <td>0.289344</td>\n", "      <td>[[0.0], [0.136206542774016], [0.10491644215937...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>0.688593</td>\n", "      <td>0.289328</td>\n", "      <td>[[0.0], [0.1339886256067608], [0.1021302244811...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>0.684895</td>\n", "      <td>0.289322</td>\n", "      <td>[[0.0], [0.1317707084395056], [0.0993440068028...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>79 rows × 3 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-3f1accf5-9e79-486d-8406-613a71d5af36')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-3f1accf5-9e79-486d-8406-613a71d5af36 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-3f1accf5-9e79-486d-8406-613a71d5af36');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-efe59416-aa05-440d-bc9d-21fd3845840b\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-efe59416-aa05-440d-bc9d-21fd3845840b')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-efe59416-aa05-440d-bc9d-21fd3845840b button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_63d7d79a-e36f-4670-be85-bb78fa882def\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('efficient_frontier_portfolios')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_63d7d79a-e36f-4670-be85-bb78fa882def button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('efficient_frontier_portfolios');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "efficient_frontier_portfolios", "summary": "{\n  \"name\": \"efficient_frontier_portfolios\",\n  \"rows\": 79,\n  \"fields\": [\n    {\n      \"column\": \"returns\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": 0.6848950166508636,\n        \"max\": 2.2882141236911924,\n        \"num_unique_values\": 66,\n        \"samples\": [\n          0.72557090559056,\n          0.6959884409071444,\n          2.2882141236911924\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"volatility\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": 0.2893221746578987,\n        \"max\": 0.7715581124191021,\n        \"num_unique_values\": 66,\n        \"samples\": [\n          0.28999332370675096,\n          0.2893721484198184,\n          0.7715581124191021\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"weights\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 7}]}, {"cell_type": "markdown", "source": ["### Visualize the efficient frontier"], "metadata": {"id": "AaQTTEhZ70e7"}}, {"cell_type": "code", "source": ["plt.figure(figsize=(10,5))\n", "plt.scatter(volatility, returns, label='Portfolios on efficient frontier')\n", "plt.legend()\n", "plt.ylabel('Expected Re<PERSON>')\n", "plt.xlabel('Volatiity')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 465}, "id": "Kt1n-3HQ1gj7", "outputId": "686d5698-8806-4c7e-ba07-46b26d76add0"}, "execution_count": 8, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x500 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["Get the weights (in perentage) of the portfolio with the lowest volatility"], "metadata": {"id": "v4jE07br-DEJ"}}, {"cell_type": "code", "source": ["optimized_weight=np.array(list(cla.max_sharpe().values()))\n", "optimized_weight= np.round(optimized_weight, 4)\n", "\n", "pie_df=pd.DataFrame(optimized_weight*100, index=prices.columns, columns=['weights'])\n", "pie_df= pie_df.sort_values(by=['weights'], ascending=False)\n", "pie_df"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 394}, "id": "FzvioR9k11Tx", "outputId": "9eae848f-52ba-47bc-c17f-4cf753c82504"}, "execution_count": 9, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["           weights\n", "symbol            \n", "SOL-USD      37.11\n", "TRX-USD      32.71\n", "BNB-USD      30.17\n", "ADA-USD       0.00\n", "BTC-USD      -0.00\n", "DOT-USD       0.00\n", "ETH-USD       0.00\n", "LTC-USD       0.00\n", "MATIC-USD     0.00\n", "XRP-USD       0.00"], "text/html": ["\n", "  <div id=\"df-64fc90f9-a1f2-4bd3-9bdc-417d5bbc22e9\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>weights</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SOL-USD</th>\n", "      <td>37.11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TRX-USD</th>\n", "      <td>32.71</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BNB-USD</th>\n", "      <td>30.17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ADA-USD</th>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BTC-USD</th>\n", "      <td>-0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DOT-USD</th>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ETH-USD</th>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LTC-USD</th>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MATIC-USD</th>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>XRP-USD</th>\n", "      <td>0.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-64fc90f9-a1f2-4bd3-9bdc-417d5bbc22e9')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-64fc90f9-a1f2-4bd3-9bdc-417d5bbc22e9 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-64fc90f9-a1f2-4bd3-9bdc-417d5bbc22e9');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-186b4ecf-aac1-46a3-a78b-119b061ba683\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-186b4ecf-aac1-46a3-a78b-119b061ba683')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-186b4ecf-aac1-46a3-a78b-119b061ba683 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_72584294-6040-4d1c-828e-f1d131bc9ad1\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('pie_df')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_72584294-6040-4d1c-828e-f1d131bc9ad1 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('pie_df');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "pie_df", "summary": "{\n  \"name\": \"pie_df\",\n  \"rows\": 10,\n  \"fields\": [\n    {\n      \"column\": \"symbol\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 10,\n        \"samples\": [\n          \"MATIC-USD\",\n          \"TRX-USD\",\n          \"DOT-USD\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"weights\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 16.184783972059133,\n        \"min\": 0.0,\n        \"max\": 37.11,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          32.71,\n          0.0,\n          37.11\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 9}]}, {"cell_type": "markdown", "source": ["Display Pie Chart of the weights"], "metadata": {"id": "WSHUOb_O_12C"}}, {"cell_type": "code", "source": ["pie_df= pie_df.query('weights != 0.000000')\n", "\n", "fig, ax = plt.subplots()\n", "ax.pie(pie_df.weights, labels=pie_df.index.values.tolist(), autopct='%1.1f%%', radius=2)\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 638}, "id": "Ad1Ncn3N2iRj", "outputId": "a6919539-1140-48d5-c244-0ea49c1e5c8c"}, "execution_count": 10, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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*****************************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****************************************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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["The above weights forms a portfolio with maximum sharpe ratio"], "metadata": {"id": "GAT1yTIEXGh4"}}], "metadata": {"kernelspec": {"display_name": "obb", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.9.19"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 0}