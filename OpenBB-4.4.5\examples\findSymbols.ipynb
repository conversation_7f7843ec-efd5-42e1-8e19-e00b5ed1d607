{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Finding Symbols\n", "\n", "\n", "Finding the ticker symbol, security identifier, the sector, and other metadata is easy if you know where to look.  This guide is intended to introduce some methods for searching, screening, and discovery.\n", "\n", "For maximum coverage and functionality, install OpenBB with `[all]` packages.\n", "\n", "The examples here will assume that the OpenBB Platform has been installed, the environment is active, and it has been imported into a Python session.  If the installation is fresh, or an extension was just installed, the Python interface will need to be rebuilt.  It will only take a few moments to complete."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from openbb import obb"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The simplest way to find tickers is with a basic text query.\n", "\n", "## Search Nasdaq"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>nasdaq_traded</th>\n", "      <th>exchange</th>\n", "      <th>etf</th>\n", "      <th>round_lot_size</th>\n", "      <th>test_issue</th>\n", "      <th>cqs_symbol</th>\n", "      <th>nasdaq_symbol</th>\n", "      <th>next_shares</th>\n", "      <th>market_category</th>\n", "      <th>financial_status</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AMJB</td>\n", "      <td>JPMorgan Chase &amp; Co. Alerian MLP Index ETNs du...</td>\n", "      <td>Y</td>\n", "      <td>P</td>\n", "      <td>Y</td>\n", "      <td>100.0</td>\n", "      <td>N</td>\n", "      <td>AMJB</td>\n", "      <td>AMJB</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>BBAG</td>\n", "      <td>JPMorgan BetaBuilders U.S. Aggregate Bond ETF</td>\n", "      <td>Y</td>\n", "      <td>P</td>\n", "      <td>Y</td>\n", "      <td>100.0</td>\n", "      <td>N</td>\n", "      <td>BBAG</td>\n", "      <td>BBAG</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>BBAX</td>\n", "      <td>JPMorgan BetaBuilders Developed Asia Pacific-e...</td>\n", "      <td>Y</td>\n", "      <td>Z</td>\n", "      <td>Y</td>\n", "      <td>100.0</td>\n", "      <td>N</td>\n", "      <td>BBAX</td>\n", "      <td>BBAX</td>\n", "      <td>N</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  symbol                                               name nasdaq_traded  \\\n", "0   AMJB  JPMorgan Chase & Co. Alerian MLP Index ETNs du...             Y   \n", "1   BBAG      JPMorgan BetaBuilders U.S. Aggregate Bond ETF             Y   \n", "2   BBAX  JPMorgan BetaBuilders Developed Asia Pacific-e...             Y   \n", "\n", "  exchange etf  round_lot_size test_issue cqs_symbol nasdaq_symbol  \\\n", "0        P   Y           100.0          N       AMJB          AMJB   \n", "1        P   Y           100.0          N       BBAG          BBAG   \n", "2        Z   Y           100.0          N       BBAX          BBAX   \n", "\n", "  next_shares market_category financial_status  \n", "0           N             NaN              NaN  \n", "1           N             NaN              NaN  \n", "2           N             NaN              NaN  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.equity.search(\"JPMorgan\", provider=\"nasdaq\").to_df().head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Search Cboe"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>description</th>\n", "      <th>data_delay</th>\n", "      <th>currency</th>\n", "      <th>time_zone</th>\n", "      <th>open_time</th>\n", "      <th>close_time</th>\n", "      <th>tick_days</th>\n", "      <th>tick_frequency</th>\n", "      <th>tick_period</th>\n", "      <th>display_override_auto_hide</th>\n", "      <th>show_intraday_chart</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>SPXUIV</td>\n", "      <td>PROSHARES ULTRAPRO SHORT SP500 ETF</td>\n", "      <td>PROSHARES ULTRAPRO SHORT SP500 ETF\\n</td>\n", "      <td>15</td>\n", "      <td>USD</td>\n", "      <td>America/Chicago</td>\n", "      <td>08:00:00</td>\n", "      <td>16:00:00</td>\n", "      <td>MonToFri</td>\n", "      <td>C</td>\n", "      <td>Regular</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>SPXVIV</td>\n", "      <td>PROSHARES S&amp;P 500 EX-HEALTH CARE ETF</td>\n", "      <td>PROSHARES S&amp;P 500 EX-HEALTH CARE ETF</td>\n", "      <td>15</td>\n", "      <td>USD</td>\n", "      <td>America/Chicago</td>\n", "      <td>08:00:00</td>\n", "      <td>16:00:00</td>\n", "      <td>MonToFri</td>\n", "      <td>C</td>\n", "      <td>Regular</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>VIX1D</td>\n", "      <td>Cboe 1-Day Volatility Index®</td>\n", "      <td>Estimates expected volatility by aggregating t...</td>\n", "      <td>15</td>\n", "      <td>USD</td>\n", "      <td>America/Chicago</td>\n", "      <td>08:00:00</td>\n", "      <td>16:00:00</td>\n", "      <td>MonToFri</td>\n", "      <td>C</td>\n", "      <td>Regular</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>VIX3M</td>\n", "      <td>Cboe S&amp;P 500 3 Month Volatility Index</td>\n", "      <td>The Cboe 3-Month Volatility Index (VIX3M) is d...</td>\n", "      <td>15</td>\n", "      <td>USD</td>\n", "      <td>America/Chicago</td>\n", "      <td>08:00:00</td>\n", "      <td>16:00:00</td>\n", "      <td>MonToFri</td>\n", "      <td>C</td>\n", "      <td>Regular</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>WPUT</td>\n", "      <td>Cboe S&amp;P 500 One-Week PutWrite Index</td>\n", "      <td>Tracks the value of a portfolio that overlays ...</td>\n", "      <td>15</td>\n", "      <td>USD</td>\n", "      <td>America/Chicago</td>\n", "      <td>08:00:00</td>\n", "      <td>16:00:00</td>\n", "      <td>MonToFri</td>\n", "      <td>C</td>\n", "      <td>Regular</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    symbol                                   name  \\\n", "31  SPXUIV     PROSHARES ULTRAPRO SHORT SP500 ETF   \n", "32  SPXVIV  PROSHARES S&P 500 EX-HEALTH CARE ETF    \n", "33   VIX1D           Cboe 1-Day Volatility Index®   \n", "34   VIX3M  Cboe S&P 500 3 Month Volatility Index   \n", "35    WPUT   Cboe S&P 500 One-Week PutWrite Index   \n", "\n", "                                          description  data_delay currency  \\\n", "31               PROSHARES ULTRAPRO SHORT SP500 ETF\\n          15      USD   \n", "32              PROSHARES S&P 500 EX-HEALTH CARE ETF           15      USD   \n", "33  Estimates expected volatility by aggregating t...          15      USD   \n", "34  The Cboe 3-Month Volatility Index (VIX3M) is d...          15      USD   \n", "35  Tracks the value of a portfolio that overlays ...          15      USD   \n", "\n", "          time_zone open_time close_time tick_days tick_frequency tick_period  \\\n", "31  America/Chicago  08:00:00   16:00:00  MonToFri              C     Regular   \n", "32  America/Chicago  08:00:00   16:00:00  MonToFri              C     Regular   \n", "33  America/Chicago  08:00:00   16:00:00  MonToFri              C     Regular   \n", "34  America/Chicago  08:00:00   16:00:00  MonToFri              C     Regular   \n", "35  America/Chicago  08:00:00   16:00:00  MonToFri              C     Regular   \n", "\n", "    display_override_auto_hide  show_intraday_chart  \n", "31                       False                 True  \n", "32                       False                 True  \n", "33                       False                 True  \n", "34                       False                 True  \n", "35                       False                 True  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.index.search(\"SPX\", provider=\"cboe\").to_df().tail(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Search ETFs"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>short_name</th>\n", "      <th>inception_date</th>\n", "      <th>issuer</th>\n", "      <th>investment_style</th>\n", "      <th>esg</th>\n", "      <th>currency</th>\n", "      <th>unit_price</th>\n", "      <th>close</th>\n", "      <th>...</th>\n", "      <th>beta_3y</th>\n", "      <th>return_5y</th>\n", "      <th>return_10y</th>\n", "      <th>beta_10y</th>\n", "      <th>beta_15y</th>\n", "      <th>mer</th>\n", "      <th>dividend_frequency</th>\n", "      <th>pe_ratio</th>\n", "      <th>pb_ratio</th>\n", "      <th>beta_20y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>ZGD</td>\n", "      <td>BMO Equal Weight Global Gold Index ETF</td>\n", "      <td>ZGD:CA</td>\n", "      <td>2012-11-14</td>\n", "      <td>BMO ETF</td>\n", "      <td>Mid Cap Blend</td>\n", "      <td>False</td>\n", "      <td>CAD</td>\n", "      <td>104.80</td>\n", "      <td>106.56</td>\n", "      <td>...</td>\n", "      <td>0.658557</td>\n", "      <td>0.130072</td>\n", "      <td>-0.079531</td>\n", "      <td>0.444583</td>\n", "      <td>NaN</td>\n", "      <td>0.0062</td>\n", "      <td>Annually</td>\n", "      <td>9.0939</td>\n", "      <td>0.8812</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>ZGLD</td>\n", "      <td>BMO Gold Bullion ETF</td>\n", "      <td>ZGLD:CA</td>\n", "      <td>2024-03-08</td>\n", "      <td>BMO ETF</td>\n", "      <td>Gold</td>\n", "      <td>False</td>\n", "      <td>CAD</td>\n", "      <td>36.83</td>\n", "      <td>36.83</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>ZGLD.U</td>\n", "      <td>BMO Gold Bullion ETF</td>\n", "      <td>ZGLD.U:CA</td>\n", "      <td>2024-03-08</td>\n", "      <td>BMO ETF</td>\n", "      <td>Gold</td>\n", "      <td>False</td>\n", "      <td>USD</td>\n", "      <td>36.05</td>\n", "      <td>36.70</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>ZGLH</td>\n", "      <td>BMO Gold Bullion Hedged to CAD ETF</td>\n", "      <td>ZGLH:CA</td>\n", "      <td>2024-03-08</td>\n", "      <td>BMO ETF</td>\n", "      <td>Gold</td>\n", "      <td>False</td>\n", "      <td>CAD</td>\n", "      <td>34.04</td>\n", "      <td>35.77</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>ZJG</td>\n", "      <td>BMO Junior Gold Index ETF</td>\n", "      <td>ZJG:CA</td>\n", "      <td>2010-01-19</td>\n", "      <td>BMO ETF</td>\n", "      <td>Small Cap Blend</td>\n", "      <td>True</td>\n", "      <td>CAD</td>\n", "      <td>92.75</td>\n", "      <td>92.84</td>\n", "      <td>...</td>\n", "      <td>0.641448</td>\n", "      <td>0.087857</td>\n", "      <td>-0.111992</td>\n", "      <td>0.449994</td>\n", "      <td>NaN</td>\n", "      <td>0.0061</td>\n", "      <td>Annually</td>\n", "      <td>13.5959</td>\n", "      <td>0.9830</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 35 columns</p>\n", "</div>"], "text/plain": ["    symbol                                    name short_name inception_date  \\\n", "21     ZGD  BMO Equal Weight Global Gold Index ETF     ZGD:CA     2012-11-14   \n", "22    ZGLD                    BMO Gold Bullion ETF    ZGLD:CA     2024-03-08   \n", "23  ZGLD.U                    BMO Gold Bullion ETF  ZGLD.U:CA     2024-03-08   \n", "24    ZGLH      BMO Gold Bullion Hedged to CAD ETF    ZGLH:CA     2024-03-08   \n", "25     ZJG               BMO Junior Gold Index ETF     ZJG:CA     2010-01-19   \n", "\n", "     issuer investment_style    esg currency  unit_price   close  ...  \\\n", "21  BMO ETF    Mid Cap Blend  False      CAD      104.80  106.56  ...   \n", "22  BMO ETF             Gold  False      CAD       36.83   36.83  ...   \n", "23  BMO ETF             Gold  False      USD       36.05   36.70  ...   \n", "24  BMO ETF             Gold  False      CAD       34.04   35.77  ...   \n", "25  BMO ETF  Small Cap Blend   True      CAD       92.75   92.84  ...   \n", "\n", "     beta_3y  return_5y  return_10y  beta_10y  beta_15y     mer  \\\n", "21  0.658557   0.130072   -0.079531  0.444583       NaN  0.0062   \n", "22       NaN        NaN         NaN       NaN       NaN     NaN   \n", "23       NaN        NaN         NaN       NaN       NaN     NaN   \n", "24       NaN        NaN         NaN       NaN       NaN     NaN   \n", "25  0.641448   0.087857   -0.111992  0.449994       NaN  0.0061   \n", "\n", "    dividend_frequency  pe_ratio  pb_ratio  beta_20y  \n", "21            Annually    9.0939    0.8812       NaN  \n", "22                 NaN       NaN       NaN       NaN  \n", "23                 NaN       NaN       NaN       NaN  \n", "24                 NaN       NaN       NaN       NaN  \n", "25            Annually   13.5959    0.9830       NaN  \n", "\n", "[5 rows x 35 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.etf.search(\"gold\", provider=\"tmx\").to_df().iloc[-5:]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>market_cap</th>\n", "      <th>sector</th>\n", "      <th>industry</th>\n", "      <th>beta</th>\n", "      <th>price</th>\n", "      <th>last_annual_dividend</th>\n", "      <th>volume</th>\n", "      <th>exchange</th>\n", "      <th>exchange_name</th>\n", "      <th>country</th>\n", "      <th>actively_trading</th>\n", "      <th>isFund</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>QYLD</td>\n", "      <td>Global X NASDAQ 100 Covered Call ETF</td>\n", "      <td>8.197931e+09</td>\n", "      <td>Financial Services</td>\n", "      <td>Asset Management - Global</td>\n", "      <td>0.65</td>\n", "      <td>17.7950</td>\n", "      <td>2.05994</td>\n", "      <td>5225008.0</td>\n", "      <td>NASDAQ</td>\n", "      <td>NASDAQ Global Market</td>\n", "      <td>US</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ZWB.TO</td>\n", "      <td>BMO Covered Call Canadian Banks ETF</td>\n", "      <td>2.988597e+09</td>\n", "      <td>Financial Services</td>\n", "      <td>Asset Management</td>\n", "      <td>0.96</td>\n", "      <td>18.2600</td>\n", "      <td>1.32000</td>\n", "      <td>88508.0</td>\n", "      <td>TSX</td>\n", "      <td>Toronto Stock Exchange</td>\n", "      <td>CA</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>XYLD</td>\n", "      <td>Global X S&amp;P 500 Covered Call ETF</td>\n", "      <td>2.885254e+09</td>\n", "      <td>Financial Services</td>\n", "      <td>Asset Management - Global</td>\n", "      <td>0.51</td>\n", "      <td>40.9825</td>\n", "      <td>3.82220</td>\n", "      <td>157906.0</td>\n", "      <td>AMEX</td>\n", "      <td>New York Stock Exchange Arca</td>\n", "      <td>US</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ZWU.TO</td>\n", "      <td>BMO Covered Call Utilities ETF</td>\n", "      <td>1.863225e+09</td>\n", "      <td>Financial Services</td>\n", "      <td>Asset Management</td>\n", "      <td>0.62</td>\n", "      <td>10.7900</td>\n", "      <td>0.84000</td>\n", "      <td>58903.0</td>\n", "      <td>TSX</td>\n", "      <td>Toronto Stock Exchange</td>\n", "      <td>CA</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ZWC.TO</td>\n", "      <td>BMO CA High Dividend Covered Call ETF</td>\n", "      <td>1.637541e+09</td>\n", "      <td>Financial Services</td>\n", "      <td>Asset Management</td>\n", "      <td>0.89</td>\n", "      <td>17.6200</td>\n", "      <td>1.54000</td>\n", "      <td>22081.0</td>\n", "      <td>TSX</td>\n", "      <td>Toronto Stock Exchange</td>\n", "      <td>CA</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   symbol                                   name    market_cap  \\\n", "0    QYLD   Global X NASDAQ 100 Covered Call ETF  8.197931e+09   \n", "1  ZWB.TO    BMO Covered Call Canadian Banks ETF  2.988597e+09   \n", "2    XYLD      Global X S&P 500 Covered Call ETF  2.885254e+09   \n", "3  ZWU.TO         BMO Covered Call Utilities ETF  1.863225e+09   \n", "4  ZWC.TO  BMO CA High Dividend Covered Call ETF  1.637541e+09   \n", "\n", "               sector                   industry  beta    price  \\\n", "0  Financial Services  Asset Management - Global  0.65  17.7950   \n", "1  Financial Services           Asset Management  0.96  18.2600   \n", "2  Financial Services  Asset Management - Global  0.51  40.9825   \n", "3  Financial Services           Asset Management  0.62  10.7900   \n", "4  Financial Services           Asset Management  0.89  17.6200   \n", "\n", "   last_annual_dividend     volume exchange                 exchange_name  \\\n", "0               2.05994  5225008.0   NASDAQ          NASDAQ Global Market   \n", "1               1.32000    88508.0      TSX        Toronto Stock Exchange   \n", "2               3.82220   157906.0     AMEX  New York Stock Exchange Arca   \n", "3               0.84000    58903.0      TSX        Toronto Stock Exchange   \n", "4               1.54000    22081.0      TSX        Toronto Stock Exchange   \n", "\n", "  country  actively_trading  isFund  \n", "0      US              True   False  \n", "1      CA              True   False  \n", "2      US              True   False  \n", "3      CA              True   False  \n", "4      CA              True   False  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.etf.search(\"covered call\", provider=\"fmp\").to_df().iloc[:5]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Search the SEC\n", "\n", "Use an empty string, `\"\"`, to return the complete list - over 10,000.\n", "\n", "The SEC sorts this list by market cap.  Applying the `to_df()` method to `all_companies` will show them from biggest-to-smallest."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10551\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>cik</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MSFT</td>\n", "      <td>MICROSOFT CORP</td>\n", "      <td>789019</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AAPL</td>\n", "      <td>Apple Inc.</td>\n", "      <td>320193</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GOOGL</td>\n", "      <td>Alphabet Inc.</td>\n", "      <td>1652044</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NVDA</td>\n", "      <td>NVIDIA CORP</td>\n", "      <td>1045810</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AMZN</td>\n", "      <td>AMAZON COM INC</td>\n", "      <td>1018724</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>META</td>\n", "      <td>Meta Platforms, Inc.</td>\n", "      <td>1326801</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>BRK-B</td>\n", "      <td>BERKSHIRE HATHAWAY INC</td>\n", "      <td>1067983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>LLY</td>\n", "      <td>ELI LILLY &amp; Co</td>\n", "      <td>59478</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>TSM</td>\n", "      <td>TAIWAN SEMICONDUCTOR MANUFACTURING CO LTD</td>\n", "      <td>1046179</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>AVGO</td>\n", "      <td>Broadcom Inc.</td>\n", "      <td>1730168</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  symbol                                       name      cik\n", "0   MSFT                             MICROSOFT CORP   789019\n", "1   AAPL                                 Apple Inc.   320193\n", "2  GOOGL                              Alphabet Inc.  1652044\n", "3   NVDA                                NVIDIA CORP  1045810\n", "4   AMZN                             AMAZON COM INC  1018724\n", "5   META                       Meta Platforms, Inc.  1326801\n", "6  BRK-<PERSON>                     BERKSHIRE HATHAWAY INC  1067983\n", "7    LLY                             ELI LILLY & Co    59478\n", "8    TSM  TAIWAN SEMICONDUCTOR MANUFACTURING CO LTD  1046179\n", "9   AVGO                              Broadcom Inc.  1730168"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["all_companies = obb.equity.search(\"\", provider=\"sec\")\n", "\n", "print(len(all_companies.results))\n", "\n", "all_companies.to_df().head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Find an Institution\n", "\n", "Some reporting companies, like invesment trusts and insurance companies, do not have a ticker symbol directly associated with them.  Filers in the US will have a CIK number, used to retrieve documents from the SEC."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>cik</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>BERKSHIRE HATHAWAY ENERGY CO</td>\n", "      <td>0001081316</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>BERKSHIRE HATHAWAY FINANCE CORP</td>\n", "      <td>0001274791</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>BERKSHIRE HATHAWAY HOMESTATE INSURANCE CO.</td>\n", "      <td>**********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>BERKSHIRE HATHAWAY INC /DE/</td>\n", "      <td>0000109694</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>BERKSHIRE HATHAWAY INC/DE</td>\n", "      <td>0000109694</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>BERKSHIRE HATHAWAY INC</td>\n", "      <td>0001067983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>BERKSHIRE HATHAWAY LIFE INSURANCE CO OF NEBRASKA</td>\n", "      <td>0001015867</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>LMZ &amp; BERKSHIRE HATHAWAY CO</td>\n", "      <td>0001652795</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                               name         cik\n", "0                      BERKSHIRE HATHAWAY ENERGY CO  0001081316\n", "1                   BERKSHIRE HATHAWAY FINANCE CORP  0001274791\n", "2        BERKSHIRE HATHAWAY HOMESTATE INSURANCE CO.  **********\n", "3                       BERKSHIRE HATHAWAY INC /DE/  0000109694\n", "4                         BERKSHIRE HATHAWAY INC/DE  0000109694\n", "5                            BERKSHIRE HATHAWAY INC  0001067983\n", "6  BERKSHIRE HATHAWAY LIFE INSURANCE CO OF NEBRASKA  0001015867\n", "7                       LMZ & BERKSHIRE HATHAWAY CO  0001652795"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["instututions = obb.regulators.sec.institutions_search(\"Berkshire Hathaway\").to_df()\n", "instututions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Find a Filing\n", "\n", "Search for filings by CIK or ticker symbol."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["filing_date                                                       2003-02-14\n", "accepted_date                                            2003-02-14 00:00:00\n", "report_type                                                           13F-NT\n", "filing_url                 https://www.sec.gov/Archives/edgar/data/000082...\n", "report_url                 https://www.sec.gov/Archives/edgar/data/000082...\n", "report_date                                                       2002-12-31\n", "act                                                                         \n", "items                                                                       \n", "primary_doc_description                 FORM 13F-NT, PERIOD ENDED 12/31/2002\n", "primary_doc                                              a87269a7e13fvnt.txt\n", "accession_number                                        0000950150-03-000213\n", "file_number                                                        028-02226\n", "film_number                                                         03565329\n", "is_inline_xbrl                                                             0\n", "is_xbrl                                                                    0\n", "size                                                                    4246\n", "complete_submission_url    https://www.sec.gov/Archives/edgar/data/000082...\n", "Name: 84, dtype: object"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["homestate_filings = obb.equity.fundamental.filings(cik=\"**********\", provider=\"sec\")\n", "\n", "homestate_filings.to_df().iloc[-1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Or, search by form type."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["filing_date                                                       2024-01-11\n", "accepted_date                                            2024-01-11 00:00:00\n", "report_type                                                          DEF 14A\n", "filing_url                 https://www.sec.gov/Archives/edgar/data/000032...\n", "report_url                 https://www.sec.gov/Archives/edgar/data/000032...\n", "report_date                                                       2024-02-28\n", "act                                                                       34\n", "items                                                                       \n", "primary_doc_description                                 APPLE INC. - DEF 14A\n", "primary_doc                                             laapl2024_def14a.htm\n", "accession_number                                        0001308179-24-000010\n", "file_number                                                        001-36743\n", "film_number                                                         24529569\n", "is_inline_xbrl                                                             1\n", "is_xbrl                                                                    1\n", "size                                                                 9051163\n", "complete_submission_url    https://www.sec.gov/Archives/edgar/data/000032...\n", "Name: 0, dtype: object"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["aapl_filings = obb.equity.fundamental.filings(\"AAPL\", type=\"4\", provider=\"sec\")\n", "\n", "aapl_filings.to_df().iloc[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Screen Markets\n", "\n", "Screeners provide a targeted search, a tool for comparison and discovery.  Find stocks from around the world with the screener endpoint, and the `openbb-fmp` provider.\n", "\n", "### Find Stocks From India"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["5662"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>market_cap</th>\n", "      <th>sector</th>\n", "      <th>industry</th>\n", "      <th>beta</th>\n", "      <th>price</th>\n", "      <th>last_annual_dividend</th>\n", "      <th>volume</th>\n", "      <th>exchange</th>\n", "      <th>exchange_name</th>\n", "      <th>country</th>\n", "      <th>is_etf</th>\n", "      <th>actively_trading</th>\n", "      <th>isFund</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>RELIANCE.NS</td>\n", "      <td>Reliance Industries Limited</td>\n", "      <td>**************</td>\n", "      <td>Energy</td>\n", "      <td>Oil &amp; Gas Refining &amp; Marketing</td>\n", "      <td>0.631</td>\n", "      <td>2996.25</td>\n", "      <td>10.0</td>\n", "      <td>5222236</td>\n", "      <td>NSE</td>\n", "      <td>National Stock Exchange of India</td>\n", "      <td>IN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>RELIANCE.BO</td>\n", "      <td>Reliance Industries Limited</td>\n", "      <td>**************</td>\n", "      <td>Energy</td>\n", "      <td>Oil &amp; Gas Refining &amp; Marketing</td>\n", "      <td>0.631</td>\n", "      <td>2995.1</td>\n", "      <td>10.0</td>\n", "      <td>193482</td>\n", "      <td>BSE</td>\n", "      <td>Bombay Stock Exchange</td>\n", "      <td>IN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>TCS.NS</td>\n", "      <td>Tata Consultancy Services Limited</td>\n", "      <td>**************</td>\n", "      <td>Technology</td>\n", "      <td>Information Technology Services</td>\n", "      <td>0.531</td>\n", "      <td>4502.0</td>\n", "      <td>56.0</td>\n", "      <td>1829132</td>\n", "      <td>NSE</td>\n", "      <td>National Stock Exchange of India</td>\n", "      <td>IN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>TCS.BO</td>\n", "      <td>Tata Consultancy Services Limited</td>\n", "      <td>**************</td>\n", "      <td>Technology</td>\n", "      <td>Information Technology Services</td>\n", "      <td>0.531</td>\n", "      <td>4499.95</td>\n", "      <td>56.0</td>\n", "      <td>81625</td>\n", "      <td>BSE</td>\n", "      <td>Bombay Stock Exchange</td>\n", "      <td>IN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>HDFCBANK.NS</td>\n", "      <td>HDFC Bank Limited</td>\n", "      <td>**************</td>\n", "      <td>Financial Services</td>\n", "      <td>Banks - Regional</td>\n", "      <td>0.833</td>\n", "      <td>1631.3</td>\n", "      <td>19.5</td>\n", "      <td>********</td>\n", "      <td>NSE</td>\n", "      <td>National Stock Exchange of India</td>\n", "      <td>IN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        symbol                               name      market_cap  \\\n", "0  RELIANCE.NS        Reliance Industries Limited  **************   \n", "1  RELIANCE.BO        Reliance Industries Limited  **************   \n", "2       TCS.NS  Tata Consultancy Services Limited  **************   \n", "3       TCS.BO  Tata Consultancy Services Limited  **************   \n", "4  HDFCBANK.NS                  HDFC Bank Limited  **************   \n", "\n", "               sector                         industry   beta    price  \\\n", "0              Energy   Oil & Gas Refining & Marketing  0.631  2996.25   \n", "1              Energy   Oil & Gas Refining & Marketing  0.631   2995.1   \n", "2          Technology  Information Technology Services  0.531   4502.0   \n", "3          Technology  Information Technology Services  0.531  4499.95   \n", "4  Financial Services                 Banks - Regional  0.833   1631.3   \n", "\n", "   last_annual_dividend    volume exchange                     exchange_name  \\\n", "0                  10.0   5222236      NSE  National Stock Exchange of India   \n", "1                  10.0    193482      BSE             Bombay Stock Exchange   \n", "2                  56.0   1829132      NSE  National Stock Exchange of India   \n", "3                  56.0     81625      BSE             Bombay Stock Exchange   \n", "4                  19.5  ********      NSE  National Stock Exchange of India   \n", "\n", "  country  is_etf  actively_trading  isFund  \n", "0      IN   False              True   False  \n", "1      IN   False              True   False  \n", "2      IN   False              True   False  \n", "3      IN   False              True   False  \n", "4      IN   False              True   False  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["results = obb.equity.screener(country=\"IN\", provider=\"fmp\").to_df()\n", "display(len(results))\n", "results.head(5).convert_dtypes()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["9"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>last_price</th>\n", "      <th>change</th>\n", "      <th>change_percent</th>\n", "      <th>market_cap</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>IBN</td>\n", "      <td>ICICI Bank Limited Common Stock</td>\n", "      <td>28.38</td>\n", "      <td>0.28</td>\n", "      <td>0.00996</td>\n", "      <td>***********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SIFY</td>\n", "      <td>Sify Technologies Limited American Depositary ...</td>\n", "      <td>0.3256</td>\n", "      <td>-0.0256</td>\n", "      <td>-0.07289</td>\n", "      <td>********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>RDY</td>\n", "      <td>Dr. Reddy's Laboratories Ltd Common Stock</td>\n", "      <td>82.698</td>\n", "      <td>-1.512</td>\n", "      <td>-0.01796</td>\n", "      <td>***********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>WIT</td>\n", "      <td>Wipro Limited Common Stock</td>\n", "      <td>6.16</td>\n", "      <td>-0.1</td>\n", "      <td>-0.01597</td>\n", "      <td>***********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>HDB</td>\n", "      <td>HDFC Bank Limited Common Stock</td>\n", "      <td>59.805</td>\n", "      <td>-0.645</td>\n", "      <td>-0.01067</td>\n", "      <td>************</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  symbol                                               name  last_price  \\\n", "0    IBN                    ICICI Bank Limited Common Stock       28.38   \n", "1   SIFY  Sify Technologies Limited American Depositary ...      0.3256   \n", "2    RDY          Dr. Reddy's Laboratories Ltd Common Stock      82.698   \n", "3    WIT                         Wipro Limited Common Stock        6.16   \n", "4    HDB                     HDFC Bank Limited Common Stock      59.805   \n", "\n", "   change  change_percent    market_cap  \n", "0    0.28         0.00996   ***********  \n", "1 -0.0256        -0.07289      ********  \n", "2  -1.512        -0.01796   ***********  \n", "3    -0.1        -0.01597   ***********  \n", "4  -0.645        -0.01067  ************  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# The Nasdaq screener is limited to the American market listings.\n", "results = obb.equity.screener(country=\"india\", provider=\"nasdaq\").to_df()\n", "display(len(results))\n", "results.head(5).convert_dtypes()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Search by Sector"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["778"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>market_cap</th>\n", "      <th>sector</th>\n", "      <th>industry</th>\n", "      <th>beta</th>\n", "      <th>price</th>\n", "      <th>last_annual_dividend</th>\n", "      <th>volume</th>\n", "      <th>exchange</th>\n", "      <th>exchange_name</th>\n", "      <th>country</th>\n", "      <th>is_etf</th>\n", "      <th>actively_trading</th>\n", "      <th>isFund</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HDFCBANK.NS</td>\n", "      <td>HDFC Bank Limited</td>\n", "      <td>**************</td>\n", "      <td>Financial Services</td>\n", "      <td>Banks - Regional</td>\n", "      <td>0.833</td>\n", "      <td>1631.3</td>\n", "      <td>19.5</td>\n", "      <td>********</td>\n", "      <td>NSE</td>\n", "      <td>National Stock Exchange of India</td>\n", "      <td>IN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ICICIBANK.NS</td>\n", "      <td>ICICI Bank Limited</td>\n", "      <td>*************</td>\n", "      <td>Financial Services</td>\n", "      <td>Banks - Regional</td>\n", "      <td>0.862</td>\n", "      <td>1191.1</td>\n", "      <td>10.0</td>\n", "      <td>8563551</td>\n", "      <td>NSE</td>\n", "      <td>National Stock Exchange of India</td>\n", "      <td>IN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SBIN.NS</td>\n", "      <td>State Bank of India</td>\n", "      <td>*************</td>\n", "      <td>Financial Services</td>\n", "      <td>Banks - Regional</td>\n", "      <td>0.888</td>\n", "      <td>820.3</td>\n", "      <td>13.7</td>\n", "      <td>7829674</td>\n", "      <td>NSE</td>\n", "      <td>National Stock Exchange of India</td>\n", "      <td>IN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SBIN.BO</td>\n", "      <td>State Bank of India</td>\n", "      <td>*************</td>\n", "      <td>Financial Services</td>\n", "      <td>Banks - Regional</td>\n", "      <td>0.888</td>\n", "      <td>820.15</td>\n", "      <td>13.7</td>\n", "      <td>494896</td>\n", "      <td>BSE</td>\n", "      <td>Bombay Stock Exchange</td>\n", "      <td>IN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>LICI.BO</td>\n", "      <td>Life Insurance Corporation of India</td>\n", "      <td>*************</td>\n", "      <td>Financial Services</td>\n", "      <td>Insurance - Life</td>\n", "      <td>0.576</td>\n", "      <td>1075.6</td>\n", "      <td>13.0</td>\n", "      <td>29486</td>\n", "      <td>BSE</td>\n", "      <td>Bombay Stock Exchange</td>\n", "      <td>IN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         symbol                                 name      market_cap  \\\n", "0   HDFCBANK.NS                    HDFC Bank Limited  **************   \n", "1  ICICIBANK.NS                   ICICI Bank Limited   *************   \n", "2       SBIN.NS                  State Bank of India   *************   \n", "3       SBIN.BO                  State Bank of India   *************   \n", "4       LICI.BO  Life Insurance Corporation of India   *************   \n", "\n", "               sector          industry   beta   price  last_annual_dividend  \\\n", "0  Financial Services  Banks - Regional  0.833  1631.3                  19.5   \n", "1  Financial Services  Banks - Regional  0.862  1191.1                  10.0   \n", "2  Financial Services  Banks - Regional  0.888   820.3                  13.7   \n", "3  Financial Services  Banks - Regional  0.888  820.15                  13.7   \n", "4  Financial Services  Insurance - Life  0.576  1075.6                  13.0   \n", "\n", "     volume exchange                     exchange_name country  is_etf  \\\n", "0  ********      NSE  National Stock Exchange of India      IN   False   \n", "1   8563551      NSE  National Stock Exchange of India      IN   False   \n", "2   7829674      NSE  National Stock Exchange of India      IN   False   \n", "3    494896      BSE             Bombay Stock Exchange      IN   False   \n", "4     29486      BSE             Bombay Stock Exchange      IN   False   \n", "\n", "   actively_trading  isFund  \n", "0              True   False  \n", "1              True   False  \n", "2              True   False  \n", "3              True   False  \n", "4              True   False  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["sector_results = obb.equity.screener(\n", "    country=\"IN\", sector=\"financial_services\", provider=\"fmp\"\n", ").to_df()\n", "display(len(sector_results))\n", "sector_results.head(5).convert_dtypes()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["1617"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>last_price</th>\n", "      <th>change</th>\n", "      <th>change_percent</th>\n", "      <th>market_cap</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CELZ</td>\n", "      <td>Creative Medical Technology Holdings, Inc. Com...</td>\n", "      <td>3.5</td>\n", "      <td>0.3169</td>\n", "      <td>0.09956</td>\n", "      <td>4683441</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>STEC</td>\n", "      <td>Santech Holdings Limited American Depositary S...</td>\n", "      <td>0.48</td>\n", "      <td>0.043</td>\n", "      <td>0.0984</td>\n", "      <td>13440000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>RILYG</td>\n", "      <td>B. Riley Financial, Inc. 5.00% Senior Notes du...</td>\n", "      <td>12.0</td>\n", "      <td>1.03</td>\n", "      <td>0.09389</td>\n", "      <td>363543636</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>PFTA</td>\n", "      <td>Perception Capital Corp. III Class A Ordinary ...</td>\n", "      <td>11.75</td>\n", "      <td>0.98</td>\n", "      <td>0.09099</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ALFUW</td>\n", "      <td>Centurion Acquisition Corp. Warrant</td>\n", "      <td>0.1199</td>\n", "      <td>0.0099</td>\n", "      <td>0.09</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  symbol                                               name  last_price  \\\n", "0   CELZ  Creative Medical Technology Holdings, Inc. Com...         3.5   \n", "1   STEC  Santech Holdings Limited American Depositary S...        0.48   \n", "2  RILYG  B. Riley Financial, Inc. 5.00% Senior Notes du...        12.0   \n", "3   PFTA  Perception Capital Corp. III Class A Ordinary ...       11.75   \n", "4  ALFUW                Centurion Acquisition Corp. Warrant      0.1199   \n", "\n", "   change  change_percent  market_cap  \n", "0  0.3169         0.09956     4683441  \n", "1   0.043          0.0984    13440000  \n", "2    1.03         0.09389   363543636  \n", "3    0.98         0.09099        <NA>  \n", "4  0.0099            0.09        <NA>  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# The same can be done with the Nasdaq provider, covering the American market.\n", "sector_results = obb.equity.screener(\n", "    sector=\"financial_services\", provider=\"nasdaq\"\n", ").to_df()\n", "display(len(sector_results))\n", "sector_results.head(5).convert_dtypes()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Search by Industry"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["25"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>country</th>\n", "      <th>sector</th>\n", "      <th>industry</th>\n", "      <th>market_cap</th>\n", "      <th>price</th>\n", "      <th>change_percent</th>\n", "      <th>volume</th>\n", "      <th>price_to_earnings</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>XELB</td>\n", "      <td>Xcel Brands Inc</td>\n", "      <td>USA</td>\n", "      <td>Consumer Cyclical</td>\n", "      <td>Apparel Manufacturing</td>\n", "      <td>1.696000e+07</td>\n", "      <td>0.72</td>\n", "      <td>0.0460</td>\n", "      <td>2070</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SGC</td>\n", "      <td>Superior Group of Companies Inc..</td>\n", "      <td>USA</td>\n", "      <td>Consumer Cyclical</td>\n", "      <td>Apparel Manufacturing</td>\n", "      <td>2.280000e+08</td>\n", "      <td>13.60</td>\n", "      <td>0.0049</td>\n", "      <td>39502</td>\n", "      <td>19.84</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>JRSH</td>\n", "      <td>Jerash holdings (US) Inc</td>\n", "      <td>USA</td>\n", "      <td>Consumer Cyclical</td>\n", "      <td>Apparel Manufacturing</td>\n", "      <td>3.626000e+07</td>\n", "      <td>2.95</td>\n", "      <td>0.0034</td>\n", "      <td>1348</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>PVH</td>\n", "      <td>PVH Corp</td>\n", "      <td>USA</td>\n", "      <td>Consumer Cyclical</td>\n", "      <td>Apparel Manufacturing</td>\n", "      <td>5.740000e+09</td>\n", "      <td>102.71</td>\n", "      <td>-0.0013</td>\n", "      <td>253106</td>\n", "      <td>9.09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>RL</td>\n", "      <td>Ralph Lauren Corp</td>\n", "      <td>USA</td>\n", "      <td>Consumer Cyclical</td>\n", "      <td>Apparel Manufacturing</td>\n", "      <td>1.051000e+10</td>\n", "      <td>169.68</td>\n", "      <td>-0.0027</td>\n", "      <td>157365</td>\n", "      <td>16.36</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  symbol                               name country             sector  \\\n", "0   XELB                    Xcel Brands Inc     USA  Consumer Cyclical   \n", "1    SGC  Superior Group of Companies Inc..     USA  Consumer Cyclical   \n", "2   JRSH           Jerash holdings (US) Inc     USA  Consumer Cyclical   \n", "3    PVH                           PVH Corp     USA  Consumer Cyclical   \n", "4     RL                  Ralph Lauren Corp     USA  Consumer Cyclical   \n", "\n", "                industry    market_cap   price  change_percent  volume  \\\n", "0  Apparel Manufacturing  1.696000e+07    0.72          0.0460    2070   \n", "1  Apparel Manufacturing  2.280000e+08   13.60          0.0049   39502   \n", "2  Apparel Manufacturing  3.626000e+07    2.95          0.0034    1348   \n", "3  Apparel Manufacturing  5.740000e+09  102.71         -0.0013  253106   \n", "4  Apparel Manufacturing  1.051000e+10  169.68         -0.0027  157365   \n", "\n", "   price_to_earnings  \n", "0                NaN  \n", "1              19.84  \n", "2                NaN  \n", "3               9.09  \n", "4              16.36  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["industry_results = obb.equity.screener(\n", "    industry=\"apparel_manufacturing\", provider=\"finviz\"\n", ").to_df()\n", "display(len(industry_results))\n", "industry_results.head(5)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["297"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>market_cap</th>\n", "      <th>sector</th>\n", "      <th>industry</th>\n", "      <th>beta</th>\n", "      <th>price</th>\n", "      <th>last_annual_dividend</th>\n", "      <th>volume</th>\n", "      <th>exchange</th>\n", "      <th>exchange_name</th>\n", "      <th>country</th>\n", "      <th>is_etf</th>\n", "      <th>actively_trading</th>\n", "      <th>isFund</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TIMKEN.BO</td>\n", "      <td>Timken India Limited</td>\n", "      <td>2.787793e+11</td>\n", "      <td>Industrials</td>\n", "      <td>Manufacturing - Tools &amp; Accessories</td>\n", "      <td>0.575</td>\n", "      <td>3706.25</td>\n", "      <td>2.5</td>\n", "      <td>5827</td>\n", "      <td>BSE</td>\n", "      <td>Bombay Stock Exchange</td>\n", "      <td>IN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>TIMKEN.NS</td>\n", "      <td>Timken India Limited</td>\n", "      <td>2.787003e+11</td>\n", "      <td>Industrials</td>\n", "      <td>Manufacturing - Tools &amp; Accessories</td>\n", "      <td>0.575</td>\n", "      <td>3705.20</td>\n", "      <td>2.5</td>\n", "      <td>115595</td>\n", "      <td>NSE</td>\n", "      <td>National Stock Exchange of India</td>\n", "      <td>IN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SKFINDIA.BO</td>\n", "      <td>SKF India Limited</td>\n", "      <td>2.615542e+11</td>\n", "      <td>Industrials</td>\n", "      <td>Manufacturing - Tools &amp; Accessories</td>\n", "      <td>0.462</td>\n", "      <td>5290.55</td>\n", "      <td>130.0</td>\n", "      <td>1950</td>\n", "      <td>BSE</td>\n", "      <td>Bombay Stock Exchange</td>\n", "      <td>IN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SKFINDIA.NS</td>\n", "      <td>SKF India Limited</td>\n", "      <td>2.614405e+11</td>\n", "      <td>Industrials</td>\n", "      <td>Manufacturing - Tools &amp; Accessories</td>\n", "      <td>0.462</td>\n", "      <td>5288.25</td>\n", "      <td>130.0</td>\n", "      <td>62289</td>\n", "      <td>NSE</td>\n", "      <td>National Stock Exchange of India</td>\n", "      <td>IN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>PTCIL.NS</td>\n", "      <td>PTC Industries Limited</td>\n", "      <td>1.890617e+11</td>\n", "      <td>Industrials</td>\n", "      <td>Manufacturing - Metal Fabrication</td>\n", "      <td>0.510</td>\n", "      <td>13092.10</td>\n", "      <td>NaN</td>\n", "      <td>2965</td>\n", "      <td>NSE</td>\n", "      <td>National Stock Exchange of India</td>\n", "      <td>IN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        symbol                    name    market_cap       sector  \\\n", "0    TIMKEN.BO    Timken India Limited  2.787793e+11  Industrials   \n", "1    TIMKEN.NS    Timken India Limited  2.787003e+11  Industrials   \n", "2  SKFINDIA.BO       SKF India Limited  2.615542e+11  Industrials   \n", "3  SKFINDIA.NS       SKF India Limited  2.614405e+11  Industrials   \n", "4     PTCIL.NS  PTC Industries Limited  1.890617e+11  Industrials   \n", "\n", "                              industry   beta     price  last_annual_dividend  \\\n", "0  Manufacturing - Tools & Accessories  0.575   3706.25                   2.5   \n", "1  Manufacturing - Tools & Accessories  0.575   3705.20                   2.5   \n", "2  Manufacturing - Tools & Accessories  0.462   5290.55                 130.0   \n", "3  Manufacturing - Tools & Accessories  0.462   5288.25                 130.0   \n", "4    Manufacturing - Metal Fabrication  0.510  13092.10                   NaN   \n", "\n", "   volume exchange                     exchange_name country  is_etf  \\\n", "0    5827      BSE             Bombay Stock Exchange      IN   False   \n", "1  115595      NSE  National Stock Exchange of India      IN   False   \n", "2    1950      BSE             Bombay Stock Exchange      IN   False   \n", "3   62289      NSE  National Stock Exchange of India      IN   False   \n", "4    2965      NSE  National Stock Exchange of India      IN   False   \n", "\n", "   actively_trading  isFund  \n", "0              True   False  \n", "1              True   False  \n", "2              True   False  \n", "3              True   False  \n", "4              True   False  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["industry_results = obb.equity.screener(\n", "    industry=\"manufacturing\", provider=\"fmp\", country=\"IN\"\n", ").to_df()\n", "display(len(industry_results))\n", "industry_results.head(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Search by Exchange\n", "\n", "Some countries, like America, have multiple exchanges.  Narrow the search by combining two or more parameters.  The example below finds the companies listed on the American Stock Exchange (AMEX) that are domiciled in China."]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>market_cap</th>\n", "      <th>sector</th>\n", "      <th>industry</th>\n", "      <th>beta</th>\n", "      <th>price</th>\n", "      <th>last_annual_dividend</th>\n", "      <th>volume</th>\n", "      <th>exchange</th>\n", "      <th>exchange_name</th>\n", "      <th>country</th>\n", "      <th>is_etf</th>\n", "      <th>actively_trading</th>\n", "      <th>isFund</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MYND</td>\n", "      <td>Mynd.ai, Inc.</td>\n", "      <td>93150152</td>\n", "      <td>Consumer Defensive</td>\n", "      <td>Education &amp; Training Services</td>\n", "      <td>0.939</td>\n", "      <td>1.9200</td>\n", "      <td>5.628</td>\n", "      <td>10348</td>\n", "      <td>AMEX</td>\n", "      <td>American Stock Exchange</td>\n", "      <td>CN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AMBO</td>\n", "      <td>Ambow Education Holding Ltd.</td>\n", "      <td>3731855</td>\n", "      <td>Consumer Defensive</td>\n", "      <td>Education &amp; Training Services</td>\n", "      <td>0.733</td>\n", "      <td>1.3065</td>\n", "      <td>NaN</td>\n", "      <td>21603</td>\n", "      <td>AMEX</td>\n", "      <td>American Stock Exchange</td>\n", "      <td>CN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CPHI</td>\n", "      <td>China Pharma Holdings, Inc.</td>\n", "      <td>3664258</td>\n", "      <td>Healthcare</td>\n", "      <td>Drug Manufacturers - Specialty &amp; Generic</td>\n", "      <td>0.726</td>\n", "      <td>0.2135</td>\n", "      <td>NaN</td>\n", "      <td>77994</td>\n", "      <td>AMEX</td>\n", "      <td>American Stock Exchange</td>\n", "      <td>CN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DXF</td>\n", "      <td>Dunxin Financial Holdings Limited</td>\n", "      <td>3243104</td>\n", "      <td>Financial Services</td>\n", "      <td>Financial - Credit Services</td>\n", "      <td>1.304</td>\n", "      <td>0.1394</td>\n", "      <td>NaN</td>\n", "      <td>187314</td>\n", "      <td>AMEX</td>\n", "      <td>American Stock Exchange</td>\n", "      <td>CN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ITP</td>\n", "      <td>IT Tech Packaging, Inc.</td>\n", "      <td>2413803</td>\n", "      <td>Basic Materials</td>\n", "      <td>Paper, Lumber &amp; Forest Products</td>\n", "      <td>-0.120</td>\n", "      <td>0.2398</td>\n", "      <td>NaN</td>\n", "      <td>3062</td>\n", "      <td>AMEX</td>\n", "      <td>American Stock Exchange</td>\n", "      <td>CN</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  symbol                               name  market_cap              sector  \\\n", "0   MYND                      Mynd.ai, Inc.    93150152  Consumer Defensive   \n", "1   AMBO       Ambow Education Holding Ltd.     3731855  Consumer Defensive   \n", "2   CPHI        China Pharma Holdings, Inc.     3664258          Healthcare   \n", "3    DXF  Dunxin Financial Holdings Limited     3243104  Financial Services   \n", "4    ITP            IT Tech Packaging, Inc.     2413803     Basic Materials   \n", "\n", "                                   industry   beta   price  \\\n", "0             Education & Training Services  0.939  1.9200   \n", "1             Education & Training Services  0.733  1.3065   \n", "2  Drug Manufacturers - Specialty & Generic  0.726  0.2135   \n", "3               Financial - Credit Services  1.304  0.1394   \n", "4           Paper, Lumber & Forest Products -0.120  0.2398   \n", "\n", "   last_annual_dividend  volume exchange            exchange_name country  \\\n", "0                 5.628   10348     AMEX  American Stock Exchange      CN   \n", "1                   NaN   21603     AMEX  American Stock Exchange      CN   \n", "2                   NaN   77994     AMEX  American Stock Exchange      CN   \n", "3                   NaN  187314     AMEX  American Stock Exchange      CN   \n", "4                   NaN    3062     AMEX  American Stock Exchange      CN   \n", "\n", "   is_etf  actively_trading  isFund  \n", "0   False              True   False  \n", "1   False              True   False  \n", "2   False              True   False  \n", "3   False              True   False  \n", "4   False              True   False  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["exchange_results = obb.equity.screener(\n", "    exchange=\"amex\", country=\"CN\", provider=\"fmp\"\n", ").to_df()\n", "display(len(exchange_results))\n", "exchange_results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Filter ADRs\n", "\n", "Use the Nasdaq screener to get only American Depositary Receipts"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>last_price</th>\n", "      <th>change</th>\n", "      <th>change_percent</th>\n", "      <th>market_cap</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>GDS</td>\n", "      <td>GDS Holdings Limited ADS</td>\n", "      <td>16.1350</td>\n", "      <td>1.3750</td>\n", "      <td>0.09316</td>\n", "      <td>95038491</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>YQ</td>\n", "      <td>17 Education &amp; Technology Group Inc. American ...</td>\n", "      <td>2.1700</td>\n", "      <td>0.1700</td>\n", "      <td>0.08500</td>\n", "      <td>7207460</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>STEC</td>\n", "      <td>Santech Holdings Limited American Depositary S...</td>\n", "      <td>0.4729</td>\n", "      <td>0.0359</td>\n", "      <td>0.08215</td>\n", "      <td>4125000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>TURB</td>\n", "      <td>Turbo Energy, S.A. American Depositary Shares</td>\n", "      <td>1.5342</td>\n", "      <td>0.1043</td>\n", "      <td>0.07294</td>\n", "      <td>1000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>FRES</td>\n", "      <td>Fresh2 Group Limited American Depositary Shares</td>\n", "      <td>1.7297</td>\n", "      <td>0.0997</td>\n", "      <td>0.06117</td>\n", "      <td>644183</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>190</th>\n", "      <td>JFU</td>\n", "      <td>9F Inc. American Depositary Shares</td>\n", "      <td>1.8000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3584421</td>\n", "    </tr>\n", "    <tr>\n", "      <th>191</th>\n", "      <td>XHG</td>\n", "      <td>XChange TEC.INC American Depositary Shares</td>\n", "      <td>0.9500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2780673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>192</th>\n", "      <td>JZ</td>\n", "      <td>Jianzhi Education Technology Group Company Lim...</td>\n", "      <td>0.8000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>NWGL</td>\n", "      <td>Nature Wood Group Limited American Depositary ...</td>\n", "      <td>1.6000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1493743</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>FORTY</td>\n", "      <td>Formula Systems (1985) Ltd. American Depositar...</td>\n", "      <td>76.0550</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>131939</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>195 rows × 6 columns</p>\n", "</div>"], "text/plain": ["    symbol                                               name  last_price  \\\n", "0      GDS                           GDS Holdings Limited ADS     16.1350   \n", "1       YQ  17 Education & Technology Group Inc. American ...      2.1700   \n", "2     STEC  Santech Holdings Limited American Depositary S...      0.4729   \n", "3     TURB      Turbo Energy, S.A. American Depositary Shares      1.5342   \n", "4     FRES    Fresh2 Group Limited American Depositary Shares      1.7297   \n", "..     ...                                                ...         ...   \n", "190    JFU                 9F Inc. American Depositary Shares      1.8000   \n", "191    XHG         XChange TEC.INC American Depositary Shares      0.9500   \n", "192     JZ  Jianzhi Education Technology Group Company Lim...      0.8000   \n", "193   NWGL  Nature Wood Group Limited American Depositary ...      1.6000   \n", "194  FORTY  Formula Systems (1985) Ltd. American Depositar...     76.0550   \n", "\n", "     change  change_percent  market_cap  \n", "0    1.3750         0.09316    95038491  \n", "1    0.1700         0.08500     7207460  \n", "2    0.0359         0.08215     4125000  \n", "3    0.1043         0.07294     1000000  \n", "4    0.0997         0.06117      644183  \n", "..      ...             ...         ...  \n", "190     NaN             NaN     3584421  \n", "191     NaN             NaN     2780673  \n", "192     NaN             NaN     1666667  \n", "193     NaN             NaN     1493743  \n", "194     NaN             NaN      131939  \n", "\n", "[195 rows x 6 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.equity.screener(exsubcategory=\"adr\", provider=\"nasdaq\").to_df()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Filter by <PERSON><PERSON>\n", "\n", "Applying some filters refines and targets the search. The example below finds listing on the NYSE domiciled in the USA, with a market cap between $100-300 billion, and exhibiting a beta value of less than 0.5"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>market_cap</th>\n", "      <th>sector</th>\n", "      <th>industry</th>\n", "      <th>beta</th>\n", "      <th>price</th>\n", "      <th>last_annual_dividend</th>\n", "      <th>volume</th>\n", "      <th>exchange</th>\n", "      <th>exchange_name</th>\n", "      <th>country</th>\n", "      <th>is_etf</th>\n", "      <th>actively_trading</th>\n", "      <th>isFund</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MRK</td>\n", "      <td>Merck &amp; Co., Inc.</td>\n", "      <td>294367485300</td>\n", "      <td>Healthcare</td>\n", "      <td>Drug Manufacturers - General</td>\n", "      <td>0.389000</td>\n", "      <td>116.130</td>\n", "      <td>3.08000</td>\n", "      <td>3111763</td>\n", "      <td>NYSE</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>US</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>VZ</td>\n", "      <td>Verizon Communications Inc.</td>\n", "      <td>171053845200</td>\n", "      <td>Communication Services</td>\n", "      <td>Telecommunications Services</td>\n", "      <td>0.393000</td>\n", "      <td>40.635</td>\n", "      <td>2.66000</td>\n", "      <td>6202285</td>\n", "      <td>NYSE</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>US</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>TBC</td>\n", "      <td>AT&amp;T Inc. 5.625% Global Notes d</td>\n", "      <td>140078065351</td>\n", "      <td>Communication Services</td>\n", "      <td>Telecommunications Services</td>\n", "      <td>0.275703</td>\n", "      <td>24.565</td>\n", "      <td>1.40628</td>\n", "      <td>18782</td>\n", "      <td>NYSE</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>US</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>PGR</td>\n", "      <td>The Progressive Corporation</td>\n", "      <td>139775286220</td>\n", "      <td>Financial Services</td>\n", "      <td>Insurance - Property &amp; Casualty</td>\n", "      <td>0.356000</td>\n", "      <td>238.660</td>\n", "      <td>0.40000</td>\n", "      <td>616656</td>\n", "      <td>NYSE</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>US</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>TBB</td>\n", "      <td>AT&amp;T Inc. 5.35% GLB NTS 66</td>\n", "      <td>139658512827</td>\n", "      <td>Communication Services</td>\n", "      <td>Telecommunications Services</td>\n", "      <td>0.253859</td>\n", "      <td>23.395</td>\n", "      <td>1.33752</td>\n", "      <td>21852</td>\n", "      <td>NYSE</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>US</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>LMT</td>\n", "      <td>Lockheed Martin Corporation</td>\n", "      <td>132376882460</td>\n", "      <td>Industrials</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "      <td>0.454000</td>\n", "      <td>555.370</td>\n", "      <td>12.60000</td>\n", "      <td>304130</td>\n", "      <td>NYSE</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>US</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  symbol                             name    market_cap  \\\n", "0    MRK                Merck & Co., Inc.  294367485300   \n", "1     VZ      Verizon Communications Inc.  171053845200   \n", "2    TBC  AT&T Inc. 5.625% Global Notes d  140078065351   \n", "3    PGR      The Progressive Corporation  139775286220   \n", "4    TBB       AT&T Inc. 5.35% GLB NTS 66  139658512827   \n", "5    LMT      Lockheed Martin Corporation  132376882460   \n", "\n", "                   sector                         industry      beta    price  \\\n", "0              Healthcare     Drug Manufacturers - General  0.389000  116.130   \n", "1  Communication Services      Telecommunications Services  0.393000   40.635   \n", "2  Communication Services      Telecommunications Services  0.275703   24.565   \n", "3      Financial Services  Insurance - Property & Casualty  0.356000  238.660   \n", "4  Communication Services      Telecommunications Services  0.253859   23.395   \n", "5             Industrials              Aerospace & Defense  0.454000  555.370   \n", "\n", "   last_annual_dividend   volume exchange            exchange_name country  \\\n", "0               3.08000  3111763     NYSE  New York Stock Exchange      US   \n", "1               2.66000  6202285     NYSE  New York Stock Exchange      US   \n", "2               1.40628    18782     NYSE  New York Stock Exchange      US   \n", "3               0.40000   616656     NYSE  New York Stock Exchange      US   \n", "4               1.33752    21852     NYSE  New York Stock Exchange      US   \n", "5              12.60000   304130     NYSE  New York Stock Exchange      US   \n", "\n", "   is_etf  actively_trading  isFund  \n", "0   False              True   False  \n", "1   False              True   False  \n", "2   False              True   False  \n", "3   False              True   False  \n", "4   False              True   False  \n", "5   False              True   False  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.equity.screener(\n", "    exchange=\"nyse\",\n", "    mktcap_min=100000000000,\n", "    mktcap_max=300000000000,\n", "    country=\"us\",\n", "    beta_max=0.5,\n", "    provider=\"fmp\",\n", ").to_df()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON> Screener\n", "\n", "The `openbb-finviz` provider extension supports screener presets from V3 SDK and Terminal. See the details here: [https://pypi.org/project/openbb-finviz/](https://pypi.org/project/openbb-finviz/)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>country</th>\n", "      <th>sector</th>\n", "      <th>industry</th>\n", "      <th>market_cap</th>\n", "      <th>price</th>\n", "      <th>change_percent</th>\n", "      <th>volume</th>\n", "      <th>price_to_earnings</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>GRFS</td>\n", "      <td>Grifols SA ADR</td>\n", "      <td>Spain</td>\n", "      <td>Healthcare</td>\n", "      <td>Drug Manufacturers - General</td>\n", "      <td>2.270000e+09</td>\n", "      <td>8.81</td>\n", "      <td>0.1488</td>\n", "      <td>5055854</td>\n", "      <td>35.13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ZM</td>\n", "      <td>Zoom Video Communications Inc</td>\n", "      <td>USA</td>\n", "      <td>Technology</td>\n", "      <td>Software - Application</td>\n", "      <td>2.097000e+10</td>\n", "      <td>67.81</td>\n", "      <td>0.1258</td>\n", "      <td>15285228</td>\n", "      <td>24.27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>EVH</td>\n", "      <td>Evolent Health Inc</td>\n", "      <td>USA</td>\n", "      <td>Healthcare</td>\n", "      <td>Health Information Services</td>\n", "      <td>3.680000e+09</td>\n", "      <td>31.61</td>\n", "      <td>0.1247</td>\n", "      <td>4519171</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>GDS</td>\n", "      <td>GDS Holdings Limited ADR</td>\n", "      <td>China</td>\n", "      <td>Technology</td>\n", "      <td>Information Technology Services</td>\n", "      <td>3.060000e+09</td>\n", "      <td>16.19</td>\n", "      <td>0.0968</td>\n", "      <td>2308546</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>LCID</td>\n", "      <td>Lucid Group Inc</td>\n", "      <td>USA</td>\n", "      <td>Consumer Cyclical</td>\n", "      <td>Auto Manufacturers</td>\n", "      <td>9.070000e+09</td>\n", "      <td>3.91</td>\n", "      <td>0.0922</td>\n", "      <td>41571321</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>OSIS</td>\n", "      <td>OSI Systems Inc.</td>\n", "      <td>USA</td>\n", "      <td>Technology</td>\n", "      <td>Electronic Components</td>\n", "      <td>2.690000e+09</td>\n", "      <td>157.68</td>\n", "      <td>0.0903</td>\n", "      <td>299756</td>\n", "      <td>21.68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>ZK</td>\n", "      <td>ZEEKR Intelligent Technology Holding Ltd. ADR</td>\n", "      <td>China</td>\n", "      <td>Consumer Cyclical</td>\n", "      <td>Auto Manufacturers</td>\n", "      <td>4.450000e+09</td>\n", "      <td>17.99</td>\n", "      <td>0.0677</td>\n", "      <td>792117</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>QXO</td>\n", "      <td>QXO Inc.</td>\n", "      <td>USA</td>\n", "      <td>Technology</td>\n", "      <td>Software - Application</td>\n", "      <td>5.870000e+09</td>\n", "      <td>14.34</td>\n", "      <td>0.0614</td>\n", "      <td>4871978</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  symbol                                           name country  \\\n", "0   GRFS                                 Grifols SA ADR   Spain   \n", "1     ZM                  Zoom Video Communications Inc     USA   \n", "2    EVH                             Evolent Health Inc     USA   \n", "3    GDS                       GDS Holdings Limited ADR   China   \n", "4   LCID                                Lucid Group Inc     USA   \n", "5   OSIS                               OSI Systems Inc.     USA   \n", "6     ZK  ZEEKR Intelligent Technology Holding Ltd. ADR   China   \n", "7    QXO                                       QXO Inc.     USA   \n", "\n", "              sector                         industry    market_cap   price  \\\n", "0         Healthcare     Drug Manufacturers - General  2.270000e+09    8.81   \n", "1         Technology           Software - Application  2.097000e+10   67.81   \n", "2         Healthcare      Health Information Services  3.680000e+09   31.61   \n", "3         Technology  Information Technology Services  3.060000e+09   16.19   \n", "4  Consumer Cyclical               Auto Manufacturers  9.070000e+09    3.91   \n", "5         Technology            Electronic Components  2.690000e+09  157.68   \n", "6  Consumer Cyclical               Auto Manufacturers  4.450000e+09   17.99   \n", "7         Technology           Software - Application  5.870000e+09   14.34   \n", "\n", "   change_percent    volume  price_to_earnings  \n", "0          0.1488   5055854              35.13  \n", "1          0.1258  15285228              24.27  \n", "2          0.1247   4519171                NaN  \n", "3          0.0968   2308546                NaN  \n", "4          0.0922  41571321                NaN  \n", "5          0.0903    299756              21.68  \n", "6          0.0677    792117                NaN  \n", "7          0.0614   4871978                NaN  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["obb.equity.screener(\n", "    metric=\"overview\", signal=\"top_gainers\", provider=\"finviz\", mktcap=\"mid_over\"\n", ").to_df()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get Available Indices\n", "\n", "List all indices from a source with:"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["274\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>code</th>\n", "      <th>symbol</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>S&amp;P/ASX 200 Index (AUD)</td>\n", "      <td>au_asx200</td>\n", "      <td>^AXJO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>S&amp;P/ASX 200 Energy Sector Index (AUD)</td>\n", "      <td>au_energy</td>\n", "      <td>^AXEJ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>S&amp;P/ASX 200 Resources Sector Index (AUD)</td>\n", "      <td>au_resources</td>\n", "      <td>^AXJR</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92</th>\n", "      <td>S&amp;P/ASX 200 Materials Sector Index (AUD)</td>\n", "      <td>au_materials</td>\n", "      <td>^AXMJ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94</th>\n", "      <td>S&amp;P/ASX 200 Industrials Sector Index (AUD)</td>\n", "      <td>au_industrials</td>\n", "      <td>^AXNJ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>S&amp;P/ASX 200 Consumer Discretionary Sector Inde...</td>\n", "      <td>au_discretionary</td>\n", "      <td>^AXDJ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>S&amp;P/ASX 200 Consumer Staples Sector Index (AUD)</td>\n", "      <td>au_staples</td>\n", "      <td>^AXSJ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>S&amp;P/ASX 200 Health Care Sector Index (AUD)</td>\n", "      <td>au_health</td>\n", "      <td>^AXHJ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>S&amp;P/ASX 200 Financials Sector Index (AUD)</td>\n", "      <td>au_financials</td>\n", "      <td>^AXFJ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>S&amp;P/ASX 200 A-REIT Industry Index (AUD)</td>\n", "      <td>au_reit</td>\n", "      <td>^AXPJ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>100</th>\n", "      <td>S&amp;P/ASX 200 Info Tech Sector Index (AUD)</td>\n", "      <td>au_tech</td>\n", "      <td>^AXIJ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101</th>\n", "      <td>S&amp;P/ASX 200 Communications Sector Index (AUD)</td>\n", "      <td>au_communications</td>\n", "      <td>^AXTJ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>102</th>\n", "      <td>S&amp;P/ASX 200 Utilities Sector Index (AUD)</td>\n", "      <td>au_utilities</td>\n", "      <td>^AXUJ</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                  name               code  \\\n", "88                             S&P/ASX 200 Index (AUD)          au_asx200   \n", "90               S&P/ASX 200 Energy Sector Index (AUD)          au_energy   \n", "91            S&P/ASX 200 Resources Sector Index (AUD)       au_resources   \n", "92            S&P/ASX 200 Materials Sector Index (AUD)       au_materials   \n", "94          S&P/ASX 200 Industrials Sector Index (AUD)     au_industrials   \n", "95   S&P/ASX 200 Consumer Discretionary Sector Inde...   au_discretionary   \n", "96     S&P/ASX 200 Consumer Staples Sector Index (AUD)         au_staples   \n", "97          S&P/ASX 200 Health Care Sector Index (AUD)          au_health   \n", "98           S&P/ASX 200 Financials Sector Index (AUD)      au_financials   \n", "99             S&P/ASX 200 A-REIT Industry Index (AUD)            au_reit   \n", "100           S&P/ASX 200 Info Tech Sector Index (AUD)            au_tech   \n", "101      S&P/ASX 200 Communications Sector Index (AUD)  au_communications   \n", "102           S&P/ASX 200 Utilities Sector Index (AUD)       au_utilities   \n", "\n", "    symbol  \n", "88   ^AXJO  \n", "90   ^AXEJ  \n", "91   ^AXJR  \n", "92   ^AXMJ  \n", "94   ^AXNJ  \n", "95   ^AXDJ  \n", "96   ^AXSJ  \n", "97   ^AXHJ  \n", "98   ^AXFJ  \n", "99   ^AXPJ  \n", "100  ^AXIJ  \n", "101  ^AXTJ  \n", "102  ^AXUJ  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["indices = obb.index.available(provider=\"yfinance\").to_df()\n", "print(len(indices))\n", "\n", "indices[indices[\"name\"].str.contains(\"ASX 200\")]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Filter the list down by querying the DataFrame."]}, {"cell_type": "markdown", "metadata": {}, "source": ["With the `openbb-yfinance` extension, index time series can be  loaded using the ticker symbol or short code.  Non-American indices have a code beginning with the two-letter country code."]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-08-22</th>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            open  high   low  close  volume\n", "date                                       \n", "2024-08-22  True  True  True   True    True"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["(\n", "    obb.index.price.historical(\"au_utilities\", provider=\"yfinance\").to_df().tail(1)\n", "    == obb.index.price.historical(\"^AXUJ\", provider=\"yfinance\").to_df().tail(1)\n", ")"]}], "metadata": {"kernelspec": {"display_name": "obb-sdk4", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}