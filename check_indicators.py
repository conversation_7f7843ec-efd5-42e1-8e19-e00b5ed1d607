#!/usr/bin/env python3
"""
检查指标卡片显示
"""

import requests
import time

def check_all_indicators():
    """检查所有指标"""
    print("🔍 检查所有指标卡片显示...")
    
    indicators = [
        'vcm', 'tei', 'lvr', 'api_index', 'mtr', 
        'atr', 'rsi', 'vwap', 'blce', 'macd', 'boll', 'kdj'
    ]
    
    symbol = "000001.SZ"
    success_count = 0
    
    for i, indicator in enumerate(indicators, 1):
        try:
            print(f"\n{i:2d}. 测试 {indicator}...")
            response = requests.get(f'http://localhost:8000/indicator/{indicator}?symbol={symbol}', timeout=8)
            
            if response.status_code == 200:
                data = response.json()
                if data and len(data) > 0:
                    latest = data[-1]
                    value = latest.get('v', latest.get('prob_up', 'N/A'))
                    print(f"    ✅ 成功 - 数据点: {len(data)}, 最新值: {value}")
                    success_count += 1
                else:
                    print(f"    ❌ 无数据")
            else:
                print(f"    ❌ 失败 - 状态码: {response.status_code}")
                print(f"    响应: {response.text[:100]}...")
                
        except Exception as e:
            print(f"    ❌ 异常: {e}")
    
    print(f"\n📊 总结: {success_count}/{len(indicators)} 个指标正常")
    
    if success_count == len(indicators):
        print("🎉 所有指标卡片应该正确显示！")
    else:
        print("⚠️ 部分指标可能无法显示")
    
    return success_count == len(indicators)

def check_search():
    """检查搜索功能"""
    print("\n🔍 检查搜索功能...")
    
    try:
        response = requests.get('http://localhost:8000/search?query=000001', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 搜索正常 - 找到 {len(data)} 个结果")
            if data:
                for item in data:
                    print(f"  - {item.get('ts_code', '')} {item.get('name', '')}")
            return True
        else:
            print(f"❌ 搜索失败 - 状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 搜索异常: {e}")
        return False

def main():
    """主检查函数"""
    print("🚀 检查指标卡片显示状态")
    print("=" * 60)
    
    # 检查搜索
    search_ok = check_search()
    
    # 检查指标
    indicators_ok = check_all_indicators()
    
    print("\n" + "=" * 60)
    print("📊 检查结果:")
    print(f"搜索功能: {'✅ 正常' if search_ok else '❌ 异常'}")
    print(f"指标显示: {'✅ 正常' if indicators_ok else '❌ 异常'}")
    
    if search_ok and indicators_ok:
        print("\n🎉 所有功能正常！指标卡片应该正确显示")
        print("🌐 请访问: http://localhost:8000/app")
        print("📝 操作步骤:")
        print("  1. 搜索股票 (如: 000001)")
        print("  2. 点击选择股票")
        print("  3. 查看右侧12个指标卡片")
        print("  4. 点击任意卡片查看图表")
    else:
        print("\n⚠️ 部分功能异常，请检查服务器状态")

if __name__ == "__main__":
    main()
