#!/usr/bin/env python3
"""
基于阿里云百炼的AI股票分析模块
"""

import os
import json
import requests
from typing import Dict, Any, Optional
from dotenv import load_dotenv

load_dotenv()


class LLMAnalyzer:
    """基于阿里云百炼的AI分析器"""
    
    def __init__(self):
        self.api_key = os.getenv('BAILIAN_API_KEY')
        self.base_url = os.getenv('BAILIAN_BASE_URL')
        self.model = os.getenv('BAILIAN_MODEL', 'qwen-plus')
        
        if not self.api_key:
            raise ValueError("BAILIAN_API_KEY not found in environment variables")
        if not self.base_url:
            raise ValueError("BAILIAN_BASE_URL not found in environment variables")
    
    def analyze_stock(self, symbol: str, indicators: Dict[str, Any], prompt: str = "") -> str:
        """
        基于真实指标数据进行AI股票分析
        
        Args:
            symbol: 股票代码
            indicators: 指标数据
            prompt: 用户提问
            
        Returns:
            AI分析结果
        """
        try:
            # 构建分析提示词
            system_prompt = self._build_system_prompt()
            user_prompt = self._build_user_prompt(symbol, indicators, prompt)
            
            # 调用阿里云百炼API
            response = self._call_api(system_prompt, user_prompt)
            
            return response
            
        except Exception as e:
            return f"AI分析暂时不可用: {str(e)}"
    
    def _build_system_prompt(self) -> str:
        """构建系统提示词"""
        return """你是一位专业的股票分析师，具有丰富的技术分析经验。请基于提供的真实股票指标数据进行专业分析。

分析要求：
1. 基于真实的技术指标数据进行分析
2. 重点关注自研指标（VCM、TEI、LVR、API等）的含义和信号
3. 结合传统技术指标（RSI、MACD、布林带等）进行综合判断
4. 提供明确的投资建议和风险提示
5. 分析要客观、专业、易懂

指标说明：
- VCM: 放量顺势动量指标，反映成交量与价格动量的关系
- TEI: 趋势熵分指标，衡量价格趋势的稳定性
- LVR: 流动性风险指标，评估市场流动性风险
- API: 非对称压力指标，检测买卖压力的不对称性
- MTR: 多时间尺度共振指标，多周期趋势分析
- RSI: 相对强弱指标，判断超买超卖
- MACD: 趋势跟踪指标
- BOLL: 布林带，支撑阻力位
- KDJ: 随机指标，短期买卖信号

请用中文回答，格式清晰，包含具体的数值分析。"""

    def _build_user_prompt(self, symbol: str, indicators: Dict[str, Any], prompt: str) -> str:
        """构建用户提示词"""
        indicator_data = indicators.get('latest', {})
        data_source = indicators.get('data_source', 'unknown')
        indicator_count = indicators.get('count', 0)
        
        # 构建指标数据描述
        indicator_desc = []
        for indicator, data in indicator_data.items():
            if isinstance(data, dict):
                value = data.get('value', 'N/A')
                trend = data.get('trend', 'unknown')
                indicator_desc.append(f"- {indicator.upper()}: {value} (趋势: {trend})")
            else:
                indicator_desc.append(f"- {indicator.upper()}: {data}")
        
        user_prompt = f"""请分析股票 {symbol} 的投资价值：

数据来源: {data_source}
指标数量: {indicator_count}个真实指标

当前指标数据：
{chr(10).join(indicator_desc)}

用户问题: {prompt if prompt else '请基于以上真实指标数据进行综合分析，给出投资建议'}

请提供：
1. 技术指标分析
2. 自研指标解读
3. 综合投资建议
4. 风险评估
5. 操作建议"""

        return user_prompt
    
    def _call_api(self, system_prompt: str, user_prompt: str) -> str:
        """调用阿里云百炼API"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': self.model,
            'messages': [
                {
                    'role': 'system',
                    'content': system_prompt
                },
                {
                    'role': 'user',
                    'content': user_prompt
                }
            ],
            'temperature': 0.7,
            'max_tokens': 2000
        }
        
        try:
            response = requests.post(
                f'{self.base_url}/chat/completions',
                headers=headers,
                json=data,
                timeout=60  # 增加超时时间
            )

            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                if content:
                    return content
                else:
                    return "AI分析返回空内容，请重试"
            else:
                error_msg = f"API调用失败: {response.status_code}"
                if response.text:
                    error_msg += f" - {response.text[:200]}"
                return error_msg

        except requests.exceptions.Timeout:
            return "AI分析超时，网络连接较慢，请稍后重试"
        except requests.exceptions.ConnectionError:
            return "无法连接到AI服务，请检查网络连接"
        except Exception as e:
            return f"AI分析出错: {str(e)[:100]}"


# 创建全局实例
try:
    llm_analyzer = LLMAnalyzer()
except Exception as e:
    print(f"LLM分析器初始化失败: {e}")
    llm_analyzer = None
