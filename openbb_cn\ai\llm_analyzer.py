#!/usr/bin/env python3
"""
基于阿里云百炼的AI股票分析模块
"""

import os
import json
import requests
from typing import Dict, Any, Optional
from dotenv import load_dotenv

load_dotenv()


class LLMAnalyzer:
    """基于阿里云百炼的AI分析器"""
    
    def __init__(self):
        self.api_key = os.getenv('BAILIAN_API_KEY')
        self.base_url = os.getenv('BAILIAN_BASE_URL')
        self.model = os.getenv('BAILIAN_MODEL', 'qwen-plus')
        
        if not self.api_key:
            raise ValueError("BAILIAN_API_KEY not found in environment variables")
        if not self.base_url:
            raise ValueError("BAILIAN_BASE_URL not found in environment variables")
    
    def analyze_stock(self, symbol: str, indicators: Dict[str, Any], prompt: str = "", news: Dict[str, Any] = None, price_data: Dict[str, Any] = None) -> str:
        """
        基于真实指标数据、价格数据和新闻进行AI股票分析

        Args:
            symbol: 股票代码
            indicators: 指标数据
            prompt: 用户提问
            news: 新闻数据
            price_data: 价格数据

        Returns:
            AI分析结果
        """
        try:
            # 构建分析提示词
            system_prompt = self._build_system_prompt()
            user_prompt = self._build_user_prompt(symbol, indicators, prompt, news, price_data)
            
            # 调用阿里云百炼API
            response = self._call_api(system_prompt, user_prompt)
            
            return response
            
        except Exception as e:
            return f"AI分析暂时不可用: {str(e)}"
    
    def _build_system_prompt(self) -> str:
        """构建系统提示词"""
        return """你是一位专业的股票分析师，具有丰富的技术分析经验。请基于提供的真实股票指标数据进行专业分析。

分析要求：
1. 基于真实的技术指标数据进行分析
2. 重点关注自研指标（VCM、TEI、LVR、API等）的含义和信号
3. 结合传统技术指标（RSI、MACD、布林带等）进行综合判断
4. 提供明确的投资建议和风险提示
5. 分析要客观、专业、易懂

指标说明：
- VCM: 放量顺势动量指标，反映成交量与价格动量的关系
- TEI: 趋势熵分指标，衡量价格趋势的稳定性
- LVR: 流动性风险指标，评估市场流动性风险
- API: 非对称压力指标，检测买卖压力的不对称性
- MTR: 多时间尺度共振指标，多周期趋势分析
- RSI: 相对强弱指标，判断超买超卖
- MACD: 趋势跟踪指标
- BOLL: 布林带，支撑阻力位
- KDJ: 随机指标，短期买卖信号

请用中文回答，格式清晰，包含具体的数值分析。"""

    def _build_user_prompt(self, symbol: str, indicators: Dict[str, Any], prompt: str, news: Dict[str, Any] = None, price_data: Dict[str, Any] = None) -> str:
        """构建用户提示词"""
        indicator_data = indicators.get('latest', {})
        data_source = indicators.get('data_source', 'unknown')
        indicator_count = indicators.get('count', 0)

        print(f"[DEBUG] 构建提示词 - 指标数据: {indicator_data}")
        print(f"[DEBUG] 数据源: {data_source}, 指标数量: {indicator_count}")

        # 检查是否有指标数据
        if not indicator_data or indicator_count == 0:
            return f"""股票 {symbol} 分析请求：

⚠️ 当前没有接收到技术指标数据。

用户问题: {prompt if prompt else '请进行股票分析'}

请回复：由于缺少技术指标数据，无法进行具体分析。请确保：
1. 已选择股票并加载指标数据
2. 指标计算完成
3. 数据传输正常

建议用户刷新页面重新选择股票。"""

        # 构建详细的指标数据描述
        indicator_sections = {
            '传统技术指标': [],
            '自研指标': []
        }

        # 分类指标
        traditional_indicators = ['rsi', 'macd', 'boll', 'kdj', 'atr', 'vwap']
        custom_indicators = ['vcm', 'tei', 'lvr', 'api_index', 'mtr', 'blce']

        for indicator, data in indicator_data.items():
            if isinstance(data, dict):
                value = data.get('value', 'N/A')
                trend = data.get('trend', 'unknown')
                date = data.get('date', 'unknown')

                indicator_line = f"- {indicator.upper()}: {value} (趋势: {trend}, 日期: {date})"

                if indicator.lower() in traditional_indicators:
                    indicator_sections['传统技术指标'].append(indicator_line)
                elif indicator.lower() in custom_indicators:
                    indicator_sections['自研指标'].append(indicator_line)
                else:
                    indicator_sections['传统技术指标'].append(indicator_line)
            else:
                indicator_line = f"- {indicator.upper()}: {data}"
                indicator_sections['传统技术指标'].append(indicator_line)

        # 构建完整的指标描述
        indicator_desc = []
        if indicator_sections['传统技术指标']:
            indicator_desc.append("### 传统技术指标：")
            indicator_desc.extend(indicator_sections['传统技术指标'])

        if indicator_sections['自研指标']:
            indicator_desc.append("\n### 自研指标：")
            indicator_desc.extend(indicator_sections['自研指标'])

        # 构建新闻分析部分
        news_desc = []
        if news and news.get('data'):
            news_data = news.get('data', [])
            news_count = news.get('count', 0)

            if news_count > 0:
                news_desc.append(f"\n### 📰 最新相关新闻 ({news_count}条)：")
                for i, news_item in enumerate(news_data[:5], 1):  # 最多显示5条新闻
                    title = news_item.get('title', '')
                    summary = news_item.get('summary', '')
                    time = news_item.get('time', '')
                    sentiment = news_item.get('sentiment', 'neutral')

                    sentiment_emoji = {'positive': '📈', 'negative': '📉', 'neutral': '📊'}.get(sentiment, '📊')
                    news_desc.append(f"{i}. {sentiment_emoji} {title}")
                    news_desc.append(f"   摘要: {summary}")
                    news_desc.append(f"   时间: {time}")
                    news_desc.append("")

        # 构建价格数据分析部分
        price_desc = []
        if price_data and price_data.get('current_price'):
            current_price = price_data.get('current_price', 0)
            change = price_data.get('change', 0)
            change_percent = price_data.get('change_percent', '0')
            volume = price_data.get('volume', 0)
            high = price_data.get('high', 0)
            low = price_data.get('low', 0)

            price_desc.append(f"\n### 💰 实时价格数据：")
            price_desc.append(f"当前价格: {current_price:.2f}元")
            price_desc.append(f"涨跌幅: {change:+.2f}元 ({change_percent}%)")
            price_desc.append(f"今日最高: {high:.2f}元")
            price_desc.append(f"今日最低: {low:.2f}元")
            price_desc.append(f"成交量: {volume:,.0f}股")

            # 价格趋势分析
            if float(change_percent) > 3:
                price_desc.append("📈 价格表现: 强势上涨")
            elif float(change_percent) > 0:
                price_desc.append("📈 价格表现: 温和上涨")
            elif float(change_percent) < -3:
                price_desc.append("📉 价格表现: 明显下跌")
            elif float(change_percent) < 0:
                price_desc.append("📉 价格表现: 小幅下跌")
            else:
                price_desc.append("📊 价格表现: 基本持平")

        user_prompt = f"""请对股票 {symbol} 进行全面投资分析：

📊 数据来源: {data_source}
📈 指标数量: {indicator_count}个真实指标
📋 数据质量: {indicators.get('data_quality', 'good')}

🔍 技术指标分析：
{chr(10).join(indicator_desc)}
{chr(10).join(price_desc) if price_desc else ''}
{chr(10).join(news_desc) if news_desc else ''}

❓ 分析要求: {prompt if prompt else '请基于以上完整数据进行深度分析，给出明确的投资建议'}

📋 请按以下结构提供专业分析：

## 1. 技术指标综合评估
- **传统指标**: RSI、MACD、布林带、KDJ等指标解读
- **自研指标**: VCM、TEI、LVR、API、MTR等专业指标分析
- **指标一致性**: 多指标信号的一致性和分歧点

## 2. 价格走势分析
- **当前价位**: 相对历史价格的位置分析
- **支撑阻力**: 关键价格支撑位和阻力位
- **成交量配合**: 价格与成交量的配合情况

## 3. 新闻面影响评估
- **利好因素**: 正面新闻对股价的推动作用
- **风险因素**: 负面信息的潜在影响
- **市场情绪**: 新闻对投资者情绪的影响

## 4. 投资建议（必须明确）
### 🟢 买入建议
- **适用条件**: 在什么情况下建议买入
- **目标价位**: 具体的买入价格区间
- **预期收益**: 短期和中期收益预期

### 🔴 卖出建议
- **适用条件**: 在什么情况下建议卖出
- **止损价位**: 具体的止损价格
- **风险控制**: 卖出的风险管理策略

### 🟡 持仓建议
- **适用条件**: 在什么情况下建议持有
- **持有策略**: 持仓期间的操作建议
- **调仓时机**: 何时考虑调整仓位

## 5. 风险评估与总结
- **主要风险**: 当前面临的主要投资风险
- **风险等级**: 高/中/低风险评级
- **操作建议**: 具体的操作策略和注意事项

请基于提供的真实数据进行客观分析，给出具体可操作的投资建议。"""

        return user_prompt
    
    def _call_api(self, system_prompt: str, user_prompt: str) -> str:
        """调用阿里云百炼API"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': self.model,
            'messages': [
                {
                    'role': 'system',
                    'content': system_prompt
                },
                {
                    'role': 'user',
                    'content': user_prompt
                }
            ],
            'temperature': 0.7,
            'max_tokens': 2000
        }
        
        try:
            response = requests.post(
                f'{self.base_url}/chat/completions',
                headers=headers,
                json=data,
                timeout=60  # 增加超时时间
            )

            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                if content:
                    return content
                else:
                    return "AI分析返回空内容，请重试"
            else:
                error_msg = f"API调用失败: {response.status_code}"
                if response.text:
                    error_msg += f" - {response.text[:200]}"
                return error_msg

        except requests.exceptions.Timeout:
            return "AI分析超时，网络连接较慢，请稍后重试"
        except requests.exceptions.ConnectionError:
            return "无法连接到AI服务，请检查网络连接"
        except Exception as e:
            return f"AI分析出错: {str(e)[:100]}"


# 创建全局实例
try:
    llm_analyzer = LLMAnalyzer()
except Exception as e:
    print(f"LLM分析器初始化失败: {e}")
    llm_analyzer = None
