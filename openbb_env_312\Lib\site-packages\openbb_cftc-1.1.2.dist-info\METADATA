Metadata-Version: 2.3
Name: openbb-cftc
Version: 1.1.2
Summary: The mission of the Commodity Futures Trading Commission (CFTC) is to promote the integrity, resilience, and vibrancy of the U.S. derivatives markets through sound regulation.
License: AGPL-3.0-only
Author: OpenBB Team
Author-email: <EMAIL>
Requires-Python: >=3.9.21,<3.13
Classifier: License :: OSI Approved :: GNU Affero General Public License v3
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Dist: openbb-core (>=1.4.8,<2.0.0)
Description-Content-Type: text/markdown

# CFTC Provider Extension

## Installation

Install from PyPI with:

```sh
pip install openbb-cftc
```

Install this extension locally with:

```sh
pip install -e .
```

## Credentials

Credentials are not required, but your IP address may be subject to throttling limits.

API requests made using an application token are not throttled.

Create a free account here: https://evergreen.data.socrata.com/signup

Then, generate the app_token by signing in with the credentials here: https://publicreporting.cftc.gov/profile/edit/developer_settings.

### Credentials Key

If adding a token, use `cftc_app_token` as the key in the `user_settings.json` file. The value expected value is the app_token and not the `secret` or `api_key`.


