@echo off
echo 🚀 启动OpenBB专业金融分析系统
echo ================================

REM 激活Python 3.12环境
call openbb_env_312\Scripts\activate.bat

echo ✅ 环境已激活
echo 📊 Python版本:
python --version

echo.
echo 🎯 启动OpenBB分析系统...
echo ================================

REM 运行主程序
python openbb_main.py

echo.
echo ================================
echo 💡 分析完成！查看生成的文件：
echo - Excel报告: openbb_analysis_*.xlsx
echo - CSV数据: openbb_analysis_*.csv
echo.

pause
