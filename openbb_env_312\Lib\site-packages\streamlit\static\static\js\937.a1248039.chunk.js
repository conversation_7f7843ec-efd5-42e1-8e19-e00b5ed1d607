/*! For license information please see 937.a1248039.chunk.js.LICENSE.txt */
"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[937],{40937:(e,t,n)=>{n.d(t,{Z:()=>Q});const{entries:o,setPrototypeOf:r,isFrozen:i,getPrototypeOf:a,getOwnPropertyDescriptor:l}=Object;let{freeze:c,seal:s,create:u}=Object,{apply:m,construct:p}="undefined"!==typeof Reflect&&Reflect;c||(c=function(e){return e}),s||(s=function(e){return e}),m||(m=function(e,t,n){return e.apply(t,n)}),p||(p=function(e,t){return new e(...t)});const f=w(Array.prototype.forEach),d=w(Array.prototype.pop),h=w(Array.prototype.push),g=w(String.prototype.toLowerCase),T=w(String.prototype.toString),y=w(String.prototype.match),E=w(String.prototype.replace),A=w(String.prototype.indexOf),_=w(String.prototype.trim),b=w(Object.prototype.hasOwnProperty),N=w(RegExp.prototype.test),S=(R=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return p(R,t)});var R;function w(e){return function(t){for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return m(e,t,o)}}function D(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:g;r&&r(e,null);let o=t.length;for(;o--;){let r=t[o];if("string"===typeof r){const e=n(r);e!==r&&(i(t)||(t[o]=e),r=e)}e[r]=!0}return e}function L(e){for(let t=0;t<e.length;t++){b(e,t)||(e[t]=null)}return e}function k(e){const t=u(null);for(const[n,r]of o(e)){b(e,n)&&(Array.isArray(r)?t[n]=L(r):r&&"object"===typeof r&&r.constructor===Object?t[n]=k(r):t[n]=r)}return t}function v(e,t){for(;null!==e;){const n=l(e,t);if(n){if(n.get)return w(n.get);if("function"===typeof n.value)return w(n.value)}e=a(e)}return function(){return null}}const C=c(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),x=c(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),O=c(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),I=c(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),M=c(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),U=c(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),P=c(["#text"]),F=c(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),H=c(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),z=c(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),B=c(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),W=s(/\{\{[\w\W]*|[\w\W]*\}\}/gm),G=s(/<%[\w\W]*|[\w\W]*%>/gm),Y=s(/\${[\w\W]*}/gm),j=s(/^data-[\-\w.\u00B7-\uFFFF]/),q=s(/^aria-[\-\w]+$/),X=s(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),K=s(/^(?:\w+script|data):/i),V=s(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),$=s(/^html$/i);var Z=Object.freeze({__proto__:null,MUSTACHE_EXPR:W,ERB_EXPR:G,TMPLIT_EXPR:Y,DATA_ATTR:j,ARIA_ATTR:q,IS_ALLOWED_URI:X,IS_SCRIPT_OR_DATA:K,ATTR_WHITESPACE:V,DOCTYPE_NAME:$});const J=function(){return"undefined"===typeof window?null:window};var Q=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:J();const n=t=>e(t);if(n.version="3.0.9",n.removed=[],!t||!t.document||9!==t.document.nodeType)return n.isSupported=!1,n;let{document:r}=t;const i=r,a=i.currentScript,{DocumentFragment:l,HTMLTemplateElement:s,Node:m,Element:p,NodeFilter:R,NamedNodeMap:w=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:L,DOMParser:W,trustedTypes:G}=t,Y=p.prototype,j=v(Y,"cloneNode"),q=v(Y,"nextSibling"),K=v(Y,"childNodes"),V=v(Y,"parentNode");if("function"===typeof s){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let Q,ee="";const{implementation:te,createNodeIterator:ne,createDocumentFragment:oe,getElementsByTagName:re}=r,{importNode:ie}=i;let ae={};n.isSupported="function"===typeof o&&"function"===typeof V&&te&&void 0!==te.createHTMLDocument;const{MUSTACHE_EXPR:le,ERB_EXPR:ce,TMPLIT_EXPR:se,DATA_ATTR:ue,ARIA_ATTR:me,IS_SCRIPT_OR_DATA:pe,ATTR_WHITESPACE:fe}=Z;let{IS_ALLOWED_URI:de}=Z,he=null;const ge=D({},[...C,...x,...O,...M,...P]);let Te=null;const ye=D({},[...F,...H,...z,...B]);let Ee=Object.seal(u(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Ae=null,_e=null,be=!0,Ne=!0,Se=!1,Re=!0,we=!1,De=!1,Le=!1,ke=!1,ve=!1,Ce=!1,xe=!1,Oe=!0,Ie=!1,Me=!0,Ue=!1,Pe={},Fe=null;const He=D({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let ze=null;const Be=D({},["audio","video","img","source","image","track"]);let We=null;const Ge=D({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Ye="http://www.w3.org/1998/Math/MathML",je="http://www.w3.org/2000/svg",qe="http://www.w3.org/1999/xhtml";let Xe=qe,Ke=!1,Ve=null;const $e=D({},[Ye,je,qe],T);let Ze=null;const Je=["application/xhtml+xml","text/html"];let Qe=null,et=null;const tt=r.createElement("form"),nt=function(e){return e instanceof RegExp||e instanceof Function},ot=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!et||et!==e){if(e&&"object"===typeof e||(e={}),e=k(e),Ze=-1===Je.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,Qe="application/xhtml+xml"===Ze?T:g,he=b(e,"ALLOWED_TAGS")?D({},e.ALLOWED_TAGS,Qe):ge,Te=b(e,"ALLOWED_ATTR")?D({},e.ALLOWED_ATTR,Qe):ye,Ve=b(e,"ALLOWED_NAMESPACES")?D({},e.ALLOWED_NAMESPACES,T):$e,We=b(e,"ADD_URI_SAFE_ATTR")?D(k(Ge),e.ADD_URI_SAFE_ATTR,Qe):Ge,ze=b(e,"ADD_DATA_URI_TAGS")?D(k(Be),e.ADD_DATA_URI_TAGS,Qe):Be,Fe=b(e,"FORBID_CONTENTS")?D({},e.FORBID_CONTENTS,Qe):He,Ae=b(e,"FORBID_TAGS")?D({},e.FORBID_TAGS,Qe):{},_e=b(e,"FORBID_ATTR")?D({},e.FORBID_ATTR,Qe):{},Pe=!!b(e,"USE_PROFILES")&&e.USE_PROFILES,be=!1!==e.ALLOW_ARIA_ATTR,Ne=!1!==e.ALLOW_DATA_ATTR,Se=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Re=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,we=e.SAFE_FOR_TEMPLATES||!1,De=e.WHOLE_DOCUMENT||!1,ve=e.RETURN_DOM||!1,Ce=e.RETURN_DOM_FRAGMENT||!1,xe=e.RETURN_TRUSTED_TYPE||!1,ke=e.FORCE_BODY||!1,Oe=!1!==e.SANITIZE_DOM,Ie=e.SANITIZE_NAMED_PROPS||!1,Me=!1!==e.KEEP_CONTENT,Ue=e.IN_PLACE||!1,de=e.ALLOWED_URI_REGEXP||X,Xe=e.NAMESPACE||qe,Ee=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&nt(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Ee.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&nt(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Ee.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"===typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Ee.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),we&&(Ne=!1),Ce&&(ve=!0),Pe&&(he=D({},P),Te=[],!0===Pe.html&&(D(he,C),D(Te,F)),!0===Pe.svg&&(D(he,x),D(Te,H),D(Te,B)),!0===Pe.svgFilters&&(D(he,O),D(Te,H),D(Te,B)),!0===Pe.mathMl&&(D(he,M),D(Te,z),D(Te,B))),e.ADD_TAGS&&(he===ge&&(he=k(he)),D(he,e.ADD_TAGS,Qe)),e.ADD_ATTR&&(Te===ye&&(Te=k(Te)),D(Te,e.ADD_ATTR,Qe)),e.ADD_URI_SAFE_ATTR&&D(We,e.ADD_URI_SAFE_ATTR,Qe),e.FORBID_CONTENTS&&(Fe===He&&(Fe=k(Fe)),D(Fe,e.FORBID_CONTENTS,Qe)),Me&&(he["#text"]=!0),De&&D(he,["html","head","body"]),he.table&&(D(he,["tbody"]),delete Ae.tbody),e.TRUSTED_TYPES_POLICY){if("function"!==typeof e.TRUSTED_TYPES_POLICY.createHTML)throw S('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!==typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw S('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');Q=e.TRUSTED_TYPES_POLICY,ee=Q.createHTML("")}else void 0===Q&&(Q=function(e,t){if("object"!==typeof e||"function"!==typeof e.createPolicy)return null;let n=null;const o="data-tt-policy-suffix";t&&t.hasAttribute(o)&&(n=t.getAttribute(o));const r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML:e=>e,createScriptURL:e=>e})}catch(i){return console.warn("TrustedTypes policy "+r+" could not be created."),null}}(G,a)),null!==Q&&"string"===typeof ee&&(ee=Q.createHTML(""));c&&c(e),et=e}},rt=D({},["mi","mo","mn","ms","mtext"]),it=D({},["foreignobject","desc","title","annotation-xml"]),at=D({},["title","style","font","a","script"]),lt=D({},[...x,...O,...I]),ct=D({},[...M,...U]),st=function(e){h(n.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){e.remove()}},ut=function(e,t){try{h(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(o){h(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!Te[e])if(ve||Ce)try{st(t)}catch(o){}else try{t.setAttribute(e,"")}catch(o){}},mt=function(e){let t=null,n=null;if(ke)e="<remove></remove>"+e;else{const t=y(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===Ze&&Xe===qe&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const o=Q?Q.createHTML(e):e;if(Xe===qe)try{t=(new W).parseFromString(o,Ze)}catch(a){}if(!t||!t.documentElement){t=te.createDocument(Xe,"template",null);try{t.documentElement.innerHTML=Ke?ee:o}catch(a){}}const i=t.body||t.documentElement;return e&&n&&i.insertBefore(r.createTextNode(n),i.childNodes[0]||null),Xe===qe?re.call(t,De?"html":"body")[0]:De?t.documentElement:i},pt=function(e){return ne.call(e.ownerDocument||e,e,R.SHOW_ELEMENT|R.SHOW_COMMENT|R.SHOW_TEXT,null)},ft=function(e){return"function"===typeof m&&e instanceof m},dt=function(e,t,o){ae[e]&&f(ae[e],(e=>{e.call(n,t,o,et)}))},ht=function(e){let t=null;if(dt("beforeSanitizeElements",e,null),(o=e)instanceof L&&("string"!==typeof o.nodeName||"string"!==typeof o.textContent||"function"!==typeof o.removeChild||!(o.attributes instanceof w)||"function"!==typeof o.removeAttribute||"function"!==typeof o.setAttribute||"string"!==typeof o.namespaceURI||"function"!==typeof o.insertBefore||"function"!==typeof o.hasChildNodes))return st(e),!0;var o;const r=Qe(e.nodeName);if(dt("uponSanitizeElement",e,{tagName:r,allowedTags:he}),e.hasChildNodes()&&!ft(e.firstElementChild)&&N(/<[/\w]/g,e.innerHTML)&&N(/<[/\w]/g,e.textContent))return st(e),!0;if(!he[r]||Ae[r]){if(!Ae[r]&&Tt(r)){if(Ee.tagNameCheck instanceof RegExp&&N(Ee.tagNameCheck,r))return!1;if(Ee.tagNameCheck instanceof Function&&Ee.tagNameCheck(r))return!1}if(Me&&!Fe[r]){const t=V(e)||e.parentNode,n=K(e)||e.childNodes;if(n&&t){for(let o=n.length-1;o>=0;--o)t.insertBefore(j(n[o],!0),q(e))}}return st(e),!0}return e instanceof p&&!function(e){let t=V(e);t&&t.tagName||(t={namespaceURI:Xe,tagName:"template"});const n=g(e.tagName),o=g(t.tagName);return!!Ve[e.namespaceURI]&&(e.namespaceURI===je?t.namespaceURI===qe?"svg"===n:t.namespaceURI===Ye?"svg"===n&&("annotation-xml"===o||rt[o]):Boolean(lt[n]):e.namespaceURI===Ye?t.namespaceURI===qe?"math"===n:t.namespaceURI===je?"math"===n&&it[o]:Boolean(ct[n]):e.namespaceURI===qe?!(t.namespaceURI===je&&!it[o])&&!(t.namespaceURI===Ye&&!rt[o])&&!ct[n]&&(at[n]||!lt[n]):!("application/xhtml+xml"!==Ze||!Ve[e.namespaceURI]))}(e)?(st(e),!0):"noscript"!==r&&"noembed"!==r&&"noframes"!==r||!N(/<\/no(script|embed|frames)/i,e.innerHTML)?(we&&3===e.nodeType&&(t=e.textContent,f([le,ce,se],(e=>{t=E(t,e," ")})),e.textContent!==t&&(h(n.removed,{element:e.cloneNode()}),e.textContent=t)),dt("afterSanitizeElements",e,null),!1):(st(e),!0)},gt=function(e,t,n){if(Oe&&("id"===t||"name"===t)&&(n in r||n in tt))return!1;if(Ne&&!_e[t]&&N(ue,t));else if(be&&N(me,t));else if(!Te[t]||_e[t]){if(!(Tt(e)&&(Ee.tagNameCheck instanceof RegExp&&N(Ee.tagNameCheck,e)||Ee.tagNameCheck instanceof Function&&Ee.tagNameCheck(e))&&(Ee.attributeNameCheck instanceof RegExp&&N(Ee.attributeNameCheck,t)||Ee.attributeNameCheck instanceof Function&&Ee.attributeNameCheck(t))||"is"===t&&Ee.allowCustomizedBuiltInElements&&(Ee.tagNameCheck instanceof RegExp&&N(Ee.tagNameCheck,n)||Ee.tagNameCheck instanceof Function&&Ee.tagNameCheck(n))))return!1}else if(We[t]);else if(N(de,E(n,fe,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==A(n,"data:")||!ze[e]){if(Se&&!N(pe,E(n,fe,"")));else if(n)return!1}else;return!0},Tt=function(e){return"annotation-xml"!==e&&e.indexOf("-")>0},yt=function(e){dt("beforeSanitizeAttributes",e,null);const{attributes:t}=e;if(!t)return;const o={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Te};let r=t.length;for(;r--;){const a=t[r],{name:l,namespaceURI:c,value:s}=a,u=Qe(l);let m="value"===l?s:_(s);if(o.attrName=u,o.attrValue=m,o.keepAttr=!0,o.forceKeepAttr=void 0,dt("uponSanitizeAttribute",e,o),m=o.attrValue,o.forceKeepAttr)continue;if(ut(l,e),!o.keepAttr)continue;if(!Re&&N(/\/>/i,m)){ut(l,e);continue}we&&f([le,ce,se],(e=>{m=E(m,e," ")}));const p=Qe(e.nodeName);if(gt(p,u,m)){if(!Ie||"id"!==u&&"name"!==u||(ut(l,e),m="user-content-"+m),Q&&"object"===typeof G&&"function"===typeof G.getAttributeType)if(c);else switch(G.getAttributeType(p,u)){case"TrustedHTML":m=Q.createHTML(m);break;case"TrustedScriptURL":m=Q.createScriptURL(m)}try{c?e.setAttributeNS(c,l,m):e.setAttribute(l,m),d(n.removed)}catch(i){}}}dt("afterSanitizeAttributes",e,null)},Et=function e(t){let n=null;const o=pt(t);for(dt("beforeSanitizeShadowDOM",t,null);n=o.nextNode();)dt("uponSanitizeShadowNode",n,null),ht(n)||(n.content instanceof l&&e(n.content),yt(n));dt("afterSanitizeShadowDOM",t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=null,r=null,a=null,c=null;if(Ke=!e,Ke&&(e="\x3c!--\x3e"),"string"!==typeof e&&!ft(e)){if("function"!==typeof e.toString)throw S("toString is not a function");if("string"!==typeof(e=e.toString()))throw S("dirty is not a string, aborting")}if(!n.isSupported)return e;if(Le||ot(t),n.removed=[],"string"===typeof e&&(Ue=!1),Ue){if(e.nodeName){const t=Qe(e.nodeName);if(!he[t]||Ae[t])throw S("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof m)o=mt("\x3c!----\x3e"),r=o.ownerDocument.importNode(e,!0),1===r.nodeType&&"BODY"===r.nodeName||"HTML"===r.nodeName?o=r:o.appendChild(r);else{if(!ve&&!we&&!De&&-1===e.indexOf("<"))return Q&&xe?Q.createHTML(e):e;if(o=mt(e),!o)return ve?null:xe?ee:""}o&&ke&&st(o.firstChild);const s=pt(Ue?e:o);for(;a=s.nextNode();)ht(a)||(a.content instanceof l&&Et(a.content),yt(a));if(Ue)return e;if(ve){if(Ce)for(c=oe.call(o.ownerDocument);o.firstChild;)c.appendChild(o.firstChild);else c=o;return(Te.shadowroot||Te.shadowrootmode)&&(c=ie.call(i,c,!0)),c}let u=De?o.outerHTML:o.innerHTML;return De&&he["!doctype"]&&o.ownerDocument&&o.ownerDocument.doctype&&o.ownerDocument.doctype.name&&N($,o.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+o.ownerDocument.doctype.name+">\n"+u),we&&f([le,ce,se],(e=>{u=E(u,e," ")})),Q&&xe?Q.createHTML(u):u},n.setConfig=function(){ot(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Le=!0},n.clearConfig=function(){et=null,Le=!1},n.isValidAttribute=function(e,t,n){et||ot({});const o=Qe(e),r=Qe(t);return gt(o,r,n)},n.addHook=function(e,t){"function"===typeof t&&(ae[e]=ae[e]||[],h(ae[e],t))},n.removeHook=function(e){if(ae[e])return d(ae[e])},n.removeHooks=function(e){ae[e]&&(ae[e]=[])},n.removeAllHooks=function(){ae={}},n}()}}]);