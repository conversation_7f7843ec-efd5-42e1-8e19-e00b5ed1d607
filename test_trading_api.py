#!/usr/bin/env python3
"""
测试模拟炒股API功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_account_api():
    """测试账户API"""
    print("🔍 测试账户API...")
    try:
        response = requests.get(f"{BASE_URL}/smart/account", timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 账户API正常")
            print(f"现金: ¥{data['cash']:,.2f}")
            print(f"总资产: ¥{data['total_value']:,.2f}")
            print(f"持仓数量: {len(data['positions'])}")
            return True
        else:
            print(f"❌ 账户API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 账户API异常: {e}")
        return False

def test_rebalance_api():
    """测试自动交易API"""
    print("\n🤖 测试自动交易API...")
    try:
        response = requests.post(
            f"{BASE_URL}/smart/rebalance",
            headers={"Content-Type": "application/json"},
            timeout=15
        )
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 自动交易API正常")
            print(f"成功: {data['success']}")
            message = data.get('message', '')
            print(f"消息: {message[:200]}...")
            return True
        else:
            print(f"❌ 自动交易API失败: {response.status_code}")
            print(f"响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 自动交易API异常: {e}")
        return False

def test_buy_api():
    """测试买入API"""
    print("\n📈 测试买入API...")
    try:
        payload = {
            "symbol": "000001",
            "amount": 5000,
            "strategy": "test"
        }
        
        response = requests.post(
            f"{BASE_URL}/smart/buy",
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 买入API正常")
            print(f"成功: {data['success']}")
            print(f"消息: {data.get('message', '')}")
            return True
        else:
            print(f"❌ 买入API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 买入API异常: {e}")
        return False

def test_config_api():
    """测试配置API"""
    print("\n⚙️ 测试配置API...")
    try:
        # 启用PSO和GA
        payload = {
            "pso_enabled": True,
            "ga_enabled": True
        }
        
        response = requests.post(
            f"{BASE_URL}/smart/config",
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 配置API正常")
            print(f"成功: {data['success']}")
            return True
        else:
            print(f"❌ 配置API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 配置API异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试模拟炒股API功能")
    print("=" * 50)
    
    # 等待服务器启动
    time.sleep(2)
    
    results = []
    
    # 测试各个API
    results.append(test_account_api())
    results.append(test_config_api())
    results.append(test_buy_api())
    results.append(test_rebalance_api())
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有API测试通过！")
    else:
        print("⚠️ 部分API测试失败，请检查服务器状态")

if __name__ == "__main__":
    main()
