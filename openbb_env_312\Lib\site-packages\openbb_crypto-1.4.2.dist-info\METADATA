Metadata-Version: 2.3
Name: openbb-crypto
Version: 1.4.2
Summary: Crypto extension for OpenBB
License: AGPL-3.0-only
Author: OpenBB Team
Author-email: <EMAIL>
Requires-Python: >=3.9.21,<3.13
Classifier: License :: OSI Approved :: GNU Affero General Public License v3
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Dist: openbb-core (>=1.4.8,<2.0.0)
Description-Content-Type: text/markdown

# Crypto data extension for OpenBB Platform

This extension provides a set of commands for crypto data retrieval.

## Installation

To install the extension, run the following command in this folder:

```bash
pip install openbb-crypto
```

Documentation available [here](https://docs.openbb.co/platform/developer_guide/contributing).

