"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[3301],{40763:(t,n,a)=>{a.d(n,{Z:()=>l});var e=a(66845),i=a(7974),o=a.n(i),r=a(23175),c=a.n(r),s=a(40864);const m=t=>{let{className:n,scriptRunId:a,numParticles:e,numParticleTypes:i,ParticleComponent:r}=t;return(0,s.jsx)("div",{className:c()(n,"stHidden"),"data-testid":"".concat(n),children:o()(e).map((t=>{const n=Math.floor(Math.random()*i);return(0,s.jsx)(r,{particleType:n},a+t)}))})},l=(0,e.memo)(m)},69436:(t,n,a)=>{a.r(n),a.d(n,{NUM_FLAKES:()=>h,default:()=>b});var e=a(66845);const i=a.p+"static/media/flake-0.beded754e8024c73d9d2.png",o=a.p+"static/media/flake-1.8077dc154e0bf900aa73.png",r=a.p+"static/media/flake-2.e3f07d06933dd0e84c24.png";var c,s=a(40763),m=a(50669),l=a(1515),d=a(7865);const p=function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return Math.random()*(t-n)+n},u=(0,l.Z)("img",{target:"ekdfb790"})((t=>{let{theme:n}=t;return{position:"fixed",top:"".concat(-150,"px"),marginLeft:"".concat(-75,"px"),zIndex:n.zIndices.balloons,left:"".concat(p(90,10),"vw"),animationDelay:"".concat(p(4e3),"ms"),height:"".concat(150,"px"),width:"".concat(150,"px"),pointerEvents:"none",animationDuration:"3000ms",animationName:(0,d.F4)(c||(c=(0,m.Z)(["\n  from {\n    transform:\n      translateY(0)\n      rotateX(","deg)\n      rotateY(","deg)\n      rotateZ(","deg);\n  }\n\n  to {\n    transform:\n      translateY(calc(100vh + ","px))\n      rotateX(0)\n      rotateY(0)\n      rotateZ(0);\n  }\n"])),p(360),p(360),p(360),150),animationTimingFunction:"ease-in",animationDirection:"normal",animationIterationCount:1,opacity:1}}),"");var f=a(40864);const h=100,g=[i,o,r],x=g.length,v=t=>{let{particleType:n}=t;return(0,f.jsx)(u,{src:g[n]})},k=function(t){let{scriptRunId:n}=t;return(0,f.jsx)(s.Z,{className:"snow",scriptRunId:n,numParticleTypes:x,numParticles:h,ParticleComponent:v})},b=(0,e.memo)(k)}}]);