"""Helper functions for innovative indicators."""

import pandas as pd
import numpy as np
from typing import Dict, <PERSON><PERSON>, Any


def validate_data(data: pd.DataFrame) -> bool:
    """
    Validate OHLCV data structure and quality.
    
    Args:
        data: DataFrame with OHLCV data
        
    Returns:
        bool: True if data is valid
        
    Raises:
        ValueError: If data is invalid
    """
    required_columns = ['open', 'high', 'low', 'close', 'volume']
    
    # Check required columns
    missing_cols = [col for col in required_columns if col not in data.columns]
    if missing_cols:
        raise ValueError(f"Missing required columns: {missing_cols}")
    
    # Check data types
    for col in required_columns:
        if not pd.api.types.is_numeric_dtype(data[col]):
            raise ValueError(f"Column {col} must be numeric")
    
    # Check for negative values where inappropriate
    if (data['high'] < 0).any() or (data['low'] < 0).any() or (data['close'] < 0).any():
        raise ValueError("Price values cannot be negative")
    
    if (data['volume'] < 0).any():
        raise ValueError("Volume cannot be negative")
    
    # Check price relationships
    invalid_prices = (
        (data['high'] < data['low']) |
        (data['high'] < data['open']) |
        (data['high'] < data['close']) |
        (data['low'] > data['open']) |
        (data['low'] > data['close'])
    )
    
    if invalid_prices.any():
        raise ValueError("Invalid price relationships detected (high < low, etc.)")
    
    # Check for sufficient data
    if len(data) < 50:
        raise ValueError("Insufficient data points. Minimum 50 required.")
    
    return True


def normalize_indicator(
    series: pd.Series, 
    method: str = "zscore", 
    window: int = 252,
    clip_range: Tuple[float, float] = (-100, 100)
) -> pd.Series:
    """
    Normalize indicator values to a standard range.
    
    Args:
        series: Input series to normalize
        method: Normalization method ('zscore', 'minmax', 'robust')
        window: Rolling window for normalization
        clip_range: Range to clip values to
        
    Returns:
        Normalized series
    """
    if method == "zscore":
        # Z-score normalization
        rolling_mean = series.rolling(window).mean()
        rolling_std = series.rolling(window).std()
        normalized = (series - rolling_mean) / rolling_std * 20
        
    elif method == "minmax":
        # Min-max normalization
        rolling_min = series.rolling(window).min()
        rolling_max = series.rolling(window).max()
        range_size = rolling_max - rolling_min
        range_size = range_size.replace(0, 1)  # Avoid division by zero
        normalized = (series - rolling_min) / range_size * 200 - 100
        
    elif method == "robust":
        # Robust normalization using percentiles
        rolling_median = series.rolling(window).median()
        rolling_q75 = series.rolling(window).quantile(0.75)
        rolling_q25 = series.rolling(window).quantile(0.25)
        iqr = rolling_q75 - rolling_q25
        iqr = iqr.replace(0, 1)  # Avoid division by zero
        normalized = (series - rolling_median) / iqr * 30
        
    else:
        raise ValueError(f"Unknown normalization method: {method}")
    
    # Fill NaN values
    normalized = normalized.fillna(0)
    
    # Clip to range
    if clip_range:
        normalized = normalized.clip(clip_range[0], clip_range[1])
    
    return normalized


def generate_signals(
    indicators: Dict[str, pd.Series], 
    threshold: float = 0.5,
    method: str = "weighted_average"
) -> Dict[str, pd.Series]:
    """
    Generate trading signals from multiple indicators.
    
    Args:
        indicators: Dictionary of indicator series
        threshold: Signal threshold for BUY/SELL decisions
        method: Signal generation method
        
    Returns:
        Dictionary with signal, strength, confidence, and consensus series
    """
    # Define signal thresholds for each indicator
    thresholds = {
        "dmfi": {"buy": 30, "sell": -30},
        "aisi": {"buy": 40, "sell": -40},
        "mvci": {"buy": 20, "sell": -20},
        "tafi": {"buy": 25, "sell": -25},
        "fmsi": {"buy": 35, "sell": -35},
    }
    
    # Define weights for each indicator
    weights = {
        "dmfi": 0.25,
        "aisi": 0.25,
        "mvci": 0.20,
        "tafi": 0.15,
        "fmsi": 0.15,
    }
    
    # Ensure all indicators have the same index
    common_index = None
    for name, series in indicators.items():
        if common_index is None:
            common_index = series.index
        else:
            common_index = common_index.intersection(series.index)
    
    if common_index.empty:
        raise ValueError("No common dates found across indicators")
    
    # Generate individual signals
    individual_signals = {}
    for name, series in indicators.items():
        if name in thresholds:
            thresh = thresholds[name]
            signals = pd.Series(index=common_index, dtype=int)
            
            # Align series to common index
            aligned_series = series.reindex(common_index).fillna(0)
            
            signals[aligned_series > thresh["buy"]] = 1
            signals[aligned_series < thresh["sell"]] = -1
            signals[(aligned_series >= thresh["sell"]) & (aligned_series <= thresh["buy"])] = 0
            
            individual_signals[name] = signals
    
    # Calculate composite signal strength
    if method == "weighted_average":
        composite_strength = pd.Series(0.0, index=common_index)
        
        for name, signals in individual_signals.items():
            weight = weights.get(name, 0.2)
            # Normalize individual signals to [-1, 1] range
            normalized_signals = signals / max(abs(signals.min()), abs(signals.max()), 1)
            composite_strength += normalized_signals * weight
    
    elif method == "majority_vote":
        # Simple majority voting
        signal_matrix = pd.DataFrame(individual_signals)
        composite_strength = signal_matrix.mean(axis=1)
    
    else:
        raise ValueError(f"Unknown signal generation method: {method}")
    
    # Generate final signals
    final_signals = pd.Series("HOLD", index=common_index)
    final_signals[composite_strength > threshold] = "BUY"
    final_signals[composite_strength < -threshold] = "SELL"
    
    # Calculate confidence based on indicator agreement
    signal_matrix = pd.DataFrame(individual_signals)
    
    # Count indicators agreeing with the final signal
    consensus_count = pd.Series(0, index=common_index)
    
    for idx in common_index:
        final_signal = final_signals[idx]
        if final_signal == "BUY":
            consensus_count[idx] = (signal_matrix.loc[idx] > 0).sum()
        elif final_signal == "SELL":
            consensus_count[idx] = (signal_matrix.loc[idx] < 0).sum()
        else:  # HOLD
            consensus_count[idx] = (signal_matrix.loc[idx] == 0).sum()
    
    # Calculate confidence as percentage of indicators agreeing
    total_indicators = len(individual_signals)
    confidence = consensus_count / total_indicators
    
    return {
        "signal": final_signals,
        "strength": composite_strength.clip(-1, 1),
        "confidence": confidence,
        "consensus": consensus_count,
        "individual_signals": individual_signals,
    }


def calculate_signal_performance(
    signals: pd.Series,
    prices: pd.Series,
    holding_period: int = 5
) -> Dict[str, float]:
    """
    Calculate performance metrics for trading signals.
    
    Args:
        signals: Series of trading signals (BUY/SELL/HOLD)
        prices: Series of price data
        holding_period: Number of periods to hold position
        
    Returns:
        Dictionary of performance metrics
    """
    # Align signals and prices
    common_index = signals.index.intersection(prices.index)
    signals_aligned = signals.reindex(common_index)
    prices_aligned = prices.reindex(common_index)
    
    returns = []
    trades = 0
    
    i = 0
    while i < len(signals_aligned) - holding_period:
        signal = signals_aligned.iloc[i]
        
        if signal in ["BUY", "SELL"]:
            entry_price = prices_aligned.iloc[i]
            exit_price = prices_aligned.iloc[i + holding_period]
            
            if signal == "BUY":
                trade_return = (exit_price - entry_price) / entry_price
            else:  # SELL (short)
                trade_return = (entry_price - exit_price) / entry_price
            
            returns.append(trade_return)
            trades += 1
            i += holding_period  # Skip holding period
        else:
            i += 1
    
    if not returns:
        return {
            "total_return": 0.0,
            "win_rate": 0.0,
            "avg_return": 0.0,
            "sharpe_ratio": 0.0,
            "max_drawdown": 0.0,
            "total_trades": 0,
        }
    
    returns_series = pd.Series(returns)
    
    # Calculate metrics
    total_return = (1 + returns_series).prod() - 1
    win_rate = (returns_series > 0).mean()
    avg_return = returns_series.mean()
    
    # Sharpe ratio (assuming daily returns)
    sharpe_ratio = avg_return / returns_series.std() * np.sqrt(252) if returns_series.std() > 0 else 0
    
    # Max drawdown
    cumulative_returns = (1 + returns_series).cumprod()
    running_max = cumulative_returns.expanding().max()
    drawdown = (cumulative_returns - running_max) / running_max
    max_drawdown = drawdown.min()
    
    return {
        "total_return": total_return,
        "win_rate": win_rate,
        "avg_return": avg_return,
        "sharpe_ratio": sharpe_ratio,
        "max_drawdown": max_drawdown,
        "total_trades": trades,
    }
