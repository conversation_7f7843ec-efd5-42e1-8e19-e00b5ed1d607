#!/usr/bin/env python3
"""
OpenBB API 客户端
连接到OpenBB API服务器进行金融数据分析
"""

import requests
import json
import pandas as pd
from datetime import datetime, timedelta
import time


class OpenBBClient:
    """OpenBB API 客户端"""
    
    def __init__(self, base_url="http://127.0.0.1:6900"):
        """
        初始化客户端
        
        Args:
            base_url: OpenBB API服务器地址
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        print(f"🔗 连接到OpenBB服务器: {self.base_url}")
        self._check_connection()
    
    def _check_connection(self):
        """检查服务器连接"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                print("✅ 服务器连接成功")
                return True
            else:
                print(f"⚠️ 服务器响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 无法连接到服务器: {e}")
            return False
    
    def _make_request(self, endpoint, params=None):
        """发送API请求"""
        try:
            url = f"{self.base_url}{endpoint}"
            print(f"📡 请求: {endpoint}")
            
            response = self.session.get(url, params=params)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ 请求失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None
    
    def get_equity_historical(self, symbol, start_date=None, end_date=None, provider="yfinance"):
        """
        获取股票历史数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            provider: 数据提供商
        """
        params = {
            "symbol": symbol,
            "provider": provider
        }
        
        if start_date:
            params["start_date"] = start_date
        if end_date:
            params["end_date"] = end_date
        
        data = self._make_request("/api/v1/equity/price/historical", params)
        
        if data and "results" in data:
            df = pd.DataFrame(data["results"])
            if not df.empty and "date" in df.columns:
                df["date"] = pd.to_datetime(df["date"])
                df.set_index("date", inplace=True)
            print(f"✅ 获取到 {len(df)} 条 {symbol} 历史数据")
            return df
        
        return None
    
    def get_equity_quote(self, symbol, provider="yfinance"):
        """
        获取股票实时报价
        
        Args:
            symbol: 股票代码
            provider: 数据提供商
        """
        params = {
            "symbol": symbol,
            "provider": provider
        }
        
        data = self._make_request("/api/v1/equity/price/quote", params)
        
        if data and "results" in data:
            results = data["results"]
            if isinstance(results, list) and results:
                quote_data = results[0]
            else:
                quote_data = results
            
            print(f"✅ 获取到 {symbol} 实时报价")
            return quote_data
        
        return None
    
    def get_economy_gdp(self, country="US", provider="fred"):
        """
        获取GDP数据
        
        Args:
            country: 国家代码
            provider: 数据提供商
        """
        params = {
            "country": country,
            "provider": provider
        }
        
        data = self._make_request("/api/v1/economy/gdp", params)
        
        if data and "results" in data:
            df = pd.DataFrame(data["results"])
            if not df.empty and "date" in df.columns:
                df["date"] = pd.to_datetime(df["date"])
                df.set_index("date", inplace=True)
            print(f"✅ 获取到 {country} GDP数据")
            return df
        
        return None
    
    def get_news(self, query="", limit=10, provider="benzinga"):
        """
        获取新闻数据
        
        Args:
            query: 搜索关键词
            limit: 返回数量
            provider: 数据提供商
        """
        params = {
            "limit": limit,
            "provider": provider
        }
        
        if query:
            params["query"] = query
        
        data = self._make_request("/api/v1/news/company", params)
        
        if data and "results" in data:
            news_list = data["results"]
            print(f"✅ 获取到 {len(news_list)} 条新闻")
            return news_list
        
        return None
    
    def get_crypto_historical(self, symbol, start_date=None, end_date=None, provider="yfinance"):
        """
        获取加密货币历史数据
        
        Args:
            symbol: 加密货币代码 (如 BTC-USD)
            start_date: 开始日期
            end_date: 结束日期
            provider: 数据提供商
        """
        params = {
            "symbol": symbol,
            "provider": provider
        }
        
        if start_date:
            params["start_date"] = start_date
        if end_date:
            params["end_date"] = end_date
        
        data = self._make_request("/api/v1/crypto/price/historical", params)
        
        if data and "results" in data:
            df = pd.DataFrame(data["results"])
            if not df.empty and "date" in df.columns:
                df["date"] = pd.to_datetime(df["date"])
                df.set_index("date", inplace=True)
            print(f"✅ 获取到 {len(df)} 条 {symbol} 加密货币数据")
            return df
        
        return None
    
    def analyze_portfolio(self, symbols, weights=None):
        """
        投资组合分析
        
        Args:
            symbols: 股票代码列表
            weights: 权重列表
        """
        if weights is None:
            weights = [1/len(symbols)] * len(symbols)
        
        print(f"📊 分析投资组合: {symbols}")
        
        portfolio_data = {}
        
        # 获取所有股票数据
        for symbol in symbols:
            data = self.get_equity_historical(symbol, 
                start_date=(datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d"))
            if data is not None:
                portfolio_data[symbol] = data
            time.sleep(1)  # 避免API限制
        
        if not portfolio_data:
            print("❌ 无法获取投资组合数据")
            return None
        
        # 计算收益率
        returns = {}
        for symbol, data in portfolio_data.items():
            if 'close' in data.columns:
                returns[symbol] = data['close'].pct_change().dropna()
        
        if not returns:
            print("❌ 无法计算收益率")
            return None
        
        returns_df = pd.DataFrame(returns)
        portfolio_returns = (returns_df * weights).sum(axis=1)
        
        # 计算统计指标
        stats = {
            'annual_return': portfolio_returns.mean() * 252,
            'annual_volatility': portfolio_returns.std() * (252**0.5),
            'sharpe_ratio': (portfolio_returns.mean() * 252) / (portfolio_returns.std() * (252**0.5)),
            'total_return': (portfolio_returns + 1).prod() - 1
        }
        
        print(f"📈 年化收益率: {stats['annual_return']*100:.2f}%")
        print(f"📊 年化波动率: {stats['annual_volatility']*100:.2f}%")
        print(f"⚡ 夏普比率: {stats['sharpe_ratio']:.2f}")
        print(f"💰 总收益率: {stats['total_return']*100:.2f}%")
        
        return {
            'stats': stats,
            'returns': returns_df,
            'portfolio_returns': portfolio_returns
        }
    
    def get_available_endpoints(self):
        """获取可用的API端点"""
        try:
            response = self.session.get(f"{self.base_url}/docs")
            if response.status_code == 200:
                print("✅ API文档可用，请访问: http://127.0.0.1:6900/docs")
                return True
        except:
            pass
        
        print("📋 常用API端点:")
        endpoints = [
            "/api/v1/equity/price/historical - 股票历史数据",
            "/api/v1/equity/price/quote - 股票实时报价", 
            "/api/v1/economy/gdp - GDP数据",
            "/api/v1/news/company - 公司新闻",
            "/api/v1/crypto/price/historical - 加密货币数据",
            "/health - 服务器健康检查"
        ]
        
        for endpoint in endpoints:
            print(f"  {endpoint}")
        
        return endpoints


def main():
    """客户端演示"""
    print("🚀 OpenBB API 客户端启动")
    print("=" * 50)
    
    # 创建客户端
    client = OpenBBClient()
    
    # 演示1: 获取股票数据
    print("\n📈 演示1: 获取苹果股票数据")
    print("-" * 30)
    aapl_data = client.get_equity_historical("AAPL", 
        start_date=(datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"))
    
    if aapl_data is not None:
        print(f"最新收盘价: ${aapl_data['close'].iloc[-1]:.2f}")
        print(f"30天最高价: ${aapl_data['high'].max():.2f}")
        print(f"30天最低价: ${aapl_data['low'].min():.2f}")
    
    # 演示2: 获取实时报价
    print("\n💰 演示2: 获取实时报价")
    print("-" * 30)
    quote = client.get_equity_quote("MSFT")
    if quote:
        print(f"微软当前价格: ${quote.get('price', 'N/A')}")
    
    # 演示3: 投资组合分析
    print("\n📊 演示3: 投资组合分析")
    print("-" * 30)
    portfolio_result = client.analyze_portfolio(
        symbols=["AAPL", "MSFT"], 
        weights=[0.6, 0.4]
    )
    
    # 演示4: 获取新闻
    print("\n📰 演示4: 获取新闻")
    print("-" * 30)
    news = client.get_news(query="Apple", limit=3)
    if news:
        for i, article in enumerate(news[:3], 1):
            print(f"{i}. {article.get('title', 'N/A')}")
    
    print("\n" + "=" * 50)
    print("✅ 客户端演示完成！")
    print("\n💡 使用提示:")
    print("1. 修改参数进行自定义查询")
    print("2. 访问 http://127.0.0.1:6900/docs 查看完整API文档")
    print("3. 使用 client.get_available_endpoints() 查看可用端点")


if __name__ == "__main__":
    main()
