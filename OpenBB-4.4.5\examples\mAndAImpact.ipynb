{"cells": [{"cell_type": "markdown", "id": "29a22578", "metadata": {}, "source": ["\n", "# M&A Impact Analysis Using OpenBB\n", "\n", "\n", "This notebook demonstrates an analysis of Mergers and Acquisitions (M&A) impact on stock performance using OpenBB's historical data. The analysis includes calculating key performance metrics for the acquirer, pre- and post-announcement, and visualizes the results. It aims to assess how M&A announcements affect stock return, volatility, and beta over time.\n", "\n", "Author:<br>\n", "[<PERSON><PERSON>](https://github.com/Nabid<PERSON>khtar)\n", "\n", "[![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/OpenBB-Finance/OpenBB/blob/develop/examples/M_A_Impact_Analysis_Notebook.ipynb)\n", "\n", "\n", "    "]}, {"cell_type": "markdown", "id": "8f028756", "metadata": {}, "source": ["## Table of Contents\n", "\n", "1. **Imports and Setup**  \n", "    Import necessary libraries and set up functions for data retrieval and analysis.\n", "    \n", "2. **Function Definitions**\n", "    - `get_stock_performance`: Retrieves stock data and calculates key metrics.\n", "    - `analyze_ma_impact`: Analyzes pre- and post-M&A performance.\n", "    - `plot_ma_analysis`: Visualizes cumulative returns and metric comparisons.\n", "    - `generate_ma_report`: Generates a formatted analysis report.\n", "\n", "3. **Running Analysis**  \n", "    Execute the analysis by specifying acquirer and target symbols, announcement date, and other parameters.\n", "\n", "4. **Visualizing and Reporting Results**  \n", "    Display the visual analysis and generate a summary report."]}, {"cell_type": "markdown", "id": "4c7a30fa", "metadata": {}, "source": ["If you are running this notebook in Colab, you can run the following command to install the OpenBB Platform:\n", "\n", "```python\n", "!pip install openbb matplotlib\n", "```\n"]}, {"cell_type": "markdown", "id": "514e94c8", "metadata": {}, "source": ["## 1. Imports and Setup"]}, {"cell_type": "code", "execution_count": 1, "id": "f9e81af5", "metadata": {}, "outputs": [], "source": ["\n", "from openbb import obb\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime, timedelta\n"]}, {"cell_type": "markdown", "id": "b27a22c0", "metadata": {}, "source": ["## 2. Function Definitions"]}, {"cell_type": "markdown", "id": "948985d1", "metadata": {}, "source": ["### Fetch Stock Performance Data"]}, {"cell_type": "code", "execution_count": 2, "id": "664672ab", "metadata": {}, "outputs": [], "source": ["def get_stock_performance(symbol, start_date, end_date):\n", "    \"\"\"\n", "    Fetches stock price data and calculates key performance metrics\n", "    \n", "    Parameters:\n", "    symbol (str): Stock symbol symbol\n", "    start_date (str): Start date in YYYY-MM-DD format\n", "    end_date (str): End date in YYYY-MM-DD format\n", "    \n", "    Returns:\n", "    dict: Performance metrics including returns, volatility, and beta\n", "    \"\"\"\n", "    try:\n", "        # Get historical price data and convert to DataFrame\n", "        stock_data = obb.equity.price.historical(symbol, start_date, end_date).to_df()\n", "        \n", "        if len(stock_data) == 0:\n", "            print(f\"No data available for {symbol}\")\n", "            return None\n", "            \n", "        # Calculate daily returns using 'close' price\n", "        stock_data['returns'] = stock_data['close'].pct_change()\n", "        \n", "        # Get market data (S&P 500)\n", "        spy_data = obb.equity.price.historical('SPY', start_date, end_date).to_df()\n", "        spy_data['returns'] = spy_data['close'].pct_change()\n", "        \n", "        # Calculate metrics\n", "        first_price = stock_data['close'].iloc[0]\n", "        last_price = stock_data['close'].iloc[-1]\n", "        total_return = ((last_price / first_price) - 1) * 100\n", "        volatility = stock_data['returns'].std() * np.sqrt(252) * 100\n", "        \n", "        # Calculate beta using aligned data\n", "        merged_data = pd.DataFrame({\n", "            'stock': stock_data['returns'],\n", "            'market': spy_data['returns']\n", "        }).dropna()\n", "        \n", "        if len(merged_data) > 0:\n", "            beta = np.cov(merged_data['stock'], merged_data['market'])[0][1] / np.var(merged_data['market'])\n", "        else:\n", "            beta = np.nan\n", "        \n", "        return {\n", "            'total_return': total_return,\n", "            'volatility': volatility,\n", "            'beta': beta,\n", "            'daily_returns': stock_data['returns']\n", "        }\n", "    except Exception as e:\n", "        print(f\"Error fetching data for {symbol}: {str(e)}\")\n", "        import traceback\n", "        print(traceback.format_exc())\n", "        return None"]}, {"cell_type": "markdown", "id": "6233a465", "metadata": {}, "source": ["### Analyze M&A Impact"]}, {"cell_type": "code", "execution_count": 3, "id": "50fd5c44", "metadata": {}, "outputs": [], "source": ["def analyze_ma_impact(acquirer_symbol, target_symbol, announcement_date, window_size=180):\n", "    \"\"\"\n", "    Analyzes the impact of M&A announcement on company performance\n", "    \n", "    Parameters:\n", "    acquirer_symbol (str): Acquirer company symbol\n", "    target_symbol (str): Target company symbol\n", "    announcement_date (str): M&A announcement date in YYYY-MM-DD format\n", "    window_size (int): Analysis window in days before and after announcement\n", "    \n", "    Returns:\n", "    dict: Analysis results including pre and post merger performance metrics\n", "    \"\"\"\n", "    try:\n", "        # Parse dates\n", "        announcement_dt = datetime.strptime(announcement_date, '%Y-%m-%d')\n", "        pre_start = (announcement_dt - timedelta(days=window_size)).strftime('%Y-%m-%d')\n", "        pre_end = announcement_date\n", "        post_start = announcement_date\n", "        post_end = (announcement_dt + timedelta(days=window_size)).strftime('%Y-%m-%d')\n", "        \n", "        print(f\"Analyzing pre-merger period: {pre_start} to {pre_end}\")\n", "        pre_merger = get_stock_performance(acquirer_symbol, pre_start, pre_end)\n", "        \n", "        if pre_merger is None:\n", "            print(\"Unable to analyze pre-merger performance\")\n", "            return None\n", "        \n", "        print(f\"Analyzing post-merger period: {post_start} to {post_end}\")\n", "        post_merger = get_stock_performance(acquirer_symbol, post_start, post_end)\n", "        \n", "        if post_merger is None:\n", "            print(\"Unable to analyze post-merger performance\")\n", "            return None\n", "        \n", "        return {\n", "            'pre_merger': pre_merger,\n", "            'post_merger': post_merger,\n", "            'impact': {\n", "                'return_change': post_merger['total_return'] - pre_merger['total_return'],\n", "                'volatility_change': post_merger['volatility'] - pre_merger['volatility'],\n", "                'beta_change': post_merger['beta'] - pre_merger['beta']\n", "            }\n", "        }\n", "    except Exception as e:\n", "        print(f\"Error in analysis: {str(e)}\")\n", "        return None\n"]}, {"cell_type": "markdown", "id": "5d323d0c", "metadata": {}, "source": ["### Plot Analysis Results"]}, {"cell_type": "code", "execution_count": 4, "id": "57fb2037", "metadata": {}, "outputs": [], "source": ["def plot_ma_analysis(analysis_results, acquirer_symbol, announcement_date):\n", "    \"\"\"\n", "    Creates visualizations for M&A impact analysis\n", "    \n", "    Parameters:\n", "    analysis_results (dict): Results from analyze_ma_impact function\n", "    acquirer_symbol (str): Acquirer company symbol\n", "    announcement_date (str): M&A announcement date\n", "    \"\"\"\n", "    if analysis_results is None:\n", "        print(\"No analysis results to plot\")\n", "        return\n", "        \n", "    try:\n", "        plt.figure(figsize=(15, 10))\n", "        \n", "        # Plot 1: Cumulative Returns\n", "        plt.subplot(2, 2, 1)\n", "        pre_cum_returns = (1 + analysis_results['pre_merger']['daily_returns']).cumprod()\n", "        post_cum_returns = (1 + analysis_results['post_merger']['daily_returns']).cumprod()\n", "        \n", "        plt.plot(range(-len(pre_cum_returns), 0), pre_cum_returns, label='Pre-merger')\n", "        plt.plot(range(len(post_cum_returns)), post_cum_returns, label='Post-merger')\n", "        plt.axvline(x=0, color='r', linestyle='--', label='Announcement')\n", "        plt.title(f'Cumulative Returns Around M&A Announcement\\n{acquirer_symbol}')\n", "        plt.xlabel('Days from Announcement')\n", "        plt.ylabel('Cumulative Return')\n", "        plt.legend()\n", "        \n", "        # Plot 2: Key Metrics Comparison\n", "        plt.subplot(2, 2, 2)\n", "        metrics = ['total_return', 'volatility', 'beta']\n", "        pre_values = [analysis_results['pre_merger'][m] for m in metrics]\n", "        post_values = [analysis_results['post_merger'][m] for m in metrics]\n", "        \n", "        x = np.arange(len(metrics))\n", "        width = 0.35\n", "        \n", "        plt.bar(x - width/2, pre_values, width, label='Pre-merger')\n", "        plt.bar(x + width/2, post_values, width, label='Post-merger')\n", "        plt.xticks(x, metrics)\n", "        plt.title('Key Metrics Comparison')\n", "        plt.legend()\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "    except Exception as e:\n", "        print(f\"Error in plotting: {str(e)}\")\n"]}, {"cell_type": "markdown", "id": "a3c8043e", "metadata": {}, "source": ["### Generate Summary Report"]}, {"cell_type": "code", "execution_count": 5, "id": "7959e982", "metadata": {}, "outputs": [], "source": ["def generate_ma_report(analysis_results, acquirer_symbol, target_symbol, announcement_date):\n", "    \"\"\"\n", "    Generates a summary report of the M&A impact analysis\n", "    \n", "    Parameters:\n", "    analysis_results (dict): Results from analyze_ma_impact function\n", "    acquirer_symbol (str): Acquirer company symbol\n", "    target_symbol (str): Target company symbol\n", "    announcement_date (str): M&A announcement date\n", "    \n", "    Returns:\n", "    str: Formatted report text\n", "    \"\"\"\n", "    if analysis_results is None:\n", "        return \"Unable to generate report due to missing analysis results\"\n", "        \n", "    try:\n", "        report = f\"\"\"\n", "M&A Impact Analysis Report\n", "=========================\n", "Acquirer: {acquirer_symbol}\n", "Target: {target_symbol}\n", "Announcement Date: {announcement_date}\n", "\n", "Performance Metrics\n", "-----------------\n", "Pre-Merger Period:\n", "- Total Return: {analysis_results['pre_merger']['total_return']:.2f}%\n", "- Volatility: {analysis_results['pre_merger']['volatility']:.2f}%\n", "- Beta: {analysis_results['pre_merger']['beta']:.2f}\n", "\n", "Post-Merger Period:\n", "- Total Return: {analysis_results['post_merger']['total_return']:.2f}%\n", "- Volatility: {analysis_results['post_merger']['volatility']:.2f}%\n", "- Beta: {analysis_results['post_merger']['beta']:.2f}\n", "\n", "Impact Analysis\n", "--------------\n", "- Return Impact: {analysis_results['impact']['return_change']:.2f}%\n", "- Volatility Impact: {analysis_results['impact']['volatility_change']:.2f}%\n", "- Beta Impact: {analysis_results['impact']['beta_change']:.2f}\n", "\n", "Summary\n", "-------\n", "The merger announcement appears to have {\n", "    'positively' if analysis_results['impact']['return_change'] > 0 else 'negatively'\n", "} impacted the acquirer's stock performance, with a {\n", "    abs(analysis_results['impact']['return_change']):.2f}% change in returns.\n", "Risk metrics show that the company's volatility has {\n", "    'increased' if analysis_results['impact']['volatility_change'] > 0 else 'decreased'\n", "} by {abs(analysis_results['impact']['volatility_change']):.2f}% and beta has {\n", "    'increased' if analysis_results['impact']['beta_change'] > 0 else 'decreased'\n", "} by {abs(analysis_results['impact']['beta_change']):.2f}.\n", "\"\"\"\n", "        return report\n", "    except Exception as e:\n", "        return f\"Error generating report: {str(e)}\"\n"]}, {"cell_type": "markdown", "id": "2ee3ee00", "metadata": {}, "source": ["## 3. Running Analysis"]}, {"cell_type": "code", "execution_count": 6, "id": "bda41487", "metadata": {}, "outputs": [], "source": ["acquirer_symbol = \"MSFT\"\n", "target_symbol = \"LNKD\"\n", "announcement_date = \"2016-06-13\""]}, {"cell_type": "code", "execution_count": 7, "id": "3e4ae811", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Analyzing pre-merger period: 2015-12-16 to 2016-06-13\n", "Analyzing post-merger period: 2016-06-13 to 2016-12-10\n"]}], "source": ["\n", "# Example usage\n", "analysis_results = analyze_ma_impact(acquirer_symbol, target_symbol, announcement_date)\n"]}, {"cell_type": "markdown", "id": "9e62cab9", "metadata": {}, "source": ["## 4. Visualizing and Reporting Results"]}, {"cell_type": "markdown", "id": "c5d445e7", "metadata": {}, "source": ["### Plot Analysis Results"]}, {"cell_type": "code", "execution_count": 8, "id": "c0ee3408", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# Plot the analysis results\n", "plot_ma_analysis(analysis_results, acquirer_symbol, announcement_date)\n"]}, {"cell_type": "markdown", "id": "7fa552be", "metadata": {}, "source": ["### Generate Summary Report"]}, {"cell_type": "code", "execution_count": 9, "id": "87d9358d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "M&A Impact Analysis Report\n", "=========================\n", "Acquirer: MSFT\n", "Target: LNKD\n", "Announcement Date: 2016-06-13\n", "\n", "Performance Metrics\n", "-----------------\n", "Pre-Merger Period:\n", "- Total Return: -10.67%\n", "- Volatility: 26.41%\n", "- Beta: 1.25\n", "\n", "Post-Merger Period:\n", "- Total Return: 23.59%\n", "- Volatility: 19.10%\n", "- Beta: 1.15\n", "\n", "Impact Analysis\n", "--------------\n", "- Return Impact: 34.27%\n", "- Volatility Impact: -7.30%\n", "- Beta Impact: -0.10\n", "\n", "Summary\n", "-------\n", "The merger announcement appears to have positively impacted the acquirer's stock performance, with a 34.27% change in returns.\n", "Risk metrics show that the company's volatility has decreased by 7.30% and beta has decreased by 0.10.\n", "\n"]}], "source": ["\n", "# Generate and print the summary report\n", "print(generate_ma_report(analysis_results, acquirer_symbol, target_symbol, announcement_date))\n"]}, {"cell_type": "markdown", "id": "ff3278e6", "metadata": {}, "source": ["\n", "---\n", "\n", "### Conclusion\n", "\n", "The notebook provides a streamlined approach to assessing M&A impact, leveraging OpenBB's data retrieval capabilities to analyze stock performance pre- and post-announcement.\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}