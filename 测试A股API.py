#!/usr/bin/env python3
"""
测试A股API数据正确性
"""

import requests

def test_astock_quotes():
    """测试A股报价"""
    print("🧪 测试A股API报价数据")
    print("=" * 50)
    
    # 测试多只股票
    symbols = "000001,600519,000858,600036,002415"
    
    try:
        response = requests.get(f"http://127.0.0.1:6901/api/v1/astock/equity/price/quote?symbol={symbols}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ 成功获取 {data['count']} 只股票数据")
            print(f"📊 数据提供商: {data['provider_used']}")
            print()
            print("📈 A股实时报价:")
            print("-" * 80)
            print(f"{'股票代码':<8} {'股票名称':<12} {'当前价格':<12} {'涨跌额':<10} {'涨跌幅':<10} {'成交量':<15}")
            print("-" * 80)
            
            for stock in data['results']:
                symbol = stock['symbol']
                name = stock['name']
                price = stock['price']
                change = stock['change']
                change_pct = stock['change_percent']
                volume = stock['volume']
                
                # 格式化成交量
                if volume > 100000000:
                    volume_str = f"{volume/100000000:.1f}亿"
                elif volume > 10000:
                    volume_str = f"{volume/10000:.1f}万"
                else:
                    volume_str = f"{volume:,}"
                
                print(f"{symbol:<8} {name:<12} ¥{price:<11.2f} {change:+.2f}     {change_pct:+.2f}%     {volume_str:<15}")
            
            print("-" * 80)
            print()
            
            # 验证数据合理性
            print("🔍 数据验证:")
            for stock in data['results']:
                name = stock['name']
                price = stock['price']
                
                # 检查价格合理性
                if price > 0:
                    print(f"✅ {name}: 价格 ¥{price:.2f} (合理)")
                else:
                    print(f"❌ {name}: 价格 ¥{price:.2f} (异常)")
                
                # 检查货币符号
                print(f"💰 货币符号: ¥ (人民币) ✅")
                
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_astock_historical():
    """测试A股历史数据"""
    print("\n🧪 测试A股历史数据")
    print("=" * 50)
    
    try:
        response = requests.get("http://127.0.0.1:6901/api/v1/astock/equity/price/historical?symbol=000001&start_date=2024-01-01&end_date=2024-01-31")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ 成功获取历史数据: {data['count']} 条")
            print(f"📊 股票代码: {data['symbol']}")
            print(f"📊 数据提供商: {data['provider_used']}")
            
            if data['results']:
                print("\n📈 最近5天数据:")
                print("-" * 70)
                print(f"{'日期':<12} {'开盘':<8} {'最高':<8} {'最低':<8} {'收盘':<8} {'成交量':<12}")
                print("-" * 70)
                
                for record in data['results'][-5:]:  # 显示最后5条
                    date = record['date']
                    open_price = record['open']
                    high = record['high']
                    low = record['low']
                    close = record['close']
                    volume = record['volume']
                    
                    volume_str = f"{volume/10000:.0f}万" if volume > 10000 else f"{volume:,}"
                    
                    print(f"{date:<12} ¥{open_price:<7.2f} ¥{high:<7.2f} ¥{low:<7.2f} ¥{close:<7.2f} {volume_str:<12}")
                
                print("-" * 70)
        else:
            print(f"❌ API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🇨🇳 A股API数据测试")
    print("=" * 60)
    
    # 测试报价数据
    test_astock_quotes()
    
    # 测试历史数据
    test_astock_historical()
    
    print("\n🎉 测试完成!")
    print("\n💡 现在您可以在Streamlit客户端中:")
    print("1. 选择 '🇨🇳 A股' 市场")
    print("2. 输入股票代码如: 000001, 600519, 000858")
    print("3. 查看正确的人民币价格和数据")

if __name__ == "__main__":
    main()
