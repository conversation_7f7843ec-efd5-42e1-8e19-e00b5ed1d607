{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Financial Statements in the OpenBB Platform\n", "\n", "OpenBB Platform data extensions provide access to financial statements as quarterly or annual.  There are also endpoints for ratios and other common non-GAAP metrics.  Most data providers require a subscription to access all data. Refer to the website of a specific provider for details on entitlements and coverage.\n", "\n", "Financial statement functions are grouped under the `obb.equity.fundamental` module.\n", "\n", "## Endpoints\n", "\n", "The typical financial statements consist of three endpoints:\n", "\n", "- Balance Sheet: `obb.equity.fundamental.balance()`\n", "- Income Statement: `obb.equity.fundamental.income()`\n", "- Cash Flow Statement: `obb.equity.fundamental.cash()`\n", "\n", "The main parameters are:\n", "\n", "- `symbol`: The company's symbol.\n", "- `period`: 'annual' or 'quarter'. Default is 'annual'.\n", "- `limit`: Limit the number of results returned, from the latest. Default is 5. For perspective, 150 will go back to 1985. The amount of historical records varies by provider.\n", "\n", "### Field Names\n", "\n", "Some considerations to keep in mind when working with financial statements data are:\n", "\n", "- Every data provider has their own way of parsing and organizing the three financial statements.\n", "- Items within each statement will vary by source and by the type of company reporting.\n", "- Names of line items will vary by source.\n", "- \"Date\" values may differ because they are from the period starting/ending or date of reporting.\n", "\n", "This example highlights how different providers will have different labels for compnay facts.\n", "\n", "\n", "**Note**: API Keys are required for FMP, Intrinio, and Polygon."]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from openbb import obb"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>yfinance</th>\n", "      <th>fmp</th>\n", "      <th>intrinio</th>\n", "      <th>polygon</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5.535600e+10</td>\n", "      <td>5.535600e+10</td>\n", "      <td>5.535600e+10</td>\n", "      <td>5.535600e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5.333500e+10</td>\n", "      <td>5.333500e+10</td>\n", "      <td>5.333500e+10</td>\n", "      <td>5.333500e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5.381100e+10</td>\n", "      <td>5.381100e+10</td>\n", "      <td>5.381100e+10</td>\n", "      <td>5.381100e+10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       yfinance           fmp      intrinio       polygon\n", "0  5.535600e+10  5.535600e+10  5.535600e+10  5.535600e+10\n", "1  5.333500e+10  5.333500e+10  5.333500e+10  5.333500e+10\n", "2  5.381100e+10  5.381100e+10  5.381100e+10  5.381100e+10"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.DataFrame()\n", "\n", "df[\"yfinance\"] = (\n", "    obb.equity.fundamental.balance(\n", "        \"TGT\", provider=\"yfinance\"\n", "    )  # There is no limit for yFinance, historical data is limited.\n", "    .to_df()\n", "    .get(\"total_assets\")\n", "    .head(3)\n", ")\n", "\n", "df[\"fmp\"] = (\n", "    obb.equity.fundamental.balance(\"TGT\", provider=\"fmp\", limit=3)\n", "    .to_df()\n", "    .get(\"total_assets\")\n", ")\n", "\n", "df[\"intrinio\"] = (\n", "    obb.equity.fundamental.balance(\"TGT\", provider=\"intrinio\", limit=3)\n", "    .to_df()\n", "    .get(\"total_assets\")\n", ")\n", "\n", "df[\"polygon\"] = (\n", "    obb.equity.fundamental.balance(\"TGT\", provider=\"polygon\", limit=3)\n", "    .to_df()\n", "    .get(\"total_assets\")\n", ")\n", "\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Weighted Average Shares Outstanding\n", "\n", "This key metric will be found under the income statement.  It might also be called, 'basic', and the numbers do not include authorized but unissued shares.  A declining count over time is a sign that the company is returning capital to shareholders in the form of buy backs.  Under ideal circumstances, it is more capital-efficient, for both company and shareholders, because distributions are double-taxed.  The company pays income tax on paid dividends, and the beneficiary pays income tax again on receipt.\n", "\n", "A company will disclose how many shares are outstanding at the end of the period  as a weighted average over the reporting period - three months.\n", "\n", "Let's take a look at Target.  To make the numbers easier to read, we'll divide the entire column by one million."]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    462.5\n", "Name: weighted_average_basic_shares_outstanding, dtype: float64"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["149    1169.248\n", "Name: weighted_average_basic_shares_outstanding, dtype: float64"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data = obb.equity.fundamental.income(\n", "    \"TGT\", provider=\"fmp\", limit=150, period=\"quarter\"\n", ").to_df()\n", "\n", "shares = data[\"weighted_average_basic_shares_outstanding\"] / 1000000\n", "\n", "display(shares.head(1))\n", "\n", "display(shares.tail(1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Thirty-seven years later, the share count is approaching a two-thirds reduction.  12.2% over the past five years.  In four reporting periods, 1.3 million shares have been taken out of the float."]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.3362834285714287"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["-65.75199999999995"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(shares.pct_change(20).iloc[-1])\n", "\n", "display(shares.iloc[-4] - shares.iloc[-1])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["With an average closing price of $143.37, that represents approximately $190M in buy backs."]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"text/plain": ["190.75"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["price = obb.equity.price.historical(\n", "    \"TGT\", start_date=\"2022-10-01\", provider=\"fmp\"\n", ").to_df()\n", "\n", "round((price[\"close\"].mean() * 1300000) / 1000000, 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Dividends Paid\n", "\n", "Dividends paid is in the cash flow statement.  We can calculate the amount-per-share with the reported data."]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/plain": ["136    0.040339\n", "137    0.023793\n", "138    0.020690\n", "139    0.022969\n", "Name: div_per_share, dtype: float64"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["dividends = obb.equity.fundamental.cash(\n", "    \"TGT\", provider=\"fmp\", limit=150, period=\"quarter\"\n", ").to_df()[[\"payment_of_dividends\"]]\n", "\n", "dividends[\"shares\"] = data[[\"weighted_average_basic_shares_outstanding\"]]\n", "dividends[\"div_per_share\"] = abs(\n", "    dividends[\"payment_of_dividends\"] / dividends[\"shares\"]\n", ")\n", "\n", "dividends[\"div_per_share\"].tail(4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This can be compared against the real amounts paid to common share holders, as announced.  Note that the dates above represent the report date, and that dividends paid are attributed to the quarter they were paid in.  The value from \"2023-01-28\" equates to the fourth quarter of 2022."]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>amount</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ex_dividend_date</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-08-15</th>\n", "      <td>1.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-16</th>\n", "      <td>1.08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-02-14</th>\n", "      <td>1.08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-11-15</th>\n", "      <td>1.08</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  amount\n", "ex_dividend_date        \n", "2023-08-15          1.10\n", "2023-05-16          1.08\n", "2023-02-14          1.08\n", "2022-11-15          1.08"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["data = obb.equity.fundamental.dividends(\"TGT\", provider=\"fmp\").to_df()[\n", "    [\"ex_dividend_date\", \"amount\"]\n", "]\n", "data.ex_dividend_date = data.ex_dividend_date.astype(str)\n", "data.set_index(\"ex_dividend_date\").loc[\"2023-08-15\":\"2022-11-15\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The numbers check out, and the $2B paid to investors over four quarters is more than ten times the $190M returned through share buy backs.\n", "\n", "### Financial Attributes\n", "\n", "The `openbb-intrinio` data extension has an endpoint for extracting a single fact from financial statements.  There is a helper function for looking up the correct `tag`.\n", "\n", "**Note:** Intrinio does not offer a free API level with access to data.\n", "\n", "#### Search Financial Attributes\n", "\n", "Search attributes by keyword."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>tag</th>\n", "      <th>statement_code</th>\n", "      <th>statement_type</th>\n", "      <th>type</th>\n", "      <th>unit</th>\n", "      <th>parent_name</th>\n", "      <th>sequence</th>\n", "      <th>factor</th>\n", "      <th>transaction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>tag_BgkbWy</td>\n", "      <td>Market Capitalization</td>\n", "      <td>marketcap</td>\n", "      <td>calculations</td>\n", "      <td>industrial</td>\n", "      <td>valuation</td>\n", "      <td>usd</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>tag_kylOqz</td>\n", "      <td>Market Capitalization</td>\n", "      <td>marketcap</td>\n", "      <td>calculations</td>\n", "      <td>financial</td>\n", "      <td>valuation</td>\n", "      <td>usd</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>tag_XLRlqy</td>\n", "      <td>Market Sector</td>\n", "      <td>market_sector</td>\n", "      <td>current</td>\n", "      <td>NaN</td>\n", "      <td>security</td>\n", "      <td>string</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>tag_2gBA8y</td>\n", "      <td>Market Category</td>\n", "      <td>market_category</td>\n", "      <td>current</td>\n", "      <td>NaN</td>\n", "      <td>security</td>\n", "      <td>string</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>tag_DzonXe</td>\n", "      <td>Marketing Expense</td>\n", "      <td>marketingexpense</td>\n", "      <td>income_statement</td>\n", "      <td>industrial</td>\n", "      <td>income_statement_metric</td>\n", "      <td>usd</td>\n", "      <td>totaloperatingexpenses</td>\n", "      <td>9.0</td>\n", "      <td>+</td>\n", "      <td>debit</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>tag_nzJAmX</td>\n", "      <td>Total Long-Term Debt</td>\n", "      <td>ltdebtandcapleases</td>\n", "      <td>calculations</td>\n", "      <td>financial</td>\n", "      <td>metric</td>\n", "      <td>usd</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>tag_9XaL5g</td>\n", "      <td>Other Net Changes in Cash</td>\n", "      <td>othernetchangesincash</td>\n", "      <td>cash_flow_statement</td>\n", "      <td>industrial</td>\n", "      <td>cash_flow_statement_metric</td>\n", "      <td>usd</td>\n", "      <td>netchangeincash</td>\n", "      <td>33.0</td>\n", "      <td>+</td>\n", "      <td>debit</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>tag_5X7p6z</td>\n", "      <td>Other Net Changes in Cash</td>\n", "      <td>othernetchangesincash</td>\n", "      <td>cash_flow_statement</td>\n", "      <td>financial</td>\n", "      <td>cash_flow_statement_metric</td>\n", "      <td>usd</td>\n", "      <td>netchangeincash</td>\n", "      <td>37.0</td>\n", "      <td>+</td>\n", "      <td>debit</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>tag_qzEwng</td>\n", "      <td>Changes in Operating Assets and Liabilities, net</td>\n", "      <td>increasedecreaseinoperatingcapital</td>\n", "      <td>cash_flow_statement</td>\n", "      <td>financial</td>\n", "      <td>cash_flow_statement_metric</td>\n", "      <td>usd</td>\n", "      <td>netcashfromcontinuingoperatingactivities</td>\n", "      <td>8.0</td>\n", "      <td>+</td>\n", "      <td>debit</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>tag_pgVB2g</td>\n", "      <td>Changes in Operating Assets and Liabilities, net</td>\n", "      <td>increasedecreaseinoperatingcapital</td>\n", "      <td>cash_flow_statement</td>\n", "      <td>industrial</td>\n", "      <td>cash_flow_statement_metric</td>\n", "      <td>usd</td>\n", "      <td>netcashfromcontinuingoperatingactivities</td>\n", "      <td>7.0</td>\n", "      <td>+</td>\n", "      <td>debit</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>100 rows × 11 columns</p>\n", "</div>"], "text/plain": ["            id                                              name  \\\n", "0   tag_BgkbWy                             Market Capitalization   \n", "1   tag_kylOqz                             Market Capitalization   \n", "2   tag_XLRlqy                                     Market Sector   \n", "3   tag_2gBA8y                                   Market Category   \n", "4   tag_DzonXe                                 Marketing Expense   \n", "..         ...                                               ...   \n", "95  tag_nzJAmX                              Total Long-Term Debt   \n", "96  tag_9XaL5g                         Other Net Changes in Cash   \n", "97  tag_5X7p6z                         Other Net Changes in Cash   \n", "98  tag_qzEwng  Changes in Operating Assets and Liabilities, net   \n", "99  tag_pgVB2g  Changes in Operating Assets and Liabilities, net   \n", "\n", "                                   tag       statement_code statement_type  \\\n", "0                            marketcap         calculations     industrial   \n", "1                            marketcap         calculations      financial   \n", "2                        market_sector              current            NaN   \n", "3                      market_category              current            NaN   \n", "4                     marketingexpense     income_statement     industrial   \n", "..                                 ...                  ...            ...   \n", "95                  ltdebtandcapleases         calculations      financial   \n", "96               othernetchangesincash  cash_flow_statement     industrial   \n", "97               othernetchangesincash  cash_flow_statement      financial   \n", "98  increasedecreaseinoperatingcapital  cash_flow_statement      financial   \n", "99  increasedecreaseinoperatingcapital  cash_flow_statement     industrial   \n", "\n", "                          type    unit  \\\n", "0                    valuation     usd   \n", "1                    valuation     usd   \n", "2                     security  string   \n", "3                     security  string   \n", "4      income_statement_metric     usd   \n", "..                         ...     ...   \n", "95                      metric     usd   \n", "96  cash_flow_statement_metric     usd   \n", "97  cash_flow_statement_metric     usd   \n", "98  cash_flow_statement_metric     usd   \n", "99  cash_flow_statement_metric     usd   \n", "\n", "                                 parent_name  sequence factor transaction  \n", "0                                        NaN       NaN    NaN         NaN  \n", "1                                        NaN       NaN    NaN         NaN  \n", "2                                        NaN       NaN    NaN         NaN  \n", "3                                        NaN       NaN    NaN         NaN  \n", "4                     totaloperatingexpenses       9.0      +       debit  \n", "..                                       ...       ...    ...         ...  \n", "95                                       NaN       NaN    NaN         NaN  \n", "96                           netchangeincash      33.0      +       debit  \n", "97                           netchangeincash      37.0      +       debit  \n", "98  netcashfromcontinuingoperatingactivities       8.0      +       debit  \n", "99  netcashfromcontinuingoperatingactivities       7.0      +       debit  \n", "\n", "[100 rows x 11 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["(obb.equity.fundamental.search_attributes(\"marketcap\", provider=\"intrinio\").to_df())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `tag` is what we need, in this case it is what we searched for."]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>tag</th>\n", "      <th>value</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-09-30</th>\n", "      <td>TGT</td>\n", "      <td>marketcap</td>\n", "      <td>4.951153e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-31</th>\n", "      <td>TGT</td>\n", "      <td>marketcap</td>\n", "      <td>6.443403e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-03-31</th>\n", "      <td>TGT</td>\n", "      <td>marketcap</td>\n", "      <td>8.082004e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-06-30</th>\n", "      <td>TGT</td>\n", "      <td>marketcap</td>\n", "      <td>6.814283e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-22</th>\n", "      <td>TGT</td>\n", "      <td>marketcap</td>\n", "      <td>7.387608e+10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           symbol        tag         value\n", "date                                      \n", "2023-09-30    TGT  marketcap  4.951153e+10\n", "2023-12-31    TGT  marketcap  6.443403e+10\n", "2024-03-31    TGT  marketcap  8.082004e+10\n", "2024-06-30    TGT  marketcap  6.814283e+10\n", "2024-08-22    TGT  marketcap  7.387608e+10"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["marketcap = obb.equity.fundamental.historical_attributes(\n", "    symbol=\"TGT\", tag=\"marketcap\", frequency=\"quarterly\", provider=\"intrinio\"\n", ").to_df()\n", "\n", "marketcap.tail(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Doing some quick math, and ignoring the most recent value, we can see that the market cap of Target was down nearly a quarter over the last four reporting periods."]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["-0.243767327909974"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["marketcap.index = marketcap.index.astype(str)\n", "(\n", "    (marketcap.loc[\"2023-09-30\"].value - marketcap.loc[\"2022-12-31\"].value)\n", "    / marketcap.loc[\"2022-12-31\"].value\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Historial market cap is also available as a daily metric from FMP. We can resample it as quarterly to approximate the same results."]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>market_cap</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022-03-31</th>\n", "      <td>98470080000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-06-30</th>\n", "      <td>65177644999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-30</th>\n", "      <td>68303916999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-12-31</th>\n", "      <td>68603112000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-03-31</th>\n", "      <td>76338867000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-30</th>\n", "      <td>60885040000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-30</th>\n", "      <td>51039112000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-31</th>\n", "      <td>65755313999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-03-31</th>\n", "      <td>81906462000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-06-30</th>\n", "      <td>68424088000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-30</th>\n", "      <td>73653125000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             market_cap\n", "date                   \n", "2022-03-31  98470080000\n", "2022-06-30  65177644999\n", "2022-09-30  68303916999\n", "2022-12-31  68603112000\n", "2023-03-31  76338867000\n", "2023-06-30  60885040000\n", "2023-09-30  51039112000\n", "2023-12-31  65755313999\n", "2024-03-31  81906462000\n", "2024-06-30  68424088000\n", "2024-09-30  73653125000"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["market_cap   -0.256023\n", "dtype: float64"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["df = obb.equity.historical_market_cap(\n", "    \"TGT\", start_date=\"2022-01-01\", provider=\"fmp\"\n", ").to_df()\n", "\n", "resampled = df.copy()\n", "resampled.index = pd.to_datetime(resampled.index)\n", "resampled = resampled[[\"market_cap\"]]\n", "resampled = resampled.resample(\"QE\").last()\n", "resampled.index = resampled.index.astype(str)\n", "display(resampled)\n", "(\n", "    (resampled.loc[\"2023-09-30\"] - resampled.loc[\"2022-12-31\"])\n", "    / resampled.loc[\"2022-12-31\"]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Ratios and Other Metrics\n", "\n", "Other valuation functions are derivatives of the financial statements, but the data provider does the math.  Values are typically ratios between line items, on a per-share basis, or as a percent growth.\n", "\n", "This data set is where you can find EPS, FCF, P/B, EBIT, quick ratio, etc.\n", "\n", "### Quick Ratio\n", "\n", "Target's quick ratio could be one reason why its share price is losing traction against the market.  Its ability to pay current obligations is not optimistically reflected in a 0.27 score, approximately 50% below the historical median."]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Current Quick Ratio: 0.8998'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'Median Quick Ratio: 0.6047'"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ratios = obb.equity.fundamental.ratios(\"TGT\", limit=50, provider=\"fmp\").to_df()\n", "\n", "display(f\"Current Quick Ratio: {round(ratios['quick_ratio'].iloc[-1], 4)}\")\n", "display(f\"Median Quick Ratio: {round(ratios['quick_ratio'].median(), 4)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Free Cash Flow Yield\n", "\n", "The `metrics` endpoint, with the `openbb-fmp` data extension, has a field for free cash flow yield.  It is calculated by taking the free cash flow per share divided by the current share price.  We could arrive at this answer by writing some code, but these types of endpoints do the work so we don't have to.  This is part of the value-add that API data distributors provide, they allow you to get straight to work with data.\n", "\n", "We'll use this endpoint to extract the data, and compare with some of Target's competition over the last ten years."]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>calendar_year</th>\n", "      <th>2023</th>\n", "      <th>2022</th>\n", "      <th>2021</th>\n", "      <th>2020</th>\n", "      <th>2019</th>\n", "      <th>2018</th>\n", "      <th>2017</th>\n", "      <th>2016</th>\n", "      <th>2015</th>\n", "      <th>2014</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>COST</th>\n", "      <td>0.027922</td>\n", "      <td>0.014860</td>\n", "      <td>0.026582</td>\n", "      <td>0.039351</td>\n", "      <td>0.025906</td>\n", "      <td>0.027438</td>\n", "      <td>0.060884</td>\n", "      <td>0.008941</td>\n", "      <td>0.030741</td>\n", "      <td>0.037483</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BJ</th>\n", "      <td>0.029338</td>\n", "      <td>0.044709</td>\n", "      <td>0.067213</td>\n", "      <td>0.113551</td>\n", "      <td>0.056631</td>\n", "      <td>0.091107</td>\n", "      <td>0.026186</td>\n", "      <td>0.065871</td>\n", "      <td>0.016947</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DLTR</th>\n", "      <td>0.018948</td>\n", "      <td>0.010756</td>\n", "      <td>0.013957</td>\n", "      <td>0.075627</td>\n", "      <td>0.040338</td>\n", "      <td>0.041252</td>\n", "      <td>0.034069</td>\n", "      <td>0.063465</td>\n", "      <td>0.016602</td>\n", "      <td>0.041047</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DG</th>\n", "      <td>0.023149</td>\n", "      <td>0.008256</td>\n", "      <td>0.037507</td>\n", "      <td>0.058973</td>\n", "      <td>0.036922</td>\n", "      <td>0.046197</td>\n", "      <td>0.042609</td>\n", "      <td>0.050776</td>\n", "      <td>0.039524</td>\n", "      <td>0.046052</td>\n", "    </tr>\n", "    <tr>\n", "      <th>WMT</th>\n", "      <td>0.030577</td>\n", "      <td>0.028374</td>\n", "      <td>0.065467</td>\n", "      <td>0.044595</td>\n", "      <td>0.062030</td>\n", "      <td>0.057280</td>\n", "      <td>0.101023</td>\n", "      <td>0.073506</td>\n", "      <td>0.059705</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BIG</th>\n", "      <td>-1.856996</td>\n", "      <td>-0.624151</td>\n", "      <td>0.025262</td>\n", "      <td>0.115757</td>\n", "      <td>0.069464</td>\n", "      <td>-0.111853</td>\n", "      <td>0.037219</td>\n", "      <td>0.100721</td>\n", "      <td>0.110443</td>\n", "      <td>0.089253</td>\n", "    </tr>\n", "    <tr>\n", "      <th>M</th>\n", "      <td>0.061077</td>\n", "      <td>0.050473</td>\n", "      <td>0.270980</td>\n", "      <td>0.039111</td>\n", "      <td>0.091301</td>\n", "      <td>0.101426</td>\n", "      <td>0.155761</td>\n", "      <td>0.098993</td>\n", "      <td>0.065634</td>\n", "      <td>0.072322</td>\n", "    </tr>\n", "    <tr>\n", "      <th>KSS</th>\n", "      <td>0.203512</td>\n", "      <td>-0.143961</td>\n", "      <td>0.189677</td>\n", "      <td>0.147968</td>\n", "      <td>0.119492</td>\n", "      <td>0.139799</td>\n", "      <td>0.096137</td>\n", "      <td>0.198790</td>\n", "      <td>0.081652</td>\n", "      <td>0.110697</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TJX</th>\n", "      <td>0.027513</td>\n", "      <td>0.023498</td>\n", "      <td>0.051975</td>\n", "      <td>0.039865</td>\n", "      <td>0.049788</td>\n", "      <td>0.039930</td>\n", "      <td>0.053697</td>\n", "      <td>0.043328</td>\n", "      <td>0.046442</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["calendar_year      2023      2022      2021      2020      2019      2018  \\\n", "COST           0.027922  0.014860  0.026582  0.039351  0.025906  0.027438   \n", "BJ             0.029338  0.044709  0.067213  0.113551  0.056631  0.091107   \n", "DLTR           0.018948  0.010756  0.013957  0.075627  0.040338  0.041252   \n", "DG             0.023149  0.008256  0.037507  0.058973  0.036922  0.046197   \n", "WMT            0.030577  0.028374  0.065467  0.044595  0.062030  0.057280   \n", "BIG           -1.856996 -0.624151  0.025262  0.115757  0.069464 -0.111853   \n", "M              0.061077  0.050473  0.270980  0.039111  0.091301  0.101426   \n", "KSS            0.203512 -0.143961  0.189677  0.147968  0.119492  0.139799   \n", "TJX            0.027513  0.023498  0.051975  0.039865  0.049788  0.039930   \n", "\n", "calendar_year      2017      2016      2015      2014  \n", "COST           0.060884  0.008941  0.030741  0.037483  \n", "BJ             0.026186  0.065871  0.016947       NaN  \n", "DLTR           0.034069  0.063465  0.016602  0.041047  \n", "DG             0.042609  0.050776  0.039524  0.046052  \n", "WMT            0.101023  0.073506  0.059705       NaN  \n", "BIG            0.037219  0.100721  0.110443  0.089253  \n", "M              0.155761  0.098993  0.065634  0.072322  \n", "KSS            0.096137  0.198790  0.081652  0.110697  \n", "TJX            0.053697  0.043328  0.046442       NaN  "]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["# List of other retail chains\n", "tickers = [\"COST\", \"BJ\", \"DLTR\", \"DG\", \"WMT\", \"BIG\", \"M\", \"KSS\", \"TJX\"]\n", "\n", "# Create a column for each.\n", "fcf_yield = pd.DataFrame()\n", "for ticker in tickers:\n", "    fcf_yield[ticker] = (\n", "        obb.equity.fundamental.metrics(\n", "            ticker, provider=\"fmp\", period=\"annual\", limit=10\n", "        )\n", "        .to_df()\n", "        .reset_index()\n", "        .set_index(\"calendar_year\")\n", "        .sort_index(ascending=False)[\"free_cash_flow_yield\"]\n", "    )\n", "fcf_yield.transpose()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are more usage examples on our [website](https://docs.openbb.co/platform/user_guides)"]}], "metadata": {"kernelspec": {"display_name": "obb-sdk4", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}