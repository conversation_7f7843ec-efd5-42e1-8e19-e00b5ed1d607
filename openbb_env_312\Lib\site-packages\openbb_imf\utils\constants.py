"""IMF Constants."""

from typing import Literal

# pylint: disable=line-too-long
# flake8: noqa: E501

DAILY_TRADE_BASE_URL = "https://services9.arcgis.com/weJ1QsnbMYJlCHdG/arcgis/rest/services/Daily_Trade_Data/FeatureServer/0/query?"

CHOKEPOINTS_BASE_URL = "https://services9.arcgis.com/weJ1QsnbMYJlCHdG/arcgis/rest/services/Daily_Chokepoints_Data/FeatureServer/0/query?"

CHOKEPOINTS_ID_MAP = {
    "chokepoint1": "Suez Canal",
    "chokepoint2": "Panama Canal",
    "chokepoint3": "Bosporus Strait",
    "chokepoint4": "Bab el-Mandeb Strait",
    "chokepoint5": "Malacca Strait",
    "chokepoint6": "Strait of Hormuz",
    "chokepoint7": "Cape of Good Hope",
    "chokepoint8": "Gibraltar Strait",
    "chokepoint9": "Dover Strait",
    "chokepoint10": "Oresund Strait",
    "chokepoint11": "Taiwan Strait",
    "chokepoint12": "Korea Strait",
    "chokepoint13": "Tsugaru Strait",
    "chokepoint14": "Luzon Strait",
    "chokepoint15": "Lombok Strait",
    "chokepoint16": "Ombai Strait",
    "chokepoint17": "Bohai Strait",
    "chokepoint18": "Torres Strait",
    "chokepoint19": "Sunda Strait",
    "chokepoint20": "Makassar Strait",
    "chokepoint21": "Magellan Strait",
    "chokepoint22": "Yucatan Channel",
    "chokepoint23": "Windward Passage",
    "chokepoint24": "Mona Passage",
}

CHOKEPOINTS_NAME_TO_ID = {
    v.replace(" ", "_").lower(): k for k, v in CHOKEPOINTS_ID_MAP.items()
}

ChokepointsNames = Literal[
    "suez_canal",
    "panama_canal",
    "bosporus_strait",
    "bab_el_mandeb_strait",
    "malacca_strait",
    "strait_of_hormuz",
    "cape_of_good_hope",
    "gibraltar_strait",
    "dover_strait",
    "oresund_strait",
    "taiwan_strait",
    "korea_strait",
    "tsugaru_strait",
    "luzon_strait",
    "lombok_strait",
    "ombai_strait",
    "bohai_strait",
    "torres_strait",
    "sunda_strait",
    "makassar_strait",
    "magellan_strait",
    "yucatan_channel",
    "windward_passage",
    "mona_passage",
]


PORT_CONTINENTS = [
    {"label": "North America", "value": "north_america"},
    {"label": "Europe", "value": "europe"},
    {"label": "Asia & Pacific", "value": "asia_pacific"},
    {"label": "South America", "value": "south_america"},
    {"label": "Africa", "value": "africa"},
]

PortContinents = Literal[
    "north_america",
    "europe",
    "asia_pacific",
    "south_america",
    "africa",
]

PORT_COUNTRIES: dict = {
    "Albania": "ALB",
    "Algeria": "DZA",
    "American Samoa": "ASM",
    "Angola": "AGO",
    "Anguilla": "AIA",
    "Antigua and Barbuda": "ATG",
    "Argentina": "ARG",
    "Aruba": "ABW",
    "Australia": "AUS",
    "Azerbaijan": "AZE",
    "Bahrain": "BHR",
    "Bangladesh": "BGD",
    "Barbados": "BRB",
    "Belgium": "BEL",
    "Belize": "BLZ",
    "Benin": "BEN",
    "Bonaire, Saint Eustatius and Saba": "BES",
    "Brazil": "BRA",
    "British Virgin Islands": "VGB",
    "Brunei Darussalam": "BRN",
    "Bulgaria": "BGR",
    "Cabo Verde": "CPV",
    "Cambodia": "KHM",
    "Cameroon": "CMR",
    "Canada": "CAN",
    "Cayman Islands": "CYM",
    "Chile": "CHL",
    "China": "CHN",
    "Colombia": "COL",
    "Comoros": "COM",
    "Cook Islands": "COK",
    "Costa Rica": "CRI",
    "Croatia": "HRV",
    "Cuba": "CUB",
    "Curaçao": "CUW",
    "Cyprus": "CYP",
    "Côte d'Ivoire": "CIV",
    "Democratic Republic of the Congo": "COD",
    "Denmark": "DNK",
    "Djibouti": "DJI",
    "Dominica": "DMA",
    "Dominican Republic": "DOM",
    "Ecuador": "ECU",
    "Egypt": "EGY",
    "El Salvador": "SLV",
    "Equatorial Guinea": "GNQ",
    "Eritrea": "ERI",
    "Estonia": "EST",
    "Faroe Islands": "FRO",
    "Fiji": "FJI",
    "Finland": "FIN",
    "France": "FRA",
    "French Guiana": "GUF",
    "French Polynesia": "PYF",
    "Gabon": "GAB",
    "Georgia": "GEO",
    "Germany": "DEU",
    "Ghana": "GHA",
    "Gibraltar": "GIB",
    "Greece": "GRC",
    "Grenada": "GRD",
    "Guadeloupe": "GLP",
    "Guam": "GUM",
    "Guatemala": "GTM",
    "Guinea": "GIN",
    "Guinea-Bissau": "GNB",
    "Guyana": "GUY",
    "Haiti": "HTI",
    "Honduras": "HND",
    "Hong Kong SAR": "HKG",
    "Iceland": "ISL",
    "India": "IND",
    "Indonesia": "IDN",
    "Iran": "IRN",
    "Iraq": "IRQ",
    "Ireland": "IRL",
    "Israel": "ISR",
    "Italy": "ITA",
    "Jamaica": "JAM",
    "Japan": "JPN",
    "Jordan": "JOR",
    "Kazakhstan": "KAZ",
    "Kenya": "KEN",
    "Kiribati": "KIR",
    "Korea": "KOR",
    "Kuwait": "KWT",
    "Latvia": "LVA",
    "Lebanon": "LBN",
    "Liberia": "LBR",
    "Libya": "LBY",
    "Lithuania": "LTU",
    "Macao SAR": "MAC",
    "Madagascar": "MDG",
    "Malaysia": "MYS",
    "Maldives": "MDV",
    "Malta": "MLT",
    "Marshall Islands": "MHL",
    "Martinique": "MTQ",
    "Mauritania": "MRT",
    "Mauritius": "MUS",
    "Mayotte": "MYT",
    "Mexico": "MEX",
    "Micronesia": "FSM",
    "Moldova": "MDA",
    "Montenegro": "MNE",
    "Montserrat": "MSR",
    "Morocco": "MAR",
    "Mozambique": "MOZ",
    "Myanmar": "MMR",
    "Namibia": "NAM",
    "Nauru": "NRU",
    "New Caledonia": "NCL",
    "New Zealand": "NZL",
    "Nicaragua": "NIC",
    "Nigeria": "NGA",
    "Northern Mariana Islands": "MNP",
    "Norway": "NOR",
    "Oman": "OMN",
    "Pakistan": "PAK",
    "Palau": "PLW",
    "Panama": "PAN",
    "Papua New Guinea": "PNG",
    "Peru": "PER",
    "Philippines": "PHL",
    "Poland": "POL",
    "Portugal": "PRT",
    "Puerto Rico": "PRI",
    "Qatar": "QAT",
    "Republic of Congo": "COG",
    "Romania": "ROU",
    "Russian Federation": "RUS",
    "Réunion": "REU",
    "Saint Martin": "MAF",
    "Saint-Barthélemy": "BLM",
    "Samoa": "WSM",
    "Saudi Arabia": "SAU",
    "Senegal": "SEN",
    "Seychelles": "SYC",
    "Sierra Leone": "SLE",
    "Singapore": "SGP",
    "Sint Maarten": "SXM",
    "Slovenia": "SVN",
    "Solomon Islands": "SLB",
    "Somalia": "SOM",
    "South Africa": "ZAF",
    "Spain": "ESP",
    "Sri Lanka": "LKA",
    "St. Kitts and Nevis": "KNA",
    "St. Lucia": "LCA",
    "St. Vincent and the Grenadines": "VCT",
    "Sudan": "SDN",
    "Suriname": "SUR",
    "Sweden": "SWE",
    "Syria": "SYR",
    "São Tomé and Príncipe": "STP",
    "Taiwan Province of China": "TWN",
    "Tanzania": "TZA",
    "Thailand": "THA",
    "The Bahamas": "BHS",
    "The Gambia": "GMB",
    "The Netherlands": "NLD",
    "Timor-Leste": "TLS",
    "Togo": "TGO",
    "Tonga": "TON",
    "Trinidad and Tobago": "TTO",
    "Tunisia": "TUN",
    "Turkmenistan": "TKM",
    "Turks and Caicos Islands": "TCA",
    "Tuvalu": "TUV",
    "Türkiye": "TUR",
    "Ukraine": "UKR",
    "United Arab Emirates": "ARE",
    "United Kingdom": "GBR",
    "United States": "USA",
    "United States Virgin Islands": "VIR",
    "Uruguay": "URY",
    "Vanuatu": "VUT",
    "Venezuela": "VEN",
    "Vietnam": "VNM",
    "Yemen": "YEM",
}

PORT_COUNTRIES_CHOICES = [
    {"label": key, "value": value} for key, value in PORT_COUNTRIES.items()
]

PortCountries = Literal[
    "ABW",
    "AGO",
    "AIA",
    "ALB",
    "ARE",
    "ARG",
    "ASM",
    "ATG",
    "AUS",
    "AZE",
    "BEL",
    "BEN",
    "BES",
    "BGD",
    "BGR",
    "BHR",
    "BHS",
    "BLM",
    "BLZ",
    "BRA",
    "BRB",
    "BRN",
    "CAN",
    "CHL",
    "CHN",
    "CIV",
    "CMR",
    "COD",
    "COG",
    "COK",
    "COL",
    "COM",
    "CPV",
    "CRI",
    "CUB",
    "CUW",
    "CYM",
    "CYP",
    "DEU",
    "DJI",
    "DMA",
    "DNK",
    "DOM",
    "DZA",
    "ECU",
    "EGY",
    "ERI",
    "ESP",
    "EST",
    "FIN",
    "FJI",
    "FRA",
    "FRO",
    "FSM",
    "GAB",
    "GBR",
    "GEO",
    "GHA",
    "GIB",
    "GIN",
    "GLP",
    "GMB",
    "GNB",
    "GNQ",
    "GRC",
    "GRD",
    "GTM",
    "GUF",
    "GUM",
    "GUY",
    "HKG",
    "HND",
    "HRV",
    "HTI",
    "IDN",
    "IND",
    "IRL",
    "IRN",
    "IRQ",
    "ISL",
    "ISR",
    "ITA",
    "JAM",
    "JOR",
    "JPN",
    "KAZ",
    "KEN",
    "KHM",
    "KIR",
    "KNA",
    "KOR",
    "KWT",
    "LBN",
    "LBR",
    "LBY",
    "LCA",
    "LKA",
    "LTU",
    "LVA",
    "MAC",
    "MAF",
    "MAR",
    "MDA",
    "MDG",
    "MDV",
    "MEX",
    "MHL",
    "MLT",
    "MMR",
    "MNE",
    "MNP",
    "MOZ",
    "MRT",
    "MSR",
    "MTQ",
    "MUS",
    "MYS",
    "MYT",
    "NAM",
    "NCL",
    "NGA",
    "NIC",
    "NLD",
    "NOR",
    "NRU",
    "NZL",
    "OMN",
    "PAK",
    "PAN",
    "PER",
    "PHL",
    "PLW",
    "PNG",
    "POL",
    "PRI",
    "PRT",
    "PYF",
    "QAT",
    "REU",
    "ROU",
    "RUS",
    "SAU",
    "SDN",
    "SEN",
    "SGP",
    "SLB",
    "SLE",
    "SLV",
    "SOM",
    "STP",
    "SUR",
    "SVN",
    "SWE",
    "SXM",
    "SYC",
    "SYR",
    "TCA",
    "TGO",
    "THA",
    "TKM",
    "TLS",
    "TON",
    "TTO",
    "TUN",
    "TUR",
    "TUV",
    "TWN",
    "TZA",
    "UKR",
    "URY",
    "USA",
    "VCT",
    "VEN",
    "VGB",
    "VIR",
    "VNM",
    "VUT",
    "WSM",
    "YEM",
    "ZAF",
]


FSI_PRESETS = [
    "fsi_core",
    "fsi_core_underlying",
    "fsi_other",
    "fsi_encouraged_set",
    "fsi_balance_sheets",
    "fsi_all",
]

IRFCL_HEADLINE = "RAF_USD,RAFA_USD,RAFAFX_USD,RAOFA_USD,RAPFA_USD,RAFAIMF_USD,RAFASDR_USD,RAFAGOLD_USD,RACFA_USD,RAMDCD_USD,RAMFIFC_USD,RAMSR_USD"

RESERVE_ASSETS_AND_OTHER_FX_ASSETS = "RAF_USD,RAFA_USD,RAFAFX_USD,RAFAFXS_USD,RAFAFXSI_USD,RAFAFXCD_USD,RAFAFXCDN_USD,RAFAFXCDBI_USD,RAFAFXCDBIA_USD,RAFAFXCDBO_USD,RAFAFXCDBOA_USD,RAFAIMF_USD,RAFASDR_USD,RAFAGOLD_USD,RAFAGOLDV_OZT,RAFAO_USD,RAFAOF_USD,RAFAOL_USD,RAFAOO_USD,RAOFA_USD,RAOFAS_USD,RAOFAD_USD,RAOFAL_USD,RAOFAF_USD,RAOFAG_USD"

PREDETERMINED_DRAINS_ON_FX_ASSETS = "RAPFA_USD,RAPFALSD_USD,RAPFALSD_1M_USD,RAPFALSD_1M_3M_USD,RAPFALSD_3M_1Y_USD,RAPFALSDOP_USD,RAPFALSDOP_1M_USD,RAPFALSDOP_1M_3M_USD,RAPFALSDOP_3M_1Y_USD,RAPFALSDOI_USD,RAPFALSDOI_1M_USD,RAPFALSDOI_1M_3M_USD,RAPFALSDOI_3M_1Y_USD,RAPFALSDIP_USD,RAPFALSDIP_1M_USD,RAPFALSDIP_1M_3M_USD,RAPFALSDIP_3M_1Y_USD,RAPFALSDII_USD,RAPFALSDII_1M_USD,RAPFALSDII_1M_3M_USD,RAPFALSDII_3M_1Y_USD,RAPFAFFS_USD,RAPFAFFS_1M_USD,RAPFAFFS_1M_3M_USD,RAPFAFFS_3M_1Y_USD,RAPFAFFL_USD,RAPFAFFL_1M_USD,RAPFAFFL_1M_3M_USD,RAPFAFFL_3M_1Y_USD,RAPFAO_USD,RAPFAO_1M_USD,RAPFAO_1M_3M_USD,RAPFAO_3M_1Y_USD,RAPFAOOR_USD,RAPFAOOR_1M_USD,RAPFAOOR_1M_3M_USD,RAPFAOOR_3M_1Y_USD,RAPFAOIRR_USD,RAPFAOIRR_1M_USD,RAPFAOIRR_1M_3M_USD,RAPFAOIRR_3M_1Y_USD,RAPFAOOC_USD,RAPFAOOC_1M_USD,RAPFAOOC_1M_3M_USD,RAPFAOOC_3M_1Y_USD,RAPFAOIC_USD,RAPFAOIC_1M_USD,RAPFAOIC_1M_3M_USD,RAPFAOIC_3M_1Y_USD,RAPFAOOP_USD,RAPFAOOP_1M_USD,RAPFAOOP_1M_3M_USD,RAPFAOOP_3M_1Y_USD,RAPFAOIR_USD,RAPFAOIR_1M_USD,RAPFAOIR_1M_3M_USD,RAPFAOIR_3M_1Y_USD,RAFA_RAPFA_RO"

CONTINGENT_DRAINS_FX_ASSETS = "RACFA_USD,RACFAL_USD,RACFAL_1M_USD,RACFAL_1M_3M_USD,RACFAL_3M_1Y_USD,RACFALG_USD,RACFALG_1M_USD,RACFALG_1M_3M_USD,RACFALO_USD,RACFALO_1M_USD,RACFALO_1M_3M_USD,RACFALO_3M_1Y_USD,RACFAS_USD,RACFACB_USD,RACFACB_1M_USD,RACFACB_1M_3M_USD,RACFACB_3M_1Y_USD,RACFACBA_USD,RACFACBA_1M_USD,RACFACBA_1M_3M_USD,RACFACBA_3M_1Y_USD,RACFACBAOI_USD,RACFACBAOI_1M_USD,RACFACBAOI_1M_3M_USD,RACFACBAON_USD,RACFACBAON_1M_USD,RACFACBAON_1M_3M_USD,RACFACBAON_3M_1Y_USD,RACFACBABIS_USD,RACFACBAIMF_1M_USD,RACFACBAIMF_1M_3M_USD,RACFACBAIMF_3M_1Y_USD,RACFACBAIMF_USD,RACFACBFIR_USD,RACFACBFIR_1M_USD,RACFACBFIR_1M_3M_USD,RACFACBFIR_3M_1Y_USD,RACFACBFIO_USD,RACFACBFIO_1M_USD,RACFACBFIO_1M_3M_USD,RACFACBFIO_3M_1Y_USD,RACFACT_USD,RACFACT_1M_USD,RACFACT_1M_3M_USD,RACFACT_3M_1Y_USD,RACFACTA_USD,RACFACTA_1M_USD,RACFACTA_1M_3M_USD,RACFACTA_3M_1Y_USD,RACFACTAOI_USD,RACFACTAOI_1M_USD,RACFACTAOI_1M_3M_USD,RACFACTAOI_3M_1Y_USD,RACFACTAON_USD,RACFACTAON_1M_USD,RACFACTAON_1M_3M_USD,RACFACTAON_3M_1Y_USD,RACFACTABIS_USD,RACFACTABIS_1M_USD,RACFACTABIS_1M_3M_USD,RACFACTABIS_3M_1Y_USD,RACFACTAIMF_USD,RACFACTAIMF_1M_USD,RACFACTFIR_USD,RACFACTFIR_1M_USD,RACFACTFIR_1M_3M_USD,RACFACTFIR_3M_1Y_USD,RACFACTFIO_USD,RACFACTFIO_1M_USD,RACFACTFIO_1M_3M_USD,RACFACTFIO_3M_1Y_USD,RACFAPPS_USD,RACFAPPS_1M_USD,RACFAPPS_1M_3M_USD,RACFAPPS_3M_1Y_USD,RACFAPPSBP_USD,RACFAPPSBP_1M_USD,RACFAPPSBP_1M_3M_USD,RACFAPPSBP_3M_1Y_USD,RACFAPPSWC_USD,RACFAPPSWC_1M_USD,RACFAPPSWC_1M_3M_USD,RACFAPPSWC_3M_1Y_USD,RACFAPPL_USD,RACFAPPL_1M_USD,RACFAPPL_1M_3M_USD,RACFAPPL_3M_1Y_USD,RACFAPPLBC_USD,RACFAPPLBC_1M_USD,RACFAPPLBC_1M_3M_USD,RACFAPPLBC_3M_1Y_USD,RACFAPPLWP_USD,RACFAPPLWP_1M_USD,RACFAPPLWP_1M_3M_USD,RACFAPPLWP_3M_1Y_USD,RACFAMPAS_USD,RACFAMPAS_1M_USD,RACFAMPAS_1M_3M_USD,RACFAMPAS_3M_1Y_USD,RACFAMPAL_USD,RACFAMPAL_1M_USD,RACFAMPAL_1M_3M_USD,RACFAMPAL_3M_1Y_USD,RACFAMPBS_USD,RACFAMPBS_1M_USD,RACFAMPBS_1M_3M_USD,RACFAMPBS_3M_1Y_USD,RACFAMPBL_USD,RACFAMPBL_1M_USD,RACFAMPBL_1M_3M_USD,RACFAMPBL_3M_1Y_USD,RACFAMPCS_USD,RACFAMPCS_1M_USD,RACFAMPCS_1M_3M_USD,RACFAMPCS_3M_1Y_USD,RACFAMPCL_USD,RACFAMPCL_1M_USD,RACFAMPCL_1M_3M_USD,RACFAMPCL_3M_1Y_USD,RACFAMPDS_USD,RACFAMPDS_1M_USD,RACFAMPDS_1M_3M_USD,RACFAMPDS_3M_1Y_USD,RACFAMPDL_USD,RACFAMPDL_1M_USD,RACFAMPDL_1M_3M_USD,RACFAMPDL_3M_1Y_USD,RACFAMPES_USD,RACFAMPES_1M_USD,RACFAMPES_1M_3M_USD,RACFAMPES_3M_1Y_USD,RACFAMPEL_USD,RACFAMPEL_1M_USD,RACFAMPEL_1M_3M_USD,RACFAMPEL_3M_1Y_USD,RACFAMPFS_USD,RACFAMPFS_1M_USD,RACFAMPFS_1M_3M_USD,RACFAMPFS_3M_1Y_USD,RACFAMPFL_USD,RACFAMPFL_1M_USD,RACFAMPFL_1M_3M_USD,RACFAMPFL_3M_1Y_USD"

IRFCL_MEMORANDUM_ITEMS = "RAMDCD_USD,RAMFIFC_USD,RAMPA_USD,RAMFFS_USD,RAMPAOA_USD,RAMSR_USD,RAMSRLRI_USD,RAMSRLRN_USD,RAMSRBRI_USD,RAMSRBAN_USD,RAMFDA_USD,RAMFDAF_USD,RAMFDAU_USD,RAMFDAW_USD,RAMFDAP_USD,RAMFDAO_USD,RAMFFL_USD,RAMPPS_USD,RAMPPSBP_USD,RAMPPSWC_USD,RAMPPL_USD,RAMPPLBP_USD,RAMPPLWC_USD,RAMCR_USD,RAMCRISDR_USD,RAMCRIC_USD_USD,RAMCRIC_EUR_USD,RAMCRIC_CNY_USD,RAMCRIC_JPY_USD,RAMCRIC_GBP_USD,RAMCROSDR_USD"

IRFCL_TABLES = {
    "reserve_assets_and_other_fx_assets": RESERVE_ASSETS_AND_OTHER_FX_ASSETS,
    "predetermined_drains_on_fx_assets": PREDETERMINED_DRAINS_ON_FX_ASSETS,
    "contingent_drains_fx_assets": CONTINGENT_DRAINS_FX_ASSETS,
    "memorandum_items": IRFCL_MEMORANDUM_ITEMS,
}

IRFCL_PRESET = {
    "irfcl_top_lines": IRFCL_HEADLINE,
    **IRFCL_TABLES,
    "gold_reserves": "RAFAGOLD_USD,RAFAGOLDV_OZT",
    "derivative_assets": "RAMFDA_USD",
}

FREQUENCY_DICT = {
    "month": "M",
    "quarter": "Q",
    "annual": "A",
}
REF_SECTORS_DICT = {
    "government_ex_social_security": "S1311",
    "central_bank": "S121",
    "monetary_authorities": "S1X",
    "all_sectors": "",
}

SECTOR_MAP = {v: k for k, v in REF_SECTORS_DICT.items()}

UNIT_MULTIPLIERS_MAP = {
    "0": "Units",
    "2": "Hundreds",
    "3": "Thousands",
    "6": "Millions",
    "9": "Billions",
    "12": "Trillions",
    "N15": "Quadrillionths",
    "N14": "Hundred Trillionths",
    "N13": "Ten Trillionths",
    "N12": "Trillionths",
    "N11": "Hundred Billionths",
    "N10": "Ten Billionths",
    "N9": "Billionths",
    "N8": "Hundred Millionths",
    "N7": "Ten Millionths",
    "N6": "Millionths",
    "N5": "Hundred Thousandths",
    "N4": "Ten Thousandths",
    "N3": "Thousandths",
    "N2": "Hundredths",
    "N1": "Tenths",
    "1": "Tens",
    "4": "Ten Thousands",
    "5": "Hundred Thousands",
    "7": "Ten Millions",
    "8": "Hundred Millions",
    "10": "Ten Billions",
    "11": "Hundred Billions",
    "13": "Ten Trillions",
    "14": "Hundred Trillions",
    "15": "Quadrillions",
}

TIME_FORMAT_MAP = {
    "P1Y": "Annual",
    "P6M": "Bi-annual",
    "P3M": "Quarterly",
    "P1M": "Monthly",
    "P7D": "Weekly",
    "P1D": "Daily",
}

REF_SECTOR_MAP = {
    "S1311": "Central government excluding social security",
    "S121": "Central Bank",
    "S1X": "Monetary Authorities",
    "1C_AS": "All Sectors",
    "AllSectorsIncludingAllSectors": "All Sectors Including All Sectors",
}


def load_symbols(dataset: str) -> dict:
    """Load IMF symbol list."""
    # pylint: disable=import-outside-toplevel
    import json  # noqa
    from json.decoder import JSONDecodeError
    from pathlib import Path
    from openbb_core.app.model.abstract.error import OpenBBError

    try:
        symbols_file = Path(__file__).parents[1].joinpath("assets", "imf_symbols.json")
        with symbols_file.open(encoding="utf-8") as file:
            symbols = json.load(file)
    except (FileNotFoundError, JSONDecodeError) as e:
        raise OpenBBError(
            f"Failed to load IMF symbols from the static file: {e}"
        ) from e

    if dataset == "all":
        return symbols

    return {k: v for k, v in symbols.items() if v["dataset"] == dataset}
