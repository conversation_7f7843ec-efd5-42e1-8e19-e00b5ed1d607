{"version": 3, "file": "3336.1430b8576b899f650fb9.js?v=1430b8576b899f650fb9", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;AAEA;AACA,8CAA8C,GAAG,IAAI;AACrD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,gEAAgE;AAChE,2DAA2D;AAC3D;AACA,6DAA6D;;AAE7D;AACA;AACA,WAAW;;AAEX;AACA,gCAAgC;AAChC,gCAAgC;AAChC,mCAAmC;;AAEnC,wEAAwE;AACxE,4GAA4G;;AAE5G;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;;AAGO;AACP;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,oBAAoB;AACpB;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/octave.js"], "sourcesContent": ["function wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\");\n}\n\nvar singleOperators = new RegExp(\"^[\\\\+\\\\-\\\\*/&|\\\\^~<>!@'\\\\\\\\]\");\nvar singleDelimiters = new RegExp('^[\\\\(\\\\[\\\\{\\\\},:=;\\\\.]');\nvar doubleOperators = new RegExp(\"^((==)|(~=)|(<=)|(>=)|(<<)|(>>)|(\\\\.[\\\\+\\\\-\\\\*/\\\\^\\\\\\\\]))\");\nvar doubleDelimiters = new RegExp(\"^((!=)|(\\\\+=)|(\\\\-=)|(\\\\*=)|(/=)|(&=)|(\\\\|=)|(\\\\^=))\");\nvar tripleDelimiters = new RegExp(\"^((>>=)|(<<=))\");\nvar expressionEnd = new RegExp(\"^[\\\\]\\\\)]\");\nvar identifiers = new RegExp(\"^[_A-Za-z\\xa1-\\uffff][_A-Za-z0-9\\xa1-\\uffff]*\");\n\nvar builtins = wordRegexp([\n  'error', 'eval', 'function', 'abs', 'acos', 'atan', 'asin', 'cos',\n  'cosh', 'exp', 'log', 'prod', 'sum', 'log10', 'max', 'min', 'sign', 'sin', 'sinh',\n  'sqrt', 'tan', 'reshape', 'break', 'zeros', 'default', 'margin', 'round', 'ones',\n  'rand', 'syn', 'ceil', 'floor', 'size', 'clear', 'zeros', 'eye', 'mean', 'std', 'cov',\n  'det', 'eig', 'inv', 'norm', 'rank', 'trace', 'expm', 'logm', 'sqrtm', 'linspace', 'plot',\n  'title', 'xlabel', 'ylabel', 'legend', 'text', 'grid', 'meshgrid', 'mesh', 'num2str',\n  'fft', 'ifft', 'arrayfun', 'cellfun', 'input', 'fliplr', 'flipud', 'ismember'\n]);\n\nvar keywords = wordRegexp([\n  'return', 'case', 'switch', 'else', 'elseif', 'end', 'endif', 'endfunction',\n  'if', 'otherwise', 'do', 'for', 'while', 'try', 'catch', 'classdef', 'properties', 'events',\n  'methods', 'global', 'persistent', 'endfor', 'endwhile', 'printf', 'sprintf', 'disp', 'until',\n  'continue', 'pkg'\n]);\n\n\n// tokenizers\nfunction tokenTranspose(stream, state) {\n  if (!stream.sol() && stream.peek() === '\\'') {\n    stream.next();\n    state.tokenize = tokenBase;\n    return 'operator';\n  }\n  state.tokenize = tokenBase;\n  return tokenBase(stream, state);\n}\n\n\nfunction tokenComment(stream, state) {\n  if (stream.match(/^.*%}/)) {\n    state.tokenize = tokenBase;\n    return 'comment';\n  };\n  stream.skipToEnd();\n  return 'comment';\n}\n\nfunction tokenBase(stream, state) {\n  // whitespaces\n  if (stream.eatSpace()) return null;\n\n  // Handle one line Comments\n  if (stream.match('%{')){\n    state.tokenize = tokenComment;\n    stream.skipToEnd();\n    return 'comment';\n  }\n\n  if (stream.match(/^[%#]/)){\n    stream.skipToEnd();\n    return 'comment';\n  }\n\n  // Handle Number Literals\n  if (stream.match(/^[0-9\\.+-]/, false)) {\n    if (stream.match(/^[+-]?0x[0-9a-fA-F]+[ij]?/)) {\n      stream.tokenize = tokenBase;\n      return 'number'; };\n    if (stream.match(/^[+-]?\\d*\\.\\d+([EeDd][+-]?\\d+)?[ij]?/)) { return 'number'; };\n    if (stream.match(/^[+-]?\\d+([EeDd][+-]?\\d+)?[ij]?/)) { return 'number'; };\n  }\n  if (stream.match(wordRegexp(['nan','NaN','inf','Inf']))) { return 'number'; };\n\n  // Handle Strings\n  var m = stream.match(/^\"(?:[^\"]|\"\")*(\"|$)/) || stream.match(/^'(?:[^']|'')*('|$)/)\n  if (m) { return m[1] ? 'string' : \"error\"; }\n\n  // Handle words\n  if (stream.match(keywords)) { return 'keyword'; } ;\n  if (stream.match(builtins)) { return 'builtin'; } ;\n  if (stream.match(identifiers)) { return 'variable'; } ;\n\n  if (stream.match(singleOperators) || stream.match(doubleOperators)) { return 'operator'; };\n  if (stream.match(singleDelimiters) || stream.match(doubleDelimiters) || stream.match(tripleDelimiters)) { return null; };\n\n  if (stream.match(expressionEnd)) {\n    state.tokenize = tokenTranspose;\n    return null;\n  };\n\n\n  // Handle non-detected items\n  stream.next();\n  return 'error';\n};\n\n\nexport const octave = {\n  name: \"octave\",\n\n  startState: function() {\n    return {\n      tokenize: tokenBase\n    };\n  },\n\n  token: function(stream, state) {\n    var style = state.tokenize(stream, state);\n    if (style === 'number' || style === 'variable'){\n      state.tokenize = tokenTranspose;\n    }\n    return style;\n  },\n\n  languageData: {\n    commentTokens: {line: \"%\"}\n  }\n};\n\n"], "names": [], "sourceRoot": ""}