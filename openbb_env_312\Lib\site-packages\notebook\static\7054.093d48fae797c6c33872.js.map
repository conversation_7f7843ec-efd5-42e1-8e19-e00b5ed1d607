{"version": 3, "file": "7054.093d48fae797c6c33872.js?v=093d48fae797c6c33872", "mappings": ";;;;;;;;;;AAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,4BAA4B,EAAE,EAAE,mCAAmC;AACnE,2BAA2B,EAAE,EAAE,oCAAoC;AACnE,6BAA6B,EAAE,EAAE;AACjC,4BAA4B,EAAE,EAAE;;AAEhC,2BAA2B,EAAE,EAAE,kCAAkC;AACjE,0BAA0B,EAAE,EAAE,2BAA2B;;AAEzD,4BAA4B,EAAE,EAAE;;AAEhC;AACA;AACA;AACA;;AAEA;AACA;;AAEA,8BAA8B;;AAE9B;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,sBAAsB;AACtB,qBAAqB;AACrB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,gBAAgB,KAAK;AACrB,wBAAwB;AACxB;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;;AAEA,cAAc,qBAAqB;AACnC;;AAEA;AACA;AACA;AACA,oEAAoE;AACpE;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,sBAAsB;AACtB;AACA;AACA;;AAEA,8BAA8B;AAC9B;AACA;AACA;;AAEA,mBAAmB;AACnB;AACA;AACA,MAAM,4BAA4B;AAClC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACO;AACP;;AAEA;AACA,YAAY;AACZ,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/tiddlywiki.js"], "sourcesContent": ["// Tokenizer\nvar textwords = {};\n\nvar keywords = {\n  \"allTags\": true, \"closeAll\": true, \"list\": true,\n  \"newJournal\": true, \"newTiddler\": true,\n  \"permaview\": true, \"saveChanges\": true,\n  \"search\": true, \"slider\": true, \"tabs\": true,\n  \"tag\": true, \"tagging\": true, \"tags\": true,\n  \"tiddler\": true, \"timeline\": true,\n  \"today\": true, \"version\": true, \"option\": true,\n  \"with\": true, \"filter\": true\n};\n\nvar isSpaceName = /[\\w_\\-]/i,\n    reHR = /^\\-\\-\\-\\-+$/,                                 // <hr>\n    reWikiCommentStart = /^\\/\\*\\*\\*$/,            // /***\n    reWikiCommentStop = /^\\*\\*\\*\\/$/,             // ***/\n    reBlockQuote = /^<<<$/,\n\n    reJsCodeStart = /^\\/\\/\\{\\{\\{$/,                       // //{{{ js block start\n    reJsCodeStop = /^\\/\\/\\}\\}\\}$/,                        // //}}} js stop\n    reXmlCodeStart = /^<!--\\{\\{\\{-->$/,           // xml block start\n    reXmlCodeStop = /^<!--\\}\\}\\}-->$/,            // xml stop\n\n    reCodeBlockStart = /^\\{\\{\\{$/,                        // {{{ TW text div block start\n    reCodeBlockStop = /^\\}\\}\\}$/,                 // }}} TW text stop\n\n    reUntilCodeStop = /.*?\\}\\}\\}/;\n\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\n\nfunction tokenBase(stream, state) {\n  var sol = stream.sol(), ch = stream.peek();\n\n  state.block = false;        // indicates the start of a code block.\n\n  // check start of  blocks\n  if (sol && /[<\\/\\*{}\\-]/.test(ch)) {\n    if (stream.match(reCodeBlockStart)) {\n      state.block = true;\n      return chain(stream, state, twTokenCode);\n    }\n    if (stream.match(reBlockQuote))\n      return 'quote';\n    if (stream.match(reWikiCommentStart) || stream.match(reWikiCommentStop))\n      return 'comment';\n    if (stream.match(reJsCodeStart) || stream.match(reJsCodeStop) || stream.match(reXmlCodeStart) || stream.match(reXmlCodeStop))\n      return 'comment';\n    if (stream.match(reHR))\n      return 'contentSeparator';\n  }\n\n  stream.next();\n  if (sol && /[\\/\\*!#;:>|]/.test(ch)) {\n    if (ch == \"!\") { // tw header\n      stream.skipToEnd();\n      return \"header\";\n    }\n    if (ch == \"*\") { // tw list\n      stream.eatWhile('*');\n      return \"comment\";\n    }\n    if (ch == \"#\") { // tw numbered list\n      stream.eatWhile('#');\n      return \"comment\";\n    }\n    if (ch == \";\") { // definition list, term\n      stream.eatWhile(';');\n      return \"comment\";\n    }\n    if (ch == \":\") { // definition list, description\n      stream.eatWhile(':');\n      return \"comment\";\n    }\n    if (ch == \">\") { // single line quote\n      stream.eatWhile(\">\");\n      return \"quote\";\n    }\n    if (ch == '|')\n      return 'header';\n  }\n\n  if (ch == '{' && stream.match('{{'))\n    return chain(stream, state, twTokenCode);\n\n  // rudimentary html:// file:// link matching. TW knows much more ...\n  if (/[hf]/i.test(ch) &&\n      /[ti]/i.test(stream.peek()) &&\n      stream.match(/\\b(ttps?|tp|ile):\\/\\/[\\-A-Z0-9+&@#\\/%?=~_|$!:,.;]*[A-Z0-9+&@#\\/%=~_|$]/i))\n    return \"link\";\n\n  // just a little string indicator, don't want to have the whole string covered\n  if (ch == '\"')\n    return 'string';\n\n  if (ch == '~')    // _no_ CamelCase indicator should be bold\n    return 'brace';\n\n  if (/[\\[\\]]/.test(ch) && stream.match(ch)) // check for [[..]]\n    return 'brace';\n\n  if (ch == \"@\") {    // check for space link. TODO fix @@...@@ highlighting\n    stream.eatWhile(isSpaceName);\n    return \"link\";\n  }\n\n  if (/\\d/.test(ch)) {        // numbers\n    stream.eatWhile(/\\d/);\n    return \"number\";\n  }\n\n  if (ch == \"/\") { // tw invisible comment\n    if (stream.eat(\"%\")) {\n      return chain(stream, state, twTokenComment);\n    } else if (stream.eat(\"/\")) { //\n      return chain(stream, state, twTokenEm);\n    }\n  }\n\n  if (ch == \"_\" && stream.eat(\"_\")) // tw underline\n    return chain(stream, state, twTokenUnderline);\n\n  // strikethrough and mdash handling\n  if (ch == \"-\" && stream.eat(\"-\")) {\n    // if strikethrough looks ugly, change CSS.\n    if (stream.peek() != ' ')\n      return chain(stream, state, twTokenStrike);\n    // mdash\n    if (stream.peek() == ' ')\n      return 'brace';\n  }\n\n  if (ch == \"'\" && stream.eat(\"'\")) // tw bold\n    return chain(stream, state, twTokenStrong);\n\n  if (ch == \"<\" && stream.eat(\"<\")) // tw macro\n    return chain(stream, state, twTokenMacro);\n\n  // core macro handling\n  stream.eatWhile(/[\\w\\$_]/);\n  return textwords.propertyIsEnumerable(stream.current()) ? \"keyword\" : null\n}\n\n// tw invisible comment\nfunction twTokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"%\");\n  }\n  return \"comment\";\n}\n\n// tw strong / bold\nfunction twTokenStrong(stream, state) {\n  var maybeEnd = false,\n      ch;\n  while (ch = stream.next()) {\n    if (ch == \"'\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"'\");\n  }\n  return \"strong\";\n}\n\n// tw code\nfunction twTokenCode(stream, state) {\n  var sb = state.block;\n\n  if (sb && stream.current()) {\n    return \"comment\";\n  }\n\n  if (!sb && stream.match(reUntilCodeStop)) {\n    state.tokenize = tokenBase;\n    return \"comment\";\n  }\n\n  if (sb && stream.sol() && stream.match(reCodeBlockStop)) {\n    state.tokenize = tokenBase;\n    return \"comment\";\n  }\n\n  stream.next();\n  return \"comment\";\n}\n\n// tw em / italic\nfunction twTokenEm(stream, state) {\n  var maybeEnd = false,\n      ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"/\");\n  }\n  return \"emphasis\";\n}\n\n// tw underlined text\nfunction twTokenUnderline(stream, state) {\n  var maybeEnd = false,\n      ch;\n  while (ch = stream.next()) {\n    if (ch == \"_\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"_\");\n  }\n  return \"link\";\n}\n\n// tw strike through text looks ugly\n// change CSS if needed\nfunction twTokenStrike(stream, state) {\n  var maybeEnd = false, ch;\n\n  while (ch = stream.next()) {\n    if (ch == \"-\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"-\");\n  }\n  return \"deleted\";\n}\n\n// macro\nfunction twTokenMacro(stream, state) {\n  if (stream.current() == '<<') {\n    return 'meta';\n  }\n\n  var ch = stream.next();\n  if (!ch) {\n    state.tokenize = tokenBase;\n    return null;\n  }\n  if (ch == \">\") {\n    if (stream.peek() == '>') {\n      stream.next();\n      state.tokenize = tokenBase;\n      return \"meta\";\n    }\n  }\n\n  stream.eatWhile(/[\\w\\$_]/);\n  return keywords.propertyIsEnumerable(stream.current()) ? \"keyword\" : null\n}\n\n// Interface\nexport const tiddlyWiki = {\n  name: \"tiddlywiki\",\n\n  startState: function () {\n    return {tokenize: tokenBase};\n  },\n\n  token: function (stream, state) {\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n    return style;\n  }\n};\n\n"], "names": [], "sourceRoot": ""}