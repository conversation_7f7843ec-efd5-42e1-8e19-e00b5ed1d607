{"version": 3, "file": "9331.5850506ebb1d3f304481.js?v=5850506ebb1d3f304481", "mappings": ";;;;;;;;;;AAAA;AACA,kBAAkB,gBAAgB;AAClC;AACA;AACA,kBAAkB,gBAAgB;AAClC;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;;;AAGD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,IAAI,gBAAgB;AACpB;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,oBAAoB,QAAQ;AAC5B;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/dylan.js"], "sourcesContent": ["function forEach(arr, f) {\n  for (var i = 0; i < arr.length; i++) f(arr[i], i)\n}\nfunction some(arr, f) {\n  for (var i = 0; i < arr.length; i++) if (f(arr[i], i)) return true\n  return false\n}\n\n// Words\nvar words = {\n  // Words that introduce unnamed definitions like \"define interface\"\n  unnamedDefinition: [\"interface\"],\n\n  // Words that introduce simple named definitions like \"define library\"\n  namedDefinition: [\"module\", \"library\", \"macro\",\n                    \"C-struct\", \"C-union\",\n                    \"C-function\", \"C-callable-wrapper\"\n                   ],\n\n  // Words that introduce type definitions like \"define class\".\n  // These are also parameterized like \"define method\" and are\n  // appended to otherParameterizedDefinitionWords\n  typeParameterizedDefinition: [\"class\", \"C-subtype\", \"C-mapped-subtype\"],\n\n  // Words that introduce trickier definitions like \"define method\".\n  // These require special definitions to be added to startExpressions\n  otherParameterizedDefinition: [\"method\", \"function\",\n                                 \"C-variable\", \"C-address\"\n                                ],\n\n  // Words that introduce module constant definitions.\n  // These must also be simple definitions and are\n  // appended to otherSimpleDefinitionWords\n  constantSimpleDefinition: [\"constant\"],\n\n  // Words that introduce module variable definitions.\n  // These must also be simple definitions and are\n  // appended to otherSimpleDefinitionWords\n  variableSimpleDefinition: [\"variable\"],\n\n  // Other words that introduce simple definitions\n  // (without implicit bodies).\n  otherSimpleDefinition: [\"generic\", \"domain\",\n                          \"C-pointer-type\",\n                          \"table\"\n                         ],\n\n  // Words that begin statements with implicit bodies.\n  statement: [\"if\", \"block\", \"begin\", \"method\", \"case\",\n              \"for\", \"select\", \"when\", \"unless\", \"until\",\n              \"while\", \"iterate\", \"profiling\", \"dynamic-bind\"\n             ],\n\n  // Patterns that act as separators in compound statements.\n  // This may include any general pattern that must be indented\n  // specially.\n  separator: [\"finally\", \"exception\", \"cleanup\", \"else\",\n              \"elseif\", \"afterwards\"\n             ],\n\n  // Keywords that do not require special indentation handling,\n  // but which should be highlighted\n  other: [\"above\", \"below\", \"by\", \"from\", \"handler\", \"in\",\n          \"instance\", \"let\", \"local\", \"otherwise\", \"slot\",\n          \"subclass\", \"then\", \"to\", \"keyed-by\", \"virtual\"\n         ],\n\n  // Condition signaling function calls\n  signalingCalls: [\"signal\", \"error\", \"cerror\",\n                   \"break\", \"check-type\", \"abort\"\n                  ]\n};\n\nwords[\"otherDefinition\"] =\n  words[\"unnamedDefinition\"]\n  .concat(words[\"namedDefinition\"])\n  .concat(words[\"otherParameterizedDefinition\"]);\n\nwords[\"definition\"] =\n  words[\"typeParameterizedDefinition\"]\n  .concat(words[\"otherDefinition\"]);\n\nwords[\"parameterizedDefinition\"] =\n  words[\"typeParameterizedDefinition\"]\n  .concat(words[\"otherParameterizedDefinition\"]);\n\nwords[\"simpleDefinition\"] =\n  words[\"constantSimpleDefinition\"]\n  .concat(words[\"variableSimpleDefinition\"])\n  .concat(words[\"otherSimpleDefinition\"]);\n\nwords[\"keyword\"] =\n  words[\"statement\"]\n  .concat(words[\"separator\"])\n  .concat(words[\"other\"]);\n\n// Patterns\nvar symbolPattern = \"[-_a-zA-Z?!*@<>$%]+\";\nvar symbol = new RegExp(\"^\" + symbolPattern);\nvar patterns = {\n  // Symbols with special syntax\n  symbolKeyword: symbolPattern + \":\",\n  symbolClass: \"<\" + symbolPattern + \">\",\n  symbolGlobal: \"\\\\*\" + symbolPattern + \"\\\\*\",\n  symbolConstant: \"\\\\$\" + symbolPattern\n};\nvar patternStyles = {\n  symbolKeyword: \"atom\",\n  symbolClass: \"tag\",\n  symbolGlobal: \"variableName.standard\",\n  symbolConstant: \"variableName.constant\"\n};\n\n// Compile all patterns to regular expressions\nfor (var patternName in patterns)\n  if (patterns.hasOwnProperty(patternName))\n    patterns[patternName] = new RegExp(\"^\" + patterns[patternName]);\n\n// Names beginning \"with-\" and \"without-\" are commonly\n// used as statement macro\npatterns[\"keyword\"] = [/^with(?:out)?-[-_a-zA-Z?!*@<>$%]+/];\n\nvar styles = {};\nstyles[\"keyword\"] = \"keyword\";\nstyles[\"definition\"] = \"def\";\nstyles[\"simpleDefinition\"] = \"def\";\nstyles[\"signalingCalls\"] = \"builtin\";\n\n// protected words lookup table\nvar wordLookup = {};\nvar styleLookup = {};\n\nforEach([\n  \"keyword\",\n  \"definition\",\n  \"simpleDefinition\",\n  \"signalingCalls\"\n], function(type) {\n  forEach(words[type], function(word) {\n    wordLookup[word] = type;\n    styleLookup[word] = styles[type];\n  });\n});\n\n\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\n\nfunction tokenBase(stream, state) {\n  // String\n  var ch = stream.peek();\n  if (ch == \"'\" || ch == '\"') {\n    stream.next();\n    return chain(stream, state, tokenString(ch, \"string\"));\n  }\n  // Comment\n  else if (ch == \"/\") {\n    stream.next();\n    if (stream.eat(\"*\")) {\n      return chain(stream, state, tokenComment);\n    } else if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n    stream.backUp(1);\n  }\n  // Decimal\n  else if (/[+\\-\\d\\.]/.test(ch)) {\n    if (stream.match(/^[+-]?[0-9]*\\.[0-9]*([esdx][+-]?[0-9]+)?/i) ||\n        stream.match(/^[+-]?[0-9]+([esdx][+-]?[0-9]+)/i) ||\n        stream.match(/^[+-]?\\d+/)) {\n      return \"number\";\n    }\n  }\n  // Hash\n  else if (ch == \"#\") {\n    stream.next();\n    // Symbol with string syntax\n    ch = stream.peek();\n    if (ch == '\"') {\n      stream.next();\n      return chain(stream, state, tokenString('\"', \"string\"));\n    }\n    // Binary number\n    else if (ch == \"b\") {\n      stream.next();\n      stream.eatWhile(/[01]/);\n      return \"number\";\n    }\n    // Hex number\n    else if (ch == \"x\") {\n      stream.next();\n      stream.eatWhile(/[\\da-f]/i);\n      return \"number\";\n    }\n    // Octal number\n    else if (ch == \"o\") {\n      stream.next();\n      stream.eatWhile(/[0-7]/);\n      return \"number\";\n    }\n    // Token concatenation in macros\n    else if (ch == '#') {\n      stream.next();\n      return \"punctuation\";\n    }\n    // Sequence literals\n    else if ((ch == '[') || (ch == '(')) {\n      stream.next();\n      return \"bracket\";\n      // Hash symbol\n    } else if (stream.match(/f|t|all-keys|include|key|next|rest/i)) {\n      return \"atom\";\n    } else {\n      stream.eatWhile(/[-a-zA-Z]/);\n      return \"error\";\n    }\n  } else if (ch == \"~\") {\n    stream.next();\n    ch = stream.peek();\n    if (ch == \"=\") {\n      stream.next();\n      ch = stream.peek();\n      if (ch == \"=\") {\n        stream.next();\n        return \"operator\";\n      }\n      return \"operator\";\n    }\n    return \"operator\";\n  } else if (ch == \":\") {\n    stream.next();\n    ch = stream.peek();\n    if (ch == \"=\") {\n      stream.next();\n      return \"operator\";\n    } else if (ch == \":\") {\n      stream.next();\n      return \"punctuation\";\n    }\n  } else if (\"[](){}\".indexOf(ch) != -1) {\n    stream.next();\n    return \"bracket\";\n  } else if (\".,\".indexOf(ch) != -1) {\n    stream.next();\n    return \"punctuation\";\n  } else if (stream.match(\"end\")) {\n    return \"keyword\";\n  }\n  for (var name in patterns) {\n    if (patterns.hasOwnProperty(name)) {\n      var pattern = patterns[name];\n      if ((pattern instanceof Array && some(pattern, function(p) {\n        return stream.match(p);\n      })) || stream.match(pattern))\n        return patternStyles[name];\n    }\n  }\n  if (/[+\\-*\\/^=<>&|]/.test(ch)) {\n    stream.next();\n    return \"operator\";\n  }\n  if (stream.match(\"define\")) {\n    return \"def\";\n  } else {\n    stream.eatWhile(/[\\w\\-]/);\n    // Keyword\n    if (wordLookup.hasOwnProperty(stream.current())) {\n      return styleLookup[stream.current()];\n    } else if (stream.current().match(symbol)) {\n      return \"variable\";\n    } else {\n      stream.next();\n      return \"variableName.standard\";\n    }\n  }\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, maybeNested = false, nestedCount = 0, ch;\n  while ((ch = stream.next())) {\n    if (ch == \"/\" && maybeEnd) {\n      if (nestedCount > 0) {\n        nestedCount--;\n      } else {\n        state.tokenize = tokenBase;\n        break;\n      }\n    } else if (ch == \"*\" && maybeNested) {\n      nestedCount++;\n    }\n    maybeEnd = (ch == \"*\");\n    maybeNested = (ch == \"/\");\n  }\n  return \"comment\";\n}\n\nfunction tokenString(quote, style) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {\n        end = true;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !escaped) {\n      state.tokenize = tokenBase;\n    }\n    return style;\n  };\n}\n\n// Interface\nexport const dylan = {\n  name: \"dylan\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      currentIndent: 0\n    };\n  },\n  token: function(stream, state) {\n    if (stream.eatSpace())\n      return null;\n    var style = state.tokenize(stream, state);\n    return style;\n  },\n  languageData: {\n    commentTokens: {block: {open: \"/*\", close: \"*/\"}}\n  }\n};\n\n"], "names": [], "sourceRoot": ""}