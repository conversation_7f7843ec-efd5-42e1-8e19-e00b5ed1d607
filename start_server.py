#!/usr/bin/env python3
"""
启动OpenBB-CN服务器的脚本
"""

import sys
import os
import uvicorn

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🚀 正在启动OpenBB-CN服务器...")
    print("📁 当前工作目录:", os.getcwd())
    print("🐍 Python版本:", sys.version)
    
    # 导入应用
    from openbb_cn.api.app import app
    print("✅ 应用导入成功")
    
    # 启动服务器
    print("🌐 启动服务器在 http://localhost:8000")
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8000,
        log_level="info",
        access_log=True
    )
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保所有依赖都已安装")
    sys.exit(1)
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
