{"cells": [{"cell_type": "markdown", "metadata": {"id": "904arS7fV-jJ"}, "source": ["# **Forecasting Currency Exchange Rates Using OpenBB Historical Data**\n", "\n", "### **Description**\n", "This notebook demonstrates how to predict future movements in currency exchange rates using using OpenBB's historical data. This notebook builds different forecasting model capable of analyzing trends in currency pairs such as USD/EUR, enabling data-driven predictions for future rates. The models evaluates risk and potential returns, providing valuable insights for traders, investors, and financial analysts.\n", "\n", "\n", "\n", "---\n", "\n", "\n", "### Author\n", "[![Author Profile](https://img.shields.io/badge/Manish-k723-Color?style=flat&logo=github)](https://github.com/Manish-k723)\n", "\n", "\n", "[![Open currencyExchangeRateForecasting.ipynb with Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/drive/1KCq_z-Td-G4hoA5eglJ0vASimn1LevLY?usp=share_link)"]}, {"cell_type": "markdown", "metadata": {"id": "y2ikbrW3YfT4"}, "source": ["If you are running this notebook in Colab, you can run the following command to install the OpenBB Platform:\n", "\n", "```python\n", "!pip install openbb\n", "```"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"collapsed": true, "id": "qoaXoZXITR63"}, "outputs": [], "source": ["# !pip install openbb -q #uncommment if you are in google colab\n", "!pip install pmdarima -q"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"id": "z8Qrm6qrVxBq"}, "outputs": [], "source": ["from openbb import obb  # Fetches historical forex data from OpenBB\n", "import pandas as pd  # Data manipulation and analysis\n", "import numpy as np  # For numerical computations\n", "import matplotlib.pyplot as plt  # Data visualization\n", "import seaborn as sns\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error  # Evaluation of model performance (e.g., MSE)\n", "from sklearn.preprocessing import MinMaxScaler  # Data normalization (scaling values)\n", "from statsmodels.tsa.statespace.sarimax import SARIMAX  # Seasonal ARIMA forecasting model\n", "from statsmodels.tsa.holtwinters import ExponentialSmoothing  # Exponential smoothing for time-series\n", "import pmdarima as pm  # Auto-ARIMA for automatic ARIMA parameter selection\n", "from keras.models import Sequential\n", "from keras.layers import LSTM, <PERSON>se, Dropout  # LSTM neural network layers for time-series data"]}, {"cell_type": "markdown", "metadata": {"id": "RriDHTqjaGBo"}, "source": ["# **Loading Data**\n", "This cell fetches historical exchange rate data for the EUR/USD pair using the yfinance provider, choose the provider accordingly.\n", "\n", "Please Refer [Yfinance](https://pypi.org/project/yfinance/) documenation for list of currency exchange symbols."]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 235}, "id": "NKgbHVcbTTLi", "outputId": "5c746fe4-7102-498b-c0af-5c8e60daa4de"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                open      high       low     close  volume  split_ratio  \\\n", "date                                                                      \n", "2012-11-19  1.275998  1.281851  1.274746  1.275950       0          0.0   \n", "2008-03-20  1.564089  1.564089  1.540310  1.544211       0          0.0   \n", "2010-06-01  1.228803  1.233898  1.211504  1.223301       0          0.0   \n", "2010-09-08  1.267893  1.276194  1.266416  1.267797       0          0.0   \n", "2017-05-30  1.112941  1.120160  1.111074  1.112904       0          0.0   \n", "\n", "            dividend  \n", "date                  \n", "2012-11-19       0.0  \n", "2008-03-20       0.0  \n", "2010-06-01       0.0  \n", "2010-09-08       0.0  \n", "2017-05-30       0.0  "], "text/html": ["\n", "  <div id=\"df-d093896e-36f5-451c-a908-f5d8aa75a7b2\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>split_ratio</th>\n", "      <th>dividend</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2012-11-19</th>\n", "      <td>1.275998</td>\n", "      <td>1.281851</td>\n", "      <td>1.274746</td>\n", "      <td>1.275950</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2008-03-20</th>\n", "      <td>1.564089</td>\n", "      <td>1.564089</td>\n", "      <td>1.540310</td>\n", "      <td>1.544211</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-06-01</th>\n", "      <td>1.228803</td>\n", "      <td>1.233898</td>\n", "      <td>1.211504</td>\n", "      <td>1.223301</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-09-08</th>\n", "      <td>1.267893</td>\n", "      <td>1.276194</td>\n", "      <td>1.266416</td>\n", "      <td>1.267797</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2017-05-30</th>\n", "      <td>1.112941</td>\n", "      <td>1.120160</td>\n", "      <td>1.111074</td>\n", "      <td>1.112904</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-d093896e-36f5-451c-a908-f5d8aa75a7b2')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-d093896e-36f5-451c-a908-f5d8aa75a7b2 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-d093896e-36f5-451c-a908-f5d8aa75a7b2');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-ecaf1077-1693-495a-bb8a-095bcf7767bb\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-ecaf1077-1693-495a-bb8a-095bcf7767bb')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-ecaf1077-1693-495a-bb8a-095bcf7767bb button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"forex_df\",\n  \"rows\": 5,\n  \"fields\": [\n    {\n      \"column\": \"date\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": \"2008-03-20\",\n        \"max\": \"2017-05-30\",\n        \"num_unique_values\": 5,\n        \"samples\": [\n          \"2008-03-20\",\n          \"2017-05-30\",\n          \"2010-06-01\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"open\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.16651136510527548,\n        \"min\": 1.1129412651062012,\n        \"max\": 1.5640885829925537,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          1.5640885829925537,\n          1.1129412651062012,\n          1.2288031578063965\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"high\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.16373579707925556,\n        \"min\": 1.1201595067977905,\n        \"max\": 1.5640885829925537,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          1.5640885829925537,\n          1.1201595067977905,\n          1.2338976860046387\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"low\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.15905792946281996,\n        \"min\": 1.1110740900039673,\n        \"max\": 1.5403099060058594,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          1.5403099060058594,\n          1.1110740900039673,\n          1.2115044593811035\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"close\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.158895861499014,\n        \"min\": 1.1129041910171509,\n        \"max\": 1.54421067237854,\n        \"num_unique_values\": 5,\n        \"samples\": [\n          1.54421067237854,\n          1.1129041910171509,\n          1.2233014106750488\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"volume\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 0,\n        \"num_unique_values\": 1,\n        \"samples\": [\n          0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"split_ratio\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.0,\n        \"min\": 0.0,\n        \"max\": 0.0,\n        \"num_unique_values\": 1,\n        \"samples\": [\n          0.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"dividend\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.0,\n        \"min\": 0.0,\n        \"max\": 0.0,\n        \"num_unique_values\": 1,\n        \"samples\": [\n          0.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 18}], "source": ["# Fetching historical data for the EUR/USD pair using the yfinance provider\n", "start_date = '1991-01-01'\n", "end_date = '2024-01-01'\n", "\n", "# Since yfinance uses \"EURUSD=X\", we'll use that\n", "forex_df = obb.equity.price.historical(symbol=\"EURUSD=X\", provider=\"yfinance\", start_date=start_date, end_date=end_date).to_df()\n", "\n", "forex_df.sample(5)"]}, {"cell_type": "markdown", "metadata": {"id": "BbE_Q2eUaW_-"}, "source": ["# **Data Preprocessing**"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"id": "lhp-Dp6eIcHB"}, "outputs": [], "source": ["forex_df.index = pd.to_datetime(forex_df.index)\n", "\n", "forex_df = forex_df.asfreq('D')  # Resamples the data to a daily frequency ('D' stands for days), ensuring data is indexed daily\n", "\n", "forex_df.ffill(inplace=True) # Forward fills missing values to fill gaps in the time series with the last available value"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "z_elN-70ds7g", "outputId": "43b615da-eeb7-479c-8cb0-112f8cac7726"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "DatetimeIndex: 7337 entries, 2003-12-01 to 2024-01-01\n", "Freq: D\n", "Data columns (total 7 columns):\n", " #   Column       Non-Null Count  Dtype  \n", "---  ------       --------------  -----  \n", " 0   open         7337 non-null   float64\n", " 1   high         7337 non-null   float64\n", " 2   low          7337 non-null   float64\n", " 3   close        7337 non-null   float64\n", " 4   volume       7337 non-null   float64\n", " 5   split_ratio  7337 non-null   float64\n", " 6   dividend     7337 non-null   float64\n", "dtypes: float64(7)\n", "memory usage: 458.6 KB\n", "None\n"]}], "source": ["print(forex_df.info())"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4R7zxgIye0vB", "outputId": "c20869e1-c824-4b6a-91b7-e2b2cbc3b79f"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Training data: 5869 rows\n", "Testing data: 1468 rows\n"]}], "source": ["# Split the data, keeping 20% of it for testing\n", "train_size = int(len(forex_df) * 0.8)\n", "train_data, test_data = forex_df['close'][:train_size], forex_df['close'][train_size:]\n", "\n", "print(f\"Training data: {len(train_data)} rows\")\n", "print(f\"Testing data: {len(test_data)} rows\")"]}, {"cell_type": "markdown", "metadata": {"id": "RY3gicZ5acTF"}, "source": ["# **Model Training & Prediction**\n", "\n", "In this section, we focus on time-series forecasting, which differs from traditional machine learning tasks. Unlike predicting a single output variable, time-series models aim to predict future values based on historical data, considering the sequential nature of the data. This is particularly important for predicting currency exchange rates, where trends, seasonality, and past values heavily influence future movements."]}, {"cell_type": "markdown", "metadata": {"id": "6o4B_y1xbJH6"}, "source": ["# **ARIMA Model**\n", "\n", "We will start by using the ARIMA (AutoRegressive Integrated Moving Average) model for time-series forecasting. ARIMA is one of the most popular models for time-series analysis, as it combines three components:\n", "\n", "1. AR (AutoRegressive): Uses past values to predict future ones.\n", "2. I (Integrated): Makes the series stationary by differencing it.\n", "3. MA (Moving Average): Models the error terms from previous time steps.\n", "\n", "This model is ideal for capturing the trends and patterns in the currency exchange data. Let’s train and evaluate it on our dataset."]}, {"cell_type": "code", "execution_count": 22, "metadata": {"id": "5UJb-mEReOwp", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "7ea76241-b0bd-4394-c2ba-ff5fefc4679c"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["                               SARIMAX Results                                \n", "==============================================================================\n", "Dep. Variable:                      y   No. Observations:                 5869\n", "Model:               SARIMAX(2, 1, 3)   Log Likelihood               19709.279\n", "Date:                Sun, 06 Oct 2024   AIC                         -39406.558\n", "Time:                        09:24:51   BIC                         -39366.494\n", "Sample:                    12-01-2003   HQIC                        -39392.629\n", "                         - 12-25-2019                                         \n", "Covariance Type:                  opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "ar.L1          0.5440      0.123      4.435      0.000       0.304       0.784\n", "ar.L2         -0.5804      0.101     -5.747      0.000      -0.778      -0.382\n", "ma.L1         -0.7656      0.123     -6.247      0.000      -1.006      -0.525\n", "ma.L2          0.6676      0.119      5.628      0.000       0.435       0.900\n", "ma.L3         -0.1507      0.020     -7.389      0.000      -0.191      -0.111\n", "sigma2      7.081e-05   1.93e-07    366.780      0.000    7.04e-05    7.12e-05\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.00   Jarque-Bera (JB):           3900377.35\n", "Prob(Q):                              0.98   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               0.20   Skew:                             2.52\n", "Prob(H) (two-sided):                  0.00   Kurtosis:                       129.20\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n", "CPU times: user 1min 28s, sys: 1.49 s, total: 1min 30s\n", "Wall time: 1min 12s\n"]}], "source": ["%%time\n", "auto_arima_model = pm.auto_arima(train_data, seasonal=False, stepwise=True, suppress_warnings=True)\n", "arima_predictions = auto_arima_model.predict(n_periods=len(test_data))\n", "print(auto_arima_model.summary())"]}, {"cell_type": "markdown", "metadata": {"id": "mlfKog8Fbhn9"}, "source": ["# **SARIMAX Model**\n", "Next, we will explore the SARIMAX (Seasonal AutoRegressive Integrated Moving Average with eXogenous factors) model for forecasting. SARIMAX is an extension of the ARIMA model that incorporates seasonality and exogenous variables (optional external factors) into the prediction process.\n", "\n", "1. Seasonality: Captures repeating patterns over a fixed period (e.g., weekly or monthly cycles).\n", "2. Exogenous Variables (X): Allows the model to include additional factors that may influence the target variable (optional).\n", "\n", "SARIMAX is particularly useful when dealing with time-series data that exhibits periodic fluctuations, making it well-suited for forecasting currency exchange rates where trends may repeat over time. Let’s apply SARIMAX to our dataset."]}, {"cell_type": "code", "execution_count": 23, "metadata": {"id": "_Xgv3bq3E3iC", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "423bdd00-ace3-4cdd-b289-ff56a81e3469"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["CPU times: user 26 s, sys: 425 ms, total: 26.4 s\n", "Wall time: 26.5 s\n"]}], "source": ["%%time\n", "sarimax_model = SARIMAX(train_data,\n", "                       order=(5, 1, 0),  # non-seasonal order\n", "                       seasonal_order=(1, 1, 1, 12),  # seasonal order: parameters tuning is required\n", "                       enforce_stationarity=False,\n", "                       enforce_invertibility=False)\n", "\n", "sarimax_fit = sarimax_model.fit(disp=False)\n", "sarimax_predictions = sarimax_fit.forecast(steps=len(test_data))"]}, {"cell_type": "markdown", "metadata": {"id": "Dkwf2JuqbvmK"}, "source": ["# **Exponential Smoothing**\n", "We will also use the Exponential Smoothing technique for time-series forecasting. Unlike ARIMA and SARIMAX, this method places greater emphasis on more recent observations, making it useful for capturing short-term trends. Exponential smoothing can model various components of time series data, such as:\n", "\n", "1. Level: The baseline value of the series.\n", "2. <PERSON>rend: The overall direction of the series.\n", "3. Seasonality: The repeating short-term patterns.\n", "\n", "This method is particularly effective for forecasting time-series data with trends and seasonality, making it suitable for currency exchange rate prediction, where both short- and long-term movements need to be captured."]}, {"cell_type": "code", "execution_count": 24, "metadata": {"collapsed": true, "id": "p4jQd0FDb6XV", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "8c383758-1f2d-4532-fe7f-2f70db5e7a0c"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["CPU times: user 950 ms, sys: 5.11 ms, total: 955 ms\n", "Wall time: 955 ms\n"]}], "source": ["%%time\n", "exp_smooth_model = ExponentialSmoothing(train_data, trend='add', seasonal='add', seasonal_periods=12)\n", "exp_smooth_fit = exp_smooth_model.fit()\n", "\n", "# Predict using Exponential Smoothing\n", "exp_smooth_predictions = exp_smooth_fit.forecast(steps=len(test_data))"]}, {"cell_type": "markdown", "metadata": {"id": "r-d9J5U5cFCU"}, "source": ["# **LSTM Model**\n", "Finally, we will employ a Long Short-Term Memory (LSTM) model, a type of recurrent neural network (RNN) specifically designed to handle sequential data like time series. LSTMs excel at capturing long-term dependencies in data by using memory cells that can retain information over extended time periods, which makes them well-suited for tasks where past values influence future ones, such as currency exchange rate prediction.\n", "\n", "LSTMs are particularly powerful for modeling complex, non-linear relationships in time series data, making them ideal for forecasting in dynamic environments like financial markets, where historical patterns may vary in unexpected ways."]}, {"cell_type": "code", "execution_count": 25, "metadata": {"id": "4OcoKC5iPyds", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "73dd23e0-3f9e-4a33-f74b-43fa1f06a0a5"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Epoch 1/25\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/keras/src/layers/rnn/rnn.py:204: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 8ms/step - loss: 0.0317\n", "Epoch 2/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 9ms/step - loss: 0.0023\n", "Epoch 3/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 8ms/step - loss: 0.0021\n", "Epoch 4/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 7ms/step - loss: 0.0018\n", "Epoch 5/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 7ms/step - loss: 0.0018\n", "Epoch 6/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 7ms/step - loss: 0.0016\n", "Epoch 7/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 8ms/step - loss: 0.0015\n", "Epoch 8/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 10ms/step - loss: 0.0015\n", "Epoch 9/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 8ms/step - loss: 0.0014\n", "Epoch 10/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 10ms/step - loss: 0.0014\n", "Epoch 11/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 8ms/step - loss: 0.0013\n", "Epoch 12/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 8ms/step - loss: 0.0012\n", "Epoch 13/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 7ms/step - loss: 0.0012\n", "Epoch 14/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 9ms/step - loss: 0.0012\n", "Epoch 15/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 9ms/step - loss: 0.0010\n", "Epoch 16/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 7ms/step - loss: 8.8138e-04\n", "Epoch 17/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 8ms/step - loss: 8.0702e-04\n", "Epoch 18/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 7ms/step - loss: 8.5983e-04\n", "Epoch 19/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 9ms/step - loss: 8.2921e-04\n", "Epoch 20/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 16ms/step - loss: 8.4360e-04\n", "Epoch 21/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m4s\u001b[0m 8ms/step - loss: 9.6020e-04\n", "Epoch 22/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 8ms/step - loss: 7.4537e-04\n", "Epoch 23/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m3s\u001b[0m 8ms/step - loss: 7.6814e-04\n", "Epoch 24/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 8ms/step - loss: 6.9731e-04\n", "Epoch 25/25\n", "\u001b[1m182/182\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 9ms/step - loss: 8.1627e-04\n", "\u001b[1m44/44\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 4ms/step\n", "CPU times: user 43.2 s, sys: 1.85 s, total: 45.1 s\n", "Wall time: 55.1 s\n"]}], "source": ["%%time\n", "# Step 1: Data Preparation\n", "\n", "# Scale the close prices of train_data and test_data (Series)\n", "scaler = MinMaxScaler(feature_range=(0, 1))\n", "scaled_train_data = scaler.fit_transform(train_data.values.reshape(-1, 1))\n", "scaled_test_data = scaler.transform(test_data.values.reshape(-1, 1))\n", "\n", "# Creating dataset for LSTM from train_data\n", "X_train, y_train = [], []\n", "for i in range(60, len(scaled_train_data)):\n", "    X_train.append(scaled_train_data[i-60:i, 0])  # Previous 60 days\n", "    y_train.append(scaled_train_data[i, 0])  # Current day\n", "X_train, y_train = np.array(X_train), np.array(y_train)\n", "\n", "# Reshaping for LSTM\n", "X_train = X_train.reshape(X_train.shape[0], X_train.shape[1], 1)\n", "\n", "# Step 2: Build and Compile LSTM Model\n", "model = Sequential()\n", "model.add(LSTM(50, return_sequences=True, input_shape=(X_train.shape[1], 1)))\n", "model.add(Dropout(0.2))\n", "model.add(LSTM(50, return_sequences=False))\n", "model.add(Dropout(0.2))\n", "model.add(<PERSON><PERSON>(1))\n", "model.compile(optimizer='adam', loss='mean_squared_error')\n", "\n", "# Step 3: Train the Model on the training data\n", "model.fit(X_train, y_train, epochs=25, batch_size=32, verbose=1)\n", "\n", "# Step 4: Preparing the test_data for making predictions\n", "\n", "# Creating the test data sequences (just like we did for train_data)\n", "X_test = []\n", "for i in range(60, len(scaled_test_data)):\n", "    X_test.append(scaled_test_data[i-60:i, 0])  # Previous 60 days\n", "X_test = np.array(X_test)\n", "\n", "# Reshaping for LSTM\n", "X_test = X_test.reshape(X_test.shape[0], X_test.shape[1], 1)\n", "\n", "# Step 5: Make Predictions on test_data\n", "lstm_predictions = model.predict(X_test)\n", "\n", "# Inverse scaling to get actual values for predictions\n", "lstm_predictions = scaler.inverse_transform(lstm_predictions)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"id": "9FOCNVX6Gok_", "colab": {"base_uri": "https://localhost:8080/", "height": 359}, "outputId": "5c5639fa-260d-4fb6-8768-b9720d3b3eaa"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["           date     close  arima_predictions  sarimax_predictions  \\\n", "1450 2023-12-15  1.099360             1.1092             1.079308   \n", "757  2022-01-21  1.131375             1.1092             1.093533   \n", "937  2022-07-20  1.023133             1.1092             1.089840   \n", "595  2021-08-12  1.174190             1.1092             1.097344   \n", "754  2022-01-18  1.141057             1.1092             1.093588   \n", "196  2020-07-09  1.133915             1.1092             1.106155   \n", "164  2020-06-07  1.133787             1.1092             1.106427   \n", "623  2021-09-09  1.181910             1.1092             1.096303   \n", "397  2021-01-26  1.214624             1.1092             1.100919   \n", "1412 2023-11-07  1.072156             1.1092             1.080822   \n", "\n", "      exp_smooth_predictions  lstm_predictions  \n", "1450                1.084464          1.083688  \n", "757                 1.096215          1.140905  \n", "937                 1.093168          1.011417  \n", "595                 1.099435          1.177916  \n", "754                 1.096244          1.147120  \n", "196                 1.106816          1.129479  \n", "164                 1.106955          1.129300  \n", "623                 1.098480          1.188914  \n", "397                 1.102308          1.217295  \n", "1412                1.085831          1.067676  "], "text/html": ["\n", "  <div id=\"df-2e3afea9-cb26-4a9c-9388-73b78c7cfc01\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>close</th>\n", "      <th>arima_predictions</th>\n", "      <th>sarimax_predictions</th>\n", "      <th>exp_smooth_predictions</th>\n", "      <th>lstm_predictions</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1450</th>\n", "      <td>2023-12-15</td>\n", "      <td>1.099360</td>\n", "      <td>1.1092</td>\n", "      <td>1.079308</td>\n", "      <td>1.084464</td>\n", "      <td>1.083688</td>\n", "    </tr>\n", "    <tr>\n", "      <th>757</th>\n", "      <td>2022-01-21</td>\n", "      <td>1.131375</td>\n", "      <td>1.1092</td>\n", "      <td>1.093533</td>\n", "      <td>1.096215</td>\n", "      <td>1.140905</td>\n", "    </tr>\n", "    <tr>\n", "      <th>937</th>\n", "      <td>2022-07-20</td>\n", "      <td>1.023133</td>\n", "      <td>1.1092</td>\n", "      <td>1.089840</td>\n", "      <td>1.093168</td>\n", "      <td>1.011417</td>\n", "    </tr>\n", "    <tr>\n", "      <th>595</th>\n", "      <td>2021-08-12</td>\n", "      <td>1.174190</td>\n", "      <td>1.1092</td>\n", "      <td>1.097344</td>\n", "      <td>1.099435</td>\n", "      <td>1.177916</td>\n", "    </tr>\n", "    <tr>\n", "      <th>754</th>\n", "      <td>2022-01-18</td>\n", "      <td>1.141057</td>\n", "      <td>1.1092</td>\n", "      <td>1.093588</td>\n", "      <td>1.096244</td>\n", "      <td>1.147120</td>\n", "    </tr>\n", "    <tr>\n", "      <th>196</th>\n", "      <td>2020-07-09</td>\n", "      <td>1.133915</td>\n", "      <td>1.1092</td>\n", "      <td>1.106155</td>\n", "      <td>1.106816</td>\n", "      <td>1.129479</td>\n", "    </tr>\n", "    <tr>\n", "      <th>164</th>\n", "      <td>2020-06-07</td>\n", "      <td>1.133787</td>\n", "      <td>1.1092</td>\n", "      <td>1.106427</td>\n", "      <td>1.106955</td>\n", "      <td>1.129300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>623</th>\n", "      <td>2021-09-09</td>\n", "      <td>1.181910</td>\n", "      <td>1.1092</td>\n", "      <td>1.096303</td>\n", "      <td>1.098480</td>\n", "      <td>1.188914</td>\n", "    </tr>\n", "    <tr>\n", "      <th>397</th>\n", "      <td>2021-01-26</td>\n", "      <td>1.214624</td>\n", "      <td>1.1092</td>\n", "      <td>1.100919</td>\n", "      <td>1.102308</td>\n", "      <td>1.217295</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1412</th>\n", "      <td>2023-11-07</td>\n", "      <td>1.072156</td>\n", "      <td>1.1092</td>\n", "      <td>1.080822</td>\n", "      <td>1.085831</td>\n", "      <td>1.067676</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-2e3afea9-cb26-4a9c-9388-73b78c7cfc01')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-2e3afea9-cb26-4a9c-9388-73b78c7cfc01 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-2e3afea9-cb26-4a9c-9388-73b78c7cfc01');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-086a5a66-7d4f-466b-8996-b3086cd88cbd\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-086a5a66-7d4f-466b-8996-b3086cd88cbd')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-086a5a66-7d4f-466b-8996-b3086cd88cbd button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"comparison_df\",\n  \"rows\": 10,\n  \"fields\": [\n    {\n      \"column\": \"date\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": \"2020-06-07 00:00:00\",\n        \"max\": \"2023-12-15 00:00:00\",\n        \"num_unique_values\": 10,\n        \"samples\": [\n          \"2021-01-26 00:00:00\",\n          \"2022-01-21 00:00:00\",\n          \"2020-07-09 00:00:00\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"close\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.05544236433756142,\n        \"min\": 1.0231330394744873,\n        \"max\": 1.214624047279358,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          1.214624047279358,\n          1.1313753128051758,\n          1.1339154243469238\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"arima_predictions\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2.340555645717801e-16,\n        \"min\": 1.1091997048213134,\n        \"max\": 1.1091997048213134,\n        \"num_unique_values\": 1,\n        \"samples\": [\n          1.1091997048213134\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"sarimax_predictions\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.009261216617393972,\n        \"min\": 1.0793080045524464,\n        \"max\": 1.1064268259760237,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          1.1009185352149946\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"exp_smooth_predictions\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.007670500667673846,\n        \"min\": 1.0844637874691918,\n        \"max\": 1.1069549941376897,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          1.1023081015287337\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"lstm_predictions\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.06136141597090024,\n        \"min\": 1.0114173889160156,\n        \"max\": 1.2172954082489014,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          1.2172954082489014\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 26}], "source": ["comparison_df = test_data.reset_index()\n", "comparison_df['arima_predictions'] = arima_predictions.reset_index(drop=True)\n", "comparison_df['sarimax_predictions'] = sarimax_predictions.reset_index(drop=True)\n", "comparison_df['exp_smooth_predictions'] = exp_smooth_predictions.reset_index(drop=True)\n", "comparison_df['lstm_predictions'] = np.nan\n", "comparison_df.loc[60:, 'lstm_predictions'] = lstm_predictions.flatten()\n", "comparison_df.sample(10)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"id": "HjOz3gJ7Rnjz", "colab": {"base_uri": "https://localhost:8080/", "height": 641}, "outputId": "f0878893-4ca1-497d-ea41-07868ac4aca2"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1400x700 with 1 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAABI0AAAJwCAYAAAAEFJHJAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdd3hT1RvA8e9N0nRPKLRllVUoQ5agCMoQBAVlKYgCRZYiiKg4cDFcP1EciJsKqICgDFGRKQgCIqjsDS1QKFAo3TPJ+f1xm7ShgxYKLfh+nidPkntvzj33Ns148573aEophRBCCCGEEEIIIYQQeRjKugNCCCGEEEIIIYQQovyRoJEQQgghhBBCCCGEyEeCRkIIIYQQQgghhBAiHwkaCSGEEEIIIYQQQoh8JGgkhBBCCCGEEEIIIfKRoJEQQgghhBBCCCGEyEeCRkIIIYQQQgghhBAiHwkaCSGEEEIIIYQQQoh8JGgkhBBCCCGEEEIIIfKRoJEQQgghLoumaUycOLGsu1Hm2rdvT/v27R33o6Oj0TSNWbNmlVmfLnZxH0vDrFmz0DSN6OjoUm23vBg8eDChoaFl3Q0hhBCiTEnQSAghhCgHPvnkEzRN45ZbbrnsNk6dOsXEiRPZvn176XWsnFu3bh2apjkuLi4u1KpVi0GDBnH06NGy7l6JbNq0iYkTJ5KQkFCm/bBarcyc<PERSON>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**********************************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\n"}, "metadata": {}}], "source": ["# Plotting the actual vs predicted prices\n", "plt.figure(figsize=(14, 7))\n", "plt.plot(comparison_df['date'], comparison_df['close'], label='Actual Close Price', color='blue')\n", "plt.plot(comparison_df['date'], comparison_df['arima_predictions'], label='ARIMA Predictions', color='orange')\n", "plt.plot(comparison_df['date'], comparison_df['sarimax_predictions'], label='SARIMAX Predictions', color='green')\n", "plt.plot(comparison_df['date'], comparison_df['exp_smooth_predictions'], label='Exponential Smoothing Predictions', color='red')\n", "plt.plot(comparison_df['date'], comparison_df['lstm_predictions'], label='Long Short Term Memomy Predictions', color='violet')\n", "plt.title('Actual vs Predicted Close Prices')\n", "plt.xlabel('Date')\n", "plt.ylabel('Close Price')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"id": "eL-VzPZ2SYYL", "colab": {"base_uri": "https://localhost:8080/", "height": 774}, "outputId": "43ca34fe-6b04-47c5-fffe-2118b105791a"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x600 with 2 Axes>"], "image/png": "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***************************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\n"}, "metadata": {}}], "source": ["# Calculate errors for each prediction method\n", "metrics = {}\n", "\n", "# Define a function to calculate metrics\n", "def calculate_metrics(actual, predicted):\n", "    mae = mean_absolute_error(actual, predicted)\n", "    mse = mean_squared_error(actual, predicted)\n", "    rmse = mse ** 0.5\n", "    mape = np.mean(np.abs((actual - predicted) / actual)) * 100\n", "    return mae, mse, rmse, mape\n", "\n", "# Get actual values\n", "actual_values = comparison_df['close'].values\n", "\n", "# Dropping rows where any of the predictions are NaN for cleaning\n", "comparison_df_clean = comparison_df.dropna(subset=['arima_predictions', 'sarimax_predictions', 'exp_smooth_predictions', 'lstm_predictions'])\n", "\n", "# Get the cleaned actual and predicted values\n", "actual_values_clean = comparison_df_clean['close'].values\n", "arima_predictions_clean = comparison_df_clean['arima_predictions'].values\n", "sarimax_predictions_clean = comparison_df_clean['sarimax_predictions'].values\n", "exp_smooth_predictions_clean = comparison_df_clean['exp_smooth_predictions'].values\n", "lstm_predictions_clean = comparison_df_clean['lstm_predictions'].values\n", "\n", "# Calculate metrics for each prediction method\n", "metrics['ARIMA'] = calculate_metrics(actual_values_clean, arima_predictions_clean)\n", "metrics['SARIMAX'] = calculate_metrics(actual_values_clean, sarimax_predictions_clean)\n", "metrics['Exponential Smoothing'] = calculate_metrics(actual_values_clean, exp_smooth_predictions_clean)\n", "metrics['LSTM'] = calculate_metrics(actual_values_clean, lstm_predictions_clean)\n", "\n", "# Create a summary DataFrame\n", "metrics_df = pd.DataFrame(metrics, index=['MAE', 'MSE', 'RMSE', 'MAPE']).T\n", "metrics_df.columns = ['Mean Absolute Error', 'Mean Squared Error', 'Root Mean Squared Error', 'Mean Absolute Percentage Error']\n", "\n", "plt.figure(figsize=(10, 6))\n", "\n", "# Create a heatmap\n", "sns.heatmap(metrics_df, annot=True, fmt='.6f', linewidths=0.1, vmax=1.0, vmin=-1.0, cbar=True, cmap=plt.cm.RdBu_r, linecolor='white')\n", "\n", "# Adding titles and labels\n", "plt.title('Model Performance Comparison')\n", "plt.xlabel('Metrics')\n", "plt.ylabel('Models')\n", "\n", "# Show the plot\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "tbMTGR1Xcbx-"}, "source": ["# **Hyperparameter Tuning of SARIMAX**\n", "\n", "In this section, we conduct hyperparameter tuning for the SARIMAX model to find the optimal combination of parameters that minimizes the Akaike Information Criterion (AIC). The AIC is a measure of the goodness of fit of a statistical model, and lower values indicate a better fit.\n", "\n", "\n", "##### **Note: Hyperparameter tuning step consumes a lot of time(more than a hour), Beneath provided is just sample code for usage if you have enough resourse and time then only try it after uncommenting.**\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"id": "nN_ykvtIMS61"}, "outputs": [], "source": ["# # Define the p, d, q parameters to take any value between 0 and 2\n", "#import itertools\n", "# p = d = q = range(0, 3)\n", "\n", "# # Define the seasonal parameters (P, D, Q, s)\n", "# P = D = Q = range(0, 2)\n", "# seasonal_period = [7, 14, 21]  # Seasonal period, e.g., 12 for monthly data\n", "\n", "# # Create a list of all possible combinations of p, d, q for non-seasonal and seasonal terms\n", "# pdq = list(itertools.product(p, d, q))\n", "# seasonal_pdq = list(itertools.product(P, D, Q, seasonal_period))\n", "\n", "# # Search for the best combination of parameters\n", "# best_aic = np.inf\n", "# best_pdq = None\n", "# best_seasonal_pdq = None\n", "# best_model = None\n", "\n", "# for param in pdq:\n", "#     for seasonal_param in seasonal_pdq:\n", "#         try:\n", "#             # Fit the SARIMAX model with the given parameters\n", "#             model = SARIMAX(train_data,\n", "#                             order=param,\n", "#                             seasonal_order=seasonal_param,\n", "#                             enforce_stationarity=False,\n", "#                             enforce_invertibility=False)\n", "#             results = model.fit(disp=False)\n", "\n", "#             # Keep track of the best model based on AIC\n", "#             if results.aic < best_aic:\n", "#                 best_aic = results.aic\n", "#                 best_pdq = param\n", "#                 best_seasonal_pdq = seasonal_param\n", "#                 best_model = results\n", "\n", "#         except Exception as e:\n", "#             continue\n", "\n", "# print(f\"Best SARIMAX model: ARIMA{best_pdq} x {best_seasonal_pdq}12 - AIC: {best_aic}\")"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"id": "_CDpgbu_UWMe"}, "outputs": [], "source": ["# sarima_predictions = best_model.forecast(steps=len(test_data))\n", "\n", "# # Compute the RMSE\n", "# rmse = np.sqrt(mean_squared_error(test_data, sarima_predictions))\n", "# print(f\"SARIMAX Test RMSE: {rmse}\")"]}], "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 0}