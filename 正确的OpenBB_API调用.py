#!/usr/bin/env python3
"""
正确的OpenBB API调用方法
基于测试结果，使用可用的API端点
"""

import requests
import json
import pandas as pd
from datetime import datetime, timedelta


class OpenBBAPIClient:
    """OpenBB API客户端 - 使用经过验证的端点"""
    
    def __init__(self, base_url="http://127.0.0.1:6900"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
    
    def get_stock_quote(self, symbols):
        """
        获取股票实时报价 ✅ 已验证可用
        
        Args:
            symbols: 股票代码，可以是字符串或列表
        """
        if isinstance(symbols, list):
            symbol_str = ",".join(symbols)
        else:
            symbol_str = symbols
        
        try:
            response = self.session.get(
                f"{self.base_url}/api/v1/equity/price/quote",
                params={"symbol": symbol_str, "provider": "yfinance"},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 成功获取 {symbol_str} 报价")
                return data['results'] if 'results' in data else None
            else:
                print(f"❌ 获取报价失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None
    
    def get_income_statement(self, symbol, period="annual", limit=5):
        """
        获取收入表 ✅ 已验证可用
        
        Args:
            symbol: 股票代码
            period: "annual" 或 "quarter"
            limit: 返回的年份/季度数量
        """
        try:
            response = self.session.get(
                f"{self.base_url}/api/v1/equity/fundamental/income",
                params={
                    "symbol": symbol,
                    "provider": "yfinance",
                    "period": period,
                    "limit": limit
                },
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 成功获取 {symbol} 收入表")
                return data['results'] if 'results' in data else None
            else:
                print(f"❌ 获取收入表失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None
    
    def get_balance_sheet(self, symbol, period="annual", limit=5):
        """
        获取资产负债表 ✅ 已验证可用
        
        Args:
            symbol: 股票代码
            period: "annual" 或 "quarter"
            limit: 返回的年份/季度数量
        """
        try:
            response = self.session.get(
                f"{self.base_url}/api/v1/equity/fundamental/balance",
                params={
                    "symbol": symbol,
                    "provider": "yfinance",
                    "period": period,
                    "limit": limit
                },
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 成功获取 {symbol} 资产负债表")
                return data['results'] if 'results' in data else None
            else:
                print(f"❌ 获取资产负债表失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None
    
    def get_cash_flow(self, symbol, period="annual", limit=5):
        """
        获取现金流量表 ✅ 已验证可用
        
        Args:
            symbol: 股票代码
            period: "annual" 或 "quarter"
            limit: 返回的年份/季度数量
        """
        try:
            response = self.session.get(
                f"{self.base_url}/api/v1/equity/fundamental/cash",
                params={
                    "symbol": symbol,
                    "provider": "yfinance",
                    "period": period,
                    "limit": limit
                },
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 成功获取 {symbol} 现金流量表")
                return data['results'] if 'results' in data else None
            else:
                print(f"❌ 获取现金流量表失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None
    
    def get_financial_summary(self, symbol):
        """
        获取完整的财务摘要
        
        Args:
            symbol: 股票代码
        """
        print(f"\n📊 获取 {symbol} 完整财务数据")
        print("=" * 50)
        
        # 获取实时报价
        quote = self.get_stock_quote(symbol)
        
        # 获取财务报表
        income = self.get_income_statement(symbol, "annual", 3)
        balance = self.get_balance_sheet(symbol, "annual", 3)
        cash_flow = self.get_cash_flow(symbol, "annual", 3)
        
        return {
            'quote': quote,
            'income_statement': income,
            'balance_sheet': balance,
            'cash_flow': cash_flow
        }
    
    def analyze_financial_data(self, symbol):
        """
        分析财务数据并生成报告
        
        Args:
            symbol: 股票代码
        """
        data = self.get_financial_summary(symbol)
        
        print(f"\n📈 {symbol} 财务分析报告")
        print("=" * 50)
        
        # 分析实时报价
        if data['quote']:
            quote = data['quote'][0] if isinstance(data['quote'], list) else data['quote']
            print(f"💰 当前股价: ${quote.get('price', 'N/A')}")
            print(f"📊 市值: ${quote.get('market_cap', 'N/A'):,}" if quote.get('market_cap') else "📊 市值: N/A")
            print(f"📈 52周最高: ${quote.get('fifty_two_week_high', 'N/A')}" if quote.get('fifty_two_week_high') else "")
            print(f"📉 52周最低: ${quote.get('fifty_two_week_low', 'N/A')}" if quote.get('fifty_two_week_low') else "")
        
        # 分析收入表
        if data['income_statement']:
            print(f"\n💼 收入表分析")
            income_data = data['income_statement']
            if isinstance(income_data, list) and income_data:
                latest = income_data[0]
                print(f"📊 营业收入: ${latest.get('revenue', 'N/A'):,}" if latest.get('revenue') else "📊 营业收入: N/A")
                print(f"💰 净利润: ${latest.get('net_income', 'N/A'):,}" if latest.get('net_income') else "💰 净利润: N/A")
                print(f"📈 每股收益: ${latest.get('basic_eps', 'N/A')}" if latest.get('basic_eps') else "📈 每股收益: N/A")
        
        # 分析资产负债表
        if data['balance_sheet']:
            print(f"\n🏦 资产负债表分析")
            balance_data = data['balance_sheet']
            if isinstance(balance_data, list) and balance_data:
                latest = balance_data[0]
                print(f"💎 总资产: ${latest.get('total_assets', 'N/A'):,}" if latest.get('total_assets') else "💎 总资产: N/A")
                print(f"💰 股东权益: ${latest.get('total_equity', 'N/A'):,}" if latest.get('total_equity') else "💰 股东权益: N/A")
                print(f"💳 总负债: ${latest.get('total_debt', 'N/A'):,}" if latest.get('total_debt') else "💳 总负债: N/A")
        
        # 分析现金流量表
        if data['cash_flow']:
            print(f"\n💸 现金流量表分析")
            cash_data = data['cash_flow']
            if isinstance(cash_data, list) and cash_data:
                latest = cash_data[0]
                print(f"💰 经营现金流: ${latest.get('net_cash_flow_from_operating_activities', 'N/A'):,}" if latest.get('net_cash_flow_from_operating_activities') else "💰 经营现金流: N/A")
                print(f"🏗️ 投资现金流: ${latest.get('net_cash_flow_from_investing_activities', 'N/A'):,}" if latest.get('net_cash_flow_from_investing_activities') else "🏗️ 投资现金流: N/A")
                print(f"💳 融资现金流: ${latest.get('net_cash_flow_from_financing_activities', 'N/A'):,}" if latest.get('net_cash_flow_from_financing_activities') else "💳 融资现金流: N/A")
        
        return data


def main():
    """演示正确的API调用"""
    print("🚀 OpenBB API 正确调用演示")
    print("=" * 60)
    
    client = OpenBBAPIClient()
    
    # 1. 获取多个股票报价
    print("\n📊 获取股票报价")
    print("-" * 30)
    quotes = client.get_stock_quote(["AAPL", "MSFT", "GOOGL"])
    
    if quotes:
        for quote in quotes:
            symbol = quote.get('symbol', 'N/A')
            price = quote.get('price', 'N/A')
            change = quote.get('change', 'N/A')
            print(f"{symbol}: ${price} ({change:+.2f})" if isinstance(change, (int, float)) else f"{symbol}: ${price}")
    
    # 2. 分析苹果公司财务数据
    print("\n" + "=" * 60)
    apple_data = client.analyze_financial_data("AAPL")
    
    # 3. 分析微软公司财务数据
    print("\n" + "=" * 60)
    microsoft_data = client.analyze_financial_data("MSFT")
    
    print("\n🎉 API调用演示完成!")
    print("\n💡 可用的API功能:")
    print("✅ 股票实时报价 - 支持单个和多个股票")
    print("✅ 财务报表 - 收入表、资产负债表、现金流量表")
    print("✅ 年度和季度数据 - 可指定period参数")
    print("✅ 历史数据限制 - 可指定limit参数")
    
    print("\n⚠️ 不可用的功能:")
    print("❌ 股票历史价格数据 (返回204)")
    print("❌ 新闻数据 (SSL证书问题)")
    print("❌ 经济数据 (端点不存在)")
    print("❌ 加密货币数据 (端点不存在)")


if __name__ == "__main__":
    main()
