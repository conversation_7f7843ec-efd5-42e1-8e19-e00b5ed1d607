@echo off
echo 🐍 Python版本检查工具
echo =====================

echo.
echo 📋 检查已安装的Python版本:
echo.

REM 检查py启动器
echo 🔍 检查py启动器:
py -0 2>nul
if %errorlevel% equ 0 (
    echo ✅ py启动器可用
    echo.
    echo 📊 可用的Python版本:
    py -0
) else (
    echo ❌ py启动器不可用
)

echo.
echo ================================

REM 检查直接python命令
echo 🔍 检查python命令:
python --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ python命令可用
    python --version
    python -c "import sys; print('路径:', sys.executable)"
) else (
    echo ❌ python命令不可用
)

echo.
echo ================================

REM 检查python3命令
echo 🔍 检查python3命令:
python3 --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ python3命令可用
    python3 --version
) else (
    echo ❌ python3命令不可用
)

echo.
echo ================================

REM 特别检查Python 3.12
echo 🎯 检查Python 3.12:
py -3.12 --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ Python 3.12已安装
    py -3.12 --version
    py -3.12 -c "import sys; print('路径:', sys.executable)"
    echo.
    echo 🚀 可以直接运行OpenBB配置脚本！
    echo    PowerShell: .\setup_python312_openbb.ps1
) else (
    echo ❌ Python 3.12未安装
    echo.
    echo 📥 请安装Python 3.12:
    echo    1. 访问: https://www.python.org/downloads/
    echo    2. 下载Python 3.12.x
    echo    3. 安装时勾选 "Add Python to PATH"
)

echo.
echo ================================
echo 💡 建议:
echo    - 如果有Python 3.12: 运行 .\setup_python312_openbb.ps1
echo    - 如果没有Python 3.12: 先安装Python 3.12
echo    - OpenBB推荐使用Python 3.9-3.12版本
echo.

pause
