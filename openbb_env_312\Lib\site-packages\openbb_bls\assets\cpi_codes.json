{"ap": {"area_code": {"0000": "U.S. city average", "0100": "Northeast", "0110": "New England", "0120": "Middle Atlantic", "0200": "Midwest", "0230": "East North Central", "0240": "West North Central", "0300": "South", "0350": "South Atlantic", "0360": "East South Central", "0370": "West South Central", "0400": "West", "0480": "Mountain", "0490": "Pacific", "A104": "Pittsburgh, PA", "A105": "Buffalo-Niagara Falls, NY", "A106": "Scranton, PA", "A210": "Cleveland-Akron, OH", "A212": "Milwaukee-Racine, WI", "A213": "Cincinnati-Hamilton, OH-KY-IN", "A214": "Kansas City, MO-KS", "A311": "Washington-Baltimore, DC-MD-VA-WV", "A315": "Washington, DC-MD-VA", "A317": "Baltimore, MD", "A421": "Los Angeles-Riverside-Orange County, CA", "A425": "Portland-Salem, OR-WA", "B000": "City size B", "B100": "Northeast size B", "B200": "North Central size B", "B300": "South size B", "B400": "West size B", "C000": "City size C", "C100": "Northeast size C", "C200": "North Central size C", "C300": "South size C", "C400": "West size C", "D000": "Size Class D", "D100": "Northeast - Size Class D", "D200": "Midwest - Size Class D", "D300": "South - Size Class D", "D400": "West - Size Class D", "N000": "Size Class B/C", "N100": "Northeast - Size Class B/C", "N200": "Midwest - Size Class B/C", "N300": "South - Size Class B/C", "N400": "West - Size Class B/C", "S000": "Size Class A", "S100": "Northeast - Size Class A", "S11A": "Boston-Cambridge-Newton, MA-NH", "S12A": "New York-Newark-Jersey City, NY-NJ-PA", "S12B": "Philadelphia-Camden-Wilmington, PA-NJ-DE-MD", "S200": "Midwest - Size Class A", "S23A": "Chicago-Naperville-Elgin, IL-IN-WI", "S23B": "Detroit-Warren-Dearborn, MI", "S24A": "Minneapolis-St<PERSON>Paul-Bloomington, MN-WI", "S24B": "St. Louis, MO-IL", "S300": "South - Size Class A", "S35A": "Washington-Arlington-Alexandria, DC-VA-MD-WV", "S35B": "Miami-Fort Lauderdale-West Palm Beach, FL", "S35C": "Atlanta-Sandy Springs-Roswell, GA", "S35D": "Tampa-St. Petersburg-Clearwater, FL", "S35E": "Baltimore-Columbia-Towson, MD", "S37A": "Dallas-Fort Worth-Arlington, TX", "S37B": "Houston-The Woodlands-Sugar Land, TX", "S400": "West - Size Class A", "S48A": "Phoenix-Mesa-Scottsdale, AZ", "S48B": "Denver-Aurora-Lakewood, CO", "S49A": "Los Angeles-Long Beach-Anaheim, CA", "S49B": "San Francisco-Oakland-Hayward, CA", "S49C": "Riverside-San Bernardino-Ontario, CA", "S49D": "Seattle-Tacoma-Bellevue WA", "S49E": "San Diego-Carlsbad, CA", "S49F": "Urban Hawaii", "S49G": "Urban Alaska"}, "item_code": {"701111": "Flour, white, all purpose, per lb. (453.6 gm)", "701311": "Rice, white, long grain, precooked (cost per pound/453.6 grams)", "701312": "Rice, white, long grain, uncooked, per lb. (453.6 gm)", "701321": "Spaghetti (cost per pound/453.6 grams)", "701322": "Spaghetti and macaroni, per lb. (453.6 gm)", "702111": "Bread, white, pan, per lb. (453.6 gm)", "702112": "Bread, French, per lb. (453.6 gm)", "702211": "Bread, rye, pan (cost per pound/453.6 grams)", "702212": "Bread, whole wheat, pan, per lb. (453.6 gm)", "702213": "Bread, wheat blend, pan (cost per pound/453.6 grams)", "702221": "Rolls, hamburger (cost per pound/453.6 grams)", "702411": "Cupcakes, chocolate (cost per pound/453.6 grams)", "702421": "Cookies, chocolate chip, per lb. (453.6 gm)", "702611": "Crackers, soda, salted, per lb. (453.6 gm)", "703111": "Ground chuck, 100% beef, per lb. (453.6 gm)", "703112": "Ground beef, 100% beef, per lb. (453.6 gm)", "703113": "Ground beef, lean and extra lean, per lb. (453.6 gm)", "703211": "Chuck roast, USDA Choice, bone-in, per lb. (453.6 gm)", "703212": "Chuck roast, graded and ungraded, excluding USDA Prime and Choice, per lb. (453.6 gm)", "703213": "Chuck roast, USDA Choice, boneless, per lb. (453.6 gm)", "703311": "Round roast, USDA Choice, boneless, per lb. (453.6 gm)", "703312": "Round roast, graded and ungraded, excluding USDA Prime and Choice, per lb. (453.6 gm)", "703411": "Rib roast, USDA Choice, bone-in, per lb. (453.6 gm)", "703421": "Steak, chuck, U.S. choice, bone-in (cost per pound/453.6 grams)", "703422": "Steak, T-Bone, USDA Choice, bone-in, per lb. (453.6 gm)", "703423": "Steak, porterhouse, U.S. choice, bone-in (cost per pound/453.6 grams)", "703425": "Steak, rib eye, USDA Choice, boneless, per lb. (453.6 gm)", "703431": "Short ribs, any primal source, bone-in, per lb. (453.6 gm)", "703432": "Beef for stew, boneless, per lb. (453.6 gm)", "703511": "Steak, round, USDA Choice, boneless, per lb. (453.6 gm)", "703512": "Steak, round, graded and ungraded, excluding USDA Prime and Choice, per lb. (453.6 gm)", "703611": "Steak, sirloin, USDA Choice, bone-in, per lb. (453.6 gm)", "703612": "Steak, sirloin, graded and ungraded, excluding USDA Prime and Choice, per lb. (453.6 gm)", "703613": "Steak, sirloin, USDA Choice, boneless, per lb. (453.6 gm)", "704111": "Bacon, sliced, per lb. (453.6 gm)", "704211": "Chops, center cut, bone-in, per lb. (453.6 gm)", "704212": "Chops, boneless, per lb. (453.6 gm)", "704311": "Ham, rump or shank half, bone-in, smoked,per lb. (453.6 gm)", "704312": "Ham, boneless, excluding canned, per lb. (453.6 gm)", "704313": "Ham, rump portion, bone-in, smoked (cost per pound/453.6 grams)", "704314": "Ham, shank portion, bone-in, smoked (cost per pound/453.6 grams)", "704321": "Ham, canned, 3 or 5 lbs, per lb. (453.6 gm)", "704411": "Pork shoulder roast, blade boston, bone-in (cost per pound/453.6 grams)", "704412": "Pork sirloin roast, bone-in (cost per pound/453.6 grams)", "704413": "Shoulder picnic, bone-in, smoked, per lb. (453.6 gm)", "704421": "Sausage, fresh, loose, per lb. (453.6 gm)", "705111": "Frankfurters, all meat or all beef, per lb. (453.6 gm)", "705121": "Bologna, all beef or mixed, per lb. (453.6 gm)", "705141": "Beef liver (cost per pound/453.6 grams)", "705142": "Lamb and mutton, bone-in, per lb. (453.6 gm)", "706111": "Chicken, fresh, whole, per lb. (453.6 gm)", "706211": "Chicken breast, bone-in, per lb. (453.6 gm)", "706212": "Chicken legs, bone-in, per lb. (453.6 gm)", "706311": "Turkey, frozen, whole, per lb. (453.6 gm)", "707111": "Tuna, light, chunk, per lb. (453.6 gm)", "708111": "Eggs, grade A, large, per doz.", "708112": "Eggs, grade AA, large, per doz.", "709111": "Milk, fresh, whole, fortified, per 1/2 gal. (1.9 lit)", "709112": "Milk, fresh, whole, fortified, per gal. (3.8 lit)", "709211": "Milk, fresh, skim (cost per one-half gallon/1.9 liters)", "709212": "Milk, fresh, low fat, per 1/2 gal. (1.9 lit)", "709213": "Milk, fresh, low fat, per gal. (3.8 lit)", "710111": "Butter, salted, grade AA, stick, per lb. (453.6 gm)", "710122": "Yogurt, natural, fruit flavored, per 8 oz. (226.8 gm)", "710211": "American processed cheese, per lb. (453.6 gm)", "710212": "Cheddar cheese, natural, per lb. (453.6 gm)", "710411": "Ice cream, prepackaged, bulk, regular, per 1/2 gal. (1.9 lit)", "711111": "Apples, Red Delicious, per lb. (453.6 gm)", "711211": "Bananas, per lb. (453.6 gm)", "711311": "Oranges, Navel, per lb. (453.6 gm)", "711312": "Oranges, Valencia, per lb. (453.6 gm)", "711411": "Grapefruit, per lb. (453.6 gm)", "711412": "Lemons, per lb. (453.6 gm)", "711413": "Pears, Anjou, per lb. (453.6 gm)", "711414": "Peaches, per lb. (453.6 gm)", "711415": "Strawberries, dry pint, per 12 oz. (340.2 gm)", "711416": "Grapes, Emperor or <PERSON>kay (cost per pound/453.6 grams)", "711417": "Grapes, <PERSON> Seedless, per lb. (453.6 gm)", "711418": "Cherries, per lb. (453.6 gm)", "712111": "Potatoes, white (cost per pound/453.6 grams)", "712112": "Potatoes, white, per lb. (453.6 gm)", "712211": "Lettuce, iceberg, per lb. (453.6 gm)", "712311": "Tomatoes, field grown, per lb. (453.6 gm)", "712401": "Cabbage, per lb. (453.6 gm)", "712402": "Celery, per lb. (453.6 gm)", "712403": "Carrots, short trimmed and topped, per lb. (453.6 gm)", "712404": "Onions, dry yellow, per lb. (453.6 gm)", "712405": "Onions, green scallions (cost per pound/453.6 grams)", "712406": "Peppers, sweet, per lb. (453.6 gm)", "712407": "Corn on the cob, per lb. (453.6 gm)", "712408": "Radishes (cost per pound/453.6 grams)", "712409": "Cucumbers, per lb. (453.6 gm)", "712410": "Beans, green, snap (cost per pound/453.6 grams)", "712411": "Mushrooms (cost per pound/453.6 grams)", "712412": "<PERSON><PERSON><PERSON><PERSON>, per lb. (453.6 gm)", "713111": "Orange juice, frozen concentrate, 12 oz. can, per 16 oz. (473.2 ml)", "713311": "Apple Sauce, any variety, all sizes, per lb. (453.6 gm)", "713312": "Peaches, any variety, all sizes, per lb. (453.6 gm)", "714111": "Potatoes, frozen, French fried, per lb. (453.6 gm)", "714221": "Corn, canned, any style, all sizes, per lb. (453.6 gm)", "714231": "Tomatoes, canned, whole, per lb. (453.6 gm)", "714232": "Tomatoes, canned, any type, all sizes,  per lb. (453.6 gm)", "714233": "Beans, dried, any type, all sizes, per lb. (453.6 gm)", "715111": "Hard candy, solid (cost per pound/453.6 grams)", "715211": "Sugar, white, all sizes, per lb. (453.6 gm)", "715212": "Sugar, white, 33-80 oz. pkg, per lb. (453.6 gm)", "715311": "Jelly (cost per pound/453.6 grams)", "716111": "Margarine, vegetable oil blends, stick (cost per pound/453.6 grams)", "716113": "Margarine, vegetable oil blends, soft, tubs (cost per pound/453.6 grams)", "716114": "Margarine, stick, per lb. (453.6 gm)", "716116": "Margarine, soft, tubs, per lb. (453.6 gm)", "716121": "Shortening, vegetable oil blends, per lb. (453.6 gm)", "716141": "Peanut butter, creamy, all sizes, per lb. (453.6 gm)", "717111": "Cola, non-diet, return bottles, 6 or 8 pack (cost per 16 ounces/473.2 ml)", "717112": "Cola, non diet, return bottles, 24-40 ounce (cost per 16 ounces/473.2 ml)", "717113": "Cola, nondiet, cans, 72 oz. 6 pk., per 16 oz. (473.2 ml)", "717114": "Cola, nondiet, per 2 liters (67.6 oz)", "717311": "Coffee, 100%, ground roast, all sizes, per lb. (453.6 gm)", "717312": "Coffee, 100%, ground roast, 13.1-20 oz. can, per lb. (453.6 gm)", "717324": "Coffee, instant, plain, regular, 6.1-14 ounce (cost per 16 ounces/453.6 grams)", "717325": "Coffee, freeze dried, regular, all sizes (cost per 16 ounces/453.6 grams)", "717326": "Coffee, freeze dried, decaf., all sizes (cost per 16 ounces/453.6 grams)", "717327": "Coffee, instant, plain, regular, all sizes, per lb. (453.6 gm)", "717411": "Coffee, instant, plain, 9.1-14 ounce (cost per 16 ounces/453.6 grams)", "717412": "Coffee, instant, plain, 3.1-6 ounce (cost per 16 ounces/453.6 grams)", "717413": "Coffee, freeze dried, plain, 3.1-9 ounce (cost per 16 ounces/453.6 grams)", "718311": "Potato chips, per 16 oz.", "718631": "Pork and beans, canned (cost per 16 ounces/453.6 grams)", "720111": "Malt beverages, all types, all sizes, any origin, per 16 oz. (473.2 ml)", "720211": "Bourbon whiskey, 375 ml-1.75 liter (cost per 25.4 ounces/750 ml)", "720221": "Vodka, domestic, 375 ml-1.75 liter (cost per 25.4 ounces/750 ml)", "720222": "Vodka, all types, all sizes, any origin, per 1 liter (33.8 oz)", "720311": "Wine, red and white table, all sizes, any origin, per 1 liter (33.8 oz)", "72511": "Fuel oil #2 per gallon (3.785 liters)", "72601": "Utility (piped) gas - 40 therms", "72610": "Electricity per KWH", "72611": "Utility (piped) gas - 100 therms", "72620": "Utility (piped) gas per therm", "72621": "Electricity per 500 KWH", "74712": "Gasoline, leaded regular (cost per gallon/3.8 liters)", "74713": "Gasoline, leaded premium (cost per gallon/3.8 liters)", "74714": "Gasoline, unleaded regular, per gallon/3.785 liters", "74715": "Gasoline, unleaded midgrade, per gallon/3.785 liters", "74716": "Gasoline, unleaded premium, per gallon/3.785 liters", "74717": "Automotive diesel fuel, per gallon/3.785 liters", "7471A": "Gasoline, all types, per gallon/3.785 liters", "FC1101": "All uncooked ground beef, per lb. (453.6 gm)", "FC2101": "All Uncooked Beef Roasts, per lb. (453.6 gm)", "FC3101": "All Uncooked Beef Steaks, per lb. (453.6 gm)", "FC4101": "All Uncooked Other Beef (Excluding Veal), per lb. (453.6 gm)", "FD2101": "All Ham (Excluding Canned Ham and Luncheon Slices), per lb. (453.6 gm)", "FD3101": "All Pork Chops, per lb. (453.6 gm)", "FD4101": "All Other Pork (Excluding Canned Ham and Luncheon Slices), per lb. (453.6 gm)", "FF1101": "Chicken breast, boneless, per lb. (453.6 gm)", "FJ1101": "Milk, fresh, low-fat, reduced fat, skim, per gal. (3.8 lit)", "FJ4101": "Yogurt, per 8 oz. (226.8 gm)", "FL2101": "Lettuce, romaine, per lb. (453.6 gm)", "FN1101": "All soft drinks, per 2 liters (67.6 oz)", "FN1102": "All soft drinks, 12 pk, 12 oz., cans, per 12 oz. (354.9 ml)", "FS1101": "Butter, stick, per lb. (453.6 gm)"}}, "cu": {"area_code": {"0000": "U.S. city average", "0100": "Northeast", "0110": "New England", "0120": "Middle Atlantic", "0200": "Midwest", "0230": "East North Central", "0240": "West North Central", "0300": "South", "0350": "South Atlantic", "0360": "East South Central", "0370": "West South Central", "0400": "West", "0480": "Mountain", "0490": "Pacific", "A104": "Pittsburgh, PA", "A210": "Cleveland-Akron, OH", "A212": "Milwaukee-Racine, WI", "A213": "Cincinnati-Hamilton, OH-KY-IN", "A214": "Kansas City, MO-KS", "A311": "Washington-Baltimore, DC-MD-VA-WV", "A421": "Los Angeles-Riverside-Orange County, CA", "A425": "Portland-Salem, OR-WA", "D000": "Size Class D", "D200": "Midwest - Size Class D", "D300": "South - Size Class D", "N000": "Size Class B/C", "N100": "Northeast - Size Class B/C", "N200": "Midwest - Size Class B/C", "N300": "South - Size Class B/C", "N400": "West - Size Class B/C", "S000": "Size Class A", "S100": "Northeast - Size Class A", "S11A": "Boston-Cambridge-Newton, MA-NH", "S12A": "New York-Newark-Jersey City, NY-NJ-PA", "S12B": "Philadelphia-Camden-Wilmington, PA-NJ-DE-MD", "S200": "Midwest - Size Class A", "S23A": "Chicago-Naperville-Elgin, IL-IN-WI", "S23B": "Detroit-Warren-Dearborn, MI", "S24A": "Minneapolis-St<PERSON>Paul-Bloomington, MN-WI", "S24B": "St. Louis, MO-IL", "S300": "South - Size Class A", "S35A": "Washington-Arlington-Alexandria, DC-VA-MD-WV", "S35B": "Miami-Fort Lauderdale-West Palm Beach, FL", "S35C": "Atlanta-Sandy Springs-Roswell, GA", "S35D": "Tampa-St. Petersburg-Clearwater, FL", "S35E": "Baltimore-Columbia-Towson, MD", "S37A": "Dallas-Fort Worth-Arlington, TX", "S37B": "Houston-The Woodlands-Sugar Land, TX", "S400": "West - Size Class A", "S48A": "Phoenix-Mesa-Scottsdale, AZ", "S48B": "Denver-Aurora-Lakewood, CO", "S49A": "Los Angeles-Long Beach-Anaheim, CA", "S49B": "San Francisco-Oakland-Hayward, CA", "S49C": "Riverside-San Bernardino-Ontario, CA", "S49D": "Seattle-Tacoma-Bellevue WA", "S49E": "San Diego-Carlsbad, CA", "S49F": "Urban Hawaii", "S49G": "Urban Alaska"}, "item_code": {"AA0": "All items - old base", "AA0R": "Purchasing power of the consumer dollar - old base", "SA0": "All items", "SA0E": "Energy", "SA0L1": "All items less food", "SA0L12": "All items less food and shelter", "SA0L12E": "All items less food, shelter, and energy", "SA0L12E4": "All items less food, shelter, energy, and used cars and trucks", "SA0L1E": "All items less food and energy", "SA0L2": "All items less shelter", "SA0L5": "All items less medical care", "SA0LE": "All items less energy", "SA0R": "Purchasing power of the consumer dollar", "SA311": "Apparel less footwear", "SAA": "<PERSON><PERSON><PERSON>", "SAA1": "Men's and boys' apparel", "SAA2": "Women's and girls' apparel", "SAC": "Commodities", "SACE": "Energy commodities", "SACL1": "Commodities less food", "SACL11": "Commodities less food and beverages", "SACL1E": "Commodities less food and energy commodities", "SACL1E4": "Commodities less food, energy, and used cars and trucks", "SAD": "Durables", "SAE": "Education and communication", "SAE1": "Education", "SAE2": "Communication", "SAE21": "Information and information processing", "SAEC": "Education and communication commodities", "SAES": "Education and communication services", "SAF": "Food and beverages", "SAF1": "Food", "SAF11": "Food at home", "SAF111": "Cereals and bakery products", "SAF112": "Meats, poultry, fish, and eggs", "SAF1121": "Meats, poultry, and fish", "SAF11211": "Meats", "SAF113": "Fruits and vegetables", "SAF1131": "Fresh fruits and vegetables", "SAF114": "Nonalcoholic beverages and beverage materials", "SAF115": "Other food at home", "SAF116": "Alcoholic beverages", "SAG": "Other goods and services", "SAG1": "Personal care", "SAGC": "Other goods", "SAGS": "Other personal services", "SAH": "Housing", "SAH1": "Shelter", "SAH2": "Fuels and utilities", "SAH21": "Household energy", "SAH3": "Household furnishings and operations", "SAH31": "Household furnishings and supplies", "SAM": "Medical care", "SAM1": "Medical care commodities", "SAM2": "Medical care services", "SAN": "Nondurables", "SAN1D": "Domestically produced farm food", "SANL1": "Nondurables less food", "SANL11": "Nondurables less food and beverages", "SANL113": "Nondurables less food, beverages, and apparel", "SANL13": "Nondurables less food and apparel", "SAR": "Recreation", "SARC": "Recreation commodities", "SARS": "Recreation services", "SAS": "Services", "SAS24": "Utilities and public transportation", "SAS2RS": "Rent of shelter", "SAS367": "Other services", "SAS4": "Transportation services", "SASL2RS": "Services less rent of shelter", "SASL5": "Services less medical care services", "SASLE": "Services less energy services", "SAT": "Transportation", "SAT1": "Private transportation", "SATCLTB": "Transportation commodities less motor fuel", "SEAA": "Men's apparel", "SEAA01": "Men's suits, sport coats, and outerwear", "SEAA02": "Men's underwear, nightwear, swimwear and accessories", "SEAA03": "Men's shirts and sweaters", "SEAA04": "Men's pants and shorts", "SEAB": "Boys' apparel", "SEAC": "Women's apparel", "SEAC01": "Women's outerwear", "SEAC02": "Women's dresses", "SEAC03": "Women's suits and separates", "SEAC04": "Women's underwear, nightwear, swimwear and accessories", "SEAD": "Girls' apparel", "SEAE": "Footwear", "SEAE01": "Men's footwear", "SEAE02": "Boys' and girls' footwear", "SEAE03": "Women's footwear", "SEAF": "Infants' and toddlers' apparel", "SEAG": "Jewelry and watches", "SEAG01": "Watches", "SEAG02": "Jewelry", "SEEA": "Educational books and supplies", "SEEB": "Tuition, other school fees, and childcare", "SEEB01": "College tuition and fees", "SEEB02": "Elementary and high school tuition and fees", "SEEB03": "Day care and preschool", "SEEB04": "Technical and business school tuition and fees", "SEEC": "Postage and delivery services", "SEEC01": "Postage", "SEEC02": "Delivery services", "SEED": "Telephone services", "SEED03": "Wireless telephone services", "SEED04": "Residential telephone services", "SEEE": "Information technology, hardware and services", "SEEE01": "Computers, peripherals, and smart home assistants", "SEEE02": "Computer software and accessories", "SEEE03": "Internet services and electronic information providers", "SEEE04": "Telephone hardware, calculators, and other consumer information items", "SEEEC": "Information technology commodities", "SEFA": "Cereals and cereal products", "SEFA01": "Flour and prepared flour mixes", "SEFA02": "Breakfast cereal", "SEFA03": "Rice, pasta, cornmeal", "SEFB": "Bakery products", "SEFB01": "Bread", "SEFB02": "Fresh biscuits, rolls, muffins", "SEFB03": "Cakes, cupcakes, and cookies", "SEFB04": "Other bakery products", "SEFC": "Beef and veal", "SEFC01": "Uncooked ground beef", "SEFC02": "Uncooked beef roasts", "SEFC03": "Uncooked beef steaks", "SEFC04": "Uncooked other beef and veal", "SEFD": "Pork", "SEFD01": "Bacon, breakfast sausage, and related products", "SEFD02": "Ham", "SEFD03": "Pork chops", "SEFD04": "Other pork including roasts, steaks, and ribs", "SEFE": "Other meats", "SEFF": "Poultry", "SEFF01": "Chicken", "SEFF02": "Other uncooked poultry including turkey", "SEFG": "Fish and seafood", "SEFG01": "Fresh fish and seafood", "SEFG02": "Processed fish and seafood", "SEFH": "Eggs", "SEFJ": "Dairy and related products", "SEFJ01": "Milk", "SEFJ02": "Cheese and related products", "SEFJ03": "Ice cream and related products", "SEFJ04": "Other dairy and related products", "SEFK": "Fresh fruits", "SEFK01": "Apples", "SEFK02": "Banana<PERSON>", "SEFK03": "Citrus fruits", "SEFK04": "Other fresh fruits", "SEFL": "Fresh vegetables", "SEFL01": "Potatoes", "SEFL02": "Lettuce", "SEFL03": "Tomatoes", "SEFL04": "Other fresh vegetables", "SEFM": "Processed fruits and vegetables", "SEFM01": "Canned fruits and vegetables", "SEFM02": "Frozen fruits and vegetables", "SEFM03": "Other processed fruits and vegetables including dried", "SEFN": "Juices and nonalcoholic drinks", "SEFN01": "Carbonated drinks", "SEFN02": "Frozen noncarbonated juices and drinks", "SEFN03": "Nonfrozen noncarbonated juices and drinks", "SEFP": "Beverage materials including coffee and tea", "SEFP01": "Coffee", "SEFP02": "Other beverage materials including tea", "SEFR": "Sugar and sweets", "SEFR01": "Sugar and sugar substitutes", "SEFR02": "Candy and chewing gum", "SEFR03": "Other sweets", "SEFS": "Fats and oils", "SEFS01": "Butter and margarine", "SEFS02": "Salad dressing", "SEFS03": "Other fats and oils including peanut butter", "SEFT": "Other foods", "SEFT01": "Soups", "SEFT02": "Frozen and freeze dried prepared foods", "SEFT03": "Snacks", "SEFT04": "Spices, seasonings, condiments, sauces", "SEFT05": "Baby food and formula", "SEFT06": "Other miscellaneous foods", "SEFV": "Food away from home", "SEFV01": "Full service meals and snacks", "SEFV02": "Limited service meals and snacks", "SEFV03": "Food at employee sites and schools", "SEFV04": "Food from vending machines and mobile vendors", "SEFV05": "Other food away from home", "SEFW": "Alcoholic beverages at home", "SEFW01": "Beer, ale, and other malt beverages at home", "SEFW02": "Distilled spirits at home", "SEFW03": "Wine at home", "SEFX": "Alcoholic beverages away from home", "SEGA": "Tobacco and smoking products", "SEGA01": "Cigarettes", "SEGA02": "Tobacco products other than cigarettes", "SEGB": "Personal care products", "SEGB01": "Hair, dental, shaving, and miscellaneous personal care products", "SEGB02": "Cosmetics, perfume, bath, nail preparations and implements", "SEGC": "Personal care services", "SEGC01": "Haircuts and other personal care services", "SEGD": "Miscellaneous personal services", "SEGD01": "Legal services", "SEGD02": "Funeral expenses", "SEGD03": "Laundry and dry cleaning services", "SEGD04": "Apparel services other than laundry and dry cleaning", "SEGD05": "Financial services", "SEGE": "Miscellaneous personal goods", "SEHA": "Rent of primary residence", "SEHB": "Lodging away from home", "SEHB01": "Housing at school, excluding board", "SEHB02": "Other lodging away from home including hotels and motels", "SEHC": "Owners' equivalent rent of residences", "SEHC01": "Owners' equivalent rent of primary residence", "SEHD": "Tenants' and household insurance", "SEHE": "Fuel oil and other fuels", "SEHE01": "Fuel oil", "SEHE02": "Propane, kerosene, and firewood", "SEHF": "Energy services", "SEHF01": "Electricity", "SEHF02": "Utility (piped) gas service", "SEHG": "Water and sewer and trash collection services", "SEHG01": "Water and sewerage maintenance", "SEHG02": "Garbage and trash collection", "SEHH": "Window and floor coverings and other linens", "SEHH01": "Floor coverings", "SEHH02": "Window coverings", "SEHH03": "Other linens", "SEHJ": "Furniture and bedding", "SEHJ01": "Bedroom furniture", "SEHJ02": "Living room, kitchen, and dining room furniture", "SEHJ03": "Other furniture", "SEHK": "Appliances", "SEHK01": "Major appliances", "SEHK02": "Other appliances", "SEHL": "Other household equipment and furnishings", "SEHL01": "Clocks, lamps, and decorator items", "SEHL02": "Indoor plants and flowers", "SEHL03": "Dishes and flatware", "SEHL04": "Nonelectric cookware and tableware", "SEHM": "Tools, hardware, outdoor equipment and supplies", "SEHM01": "Tools, hardware and supplies", "SEHM02": "Outdoor equipment and supplies", "SEHN": "Housekeeping supplies", "SEHN01": "Household cleaning products", "SEHN02": "Household paper products", "SEHN03": "Miscellaneous household products", "SEHP": "Household operations", "SEHP01": "Domestic services", "SEHP02": "Gardening and lawncare services", "SEHP03": "Moving, storage, freight expense", "SEHP04": "Repair of household items", "SEMC": "Professional services", "SEMC01": "Physicians' services", "SEMC02": "Dental services", "SEMC03": "Eyeglasses and eye care", "SEMC04": "Services by other medical professionals", "SEMD": "Hospital and related services", "SEMD01": "Hospital services", "SEMD02": "Nursing homes and adult day services", "SEMD03": "Care of invalids and elderly at home", "SEME": "Health insurance", "SEMF": "Medicinal drugs", "SEMF01": "Prescription drugs", "SEMF02": "Nonprescription drugs", "SEMG": "Medical equipment and supplies", "SERA": "Video and audio", "SERA01": "Televisions", "SERA02": "Cable, satellite, and live streaming television service", "SERA03": "Other video equipment", "SERA04": "Purchase, subscription, and rental of video", "SERA05": "Audio equipment", "SERA06": "Recorded music and music subscriptions", "SERAC": "Video and audio products", "SERAS": "Video and audio services", "SERB": "Pets, pet products and services", "SERB01": "Pets and pet products", "SERB02": "Pet services including veterinary", "SERC": "Sporting goods", "SERC01": "Sports vehicles including bicycles", "SERC02": "Sports equipment", "SERD": "Photography", "SERD01": "Photographic equipment and supplies", "SERD02": "Photographers and photo processing", "SERE": "Other recreational goods", "SERE01": "Toys", "SERE02": "Sewing machines, fabric and supplies", "SERE03": "Music instruments and accessories", "SERF": "Other recreation services", "SERF01": "Club membership for shopping clubs, fraternal, or other organizations, or participant sports fees", "SERF02": "Admissions", "SERF03": "Fees for lessons or instructions", "SERG": "Recreational reading materials", "SERG01": "Newspapers and magazines", "SERG02": "Recreational books", "SETA": "New and used motor vehicles", "SETA01": "New vehicles", "SETA02": "Used cars and trucks", "SETA03": "Leased cars and trucks", "SETA04": "Car and truck rental", "SETB": "Motor fuel", "SETB01": "Gasoline (all types)", "SETB02": "Other motor fuels", "SETC": "Motor vehicle parts and equipment", "SETC01": "Tires", "SETC02": "Vehicle accessories other than tires", "SETD": "Motor vehicle maintenance and repair", "SETD01": "Motor vehicle body work", "SETD02": "Motor vehicle maintenance and servicing", "SETD03": "Motor vehicle repair", "SETE": "Motor vehicle insurance", "SETF": "Motor vehicle fees", "SETF01": "State motor vehicle registration and license fees", "SETF03": "Parking and other fees", "SETG": "Public transportation", "SETG01": "Airline fares", "SETG02": "Other intercity transportation", "SETG03": "Intracity transportation", "SS01031": "Rice", "SS02011": "White bread", "SS02021": "Bread other than white", "SS02041": "Fresh cakes and cupcakes", "SS02042": "Cookies", "SS02063": "Fresh sweetrolls, coffeecakes, doughnuts", "SS0206A": "Crackers, bread, and cracker products", "SS0206B": "Frozen and refrigerated bakery products, pies, tarts, turnovers", "SS04011": "Bacon and related products", "SS04012": "Breakfast sausage and related products", "SS04031": "Ham, excluding canned", "SS05011": "Frankfurters", "SS05014": "Lamb and organ meats", "SS05015": "Lamb and mutton", "SS0501A": "Lunchmeats", "SS06011": "Fresh whole chicken", "SS06021": "Fresh and frozen chicken parts", "SS07011": "Shelf stable fish and seafood", "SS07021": "Frozen fish and seafood", "SS09011": "Fresh whole milk", "SS09021": "Fresh milk other than whole", "SS10011": "Butter", "SS11031": "Oranges, including tangerines", "SS13031": "Canned fruits", "SS14011": "Frozen vegetables", "SS14021": "Canned vegetables", "SS14022": "Dried beans, peas, and lentils", "SS16011": "Margarine", "SS16014": "Peanut butter", "SS17031": "Roasted coffee", "SS17032": "Instant coffee", "SS18041": "Salt and other seasonings and spices", "SS18042": "Olives, pickles, relishes", "SS18043": "Sauces and gravies", "SS1804B": "Other condiments", "SS18064": "Prepared salads", "SS20021": "Whiskey at home", "SS20022": "Distilled spirits, excluding whiskey, at home", "SS20051": "Beer, ale, and other malt beverages away from home", "SS20052": "Wine away from home", "SS20053": "Distilled spirits away from home", "SS27051": "Land-line interstate toll calls", "SS27061": "Land-line intrastate toll calls", "SS30021": "Laundry equipment", "SS31022": "Video discs and other media", "SS31023": "Video game hardware, software and accessories", "SS33032": "Stationery, stationery supplies, gift wrap", "SS45011": "New cars", "SS4501A": "New cars and trucks", "SS45021": "New trucks", "SS45031": "New motorcycles", "SS47014": "Gasoline, unleaded regular", "SS47015": "Gasoline, unleaded midgrade", "SS47016": "Gasoline, unleaded premium", "SS47021": "Motor oil, coolant, and fluids", "SS48021": "Vehicle parts and equipment other than tires", "SS52051": "Parking fees and tolls", "SS53021": "Intercity bus fare", "SS53022": "Intercity train fare", "SS53023": "Ship fare", "SS53031": "Intracity mass transit", "SS5702": "Inpatient hospital services", "SS5703": "Outpatient hospital services", "SS61011": "Toys, games, hobbies and playground equipment", "SS61021": "Film and photographic supplies", "SS61023": "Photographic equipment", "SS61031": "Pet food", "SS61032": "Purchase of pets, pet supplies, accessories", "SS62011": "Automobile service clubs", "SS62031": "Admission to movies, theaters, and concerts", "SS62032": "Admission to sporting events", "SS62051": "Photographer fees", "SS62052": "Photo Processing", "SS62053": "Pet services", "SS62054": "Veterinarian services", "SS62055": "Subscription and rental of video and video games", "SS68021": "Checking account and other bank services", "SS68023": "Tax return preparation and other accounting fees", "SSEA011": "College textbooks", "SSEE041": "Smartphones", "SSFV031A": "Food at elementary and secondary schools", "SSGE013": "Infants' equipment", "SSHJ031": "Infants' furniture"}, "base_code": {"A": "Alternate", "S": "Current"}}, "cw": {"area_code": {"0000": "U.S. city average", "0100": "Northeast", "0110": "New England", "0120": "Middle Atlantic", "0200": "Midwest", "0230": "East North Central", "0240": "West North Central", "0300": "South", "0350": "South Atlantic", "0360": "East South Central", "0370": "West South Central", "0400": "West", "0480": "Mountain", "0490": "Pacific", "A104": "Pittsburgh, PA", "A210": "Cleveland-Akron, OH", "A212": "Milwaukee-Racine, WI", "A213": "Cincinnati-Hamilton, OH-KY-IN", "A214": "Kansas City, MO-KS", "A311": "Washington-Baltimore, DC-MD-VA-WV", "A421": "Los Angeles-Riverside-Orange County, CA", "A425": "Portland-Salem, OR-WA", "D000": "Size Class D", "D200": "Midwest - Size Class D", "D300": "South - Size Class D", "N000": "Size Class B/C", "N100": "Northeast - Size Class B/C", "N200": "Midwest - Size Class B/C", "N300": "South - Size Class B/C", "N400": "West - Size Class B/C", "S000": "Size Class A", "S100": "Northeast - Size Class A", "S11A": "Boston-Cambridge-Newton, MA-NH", "S12A": "New York-Newark-Jersey City, NY-NJ-PA", "S12B": "Philadelphia-Camden-Wilmington, PA-NJ-DE-MD", "S200": "Midwest - Size Class A", "S23A": "Chicago-Naperville-Elgin, IL-IN-WI", "S23B": "Detroit-Warren-Dearborn, MI", "S24A": "Minneapolis-St<PERSON>Paul-Bloomington, MN-WI", "S24B": "St. Louis, MO-IL", "S300": "South - Size Class A", "S35A": "Washington-Arlington-Alexandria, DC-VA-MD-WV", "S35B": "Miami-Fort Lauderdale-West Palm Beach, FL", "S35C": "Atlanta-Sandy Springs-Roswell, GA", "S35D": "Tampa-St. Petersburg-Clearwater, FL", "S35E": "Baltimore-Columbia-Towson, MD", "S37A": "Dallas-Fort Worth-Arlington, TX", "S37B": "Houston-The Woodlands-Sugar Land, TX", "S400": "West - Size Class A", "S48A": "Phoenix-Mesa-Scottsdale, AZ", "S48B": "Denver-Aurora-Lakewood, CO", "S49A": "Los Angeles-Long Beach-Anaheim, CA", "S49B": "San Francisco-Oakland-Hayward, CA", "S49C": "Riverside-San Bernardino-Ontario, CA", "S49D": "Seattle-Tacoma-Bellevue WA", "S49E": "San Diego-Carlsbad, CA", "S49F": "Urban Hawaii", "S49G": "Urban Alaska"}, "item_code": {"AA0": "All items - old base", "AA0R": "Purchasing power of the consumer dollar - old base", "SA0": "All items", "SA0E": "Energy", "SA0L1": "All items less food", "SA0L1E": "All items less food and energy", "SA0L2": "All items less shelter", "SA0L5": "All items less medical care", "SA0LE": "All items less energy", "SA0R": "Purchasing power of the consumer dollar", "SA311": "Apparel less footwear", "SAA": "<PERSON><PERSON><PERSON>", "SAA1": "Men's and boys' apparel", "SAA2": "Women's and girls' apparel", "SAC": "Commodities", "SACE": "Energy commodities", "SACL1": "Commodities less food", "SACL11": "Commodities less food and beverages", "SACL1E": "Commodities less food and energy commodities", "SAD": "Durables", "SAE": "Education and communication", "SAE1": "Education", "SAE2": "Communication", "SAE21": "Information and information processing", "SAEC": "Education and communication commodities", "SAES": "Education and communication services", "SAF": "Food and beverages", "SAF1": "Food", "SAF11": "Food at home", "SAF111": "Cereals and bakery products", "SAF112": "Meats, poultry, fish, and eggs", "SAF1121": "Meats, poultry, and fish", "SAF11211": "Meats", "SAF113": "Fruits and vegetables", "SAF1131": "Fresh fruits and vegetables", "SAF114": "Nonalcoholic beverages and beverage materials", "SAF115": "Other food at home", "SAF116": "Alcoholic beverages", "SAG": "Other goods and services", "SAG1": "Personal care", "SAGC": "Other goods", "SAGS": "Other personal services", "SAH": "Housing", "SAH1": "Shelter", "SAH2": "Fuels and utilities", "SAH21": "Household energy", "SAH3": "Household furnishings and operations", "SAH31": "Household furnishings and supplies", "SAM": "Medical care", "SAM1": "Medical care commodities", "SAM2": "Medical care services", "SAN": "Nondurables", "SAN1D": "Domestically produced farm food", "SANL1": "Nondurables less food", "SANL11": "Nondurables less food and beverages", "SANL113": "Nondurables less food, beverages, and apparel", "SANL13": "Nondurables less food and apparel", "SAR": "Recreation", "SARC": "Recreation commodities", "SARS": "Recreation services", "SAS": "Services", "SAS24": "Utilities and public transportation", "SAS2RS": "Rent of shelter", "SAS367": "Other services", "SAS4": "Transportation services", "SASL2RS": "Services less rent of shelter", "SASL5": "Services less medical care services", "SASLE": "Services less energy services", "SAT": "Transportation", "SAT1": "Private transportation", "SATCLTB": "Transportation commodities less motor fuel", "SEAA": "Men's apparel", "SEAA01": "Men's suits, sport coats, and outerwear", "SEAA02": "Men's underwear, nightwear, swimwear and accessories", "SEAA03": "Men's shirts and sweaters", "SEAA04": "Men's pants and shorts", "SEAB": "Boys' apparel", "SEAC": "Women's apparel", "SEAC01": "Women's outerwear", "SEAC02": "Women's dresses", "SEAC03": "Women's suits and separates", "SEAC04": "Women's underwear, nightwear, swimwear and accessories", "SEAD": "Girls' apparel", "SEAE": "Footwear", "SEAE01": "Men's footwear", "SEAE02": "Boys' and girls' footwear", "SEAE03": "Women's footwear", "SEAF": "Infants' and toddlers' apparel", "SEAG": "Jewelry and watches", "SEAG01": "Watches", "SEAG02": "Jewelry", "SEEA": "Educational books and supplies", "SEEB": "Tuition, other school fees, and childcare", "SEEB01": "College tuition and fees", "SEEB02": "Elementary and high school tuition and fees", "SEEB03": "Day care and preschool", "SEEB04": "Technical and business school tuition and fees", "SEEC": "Postage and delivery services", "SEEC01": "Postage", "SEEC02": "Delivery services", "SEED": "Telephone services", "SEED03": "Wireless telephone services", "SEED04": "Residential telephone services", "SEEE": "Information technology, hardware and services", "SEEE01": "Computers, peripherals, and smart home assistants", "SEEE02": "Computer software and accessories", "SEEE03": "Internet services and electronic information providers", "SEEE04": "Telephone hardware, calculators, and other consumer information items", "SEEEC": "Information technology commodities", "SEFA": "Cereals and cereal products", "SEFA01": "Flour and prepared flour mixes", "SEFA02": "Breakfast cereal", "SEFA03": "Rice, pasta, cornmeal", "SEFB": "Bakery products", "SEFB01": "Bread", "SEFB02": "Fresh biscuits, rolls, muffins", "SEFB03": "Cakes, cupcakes, and cookies", "SEFB04": "Other bakery products", "SEFC": "Beef and veal", "SEFC01": "Uncooked ground beef", "SEFC02": "Uncooked beef roasts", "SEFC03": "Uncooked beef steaks", "SEFC04": "Uncooked other beef and veal", "SEFD": "Pork", "SEFD01": "Bacon, breakfast sausage, and related products", "SEFD02": "Ham", "SEFD03": "Pork chops", "SEFD04": "Other pork including roasts, steaks, and ribs", "SEFE": "Other meats", "SEFF": "Poultry", "SEFF01": "Chicken", "SEFF02": "Other uncooked poultry including turkey", "SEFG": "Fish and seafood", "SEFG01": "Fresh fish and seafood", "SEFG02": "Processed fish and seafood", "SEFH": "Eggs", "SEFJ": "Dairy and related products", "SEFJ01": "Milk", "SEFJ02": "Cheese and related products", "SEFJ03": "Ice cream and related products", "SEFJ04": "Other dairy and related products", "SEFK": "Fresh fruits", "SEFK01": "Apples", "SEFK02": "Banana<PERSON>", "SEFK03": "Citrus fruits", "SEFK04": "Other fresh fruits", "SEFL": "Fresh vegetables", "SEFL01": "Potatoes", "SEFL02": "Lettuce", "SEFL03": "Tomatoes", "SEFL04": "Other fresh vegetables", "SEFM": "Processed fruits and vegetables", "SEFM01": "Canned fruits and vegetables", "SEFM02": "Frozen fruits and vegetables", "SEFM03": "Other processed fruits and vegetables including dried", "SEFN": "Juices and nonalcoholic drinks", "SEFN01": "Carbonated drinks", "SEFN02": "Frozen noncarbonated juices and drinks", "SEFN03": "Nonfrozen noncarbonated juices and drinks", "SEFP": "Beverage materials including coffee and tea", "SEFP01": "Coffee", "SEFP02": "Other beverage materials including tea", "SEFR": "Sugar and sweets", "SEFR01": "Sugar and sugar substitutes", "SEFR02": "Candy and chewing gum", "SEFR03": "Other sweets", "SEFS": "Fats and oils", "SEFS01": "Butter and margarine", "SEFS02": "Salad dressing", "SEFS03": "Other fats and oils including peanut butter", "SEFT": "Other foods", "SEFT01": "Soups", "SEFT02": "Frozen and freeze dried prepared foods", "SEFT03": "Snacks", "SEFT04": "Spices, seasonings, condiments, sauces", "SEFT05": "Baby Food and Formula", "SEFT06": "Other miscellaneous foods", "SEFV": "Food away from home", "SEFV01": "Full service meals and snacks", "SEFV02": "Limited service meals and snacks", "SEFV03": "Food at employee sites and schools", "SEFV04": "Food from vending machines and mobile vendors", "SEFV05": "Other food away from home", "SEFW": "Alcoholic beverages at home", "SEFW01": "Beer, ale, and other malt beverages at home", "SEFW02": "Distilled spirits at home", "SEFW03": "Wine at home", "SEFX": "Alcoholic beverages away from home", "SEGA": "Tobacco and smoking products", "SEGA01": "Cigarettes", "SEGA02": "Tobacco products other than cigarettes", "SEGB": "Personal care products", "SEGB01": "Hair, dental, shaving, and miscellaneous personal care products", "SEGB02": "Cosmetics, perfume, bath, nail preparations and implements", "SEGC": "Personal care services", "SEGC01": "Haircuts and other personal care services", "SEGD": "Miscellaneous personal services", "SEGD01": "Legal services", "SEGD02": "Funeral expenses", "SEGD03": "Laundry and dry cleaning services", "SEGD04": "Apparel services other than laundry and dry cleaning", "SEGD05": "Financial services", "SEGE": "Miscellaneous personal goods", "SEHA": "Rent of primary residence", "SEHB": "Lodging away from home", "SEHB01": "Housing at school, excluding board", "SEHB02": "Other lodging away from home including hotels and motels", "SEHC": "Owners' equivalent rent of residences", "SEHC01": "Owners' equivalent rent of primary residence", "SEHD": "Tenants' and household insurance", "SEHE": "Fuel oil and other fuels", "SEHE01": "Fuel oil", "SEHE02": "Propane, kerosene, and firewood", "SEHF": "Energy services", "SEHF01": "Electricity", "SEHF02": "Utility (piped) gas service", "SEHG": "Water and sewer and trash collection services", "SEHG01": "Water and sewerage maintenance", "SEHG02": "Garbage and trash collection", "SEHH": "Window and floor coverings and other linens", "SEHH01": "Floor coverings", "SEHH02": "Window coverings", "SEHH03": "Other linens", "SEHJ": "Furniture and bedding", "SEHJ01": "Bedroom furniture", "SEHJ02": "Living room, kitchen, and dining room furniture", "SEHJ03": "Other furniture", "SEHK": "Appliances", "SEHK01": "Major appliances", "SEHK02": "Other appliances", "SEHL": "Other household equipment and furnishings", "SEHL01": "Clocks, lamps, and decorator items", "SEHL02": "Indoor plants and flowers", "SEHL03": "Dishes and flatware", "SEHL04": "Nonelectric cookware and tableware", "SEHM": "Tools, hardware, outdoor equipment and supplies", "SEHM01": "Tools, hardware and supplies", "SEHM02": "Outdoor equipment and supplies", "SEHN": "Housekeeping supplies", "SEHN01": "Household cleaning products", "SEHN02": "Household paper products", "SEHN03": "Miscellaneous household products", "SEHP": "Household operations", "SEHP01": "Domestic services", "SEHP02": "Gardening and lawncare services", "SEHP03": "Moving, storage, freight expense", "SEHP04": "Repair of household items", "SEMC": "Professional services", "SEMC01": "Physicians' services", "SEMC02": "Dental services", "SEMC03": "Eyeglasses and eye care", "SEMC04": "Services by other medical professionals", "SEMD": "Hospital and related services", "SEMD01": "Hospital services", "SEMD02": "Nursing homes and adult day services", "SEMD03": "Care of invalids and elderly at home", "SEME": "Health insurance", "SEMF": "Medicinal drugs", "SEMF01": "Prescription drugs", "SEMF02": "Nonprescription drugs", "SEMG": "Medical equipment and supplies", "SERA": "Video and audio", "SERA01": "Televisions", "SERA02": "Cable, satellite, and live streaming television service", "SERA03": "Other video equipment", "SERA04": "Purchase, subscription, and rental of video", "SERA05": "Audio equipment", "SERA06": "Recorded music and music subscriptions", "SERAC": "Video and audio products", "SERAS": "Video and audio services", "SERB": "Pets, pet products and services", "SERB01": "Pets and pet products", "SERB02": "Pet services including veterinary", "SERC": "Sporting goods", "SERC01": "Sports vehicles including bicycles", "SERC02": "Sports equipment", "SERD": "Photography", "SERD01": "Photographic equipment and supplies", "SERD02": "Photographers and photo processing", "SERE": "Other recreational goods", "SERE01": "Toys", "SERE02": "Sewing machines, fabric and supplies", "SERE03": "Music instruments and accessories", "SERF": "Other recreation services", "SERF01": "Club membership for shopping clubs, fraternal, or other organizations, or participant sports fees", "SERF02": "Admissions", "SERF03": "Fees for lessons or instructions", "SERG": "Recreational reading materials", "SERG01": "Newspapers and magazines", "SERG02": "Recreational books", "SETA": "New and used motor vehicles", "SETA01": "New vehicles", "SETA02": "Used cars and trucks", "SETA03": "Leased cars and trucks", "SETA04": "Car and truck rental", "SETB": "Motor fuel", "SETB01": "Gasoline (all types)", "SETB02": "Other motor fuels", "SETC": "Motor vehicle parts and equipment", "SETC01": "Tires", "SETC02": "Vehicle accessories other than tires", "SETD": "Motor vehicle maintenance and repair", "SETD01": "Motor vehicle body work", "SETD02": "Motor vehicle maintenance and servicing", "SETD03": "Motor vehicle repair", "SETE": "Motor vehicle insurance", "SETF": "Motor vehicle fees", "SETF01": "State motor vehicle registration and license fees", "SETF03": "Parking and other fees", "SETG": "Public transportation", "SETG01": "Airline fares", "SETG02": "Other intercity transportation", "SETG03": "Intracity transportation", "SS47014": "Gasoline, unleaded regular", "SS47015": "Gasoline, unleaded midgrade", "SS47016": "Gasoline, unleaded premium", "SS5702": "Inpatient hospital services", "SS5703": "Outpatient hospital services"}, "base_code": {"A": "Alternate", "S": "Current"}}, "li": {"area_code": {"0000": "U.S. city average"}, "item_code": {"SL00001": "Piece goods", "SL00002": "Domestics and draperies", "SL00003": "Women's and children's shoes", "SL00004": "Men's shoes", "SL00005": "Infants' wear and furniture", "SL00006": "Women's underwear", "SL00007": "Women's and girls' hosiery", "SL00008": "Women's and girls' accessories", "SL00009": "Women's outerwear & girls' wear", "SL00010": "Men's clothing", "SL00011": "Men's furnishings", "SL00012": "Boys' wear", "SL00013": "Jewelry and silverware", "SL00014": "Notions", "SL00015": "Toilet articles & drugs", "SL00016": "Furniture and bedding", "SL00017": "Floor coverings", "SL00018": "Housewares", "SL00019": "Major appliances", "SL00020": "Radios and television sets", "SL00021": "Recreation & education", "SL00022": "Home improvements", "SL00023": "Automotive accessories", "SLDUR": "Durable goods", "SLMISC": "Miscellaneous", "SLSOFT": "Soft goods", "SLTOTAL": "Store total"}}, "su": {"area_code": {"0000": "U.S. city average"}, "item_code": {"SA0": "All items", "SA0E": "Energy", "SA0L1E": "All items less food and energy", "SAA": "<PERSON><PERSON><PERSON>", "SAC": "Commodities", "SAD": "Durables", "SAE": "Education and communication", "SAE1": "Education", "SAE2": "Communication", "SAF": "Food and beverages", "SAF1": "Food", "SAF11": "Food at home", "SAF116": "Alcoholic beverages", "SAG": "Other goods and services", "SAH": "Housing", "SAH1": "Shelter", "SAH2": "Fuels and utilities", "SAH3": "Household furnishings and operations", "SAM": "Medical care", "SAM1": "Medical care commodities", "SAM2": "Medical care services", "SAN": "Nondurables", "SAR": "Recreation", "SAS": "Services", "SAT": "Transportation", "SAT1": "Private transportation", "SEFV": "Food away from home", "SETA01": "New vehicles", "SETG": "Public transportation"}, "base_code": {"S": "Current"}, "footnote_code": {"I": "Initial", "U": "Interim"}}, "ei": {"index_code": {"CD": "Locality of Destination Price Indexes", "CO": "Locality of Origin Price Indexes", "CT": "Terms of Trade Indexes", "IC": "Services Inbound Price Indexes", "ID": "Harmonized System Export Price Indexes", "IH": "Services Export Price Indexes", "IP": "Harmonized System Import Price Indexes", "IQ": "BEA End Use Export Price Indexes", "IR": "BEA End Use Import Price Indexes", "IS": "Services Outbound Price Indexes", "IV": "Services Import Price Indexes", "IY": "NAICS Export Price Indexes", "IZ": "NAICS Import Price Indexes"}, "footnote_code": {"2": "Western Europe, Canada, Japan, Australia, New Zealand, and South Africa.", "3": "Mexico, Central America, South America, and the Caribbean.", "4": "China, Japan, Australia, Brunei, Indonesia, Macao, Malaysia, New Zealand, Papua New Guinea, Philippines, and the Asian Newly Industrialized Countries.", "5": "Asian Newly Industrialized Countries - Hong Kong, Singapore, South Korea, and Taiwan.", "6": "Association of Southeast Asian Nations - Brunei, Cambodia, Indonesia, Laos, Malaysia, Myanmar, Philippines, Singapore, Thailand, and Vietnam.", "7": "Bahrain, Iran, Iraq, Israel, Jordan, Kuwait, Lebanon, Oman, Qatar, Saudi Arabia, Syria, United Arab Emirates, and Yemen.", "R": "Revised."}}}